using Bis;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq.Expressions;
using static Bis.RiskIndex;

namespace BisUT
{
	[TestClass]
	public class Rules_Validations
	{
		//private Parameters parameters;
		private RiskRule id_1__si_menor_18__si_menor_18_deny;
		private RiskRule id_2__salario_menor_4999_deny;
		private RiskRule id_3__salario_mayorigual_5k_allow;
		private RiskIndex onPurchase;

		public void SetUp()
		{
			onPurchase = new RiskIndex("onPurchase");//fresh
			onPurchase
				.IndexOn("edad", typeof(int))
				.IndexOn("salario", typeof(decimal));//fresh

			id_1__si_menor_18__si_menor_18_deny = new RiskRule
				(
					1,
					new RiskPredicate[]
					{
							new RiskPredicate(
								new LowerOp(
									onPurchase.Variable("edad"),
									new IntLiteral(18)
								)
							),
							new RiskPredicate(
								new LowerOp(
									onPurchase.Variable("edad"),
									new IntLiteral(18)
								)
							)
					},
					RiskAction.Deny
				);
			id_2__salario_menor_4999_deny = new RiskRule
			(
				2,
				new RiskPredicate[]
				{
								new RiskPredicate(
									new LowerOp(
										onPurchase.Variable("salario"),
										new DecimalLiteral(4999)
									)
								)
				},
				RiskAction.Deny
			);
			id_3__salario_mayorigual_5k_allow = new RiskRule
			(
				3,
				new RiskPredicate[]
				{
								new RiskPredicate(
									new GreatherOrEqualOp(
										onPurchase.Variable("salario"),
										new DecimalLiteral(5000)
									)
								)
				},
				RiskAction.Allow
			);
		}
		[TestMethod]
		public void Solo_Mayores_de_edad_con_salario_mayor_a_5k()
		{
			SetUp();

			//magnet con tablas hace crud.
			onPurchase.Include(id_1__si_menor_18__si_menor_18_deny);//consumer
			onPurchase.Include(id_2__salario_menor_4999_deny);//consumer
			onPurchase.Include(id_3__salario_mayorigual_5k_allow);//consumer

			Result result = onPurchase.Evaluate(new Parameters()
			{
				["edad"] = 17,
				["salario"] = 0
			});// servicio compra
			Assert.AreEqual(1, result.RuleId);// 
			Assert.AreEqual(RiskAction.Deny, result.Action);

			result = onPurchase.Evaluate(new Parameters()
			{
				["edad"] = 18,
				["salario"] = 100
			});
			Assert.AreEqual(2, result.RuleId);
			Assert.AreEqual(RiskAction.Deny, result.Action);

			result = onPurchase.Evaluate(new Parameters()
			{
				["edad"] = 26,
				["salario"] = 10000
			});
			Assert.AreEqual(3, result.RuleId);
			Assert.AreEqual(RiskAction.Allow, result.Action);

			result = onPurchase.Evaluate(new Parameters()
			{
				["edad"] = 26,
				["salario"] = 100
			});
			Assert.AreEqual(2, result.RuleId);
			Assert.AreEqual(RiskAction.Deny, result.Action);
		}

		[TestMethod]
		public void Result_Constant_Validations()
		{
			Func<Result> executable = Expression.Lambda<Func<Result>>(Result.ALLOW_EXP).Compile();
			Result result = executable();
			Assert.AreEqual(0, result.RuleId);
			Assert.AreEqual(RiskAction.Allow, result.Action);


			executable = Expression.Lambda<Func<Result>>(Result.UNMATCHED_EXP).Compile();
			result = executable();
			Assert.AreEqual(-1, result.RuleId);
			Assert.AreEqual(RiskAction.Allow, result.Action);
		}


		[TestMethod]
		public void RiskRule_One_Predicate_Validation()
		{
			RiskRule aRule = new RiskRule
			(
				10,
				new RiskPredicate[]
				{
					new RiskPredicate(
						new EqualOp(
							new IntLiteral(1),
							new IntLiteral(1)
						)
					)
				},
				RiskAction.Allow
			);

			Func<Result> executable = Expression.Lambda<Func<Result>>(aRule.Evaluate).Compile();
			Result result = executable();
			Assert.AreEqual(10, result.RuleId);
			Assert.AreEqual(RiskAction.Allow, result.Action);
		}

		[TestMethod]
		public void RiskRule_Hundred_Predicates_Validation()
		{
			List<RiskPredicate> predicates = new List<RiskPredicate>();
			for (int i = 0; i <= 99; i++)
			{
				predicates.Add(new RiskPredicate(
					new EqualOp(
						new IntLiteral(1),
						new IntLiteral(1)
					)
				));
			}
			RiskRule aRule = new RiskRule
			(
				11,
				predicates.ToArray(),
				RiskAction.Allow
			);

			Func<Result> executable = Expression.Lambda<Func<Result>>(aRule.Evaluate).Compile();
			Result result = executable();
			Assert.AreEqual(11, result.RuleId);
			Assert.AreEqual(RiskAction.Allow, result.Action);
		}

		[TestMethod]
		public void RiskRule_No_Match_Hundred_Predicates_Validation()
		{
			List<RiskPredicate> predicates = new List<RiskPredicate>();
			for (int i = 0; i <= 99; i++)
			{
				if (i == 56)
				{
					predicates.Add(new RiskPredicate(
						new EqualOp(
							new IntLiteral(1),
							new IntLiteral(2)
						)
					));
				}
				else
				{
					predicates.Add(new RiskPredicate(
						new EqualOp(
							new IntLiteral(1),
							new IntLiteral(1)
						)
					));
				}

			}
			RiskRule aRule = new RiskRule
			(
				12,
				predicates.ToArray(),
				RiskAction.Allow
			);

			Func<Result> executable = Expression.Lambda<Func<Result>>(aRule.Evaluate).Compile();
			Result result = executable();
			Assert.AreEqual(-1, result.RuleId);
			Assert.AreEqual(RiskAction.Allow, result.Action);
			Assert.AreEqual(Result.UNMATCHED, result);
		}

		[TestMethod]
		public void RiskIndex_One_RiskRule_Validation()
		{
			RiskIndex onPurchase = new RiskIndex("onPurchase");
			onPurchase
				.IndexOn("account", typeof(string));

			RiskRule aRule = new RiskRule
			(
				9,
				new RiskPredicate[]
				{
					new RiskPredicate(
						new EqualOp(
							new StringLiteral("abc123"),
							new StringLiteral("abc123")
						)
					)
				},
				RiskAction.Allow
			);
			onPurchase.Include(aRule);

			Result result = onPurchase.Evaluate(new Parameters()
			{
			});

			Assert.AreEqual(9, result.RuleId);
			Assert.AreEqual(RiskAction.Allow, result.Action);
		}

		[TestMethod]
		public void RiskIndex_Variable_In_RiskRule_Validation()
		{
			RiskIndex onPurchase = new RiskIndex("onPurchase");
			onPurchase
				.IndexOn("account", typeof(string));

			RiskRule aRule = new RiskRule
			(
				9,
				new RiskPredicate[]
				{
					new RiskPredicate(
						new EqualOp(
							onPurchase.Variable("account"),
							new StringLiteral("123")
						)
					)
				},
				RiskAction.Allow
			);
			onPurchase.Include(aRule);

			Result result = onPurchase.Evaluate(new Parameters()
			{
				["account"] = "123"
			});
			Assert.AreEqual(9, result.RuleId);
			Assert.AreEqual(RiskAction.Allow, result.Action);
		}

		[TestMethod]
		public void RiskIndex_More_RiskRule_Validation()
		{
			RiskIndex onPurchase = new RiskIndex("onPurchase");
			onPurchase
				.IndexOn("account", typeof(string));

			RiskRule aRule = new RiskRule
			(
				9,
				new RiskPredicate[]
				{
					new RiskPredicate(
						new EqualOp(
							onPurchase.Variable("account"),
							new StringLiteral("123")
						)
					)
				},
				RiskAction.Allow
			);
			onPurchase.Include(aRule);

			Result result = onPurchase.Evaluate(new Parameters()
			{
				["account"] = "123"
			});
			Assert.AreEqual(9, result.RuleId);
			Assert.AreEqual(RiskAction.Allow, result.Action);

			RiskRule bRule = new RiskRule
			(
				10,
				new RiskPredicate[]
				{
					new RiskPredicate(
						new EqualOp(
							onPurchase.Variable("account"),
							new StringLiteral("1234")
						)
					)
				},
				RiskAction.Deny
			);
			onPurchase.Include(bRule);

			result = onPurchase.Evaluate(new Parameters()
			{
				["account"] = "123"
			});
			Assert.AreEqual(9, result.RuleId);
			Assert.AreEqual(RiskAction.Allow, result.Action);

			result = onPurchase.Evaluate(new Parameters()
			{
				["account"] = "1234"
			});
			Assert.AreEqual(10, result.RuleId);
			Assert.AreEqual(RiskAction.Deny, result.Action);


			result = onPurchase.Evaluate(new Parameters()
			{
				["account"] = ""
			});
			Assert.AreEqual(0, result.RuleId);
			Assert.AreEqual(RiskAction.Allow, result.Action);
		}

		[TestMethod]
		public void Int_Aggregation_WithTwo_rules_and_one_is_disable()
		{
			RiskIndex index = new RiskIndex("onTest");
			index.IndexOn("amount", typeof(int));
			index.IndexOn("account", typeof(string));
			index.IndexOn("gameType", typeof(string));

			RiskRule rule1 = new RiskRule
			(
				13,
				new RiskPredicate[]
				{
					new RiskPredicate(
						new LowerOrEqualOp(
							index.Total("amount"),
							new IntLiteral(5000)
						)
					),
					new RiskPredicate(
						new IsOp(
							index.Variable("gameType"),
							new StringLiteral("soccer")
						)
					)
					,
					new RiskPredicate(
						new IsOp(
							index.Variable("account"),
							new StringLiteral("ABC123")
						)
					)
				},
				RiskAction.Allow
			);
			index.Include(rule1);
			RiskRule rule2 = new RiskRule
			(
				15,
				new RiskPredicate[]
				{
					new RiskPredicate(
						new LowerOrEqualOp(
							index.Total("amount"),
							new IntLiteral(10000)
						)
					),
					new RiskPredicate(
						new IsOp(
							index.Variable("gameType"),
							new StringLiteral("golf")
						)
					)
					,
					new RiskPredicate(
						new IsOp(
							index.Variable("account"),
							new StringLiteral("ABC123")
						)
					)
				},
				RiskAction.Allow
			);
			index.Include(rule2);

			Parameters parameters = new Parameters();

			parameters["account"] = "ABC123";
			parameters["gameType"] = "soccer";
			parameters["amount"] = 500;

			Debug.WriteLine("Test 1");
			var result = index.Evaluate(parameters);

			Assert.AreEqual(13, result.RuleId);
			Assert.AreEqual(RiskAction.Allow, result.Action);

			Debug.WriteLine("Test 2");
			rule1.Disable();
			result = index.Evaluate(parameters);

			Assert.AreEqual(Result.ALLOW, result);

			parameters = new Parameters();

			parameters["account"] = "ABC123";
			parameters["gameType"] = "golf";
			parameters["amount"] = 500;

			Debug.WriteLine("Test 3");
			rule2.Disable();
			result = index.Evaluate(parameters);

			Assert.AreEqual(Result.ALLOW, result);

			Debug.WriteLine("Test 4");
			rule2.Enable();
			result = index.Evaluate(parameters);

			Assert.AreEqual(15, result.RuleId);
			Assert.AreEqual(RiskAction.Allow, result.Action);
		}
	}

}