﻿using GamesEngine.Business;
using GamesEngine.Domains;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using town.connectors.drivers;
using static GamesEngine.Business.WholePaymentProcessor;

[assembly: InternalsVisibleTo("GuardianAPI")]
namespace GamesEngine.Custodian.Operations
{
	internal abstract class WithDrawal : Operation
	{
		internal Disbursements Disbursements{ get; private set; }
		internal int ReferenceNumber { get;}
		internal string Description { get { return $"{DisbursementAmount.ToDisplayFormat()} {nameof(WithDrawal)}"; } }
		internal Currency DisbursementAmount { get;}
		internal int Group { get;}
		internal PaymentProcessor Processor { get;}
		internal TransactionType Abbreviation { get;}
		internal TransactionFees Fees { get;}
		internal DateTime CreationDate { get;}
		internal string CurrencyCode => DisbursementAmount.Coin.Iso4217Code;
		internal Coin Coin { get { return DisbursementAmount.Coin;  } }
		public bool Scheduled { get; protected set; }
		internal Domain Domain { get; }
		internal StatusCodes Status { get; }
		internal string Identifier { get; }
		internal string EmployeeName { get; }
		private readonly Guardian guardian;
		internal AccountNumber ProcessorAccount { get; }

		public Approvers ApproversWhoAlreadyApprove { get; protected set; }
		internal WithDrawal(Guardian guardian, StatusCodes status, TransactionType abbreviation, DateTime creationDate, int transctionId, int referenceNumber, Currency disbursementAmount, int group,
			PaymentProcessor processor, Domain domain, string identificationDocumentNumber, string employeeName)
			:this(guardian, status, abbreviation, creationDate, transctionId, referenceNumber, disbursementAmount, group, processor, domain, null, identificationDocumentNumber, employeeName)
		{

		}
		internal WithDrawal(Guardian guardian, StatusCodes status, TransactionType abbreviation, DateTime creationDate, int transctionId, int referenceNumber, Currency disbursementAmount, int group,
			PaymentProcessor processor, Domain domain, Disbursements disbursements, string identificationDocumentNumber, string employeeName)
			: base(transctionId)
		{
			if (guardian == null) throw new ArgumentNullException(nameof(guardian));
			if (transctionId <= 0) throw new GameEngineException($"{nameof(transctionId)} must be greater than 0");
			if (referenceNumber <= 0) throw new GameEngineException($"{nameof(referenceNumber)} must be greater than 0");
			if (disbursementAmount == null) throw new ArgumentNullException(nameof(disbursementAmount));
			if (group <= 0) throw new GameEngineException($"{nameof(group)} must be greater than 0");
			if (processor == null) throw new ArgumentNullException(nameof(processor));
			if (domain == null) throw new ArgumentNullException(nameof(domain));
			if (disbursements != null && disbursements.Count()<=0) throw new GameEngineException($"{nameof(disbursements)} must have at leat one {nameof(Disbursement)}.");
			if (string.IsNullOrEmpty(identificationDocumentNumber)) throw new ArgumentNullException(nameof(identificationDocumentNumber));

			DisbursementAmount = disbursementAmount;
			Group = group;
			Processor = processor;
			Abbreviation = abbreviation;
			Fees = new TransactionFees();
			CreationDate = creationDate;
			ReferenceNumber = referenceNumber;
			Scheduled = false;
			Domain = domain;
			Status = status;
			Identifier = identificationDocumentNumber;
			EmployeeName = employeeName;
			this.guardian = guardian;
			ProcessorAccount = guardian.Accounts().SearchBy(processor);
			if (disbursements != null)
			{
				Disbursements = disbursements.Copy(this);
			}
			else
			{
				Disbursements = disbursements;
			}
		}
		internal void Add(TransactionFee fee)
		{
			if (DisbursementAmount.CurrencyCode != fee.CurrencyCode) throw new GameEngineException($"{nameof(DisbursementAmount)} and {nameof(fee)} has no the same currency.");

			Fees.Add(fee);
		}
		internal abstract InternalOperationUpdateMessage GenerateMessage(DateTime now);
		internal void Apply(bool itIsThePresent, DateTime now, DisbursementsChanges disbursementsToBeApplied)
		{
			Disbursements.ValidateChanges(Disbursements, now, disbursementsToBeApplied, DisbursementAmount);

			if (Disbursements == null)
			{
				if (disbursementsToBeApplied.DisbursementsToBeRemoved.Count > 0)
				{
					throw new GameEngineException($"There are no {nameof(Disbursement)} to remove.");
				}

				Disbursements = new Disbursements(
					disbursementsToBeApplied.ListToDisbursementUnpayed(this),
					BeforeDisbursementIsUpdate,
					AfterDisbursementIsUpdate,
					DisbursmentHasBeenAdded,
					DisbursementHasBeenARemoved
					);
			}
			else
			{
				Disbursements.Update(this, disbursementsToBeApplied);
			}

			ScheduledThisOperation();

			if (itIsThePresent && Scheduled)
			{
				Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForGuardianInternalOperations, this.GenerateMessage(now));
			}
		}
		internal void BeforeDisbursementIsUpdate(DateTime newDate, decimal newAmount, Disbursement disbursement, AccountNumber account, DateTime scheduledDate, decimal amount)
		{
			if (disbursement is DisbursemenWithTransaction && ((DisbursemenWithTransaction)disbursement).TransactionStatus == StatusCodes.IN_PROCESS)
			{
				account.UpdateAccumulate(DisbursementAmount.Coin, scheduledDate, amount, Processor.Driver.Id, Abbreviation);
			}
		}
		internal void AfterDisbursementIsUpdate(DateTime previousDate, decimal previousAmount, Disbursement disbursement, AccountNumber account, DateTime scheduledDate, decimal amount)
		{
			if (disbursement is DisbursemenWithTransaction && ((DisbursemenWithTransaction)disbursement).TransactionStatus == StatusCodes.IN_PROCESS)
			{
				account.UpdateAccumulate(DisbursementAmount.Coin, scheduledDate, amount, Processor.Driver.Id, Abbreviation);

				bool dateHasBeenChanged = previousDate != disbursement.ScheduledDate;
				if (dateHasBeenChanged)
				{
					DisbursementHasBeenARemoved(previousDate, disbursement);
					DisbursmentHasBeenAdded(disbursement);
				}
			}
		}
		private void DisbursmentHasBeenAdded(Disbursement disbursement)
		{
			if (disbursement is DisbursemenWithTransaction && ((DisbursemenWithTransaction)disbursement).TransactionStatus == StatusCodes.IN_PROCESS)
			{
				disbursement.Account.UpdateAccumulate(DisbursementAmount.Coin, disbursement.ScheduledDate, disbursement.Amount, Processor.Driver.Id, Abbreviation);
				guardian.DisbursmentsByDate().Add(disbursement);
			}
		}
		internal void DisbursementHasBeenARemoved(DateTime scheduledDate, Disbursement disbursement)
		{
			if (disbursement is DisbursemenWithTransaction && ((DisbursemenWithTransaction)disbursement).TransactionStatus == StatusCodes.IN_PROCESS)
			{
				disbursement.Account.UpdateAccumulate(DisbursementAmount.Coin, disbursement.ScheduledDate, -disbursement.Amount, Processor.Driver.Id, Abbreviation);
				guardian.DisbursmentsByDate().Remove(scheduledDate, disbursement);
			}
		}
		internal void ReplaceDisbursment(DisbursementUnpayed disbursement, DisbursementExecuted disbursementExecuted)
		{
			Disbursements.ReplaceDisbursment(disbursement, disbursementExecuted);
		}
		internal int AmountOfDisbursements()
		{
			if (Disbursements == null) return 0;
			return Disbursements.Count();
		}
		internal Disbursement SearchDisbusmentById(int disburmentId)
		{
			return Disbursements.SearchById(disburmentId);
		}
		internal void ReplaceDisbursment(Disbursement disbursementUnpayed, Disbursement disbursementExecuted)
		{
			Disbursements.ReplaceDisbursment(disbursementUnpayed, disbursementExecuted);
		}
		private void ScheduledThisOperation()
		{
			Currency amountToBeDisbursement = Currency.Factory(DisbursementAmount.CurrencyCode, DisbursementAmount.Value);
			foreach (Disbursement disbursement in Disbursements.List())
			{
				amountToBeDisbursement.Subtract(disbursement.Amount);
			}

			if (amountToBeDisbursement.Value != 0) throw new GameEngineException($"It's not valid to schedule a {nameof(PendingWithDrawal)} with pending amount;");

			Scheduled = true;
		}
		public bool TransactionItsCompletelyPayed
		{
			get
			{
				bool allDisbursementsArePayed = true;
				foreach (Disbursement disbursementn in Disbursements.List())
				{
					if (!disbursementn.ItsApproved)
					{
						allDisbursementsArePayed = false;
						break;
					}
				}
				return allDisbursementsArePayed;
			}
		}
		protected int CalculatePercentajeOfExecution()
		{
			if (Disbursements == null || Disbursements.Count() <= 0) return 0;
			int executed = 0;
			foreach (Disbursement disbursementn in Disbursements.List())
			{
				if (disbursementn.Status == DisbursmentStatusCode.APPROVED) executed++;
			}

			return (executed * 100) / Disbursements.Count();
		}
	}

	[Puppet]
	internal class RequiredProfilesApproval : Objeto
	{
		private Dictionary<int, RequiredProfileApproval> requiredProfilesApproval = new Dictionary<int, RequiredProfileApproval>();

		internal RequiredProfilesApproval(Profiles requiredProfilesApprove)
		{
			foreach (Profile profile in requiredProfilesApprove.List())
			{
				if (!requiredProfilesApproval.ContainsKey(profile.Id)) requiredProfilesApproval.Add(profile.Id, new RequiredProfileApproval(profile));
			}
		}

		internal Profiles NotApproved()
		{
			Profiles result = new Profiles();
			foreach (RequiredProfileApproval requiredProfileApproval in requiredProfilesApproval.Values)
			{
				if (!requiredProfileApproval.ItAlreadyApproved) result.Add(requiredProfileApproval.Profile);
			}

			return result;
		}

		internal bool Exists(Approver approver)
		{
			foreach (RequiredProfileApproval requiredProfileApproval in requiredProfilesApproval.Values)
			{
				if (requiredProfileApproval.Profile.Exists(approver)) return true;
			}
			return false;
		}

		internal void Approve(Profiles profilesWhichApproverRepresents)
		{
			foreach (Profile profile in profilesWhichApproverRepresents.List())
			{
				requiredProfilesApproval[profile.Id].Approve();
			}
		}

		internal int Approved()
		{
			int result = 0;
			foreach (RequiredProfileApproval requiredProfileApproval in requiredProfilesApproval.Values)
			{
				if (requiredProfileApproval.ItAlreadyApproved) result++;
			}

			return result;
		}

		internal int Count()
		{
			return requiredProfilesApproval.Count;
		}

		internal IEnumerable<RequiredProfileApproval> List()
		{
			return requiredProfilesApproval.Values.ToList();
		}

		internal List<int> ListProfileIds()
		{
			List<int> result = new List<int>();
			foreach (RequiredProfileApproval requiredProfileApproval in requiredProfilesApproval.Values)
			{
				result.Add(requiredProfileApproval.Profile.Id);
			}
			return result;
		}
	}

	[Puppet]
	internal class RequiredProfileApproval : Objeto
	{
		public override string ToString()
		{
			return Profile.Name ;
		}

		public Profile Profile { get; }

		private bool approved;
		internal RequiredProfileApproval(Profile profile)
		{
			if (profile == null) throw new ArgumentNullException(nameof(profile));

			Profile = profile;
			this.approved = false;
		}

		internal bool ItAlreadyApproved { get { return approved; } }

		internal void Approve()
		{
			approved = true;
		}
	}

	public class WithdrawalModification
	{
		public List<int> ProfileIds { get; set; }
		public bool HasProfiles { get { return ProfileIds != null && ProfileIds.Count > 0; } }
		public string ProfilesToCommand { get { return string.Join(",", ProfileIds); } }
	}
}
