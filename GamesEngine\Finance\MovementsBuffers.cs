﻿using GamesEngine.MessageQueuing;
using GamesEngine.Settings;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;

namespace GamesEngine.Finance
{
    public class MovementsBuffers
    {
        internal const int NoBuffer = -1;
        readonly static ConcurrentQueue<MovementBuffer> buffersInUse = new ConcurrentQueue<MovementBuffer>();
        readonly static ConcurrentQueue<MovementBuffer> freeBuffers = new ConcurrentQueue<MovementBuffer>();

        internal static MovementBuffer Get(int id)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");

            var result = buffersInUse.FirstOrDefault(buffer => buffer.Id == id);
            if (result == null) throw new GameEngineException($"No buffer with id '{id}'");
            return result;
        }

        static int nextConsecutive = 0;
        static int NextConsecutive()
        {
            return nextConsecutive + 1;
        }

        static bool AreThereFreeBuffers()
        {
            var result = freeBuffers.Count > 0;
            return result;
        }

        static object lockFreeBuffers = new object();
        public static MovementBuffer GetOrCreateBuffer(bool itIsThePresent)
        {
            MovementBuffer buffer;
            lock (lockFreeBuffers)
            {
                if (AreThereFreeBuffers())
                {
                    if (!freeBuffers.TryDequeue(out buffer)) throw new GameEngineException($"No free buffers");
                }
                else
                {
                    nextConsecutive = NextConsecutive();
                    buffer = new MovementBuffer(nextConsecutive, itIsThePresent);
                }
            }
            buffersInUse.Enqueue(buffer);
            return buffer;
        }

        public static void Flush(MovementBuffer buffer)
        {
            if (buffer == null) throw new ArgumentNullException(nameof(buffer));
            buffer.Flush();
            lock (lockFreeBuffers)
            {
                freeBuffers.Enqueue(buffer);
            }
        }
    }

    public class MovementBuffer
    {
        readonly MovementMessagesBuffer<NewMovementMessage> buffer;
        public int Id { get; }

        public MovementBuffer(int id, bool itIsThePresent)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");

            Id = id;
            buffer = new MovementMessagesBuffer<NewMovementMessage>(Integration.Kafka.TopicForMovements, itIsThePresent);
        }

        internal void Add(NewMovementMessage message)
        {
            if (message == null) throw new ArgumentNullException(nameof(message));

            buffer.Add(message);
        }

        public void Flush()
        {
            buffer.Flush();
        }

        const int DefaultPartition = 0;
        string currentAtAddress = string.Empty;
        public void UpdateTopicName(string atAddress)
        {
            if (string.IsNullOrWhiteSpace(atAddress)) throw new ArgumentNullException(nameof(atAddress));
            if (! RequiresToChangeTopic(atAddress)) throw new GameEngineException($"{nameof(atAddress)} '{atAddress}' to update is the same than the current");

            currentAtAddress = atAddress;
            var lastDigit = ((int)atAddress.Last()) % 10;
            buffer.UpdateTopicName($"{Integration.Kafka.TopicForMovements}_{lastDigit}", DefaultPartition);
        }

        public bool RequiresToChangeTopic(string atAddress)
        {
            return ! currentAtAddress.Equals(atAddress, StringComparison.OrdinalIgnoreCase);
        }

        public bool IsEmpty()
        {
            return buffer.IsEmpty();
        }
    }

	internal class MovementMessagesBuffer<T> : IEnumerable<T> where T : KafkaMessage
    {
		private readonly List<T> messages = new List<T>();
		private string kafkaTopic;
		private readonly bool itIsThePresent;
		int partition = 0;

		internal MovementMessagesBuffer(string kafkaTopic, bool itIsThePresent)
		{
            if (string.IsNullOrWhiteSpace(kafkaTopic)) throw new ArgumentNullException(nameof(kafkaTopic));

            this.kafkaTopic = kafkaTopic;
            this.itIsThePresent = itIsThePresent;
        }

		internal void Add(T message)
		{
			this.messages.Add(message);
		}

		internal void Flush()
		{
			if (this.itIsThePresent)
			{
				if (Integration.UseKafka || Integration.UseKafkaForAuto)
				{
					KafkaMessages buffer = new KafkaMessages();

					foreach (T info in messages)
					{
						string message = info.Serialize();
                        buffer.Add(message);
                    }

					if (buffer.ItsNotEmpty())
					{
						if (!Integration.UseKafkaForAuto)
						{
							Integration.Kafka.Send(this.itIsThePresent, this.kafkaTopic, buffer, partition);
							buffer.Clear();
						}
						else
						{
							Integration.Kafka.Send(this.kafkaTopic, buffer);
						}
					}
				}
			}
			this.messages.Clear();
		}

		internal void UpdateTopicName(string kafkaTopic, int partition)
		{
			if (string.IsNullOrWhiteSpace(kafkaTopic)) throw new ArgumentNullException(nameof(kafkaTopic));
			if (! IsEmpty()) throw new GameEngineException("Messages collection must be empty to change the topic");

			this.kafkaTopic = kafkaTopic;
			this.partition = partition;
		}

        internal bool IsEmpty()
        {
            return messages.Count == 0;
        }

        public IEnumerator<T> GetEnumerator() => this.messages.GetEnumerator();

        System.Collections.IEnumerator System.Collections.IEnumerable.GetEnumerator() => (System.Collections.IEnumerator)this;
    }
}
