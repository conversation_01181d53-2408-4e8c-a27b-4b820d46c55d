﻿using GamesEngine.Business;
using GamesEngine.Finance;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using town.connectors.drivers;
using static GamesEngine.Finance.Currencies;

namespace GamesEngine.Exchange
{
    [Puppet]
    public class RiskRatings : Objeto
    {
        internal const int MAX_AMOUNT_LOG_ENTRIES = 10;
        internal static Log Log { get; } = new Log(MAX_AMOUNT_LOG_ENTRIES);

        private readonly List<RiskRating> riskRatings = new List<RiskRating>();

        internal int Count => riskRatings.Count;

        internal int MaxValidPriority => riskRatings.Count + 1;

        internal RiskRating NewRiskRating(string name, string description, int priority)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (string.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));
            if (ExistsName(name)) throw new GameEngineException($"There is a {nameof(RiskRating)} with the {nameof(name)} '{name}'");
            if (! IsValidPriorityToAdd(priority)) throw new GameEngineException($"{nameof(priority)} '{priority}' is out of range");

            var riskRating = new RiskRating(this, name, description, priority);
            riskRatings.Add(riskRating);

            ReorderByAddition(priority, riskRating);
            riskRating.LockPriority();
            return riskRating;
        }
        private void ReorderByAddition(int priorityToStartMoving, RiskRating fixedRiskRating)
        {
            if (fixedRiskRating.PriorityLocked) throw new GameEngineException($"Priority is already locked");

            foreach (var riskRating in riskRatings)
            {
                if (riskRating == fixedRiskRating) continue;
                if (riskRating.Priority >= priorityToStartMoving) 
                    riskRating.IncreasePriorityAccording(fixedRiskRating);
            }
        }
        internal void ReorderByEdition(int lastPriority, RiskRating fixedRiskRating)
        {
            if (fixedRiskRating.PriorityLocked) throw new GameEngineException($"Priority is already locked");

            var currentPriority = fixedRiskRating.Priority;
            if (lastPriority == currentPriority) return;

            var isUpper = currentPriority < lastPriority;
            foreach (var riskRating in List())
            {
                if (riskRating == fixedRiskRating) continue;
                if (isUpper)
                {
                    if (riskRating.Priority < currentPriority) continue;
                    if (riskRating.Priority > lastPriority) break;
                    if (riskRating.Priority >= currentPriority)
                        riskRating.IncreasePriorityAccording(fixedRiskRating);
                }
                else
                {
                    if (riskRating.Priority < lastPriority) continue;
                    if (riskRating.Priority > currentPriority) break;
                    if (riskRating.Priority >= lastPriority)
                        riskRating.DecreasePriorityAccording(fixedRiskRating);
                }
            }
        }
        internal IEnumerable<RiskRating> List()
        {
            return riskRatings.OrderBy(riskRating => riskRating.Priority).ToList();
        }
        internal IEnumerable<RiskRating> ListEnabledRiskRatings()
        {
            var result = riskRatings.Where(riskRating => riskRating.Enabled).ToList();
            return result;
        }
        internal RiskRating FindRiskRating(string name)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

            foreach (var riskRating in riskRatings)
            {
                if (riskRating.Name == name) return riskRating;
            }
            throw new GameEngineException($"There is no {nameof(RiskRating)} for {nameof(name)} '{name}' yet.");
        }

        internal bool IsValidPriorityToAdd(int priority)
        {
            return priority >= 1 && priority <= MaxValidPriority;
        }

        internal bool IsValidPriorityToUpdate(int priority)
        {
            return priority >= 1 && priority <= Count;
        }

        internal bool ExistsName(string name)
        {
            if (string.IsNullOrWhiteSpace(name)) return false;

            foreach (var riskRating in riskRatings)
            {
                if (riskRating.Name == name) return true;
            }
            return false;
        }

        internal bool ExistsAllRiskRating(List<string> names)
        {
            foreach (var name in names)
            {
                if ( ! ExistsName(name)) return false;
            }
            return true;
        }

        internal IEnumerable<string> ListLog()
        {
            return Log.List();
        }
    }

    [Puppet]
    internal class RiskRating : Objeto
    {
        internal string Name { get; private set; }

        internal string Description { get; private set; }

        internal bool Enabled { get; private set; }

        internal int Priority { get; private set; }

        internal bool PriorityLocked { get; private set; }

        private readonly RiskRatings riskRatings;

        internal RiskRating(RiskRatings riskRatings, string name, string description, int priority)
        {
            if (riskRatings == null) throw new ArgumentNullException(nameof(riskRatings));
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (! riskRatings.IsValidPriorityToAdd(priority)) throw new GameEngineException($"{nameof(priority)} '{priority}' is out of range");

            this.riskRatings = riskRatings;
            Name = name;
            Description = description;
            Priority = priority;
            Enabled = true;
        }

        internal void Update(string name, string description, int priority, bool enabled)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (string.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));
            if (Name != name && riskRatings.ExistsName(name)) throw new GameEngineException($"There is a {nameof(RiskRating)} with the {nameof(name)} '{name}'");
            if (! riskRatings.IsValidPriorityToUpdate(priority)) throw new GameEngineException($"{nameof(priority)} '{priority}' is out of range");

            Name = name;
            Description = description;
            var lastPriority = Priority;
            Priority = priority;
            Enabled = enabled;

            PriorityLocked = false;
            riskRatings.ReorderByEdition(lastPriority, this);
            PriorityLocked = true;
        }

        internal void IncreasePriorityAccording(RiskRating riskRating)
        {
            if (riskRating.PriorityLocked) throw new GameEngineException($"{nameof(Priority)} is already locked");

            Priority++;
        }

        internal void DecreasePriorityAccording(RiskRating riskRating)
        {
            if (riskRating.PriorityLocked) throw new GameEngineException($"{nameof(Priority)} is already locked");

            Priority--;
        }

        internal void Enable()
        {
            Enabled = true;
        }

        internal void Disable()
        {
            Enabled = false;
        }

        internal void LockPriority()
        {
            PriorityLocked = true;
        }
    }

    [Puppet]
    internal class AmountRangeByCurrencies : Objeto
    {
        private readonly Dictionary<Coin, AmountRange> amountRangesByCurrency = new Dictionary<Coin, AmountRange>();
        private readonly List<TransactionType> transactionTypes = new List<TransactionType>();
        internal RiskRating RiskRating { get; }

        internal string RiskRatingName => RiskRating.Name;

        internal AmountRangeByCurrencies(RiskRating riskRating, List<string> transactionTypes)
        {
            if (riskRating == null) throw new ArgumentNullException(nameof(riskRating));
            if (transactionTypes == null || transactionTypes.Count == 0) throw new GameEngineException($"{nameof(transactionTypes)} cannot be empty");

            RiskRating = riskRating;

            foreach (var type in transactionTypes)
            {
                TransactionType transactionType;
                if (!Enum.TryParse(type, out transactionType)) throw new GameEngineException($"{nameof(type)} '{type}' does not exist");
                this.transactionTypes.Add(transactionType);
            }
        }

        internal IEnumerable<AmountRange> ListAmountRanges()
        {
            return amountRangesByCurrency.Values.ToList();
        }

        internal IEnumerable<string> ListTransactionTypes()
        {
            return transactionTypes.Select(type => type.ToString()).ToList();
        }

        internal void SetAmountRange(Currency maxAmount, Currency minAmount, DateTime now, string employeeName)
        {
            if (maxAmount == null) throw new ArgumentNullException(nameof(maxAmount));
            if (minAmount == null) throw new ArgumentNullException(nameof(minAmount));
            if (maxAmount.CurrencyCode != minAmount.CurrencyCode) throw new GameEngineException($"{nameof(maxAmount)} and {nameof(minAmount)} must have the same {nameof(maxAmount.CurrencyCode)}");
            var coin = maxAmount.Coin;
            if (! Coinage.Exists(coin.Iso4217Code)) throw new GameEngineException($"{nameof(maxAmount.CurrencyCode)} '{maxAmount.CurrencyCode}' is not valid");
            if (maxAmount.Value < 0) throw new GameEngineException($"{nameof(maxAmount)} must be greater or equal than 0");
            if (minAmount.Value < 0) throw new GameEngineException($"{nameof(minAmount)} must be greater or equal than 0");
            if (maxAmount.Value < minAmount.Value) throw new GameEngineException($"{nameof(minAmount)} cannot be greater than {nameof(maxAmount)}");
            if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

            AmountRange amountRange = null;
            amountRangesByCurrency.TryGetValue(coin, out amountRange);
            if (amountRange == null)
            {
                amountRange = new AmountRange(maxAmount, minAmount);
                amountRangesByCurrency.Add(coin, amountRange);
                AddEntryInLog(maxAmount, minAmount, now, employeeName, amountRange, true);
            }
            else
            {
                amountRange.UpdateRange(maxAmount, minAmount);
                AddEntryInLog(maxAmount, minAmount, now, employeeName, amountRange, false);
            }
        }

        internal bool IsInRange(Currency amount)
        {
            if (amount == null) throw new ArgumentNullException(nameof(amount));
            if (amount.Value < 0) throw new GameEngineException($"{nameof(amount)} must be greater or equal than 0");

            AmountRange amountRange = null;
            amountRangesByCurrency.TryGetValue(amount.Coin, out amountRange);
            return amountRange.IsInRange(amount);
        }

        private void AddEntryInLog(Currency maxAmount, Currency minAmount, DateTime now, string employeeName, AmountRange amountRange, bool isNewAmountRange)
        {
            if (isNewAmountRange)
            {
                RiskRatings.Log.AddEntry($"{employeeName} set min amount {minAmount.ToDisplayFormat()} and max amount {maxAmount.ToDisplayFormat()} for risk rating '{RiskRating.Name}' at {now.ToString("dd/MM/yyyy hh:mm tt")}");
            }
            else
            {
                string maxAmountString = string.Empty;
                if (maxAmount.Value != amountRange.MaxAmount.Value)
                {
                    maxAmountString = $"max amount from {amountRange.MaxAmount.ToDisplayFormat()} to {maxAmount.ToDisplayFormat()}";
                }

                string minAmountString = string.Empty;
                if (minAmount.Value != amountRange.MinAmount.Value)
                {
                    var separator = string.IsNullOrWhiteSpace(maxAmountString) ? string.Empty : " and ";
                    minAmountString = $"{separator}min amount from {amountRange.MinAmount.ToDisplayFormat()} to {minAmount.ToDisplayFormat()}";
                }
                RiskRatings.Log.AddEntry($"{employeeName} changed {maxAmountString}{minAmountString} for risk rating '{RiskRating.Name}' at {now.ToString("dd/MM/yyyy hh:mm tt")}");
            }
        }
    }

    [Puppet]
    internal class AmountRange : Objeto
    {
        internal string CurrencyCode 
        { 
            get 
            {
                return MaxAmount.CurrencyCode;
            } 
        }

        internal Currency MaxAmount { get; private set; }

        internal Currency MinAmount { get; private set; }

        internal AmountRange(Currency maxAmount, Currency minAmount)
        {
            if (maxAmount.CurrencyCode != minAmount.CurrencyCode) throw new GameEngineException($"{nameof(maxAmount)} and {nameof(minAmount)} must have the same {nameof(maxAmount.CurrencyCode)}");
            if (maxAmount.Value < 0) throw new GameEngineException($"{nameof(maxAmount)} must be greater or equal than 0");
            if (minAmount.Value < 0) throw new GameEngineException($"{nameof(minAmount)} must be greater or equal than 0");
            if (maxAmount.Value < minAmount.Value) throw new GameEngineException($"{nameof(minAmount)} cannot be greater than {nameof(maxAmount)}");

            MaxAmount = maxAmount;
            MinAmount = minAmount;
        }

        internal void UpdateRange(Currency maxAmount, Currency minAmount)
        {
            if (maxAmount.CurrencyCode != minAmount.CurrencyCode) throw new GameEngineException($"{nameof(maxAmount)} and {nameof(minAmount)} must have the same {nameof(maxAmount.CurrencyCode)}");
            if (maxAmount.Value < 0) throw new GameEngineException($"{nameof(maxAmount)} must be greater or equal than 0");
            if (minAmount.Value < 0) throw new GameEngineException($"{nameof(minAmount)} must be greater or equal than 0");
            if (maxAmount.Value < minAmount.Value) throw new GameEngineException($"{nameof(minAmount)} cannot be greater than {nameof(maxAmount)}");

            MaxAmount = maxAmount;
            MinAmount = minAmount;
        }

        internal bool IsInRange(Currency amount)
        {
            if (amount.Value < 0) throw new GameEngineException($"{nameof(amount)} must be greater or equal than 0");
            if (amount.CurrencyCode != MinAmount.CurrencyCode) throw new GameEngineException($"{nameof(amount)} to compare have a different {nameof(amount.CurrencyCode)}");

            var result = (amount.Value <= MaxAmount.Value) && (amount.Value >= MinAmount.Value);
            return result;
        }

        internal bool ExistsRange()
        {
            var result = (MaxAmount.Value > 0) && (MinAmount.Value >= 0);
            return result;
        }
    }

    internal class Log : Objeto
    {
        private readonly int maxCapacity;
        private readonly Queue<string> entries = new Queue<string>();

        public Log(int maxCapacity)
        {
            this.maxCapacity = maxCapacity;
        }

        internal IEnumerable<string> List()
        {
            return entries.ToList();
        }

        internal void AddEntry(string newEntry)
        {
            if (string.IsNullOrWhiteSpace(newEntry)) throw new ArgumentNullException(nameof(newEntry));

            if (entries.Count >= maxCapacity) entries.Dequeue();
            entries.Enqueue(newEntry);
        }
    }


    [DataContract(Name = "RiskRatingAndAmountRangeAssignationBody")]
    public class RiskRatingAndAmountRangeAssignationBody
    {
        [DataMember(Name = "userName")]
        public string UserName { get; set; }
        [DataMember(Name = "amountRangesByRiskRating")]
        public List<AmountRangesByRiskRatingBody> AmountRangesByRiskRating { get; set; }
    }


    [DataContract(Name = "AmountRangesByRiskRatingBody")]
    public class AmountRangesByRiskRatingBody
    {
        [DataMember(Name = "riskRating")]
        public string RiskRating { get; set; }
        [DataMember(Name = "amountRangesByCurrency")]
        public List<AmountRangeByCurrencyBody> AmountRangeByCurrencies { get; set; }
        [DataMember(Name = "transactionTypes")]
        public List<TransactionType> TransactionTypes { get; set; }
    }

    [DataContract(Name = "AmountRangeByCurrencyBody")]
    public class AmountRangeByCurrencyBody
    {
        [DataMember(Name = "currencyCode")]
        public string CurrencyCode { get; set; }
        [DataMember(Name = "maxAmount")]
        public decimal MaxAmount { get; set; }
        [DataMember(Name = "minAmount")]
        public decimal MinAmount { get; set; }
    }

    [DataContract(Name = "RiskRatingAndAmountRangeCopyBody")]
    public class RiskRatingAndAmountRangeCopyBody
    {
        [DataMember(Name = "fromUserName")]
        public string FromUserName { get; set; }
        [DataMember(Name = "toUserName")]
        public string ToUserName { get; set; }
    }
    struct RiskRatingsAndAgentExistence
    {
        public bool existsAllRiskRating { get; set; }
    }

}
