﻿using System;
using System.Collections.Generic;

namespace ExchangeAPI.ExchangeEntities
{
    public partial class Exchangerate
    {
        public Exchangerate()
        {
            Deposit = new HashSet<Deposit>();
            Profitabletransaction = new HashSet<Profitabletransaction>();
            Transfer = new HashSet<Transfer>();
            Withdrawal = new HashSet<Withdrawal>();
        }

        public long Id { get; set; }
        public DateTime Date { get; set; }
        public int Fromcurrencyid { get; set; }
        public int Tocurrencyid { get; set; }
        public decimal Purchaseprice { get; set; }
        public decimal Saleprice { get; set; }
        public long Whocreated { get; set; }
        public bool Deleted { get; set; }

        public virtual Currency Fromcurrency { get; set; }
        public virtual Currency Tocurrency { get; set; }
        public virtual Exchangeuser WhocreatedNavigation { get; set; }
        public virtual ICollection<Deposit> Deposit { get; set; }
        public virtual ICollection<Profitabletransaction> Profitabletransaction { get; set; }
        public virtual ICollection<Transfer> Transfer { get; set; }
        public virtual ICollection<Withdrawal> Withdrawal { get; set; }
    }
}
