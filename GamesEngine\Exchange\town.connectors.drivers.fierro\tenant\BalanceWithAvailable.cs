﻿using GamesEngine.Settings;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Linq;
using System.Threading.Tasks;
using BalancesResponse = GamesEngine.Finance.BalancesResponse;

namespace town.connectors.drivers.fiero
{
    internal class BalanceWithAvailable : FieroTenantDriver
	{
		public BalanceWithAvailable() : base(Tenant_Actions.Balance)
		{
		}

		public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			var result = await AvailableBalanceAsync(now, recordSet);

			return (T)Convert.ChangeType(result, typeof(T));
		}
		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			throw new NotImplementedException();
		}
		public override void Prepare(DateTime now)
		{
			CustomSettings.AddVariableParameter("httpContext");
			CustomSettings.AddVariableParameter("atAddress");
			CustomSettings.AddVariableParameter("currencyCode");
			CustomSettings.AddVariableParameter("actor");
		}

		private async Task<decimal> AvailableBalanceAsync(DateTime now, CustomSettings.RecordSet recordSet)
		{
			var HttpContext = recordSet.Mappings["httpContext"];
			var AtAddress = recordSet.Mappings["atAddress"];
			var CurrencyCode = recordSet.Mappings["currencyCode"];
			var Actor = recordSet.Mappings["actor"];

			HttpContext httpContext = HttpContext.As<HttpContext>();
			string atAddress = AtAddress.AsString;
			string currencyCode = CurrencyCode.AsString;
			RestAPISpawnerActor actor = Actor.As<RestAPISpawnerActor>();

			var result = await BalancesAsync(actor, httpContext, atAddress, currencyCode);
			return result;
		}

		private async Task<decimal> BalancesAsync(RestAPISpawnerActor actor, HttpContext context, string atAddress, string currencyCode)
		{
			var strCodes = new string[] {
				currencyCode
			};
			var strCurrencyCodes = string.Join(",", strCodes.Select(code => $"'{code}'").ToArray());

			var result = await actor.PerformQryAsync(atAddress, context, $@"
			{{
				accumulatedBalances = atAddress.GetAccumulatedBalances({{{strCurrencyCodes}}});
				if (! accumulatedBalances.IsEmpty)
				{{
                    balances = accumulatedBalances.Balances;
					for (balance : accumulatedBalances.Balances)
					{{
						currentBalance = balance;
						currencyCode = currentBalance.CurrencyCodeAsText;
						if (currentBalance.Available > 0) 
						{{
							print currencyCode code;
							print currentBalance.Locked locked;
							print currentBalance.Available balance;
							for (accounts : currentBalance.Accounts)
							{{
								account = accounts;
								print account.Number accountNumber;
								print currencyCode code;
								print account.Balance.Available available;
							}}
						}}
					}}
				}}
			}}
			");

			if (!(result is OkObjectResult))
			{
				ErrorsSender.Send($"atAddress:{atAddress} balance is failing.", $"Balance for atAddress:{atAddress} its not loading.");
				return 0;
			}
			
			string resultToString = ((OkObjectResult)result).Value.ToString();
			if (string.IsNullOrWhiteSpace(resultToString)) return 0;
			var temp = JsonConvert.DeserializeObject<BalancesResponse>(resultToString);
			return temp.Balances.First().Balance;
		}

	}
}
