﻿using GamesEngine.Finance;
using GamesEngine.PurchaseOrders;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GamesEngine.Exchange
{
	internal class CustomerAccounts
	{
		private List<CustomerAccount> accounts;
		private Dictionary<Coin,  CustomerAccount> _defaults;
		private readonly Customer customer;
		private readonly FavoriteAccounts favoriteAccounts;

		internal CustomerAccounts(Customer customer)
		{
			if (customer == null) throw new ArgumentNullException(nameof(customer));
			this.customer = customer;

			_defaults = new Dictionary<Coin, CustomerAccount>();
			_defaults.Add(Coinage.Coin(Currencies.CODES.USD), new CustomerAccount(this, customer, Coinage.Coin(Currencies.CODES.USD)));
            accounts = new List<CustomerAccount>();

			favoriteAccounts = new FavoriteAccounts(customer, _defaults.Values);
		}

		public CustomerAccount DefaultAccount(Coin currencyCode) 
		{
			CustomerAccount result = null;
			this._defaults.TryGetValue(currencyCode, out result);
			if (result == null) throw new GameEngineException("There is no default account number yet.");

			return result;
		}
		internal CustomerAccount CreateNewAccountFor(Coin currencyCode)
		{
			var account = new CustomerAccount(this, customer, currencyCode);

			accounts.Add(account);
			AddInternalAccountAsFavorite(account, account.Alias);
			return account;
		}
		internal CustomerAccount FindAccountByCurrency(Coin currencyCode)
		{
			return _defaults[currencyCode];
		}

		internal CustomerAccount FindAccountByNumber(string accountNumber)
		{
			foreach (CustomerAccount account in _defaults.Values.ToArray())
			{
				if (account.Identificator == accountNumber) return account;
			}
			foreach (CustomerAccount account in accounts)
			{
				if (account.Identificator == accountNumber) return account;
			}

			throw new GameEngineException($"There is no account for number {accountNumber}.");
		}

		internal bool Exists(string accountNumber)
		{
			foreach (CustomerAccount account in _defaults.Values.ToArray())
			{
				if (account.Identificator == accountNumber) return true;
			}
			foreach (CustomerAccount account in accounts)
			{
				if (account.Identificator == accountNumber) return true;
			}

			return false;
		}

		internal IEnumerable<CustomerAccount> FindAccountsByCurrency(Coin currencyCode)
		{
			List<CustomerAccount> result = new List<CustomerAccount>();
			CustomerAccount defaultAccount = null;
			this._defaults.TryGetValue(currencyCode, out defaultAccount);
			if (defaultAccount != null) 
			{ 
				result.Add(defaultAccount); 
			}
			foreach (CustomerAccount account in accounts)
			{
				if (account.Coin == currencyCode)
				{
					result.Add(account);
				}
			}

			return result;
		}

		internal bool ExistsAlias(string alias)
		{
			foreach (CustomerAccount account in _defaults.Values.ToArray())
			{
				if (account.Alias == alias) return true;
			}
			foreach (CustomerAccount account in accounts)
			{
				if (account.Alias == alias) return true;
			}

			return false;
		}

		internal void UpdateAlias(string accountNumber, string newAlias)
		{
			foreach (CustomerAccount account in _defaults.Values.ToArray())
			{
				if (account.Identificator == accountNumber) 
				{ 
					account.Alias = newAlias;
					return;
				}
			}
			foreach (CustomerAccount account in accounts)
			{
				if (account.Identificator == accountNumber)
				{
					account.Alias = newAlias;
					return;
				}
			}

			throw new GameEngineException($"There is no account for number {accountNumber}.");
		}

		internal IEnumerable<CustomerAccount> ListAccounts()//TODO Cambiar ese nombre, NO revelar el nombre de estructura de datos
		{
			List<CustomerAccount> result = new List<CustomerAccount>();
			result.AddRange(_defaults.Values.ToArray());
			result.AddRange(accounts);

			return result;
		}

		internal IEnumerable<CustomerAccount> DefaultAccounts()
		{
			return _defaults.Values.ToList();
		}

		internal void SetDefaultAccount(CustomerAccount account)
		{
			if (account == null) throw new ArgumentNullException(nameof(account));

			CustomerAccount defaultAccount = null;
			_defaults.TryGetValue(account.Coin, out defaultAccount);
			if (defaultAccount == null) throw new GameEngineException("There is no default account number yet.");
			if (defaultAccount == account) throw new GameEngineException($"{nameof(account)} '{account.Identificator}' is already a default account");

			_defaults[account.Coin] = account;
		}

		internal bool IsOneOfMyDefaultAccount(CustomerAccount account)
		{
			if (account == null) throw new ArgumentNullException(nameof(account));

			CustomerAccount defaultAccount = null;
			_defaults.TryGetValue(account.Coin, out defaultAccount);
			return defaultAccount == account;
		}

		internal IEnumerable<FavoriteAccount> GetFavoriteAccounts(int initialIndex, int amountOfElements)
		{
			var result = favoriteAccounts.GetFavoriteAccounts(initialIndex, amountOfElements);
			return result;
		}

		internal void AddInternalAccountAsFavorite(CustomerAccount account, string alias)
		{
			favoriteAccounts.Add(account, alias);
		}

		internal void AddExternalAccountAsFavorite(Coin currencyCode, string accountNumber, string customerIdentifier, string alias)
		{
			favoriteAccounts.Add(currencyCode, accountNumber, customerIdentifier, alias);
		}

		internal void RemoveInternalAccountAsFavorite(string accountNumber)
		{
			favoriteAccounts.RemoveInternalAccount(accountNumber);
		}

		internal void RemoveExternalAccountAsFavorite(string accountNumber)
		{
			favoriteAccounts.RemoveExternalAccount(accountNumber);
		}

		internal bool HasFavoriteInternalAccount(string accountNumber)
		{
			return favoriteAccounts.HasInternalAccount(accountNumber);
		}

		internal bool HasFavoriteExternalAccount(string accountNumber)
		{
			return favoriteAccounts.HasExternalAccount(accountNumber);
		}
	}

	internal class CustomerAccount : Objeto
	{
		private string alias;
		private string identificator;
		private readonly Customer customer;
		internal string CustomerAccountNumber { get { return customer.AccountNumber; } }
		internal string CustomerIdentifier { get { return customer.Identifier; } }

		private readonly Coin coin;

		internal CustomerAccount(CustomerAccounts accounts, Customer customer, Coin coin) 
			: this("_", accounts, customer, coin)
		{

		}
		internal CustomerAccount(string alias, CustomerAccounts accounts, Customer customer, Coin coin)
		{
			if (string.IsNullOrEmpty(alias)) throw new ArgumentNullException(nameof(alias));
			if ( customer == null) throw new ArgumentNullException(nameof(customer));

			this.coin = coin;
			this.alias = alias;
			this.customer = customer;
		}

		internal string CurrencyCode { get { return coin.Iso4217Code; } }
		internal Coin Coin { get { return coin; } }

		internal string CurrencyCodeAsText 
		{
			get 
			{ 
				return CurrencyCode.ToString();
			} 
		}

		internal string Identificator
		{
			get
			{
				if (string.IsNullOrEmpty(identificator)) throw new GameEngineException($"This account it's not registered in a marketplace yet.");

				return identificator;
			}
			set
			{
				if (string.IsNullOrEmpty(value)) throw new ArgumentNullException("Account identificator it's required.");
				if(!string.IsNullOrEmpty(identificator)) throw new GameEngineException("This account it's already registered.");

				identificator = value;
				alias = $"Account {identificator}";
			}
		}

		internal string Alias
		{
			get
			{
				return this.alias;
			}
			set
			{
				if (string.IsNullOrWhiteSpace(value)) throw new ArgumentNullException($"{nameof(alias)} it's required.");

				alias = value;
			}
		}
	}
}
