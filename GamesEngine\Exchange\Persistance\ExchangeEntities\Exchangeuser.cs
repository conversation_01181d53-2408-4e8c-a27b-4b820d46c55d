﻿using System;
using System.Collections.Generic;

namespace ExchangeAPI.ExchangeEntities
{
    public partial class Exchangeuser
    {
        public Exchangeuser()
        {
            CreditNoteWhoapprovedNavigation = new HashSet<CreditNote>();
            CreditNoteWhocreatedNavigation = new HashSet<CreditNote>();
            CreditNoteWhorejectedNavigation = new HashSet<CreditNote>();
            DebitNoteWhoapprovedNavigation = new HashSet<DebitNote>();
            DebitNoteWhocreatedNavigation = new HashSet<DebitNote>();
            DebitNoteWhorejectedNavigation = new HashSet<DebitNote>();
            DepositWhoapprovedNavigation = new HashSet<Deposit>();
            DepositWhocreatedNavigation = new HashSet<Deposit>();
            DepositWhorejectedNavigation = new HashSet<Deposit>();
            Exchangerate = new HashSet<Exchangerate>();
            Journalentry = new HashSet<Journalentry>();
            ProfitabletransactionWhoapprovedNavigation = new HashSet<Profitabletransaction>();
            ProfitabletransactionWhocreatedNavigation = new HashSet<Profitabletransaction>();
            TransferWhoapprovedNavigation = new HashSet<Transfer>();
            TransferWhocreatedNavigation = new HashSet<Transfer>();
            TransferWhorejectedNavigation = new HashSet<Transfer>();
            WithdrawalWhoapprovedNavigation = new HashSet<Withdrawal>();
            WithdrawalWhocreatedNavigation = new HashSet<Withdrawal>();
            WithdrawalWhorejectedNavigation = new HashSet<Withdrawal>();
        }

        public long Id { get; set; }
        public string Name { get; set; }

        public virtual ICollection<CreditNote> CreditNoteWhoapprovedNavigation { get; set; }
        public virtual ICollection<CreditNote> CreditNoteWhocreatedNavigation { get; set; }
        public virtual ICollection<CreditNote> CreditNoteWhorejectedNavigation { get; set; }
        public virtual ICollection<DebitNote> DebitNoteWhoapprovedNavigation { get; set; }
        public virtual ICollection<DebitNote> DebitNoteWhocreatedNavigation { get; set; }
        public virtual ICollection<DebitNote> DebitNoteWhorejectedNavigation { get; set; }
        public virtual ICollection<Deposit> DepositWhoapprovedNavigation { get; set; }
        public virtual ICollection<Deposit> DepositWhocreatedNavigation { get; set; }
        public virtual ICollection<Deposit> DepositWhorejectedNavigation { get; set; }
        public virtual ICollection<Exchangerate> Exchangerate { get; set; }
        public virtual ICollection<Journalentry> Journalentry { get; set; }
        public virtual ICollection<Profitabletransaction> ProfitabletransactionWhoapprovedNavigation { get; set; }
        public virtual ICollection<Profitabletransaction> ProfitabletransactionWhocreatedNavigation { get; set; }
        public virtual ICollection<Transfer> TransferWhoapprovedNavigation { get; set; }
        public virtual ICollection<Transfer> TransferWhocreatedNavigation { get; set; }
        public virtual ICollection<Transfer> TransferWhorejectedNavigation { get; set; }
        public virtual ICollection<Withdrawal> WithdrawalWhoapprovedNavigation { get; set; }
        public virtual ICollection<Withdrawal> WithdrawalWhocreatedNavigation { get; set; }
        public virtual ICollection<Withdrawal> WithdrawalWhorejectedNavigation { get; set; }
    }
}
