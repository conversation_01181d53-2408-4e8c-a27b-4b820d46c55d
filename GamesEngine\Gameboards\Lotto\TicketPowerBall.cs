﻿using GamesEngine.Bets;
using GamesEngine.Games.Lotto;
using System;
using System.Collections.Generic;
using System.Text;

namespace GamesEngine.Gameboards.Lotto
{
	internal abstract class TicketPowerBall : Ticket
	{
		private const int CRITERIA_HAS_NOT_BEEN_ASSIGNED = int.MinValue;
		private PowerBall number;
		private int prizeCriteria = CRITERIA_HAS_NOT_BEEN_ASSIGNED;

		internal override int Count => number.Count;

		internal TicketPowerBall(Player player, Lottery lottery, DateTime drawDate, string number1, string number2, string number3, string number4, string number5, string powerBall, DateTime creationDate, decimal ticketCost, Prizes prizes) :
			base(player, lottery, drawDate, Selection.MultipleInputSingleAmount, creationDate, ticketCost, prizes)
		{
			number = new PowerBall(number1, number2, number3, number4, number5, powerBall);
		}

		internal TicketPowerBall(Player player, Lottery lottery, DateTime drawDate, string numbers, DateTime creationDate, decimal ticketCost, Prizes prizes) :
			base(player, lottery, drawDate, Selection.MultipleInputSingleAmount, creationDate, ticketCost, prizes)
		{
			number = new PowerBall(numbers);
		}

		internal override string AsString()
		{
			StringBuilder result = new StringBuilder();
			result.Append(IdOfType().ToString());
			result.Append(number.AsString());
			return result.ToString();
		}

		internal string AsStringForAccounting()
		{
			return number.AsStringForAccounting();
		}

		internal override IEnumerable<SubTicket<IPick>> SubTickets()
		{
			return number.SubTickets();
		}

		internal override IEnumerable<SubTicket<IPick>> Permute()
		{
			throw new GameEngineException($"Powerball cannot be permuted");
		}

		internal override void LotteryDraw(LotteryDraw lotteryDraw)
		{
			if (!base.IsUnprized()) throw new GameEngineException($"Ticket should be in {GameboardStatus.UNPRIZED} status but it is at {Prizing}");
			if (lotteryDraw == null) throw new ArgumentNullException(nameof(lotteryDraw));
			string sequenceOfNumbers = lotteryDraw.SequenceOfNumbers;
			if (!IsValidSequenceOfNumbers(sequenceOfNumbers)) throw new GameEngineException($"Number '{sequenceOfNumbers}' is not a valid Lottery number");
			var winnerNumber = new PowerBall(sequenceOfNumbers);
			if (this is TicketPowerBallSingle)
			{
				GradeNumber(winnerNumber, 1);
			}
			else
			{
				var draw = (LotteryDrawPowerball)lotteryDraw;
				GradeNumber(winnerNumber, draw.MultiplierNumber);
			}
			base.Draw = lotteryDraw;
		}

		private void GradeNumber(PowerBall winnerNumber, int multiplierNumber)
		{
			if (winnerNumber.Count != 1) throw new GameEngineException($"Winner {winnerNumber.AsString()} must be a single number");

			this.prizeCriteria = CalculatePrizeCriteria(winnerNumber);
			if (this.prizeCriteria != 0)
			{
				base.GradeAsWinner();
			}
			else
			{
				base.GradeAsLoser();
			}

			if (this.prizeCriteria == PrizesPicks.RED_FIVE_WHITE_BALL && multiplierNumber > 1)
			{
				//TODO: Erick Verify which is the correct prize
				var grandPrize = this.prizeCriteria;
				payout = grandPrize;
				throw new NotImplementedException("El grand prize no ha sido definido, esta pendiente de validar");
			}
			else
			{
				payout = Math.Min(PrizesPicks.MAXIMUM_POWERBALL_PRIZE, this.prizeCriteria * multiplierNumber);
			}
        }

		protected int PrizeCriteria
		{
			get
			{
				if (this.prizeCriteria == CRITERIA_HAS_NOT_BEEN_ASSIGNED) throw new GameEngineException("This number has not been graded, therefore prize criteria does not exist yet");
				return this.prizeCriteria;
			}
		}

		protected int CalculatePrizeCriteria(PowerBall winnerNumber)
		{
			var criteria = 0;
			Prizes currPrizes = base.Prizes;
			int whiteMatches = number.WhiteMatches(winnerNumber);
			bool matchesRedBall = number.RedBall == winnerNumber.RedBall;
			switch (whiteMatches)
			{
				case 0:
					if (matchesRedBall)
					{
						criteria = currPrizes.Prize(TicketType.PBS, PrizesPicks.RED_BALL);
					}
					break;
				case 1:
					if (matchesRedBall)
					{
						criteria = currPrizes.Prize(TicketType.PBS, PrizesPicks.RED_ONE_WHITE_BALL);
					}
					break;
				case 2:
					if (matchesRedBall)
					{
						criteria = currPrizes.Prize(TicketType.PBS, PrizesPicks.RED_TWO_WHITE_BALL);
					}
					break;
				case 3:
					if (matchesRedBall)
					{
						criteria = currPrizes.Prize(TicketType.PBS, PrizesPicks.RED_THREE_WHITE_BALL);
					}
					else
					{
						criteria = currPrizes.Prize(TicketType.PBS, PrizesPicks.THREE_WHITE_BALL);
					}
					break;
				case 4:
					if (matchesRedBall)
					{
						criteria = currPrizes.Prize(TicketType.PBS, PrizesPicks.RED_FOUR_WHITE_BALL);
					}
					else
					{
						criteria = currPrizes.Prize(TicketType.PBS, PrizesPicks.FOUR_WHITE_BALL);
					}
					break;
				case 5:
					if (matchesRedBall)
					{
						criteria = currPrizes.Prize(TicketType.PBS, PrizesPicks.RED_FIVE_WHITE_BALL);
					}
					else
					{
						criteria = currPrizes.Prize(TicketType.PBS, PrizesPicks.FIVE_WHITE_BALL);
					}
					break;
			}
			return criteria;
		}

		internal override IEnumerable<TicketByPrize> TicketsByPrize()
		{
			var tickets = new TicketByPrize[] {
				new TicketByPrizePowerball(this, PrizesPicks.PRIZE_CRITERIA)
			};
			foreach (SubTicket<IPick> subTicket in SubTickets())
			{
				tickets[0].Add(subTicket);
			}
			return tickets;
		}

		internal override void RemoveWager(TicketWager wager)
		{
			if (CountWagers == 1) throw new GameEngineException("Cannot remove the last wager. Please remove the ticket.");

			Bet bet = this.Player.FindBet(this);
			foreach (var subticket in wager.Subtickets)
			{
				bet.Contribution -= BetAmount();
			}

			RemoveThisWager(wager);
		}

		internal override bool WasCreatedByPattern()
		{
			return false;
		}

		internal override bool NumbersFollowPattern()
		{
			return false;
		}

		internal override bool IsPowerball()
		{
			return true;
		}

		internal abstract decimal PowerplayCost();

		internal override decimal CalculatedPrize()
		{
			return Grade() * BetAmount() * CountOfWinners();
		}

		internal override string WinnerNumbers()
		{
			var result = ((LotteryDraw)Draw).SequenceOfNumbers;
			return result;
		}
	}

	internal class TicketByPrizePowerball : TicketByPrize
	{
		internal TicketByPrizePowerball(Ticket ticket, int prizeCriteria) : base(ticket, prizeCriteria)
		{

		}

		internal override string AsString()
		{
			var result = Ticket.AsString();
			return result;
		}

		internal override decimal TicketAmount()
		{
			var powerballTicket = (TicketPowerBall)Ticket;
			var amount = powerballTicket.BetAmount() * Count + powerballTicket.PowerplayCost();
			return amount;
		}

		internal override bool HasNumber(int[] numbers)
		{
			var result = Ticket.AsString() == $"{Ticket.IdOfType().ToString()}[{numbers[0]}{numbers[1]},{numbers[2]}{numbers[3]},{numbers[4]}{numbers[5]},{numbers[6]}{numbers[7]},{numbers[8]}{numbers[9]},{numbers[10]}{numbers[11]}]";
			return result;
		}

		internal override IEnumerable<TicketWager> Wagers()
		{
			return base.Wagers();
		}
	}
}
