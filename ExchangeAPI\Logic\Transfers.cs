﻿using ExchangeAPI.ExchangeEntities;
using ExchangeAPI.Model;
using ExchangeAPI.Service;
using GamesEngine;
using GamesEngine.Exchange.Persistance;
using GamesEngine.Settings;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ExchangeAPI.Logic
{
    public class Transfers: ITransfers
    {
        private const string ALL_SELECTED = "all";
        private const int MAXIMUM_AMOUNT_OF_ROWS_ALLOWED = 100;

        public static TransfersDTO FilteredBy(DateTime startDate, DateTime endDate, string state, string identificationNumber, string transactionId, string cashierName, string domain, 
            int initialIndex, int amountOfRows)
        {
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate.Hour != 0 || startDate.Minute != 0 || startDate.Second != 0) throw new GameEngineException($"{nameof(startDate)} {startDate} is not valid");
            if (endDate.Hour != 0 || endDate.Minute != 0 || endDate.Second != 0) throw new GameEngineException($"{nameof(endDate)} {endDate} is not valid");
            if (string.IsNullOrWhiteSpace(state)) throw new ArgumentNullException(nameof(state));
            if (string.IsNullOrWhiteSpace(identificationNumber)) throw new ArgumentNullException(nameof(identificationNumber));
            if (string.IsNullOrWhiteSpace(transactionId)) throw new ArgumentNullException(nameof(transactionId));
			if (string.IsNullOrWhiteSpace(cashierName)) throw new ArgumentNullException(nameof(cashierName));
            if (string.IsNullOrWhiteSpace(domain)) throw new ArgumentNullException(nameof(domain));
            if (initialIndex < 0) throw new GameEngineException($"{nameof(initialIndex)} {initialIndex} is not valid");
            if (amountOfRows <= 0) throw new GameEngineException($"{nameof(amountOfRows)} {amountOfRows} is not valid");

            if (amountOfRows > MAXIMUM_AMOUNT_OF_ROWS_ALLOWED)
            {
                amountOfRows = MAXIMUM_AMOUNT_OF_ROWS_ALLOWED;
            }

            var allStatesSelected = state.ToLower().Trim() == ALL_SELECTED;
            var allIdentificationNumberSelected = identificationNumber.ToLower().Trim() == ALL_SELECTED;
            var allTransactionIdsSelected = transactionId.ToLower().Trim() == ALL_SELECTED;
			var allCashiersSelected = cashierName.ToLower().Trim() == ALL_SELECTED;
            var allDomainsSelected = domain.ToLower().Trim() == ALL_SELECTED;

            using (var context = new ExchangeContext())
            {
                var transfers = context.Transfer
                    .Include(transfer => transfer.WhocreatedNavigation)
                    .Include(transfer => transfer.DomainNavigation)
                    .Include(transfer => transfer.Exchangerate)
                    .Include(transfer => transfer.Fromcurrency)
                    .Include(transfer => transfer.Tocurrency)
                    .Where(transfer => ! transfer.Deleted && transfer.Datecreated.Date >= startDate && transfer.Datecreated.Date <= endDate
                        && (allStatesSelected || (transfer.Approvals == transfer.Approvalsrequired && state == TransactionState.Approved.ToString()) || (transfer.Rejections > 0 && state == TransactionState.Rejected.ToString()) || (transfer.Approvals == 0 && transfer.Rejections == 0 && state == TransactionState.Pending.ToString()))
                        && (allIdentificationNumberSelected || transfer.Fromcustomer == identificationNumber)
                        && (allTransactionIdsSelected || transfer.Id.ToString() == transactionId)
						&& (allCashiersSelected || transfer.WhocreatedNavigation.Name == cashierName)
                        && (allDomainsSelected || transfer.DomainNavigation.Domain == domain)
                    )
                    .OrderByDescending(transfer => transfer.Datecreated);
                var transfersPaged = transfers
                    .Skip(initialIndex)
                    .Take(amountOfRows);
                var transfersDTO = transfersPaged.MapToTransfersDTO();
                transfersDTO.TotalRecords = transfers.Count();
                return transfersDTO;
            }
        }

		private static Transfer TransferFor(int id)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");

            using (var context = new ExchangeContext())
            {
                var transferFound = context.Transfer
                    .Include(transfer => transfer.Exchangerate)
                    .Where(transfer => ! transfer.Deleted && transfer.Id == id)
                    .FirstOrDefault();
                return transferFound;
            }
        }

        void ITransfers.Save(Transfer transfer)
        {
            if (transfer == null) throw new ArgumentNullException(nameof(transfer));

            using (var context = new ExchangeContext())
            {
                context.Transfer.Add(transfer);
                context.SaveChanges();
            }
        }

        public static Transfer UpdateAsApproved(ExchangeContext context, int id, DateTime date, decimal profit, decimal purchase, decimal sale, string employeeName, int processorId)
        {
            if (context == null) throw new ArgumentNullException(nameof(context));
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");

            var user = Integration.ExchangeUsers.User(employeeName);
            var transfer = TransferFor(id);
            if (transfer == null) throw new ArgumentNullException(nameof(transfer));

            transfer.Approvals = 1;
            transfer.Whoapproved = user.Id;
            transfer.Dateapproved = date;
            transfer.Net = purchase;
            transfer.Profit = profit;
            transfer.Amount = sale;
            transfer.Processorid = processorId;
            context.Transfer.Update(transfer);
            context.SaveChanges();
            return transfer;
        }

        public static Transfer UpdateJournalEntry(ExchangeContext context, int id, string journalEntryDetailId)
        {
            if (context == null) throw new ArgumentNullException(nameof(context));
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(journalEntryDetailId)) throw new ArgumentNullException(nameof(journalEntryDetailId));

            var transfer = TransferFor(id);
            if (transfer == null) throw new ArgumentNullException(nameof(transfer));

            transfer.Journalentrydetailid = journalEntryDetailId;
            context.Transfer.Update(transfer);
            context.SaveChanges();
            return transfer;
        }

        public static Transfer UpdateAsRejected(int id, DateTime date, string rejectionReason, string employeeName)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");

            var user = Integration.ExchangeUsers.User(employeeName);
            var transfer = TransferFor(id);
            if (transfer == null) throw new ArgumentNullException(nameof(transfer));

            transfer.Rejections = 1;
            transfer.Whorejected = user.Id;
            transfer.Daterejected = date;
            transfer.Rejectionreason = rejectionReason;
            using (var context = new ExchangeContext())
            {
                context.Transfer.Update(transfer);
                context.SaveChanges();
            }
            return transfer;
        }

        public static Transfer UpdateVoucherUrl(int id, string voucherUrl)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(voucherUrl)) throw new ArgumentNullException(nameof(voucherUrl));

            var transfer = TransferFor(id);
            if (transfer == null) throw new ArgumentNullException(nameof(transfer));

            transfer.Voucherurl = voucherUrl;
            using (var context = new ExchangeContext())
            {
                context.Transfer.Update(transfer);
                context.SaveChanges();
            }
            return transfer;
        }
    }
}
