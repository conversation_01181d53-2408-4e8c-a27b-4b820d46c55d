﻿using GamesEngine.Finance;
using GamesEngine.Settings;
using Nest;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Exchange.Batch
{
	[Puppet]
	internal abstract class BatchSet : Objeto
	{
		internal enum BatchStatus { READY, OPEN, CLOSE, DONE };
		private BatchTransactions batchTransactions;
		protected List<AgentBatch> agents;
		private readonly Marketplace marketplace;
		private readonly DateTime creationDate;
		protected const char AGENT_SEPARATOR = '/';
		protected int consecutive;
		protected readonly InBalances inTransaction;
		protected readonly OutBalances outTransaction;
		private BatchStatus status;
		private int batchNumber;
		private DateTime? openDate;
		private DateTime? closeDate;
		private DateTime? verifyDate;
		private SetOfBalances initialAccount;

		internal BatchSet(Marketplace marketplace, DateTime creationDate)
		{
			if (marketplace == null) throw new ArgumentNullException(nameof(marketplace));
			if (creationDate == default(DateTime)) throw new ArgumentNullException(nameof(creationDate));

			this.batchNumber = 0;
			this.marketplace = marketplace;
			this.creationDate = creationDate;
			this.inTransaction = new InBalances(batchNumber, initialAccount);
			this.outTransaction = new OutBalances(batchNumber, this, initialAccount);
			this.status = BatchStatus.READY;
		}

		protected bool InitialAccountItsNotConfiguredFor()
		{
			return initialAccount == null;
		}

		internal bool InitialAccountItsNotConfiguredFor(Coin code)
		{

			return initialAccount == null || initialAccount.ItsConfiguredNotFor(code);
		}

		protected void LoadNewInitialAccounts(SetOfBalances accounts)
		{
			if (accounts == null) throw new ArgumentNullException(nameof(accounts));
			initialAccount = new SetOfBalances(accounts);
			inTransaction.InitialAccount = initialAccount;
			outTransaction.InitialAccount = initialAccount;
		}

		internal SetOfBalances InitialAccount
		{
			get
			{
				return initialAccount;
			}
		}

		internal AgentBatch AddAgent(string name, DateTime creationDate)
		{
			if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
			if (agents == null) agents = new List<AgentBatch>();
			if (agents.Any(x => x.Name == name)) throw new GameEngineException($"Can not create agent {name} because an agent with the same name already exist.");

			AgentBatch agent = new AgentBatch(this, name, creationDate);
			if (agent.agents == null) agent.agents = new List<AgentBatch>();

			agents.Add(agent);
			return agent;
		}

		internal bool ExistsAgent(string name)
		{
			if (string.IsNullOrWhiteSpace(name)) return false;
			if (agents == null) return false;
			if (agents.Any(x => x.Name == name)) return true;

			return false;
		}

		abstract internal void ReceiveAmount(Currency amount, Currency cost, string employeeName);

		protected BatchTransactions CreateNewBatchTransaction(int batchNumber, DateTime now)
		{
			batchTransactions = new BatchTransactions(this.marketplace, batchNumber, this, now, outTransaction, inTransaction);
			return batchTransactions;
		}

		internal string OutAccountFor(Coin currencyCode)
		{
			return initialAccount.OutAccountFor(currencyCode);
		}

		internal bool CanAssignFunds(Currency amount)
		{
			var result = this.outTransaction.CanAssignFunds(amount);
			return result;
		}

		internal void UnAssignFunds(string agentName, int batchNumber, Currency amount, string employeeName, DateTime now)
		{
			if (string.IsNullOrWhiteSpace(agentName)) throw new ArgumentNullException(nameof(agentName));
			if (batchNumber <= 0) throw new GameEngineException("Invalid batch number");
			if (amount == null) throw new ArgumentNullException(nameof(amount));
			if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));
			if (now == default(DateTime)) throw new ArgumentNullException(nameof(now));

			//TODO la idea es que los lotes por agente se creen solo cuando yo soy un Final agent y no tengo mas lotes Open.
			if (batchTransactions == null) throw new GameEngineException("Funds cannot be deducted, initial deposit does not exist.");

			AgentBatch child = agents.FirstOrDefault(x => x.Name == agentName);
			if (child == null) throw new GameEngineException($"Agent {agentName} does not exist");

			BatchTransactions batchTransaction = child.BatchTransactions;
			if (batchTransaction == null) throw new GameEngineException($"Batch number {batchNumber} does not exist");
			
			//Quitarle a este child ese amount, chequear que el child tenga ese amount y restarlo
			//Asignarselo a a mi cuenta
			//validar que el batch de este child NO este Opened...  es decir que este Ready
			if (child.Available(amount.Coin).Value < amount.Value) throw new GameEngineException($"There is no enough available in {amount.GetType()}.  You can not unassign a greater amount than available.");
			if (!child.IsFinalAgent && !batchTransaction.IsReady) throw new GameEngineException($"Can not assign funds to {agentName} because is an internal agent or batch status is not {BatchStatus.READY.ToString()}");

			batchTransaction.DebitInitialAmount(amount, employeeName, now);
			batchTransaction.Withdraw(amount, employeeName, now);
			batchTransaction.UnLock(amount, employeeName, now);

			child.outTransaction.DebitInitialAmount(amount, employeeName);
			child.outTransaction.WithDraw(amount, employeeName);
			child.outTransaction.UnLock(amount, employeeName);

			outTransaction.Deposit(amount, employeeName);
		}

		internal abstract void ApplyBalancesModifications(BatchMovements draftMovements);

		internal Currency Available(Coin currencyCode)
		{
			Currency total = Currency.Factory(currencyCode.Iso4217Code, 0);
			if (IsInternalAgent)
			{
				foreach (var agent in agents)
				{
					total.Add( agent.Available(currencyCode) );
				}
			}
			else
			{
				if (batchTransactions != null)
				{
					total.Add(batchTransactions.Available(currencyCode) );
				}
			}
			return total;
		}

		internal BatchAccount SaleAccount(string currencyCodeAsText)
		{
			return SaleAccount(Coinage.Coin(currencyCodeAsText));
		}

		internal BatchAccount SaleAccount(Coin currencyCode)
		{
			BatchAccount result = outTransaction.FindAccountByCurrency(currencyCode);
			return result;
		}

		internal IEnumerable<BatchAccount> SelfSaleAccounts()
		{
			List<BatchAccount> result = new List<BatchAccount>();
			result.AddRange(outTransaction.Accounts());
			return result;
		}

		internal bool HasSaleAmountInAnyCurrency
		{
			get
			{
				return this.outTransaction.HasAvailableAmountInAnyCurrency;
			}
		}

		internal IEnumerable<BatchAccount> SelfReceptionAccounts()
		{
			List<BatchAccount> result = new List<BatchAccount>();
			result.AddRange(inTransaction.Accounts());
			return result;
		}

		internal BatchAccount SelfReceptionAccounts(Coin currencyCode)
		{
			return inTransaction.FindAccountByCurrency(currencyCode);
		}

		internal IEnumerable<BatchAccount> SaleAccounts()
		{
			List<BatchAccount> result = new List<BatchAccount>();
			if (IsInternalAgent)
			{
				result.AddRange(outTransaction.Accounts());
			}
			else
			{
				if (batchTransactions != null)
				{
					result.AddRange(batchTransactions.SaleAccounts());
				}
			}
			return result;
		}

		internal BatchAccount ReceptionAccount(string currencyCodeAsText)
		{
			BatchAccount result = inTransaction.FindAccountByCurrency(Coinage.Coin(currencyCodeAsText));
			return result;
		}

		internal IEnumerable<BatchAccount> ReceptionAccounts()
		{
			List<BatchAccount> result = new List<BatchAccount>();
			if (IsInternalAgent)
			{
				result.AddRange(inTransaction.Accounts());
			}
			else
			{
				if (batchTransactions != null)
				{
					result.AddRange(batchTransactions.ReceptionAccounts());
				}
			}
			return result;
		}

		internal string TailOFFullName(string fullName)
		{
			int indexSeparator = fullName.IndexOf(AGENT_SEPARATOR);
			string tail = fullName.Substring(indexSeparator + 1);
			return tail;
		}

		internal bool ExistAgentBatch(string fullName)
		{
			if (string.IsNullOrWhiteSpace(fullName)) throw new ArgumentNullException(nameof(fullName));

			if (this.agents == null) this.agents = new List<AgentBatch>();
			int indexSeparator = fullName.IndexOf(AGENT_SEPARATOR);
			string agentName;
			if (indexSeparator < 0)
			{
				agentName = fullName;

				foreach (AgentBatch agent in agents)
				{
					if (agent.Name == agentName)
					{
						return true;
					}
				}
				return false;
			}

			agentName = fullName.Substring(0, indexSeparator);
			
			foreach (AgentBatch agent in agents)
			{
				if (agent.Name == agentName)
				{
					string tail = fullName.Substring(indexSeparator + 1);
					return agent.ExistAgentBatch(tail);
				}
			}
			return false;
		}

		internal AgentBatch SearchBatchTransaction(string fullName)
		{
			if (string.IsNullOrWhiteSpace(fullName)) throw new ArgumentNullException(nameof(fullName));

			int indexSeparator = fullName.IndexOf(AGENT_SEPARATOR);
			string agentName;
			if (indexSeparator < 0)
			{
				agentName = fullName;
	
				foreach (AgentBatch agent in agents)
				{
					if (agent.Name == agentName)
					{
						return (AgentBatch)agent;
					}
				}
				throw new GameEngineException($"Agent {fullName} is unknown");
			}

			agentName = fullName.Substring(0, indexSeparator);
			foreach (AgentBatch agent in agents)
			{
				if (agent.Name == agentName)
				{
					string tail = fullName.Substring(indexSeparator + 1);
					return agent.SearchBatchTransaction(tail);
				}
			}
			throw new GameEngineException($"Agent {fullName} is unknown");
		}

		internal bool HasUnClosedTransaction()
		{
			if (batchTransactions != null && !batchTransactions.ItsClose)
			{
				 return true;
			}

			if (agents != null && agents.Count > 0)
			{
				foreach (BatchSet batchset in agents)
				{
					if (batchset.HasUnClosedTransaction()) return true;
				}
			}

			return false;
		}

		internal bool HasUnVerifiedChildren()
		{
			if (batchTransactions != null && !batchTransactions.ItsDone)
			{
				return true;
			}

			if (agents != null && agents.Count > 0)
			{
				foreach (BatchSet batchset in agents)
				{
					if (batchset.HasUnVerifiedChildren()) return true;
				}
			}

			return false;
		}

		internal BatchTransactions BatchTransactions
		{
			get
			{
				return batchTransactions;
			}
		
		}

		internal Currency TotalAssigned(string currencyCodeAsText)
		{
			Currency total = Currency.Factory(currencyCodeAsText, 0);
			if (IsInternalAgent)
			{
				foreach (var agent in agents)
				{
					total.Add(agent.outTransaction.Initial(Coinage.Coin(currencyCodeAsText)));
				}
			}
			return total;
		}

		internal Currency TotalAvailable(string currencyCodeAsText)
		{
			Currency total = Currency.Factory(currencyCodeAsText, 0);
			if (IsInternalAgent)
			{
				foreach (var agent in agents)
				{
					total.Add(agent.inTransaction.Available(Coinage.Coin(currencyCodeAsText)));
				}
			}
			return total;
		}

		private void AllAgencies(List<BatchSet> currentAgents)
		{
			currentAgents.Add(this);
		}

		internal IEnumerable<BatchSet> AllAgencies()
		{
			List<BatchSet> currentAgents = new List<BatchSet>();
			foreach (AgentBatch node in agents)
			{
				node.AllAgencies(currentAgents);
			}

			return currentAgents;
		}

		internal IEnumerable<BatchSet> AllAgents()
		{
			List<BatchSet> currentAgents = new List<BatchSet>();
			foreach (AgentBatch node in agents)
			{
				currentAgents.Add(node);
			}

			return currentAgents;
		}

		private void AllValidPaths(List<BatchSet> current)
		{
			current.Add(this);
			if (agents == null) agents = new List<AgentBatch>();
			foreach (AgentBatch node in agents)
			{
				current.Add(node);
			}
		}

		internal IEnumerable<BatchSet> AllValidPaths()
		{
			List<BatchSet> current = new List<BatchSet>();
			if (agents == null) agents = new List<AgentBatch>();
			foreach (AgentBatch node in agents)
			{
				node.AllValidPaths(current);
			}

			return current;
		}

		internal IEnumerable<AgentBatch> Agents
		{
			get
			{
				if (agents == null) agents = new List<AgentBatch>();
				return agents;
			}
		}

		internal bool HasAgents()
		{
			if (agents == null) agents = new List<AgentBatch>();
			return agents.Count > 0;
		}

		internal abstract void Open(bool itIsThePresent, DateTime openedDate, string employee);

		internal abstract void Close(bool itIsThePresent, DateTime closeDate, string employee);

		internal abstract void Verify(bool itIsThePresent, DateTime closeDate, string employee);

		protected void ApplyInBalancesModifications(BatchMovement movement)
		{
			if (movement is UnProfitApprovalMovement)
			{
			}
			else if (movement is UnProfitRejectionMovement)
			{
			}
			else if (movement is BatchUnProfitMovement)
			{
			}
			else if (movement is BatchAccreditMovement)
			{
				inTransaction.Accredit(movement.Amount);
			}
			else if (movement is BatchLockMovement)
			{
				inTransaction.Lock(movement.Amount);
			}
			else if (movement is BatchUnLockMovement)
			{
				inTransaction.Unlock(movement.Amount);
			}
			else if (movement is BatchDebitMovement)
			{
				inTransaction.Debit(movement.Amount);
			}
			else
			{
				throw new GameEngineException($"There is no valid implementation for {movement.GetType().Name} in {nameof(ApplyBalancesModifications)}.");
			}
		}

		protected void ApplyOutBalancesModifications(BatchMovement movement)
		{
			if ( movement is AccumulateInSpendAccumulatedMovement)
			{
				outTransaction.AddInSpendAcculumated(movement.Amount);
			}
			else if (movement is AccumulateInLockAccumulatedMovement)
			{
				outTransaction.AddInLockAccumulated(movement.Amount);
			}
			else if (movement is SubstractFromLockAccumulatedMovement)
			{
				outTransaction.SubtractFromLockAccumulated(movement.Amount);
			}
			else
			{
				throw new GameEngineException($"There is no valid implementation for {movement.GetType().Name} in {nameof(ApplyBalancesModifications)}.");
			}
		}

		protected void OpenCurrentBatchSet(bool itIsThePresent, DateTime date)
		{
			if (this.status == BatchStatus.OPEN || this.status == BatchStatus.CLOSE) throw new GameEngineException($"You could not open a batch that is already opened or closed");

			this.status = BatchStatus.OPEN;
			this.openDate = date;
		}

		protected void CloseCurrentBatchSet(bool itIsThePresent, DateTime date)
		{
			if (this.status == BatchStatus.READY) throw new GameEngineException($"You could not close a batch that has not been previously opened");
			if (this.status == BatchStatus.READY || this.status == BatchStatus.CLOSE) throw new GameEngineException($"You could not close a batch that is already closed");
			if (batchTransactions != null && batchTransactions.HasPendingDraftTransactions()) throw new GameEngineException("There are draft transactions pending.");

			if(batchTransactions != null) batchTransactions.DropTemporaryTransactionsIfExists();

			foreach (BatchSet batchset in agents)
			{
				if (batchset.HasUnClosedTransaction()) throw new GameEngineException("There are children with unclosed transactions.");
			}

			this.status = BatchStatus.CLOSE;
			this.closeDate = date;
		}

		protected void VerifyCurrentBatchSet(bool itIsThePresent, DateTime date)
		{
			if (this.status != BatchStatus.CLOSE) throw new GameEngineException($"You could not verify a batch that has not been previously closed");
			if (batchTransactions != null && batchTransactions.HasPendingTransactions()) throw new GameEngineException("There are draft transactions pending.");

			foreach (BatchSet batchset in agents)
			{
				if (batchset.HasUnVerifiedChildren()) throw new GameEngineException("There are children with unverified transactions.");
			}

			this.status = BatchStatus.DONE;
			this.verifyDate = date;

			using (KafkaMessagesBuffer bufferForAll = new KafkaMessagesBuffer(itIsThePresent, Integration.Kafka.TopicForBalances))
			{
				BalanceMessage inMessage = inTransaction.CreateMessage(date);
				BalanceMessage outMessage = outTransaction.CreateMessage(date);
				bufferForAll.Send(inMessage);
				bufferForAll.Send(outMessage);
			}
		}

		internal bool HasClosedDate()
		{
			bool result = this.closeDate.HasValue;
			return result;
		}

		internal Marketplace Marketplace
		{
			get
			{
				return this.marketplace;
			}
		}

		internal bool IsInternalAgent
		{
			get
			{
				return agents.Count > 0;
			}
		}

		internal bool IsFinalAgent
		{
			get
			{
				return agents.Count == 0;
			}
		}

		internal int Consecutive
		{
			get
			{
				return this.consecutive;
			}
		}

		internal abstract string FullName { get; }

		internal abstract int Level { get; set; }

		internal BatchStatus Status
		{
			get
			{
				return status;
			}
		}

		internal bool ItsOpen
		{
			get
			{
				return this.status == BatchStatus.OPEN;
			}
		}

		internal bool IsClosed
		{
			get
			{
				return this.status == BatchStatus.CLOSE;
			}
		}

		internal DateTime OpenDate
		{
			get
			{
				return (DateTime)this.openDate;
			}
		}

		internal DateTime CloseDate
		{
			get
			{
				return (DateTime)this.closeDate;
			}
		}

		internal DateTime VerifyDate
		{
			get
			{
				return (DateTime)this.verifyDate;
			}
		}
	}
}
