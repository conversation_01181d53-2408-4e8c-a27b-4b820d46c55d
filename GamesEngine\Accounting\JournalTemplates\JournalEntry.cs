﻿using GamesEngine.Settings;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Accounting.JournalTemplates
{
	internal class JournalEntry
	{
		private readonly string id;
		internal string Id
		{
			get
			{
				return id;
			}
		}

		private readonly DateTime date;
		internal DateTime Date
        {
			get
            {
				return date;
            }
        }

		private readonly string reference;
		internal string Reference
		{
			get
			{
				return reference;
			}
		}

		private readonly string title;
		internal string Title
		{
			get
			{
				return title;
			}
		}

		private readonly AccountingMovement [] journalRows;
		internal IEnumerable<AccountingMovement> Rows
		{
			get
			{
				return journalRows.ToList();
			}
		}

		internal const string SYSTEM_ID_EXCHANGE = "Fiero";


		internal JournalEntry(string id, DateTime date, string reference, string title, AccountingMovement[] journalRows)
		{
			if (string.IsNullOrWhiteSpace(id)) throw new ArgumentNullException(nameof(id));
			if (date == default(DateTime)) throw new ArgumentNullException(nameof(date));
			if (string.IsNullOrWhiteSpace(reference)) throw new ArgumentNullException(nameof(reference));
			if (journalRows == null) throw new ArgumentNullException(nameof(journalRows));

			this.date = date;
			this.journalRows = journalRows;
			this.id = id;
			this.reference = reference;
			this.title = title;
			decimal debits = 0;
			decimal credits = 0;
			foreach (var row in journalRows)
            {
				if (row.IsCredit) 
				{ 
					credits += row.Credit; 
				} 
				else 
				{ 
					debits += row.Debit; 
				}
            }
			if (credits + debits != 0m) throw new GameEngineException($"Journal Entry is not balanced");
		}

	}

}
