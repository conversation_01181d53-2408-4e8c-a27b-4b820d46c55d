﻿using GamesEngine.Logs;
using GamesEngine.MessageQueuing;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Domains
{
    [Puppet]
    internal class DomainsActivation:Ob<PERSON><PERSON>, ILogs
    {
        internal const byte MAX_LOG_CAPACITY = 5;
        private readonly Logs.Logs logs = new Logs.Logs();
        private readonly Dictionary<Domain,bool> enabledDomains = new Dictionary<Domain, bool>();
        private readonly byte maxLogCapacity;

        internal DomainsActivation():this(MAX_LOG_CAPACITY)
        {
        }

        internal DomainsActivation(byte maxLogCapacity)
        {
            this.maxLogCapacity = maxLogCapacity;
        }

        internal void IncludeDomain(Domain domain, bool enabled = true)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (enabledDomains.ContainsKey(domain)) throw new GameEngineException($"Domain {domain.Url} was already included");

            enabledDomains.Add(domain, enabled);
            logs.CreateLog(domain.Id.ToString(), maxLogCapacity);
        }

        internal bool IsEnabled(Domain domain)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            bool result;
            if (! enabledDomains.TryGetValue(domain, out result)) return false;
            return result;
        }

        internal void EnableDomain(Domain domain)
        {
            bool avoidResendingALoopMessageForDomain = false;
            EnableDomain(avoidResendingALoopMessageForDomain, domain);
        }

        internal void EnableDomain(bool itIsThePresent, Domain domain)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (!enabledDomains.ContainsKey(domain)) throw new GameEngineException($"Domain {domain.Url} is unknown. Use {nameof(IncludeDomain)} first");
            if (enabledDomains.TryGetValue(domain, out bool currentStatus) && currentStatus) throw new GameEngineException($"Domain {domain} is already enabled. Check the status before change it.");

            enabledDomains[domain] = true;
            if (itIsThePresent && (Integration.UseKafka || Integration.UseKafkaForAuto)) Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForCatalog, new DomainOnOffMessage(domain.Id, true));
        }

        internal void DisableDomain(Domain domain)
        {
            bool avoidResendingALoopMessageForDomain = false;
            DisableDomain(avoidResendingALoopMessageForDomain, domain);
        }

        internal void DisableDomain(bool itIsThePresent, Domain domain)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (!enabledDomains.ContainsKey(domain)) throw new GameEngineException($"Domain {domain.Url} is unknown. Use {nameof(IncludeDomain)} first");
            if (enabledDomains.TryGetValue(domain, out bool currentStatus) && !currentStatus) throw new GameEngineException($"Domain {domain} is already disabled. Check the status before change it.");

            enabledDomains[domain] = false;
            if (itIsThePresent && (Integration.UseKafka || Integration.UseKafkaForAuto)) Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForCatalog, new DomainOnOffMessage(domain.Id, false));
        }

        internal bool Contains(Domain domain)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            var result = enabledDomains.Keys.Contains(domain);
            return result;
        }

        internal IEnumerable<Domain> IncludedDomains => enabledDomains.Keys.ToList();

        internal void CopyActivationsFrom(DomainsActivation domainsActivation)
        {
            if (domainsActivation == null) throw new ArgumentNullException(nameof(domainsActivation));

            foreach (var activation in domainsActivation.enabledDomains)
            {
                var domain = activation.Key;
                this.enabledDomains.Add(domain, activation.Value);
                logs.CreateLog(domain.Id.ToString(), maxLogCapacity);
            }
        }

        private Domain DomainFrom(int id)
        {
            var domain = enabledDomains.Keys.SingleOrDefault(domain => domain.Id == id);
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            return domain;
        }

        internal bool AnyEnabledDomain()
        {
            var result = enabledDomains.Any(kv => kv.Value == true);
            return result;
        }

        public void AddAnnotation(string id, string message, string who, DateTime now)
        {
            if (string.IsNullOrEmpty(message)) throw new ArgumentNullException(nameof(message));
            if (string.IsNullOrEmpty(who)) throw new ArgumentNullException(nameof(who));

            if (!int.TryParse(id, out int domainId)) throw new GameEngineException($"{nameof(id)} is not an integer");
            var domain = DomainFrom(domainId);
            var log = logs.SearchLog(id);
            log.AddEntry(now, who, $"{who} set as {message} for {domain.Url} at {now.ToString("dd/MM/yyyy hh:mm tt")}");
        }

        internal Log Log(string id)
        {
            if (string.IsNullOrEmpty(id)) throw new ArgumentNullException(nameof(id));

            var log = logs.SearchLog(id);
            return log;
        }
    }
}
