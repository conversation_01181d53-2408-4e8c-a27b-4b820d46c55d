﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.IO;
using System.Runtime.Serialization;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using GamesEngine;
using GamesEngine.Finance;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using MySql.Data.MySqlClient;
using Newtonsoft.Json;
using Puppeteer.EventSourcing;

namespace CashierAPI
{
	public static class CashierAPI
	{
		public static RestAPISpawnerActor Cashier;

		public static void Initialize(MockLoader mockLoader)
		{
			string dbSelected = Settings.DbSelected;
			string connectionString = null;
			string scriptBeforeRecovering = null;

			if (dbSelected == DatabaseType.MySQL.ToString())
			{
				connectionString = Settings.MySQL;
				scriptBeforeRecovering = Settings.ScriptBeforeRecovering;
			}
			else if (dbSelected == DatabaseType.SQLServer.ToString())
			{
				connectionString = Settings.SqlServer;
				scriptBeforeRecovering = Settings.ScriptBeforeRecovering;
			}
			else if (dbSelected == DatabaseType.IN_MEMORY.ToString())
			{
				connectionString = Settings.IN_MEMORY;
				scriptBeforeRecovering = Settings.ScriptBeforeRecovering;
			}
			else if (String.IsNullOrWhiteSpace(dbSelected))
			{
				var DOTNET_RUNNING_IN_CONTAINER = bool.Parse(System.Environment.GetEnvironmentVariable("DOTNET_RUNNING_IN_CONTAINER") ?? "false");
				if (DOTNET_RUNNING_IN_CONTAINER)
                {
					connectionString = Settings.MySQL;
					scriptBeforeRecovering = Settings.ScriptBeforeRecovering;
				}
                else
                {
					const string mock = "Mocks";
					connectionString = mock;
					scriptBeforeRecovering = mock;
				}
			}
			else
			{
				throw new Exception($" dbSelected: {dbSelected} its not valid.");
			}

			Cashier = new RestAPISpawnerActor(mockLoader, dbSelected, connectionString, scriptBeforeRecovering);
			Cashier.LoadGeneralActor();
		}

	}
}
