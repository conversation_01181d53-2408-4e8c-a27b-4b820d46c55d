﻿using GamesEngine.Accounting.JournalTemplates;
using GamesEngine.Business;
using GamesEngine.Finance;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using System;
using town.connectors.drivers;
using static GamesEngine.Finance.PaymentChannels;

namespace GamesEngine.Exchange
{
	internal class TransferTransaction : Transaction
	{
		internal const int TEMPLATE_ID = 2;

		private int authorizationId;
		private CustomerAccount toAccount;
		internal TransferTransaction(TransactionDefinition transactionDefinition, bool itsThePresent, IConversionSpread conversionSpread, CustomerAccount toAccount, int authorizationId, string employeeName, Currency amount, DateTime now)
	: base(transactionDefinition, conversionSpread, amount, now, TransactionType.Transfer)
		{
			this.authorizationId = authorizationId;
			this.toAccount = toAccount;
			CustomerAccount fromAccount = TransactionDefinition.Account;

			transactionDefinition.Batch.AddTransactionDenifition(this);

			string description = $"Transfer {amount.CurrencyCode} {amount.Value} from {fromAccount.Identificator} to {toAccount.Identificator}";
			SendFragmentsCreationMessage(itsThePresent, description, amount.Value, fromAccount.CustomerAccountNumber, amount.Coin, authorizationId, amount.Value, Agents.INSIDER, now);
        }

		public static JournalEntryTemplate Template { get; private set; }
		internal static void InitializeTemplate(JournalEntryTemplates templates)
		{
			Template = templates.CreateTemplate(TransferTransaction.TEMPLATE_ID, "Template for " + nameof(TransferTransaction));
			var parameters = Template.Parameters;

			parameters.AddTextParameter("TransactionId");
			parameters.AddTextParameter("AuthorizationId");
			parameters.AddTextParameter("Date");
			parameters.AddTextParameter("FromCustomerNumber");
			parameters.AddTextParameter("FromAccountNumber");
			parameters.AddTextParameter("ToCustomerNumber");
			parameters.AddTextParameter("ToAccountNumber");
			parameters.AddTextParameter("Currency");
			parameters.AddAmountParameter("SaleRate");
			parameters.AddAmountParameter("PurchaseRate");
			parameters.AddAmountParameter("CurrencyCost");
			parameters.AddAmountParameter("TotalAmount");

			Template.AddDebit("100-01", "Dummy Swap Transaction {TransactionId}", "TotalAmount");
			Template.AddCredit("200-01", "Dummy Swap Transaction {TransactionId}", "TotalAmount");
		}

		protected override void AfterApprove(DateTime date, bool itsThePresent, Currency gross, Currency comission, Currency profit, Currency net, Currency amountToCustomer, string employeeName, TransactionCompleted transactionCompleted, int journalEntryNumber)
		{
			if (Integration.UseKafka || Integration.UseKafkaForAuto)
			{
				if (!itsThePresent) return;

				string RealAccount = toAccount.CustomerAccountNumber;
				string thirdPartyOutAccount = TransactionDefinition.OutAccountFor(gross.Coin);

				CustomerAccount fromAccount = TransactionDefinition.Account;
				var isTheSameCurrencyCode = gross.CurrencyCode == toAccount.CurrencyCode;
				string concept = isTheSameCurrencyCode ?
					$"Transfer {amountToCustomer.CurrencyCode} {amountToCustomer.Value} from {fromAccount.Identificator} to {toAccount.Identificator}" :
					$"Transfer {gross.CurrencyCode} {gross.Value} from {fromAccount.Identificator} converted to {amountToCustomer.CurrencyCode} {amountToCustomer.Value} in {toAccount.Identificator}";

				SendFragmentPaymentMessage(
					itsThePresent, 
					date, 
					gross.CurrencyCode.ToString(), 
					fromAccount.CustomerAccountNumber, 
					authorizationId, 
					employeeName, 
					WagerStatus.L, 
					concept,
					net,
					RealAccount,
					thirdPartyOutAccount,
					Type,
					Status);
				const int NoProcessorAccountId = 0;
				SendDepositMessage(
					itsThePresent,
					toAccount.CurrencyCode.ToString(),
					toAccount.CustomerAccountNumber,
					amountToCustomer.Value,
					concept,
					this.Id,
					employeeName,
					toAccount.Identificator,
					NoProcessorAccountId,
					PaymentChannels.Agents.INSIDER
					);

				if (base.JournalEntryTemplate.IsActive)//TODO: no specific template for this transaction type
				{
					var currencyCost = TransactionDefinition.Batch.CalculateCost(amountToCustomer);
					var saleRate = ConversionSpread is NoConversionSpread ? 0m : ConversionSpread.SaleRate.Price;
					var purchaseRate = ConversionSpread is NoConversionSpread ? 0m : ConversionSpread.PurchaseRate.Price;
					Integration.Kafka.Send(
						itsThePresent,
						$"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForTransacctions}",
						new JournalEntryForTransferMessage(
							base.Id,
							date,
							journalEntryNumber,
							employeeName,
							saleRate,
							purchaseRate,
							authorizationId,
							fromAccount.CustomerAccountNumber,
							fromAccount.Identificator,
							toAccount.CustomerAccountNumber,
							toAccount.Identificator,
							toAccount.Coin,
							amountToCustomer.Value,
							0m,
							currencyCost.Value
						)
					);
				}

				Integration.Kafka.Send(
                    itsThePresent,
                    $"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForTransacctions}",
                    new ApprovedTransactionMessage(TransactionType.Transfer, Id, date, profit.Value, net.Value, amountToCustomer.Value, gross.Coin, amountToCustomer.Coin, employeeName, authorizationId, NoProcessorAccountId)
                );

				SendProfitNotification(date, itsThePresent, fromAccount, profit);
			}
        }

		private void SendProfitNotification(DateTime date, bool itIsThePresent, CustomerAccount account, Currency profit)
        {
			if (profit.Value > 0)
            {
				var player = TransactionDefinition.FindOwner().Player;
				Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForCustomers,
					new CustomerSaleMessage(account.CustomerIdentifier, 
					account.CustomerAccountNumber, 
					player.Company.Sales.CurrentStore.Id, 
					date, 
					profit,
					(int)player.Agent));
			}
		}

		internal override void AfterDeny(DateTime date, bool itsThePresent, string employeeName, string reason)
		{
            TransactionDefinition.Deny(this);

            if (!itsThePresent) return;

			Currency net = Currency.Factory(TransactionDefinition.Account.CurrencyCode, 0);
			string RealAccount = "";
			string thirdPartyOutAccount = TransactionDefinition.OutAccountFor(TransactionDefinition.Account.Coin);

			CustomerAccount fromAccount = TransactionDefinition.Account;
           SendFragmentPaymentMessage(
				itsThePresent, 
				date, 
				fromAccount.CurrencyCode.ToString(), 
				fromAccount.CustomerAccountNumber, 
				authorizationId, employeeName,
				WagerStatus.X, 
				reason,
				net,
				RealAccount,
				thirdPartyOutAccount,
				Type,
				Status);

			if (Integration.UseKafka || Integration.UseKafkaForAuto)
            {
                Integration.Kafka.Send(
                    itsThePresent,
					$"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForTransacctions}",
                    new DeniedTransactionMessage(TransactionType.Transfer, Id, date, reason, employeeName)
                );
            }
        }

		internal override bool isSameCurrencyTransaction()
		{
			return TransactionDefinition.Account.CurrencyCode == Amount.CurrencyCode && Amount.CurrencyCode == toAccount.CurrencyCode;
		}

		protected sealed override int TemplateNumber
		{
			get
			{
				return TEMPLATE_ID;
			}
		}

	}

	public class JournalEntryForTransferMessage : JournalEntryMessage
	{
		public int AuthorizationId { get; private set; }
		public string FromCustomerNumber { get; private set; }
		public string FromAccountNumber { get; private set; }
		public string ToCustomerNumber { get; private set; }
		public string ToAccountNumber { get; private set; }
		public Coin CurrencyCode { get; private set; }
		public decimal TotalAmount { get; private set; }
		public decimal Fee { get; private set; }
		public decimal CurrencyCost { get; private set; }

		public JournalEntryForTransferMessage(int transactionId, DateTime date, int journalEntryId, string employeeName, decimal saleRate, decimal purchaseRate, int authorizationId, 
			string fromCustomerNumber, string fromAccountNumber, string toCustomerNumber, string toAccountNumber, Coin currencyCode, decimal totalAmount, decimal fee, decimal currencyCost) :
			base(TransactionType.Transfer, transactionId, date, journalEntryId, employeeName, saleRate, purchaseRate)
		{
			this.AuthorizationId = authorizationId;
			this.FromCustomerNumber = fromCustomerNumber;
			this.FromAccountNumber = fromAccountNumber;
			this.ToCustomerNumber = toCustomerNumber;
			this.ToAccountNumber = toAccountNumber;
			this.CurrencyCode = currencyCode;
			this.TotalAmount = totalAmount;
			this.Fee = fee;
			CurrencyCost = currencyCost;
		}

		public JournalEntryForTransferMessage(string serialized) : base(serialized)
		{

		}

		protected override void InternalSerialize()
		{
			base.InternalSerialize();
			AddProperty(AuthorizationId).
			AddProperty(FromCustomerNumber).
			AddProperty(FromAccountNumber).
			AddProperty(ToCustomerNumber).
			AddProperty(ToAccountNumber).
			AddProperty(CurrencyCode.Iso4217Code).
			AddProperty(TotalAmount).
			AddProperty(Fee).
			AddProperty(CurrencyCost);
		}

		protected override void Deserialize(string [] message, out int fieldOrder)
		{
			base.Deserialize(message, out fieldOrder);
			AuthorizationId = int.Parse(message[fieldOrder++]);
			FromCustomerNumber = message[fieldOrder++];
			FromAccountNumber = message[fieldOrder++];
			ToCustomerNumber = message[fieldOrder++];
			ToAccountNumber = message[fieldOrder++];
			CurrencyCode = Coinage.KafkaProperty2Coin(message[fieldOrder++]);
			TotalAmount = decimal.Parse(message[fieldOrder++]);
			Fee = decimal.Parse(message[fieldOrder++]);
			CurrencyCost = decimal.Parse(message[fieldOrder++]);
		}
	}

}
