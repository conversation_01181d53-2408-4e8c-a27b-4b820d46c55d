﻿using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using static GamesEngine.Finance.Currencies;

namespace GamesEngine.Finance
{
	[Puppet]
	[DataContract(Name = "movementsReportResponse")]
	public class MovementsReport:Objeto
	{
		private readonly List<MovementRecord> movementRecords = new List<MovementRecord>();
		[DataMember(Name = "transactions")]
		internal IEnumerable<MovementRecord> MovementRecords
		{
			get
			{
                var requiresFilter = amountOfRows != 0;
                if (requiresFilter)
                {
                    var result = movementRecords.OrderBy(m => m.DocumentNumber).Skip(initialIndex).Take(amountOfRows).ToList();
                    return result;
                }
                return movementRecords;
			}
		}

        internal IEnumerable<MovementDetailRecord> MovementDetails
        {
            get
            {
                var movementDetails = new List<MovementDetailRecord>();
                foreach (var movementRecord in movementRecords)
                {
                    movementDetails.AddRange(movementRecord.MovementDetails);
                }
                lastTotalCountForDetailRecords = movementDetails.Count;

                var requiresFilter = amountOfRows != 0;
                if (requiresFilter)
                {
                    var result = movementDetails.OrderBy(m => m.DocumentNumber).Skip(initialIndex).Take(amountOfRows).ToList();
                    return result;
                }
                return movementDetails;
            }
        }

        internal int Count
		{
			get
			{
				return movementRecords.Count;
			}
		}

        private int lastTotalCountForDetailRecords;
        internal int LastTotalCountForDetailRecords
        {
            get
            {
                return lastTotalCountForDetailRecords;
            }
        }

        private readonly int countFoundRows;
		[DataMember(Name = "recordsTotal")]
		internal int CountFoundRows
		{
			get
			{
				return countFoundRows;
			}
		}

		private bool foundAtAddress;
		[DataMember(Name = "foundAtAddress")]
		public bool FoundAtAddress
		{
			get
			{
				return foundAtAddress;
			}
			set
			{
				foundAtAddress = value;
			}
		}

        private readonly int initialIndex;
        private readonly int amountOfRows;

        public MovementsReport()
		{

		}

		internal MovementsReport(List<MovementRecord> movementRecords, int countFoundRows)
		{
			if (movementRecords == null) throw new ArgumentNullException(nameof(movementRecords));
			if (countFoundRows < 0) throw new GameEngineException($"{nameof(countFoundRows)} must be greater or equal than 0");

			this.movementRecords = movementRecords;
			this.countFoundRows = countFoundRows;
			foundAtAddress = true;
		}

        internal MovementsReport(Coin currencyCode, List<FragmentInfo> fragmentsInfo, int countFoundRows, int initialIndex, int amountOfRows)
        {
            if (fragmentsInfo == null) throw new ArgumentNullException(nameof(fragmentsInfo));
            if (countFoundRows < 0) throw new GameEngineException($"{nameof(countFoundRows)} must be greater or equal than 0");
            if (initialIndex < 0) throw new GameEngineException($"{nameof(initialIndex)} must be greater or equal than 0");
            if (amountOfRows <= 0) throw new GameEngineException($"{nameof(amountOfRows)} must be greater than 0");

            this.countFoundRows = countFoundRows;
            this.initialIndex = initialIndex;
            this.amountOfRows = amountOfRows;
            string currentDocumentNumber = string.Empty;
            MovementRecord currentMovementRecord = null;
            foreach (var fragmentInfo in fragmentsInfo)
            {
                if (currentDocumentNumber != fragmentInfo.DocumentNumber)
                {
                    currentDocumentNumber = fragmentInfo.DocumentNumber;
                    currentMovementRecord = new MovementRecord(currencyCode, fragmentInfo);
                    this.movementRecords.Add(currentMovementRecord);
                    if (fragmentInfo.IsPending && !fragmentInfo.IsCompletelyPending)
                    {
                        currentMovementRecord.AddPendingDetailFor(fragmentInfo.AtAddress, fragmentInfo.DocumentNumberAsInt);
                    }
                }

                var authorizationNumber = int.Parse(currentDocumentNumber);
                if (fragmentInfo.IsCompletelyPending)
                {
                    currentMovementRecord.AddPendingDetailFor(fragmentInfo.AtAddress, authorizationNumber);
                }
                else
                {
                    currentMovementRecord.AddDetail(new MovementDetailRecord(currencyCode, currentMovementRecord, fragmentInfo));
                }
            }

            foundAtAddress = true;
        }
    }

	[Puppet]
	internal class MovementDetailReport : Objeto
	{
		private List<MovementDetailRecord> movementRecords;
		internal IEnumerable<MovementDetailRecord> MovementRecords
		{
			get
			{
				var movementRecordsOrdered = movementRecords.OrderBy(m => m.Number);
				if (movementRecordsOrdered.Count() <= amountOfRows)
				{
					return movementRecordsOrdered.ToList();
				}
				else
				{
					return movementRecordsOrdered.Skip(initialIndex).Take(amountOfRows).ToList();
				}
			}
		}

		internal int Count
		{
			get
			{
				return movementRecords.Count;
			}
		}

		private readonly int countFoundRows;
		internal int CountFoundRows
		{
			get
			{
				return countFoundRows;
			}
		}

		internal Coin Coin
		{
			get; private set;
		}
		internal string CurrencyCode
		{
			get
			{
				return Coin.Iso4217Code;
			}
		}

		private readonly int initialIndex;
		private readonly int amountOfRows;
		internal MovementDetailReport(Coin coin, List<MovementDetailRecord> movementRecords, int countFoundRows, AtAddress atAddress, int authorizationNumber, int initialIndex, int amountOfRows)
		{
			if (movementRecords == null) throw new ArgumentNullException(nameof(movementRecords));
			if (countFoundRows < 0) throw new GameEngineException($"{nameof(countFoundRows)} must be greater or equal than 0");
			if (atAddress == null) throw new ArgumentNullException(nameof(atAddress));
			if (authorizationNumber <= 0) throw new GameEngineException($"{nameof(authorizationNumber)} must be greater than 0");
			if (initialIndex < 0) throw new GameEngineException($"{nameof(initialIndex)} must be greater or equal than 0");
			if (amountOfRows <= 0) throw new GameEngineException($"{nameof(amountOfRows)} must be greater than 0");

			Coin = coin;
			this.movementRecords = movementRecords;
			this.initialIndex = initialIndex;
			this.amountOfRows = amountOfRows;

			Authorization authorization = atAddress.GetAuthorization(authorizationNumber);
			var pendingFragments = authorization.PendingFragments();
			pendingFragments.Concat(authorization.PayedFragments());
			
			int countPendingFragments = 0;
            foreach (var pendingFragment in pendingFragments)
            {
                countPendingFragments++;
				var detailRecordToAdd = new MovementDetailRecord(Coin, pendingFragment);
				if (movementRecords.Where(x=>x.Number == detailRecordToAdd.Number).Count() == 0)
					movementRecords.Add(detailRecordToAdd);
            }
			
			this.countFoundRows = countFoundRows + countPendingFragments;
		}

	}

	[Puppet]
	[DataContract(Name = "movementRecord")]
	internal class MovementRecord: Objeto
	{
		private readonly string atAddress;
		[DataMember(Name = "atAddress")]
		internal string AtAddress
		{
			get
			{
				return atAddress;
			}
		}

		private readonly string ticketNumber;
		[DataMember(Name = "ticketNumber")]
		internal string TicketNumber
		{
			get
			{
				return ticketNumber;
			}
		}

        internal string DocumentNumber => ticketNumber;


        private readonly DateTime creationDate;
		internal DateTime CreationDate
		{
			get
			{
				return creationDate;
			}
		}

		[DataMember(Name = "creationDate")]
		internal string CreationDateAsString
		{
			get
			{
				var date = creationDate.ToString("MM/dd/yyyy HH:mm:ss");
				return date;
			}
		}

		private readonly bool isPending;
		[DataMember(Name = "description")]
		internal string Description
		{
			get
			{
				if (MovementType== Movement.type.Credit || !isPending) return "Transaction approved";
				return "Transaction drafted";
			}
		}

        internal string TransactionDescription
        {
            get
            {
                var onlyOneWager = movementDetailRecords.Count == 1;
                if (onlyOneWager)
                {
                    var firstDetailRecord = movementDetailRecords.ElementAt(0);
                    if (firstDetailRecord.IsReason(FragmentReason.Winner))
                    {
                        return "Transaction Approved";
                    }
                }
                
                return "Transaction(s) Placed";
            }
        }

        private readonly decimal totalAmount;
		[DataMember(Name = "totalAmount")]
		internal decimal TotalAmount
		{
			get
			{
				return totalAmount;
			}
		}

		internal string TotalAmountFormatted
		{
			get
			{
				return Currency.Factory(currencyCode, totalAmount).ToDisplayFormat();
			}
		}

		private readonly decimal availableBalance;
		[DataMember(Name = "availableBalance")]
		internal decimal AvailableBalance
		{
			get
			{
				return availableBalance;
			}
		}

		internal string AvailableBalanceFormatted
		{
			get
			{
				return Currency.Factory(currencyCode, availableBalance).ToDisplayFormat();
			}
		}

		private List<MovementDetailRecord> movementDetailRecords = new List<MovementDetailRecord>();
        internal IEnumerable<MovementDetailRecord> MovementDetails
        {
            get
            {
                return movementDetailRecords;
            }
        }

		private readonly string currencyCode;
		internal string CurrencyCode
		{
			get
			{
				return currencyCode;
			}
		}
		private readonly Coin coin;
		internal Coin Coin
		{
			get
			{
				return coin;
			}
		}

		internal Movement.type MovementType { get; }
		internal int ProcessorId { get; }
		internal string Reference { get; }

		internal MovementRecord(Coin currencyCode, string atAddress, string documentNumber, DateTime creationDate, bool isPending, decimal totalAmount, decimal availableBalance, Movement.type type, int processorId, string reference)
		{
			if (string.IsNullOrWhiteSpace(atAddress)) throw new ArgumentNullException(nameof(atAddress));
			if (string.IsNullOrWhiteSpace(documentNumber)) throw new ArgumentNullException(nameof(documentNumber));
			if (creationDate == default(DateTime)) throw new ArgumentNullException(nameof(creationDate));
			if (totalAmount < 0) throw new GameEngineException($"{nameof(totalAmount)} must be greater or equal than 0");
			if (availableBalance < 0) throw new GameEngineException($"{nameof(availableBalance)} must be greater or equal than 0");
			if (processorId < 0) throw new GameEngineException($"{nameof(processorId)} must be greater or equal than 0");

			this.coin = currencyCode;
			this.currencyCode = currencyCode.Iso4217Code;
			this.atAddress = atAddress;
			this.ticketNumber = documentNumber;
			this.creationDate = creationDate;
			this.isPending = isPending;
			this.totalAmount = totalAmount;
			this.availableBalance = availableBalance;
			MovementType = type;
			ProcessorId = processorId;
			Reference = reference;
		}

        internal MovementRecord(Coin currencyCode, FragmentInfo fragmentInfo):
            this(currencyCode, fragmentInfo.AtAddress.Number, fragmentInfo.DocumentNumber, fragmentInfo.CreationDate, fragmentInfo.IsPending, fragmentInfo.TotalAmount, fragmentInfo.NewBalance, Movement.type.Lock, 0, "0")
        {
        }

        internal void AddDetail(MovementDetailRecord movementDetailRecord)
        {
            if (movementDetailRecord == null) throw new ArgumentNullException(nameof(movementDetailRecord));

            movementDetailRecords.Add(movementDetailRecord);
        }

        internal void AddPendingDetailFor(AtAddress atAddress, int authorizationNumber)
        {
            if (atAddress == null) throw new ArgumentNullException(nameof(atAddress));
            if (authorizationNumber <= 0) throw new GameEngineException($"{nameof(authorizationNumber)} must be greater than 0");

            Authorization authorization = atAddress.GetAuthorization(authorizationNumber);
            var pendingFragments = authorization.PendingFragments();
            pendingFragments.Concat(authorization.PayedFragments());

            int countPendingFragments = 0;
            foreach (var pendingFragment in pendingFragments)
            {
                countPendingFragments++;
                var detailRecordToAdd = new MovementDetailRecord(this.Coin, this, pendingFragment);
                movementDetailRecords.Add(detailRecordToAdd);
            }
        }
    }

	[Puppet]
	internal class MovementDetailRecord : Objeto
	{
        private readonly int documentNumber;
        internal int DocumentNumber
        {
            get
            {
                return documentNumber;
            }
        }

        private readonly int number;
		internal int Number
		{
			get
			{
				return number;
			}
		}

        internal string FullNumber
        {
            get
            {
                return $"{documentNumber}-{number}";
            }
        }

        private readonly decimal risk;
		internal decimal Risk
		{
			get
			{
				return risk;
			}
		}

		internal string RiskFormatted
		{
			get
			{
				return Currency.Factory(CurrencyCode, risk).ToDisplayFormat();
			}
		}

		private readonly decimal toWin;
		internal decimal ToWin
		{
			get
			{
				return toWin;
			}
		}

		internal string ToWinFormatted
		{
			get
			{
				return Currency.Factory(CurrencyCode, toWin).ToDisplayFormat();
			}
		}

		internal decimal Paid
		{
			get
			{
				if (IsReason(FragmentReason.Winner))
				{
					if (AdjustedWinAmount != 0)
					{
						var paid = AdjustedWinAmount + Risk;
						return paid;
					}
					else
					{
						var paid = ToWin + Risk;
						return paid;
					}
				}
				else if (IsReason(FragmentReason.NoAction))
				{
					return Risk;
				}
				else
				{
					return 0;
				}
			}
		}

		internal string PaidFormatted
		{
			get
			{
				return Currency.Factory(CurrencyCode, Paid).ToDisplayFormat();
			}
		}

		private readonly decimal adjustedWinAmount;
		internal decimal AdjustedWinAmount
		{
			get
			{
				return adjustedWinAmount;
			}
		}

		internal string AdjustedWinAmountFormatted
		{
			get
			{
				return Currency.Factory(CurrencyCode, adjustedWinAmount).ToDisplayFormat();
			}
		}

		private readonly decimal adjustedLossAmount;
		internal decimal AdjustedLossAmount
		{
			get
			{
				return adjustedLossAmount;
			}
		}

		internal string AdjustedLossAmountFormatted
		{
			get
			{
				return Currency.Factory(CurrencyCode, adjustedLossAmount).ToDisplayFormat();
			}
		}

		private readonly string description;
		internal string Description
		{
			get
			{
				return description;
			}
		}

		private readonly FragmentReason reason;
		internal string ReasonAsString
		{
			get
			{
				switch (reason)
				{
					case FragmentReason.Pending:
						return "Pending";
					case FragmentReason.Loser:
						return "Approved";
					case FragmentReason.Winner:
						return "Approved";
					case FragmentReason.NoAction:
						return "Rejected";
					default:
						throw new GameEngineException($"There is no case for {nameof(reason)} {reason}");
				}
			}
		}

		internal bool IsReason(FragmentReason reason)
		{
			return this.reason == reason;
		}

        private readonly MovementRecord movementRecord;
        internal MovementRecord MovementRecord
        {
            get
            {
                return movementRecord;
            }
        }
		internal Coin Coin
		{
			get; private set;
		}
		internal string CurrencyCode
		{
			get
			{
				return Coin.Iso4217Code;
			}
		}

		internal MovementDetailRecord(Coin coin, int documentNumber, int number, decimal risk, decimal toWin, string description, FragmentReason reason, decimal adjustedWinAmount, decimal adjustedLossAmount)
		{
            if (documentNumber <= 0) throw new GameEngineException($"{nameof(documentNumber)} must be greater than 0");
            if (number <= 0) throw new GameEngineException($"{nameof(number)} must be greater than 0");
			if (risk < 0) throw new GameEngineException($"{nameof(risk)} must be greater than 0");
			if (toWin <= 0) throw new GameEngineException($"{nameof(toWin)} must be greater than 0");
			if (string.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));
            if (adjustedWinAmount < 0) throw new GameEngineException($"{nameof(adjustedWinAmount)} must be greater or equal than 0");
            if (adjustedLossAmount < 0) throw new GameEngineException($"{nameof(adjustedLossAmount)} must be greater or equal than 0");

			Coin = coin;
			this.documentNumber = documentNumber;
            this.number = number;
			this.risk = risk;
			this.toWin = toWin;
			this.description = description;
			this.reason = reason;
			this.adjustedWinAmount = adjustedWinAmount;
			this.adjustedLossAmount = adjustedLossAmount;
		}

        internal MovementDetailRecord(Coin coin, AuthorizationFragment fragment) :
            this(coin, fragment.Authorization.Number, fragment.Number, fragment.Risk, fragment.ToWin, fragment.Description, fragment.Reason, fragment.AdjustedWinAmount, fragment.AdjustedLossAmount)
        {
            if (fragment == null) throw new ArgumentNullException(nameof(fragment));
        }

        internal MovementDetailRecord(Coin coin, MovementRecord movementRecord, AuthorizationFragment fragment):
            this(coin, fragment.Authorization.Number, fragment.Number, fragment.Risk, fragment.ToWin, fragment.Description, fragment.Reason, fragment.AdjustedWinAmount, fragment.AdjustedLossAmount)
		{
			if (fragment==null) throw new ArgumentNullException(nameof(fragment));
            if (movementRecord == null) throw new ArgumentNullException(nameof(movementRecord));

            this.movementRecord = movementRecord;
        }

        internal MovementDetailRecord(Coin coin, MovementRecord movementRecord, FragmentInfo fragmentInfo) : 
            this(coin, fragmentInfo.DocumentNumberAsInt, fragmentInfo.Number.Value, fragmentInfo.Risk.Value, fragmentInfo.ToWin.Value, fragmentInfo.Description, fragmentInfo.Reason, fragmentInfo.AdjustedWinAmount.Value, fragmentInfo.AdjustedLossAmount.Value)
        {
            this.movementRecord = movementRecord;
        }
    }
}
