﻿using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Text;

namespace GamesEngine.Customers
{
    public enum AttributeType
    {
        Checked,
        Disabled,
        <PERSON>,
        <PERSON><PERSON><PERSON>,
        <PERSON>,
        <PERSON>,
        Pattern,
        Placeholder,
        Readonly,
        Required,
        <PERSON>ze,
        Value
    }

    [Puppet]
    internal abstract class Attribute:Objeto
    {
        internal AttributeType Type { get; }

        public Attribute(AttributeType type)
        {
            Type = type;
        }

        internal string TypeAsString()
        {
            return Type.ToString().ToLower();
        }

        internal abstract string GetValue();
    }

    internal sealed class BooleanAttribute : Attribute
    {
        private readonly bool value;

        public BooleanAttribute(bool value, AttributeType type) : base(type)
        {
            if (type != AttributeType.Checked && type != AttributeType.Disabled && type != AttributeType.Multiple && type != AttributeType.Readonly && type != AttributeType.Required) throw new GameEngineException($"{nameof(type)} {type} is not a {nameof(BooleanAttribute)}");

            this.value = value;
        }

        internal override string GetValue()
        {
            return value.ToString();
        }
    }

    internal sealed class IntegerAttribute : Attribute
    {
        private readonly int value;

        public IntegerAttribute(int value, AttributeType type) : base(type)
        {
            if (value <= 0) throw new GameEngineException($"{nameof(value)} must be greater than 0");
            if ( type != AttributeType.MaxLength && type != AttributeType.Size) throw new GameEngineException($"{nameof(type)} {type} is not a {nameof(IntegerAttribute)}");

            this.value = value;
        }

        internal override string GetValue()
        {
            return value.ToString();
        }
    }

    internal sealed class StringAttribute : Attribute
    {
        private readonly string value;

        public StringAttribute(string value, AttributeType type) : base(type)
        {
            if (string.IsNullOrWhiteSpace(value)) throw new ArgumentNullException(nameof(value));
            if (type != AttributeType.Max && type != AttributeType.Min && type != AttributeType.Pattern && type != AttributeType.Placeholder && type != AttributeType.Value) throw new GameEngineException($"{nameof(type)} {type} is not a {nameof(StringAttribute)}");

            this.value = value;
        }

        internal override string GetValue()
        {
            return value;
        }
    }
}
