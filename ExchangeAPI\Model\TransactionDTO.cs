﻿using GamesEngine.Finance;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Threading.Tasks;

namespace ExchangeAPI.Model
{
	[DataContract(Name = "TransactionDTO")]
	public class TransactionDTO
    {
		public DateTime Date { get; set; }
		[DataMember(Name = "date")]
        public string DateAsString 
		{ 
			get
			{
				return Date.ToString("MM/dd/yyyy HH:mm:ss");
			} 
		}
        [DataMember(Name = "description")]
        public string Description { get; set; }
        [DataMember(Name = "identificationNumber")]
        public string IdentificationNumber { get; set; }
        [DataMember(Name = "transactionId")]
        public string TransactionId { get; set; }

		[DataMember(Name = "state")]
		public string StateAsString
		{
			get
			{
				return State.ToString();
			}
		}
		internal TransactionState State
		{
			get
			{
				if (Approvals == ApprovalsRequired) return TransactionState.Approved;
				if (Rejections == ApprovalsRequired) return TransactionState.Rejected;
				return TransactionState.Pending;
			}
		}
		public int Approvals { get; set; }
		public int ApprovalsRequired { get; set; }
		public int Rejections { get; set; }

		[DataMember(Name = "voucherUrl")]
		public string VoucherUrl { get; set; }
		[DataMember(Name = "whoCreated")]
		public string WhoCreated { get; set; }
	}

	
}
