﻿using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Customers
{
    internal sealed class SortedField : Objeto
    {
        internal int Position { get; }

        internal Field Field { get; }

        public SortedField(int position, Field field)
        {
            if (position <= 0) throw new GameEngineException($"{nameof(position)} must be greater than 0");
            if (field == null) throw new ArgumentNullException(nameof(field));

            Position = position;
            Field = field;
        }
    }

    internal sealed class SortedFields
    {
        private readonly Dictionary<string, SortedField> fields = new Dictionary<string, SortedField>();

        internal IEnumerable<SortedField> GetAll
        {
            get
            {
                return fields.Values.ToList();
            }
        }

        internal int Count => fields.Count;

        internal void Clear()
        {
            fields.Clear();
        }

        internal bool Exists(int position)
        {
            foreach (var field in fields.Values)
            {
                if (field.Position == position) return true;
            }
            return false;
        }

        internal bool ContainsKey(string key)
        {
            return fields.ContainsKey(key);
        }

        internal void Add(SortedField field)
        {
            if (field == null) throw new ArgumentNullException(nameof(field));

            fields.Add(field.Field.Id, field);
        }

        internal bool IsVerifiable(string key)
        {
            if (! ContainsKey(key)) return false;

            var result = fields[key].Field.Verifiable;
            return result;
        }

        internal bool IsUnique(string key)
        {
            if (!ContainsKey(key)) return false;

            var result = fields[key].Field.Unique;
            return result;
        }

        internal bool AnyUniqueField()
        {
            foreach (var field in fields.Values)
            {
                if (field.Field.Unique) return true;
            }

            return false;
        }
    }
}
