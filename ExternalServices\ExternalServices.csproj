﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
    <SignAssembly>true</SignAssembly>
    <AssemblyOriginatorKeyFile>ExternalServices.snk</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="Properties\DataSources\**" />
    <EmbeddedResource Remove="Properties\DataSources\**" />
    <None Remove="Properties\DataSources\**" />
  </ItemGroup>
  <ItemGroup>
    <Compile Remove="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Remove="app.config" />
    <None Remove="packages.config" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="log4net" Version="2.0.8" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="2.1.1" />
    <PackageReference Include="Microsoft.VisualStudio.Threading.Analyzers" Version="16.4.43" />
    <PackageReference Include="RestSharp" Version="106.10.1" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="6.6.0" />
    <PackageReference Include="System.ServiceModel.Duplex" Version="4.4.*" />
    <PackageReference Include="System.ServiceModel.Http" Version="4.4.*" />
    <PackageReference Include="System.ServiceModel.NetTcp" Version="4.4.*" />
    <PackageReference Include="System.ServiceModel.Security" Version="4.4.*" />
  </ItemGroup>

  <ItemGroup>
    <WCFMetadata Include="Connected Services" />
  </ItemGroup>

  <ItemGroup>
    <None Update="ExternalServices.snk">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Connected Services\" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Connectors\Connectors.csproj" />
    <ProjectReference Include="..\Puppeteer\Puppeteer.csproj" />
  </ItemGroup>

</Project>