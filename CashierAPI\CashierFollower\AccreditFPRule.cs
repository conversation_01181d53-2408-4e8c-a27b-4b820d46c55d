﻿using GamesEngine.Bets;
using Microsoft.VisualStudio.Web.CodeGeneration.Design;
using MySql.Data.MySqlClient;
using Puppeteer;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Interprete.Libraries;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CashierAPI.CashierFollower
{
    public class AccreditFPRule : RuleWithoutActor
    {
        protected const string START_TEXT_LINE1 = "atAddress.GetOrCreateSource(";
        protected const string START_TEXT_LINE2 = ".Accredit(";
        protected const string START_TEXT_LINE3 = "FreePlay(";
        protected const string TEXT_SetInitialBalance = "balance.SetInitialBalance(";
        protected const string END_TEXT_LINE = ");";

        public override void Then(<PERSON><PERSON><PERSON> script)
        {
            if (script.Text.IndexOf(TEXT_SetInitialBalance) != -1)
            {
                if (InitialBalanceHandler.ExistsBalance("FP")) DBHelperCashierFollower.ProceedToMarkSkipsForSpecificDairyId(InitialBalanceHandler.GetCurrentId("FP"));
                InitialBalanceHandler.SetCurrentId("FP", script.DairyId);
            }
            else if (script.DairyId != LastIdRule.Id)
            {
                DBHelperCashierFollower.ProceedToMarkSkipsForSpecificDairyId(script.DairyId);
            }
        }

        public override void When(Script script)
        {
            if (script.Text.IndexOf(START_TEXT_LINE1) != -1 && script.Text.IndexOf(START_TEXT_LINE2) != -1 && script.Text.IndexOf(START_TEXT_LINE3) != -1)
            {
                Then(script);
            }

        }

        public override void FinalizeRule()
        {
            return;
        }
    }
}

/*Accredit Viejo
{
    source2 = atAddress.GetOrCreateSource(itIsThePresent, now, 2, FP, 'Manual FP');
    atAddress.CreateAccountIfNotExists(FP, 'FP');
    store = company.Sales.StoreById(1);
    source2.Accredit(itIsThePresent, Now, FreePlay(5.4), 'Jason M', '25230', store, '20% LOTTO RE-UP BONUS- Jason
 - 9944548', '2-*************', 'FP');
}*/
