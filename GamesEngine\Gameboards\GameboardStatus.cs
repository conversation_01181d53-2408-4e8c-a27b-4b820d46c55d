﻿using System;

namespace GamesEngine.Gameboards
{
	public enum GameboardStatus
	{
		PENDING = 0b_0000_0001,
		GRADED = 0b_0000_0010,
		NOACTION = 0b_0000_0100,
		REGRADED = 0b_0000_1000,

		WINNER = 0b_0001_0000,
		LOSER = 0b_0010_0000,
		UNPRIZED = 0b_0100_0000,
		REFUNDED = 0b_1000_0000,

		GRADING_MASK = 0b_0000_1111,
		PRIZING_MASK = 0b_1111_0000,
		UNKNOWN = 0b_0000_0000
	}

	internal static class GameboardStatusExtention
	{
		internal static void Init(this ref GameboardStatus currentStatus)
		{
			currentStatus = GameboardStatus.PENDING | GameboardStatus.UNPRIZED;
		}

		internal static void GradeAsWinner(this ref GameboardStatus currentStatus)
		{
			var pStatus = currentStatus.PrizingStatus();
			if (pStatus != GameboardStatus.REFUNDED)
			{
				if (pStatus != GameboardStatus.UNPRIZED) throw new GameEngineException($"Prizing Status must be {nameof(GameboardStatus.UNPRIZED)} but it is {pStatus}");
				var gStatus = currentStatus.GradingStatus();
				if (gStatus == GameboardStatus.GRADED) throw new GameEngineException($"Grading Status is already {nameof(GameboardStatus.GRADED)}. It can not be {nameof(GameboardStatus.GRADED)} again");

				currentStatus.StoreCurrentValue();
				currentStatus = (currentStatus & ~(gStatus | pStatus)) | GameboardStatus.GRADED | GameboardStatus.WINNER;
			}
		}

		internal static void GradeAsLoser(this ref GameboardStatus currentStatus)
		{
			var pStatus = currentStatus.PrizingStatus();
			if (pStatus != GameboardStatus.REFUNDED)
			{
				if (pStatus != GameboardStatus.UNPRIZED) throw new GameEngineException($"Prizing Status must be {nameof(GameboardStatus.UNPRIZED)} but it is {pStatus}");
				var gStatus = currentStatus.GradingStatus();
				if (gStatus == GameboardStatus.GRADED) throw new GameEngineException($"Grading Status is already {nameof(GameboardStatus.GRADED)}. It can not be {nameof(GameboardStatus.GRADED)} again");

				currentStatus.StoreCurrentValue();
				currentStatus = (currentStatus & ~(gStatus | pStatus)) | GameboardStatus.GRADED | GameboardStatus.LOSER;
			}
		}

		internal static char GradingAsChar(this GameboardStatus currentStatus)
		{
			var gStatus = currentStatus.GradingStatus();
			switch (gStatus)
			{
				case GameboardStatus.PENDING:
					return 'P';
				case GameboardStatus.GRADED:
					return 'G';
				case GameboardStatus.NOACTION:
					return 'N';
				case GameboardStatus.REGRADED:
					return 'R';

			}
			throw new GameEngineException($"Grading is unknown");
		}

		internal static void ChangeToRegraded(this ref GameboardStatus currentStatus)
		{
			var pStatus = currentStatus.PrizingStatus();
			if (pStatus != GameboardStatus.REFUNDED)
			{
				var gStatus = currentStatus.GradingStatus();
				if (gStatus == GameboardStatus.PENDING) throw new GameEngineException($"Grading Status is {nameof(GameboardStatus.PENDING)}. It can not be {nameof(GameboardStatus.REGRADED)}");
				if (gStatus == GameboardStatus.REGRADED) throw new GameEngineException($"Grading Status is already {nameof(GameboardStatus.REGRADED)}. It can not be {nameof(GameboardStatus.REGRADED)} again");

				currentStatus.StoreCurrentValue();
				currentStatus = (currentStatus & ~(gStatus | pStatus)) | GameboardStatus.REGRADED | GameboardStatus.UNPRIZED;
			}
		}

		internal static void ChangeToRefunded(this ref GameboardStatus currentStatus)
		{
			var pStatus = currentStatus.PrizingStatus();
			if (pStatus == GameboardStatus.REFUNDED) throw new GameEngineException($"Grading Status is already {nameof(GameboardStatus.REFUNDED)}. It can not be {nameof(GameboardStatus.REFUNDED)} again");

			if(pStatus != GameboardStatus.REFUNDED)
			{
				var gStatus = currentStatus.GradingStatus();
				currentStatus.StoreCurrentValue();
				currentStatus = (currentStatus & ~(gStatus | pStatus)) | GameboardStatus.NOACTION | GameboardStatus.REFUNDED;
			}
		}

		internal static void ChangeToNoAction(this ref GameboardStatus currentStatus)
		{
			var pStatus = currentStatus.PrizingStatus();
			if (pStatus != GameboardStatus.REFUNDED)
			{
				var gStatus = currentStatus.GradingStatus();
				if (gStatus == GameboardStatus.NOACTION) throw new GameEngineException($"Grading Status is already {nameof(GameboardStatus.NOACTION)}. It can not be {nameof(GameboardStatus.NOACTION)} again");

				currentStatus.StoreCurrentValue();
				currentStatus = (currentStatus & ~(gStatus | pStatus)) | GameboardStatus.NOACTION | GameboardStatus.UNPRIZED;
			}
		}

		internal static void SetAsRefunded(this ref GameboardStatus currentStatus)
		{
			var pStatus = currentStatus.PrizingStatus();
			if (pStatus != GameboardStatus.REFUNDED)
			{
				if (pStatus != GameboardStatus.UNPRIZED) throw new GameEngineException($"Prizing Status must be {nameof(GameboardStatus.UNPRIZED)} but it is {pStatus}");
				var gStatus = currentStatus.GradingStatus();
				if (gStatus == GameboardStatus.GRADED) throw new GameEngineException($"Grading Status is {nameof(GameboardStatus.GRADED)}. It must be {nameof(GameboardStatus.REGRADED)} before");

				currentStatus.StoreCurrentValue();
				currentStatus = (currentStatus & ~(gStatus | pStatus)) | GameboardStatus.NOACTION | GameboardStatus.REFUNDED;
			}
		}

		private static void StoreCurrentValue(this ref GameboardStatus currentStatus)
		{
			GameboardStatus currValue = currentStatus & (GameboardStatus.GRADING_MASK | GameboardStatus.PRIZING_MASK);
			GameboardStatus storedValue = (GameboardStatus)((int)currentStatus << 8);
			currentStatus = storedValue | currValue;
		}

		internal static void RestorePreviousValue(this ref GameboardStatus currentStatus)
		{
			GameboardStatus prevValue = currentStatus.PreviousValue();
			if (prevValue == 0) throw new GameEngineException("Status was not previously stored. Therefore it can not be restored");
			currentStatus = prevValue;
		}

		private static GameboardStatus PreviousValue(this GameboardStatus currentStatus)
		{
			GameboardStatus result = (GameboardStatus)((int)currentStatus >> 8);
			return result;
		}

		internal static bool IsGraded(this GameboardStatus currentStatus)
		{
			var result = currentStatus.GradingStatus() == GameboardStatus.GRADED;
			return result;
		}

		internal static bool IsPending(this GameboardStatus currentStatus)
		{
			var result = currentStatus.GradingStatus() == GameboardStatus.PENDING;
			return result;
		}

		internal static bool IsNoAction(this GameboardStatus currentStatus)
		{
			var result = currentStatus.GradingStatus() == GameboardStatus.NOACTION;
			return result;
		}

		internal static bool IsRegraded(this GameboardStatus currentStatus)
		{
			var result = currentStatus.GradingStatus() == GameboardStatus.REGRADED;
			return result;
		}

		internal static bool IsWinner(this GameboardStatus currentStatus)
		{
			var result = currentStatus.PrizingStatus() == GameboardStatus.WINNER;
			return result;
		}

		internal static bool IsLoser(this GameboardStatus currentStatus)
		{
			var result = currentStatus.PrizingStatus() == GameboardStatus.LOSER;
			return result;
		}

		internal static bool PreviouslyWasPending(this GameboardStatus currentStatus)
		{
			GameboardStatus prevValue = currentStatus.PreviousValue();
			if (prevValue == 0) throw new GameEngineException("Status was not previously stored. Therefore it can not be restored");

			var result = prevValue.GradingStatus() == GameboardStatus.PENDING;
			return result;
		}

		internal static bool PreviouslyWasWinner(this GameboardStatus currentStatus)
		{
			GameboardStatus prevValue = currentStatus.PreviousValue();
			if (prevValue == 0) throw new GameEngineException("Status was not previously stored. Therefore it can not be restored");

			var result = prevValue.PrizingStatus() == GameboardStatus.WINNER;
			return result;
		}

		internal static bool PreviouslyWasLoser(this GameboardStatus currentStatus)
		{
			GameboardStatus prevValue = currentStatus.PreviousValue();
			if (prevValue == 0) throw new GameEngineException("Status was not previously stored. Therefore it can not be restored");

			var result = prevValue.PrizingStatus() == GameboardStatus.LOSER;
			return result;
		}

		internal static bool PreviouslyWasNoAction(this GameboardStatus currentStatus)
		{
			GameboardStatus prevValue = currentStatus.PreviousValue();
			if (prevValue == 0) throw new GameEngineException("Status was not previously stored. Therefore it can not be restored");

			var result = prevValue.GradingStatus() == GameboardStatus.NOACTION;
			return result;
		}

		internal static bool IsUnprized(this GameboardStatus currentStatus)
		{
			var result = currentStatus.PrizingStatus() == GameboardStatus.UNPRIZED;
			return result;
		}

		internal static bool IsRefunded(this GameboardStatus currentStatus)
		{
			var result = currentStatus.PrizingStatus() == GameboardStatus.REFUNDED;
			return result;
		}

		internal static GameboardStatus GradingStatus(this GameboardStatus currentStatus)
		{
			var result = currentStatus & GameboardStatus.GRADING_MASK;
			return result;
		}

		internal static GameboardStatus PrizingStatus(this GameboardStatus currentStatus)
		{
			var result = currentStatus & GameboardStatus.PRIZING_MASK;
			return result;
		}
	}
}
