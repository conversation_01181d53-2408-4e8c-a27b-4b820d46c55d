﻿using GamesEngine;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
[assembly: InternalsVisibleTo("ResourcesAPI")]

namespace Resources.BL
{
    [Puppet]
    public sealed class Files : Objeto
    {
        private readonly Dictionary<FileKey, File> files = new Dictionary<FileKey, File>();

        public Files()
        {

        }

        internal File GetFile(string gameName, string resourceOwner, string virtualPath)
        {
            if (String.IsNullOrWhiteSpace(gameName)) throw new ArgumentNullException(nameof(gameName));
            if (String.IsNullOrWhiteSpace(resourceOwner)) throw new ArgumentNullException(nameof(resourceOwner));
            if (String.IsNullOrWhiteSpace(virtualPath)) throw new ArgumentNullException(nameof(virtualPath));

            var fileKey = new FileKey(gameName, resourceOwner, virtualPath);
            if (!ExistsFile(fileKey)) throw new GameEngineException("File does not exist");

            File file = FindFile(fileKey);
            return file;
        }

        internal File CreateFile(string gameName, string resourceOwner, string virtualPath)
        {
            if (String.IsNullOrWhiteSpace(gameName)) throw new ArgumentNullException(nameof(gameName));
            if (String.IsNullOrWhiteSpace(resourceOwner)) throw new ArgumentNullException(nameof(resourceOwner));
            if (String.IsNullOrWhiteSpace(virtualPath)) throw new ArgumentNullException(nameof(virtualPath));

            FileKey fileKey = new FileKey(gameName, resourceOwner, virtualPath);
            var file = new File(fileKey);
            if (files.ContainsKey(fileKey))
            {
                files[fileKey] = file;
            }
            else
            {
                files.Add(fileKey, file);
            }

            return file;
        }

        private bool ExistsFile(FileKey fileKey)
        {
            var exists = files.ContainsKey(fileKey);
            return exists;
        }

		internal bool ExistsFile(string gameName, string resourceOwner, string virtualPath)
		{
			if (String.IsNullOrWhiteSpace(gameName)) throw new ArgumentNullException(nameof(gameName));
			if (String.IsNullOrWhiteSpace(resourceOwner)) throw new ArgumentNullException(nameof(resourceOwner));
			if (String.IsNullOrWhiteSpace(virtualPath)) throw new ArgumentNullException(nameof(virtualPath));

			var fileKey = new FileKey(gameName, resourceOwner, virtualPath);
			return ExistsFile(fileKey);
		}

		private File FindFile(FileKey fileKey)
        {
            var file = files.SingleOrDefault(k => k.Key.Equals(fileKey));
            return file.Value;
        }

        internal string GenerateResourceOwner(string gameName, string virtualPath, DateTime now)
        {
            string text = $"{gameName}{virtualPath}{now.ToString("dd/MM/yyyy hh:mm:ss tt")}";
            if (String.IsNullOrEmpty(text))
                return String.Empty;

            using (var sha = new System.Security.Cryptography.SHA256Managed())
            {
                byte[] textData = System.Text.Encoding.UTF8.GetBytes(text);
                byte[] hash = sha.ComputeHash(textData);
                return BitConverter.ToString(hash).Replace("-", String.Empty);
            }
        }
    }

    internal class FileKey: Objeto, IEquatable<FileKey>
    {
        private string gameName;
        private string resourceOwner;
        private string virtualPath;

        internal string GameName
        {
            get { return gameName; }
        }
        internal string ResourceOwner
        {
            get { return resourceOwner; }
        }
        internal string VirtualPath
        {
            get { return virtualPath; }
        }

        internal FileKey(string gameName, string resourceOwner, string virtualPath)
        {
            if (String.IsNullOrWhiteSpace(gameName)) throw new ArgumentNullException(nameof(gameName));
            if (String.IsNullOrWhiteSpace(resourceOwner)) throw new ArgumentNullException(nameof(resourceOwner));
            if (String.IsNullOrWhiteSpace(virtualPath)) throw new ArgumentNullException(nameof(virtualPath));

            this.gameName = gameName;
            this.resourceOwner = resourceOwner;
            this.virtualPath = virtualPath;
        }

        public override int GetHashCode()
        {
            unchecked // Overflow is fine, just wrap
            {
                int hash = 17;
                hash = hash * 23 + gameName.GetHashCode();
                hash = hash * 23 + resourceOwner.GetHashCode();
                hash = hash * 23 + virtualPath.GetHashCode();
                return hash;
            }
        }

        public bool Equals(FileKey fileKey)
        {
            return fileKey.VirtualPath == virtualPath && fileKey.GameName == gameName && fileKey.ResourceOwner == resourceOwner;
        }

        internal string GetNameInDisk()
        {
            string text = gameName + resourceOwner + virtualPath;
            if (String.IsNullOrEmpty(text))
                return String.Empty;

            using (var sha = new System.Security.Cryptography.SHA256Managed())
            {
                byte[] textData = System.Text.Encoding.UTF8.GetBytes(text);
                byte[] hash = sha.ComputeHash(textData);
                return BitConverter.ToString(hash).Replace("-", String.Empty);
            }
        }
    }

    internal class FileDataWithKey : Objeto
    {
        private FileKey key;
        private File file;

        internal FileKey Key
        {
            get { return key; }
        }

        internal File File
        {
            get { return file; }
        }

        public FileDataWithKey(File file, FileKey key)
        {
            this.file = file;
            this.key = key;
        }
    }
}
