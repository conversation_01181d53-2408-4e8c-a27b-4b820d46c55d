﻿using ExchangeAPI.ExchangeEntities;
using GamesEngine.Finance;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static ExchangeAPI.Controllers.APIController;

namespace ExchangeAPI.Service
{
    public static class DTOMapperToEntities
    {
        public static Exchangerate MapToExchangerate(this ExchangeRateBody body, DateTime now)
        {
            var result = new Exchangerate()
            {
                Date = now,
                Fromcurrencyid = Coinage.Coin(body.FromCurrencyCode).Id,
                Tocurrencyid = Coinage.Coin(body.ToCurrencyCode).Id,
                Purchaseprice = body.PurchasePrice,
                Saleprice = body.SalePrice
            };
            return result;
        }

    }
}
