﻿using ExchangeAPI.ExchangeEntities;
using ExchangeAPI.Model;
using ExchangeAPI.Service;
using GamesEngine;
using GamesEngine.Finance;
using GamesEngine.Settings;
using Microsoft.EntityFrameworkCore;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Currency = GamesEngine.Finance.Currency;

namespace ExchangeAPI.Logic
{
    public class DebitNotes
    {
        private const string ALL_SELECTED = "all";
        private const int MAXIMUM_AMOUNT_OF_ROWS_ALLOWED = 100;

        public static DebitNotesDTO FilteredBy(DateTime startDate, DateTime endDate, string state, string identificationNumber, string transactionId, string currencyCode, string cashierName, 
            string domain, int initialIndex, int amountOfRows)
        {
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate.Hour != 0 || startDate.Minute != 0 || startDate.Second != 0) throw new GameEngineException($"{nameof(startDate)} {startDate} is not valid");
            if (endDate.Hour != 0 || endDate.Minute != 0 || endDate.Second != 0) throw new GameEngineException($"{nameof(endDate)} {endDate} is not valid");
            if (string.IsNullOrWhiteSpace(state)) throw new ArgumentNullException(nameof(state));
            if (string.IsNullOrWhiteSpace(identificationNumber)) throw new ArgumentNullException(nameof(identificationNumber));
            if (string.IsNullOrWhiteSpace(transactionId)) throw new ArgumentNullException(nameof(transactionId));
			if (string.IsNullOrWhiteSpace(cashierName)) throw new ArgumentNullException(nameof(cashierName));
            if (string.IsNullOrWhiteSpace(domain)) throw new ArgumentNullException(nameof(domain));
            if (initialIndex < 0) throw new GameEngineException($"{nameof(initialIndex)} {initialIndex} is not valid");
            if (amountOfRows <= 0) throw new GameEngineException($"{nameof(amountOfRows)} {amountOfRows} is not valid");

            if (amountOfRows > MAXIMUM_AMOUNT_OF_ROWS_ALLOWED)
            {
                amountOfRows = MAXIMUM_AMOUNT_OF_ROWS_ALLOWED;
            }

            var allStatesSelected = state.ToLower().Trim() == ALL_SELECTED;
            var allIdentificationNumberSelected = identificationNumber.ToLower().Trim() == ALL_SELECTED;
            var allTransactionIdsSelected = transactionId.ToLower().Trim() == ALL_SELECTED;
            var allCurrencyCode = currencyCode.ToLower().Trim() == ALL_SELECTED;
			var allCashiersSelected = cashierName.ToLower().Trim() == ALL_SELECTED;
            var allDomainsSelected = domain.ToLower().Trim() == ALL_SELECTED;

            using (var context = new ExchangeContext())
            {
                var debitNotes = context.DebitNote
                    .Include(debitNote => debitNote.WhocreatedNavigation)
                    .Include(debitNote => debitNote.DomainNavigation)
                    .Include(debitNote => debitNote.DebitNoteAttachment)
                    .Include(debitNote => debitNote.Currency)
                    .Where(debitNote => ! debitNote.Deleted && debitNote.Datecreated.Date >= startDate && debitNote.Datecreated.Date <= endDate
                        && (allStatesSelected || (debitNote.Approvals == debitNote.Approvalsrequired && state == TransactionState.Approved.ToString()) || (debitNote.Rejections > 0 && state == TransactionState.Rejected.ToString()) || (debitNote.Approvals == 0 && debitNote.Rejections == 0 && state == TransactionState.Pending.ToString()))
                        && (allIdentificationNumberSelected || debitNote.Customer == identificationNumber)
                        && (allTransactionIdsSelected || debitNote.Id.ToString() == transactionId)
                        && (allCurrencyCode || debitNote.Currency.Isocode == currencyCode)
						&& (allCashiersSelected || debitNote.WhocreatedNavigation.Name == cashierName)
                        && (allDomainsSelected || debitNote.DomainNavigation.Domain == domain)
                        )
                    .OrderBy(debitNote => debitNote.Datecreated);
                var debitNotesPaged = debitNotes
                    .Skip(initialIndex)
                    .Take(amountOfRows);
                var debitNotesDTO = debitNotesPaged.MapToDebitNotesDTO();
                debitNotesDTO.TotalRecords = debitNotes.Count();
                debitNotesDTO.TotalAmount = debitNotes.Sum(debitNote => debitNote.Amount);
                debitNotesDTO.TotalAmountFormatted = allCurrencyCode ?
                    Currency.ToDisplayFormatWithoutSign(GamesEngine.Finance.Currencies.CODES.USD.ToString(), debitNotesDTO.TotalAmount) :
                    Currency.Factory(currencyCode, debitNotesDTO.TotalAmount).ToDisplayFormat();
                return debitNotesDTO;
            }
        }

		private static DebitNote DebitNoteFor(int id)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");

            using (var context = new ExchangeContext())
            {
                var debitNoteFound = context.DebitNote
                    .Where(debitNote => ! debitNote.Deleted && debitNote.Id == id)
                    .FirstOrDefault();
                return debitNoteFound;
            }
        }

        public static void Save(DebitNote debitNote)
        {
            if (debitNote == null) throw new ArgumentNullException(nameof(debitNote));

            using (var context = new ExchangeContext())
            {
                context.DebitNote.Add(debitNote);
                context.SaveChanges();
            }
        }

        public static void SaveWithAttachments(DebitNote debitNote, IEnumerable<string> urls)
        {
            if (debitNote == null) throw new ArgumentNullException(nameof(debitNote));

            using (var context = new ExchangeContext())
            {
                using (var transaction = context.Database.BeginTransaction())
                {
                    try
                    {
                        context.DebitNote.Add(debitNote);
                        context.SaveChanges();

                        var attachments = new List<DebitNoteAttachment>();
                        foreach (var url in urls)
                        {
                            attachments.Add(new DebitNoteAttachment()
                            {
                                Debitnoteid = debitNote.Id,
                                Url = url
                            });
                        }
                
                        context.DebitNoteAttachment.AddRange(attachments);
                        context.SaveChanges();
                        transaction.Commit();
                    }
                    catch (Exception e)
                    {
                        transaction.Rollback();
                        var message = $"{nameof(SaveWithAttachments)} method cannot add id:{debitNote.Id}";
                        Loggers.GetIntance().Db.Error(message, e);
                        ErrorsSender.Send(e, message);
                        throw;
                    }
                }
            }
        }

        public static DebitNote UpdateAsApproved(int id, DateTime date, string employeeName, int processorId)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");

            var user = Integration.ExchangeUsers.User(employeeName);
            var debitNote = DebitNoteFor(id);
            if (debitNote == null) throw new ArgumentNullException(nameof(debitNote));

            debitNote.Approvals = 1;
            debitNote.Whoapproved = user.Id;
            debitNote.Dateapproved = date;
            debitNote.Processorid = processorId;
            using (var context = new ExchangeContext())
            {
                context.DebitNote.Update(debitNote);
                context.SaveChanges();
            }
            return debitNote;
        }

        public static DebitNote UpdateJournalEntry(ExchangeContext context, int id, string journalEntryDetailId)
        {
            if (context == null) throw new ArgumentNullException(nameof(context));
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(journalEntryDetailId)) throw new ArgumentNullException(nameof(journalEntryDetailId));

            var debitNote = DebitNoteFor(id);
            if (debitNote == null) throw new ArgumentNullException(nameof(debitNote));

            debitNote.Journalentrydetailid = journalEntryDetailId;
            context.DebitNote.Update(debitNote);
            context.SaveChanges();
            return debitNote;
        }

        public static DebitNote UpdateAsRejected(int id, DateTime date, string rejectionReason, string employeeName)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");

            var user = Integration.ExchangeUsers.User(employeeName);
            var debitNote = DebitNoteFor(id);
            if (debitNote == null) throw new ArgumentNullException(nameof(debitNote));

            debitNote.Rejections = 1;
            debitNote.Whorejected = user.Id;
            debitNote.Daterejected = date;
            debitNote.Rejectionreason = rejectionReason;
            using (var context = new ExchangeContext())
            {
                context.DebitNote.Update(debitNote);
                context.SaveChanges();
            }
            return debitNote;
        }

        public static DebitNote UpdateVoucherUrl(int id, string voucherUrl)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(voucherUrl)) throw new ArgumentNullException(nameof(voucherUrl));

            var debitNote = DebitNoteFor(id);
            if (debitNote == null) throw new ArgumentNullException(nameof(debitNote));

            debitNote.Voucherurl = voucherUrl;
            using (var context = new ExchangeContext())
            {
                context.DebitNote.Update(debitNote);
                context.SaveChanges();
            }
            return debitNote;
        }
    }
}
