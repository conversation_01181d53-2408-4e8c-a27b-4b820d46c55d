﻿CREATE PROCEDURE [dbo].[Load_Accounts_LRU_SP] AS BEGIN

	IF NOT EXISTS (SELECT 1 FROM sys.tables WHERE name = 'accountsLRU')
	BEGIN
		CREATE TABLE accountsLRU(
			[Cuenta] VARCHAR(50) PRIMARY KEY,
			[FechaHora] [datetime]
		);
	END

	DECLARE @command nVARCHAR(600);
	DECLARE @table_name VARCHAR(50);

	DECLARE accounts_cursor CURSOR FAST_FORWARD FOR SELECT DISTINCT TABLE_NAME FROM INFORMATION_SCHEMA.COLUMNS WITH (nolock) WHERE (TABLE_NAME LIKE 'C%' AND TABLE_NAME NOT LIKE 'C%[_]%') ORDER BY TABLE_NAME ASC;

	OPEN accounts_cursor FETCH NEXT FROM accounts_cursor INTO @table_name;

	WHILE @@FETCH_STATUS = 0
	BEGIN

		BEGIN TRAN
		IF EXISTS (SELECT 1 FROM accountsLRU WHERE Cuenta = @table_name) BEGIN
			BEGIN TRY
				DECLARE @fechaHora DATETIME;
				SET @command = 'SELECT TOP 1 @fh = fechaHora FROM ' + @table_name + ' WITH (nolock) ORDER BY id DESC;';
				DECLARE @params NVARCHAR(255) = '@fh DATETIME OUTPUT';
				EXEC sp_executesql @command, @params, @fh = @fechaHora OUTPUT;
				SET @command = 'UPDATE accountsLRU SET FechaHora = ''' + CONVERT(VARCHAR(23), @fechaHora, 121) + ''' WHERE Cuenta = ''' + @table_name + ''';';
				EXEC sp_executesql @command;
			END TRY
			BEGIN CATCH
			END CATCH
		END	
		ELSE BEGIN
			BEGIN TRY
				SET @command = 'INSERT INTO accountsLRU SELECT TOP 1 ''' + @table_name + ''' , FechaHora FROM ' + @table_name + ' WITH (nolock) ORDER BY id DESC;';
				EXEC sp_executesql @command;
			END TRY
			BEGIN CATCH
			END CATCH
		END
		COMMIT TRAN

		FETCH NEXT FROM accounts_cursor INTO @table_name;
	END

	CLOSE accounts_cursor;
	DEALLOCATE accounts_cursor;

END
--SELECT Cuenta, FechaHora, DATEDIFF(day, FechaHora, GETDATE()) As Dias FROM accountsLRU ORDER BY Dias Asc;