﻿using System;
using System.Collections.Generic;

namespace ExchangeAPI.ExchangeEntities
{
    public partial class Currency
    {
        public Currency()
        {
            CreditNote = new HashSet<CreditNote>();
            Dailyprofits = new HashSet<Dailyprofits>();
            DebitNote = new HashSet<DebitNote>();
            DepositFromcurrency = new HashSet<Deposit>();
            DepositTocurrency = new HashSet<Deposit>();
            ExchangerateFromcurrency = new HashSet<Exchangerate>();
            ExchangerateTocurrency = new HashSet<Exchangerate>();
            ProfitabletransactionFromcurrency = new HashSet<Profitabletransaction>();
            ProfitabletransactionTocurrency = new HashSet<Profitabletransaction>();
            TransferFromcurrency = new HashSet<Transfer>();
            TransferTocurrency = new HashSet<Transfer>();
            WithdrawalFromcurrency = new HashSet<Withdrawal>();
            WithdrawalTocurrency = new HashSet<Withdrawal>();
        }

        public int Id { get; set; }
        public string Sign { get; set; }
        public string Isocode { get; set; }
        public string Unicode { get; set; }
        public sbyte Decimalprecision { get; set; }

        public virtual ICollection<CreditNote> CreditNote { get; set; }
        public virtual ICollection<Dailyprofits> Dailyprofits { get; set; }
        public virtual ICollection<DebitNote> DebitNote { get; set; }
        public virtual ICollection<Deposit> DepositFromcurrency { get; set; }
        public virtual ICollection<Deposit> DepositTocurrency { get; set; }
        public virtual ICollection<Exchangerate> ExchangerateFromcurrency { get; set; }
        public virtual ICollection<Exchangerate> ExchangerateTocurrency { get; set; }
        public virtual ICollection<Profitabletransaction> ProfitabletransactionFromcurrency { get; set; }
        public virtual ICollection<Profitabletransaction> ProfitabletransactionTocurrency { get; set; }
        public virtual ICollection<Transfer> TransferFromcurrency { get; set; }
        public virtual ICollection<Transfer> TransferTocurrency { get; set; }
        public virtual ICollection<Withdrawal> WithdrawalFromcurrency { get; set; }
        public virtual ICollection<Withdrawal> WithdrawalTocurrency { get; set; }
    }
}
