﻿using GamesEngine.Bets;
using GamesEngine.PurchaseOrders;
using MySql.Data.MySqlClient;
using Puppeteer;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Interprete.Libraries;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Reflection.Metadata.Ecma335;
using System.Text;
using System.Threading.Tasks;

namespace CashierAPI.CashierFollower
{
	internal class FragmentCreationRule : RuleWithoutActor
	{
		protected const string START_TEXT_LINE1 = "authorization = atAddress.GetAuthorization(";
        protected const string START_TEXT_LINE1AlternativeA = "authorizations = atAddress.GetAuthorizations(";
        protected const string START_TEXT_LINE1AlternativeB = "auths = AuthorizationsNumbers(";
        protected const string START_TEXT_LINEAuthorizationsNumbersAdd = "auths.Add(";
        protected const string START_TEXT_LINE2 = "authorization.CreateFragments(";
        protected const string START_TEXT_LINE2Alternative = "authorizations.CreateFragments()";
        protected const string START_TEXT_LINE3 = "authorization.AddFragments";
        protected const string START_TEXT_LINE3Alternative = "authorizations.AddFragments(";
        protected const string END_TEXT_LINE = ");";

        public override void Then(Script script)
        {
            int fragmentsSize = FragmentsSize(script.Text);
            if (script.Text.IndexOf(START_TEXT_LINE1) != -1)
            {
                var authorizationNumber = AuthorizationNumber(script.Text);
                if (!string.IsNullOrEmpty(authorizationNumber) && fragmentsSize > 0)
                {
                    DBHelperCashierFollower.UpdateAuthorizationInfoForFragmentCreationWithFragments(authorizationNumber, fragmentsSize, script.DairyId);
                }
                else
                {
                    DBHelperCashierFollower.UpdateAuthorizationInfoForFragmentCreation(authorizationNumber, script.DairyId);
                }
            }
            else if (script.Text.IndexOf(START_TEXT_LINE1AlternativeB) != -1)
            {
                var authorizationNumbers = AuthorizationNumbersWithClass(script.Text);
                if (authorizationNumbers != null)
                {
                    foreach (var authorizationNumber in authorizationNumbers)
                    {
                        var authorization = authorizationNumber.Trim();
                        DBHelperCashierFollower.UpdateAuthorizationInfoForFragmentCreationWithFragments(authorization, fragmentsSize, script.DairyId);
                    }
                }
            }
            else if (script.Text.IndexOf(START_TEXT_LINE1AlternativeA) != -1)
            {
                var authorizationNumbers = AuthorizationNumbers(script.Text);
                if (authorizationNumbers != null)
                {
                    foreach (var authorizationNumber in authorizationNumbers)
                    {
                        var authorization = authorizationNumber.Trim();
                        DBHelperCashierFollower.UpdateAuthorizationInfoForFragmentCreationWithFragments(authorization, fragmentsSize, script.DairyId);
                    }
                }
            }
        }

        public override void When(Script script)
        {
            if ((script.Text.IndexOf(START_TEXT_LINE1) != -1 || script.Text.IndexOf(START_TEXT_LINE1AlternativeA) != -1 || script.Text.IndexOf(START_TEXT_LINE1AlternativeB) != -1) && 
                (script.Text.IndexOf(START_TEXT_LINE2) != -1 || script.Text.IndexOf(START_TEXT_LINE2Alternative) != -1) && 
                (script.Text.IndexOf(START_TEXT_LINE3) != -1 || script.Text.IndexOf(START_TEXT_LINE3Alternative) != -1))
            {
                Then(script);
            }
            else if ((script.Text.IndexOf(START_TEXT_LINE1) != -1 || script.Text.IndexOf(START_TEXT_LINE1AlternativeA) != -1 || script.Text.IndexOf(START_TEXT_LINE1AlternativeB) != -1) && 
                (script.Text.IndexOf(START_TEXT_LINE2) == -1 || script.Text.IndexOf(START_TEXT_LINE2Alternative) == -1) && 
                (script.Text.IndexOf(START_TEXT_LINE3) != -1 || script.Text.IndexOf(START_TEXT_LINE3Alternative) != -1))
            {
                Then(script);
            }
        }

        public string AuthorizationNumber(string script)
		{
            UtilStringRules.SetHeaderText(START_TEXT_LINE1, END_TEXT_LINE);
            return UtilStringRules.GetBodyArgText(script).Trim();
		}

        internal string[] AuthorizationNumbers(string script)
        {
            UtilStringRules.SetHeaderText(START_TEXT_LINE1AlternativeA, END_TEXT_LINE);
            var body = UtilStringRules.GetBodyArgText(script).Trim();

            if (body.StartsWith("{") && body.EndsWith("}"))
            {
                var result = body.TrimStart('{').TrimEnd('}').Split(',');
                return result;
            }
            else
            {
                throw new Exception("Error getting Authorizations Numbers");
            }
        }

        internal string[] AuthorizationNumbersWithClass(string script)
        {
            UtilStringRules.SetHeaderText(START_TEXT_LINE1AlternativeB, END_TEXT_LINE);
            var body = UtilStringRules.GetBodyArgText(script).Trim();

            string[] arguments = body.Split(',');
            int authorization = int.Parse(arguments[0]);
            var consecutive = arguments.Length > 1 ? int.Parse(arguments[1]) : 1;
            var authorizationNumbers = arguments.Length > 1 ? new AuthorizationsNumbers(authorization, consecutive) : new AuthorizationsNumbers(authorization);

            if (script.IndexOf(START_TEXT_LINEAuthorizationsNumbersAdd) != -1)
            {
                UtilStringRules.SetHeaderText(START_TEXT_LINEAuthorizationsNumbersAdd, END_TEXT_LINE);
                body = UtilStringRules.GetBodyArgText(script).Trim();
                arguments = body.Split(").Add(");
                foreach (var arg in arguments)
                {
                    if (int.TryParse(arg, out authorization))
                    {
                        authorizationNumbers.Add(authorization);
                    }
                    else
                    {
                        var addArgs = arg.Split(',');
                        authorization = int.Parse(addArgs[0]);
                        consecutive = addArgs.Length > 1 ? int.Parse(addArgs[1]) : 1;
                        authorizationNumbers.Add(authorization, consecutive);
                    }
                }
            }
            return authorizationNumbers.Numbers.Select(n => n.ToString()).ToArray();
        }

        public int FragmentsSize(string script)
        {
            if (script.IndexOf(START_TEXT_LINE2) != -1)
            {
                UtilStringRules.SetHeaderText(START_TEXT_LINE3, END_TEXT_LINE);
                string[] argumentos = UtilStringRules.GetBodyArgText(script).Split(',');

                if (argumentos.Length <= 1) return 0;

                int fragmentsSize = int.Parse(argumentos[1].Trim());
                return fragmentsSize;
            }
            else
            {
                return 1;
            }
        }

        public override void FinalizeRule()
        {
            return;
        }
    }
}
