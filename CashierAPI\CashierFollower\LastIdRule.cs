﻿using MySql.Data.MySqlClient;
using Puppeteer.EventSourcing;
using System.Data.SqlClient;

namespace CashierAPI.CashierFollower
{
	internal class LastIdRule 
	{

		internal static int Id { get; private set; } = 0;

		public static void GetLastId(string connectionString, DatabaseType dbtype, string actorName)
		{
            if (dbtype == DatabaseType.MySQL)
            {
                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    string sql = $"SELECT MAX(id) FROM {actorName} nolock";
                    try
                    {
                        connection.Open();
                        using (MySqlCommand command = new MySqlCommand(sql, connection))
                        using (MySqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                Id = reader.GetInt32(0);
                            }
                            reader.Close();
                        }
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
            }
            else if (dbtype == DatabaseType.SQLServer)
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    string sql = $"SELECT TOP 1 id FROM {actorName} WITH (nolock) ORDER BY id DESC";
                    try
                    {
                        connection.Open();
                        using (SqlCommand command = new SqlCommand(sql, connection))
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                Id = reader.GetInt32(0);
                            }
                            reader.Close();
                        }
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
            }
        }
    }
}
