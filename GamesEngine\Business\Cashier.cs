﻿using GamesEngine.Finance;
using GamesEngine.Gameboards;
using GamesEngine.Gameboards.Lines;
using GamesEngine.Gameboards.Lotto;
using GamesEngine.Games.Lotto;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using static GamesEngine.Finance.PaymentChannels;

namespace GamesEngine.Business
{
	internal class Cashier
	{
		private Company company;

		internal Cashier(Company company)
		{
			if (company == null) throw new ArgumentNullException(nameof(company));

			this.company = company;
		}

		private Accounting Accounting
		{
			get
			{
				return this.company.Accounting;
			}
		}

		internal void PayGameboards(bool itIsThePresent, IEnumerable<Gameboard> gameboards, DateTime now, string employeeName, bool sometimeWereTicketsSentToAccounting)
		{
			if (!(Integration.UseKafka || Integration.UseKafkaForAuto)) return;

			if (!itIsThePresent) return;

			if (gameboards == null) throw new ArgumentNullException(nameof(gameboards));
			if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

			PayWinnerGameboards(itIsThePresent, gameboards, now, employeeName, sometimeWereTicketsSentToAccounting);
			PayLoserAndNoActionGameboards(itIsThePresent, gameboards, now, employeeName, sometimeWereTicketsSentToAccounting);
		}

		private void SendRewards(KafkaMessagesBuffer depositBuffer, KafkaMessagesBuffer withDrawalBuffer, Gameboard gameboard, Wager wager, int version, string employeeName)
		{
			var storeId = wager.Betboard.Company.Sales.CurrentStore.Id;
			decimal prize = wager.ToWin() + wager.Risk; //Dont use wager.Risk, coz in balls selection has another amount;
			string reference = version == 0 ? $"{wager.Id}-{wager.AuthorizationId}" :
				$"{wager.Id}-{wager.AuthorizationId}-{version}";
			if (reference.Length > Movement.MAX_REFERENCE_LENGTH) reference = reference.Substring(0, Movement.MAX_REFERENCE_LENGTH);

			if (gameboard.PreviouslyWasPending() && wager.IsWinner()
				|| gameboard.PreviouslyWasLoser() && wager.IsWinner() && gameboard.IsRefunded()
				|| gameboard.PreviouslyWasNoAction() && wager.IsWinner() && gameboard.IsRefunded())
			{
				DepositMessage info = new DepositMessage(
					atAddress: wager.Player.AccountNumber,
					currency: wager.Order.Coin,
					storeId: storeId,
					amount: prize,
					who: employeeName,
					description: $"Lines Win for TN#{wager.Id}-{wager.AuthorizationId} - Posted date {gameboard.PostedDateAsString()}",
					reference: reference,
					agent: wager.Player.Agent,
					processorId: WholePaymentProcessor.NoPaymentProcessor,
					processorKey: string.Empty
					);

				depositBuffer.Send(info);
			}
			else if (gameboard.PreviouslyWasWinner() && !wager.IsWinner() && gameboard.IsRefunded())
			{

				WithdrawMessage info = new WithdrawMessage(
					atAddress: gameboard.Player.AccountNumber,
					currency: wager.Order.Coin,
					storeId: storeId,
					amount: prize,
					who: employeeName,
					description: $"Lines Regrade for TN#{wager.Id}-{wager.AuthorizationId} - Posted date {gameboard.PostedDateAsString()}",
					reference: reference,
					agent: wager.Player.Agent,
					processorId: WholePaymentProcessor.NoPaymentProcessor,
                    processorKey: string.Empty
                );
				withDrawalBuffer.Send(info);
			}
		}

		void SendRefunds(KafkaMessagesBuffer depositBuffer, KafkaMessagesBuffer withDrawalBuffer, Ticket ticket, int version, string employeeName)
		{
			var storeId = ticket.SoldBy.Id;
			string reference = version == 0 ? $"{ticket.TicketNumber}" : $"{ticket.TicketNumber}-{version}";
			if (reference.Length > Movement.MAX_REFERENCE_LENGTH) reference = reference.Substring(0, Movement.MAX_REFERENCE_LENGTH);

			if (ticket.PreviouslyWasNoAction() && ticket.IsGraded())
			{
                var fireballTag = ticket.BelongsToFireBallDraw ? " FB" : string.Empty;
                var processorKey = ticket.Player.Agent == Agents.ARTEMIS ? WholePaymentProcessor.Instance().SearchGradeProcessorBy(typeof(Connectors.town.connectors.drivers.artemis.Grade)).ProcessorKey : string.Empty;
                WithdrawMessage info = new WithdrawMessage(
					atAddress: ticket.Player.AccountNumber,
					currency: ticket.Order.Coin,
					storeId: storeId,
					amount: ticket.TicketAmount(),
					who: employeeName,
					description: $"Lotto{fireballTag} TN#{ticket.TicketNumber} - Posted date {ticket.PostedDateAsString()}",
					reference: reference,
					agent: ticket.Player.Agent,
					processorId: WholePaymentProcessor.NoPaymentProcessor,
                    processorKey: processorKey
                );
				withDrawalBuffer.Send(info);
			}
			else if (ticket.IsNoAction())
			{
                var fireballTag = ticket.BelongsToFireBallDraw ? " FB" : string.Empty;
                var processorKey = ticket.Player.Agent == Agents.ARTEMIS ? WholePaymentProcessor.Instance().SearchGradeProcessorBy(typeof(Connectors.town.connectors.drivers.artemis.Grade)).ProcessorKey : string.Empty;
                DepositMessage info = new DepositMessage(
					atAddress: ticket.Player.AccountNumber,
					currency: ticket.Order.Coin,
					storeId: storeId,
					amount: ticket.TicketAmount(),
					who: employeeName,
					description: $"Lotto{fireballTag} Refund for TN#{ticket.TicketNumber} - Posted date {ticket.PostedDateAsString()}",
					reference: reference,
					agent: ticket.Player.Agent,
					processorId: WholePaymentProcessor.NoPaymentProcessor,
                    processorKey: processorKey
                    );

				depositBuffer.Send(info);
			}
		}

		void SendRefunds(KafkaMessagesBuffer depositBuffer, Ticket ticket, string employeeName)
		{
			var storeId = ticket.SoldBy.Id;
			string reference = $"{ticket.TicketNumber}";
			if (reference.Length > Movement.MAX_REFERENCE_LENGTH) reference = reference.Substring(0, Movement.MAX_REFERENCE_LENGTH);
            var fireballTag = ticket.BelongsToFireBallDraw ? " FB" : string.Empty;
            var processorKey = ticket.Player.Agent == Agents.ARTEMIS ? WholePaymentProcessor.Instance().SearchGradeProcessorBy(typeof(Connectors.town.connectors.drivers.artemis.Grade)).ProcessorKey : string.Empty;
			DepositMessage info = new DepositMessage(
				atAddress: ticket.Player.AccountNumber,
				currency: ticket.Order.Coin,
				storeId: storeId,
				amount: ticket.TicketAmount(),
				who: employeeName,
				description: $"Lotto{fireballTag} Refund for TN#{ticket.TicketNumber} - Posted date {ticket.PostedDateAsString()}",
				reference: reference,
				agent: ticket.Player.Agent,
				processorId: WholePaymentProcessor.NoPaymentProcessor,
				processorKey: processorKey
				);

			depositBuffer.Send(info);
		}

		void SendRefunds(KafkaMessagesBuffer depositBuffer, TicketWager wager, string employeeName)
		{
			var ticket = wager.Ticket;
			if (ticket.Order.CurrencyCode.ToString() == wager.Ticket.Lottery.PicksLotteryGame.StandardCurrency)
			{
				var storeId = ticket.SoldBy.Id;
				string reference = wager.TicketNumber.ToString();
				if (reference.Length > Movement.MAX_REFERENCE_LENGTH) reference = reference.Substring(0, Movement.MAX_REFERENCE_LENGTH);
                var fireballTag = ticket.BelongsToFireBallDraw ? " FB" : string.Empty;
                var amountToRefund = ticket.TicketAmount() / ticket.CountWagers;
                var processorKey = ticket.Player.Agent == Agents.ARTEMIS ? WholePaymentProcessor.Instance().SearchGradeProcessorBy(typeof(Connectors.town.connectors.drivers.artemis.Grade)).ProcessorKey : string.Empty;
                DepositMessage info = new DepositMessage(
					atAddress: ticket.Player.AccountNumber,
					currency: ticket.Order.Coin,
					storeId: storeId,
					amount: amountToRefund,
					who: employeeName,
					description: $"Lotto{fireballTag} Refund for TN#{ticket.TicketNumber}-{wager.WagerNumber}",
					reference: reference,
					agent: ticket.Player.Agent,
					processorId: WholePaymentProcessor.NoPaymentProcessor,
					processorKey: processorKey
					);
				depositBuffer.Send(info);
			}
		}

		private void SendRewards(KafkaMessagesBuffer depositBuffer, KafkaMessagesBuffer withDrawalBuffer, Gameboard gameboard, Ticket ticket, TicketWager wager, int version, string employeeName)
		{
			var storeId = ticket.SoldBy.Id;
			decimal prize;  //Dont use wager.Risk, coz in balls selection has another amount;
			string reference = version == 0 ? wager.TicketNumber.ToString() :
				$"{wager.TicketNumber}-{version}";
			if (reference.Length > Movement.MAX_REFERENCE_LENGTH) reference = reference.Substring(0, Movement.MAX_REFERENCE_LENGTH);
            
            if (wager.IsWinner())
			{
                var fireballTag = ticket.BelongsToFireBallDraw ? " FB" : string.Empty;
                prize = ticket.Payout;
                var processorKey = ticket.Player.Agent == Agents.ARTEMIS ? WholePaymentProcessor.Instance().SearchGradeProcessorBy(typeof(Connectors.town.connectors.drivers.artemis.Grade)).ProcessorKey : string.Empty;
                DepositMessage info = new DepositMessage(
					atAddress: ticket.Player.AccountNumber,
					currency: wager.Ticket.Order.Coin,
					storeId: storeId,
					amount: prize,
					who: employeeName,
					description: $"Lotto{fireballTag} Win for TN#{ticket.TicketNumber}-{wager.WagerNumber} - Posted date {ticket.PostedDateAsString()}",
					reference: reference,
					agent: ticket.Player.Agent,
					processorId: WholePaymentProcessor.NoPaymentProcessor,
					processorKey: processorKey
					);

				depositBuffer.Send(info);
			}
			else if(ticket.PreviouslyWasWinner() && !wager.IsWinner() && (gameboard.IsNoAction() || gameboard.IsRegraded()))
			{
                var fireballTag = ticket.BelongsToFireBallDraw ? " FB" : string.Empty;
                prize = wager.ToWin + ticket.BetAmount();
                var processorKey = ticket.Player.Agent == Agents.ARTEMIS ? WholePaymentProcessor.Instance().SearchGradeProcessorBy(typeof(Connectors.town.connectors.drivers.artemis.Grade)).ProcessorKey : string.Empty;
                WithdrawMessage info = new WithdrawMessage(
					atAddress: ticket.Player.AccountNumber,
					currency: wager.Ticket.Order.Coin,
					storeId: storeId,
					amount: prize,
					who: employeeName,
					description: $"Lotto{fireballTag} Regrade for TN#{ticket.TicketNumber}-{wager.WagerNumber} - Posted date {ticket.PostedDateAsString()}",
					reference: reference,
					agent: ticket.Player.Agent,
					processorId: WholePaymentProcessor.NoPaymentProcessor,
					processorKey: processorKey
				);
				withDrawalBuffer.Send(info);
			}
		}

		private void PayWinnerGameboards(bool itIsThePresent, IEnumerable<Gameboard> gameboards, DateTime now, string employeeName, bool sometimeWereTicketsSentToAccounting)
		{
			var winners = gameboards.Where(x => x.IsHighPrioritySettle);
			if (Enumerable.Any(winners))
			{
				var transactionsBuffer = new SenderToCashier();

				using (FragmentPaymentCompressor highPriorityBuffer = new FragmentPaymentCompressor(itIsThePresent, Integration.Kafka.TopicForFragmentPaymentsForWinners))
				using (FragmentPaymentCompressor normalPriorityBuffer = new FragmentPaymentCompressor(itIsThePresent, Integration.Kafka.TopicForFragmentPaymentsForAll))
				using (KafkaMessagesBuffer depositsBuffer = new KafkaMessagesBuffer(itIsThePresent, Integration.Kafka.TopicForDeposits))
				using (KafkaMessagesBuffer withdrawalBuffer = new KafkaMessagesBuffer(itIsThePresent, Integration.Kafka.TopicForWithdrawals))
				{

					highPriorityBuffer.BeforeSendTheFirstMessage = highPriorityBuffer.GenerateHeader(employeeName, company.Sales.CurrentStore.Id, WholePaymentProcessor.NoPaymentProcessor, sometimeWereTicketsSentToAccounting, now);
					normalPriorityBuffer.BeforeSendTheFirstMessage = normalPriorityBuffer.GenerateHeader(employeeName, company.Sales.CurrentStore.Id, WholePaymentProcessor.NoPaymentProcessor, sometimeWereTicketsSentToAccounting, now);
					FragmentPaymentMessage gradedWagerMessage;
					int version;
					foreach (var gameboard in winners)
					{
						switch (gameboard)
						{
							case Ticket ticket:
								version = ticket.DrawVersion;
								switch (ticket.Prizing)
								{
									case GameboardStatus.LOSER:
										foreach (var wager in ticket.Wagers)
										{
											gradedWagerMessage = wager.FragmentPaymentMessage(now);
											highPriorityBuffer.Send(gradedWagerMessage);

											if (ticket.Order.CurrencyCode.ToString() == ticket.Lottery.PicksLotteryGame.StandardCurrency) SendRewards(depositsBuffer, withdrawalBuffer, gameboard, ticket, wager, version, employeeName);
										}
										
										if (ticket.Order.CurrencyCode.ToString() == ticket.Lottery.PicksLotteryGame.RewardCurrency || ticket.Order.CurrencyCode.ToString() == Currencies.CODES.KRW.ToString())
										{
											var previousSequenceOfNumbers = ticket.Draw.PreviousConfirmedSequenceOfNumbers;
											transactionsBuffer.WriteDebitDataForRegrade(withdrawalBuffer, ticket, previousSequenceOfNumbers, version, employeeName);
										}
																		
										break;
									case GameboardStatus.WINNER:
										
										foreach (var wager in ticket.Wagers)
										{
											gradedWagerMessage = wager.FragmentPaymentMessage(now);
											highPriorityBuffer.Send(gradedWagerMessage);

											if (ticket.Order.CurrencyCode.ToString() == ticket.Lottery.PicksLotteryGame.StandardCurrency) SendRewards(depositsBuffer, withdrawalBuffer, gameboard, ticket, wager, version, employeeName);
										}

										if (ticket.Order.CurrencyCode.ToString() == ticket.Lottery.PicksLotteryGame.RewardCurrency || ticket.Order.CurrencyCode.ToString() == Currencies.CODES.KRW.ToString())
										{
											transactionsBuffer.WriteCreditData(depositsBuffer, ticket, version, employeeName);
										}

										break;
									case GameboardStatus.UNPRIZED:
										if (ticket.IsRegraded() || ticket.IsNoAction())
										{
											foreach (var wager in ticket.Wagers)
											{
												gradedWagerMessage = wager.FragmentPaymentMessage(now);
												highPriorityBuffer.Send(gradedWagerMessage);
											}

											if (ticket.Order.CurrencyCode.ToString() == ticket.Lottery.PicksLotteryGame.StandardCurrency)
                                            {
												var sequenceOfNumbers = !ticket.IsNoAction() ? ticket.Draw.PreviousSequenceOfNumber : ticket.NoActionDraw.PreviousSequenceOfNumber;
												foreach (var wager in ticket.FindWagersWithThisSequence(sequenceOfNumbers))
												{
													SendRewards(depositsBuffer, withdrawalBuffer, gameboard, ticket, wager, version, employeeName);
												}
											}
											else if (ticket.Order.CurrencyCode.ToString() == ticket.Lottery.PicksLotteryGame.RewardCurrency || ticket.Order.CurrencyCode.ToString() == Currencies.CODES.KRW.ToString())
											{
												transactionsBuffer.WriteDebitDataForNoAction(withdrawalBuffer, ticket, version, employeeName);
											}
										}
										else
										{
											throw new GameEngineException("At this point ticket must be winner/loser or no action");
										}
																				
										break;
									default:
										throw new GameEngineException("At this point ticket must be winner/loser or no action");
								}

								break;
							case Wager wager:
								version = wager.Showcase.Wagers.Version;
								switch (wager.Prizing)
								{
									case GameboardStatus.LOSER:
										if (wager.Grading != GameboardStatus.GRADED) throw new GameEngineException("At this point all tickets must be graded");
										gradedWagerMessage = wager.GenerateLoserFragmentMessage(now);
										highPriorityBuffer.Send(gradedWagerMessage);
										
										SendRewards(depositsBuffer, withdrawalBuffer, gameboard, wager, version, employeeName);

										break;
									case GameboardStatus.WINNER:
										if (wager.Grading != GameboardStatus.GRADED) throw new GameEngineException("At this point all tickets must be graded");

										gradedWagerMessage = wager.GenerateWinnerFragmentMessage(now);
										highPriorityBuffer.Send(gradedWagerMessage);

										SendRewards(depositsBuffer, withdrawalBuffer, gameboard, wager, version, employeeName);
										break;
									case GameboardStatus.UNPRIZED:
										if (wager.Grading != GameboardStatus.NOACTION) throw new GameEngineException("At this point all tickets must be graded");
										if (wager.IsNoAction())
										{
											gradedWagerMessage = wager.GenerateNoActionFragmentMessage(now);
											highPriorityBuffer.Send(gradedWagerMessage);

											SendRewards(depositsBuffer, withdrawalBuffer, gameboard, wager, version, employeeName);
										}
										else if (wager.IsRegraded())
										{
											gradedWagerMessage = wager.GenerateLoserFragmentMessage(now);
											highPriorityBuffer.Send(gradedWagerMessage);

											SendRewards(depositsBuffer, withdrawalBuffer, gameboard, wager, version, employeeName);
										}
										else
										{
											throw new GameEngineException("At this point ticket must be winner/loser or no action");
										}
										break;
									default:
										throw new GameEngineException("At this point all tickets must be graded");
								}

								break;
							default:
								break;
						}
					}
				}
			}
		}

		internal void PayRefund(bool itIsThePresent, IEnumerable<Gameboard> gameboards, DateTime now, string employeeName)
        {
			using (KafkaMessagesBuffer depositsBuffer = new KafkaMessagesBuffer(itIsThePresent, Integration.Kafka.TopicForDeposits))
			{
                foreach (var gameboard in gameboards)
                {
					var ticket = gameboard as Ticket;
					SendRefunds(depositsBuffer, ticket, employeeName);
				}
			}
		}

		internal void PayRefund(bool itIsThePresent, TicketWager wager, DateTime now, string employeeName)
		{
			using (KafkaMessagesBuffer depositsBuffer = new KafkaMessagesBuffer(itIsThePresent, Integration.Kafka.TopicForDeposits))
			{
				SendRefunds(depositsBuffer, wager, employeeName);
			}
		}

		private void PayLoserAndNoActionGameboards(bool itIsThePresent, IEnumerable<Gameboard> gameboards, DateTime now, string employeeName, bool sometimeWereTicketsSentToAccounting)
		{
			if (gameboards == null) throw new ArgumentNullException(nameof(gameboards));
			if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

			using (FragmentPaymentCompressor bufferForAll = new FragmentPaymentCompressor(itIsThePresent, Integration.Kafka.TopicForFragmentPaymentsForAll))
			using (KafkaMessagesBuffer depositsBuffer = new KafkaMessagesBuffer(itIsThePresent, Integration.Kafka.TopicForDeposits))
			using (KafkaMessagesBuffer withdrawalBuffer = new KafkaMessagesBuffer(itIsThePresent, Integration.Kafka.TopicForWithdrawals))
			{
				bufferForAll.BeforeSendTheFirstMessage = bufferForAll.GenerateHeader(employeeName, company.Sales.CurrentStore.Id, WholePaymentProcessor.NoPaymentProcessor, sometimeWereTicketsSentToAccounting, now);
				int version;
				FragmentPaymentMessage gradedWagerMessage;
				foreach (var gameboard in gameboards.Where(x => !x.IsHighPrioritySettle))
				{
					switch (gameboard)
					{
						case Ticket ticket:
							version = ticket.DrawVersion;
							switch (ticket.Prizing)
							{
								case GameboardStatus.LOSER:
									foreach (var wager in ticket.Wagers)
									{
										gradedWagerMessage = wager.FragmentPaymentMessage(now);
										bufferForAll.Send(gradedWagerMessage);

										SendRewards(depositsBuffer, withdrawalBuffer, gameboard, ticket, wager, version, employeeName);
									}
									break;
								case GameboardStatus.WINNER:
									break;
								case GameboardStatus.UNPRIZED:
									if (ticket.IsRegraded() || ticket.IsNoAction())
									{
										foreach (var wager in ticket.Wagers)
										{
											gradedWagerMessage = wager.FragmentPaymentMessage(now);
											bufferForAll.Send(gradedWagerMessage);

											SendRewards(depositsBuffer, withdrawalBuffer, gameboard, ticket, wager, version, employeeName);
										}
										if (ticket.Order.CurrencyCode.ToString() == ticket.Lottery.PicksLotteryGame.StandardCurrency)
											SendRefunds(depositsBuffer, withdrawalBuffer, ticket, version, employeeName);
									}
									else
									{
										throw new GameEngineException("At this point ticket must be winner/loser or no action");
									}
									break;
								default:
									throw new GameEngineException("At this point ticket must be winner/loser or no action");
							}
							break;
						case Wager wager:
							version = wager.Showcase.Wagers.Version;
							switch (wager.Prizing)
							{
								case GameboardStatus.LOSER:
									if (wager.Grading != GameboardStatus.GRADED) throw new GameEngineException("At this point all tickets must be graded");
									gradedWagerMessage = wager.GenerateLoserFragmentMessage(now);
									bufferForAll.Send(gradedWagerMessage);

									SendRewards(depositsBuffer, withdrawalBuffer, gameboard, wager, version, employeeName);

									break;
								case GameboardStatus.WINNER:
									if (wager.Grading != GameboardStatus.GRADED) throw new GameEngineException("At this point all tickets must be graded");
									break;
								case GameboardStatus.UNPRIZED:
									if(wager.Grading != GameboardStatus.NOACTION) throw new GameEngineException("At this point all tickets must be graded");
									if (wager.IsNoAction())
									{
										gradedWagerMessage = wager.GenerateNoActionFragmentMessage(now);
										bufferForAll.Send(gradedWagerMessage);

										SendRewards(depositsBuffer, withdrawalBuffer, gameboard, wager, version, employeeName);
									}
									else if (wager.IsRegraded())
									{
										gradedWagerMessage = wager.GenerateLoserFragmentMessage(now);
										bufferForAll.Send(gradedWagerMessage);

										SendRewards(depositsBuffer, withdrawalBuffer, gameboard, wager, version, employeeName);
									}
									else
									{
										throw new GameEngineException("At this point ticket must be winner/loser or no action");
									}
									break;
								default:
									throw new GameEngineException("At this point all tickets must be graded");
							}
							break;
						default:
							throw new GameEngineException("This gameboard has not been considered");
					}
				}
			}
		}
	}
}
