﻿using GamesEngine.Bets;
using GamesEngine.PurchaseOrders;
using Microsoft.VisualStudio.Web.CodeGeneration.Design;
using MySql.Data.MySqlClient;
using Puppeteer;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Interprete.Libraries;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CashierAPI.CashierFollower
{
    public class ExecutionErrorRule : RuleWithoutActor
    {
        protected const string START_TEXT_LINE1 = "//EXECUTION ERROR";
        protected const string START_TEXT_LINE2 = "authorization = atAddress.GetAuthorization(";
        protected const string START_TEXT_LINE2AlternativeA = "authorizations = atAddress.GetAuthorizations(";
        protected const string START_TEXT_LINE2AlternativeB = "auths = AuthorizationsNumbers(";
        protected const string START_TEXT_LINEAuthorizationsNumbersAdd = "auths.Add(";
        protected const string END_TEXT_LINE = ");";

        public override void Then(<PERSON>ript script)
        {
            if (script.Text.IndexOf(START_TEXT_LINE2) != -1)
            {
                var authorizationNumber = AuthorizationNumber(script.Text);
                var needToSkipThisError = authorizationNumber.IndexOf("78");
                if (!string.IsNullOrEmpty(authorizationNumber) && needToSkipThisError != -1)
                {
                    DBHelperCashierFollower.SkipDiaryWithExecutionError(new[] { authorizationNumber }, script.DairyId);
                }
            }
            else if (script.Text.IndexOf(START_TEXT_LINE2AlternativeB) != -1)
            {
                var authorizationNumbers = AuthorizationNumbersWithClass(script.Text);
                if (authorizationNumbers != null)
                {
                    DBHelperCashierFollower.SkipDiaryWithExecutionError(authorizationNumbers, script.DairyId);
                }
            }
            else if (script.Text.IndexOf(START_TEXT_LINE2AlternativeA) != -1)
            {
                var authorizationNumbers = AuthorizationNumbers(script.Text);
                if (authorizationNumbers!=null)
                {
                    DBHelperCashierFollower.SkipDiaryWithExecutionError(authorizationNumbers, script.DairyId);
                }
            }
        }

        public override void When(Script script)
        {
            if (script.Text.IndexOf(START_TEXT_LINE1) != -1)
            {
                Then(script);
            }

        }

        public string AuthorizationNumber(string script)
        {
            UtilStringRules.SetHeaderText(START_TEXT_LINE2, END_TEXT_LINE);
            return UtilStringRules.GetBodyArgText(script).Trim();
        }

        internal string[] AuthorizationNumbers(string script)
        {
            UtilStringRules.SetHeaderText(START_TEXT_LINE2AlternativeA, END_TEXT_LINE);
            var body = UtilStringRules.GetBodyArgText(script).Trim();

            if (body.StartsWith("{") && body.EndsWith("}"))
            {
                var result = body.TrimStart('{').TrimEnd('}').Split(',');
                return result;
            }
            else
            {
                throw new Exception("Error getting Authorizations Numbers");
            }
        }

        internal string[] AuthorizationNumbersWithClass(string script)
        {
            UtilStringRules.SetHeaderText(START_TEXT_LINE2AlternativeB, END_TEXT_LINE);
            var body = UtilStringRules.GetBodyArgText(script).Trim();

            string[] arguments = body.Split(',');
            int authorization = int.Parse(arguments[0]);
            var consecutive = arguments.Length > 1 ? int.Parse(arguments[1]) : 1;
            var authorizationNumbers = arguments.Length > 1 ? new AuthorizationsNumbers(authorization, consecutive) : new AuthorizationsNumbers(authorization);
            if (script.IndexOf(START_TEXT_LINEAuthorizationsNumbersAdd) != -1)
            {
                UtilStringRules.SetHeaderText(START_TEXT_LINEAuthorizationsNumbersAdd, END_TEXT_LINE);
                body = UtilStringRules.GetBodyArgText(script).Trim();
                arguments = body.Split(").Add(");
                foreach (var arg in arguments)
                {
                    if (int.TryParse(arg, out authorization))
                    {
                        authorizationNumbers.Add(authorization);
                    }
                    else
                    {
                        var addArgs = arg.Split(',');
                        authorization = int.Parse(addArgs[0]);
                        consecutive = addArgs.Length > 1 ? int.Parse(addArgs[1]) : 1;
                        authorizationNumbers.Add(authorization, consecutive);
                    }
                }
            }

            return authorizationNumbers.Numbers.Select(n => n.ToString()).ToArray();
        }

        public override void FinalizeRule()
        {
            return;
        }
    }
}
