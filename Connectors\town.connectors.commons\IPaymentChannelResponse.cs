﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Connectors.town.connectors.commons
{
    public interface IPaymentChannelResponse
    {
        string Url { get; }
        AuthorizationResponseCode Code { get; }
        string Response { get; }
    }

    public enum AuthorizationResponseCode
    {
        OK = 0,
        AuthorizationFail = 1,
        UnexpectedFormat = 2,
        InsufficientFunds = 101
    }
}
