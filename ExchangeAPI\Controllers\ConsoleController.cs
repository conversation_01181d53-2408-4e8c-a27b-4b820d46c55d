using Connectors.town.connectors.drivers;
using GamesEngine;
using GamesEngine.Business;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using town.connectors.drivers.artemis;
using static GamesEngine.Finance.PaymentChannels;

namespace ExchangeAPI.Controllers
{
    public class ConsoleController : AuthorizeController
    {
        [HttpPost("console/command")]
        [Authorize(Roles = "devops")]
        public async Task<IActionResult> processCommandAsync()
        {
            string body = "";

            using (StreamReader reader = new StreamReader(Request.Body, Encoding.UTF8))
            {
                body = await reader.ReadToEndAsync();
            }

            if (body == null) return NotFound("Body is required");
            if (String.IsNullOrWhiteSpace(body)) return NotFound($"Parameter {nameof(body)} is required");

            var result = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, body);
            return result;
        }

        [HttpPost("console/query")]
        [Authorize(Roles = "devops")]
        public async Task<IActionResult> processQueryAsync()
        {
            string body = "";

            using (StreamReader reader = new StreamReader(Request.Body, Encoding.UTF8))
            {
                body = await reader.ReadToEndAsync();
            }

            if (body == null) return NotFound("Body is required");
            if (String.IsNullOrWhiteSpace(body)) return NotFound($"Parameter {nameof(body)} is required");

            var result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, body);
            return result;
        }

        [HttpGet("producer/stop")]
        [AllowAnonymous]
        public async Task<IActionResult> StopProducerAsync()
        {
			await Consumer.StopAllConsumerActiveInMemoryAsync();

			IProducer kafka = Integration.Kafka;
            if (kafka != null)
            {
                await Integration.Kafka.StopProducerAsync();
                return Ok();
            }
            return BadRequest("Kafka is not configured.");
        }

        [HttpGet("consumer/stop")]
        [AllowAnonymous]
        public async Task<IActionResult> StopConsumerAsync()
        {
            await Consumer.StopAllConsumerActiveInMemoryAsync();
            return Ok("All consumers are stopped!");
        }

        [HttpPost("consumer/reset")]
        [Authorize(Roles = "devops")]
        public async Task<IEnumerable<int>> ResetAsync(int[] consumerIds)
        {
            return await Consumer.ResetAsync(consumerIds);
        }

        [HttpGet("consumers")]
        [Authorize(Roles = "devops")]
        public List<string> List()
        {
            return Consumer.ConsumersInfo();
        }

        [HttpGet("consumer/start")]
        [AllowAnonymous]
        public IActionResult StartConsumer()
        {
            Integration.UseKafka = true;
            if (Integration.UseKafka)
            {
                new Consumers().CreateConsumerForTopics();
            }
            return Ok("Consumers restarted!");
        }

        const string ClientExchange = "exchange";
        [HttpGet("api/security/roles")]
        [Authorize(Roles = "ViewUsers,d1")]
        public async Task<IActionResult> GetRolesAsync()
        {
            return Ok(await Security.ClerksAPI().AvailableClientRolesAsync(ClientExchange));
        }

        User[] FakeUsers()
        {
            User user1 = new UserBody
            {
                Id = "1",
                Username = "user1",
                Email = "<EMAIL>",
                Enabled = true,
                FirstName = "John",
                LastName = "Doe",
                UserTypeName = "Type1",
                Credentials = new Credentials[]
    {
        new Credentials
        {
            Type = "Type1",
            Value = "Value1",
            Temporary = "No"
        },
        new Credentials
        {
            Type = "Type2",
            Value = "Value2",
            Temporary = "Yes"
        }
    },
                Attributes = new Attributes
                {
                    Domains = new string[] { "example.com" },
                    Path = new string[] { "CR/1" },
                    Agent = new string[] { "agent1", "agent2" },
                    TenantId = new string[] { "tenant1", "tenant2" },
                    ProfileId = new string[] { "profile1", "profile2" },
                    UserTypeName = new string[] { "Type1", "Type2" }
                }
            };

            User user2 = new UserBody
            {
                Id = "2",
                Username = "user2",
                Email = "<EMAIL>",
                Enabled = true,
                FirstName = "Jane",
                LastName = "Smith",
                UserTypeName = "Type2",
                Credentials = new Credentials[]
                {
        new Credentials
        {
            Type = "Type1",
            Value = "Value1",
            Temporary = "No"
        }
                },
                Attributes = new Attributes
                {
                    Domains = new string[] { "example.com", "example.org" },
                    Path = new string[] { "CR/2" },
                    Agent = new string[] { "agent1" },
                    TenantId = new string[] { "tenant1" },
                    ProfileId = new string[] { "profile1" },
                    UserTypeName = new string[] { "Type1" }
                }
            };

            User user3 = new UserBody
            {
                Id = "3",
                Username = "user3",
                Enabled = false,
                FirstName = "Alex",
                LastName = "Johnson",
                UserTypeName = "Type3",
                Credentials = new Credentials[]
                {
        new Credentials
        {
            Type = "Type1",
            Value = "Value1",
            Temporary = "No"
        },
        new Credentials
        {
            Type = "Type2",
            Value = "Value2",
            Temporary = "No"
        }
                },
                Attributes = new Attributes
                {
                    Domains = new string[] { "example.com", "example.net" },
                    Path = new string[] { },
                    Agent = new string[] { "agent1", "agent2", "agent3" },
                    TenantId = new string[] { "tenant1", "tenant2" },
                    ProfileId = new string[] { "profile1" },
                    UserTypeName = new string[] { "Type2" }
                }
            };

            User user4 = new UserBody
            {
                Id = "4",
                Username = "user4",
                Email = "<EMAIL>",
                Enabled = true,
                FirstName = "Emily",
                LastName = "Brown",
                UserTypeName = "Type1",
                Credentials = new Credentials[]
                {
        new Credentials
        {
            Type = "Type1",
            Value = "Value1",
            Temporary = "No"
        },
        new Credentials
        {
            Type = "Type2",
            Value = "Value2",
            Temporary = "Yes"
        },
        new Credentials
        {
            Type = "Type3",
            Value = "Value3",
            Temporary = "No"
        }
                },
                Attributes = new Attributes
                {
                    Domains = new string[] { "example.com" },
                    Path = new string[] { "CR/1/path2" },
                    Agent = new string[] { "agent1", "agent2" },
                    TenantId = new string[] { "tenant1" },
                    ProfileId = new string[] { "profile1", "profile2" },
                    UserTypeName = new string[] { "Type1", "Type3" }
                }
            };

            User user5 = new UserBody
            {
                Id = "5",
                Username = "user5",
                Email = "<EMAIL>",
                Enabled = true,
                FirstName = "",
                LastName = "",
                UserTypeName = "Type2",
                Credentials = new Credentials[]
                {
        new Credentials
        {
            Type = "Type1",
            Value = "Value1",
            Temporary = "No"
        },
        new Credentials
        {
            Type = "Type3",
            Value = "Value3",
            Temporary = "No"
        }
                },
                Attributes = new Attributes
                {
                    Domains = new string[] { "example.org" },
                    Path = new string[] { "CR/1/path1" },
                    Agent = new string[] { "agent1", "agent2" },
                    TenantId = new string[] { "tenant1", "tenant2" },
                    ProfileId = new string[] { "profile1" },
                    UserTypeName = new string[] { "Type2", "Type3" }
                }
            };
            return new User[] { user1, user2, user3, user4, user5 };
        }

        [HttpGet("api/security/users")]
        [Authorize(Roles = "ViewUsers,d2")]
        public async Task<IActionResult> GetInternalUsersAsync(string search, int first, int max)
        {
            var result = await Security.ClerksAPI().GetAgentsAsync(ClientExchange, search, first, max);

            if (result is OkObjectResult)
            {
                string jsonResult = (result as ObjectResult).Value.ToString();
                User[] users = JsonConvert.DeserializeObject<User[]>(jsonResult);//FakeUsers();
                string path = Security.UserPath(HttpContext);
                var internalUsers = users.Where(x => x.Attributes?.Path?.Length > 0 &&
                        x.Attributes.Path.First().StartsWith(path)
                    );

                return Ok(internalUsers);
            }
            return result;
        }

        [HttpGet("api/security/users/count")]
        [Authorize(Roles = "ViewUsers,d3")]
        public async Task<IActionResult> CountInternalUsersAsync()
        {
            return await Security.ClerksAPI().CountUsersAsync(ClientExchange);
        }

        [HttpPost("api/security/users")]
        [Authorize(Roles = "ViewUsers,d4")]
        public async Task<IActionResult> CreateUserAsync([FromBody]UserInternal user, [FromHeader(Name = "domain-url")] string domain)
        {
            if (string.IsNullOrWhiteSpace(HttpContext.Request.Headers["domain-url"])) return BadRequest("Header 'domain-url' is not sent in request");

            User userKc = new User()
			{
				Id = user.Id,
				Username = user.Username,
				Email = user.Email,
				Enabled = user.Enabled,
				FirstName = user.FirstName,
				LastName = user.LastName,
				Credentials = user.Credentials
			};

			User securedUser = await GetUserFromSecurityServerAsync(userKc);
            if (securedUser != null) return BadRequest($@"{{""type"":""error"",""message"":""User was not created because username {user.Username} was already taken.""}}");

            User emailSecuredUser = await GetUserFromEmailSecurityServerAsync(userKc);
            if (emailSecuredUser != null) return BadRequest($@"{{""type"":""error"",""message"":""User was not created because email {user.Email} was already taken.""}}");
            
			var result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
                {{
					agent = marketplace.SearchAgent('{user.AgencyName}');
					print agent.HasAgent('{user.Username}') hasAgent;
                    domain = company.Sales.DomainFrom('{domain}');
                    print domain.AgentId tenantId;
                }}
            ");

			if (!(result is OkObjectResult))
			{
                if (result is ContentResult contentResult) throw new GameEngineException($"Error: {contentResult.Content}");
                if (result is ObjectResult objResult) throw new GameEngineException($"Error: {objResult.Value}");
                throw new GameEngineException($@"Error:{result}");
			}

			OkObjectResult o = (OkObjectResult)result;
			string json = o.Value.ToString();
			ExistAgent existAgent = JsonConvert.DeserializeObject<ExistAgent>(json);

			if (existAgent.HasAgent)
			{
				ApiError error = new ApiError($"User was not created because user name or email was already taken.",
					"No stack.",
					"User name or email was already taken.",
					"No Error info.");

				var contentResult = new ContentResult
				{
					ContentType = "application/json",
					Content = JsonConvert.SerializeObject(error),
					StatusCode = 400
				};
				return contentResult;
			}

			result = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, $@"
				agent = marketplace.SearchAgent('{user.AgencyName}');
				child = agent.AddUser('{user.UserTypeName}', '{user.Username}');
                profiles = guardian.Profiles();
				profile = profiles.SearchById({user.ProfileId});
                agent.AssignProfile(child, profile);
				print agent.FullName path;
			");
            if (!(result is OkObjectResult))
            {
                return result;
            }
            o = (OkObjectResult)result;
			json = o.Value.ToString();
			Agent agent = JsonConvert.DeserializeObject<Agent>(json);

			userKc.Attributes = new Attributes()
			{
				Path = new[] { agent.AgentPath },
                Agent = new[] { user.Agent },
				Spaces = new[] { user.Spaces },
                TenantId = new[] { existAgent.TenantId.ToString() },
                ProfileId = new[] { user.ProfileId.ToString() },
                UserTypeName = new[] { user.UserTypeName.ToString() }
            };
            
			result = await Security.ClerksAPI().CreateAgentsAsync(ClientExchange, userKc);

            if (result is OkObjectResult)
            {
                User userToReturn = await GetUserFromSecurityServerAsync(userKc);
                if (userToReturn != null)
                {
                    Password pass = new Password();
                    pass.Type = "password";
                    pass.Value = user.Credentials[0].Value;
                    pass.Temporary = true;

                    result = await ResetPasswordAsync(userToReturn.Id, pass);
                    if (result is OkObjectResult)
                    {
                        return Ok(userToReturn);
                    }
                    return result;
                }
                else
                {
                    return BadRequest($@"User {JsonConvert.SerializeObject(user)} was not created.");
                }
            }
            return result;
        }

        [HttpGet("api/accountingservices/amountOfWagersPerChunk")]
        [Authorize(Roles = "devops")]
        public int GetAmountOfWagerPerChunk()
        {
            var paymentProcessor = WholePaymentProcessor.Instance().SearchFragmentProcessor();
            return (paymentProcessor.Driver as IFragmentDriver).AmountOfWagersPerChunk;
        }

        [HttpPut("api/accountingservices/amountOfWagersPerChunk")]
        [Authorize(Roles = "devops")]
        public int ChangeAmountOfWagerPerChunk([FromBody]AmountOfWagersPerChunkBody body)
        {
            var paymentProcessors = WholePaymentProcessor.Instance().SearchFragmentProcessors();
            foreach (var processor in paymentProcessors)
            {
                (processor.Driver as IFragmentDriver).AmountOfWagersPerChunk = body.AmountOfWagersPerChunk;
            }

            return body.AmountOfWagersPerChunk;
        }

        [HttpGet("api/artemis/accountingservices/gradedWagersPerChunk")]
        [Authorize(Roles = "devops")]
        public int GetArtemisAmountOfWagerPerChunk()
        {
            var paymentProcessor = WholePaymentProcessor.Instance().SearchGradeProcessorBy(typeof(Connectors.town.connectors.drivers.artemis.Grade));
            return ((DGSTenantDriver)paymentProcessor.Driver).GradedWagersPerChunk;
        }

        [HttpPut("api/artemis/accountingservices/gradedWagersPerChunk")]
        [Authorize(Roles = "devops")]
        public int ChangeArtemisAmountOfWagerPerChunk([FromBody] AmountOfWagersPerChunkBody body)
        {
            var paymentProcessor = WholePaymentProcessor.Instance().SearchGradeProcessorBy(typeof(Connectors.town.connectors.drivers.artemis.Grade));
            ((DGSTenantDriver)paymentProcessor.Driver).GradedWagersPerChunk = body.AmountOfWagersPerChunk;
            paymentProcessor = WholePaymentProcessor.Instance().SearchGradeProcessorBy(typeof(Connectors.town.connectors.drivers.artemis.ReGrade));
            ((DGSTenantDriver)paymentProcessor.Driver).GradedWagersPerChunk = body.AmountOfWagersPerChunk;

            return body.AmountOfWagersPerChunk;
        }

        private async Task<User> GetUserFromSecurityServerAsync(User user)
        {
            var result = await Security.ClerksAPI().GetByUsernameAsync(ClientExchange, user);
            string jsonResult = (result as ObjectResult).Value.ToString();
            User[] users = Newtonsoft.Json.JsonConvert.DeserializeObject<User[]>(jsonResult);
            foreach (User userToCheck in users)
            {
                if (userToCheck.Username.ToLower().Equals(user.Username.ToLower())) return userToCheck;
            }
            return null;
        }

        private async Task<User> GetUserFromEmailSecurityServerAsync(User user)
        {
            var result = await Security.ClerksAPI().GetByEmailAsync(ClientExchange, user);
            string jsonResult = (result as ObjectResult).Value.ToString();
            User[] users = Newtonsoft.Json.JsonConvert.DeserializeObject<User[]>(jsonResult);
            foreach (User userToCheck in users)
            {
                if (userToCheck.Email.ToLower().Equals(user.Email.ToLower())) return userToCheck;
            }
            return null;
        }

        [HttpGet("api/security/users/{userID}")]
        [Authorize(Roles = "ViewUsers,d5")]
        public async Task<IActionResult> GetUserAsync(string userID)
        {
            return await Security.ClerksAPI().GetAgentsAsync(ClientExchange, userID);
        }

        [HttpGet("api/security/users/{userID}/roles")]
        [Authorize(Roles = "ViewUsers,d6")]
        public async Task<IActionResult> GetRolesOFUserAsync(string userID)
        {
            var roles = await Security.ClerksAPI().GetRolesOFUserAsync(ClientExchange, userID);
            return Ok(roles);
        }

        [HttpPost("api/security/users/{userID}/roles")]
        [Authorize(Roles = "AssignRoles,d7")]
        public async Task<IActionResult> MapRoleToUsersAsync(string userID, [FromBody] RoleMapping[] roleMapping)
        {
            return await Security.ClerksAPI().MapRoleToUsersAsync(ClientExchange, userID, roleMapping);
        }

        [HttpPost("api/security/users/{userID}/logout")]
        [AllowAnonymous]
        public async Task<IActionResult> LogoutAsync(string userID)
        {
            var result = await Security.ClerksAPI().GetAgentsAsync(ClientExchange, userID);
            if (result is OkObjectResult)
            {
                string jsonResult = (result as ObjectResult).Value.ToString();
                User user = Newtonsoft.Json.JsonConvert.DeserializeObject<User>(jsonResult);

                result = await Security.ClerksAPI().LogOutAsync(ClientExchange, userID);
                if (result is OkObjectResult)
                {
                    LogOutResponse output = new LogOutResponse();
                    output.HasEmail = user.HasEmail;
                    return Ok(output);
                }
            }

            return result;
        }

        [HttpPut("api/security/users/{userID}/rolesremoval")]
        [Authorize(Roles = "AssignRoles,d8")]
        public async Task<IActionResult> MapRoleToUsersDeleteAsync(string userID, [FromBody] RoleMapping[] roleMapping)
        {
            return await Security.ClerksAPI().DeleteMapRoleToUsersAsync(ClientExchange, userID, roleMapping);
        }

        [HttpPut("api/security/users/{userID}")]
        [Authorize(Roles = "AssignRoles,d9")]
        public async Task<IActionResult> UpdateUserAsync(string userID, [FromBody]UserBody user)
        {
            if (user == null) return BadRequest($"Body is required");
            if (string.IsNullOrWhiteSpace(userID)) return BadRequest($"Parameter {nameof(userID)} is required");
            if (user.Attributes == null) return BadRequest($"{nameof(user.Attributes)} is required");
            if (user.Attributes.Path.Length != 1) return BadRequest($"Only 1 '{nameof(user.Attributes.Path)}' is expected");
            if (user.Attributes.ProfileId.Length != 1) return BadRequest($"Only 1 '{nameof(user.Attributes.ProfileId)}' is expected");
            if (string.IsNullOrWhiteSpace(user.UserTypeName)) return BadRequest($"'{nameof(user.UserTypeName)}' is required");

            User currentUser = await GetUserFromSecurityServerAsync(user);  //SecurityConfiguration.GetFakeUser(); await GetUserFromSecurityServerAsync(user);
            if (currentUser != null)
            {
                if (currentUser.Email != user.Email)
                {
                    User emailSecuredUser = await GetUserFromEmailSecurityServerAsync(user);

                    if (emailSecuredUser != null) return BadRequest($@"{{""type"":""error"",""message"":""User was not updated because email {user.Email} was already taken.""}}");
                }
            }

            IActionResult result = Ok();
            var profileId = user.Attributes.ProfileId.First();
            var profileChanged = !currentUser.Attributes.ProfileId.First().Equals(profileId, StringComparison.OrdinalIgnoreCase);
            var userTypeName = user.UserTypeName;
            var userTypeNameChanged = !currentUser.Attributes.UserTypeName.First().Equals(userTypeName, StringComparison.OrdinalIgnoreCase);
            var usernameChanged = !currentUser.Username.Equals(user.Username, StringComparison.OrdinalIgnoreCase);
            if (profileChanged || usernameChanged || userTypeNameChanged)
            {
                var commandToChangeName = usernameChanged ? $"child.Name = '{user.Username}';" : string.Empty;
                var commandToChangeUserTypeName = userTypeNameChanged ? $"agent.ChangeUserType('{userTypeName}', child);" : string.Empty;

                var agentPath = user.Attributes.Path.First();
                result = await ExchangeManagerAPI.ExchangeManager.PerformChkThenQryAsync(HttpContext, $@"
                    existsAgent = marketplace.ExistsAgent('{agentPath}');
                    Check(existsAgent) Error 'Agent path {agentPath} does not exist';
                ", $@"
                {{
				    agent = marketplace.SearchAgent('{agentPath}');
                    existsCashier = agent.ExistsCashier('{currentUser.Username}');
                    existsManager = agent.ExistsManager('{currentUser.Username}');
                    print existsCashier existsCashier;
                    print existsManager existsManager;
                    if (existsCashier || existsManager) 
                    {{
                        child = agent.SearchUser('{currentUser.Username}');
                        print child.HasProfile hasProfile;
                    }} 
                    else 
                    {{
                        print false hasProfile;
                    }}
                }}
                ");
                if (!(result is OkObjectResult)) return result;
                var o = (OkObjectResult)result;
                string json = o.Value.ToString();
                var userTypeExistence = JsonConvert.DeserializeObject<UserTypeExistence>(json);

                var commandToGetOrAddUser = (userTypeExistence.ExistsCashier || userTypeExistence.ExistsManager) ? 
                    $"child = agent.SearchUser('{currentUser.Username}');" : 
                    $"child = agent.AddUser('{userTypeName}', '{currentUser.Username}');";
                var commandToChangeProfile = profileChanged ? $@"
                    profiles = guardian.Profiles();
				    profile = profiles.SearchById({profileId});
                " : string.Empty;
                if (profileChanged) commandToChangeProfile += userTypeExistence.HasProfile ? "agent.ReassignProfile(child, profile);" : "agent.AssignProfile(child, profile);";

                result = await ExchangeManagerAPI.ExchangeManager.PerformChkThenCmdAsync(HttpContext, $@"
                    existsAgent = marketplace.ExistsAgent('{agentPath}');
                    Check(existsAgent) Error 'Agent path {agentPath} does not exist';
                ", $@"
				    agent = marketplace.SearchAgent('{agentPath}');
				    {commandToGetOrAddUser}
                    {commandToChangeName}
                    {commandToChangeProfile}
                    {commandToChangeUserTypeName}
			    ");

                if (!(result is OkObjectResult)) return result;
            }

            var userBody = new User
            {
                Id = user.Id,
                Username = user.Username,
                Email = user.Email,
                Enabled = user.Enabled,
                FirstName = user.FirstName,
                LastName = user.LastName,
                Credentials = user.Credentials,
                Attributes = user.Attributes
            };
            result = await Security.ClerksAPI().UpdateUserAsync(ClientExchange, userID, userBody);
            return result;
        }

        [HttpPut("api/security/users/{userID}/reset-password")]
        [Authorize(Roles = "AssignRoles,d10")]
        public async Task<IActionResult> ResetPasswordAsync(string userID, [FromBody]Password pass)
        {
            return await Security.ClerksAPI().UpdatePasswordAsync(ClientExchange, userID, pass);
        }


        [HttpDelete("api/security/users/{userID}")]
        [Authorize(Roles = "DeleteUser,d11")]
        public async Task<IActionResult> DeleteUserAsync(string userID)
        {
            return await Security.ClerksAPI().DeleteUserAsync(ClientExchange, userID);
        }

        [HttpGet("console/ping")]
        [AllowAnonymous]
        public IActionResult Ping()
        {
            return Ok("pong");
        }

        [HttpGet("api/userTypes")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> UserTypesAsync()
        {
            IActionResult result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
				{{
					for( userTypes : marketplace.UserTypes )
					{{
						print userTypes.Id id;
						print userTypes.Name name;
					}}
				}}
				");
            if (!(result is OkObjectResult))
            {
                throw new GameEngineException($@"Error:{((ObjectResult)result).Value}");
            }

            return result;
        }

        [HttpGet("api/profiles")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> ListProfilesAsync()
        {
            IActionResult result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
				{{
					profilesStored = guardian.Profiles();
					for( profiles : profilesStored.List() )
					{{
						print profiles.Id id;
						print profiles.Name name;
						print profiles.Emails emails;
					}}
				}}
				");
            if (!(result is OkObjectResult))
            {
                throw new GameEngineException($@"Error:{((ObjectResult)result).Value.ToString()}");
            }

            return result;
        }

        [DataContract]
        public class UserTypeExistence
        {
            [DataMember(Name = "existsCashier")]
            public bool ExistsCashier { get; set; }
            [DataMember(Name = "existsManager")]
            public bool ExistsManager { get; set; }
            [DataMember(Name = "hasProfile")]
            public bool HasProfile { get; set; }
        }
    }
}

