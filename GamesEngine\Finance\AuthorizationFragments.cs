﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Finance
{
	sealed internal class AuthorizationFragments
	{
		private readonly Fragments fragments;

		internal AuthorizationFragments(Authorization authorization, string reference, string description, decimal toWin)
		{
			if (authorization == null) throw new ArgumentNullException(nameof(authorization));
			if (string.IsNullOrWhiteSpace(reference)) throw new ArgumentNullException(nameof(reference));
			if (string.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));
			if (toWin <= 0) throw new GameEngineException("To win must be upper than zero");
			if (!authorization.Amount.ItsZero()) throw new GameEngineException("This constructor is only valid for authorizations with value zero");

			fragments = new Fragments(1, 1);
			AddFragmentWithRiskZero(authorization, authorization.Amount, reference, description, toWin);
		}

		internal AuthorizationFragments(Authorization authorization, int lowerId, int higherId, Currency amountOfEachFragment, IEnumerable<string> references, IEnumerable<string> descriptions, decimal risk, decimal toWin)
		{
			if (authorization == null) throw new ArgumentNullException(nameof(authorization));
			if (higherId < lowerId) throw new GameEngineException($"Low consetive {lowerId} can not be higher than highest {higherId}");
			if (amountOfEachFragment.Value <= 0) throw new GameEngineException($"Amount per fragment {amountOfEachFragment} must be greater than zero");
			decimal total = (higherId - lowerId + 1) * amountOfEachFragment.Value;
			if (authorization.Amount.Value != total) throw new GameEngineException($"The sumatory of all fragments is {total} and is not the same as requested in the authorization of {authorization.Amount.Value}.");
			if (references == null) throw new ArgumentNullException(nameof(references));
			if(descriptions == null) throw new ArgumentNullException(nameof(descriptions));
			if (risk <= 0) throw new GameEngineException("Risk must be upper than zero");
			if (toWin <= 0) throw new GameEngineException("To win must be upper than zero");

			fragments = new Fragments(lowerId, higherId);
			Add(authorization, lowerId, higherId, amountOfEachFragment, references, descriptions, risk, toWin);
		}

		internal AuthorizationFragments(Authorization authorization, int lowerId, int higherId, Currency amountOfEachFragment, IEnumerable<string> references, IEnumerable<string> descriptions, decimal risk, IEnumerable<decimal> toWins)
		{
			if (authorization == null) throw new ArgumentNullException(nameof(authorization));
			if (higherId < lowerId) throw new GameEngineException($"Low consetive {lowerId} can not be higher than highest {higherId}");
			if (amountOfEachFragment.Value <= 0) throw new GameEngineException($"Amount per fragment {amountOfEachFragment} must be greater than zero");
			decimal total = (higherId - lowerId + 1) * amountOfEachFragment.Value;
			if (authorization.Amount.Value != total) throw new GameEngineException($"The sumatory of all fragments is {total} and is not the same as requested in the authorization of {authorization.Amount.Value}.");
			if (references == null) throw new ArgumentNullException(nameof(references));
			if (descriptions == null) throw new ArgumentNullException(nameof(descriptions));
			if (risk <= 0) throw new GameEngineException("Risk must be upper than zero");

			fragments = new Fragments(lowerId, higherId);
			Add(authorization, lowerId, higherId, amountOfEachFragment, references, descriptions, risk, toWins);
		}

		internal AuthorizationFragments(Authorization authorization, int lowerId, int higherId, Coin currencyCode, IEnumerable<string> references, IEnumerable<string> descriptions, IEnumerable<decimal> risks, decimal toWin)
		{
			if (authorization == null) throw new ArgumentNullException(nameof(authorization));
			if (higherId < lowerId) throw new GameEngineException($"Low consetive {lowerId} can not be higher than highest {higherId}");
			if (references == null) throw new ArgumentNullException(nameof(references));
			if (descriptions == null) throw new ArgumentNullException(nameof(descriptions));
			if (toWin <= 0) throw new GameEngineException("To win must be upper than zero");

			fragments = new Fragments(lowerId, higherId);
			Add(authorization, lowerId, higherId, currencyCode, references, descriptions, risks, toWin);
		}

		internal AuthorizationFragments(Authorization authorization, int lowerId, int higherId, Coin currencyCode, IEnumerable<string> references, IEnumerable<string> descriptions, IEnumerable<decimal> risks, IEnumerable<decimal> toWins)
		{
			if (authorization == null) throw new ArgumentNullException(nameof(authorization));
			if (higherId < lowerId) throw new GameEngineException($"Low consetive {lowerId} can not be higher than highest {higherId}");
			if (references == null) throw new ArgumentNullException(nameof(references));
			if (descriptions == null) throw new ArgumentNullException(nameof(descriptions));

			fragments = new Fragments(lowerId, higherId);
			Add(authorization, lowerId, higherId, currencyCode, references, descriptions, risks, toWins);
		}

		internal AuthorizationFragments(Authorization authorization, int lowerId, int higherId, Currency amountOfEachFragment, IEnumerable<PartialFragment> fragmentInfo)
		{
			if (authorization == null) throw new ArgumentNullException(nameof(authorization));
			if (higherId < lowerId) throw new GameEngineException($"Low consetive {lowerId} can not be higher than highest {higherId}");
			if (amountOfEachFragment.Value <= 0) throw new GameEngineException($"Amount per fragment {amountOfEachFragment} must be greater than zero");
			decimal total = (higherId - lowerId + 1) * amountOfEachFragment.Value;
			if (authorization.Amount.Value != total) throw new GameEngineException($"The sumatory of all fragments is {total} and is not the same as requested in the authorization of {authorization.Amount.Value}.");

			fragments = new Fragments(lowerId, higherId);
			for (int i = lowerId; i <= higherId; i++)
			{
				var info = fragmentInfo.First(x => x.FragmentId == i);
				var fragment = new AuthorizationFragment(authorization, i, amountOfEachFragment, info.Reference, info.Description, info.Risk, info.ToWin, info.Status, info.Reason);
				fragments.Insert(fragment);
			}
		}

		internal AuthorizationFragments(int lowerId, int higherId)
		{
			fragments = new Fragments(lowerId, higherId);
		}

		internal void AddFragmentWithRiskZero(Authorization authorization, Currency amountOfEachFragment, string reference, string description, decimal toWin)
		{
			if (authorization == null) throw new ArgumentNullException(nameof(authorization));
			if (amountOfEachFragment == null) throw new ArgumentNullException(nameof(amountOfEachFragment));
			if (string.IsNullOrWhiteSpace(reference)) throw new ArgumentNullException(nameof(reference));
			if (string.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));
			if (toWin <= 0) throw new GameEngineException("To win must be upper than zero");

			var fragment = new AuthorizationFragment(authorization, 1, amountOfEachFragment, reference, description, toWin);
			fragments.Insert(fragment);
		}

		internal void Add(Authorization authorization, int lowerId, int higherId, Currency amountOfEachFragment, IEnumerable<string> references, IEnumerable<string> descriptions, decimal risk, decimal toWin)
		{
			if (authorization == null) throw new ArgumentNullException(nameof(authorization));
			if (higherId < lowerId) throw new GameEngineException($"Low consetive {lowerId} can not be higher than highest {higherId}");
			if (amountOfEachFragment.Value <= 0) throw new GameEngineException($"Amount per fragment {amountOfEachFragment} must be greater than zero");
			if (references == null) throw new ArgumentNullException(nameof(references));
			if (descriptions == null) throw new ArgumentNullException(nameof(descriptions));
			if (risk <= 0) throw new GameEngineException("Risk must be upper than zero");
			if (toWin <= 0) throw new GameEngineException("To win must be upper than zero");

			var fragmentReferences = references.ToArray();
			if (fragmentReferences.Length != higherId - lowerId + 1) throw new GameEngineException($"References length {fragmentReferences.Length} can not be upper or lower than low consecutive and high consecutive");
			var fragmentDescriptions = descriptions.ToArray();
			if (fragmentDescriptions.Length != higherId - lowerId + 1) throw new GameEngineException($"Descriptions length {fragmentDescriptions.Length} can not be upper or lower than low consecutive and high consecutive");

			var fragmentsToBeAdded = new List<AuthorizationFragment>();
			for (int i = lowerId; i <= higherId; i++)
			{
				var reference = fragmentReferences[i - 1];
				var description = fragmentDescriptions[i - 1];
				var fragment = new AuthorizationFragment(authorization, i + fragments.LastIndex, amountOfEachFragment, reference, description, risk, toWin);
				fragmentsToBeAdded.Add(fragment);
			}
			fragments.Insert(fragmentsToBeAdded);
		}

		internal void Add(Authorization authorization, int lowerId, int higherId, Currency amountOfEachFragment, IEnumerable<string> references, IEnumerable<string> descriptions, decimal risk, IEnumerable<decimal> toWins)
		{
			if (authorization == null) throw new ArgumentNullException(nameof(authorization));
			if (higherId < lowerId) throw new GameEngineException($"Low consetive {lowerId} can not be higher than highest {higherId}");
			if (amountOfEachFragment.Value <= 0) throw new GameEngineException($"Amount per fragment {amountOfEachFragment} must be greater than zero");
			if (references == null) throw new ArgumentNullException(nameof(references));
			if (descriptions == null) throw new ArgumentNullException(nameof(descriptions));
			if (risk <= 0) throw new GameEngineException("Risk must be upper than zero");

			var fragmentReferences = references.ToArray();
			if (fragmentReferences.Length != higherId - lowerId + 1) throw new GameEngineException($"References length {fragmentReferences.Length} can not be upper or lower than low consecutive and high consecutive");
			var fragmentDescriptions = descriptions.ToArray();
			if (fragmentDescriptions.Length != higherId - lowerId + 1) throw new GameEngineException($"Descriptions length {fragmentDescriptions.Length} can not be upper or lower than low consecutive and high consecutive");
			var fragmenToWins = toWins.ToArray();
			if (fragmenToWins.Length != higherId - lowerId + 1) throw new GameEngineException($"Towins length {fragmenToWins.Length} can not be upper or lower than low consecutive and high consecutive");

			List<AuthorizationFragment> fragmentsToBeAdded = new List<AuthorizationFragment>();
			for (int i = lowerId; i <= higherId; i++)
			{
				var reference = fragmentReferences[i - 1];
				var description = fragmentDescriptions[i - 1];
				var toWin = fragmenToWins[i - 1];
				if (toWin <= 0) throw new GameEngineException("To win must be upper than zero.");

				var fragment = new AuthorizationFragment(authorization, i + fragments.LastIndex, amountOfEachFragment, reference, description, risk, toWin);
				fragmentsToBeAdded.Add(fragment);
			}

			fragments.Insert(fragmentsToBeAdded);
		}

		internal void Add(Authorization authorization, int lowerId, int higherId, Coin currencyCode, IEnumerable<string> references, IEnumerable<string> descriptions, IEnumerable<decimal> risks, decimal toWin)
		{
			if (authorization == null) throw new ArgumentNullException(nameof(authorization));
			if (higherId < lowerId) throw new GameEngineException($"Low consetive {lowerId} can not be higher than highest {higherId}");
			if (references == null) throw new ArgumentNullException(nameof(references));
			if (descriptions == null) throw new ArgumentNullException(nameof(descriptions));
			if (toWin <= 0) throw new GameEngineException("To win must be upper than zero");

			var fragmentReferences = references.ToArray();
			if (fragmentReferences.Length != higherId - lowerId + 1) throw new GameEngineException($"References length {fragmentReferences.Length} can not be upper or lower than low consecutive and high consecutive");
			var fragmentDescriptions = descriptions.ToArray();
			if (fragmentDescriptions.Length != higherId - lowerId + 1) throw new GameEngineException($"Descriptions length {fragmentDescriptions.Length} can not be upper or lower than low consecutive and high consecutive");

			var fragmenToRisk = risks.ToArray();
			List<AuthorizationFragment> fragmentsToBeAdded = new List<AuthorizationFragment>();
			decimal total = 0;
			for (int i = lowerId; i <= higherId; i++)
			{
				decimal risk = fragmenToRisk[i - 1];
				decimal amountOfEachFragment = risk;
				if (amountOfEachFragment <= 0) throw new GameEngineException($"Amount per fragment {amountOfEachFragment} must be greater than zero");
				total = total + amountOfEachFragment;
				if (risk <= 0) throw new GameEngineException("Risk must be upper than zero");

				var reference = fragmentReferences[i - 1];
				var description = fragmentDescriptions[i - 1];
				var fragment = new AuthorizationFragment(authorization, i + fragments.LastIndex, Currency.Factory(currencyCode.Iso4217Code, amountOfEachFragment), reference, description, risk, toWin);
				fragmentsToBeAdded.Add(fragment);
			}

			fragments.Insert(fragmentsToBeAdded);
		}

		internal void Add(Authorization authorization, int lowerId, int higherId, Coin currencyCode, IEnumerable<string> references, IEnumerable<string> descriptions, IEnumerable<decimal> risks, IEnumerable<decimal> toWins)
		{
			if (authorization == null) throw new ArgumentNullException(nameof(authorization));
			if (higherId < lowerId) throw new GameEngineException($"Low consetive {lowerId} can not be higher than highest {higherId}");
			if (references == null) throw new ArgumentNullException(nameof(references));
			if (descriptions == null) throw new ArgumentNullException(nameof(descriptions));

			var fragmentReferences = references.ToArray();
			if (fragmentReferences.Length != higherId - lowerId + 1) throw new GameEngineException($"References length {fragmentReferences.Length} can not be upper or lower than low consecutive and high consecutive");
			var fragmentDescriptions = descriptions.ToArray();
			if (fragmentDescriptions.Length != higherId - lowerId + 1) throw new GameEngineException($"Descriptions length {fragmentDescriptions.Length} can not be upper or lower than low consecutive and high consecutive");
			var fragmenToWins = toWins.ToArray();
			if (fragmenToWins.Length != higherId - lowerId + 1) throw new GameEngineException($"Towins length {fragmenToWins.Length} can not be upper or lower than low consecutive and high consecutive");

			var fragmenToRisk = risks.ToArray();
			List<AuthorizationFragment> fragmentsToBeAdded = new List<AuthorizationFragment>();
			decimal total = 0;
			for (int i = lowerId; i <= higherId; i++)
			{
				decimal risk = fragmenToRisk[i - 1];
				decimal amountOfEachFragment = risk;
				if (amountOfEachFragment <= 0) throw new GameEngineException($"Amount per fragment {amountOfEachFragment} must be greater than zero");
				total = total + amountOfEachFragment;
				if (risk <= 0) throw new GameEngineException("Risk must be upper than zero");

				var reference = fragmentReferences[i - 1];
				var description = fragmentDescriptions[i - 1];
				var toWin = fragmenToWins[i - 1];
				if (toWin <= 0) throw new GameEngineException("To win must be upper than zero.");

				var fragment = new AuthorizationFragment(authorization, i + fragments.LastIndex, Currency.Factory(currencyCode.Iso4217Code, amountOfEachFragment), reference, description, risk, toWin);
				fragmentsToBeAdded.Add(fragment);
			}

			//if (authorization.Amount.Value != total) throw new GameEngineException($"The sumatory of all fragments is {total} and is not the same as requested in the authorization of {authorization.Amount.Value}.");

			fragments.Insert(fragmentsToBeAdded);
		}

		internal IEnumerable<AuthorizationFragment> PendingFragments()
		{
			var result = fragments.PendingFragments();
			return result;
		}

		internal IEnumerable<AuthorizationFragment> PayedFragments()
		{
			var result = fragments.PayedFragments();
			return result;
		}

		internal void PayFragment(MovementsCollector movements, string concept, int fragmentId, FragmentReason reason, string reference, AccountWithBalance account, int processorId)
		{
			fragments.PayFragment(movements, concept, fragmentId, reason, reference, account, processorId);
		}

		internal void PayFakeFragment(MovementsCollector movements, FragmentReason reason)
		{
			fragments.PayFakeFragment(movements, reason);
		}

		internal void RefundFragment(MovementsCollector movements, string concept, int fragmentId, FragmentReason reason, string reference, AccountWithBalance account, int processorId)
		{
			fragments.RefundFragment(movements, concept, fragmentId, reason, reference, account, processorId);
		}

		internal void ChangeInitialFragmentsStatus(int fragmentId, FragmentStatus status)
		{
			fragments.ChangeInitialFragmentStatus(fragmentId, status);
		}

		internal int Count()
		{
			return fragments.Count;
		}

		internal bool Search(int fragmentId, out AuthorizationFragment fragment)
		{
			var fragmentFound = fragments.Fragment(fragmentId);
			fragment = null;

			if (fragmentFound != null)
			{
				fragment = fragmentFound;
				return true;
			}

			return false;
		}

		internal void Forget(IEnumerable<AuthorizationFragment> fragmentsToBeRemoved)
		{
			foreach (AuthorizationFragment fragment in fragmentsToBeRemoved)
			{
				fragments.Forget(fragment.Number);
			}
		}

		internal bool HasPendings()
		{
			return fragments.HasPendings();
		}

		internal FragmentStatus FragmentsStatus(IEnumerable<int> fragmentNumbers)
		{
			return fragments.FragmentsStatus(fragmentNumbers);
		}

		internal void Add(AuthorizationFragment fragment)
		{
			fragments.Add(fragment);
		}

		private class Fragments
		{
			//TODO Optimizar esta estructura
			private int lowerIndex;
			private int higherIndex;
			private List<AuthorizationFragment> elements = new List<AuthorizationFragment>();
			private int countOfPendings = 0;
			int lastIndex = 0;
			internal int LastIndex
            {
				get
                {
					return lastIndex;
                }
            }

			internal Fragments(int lowerIndex, int higherIndex)
			{
				this.lowerIndex = lowerIndex;
				this.higherIndex = higherIndex;
			}

			internal void Insert(List<AuthorizationFragment> authorizationFragments)
			{
				foreach (AuthorizationFragment fragment in authorizationFragments)
				{
					Insert(fragment);
				}
			}

			internal void Insert(AuthorizationFragment authorizationFragment)
			{
				if (authorizationFragment == null) throw new ArgumentNullException(nameof(authorizationFragment));
				int index = authorizationFragment.Number;
				if (index < lowerIndex) throw new GameEngineException($"Number {index} can not be lower than {lowerIndex}");
				if (index > higherIndex) throw new GameEngineException($"Number {index} can not be upper than {higherIndex}");

				int normalizedIndex = index - lowerIndex;
				if (normalizedIndex == elements.Count)
				{
					elements.Add(authorizationFragment);
				}
				else if (normalizedIndex + 1 < elements.Count)
				{
					elements[normalizedIndex] = authorizationFragment;
				}
				else
				{
					for (int i = normalizedIndex - elements.Count; i > 0; i--) elements.Add(null);
					elements.Add(authorizationFragment);
				}
				if (authorizationFragment.IsPending()) this.countOfPendings++;
				lastIndex++;
			}

			internal AuthorizationFragment Fragment(int number)
			{
				if (number < lowerIndex) throw new GameEngineException($"Number {number} can not be lower than {lowerIndex}");
				if (number > higherIndex) throw new GameEngineException($"Number {number} can not be upper than {higherIndex}");

				int normalizedIndex = number - lowerIndex;
				if (normalizedIndex > elements.Count) throw new GameEngineException($"Number {number} has not been set yet");
				var result = elements[normalizedIndex];
				if (result == null) throw new GameEngineException($"Number {number} has not been set yet");

				return result;
			}

			internal void Forget(int number)
			{
				if (number < lowerIndex) throw new GameEngineException($"Number {number} can not be lower than {lowerIndex}");
				if (number > higherIndex) throw new GameEngineException($"Number {number} can not be upper than {higherIndex}");
				int normalizedIndex = number - lowerIndex;
				var element = elements[normalizedIndex];
				if (element == null) throw new GameEngineException($"There is not fragment with number {number}");
				if (element.IsPending()) countOfPendings--;
				elements[normalizedIndex] = null;
			}

			internal IEnumerable<AuthorizationFragment> PendingFragments()
			{
				var result = elements.Where(x => x.Status == FragmentStatus.Pending);
				return result;
			}

			internal IEnumerable<AuthorizationFragment> PayedFragments()
			{
				var result = elements.Where(x => x.Status == FragmentStatus.Payed);
				return result;
			}

			internal bool HasPendings()
			{
				bool result = countOfPendings != 0;
				return result;
			}

			internal int Count
			{
				get
				{
					return this.elements.Count;
				}
			}

			internal void Add(AuthorizationFragment fragment)
			{
				if (fragment == null) throw new ArgumentNullException(nameof(fragment));

				int normalizedIndex = fragment.Number - lowerIndex;
				this.elements[normalizedIndex] = fragment;
			}

			internal FragmentStatus FragmentsStatus(IEnumerable<int> fragmentNumbers)
			{
				int normalizedIndex = fragmentNumbers.ElementAt(0) - lowerIndex;
				if(elements[normalizedIndex] == null) throw new GameEngineException($"There is not fragment with number {fragmentNumbers.ElementAt(0)}");

				var status = elements[normalizedIndex].Status;
				for (int index = 1; index < fragmentNumbers.Count(); index++)
				{
					normalizedIndex = fragmentNumbers.ElementAt(index) - lowerIndex;
					if (elements[normalizedIndex] == null) throw new GameEngineException($"There is not fragment with number {fragmentNumbers.ElementAt(index)}");
					var fragmentStatus = elements[normalizedIndex].Status;
					if (status !=  fragmentStatus) throw new GameEngineException($"Fragment status needs to be the same for all fragments");
				}
				return status;
			}

			internal void ChangeInitialFragmentStatus(int number, FragmentStatus status)
			{
				var authorizationFragment = Fragment(number);
				authorizationFragment.ChangeInitialFragmentStatus(status);
			}

			internal void PayFragment(MovementsCollector movements, string concept, int number, FragmentReason reason, string reference, AccountWithBalance account, int processorId)
			{
				var authorizationFragment = Fragment(number);
				int normalizedIndex = number - lowerIndex;
				bool wasPending = authorizationFragment.IsPending();
				authorizationFragment.Reason = reason;
				authorizationFragment.Pay(movements, concept, reference, account, processorId);
				elements[normalizedIndex] = authorizationFragment;
				if (wasPending)
				{
					if(countOfPendings <= 0) throw new GameEngineException("There are not pending fragments");
					countOfPendings--;
				}
			}

			internal void PayFakeFragment(MovementsCollector movements, FragmentReason reason)
			{
				var authorizationFragment = Fragment(1);
				int normalizedIndex = 1 - lowerIndex;
				bool wasPending = authorizationFragment.IsPending();
				authorizationFragment.Reason = reason;
				authorizationFragment.PayFragmentWithRiskZero(movements);
				elements[normalizedIndex] = authorizationFragment;
				if (wasPending)
				{
					if (countOfPendings <= 0) throw new GameEngineException("There are not pending fragments");
					countOfPendings--;
				}
			}

			internal void RefundFragment(MovementsCollector movements, string concept, int number, FragmentReason reason, string reference, AccountWithBalance account, int processorId)
			{
				var authorizationFragment = Fragment(number);
				int normalizedIndex = number - lowerIndex;
				bool wasPending = authorizationFragment.IsPending();
				authorizationFragment.Reason = reason;
				authorizationFragment.Refund(movements, concept, reference, account, processorId);
				elements[normalizedIndex] = authorizationFragment;
				if (wasPending)
				{
					if (countOfPendings <= 0) throw new GameEngineException("There are not pending fragments");
					countOfPendings--;
				}
			}
		}
	}
	sealed internal class AuthorizationTemporaryFragments
	{
		public Authorization Authorization { get; }
		public int TheLowestFragmentNumber { get; }
		public int TheHighestFragmentNumber { get; }
		public List<string> References { get; }
		public List<string> Descriptions { get; }
		public List<decimal> Risks { get; }
		public List<decimal> ToWins { get; }

		public AuthorizationTemporaryFragments(Authorization authorization, int theLowestFragmentNumber, int theHighestFragmentNumber)
		{
			Authorization = authorization;
			TheLowestFragmentNumber = theLowestFragmentNumber;
			TheHighestFragmentNumber = theHighestFragmentNumber;
			References = new List<string>();
			Descriptions = new List<string>();
			Risks = new List<decimal>();
			ToWins = new List<decimal>();
		}

		internal void AppendFragments(IEnumerable<string> references, IEnumerable<string> descriptions, IEnumerable<decimal> risks, IEnumerable<decimal> toWins)
		{
			References.AddRange(references);
			Descriptions.AddRange(descriptions);
			Risks.AddRange(risks);
			ToWins.AddRange(toWins);
		}

		internal void AppendFragments(IEnumerable<string> references, IEnumerable<string> descriptions, IEnumerable<decimal> risks, decimal toWin)
		{
			References.AddRange(references);
			Descriptions.AddRange(descriptions);
			Risks.AddRange(risks);
			for (int i = 0; i < references.Count(); i++)
			{
				ToWins.Add(toWin);
			}
		}

		internal void AppendFragments(IEnumerable<string> references, IEnumerable<string> descriptions, decimal risk, IEnumerable<decimal> toWins)
		{
			References.AddRange(references);
			Descriptions.AddRange(descriptions);
			ToWins.AddRange(toWins);
			for (int i = 0; i < references.Count(); i++)
			{
				Risks.Add(risk);
			}
		}

		internal void AppendFragments(IEnumerable<string> references, IEnumerable<string> descriptions, decimal risk, decimal toWin)
		{
			References.AddRange(references);
			Descriptions.AddRange(descriptions);
			for (int i = 0; i < references.Count(); i++)
			{
				Risks.Add(risk);
				ToWins.Add(toWin);
			}
		}

	}
}
