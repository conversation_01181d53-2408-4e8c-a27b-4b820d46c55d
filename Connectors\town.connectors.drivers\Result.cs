﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace town.connectors.drivers
{
    public abstract class Result
    {

        public Result(string entity, PaymentMethod paymentMethod, string[] currencyIsoCode, TransactionType transactionType)
        {
            if (string.IsNullOrEmpty(entity)) throw new ArgumentNullException(nameof(entity));
            if (currencyIsoCode == null || currencyIsoCode.Length == 0) throw new ArgumentNullException(nameof(currencyIsoCode));

            Entity = entity;
            PaymentMethod = paymentMethod;
            CurrencyIsoCode = currencyIsoCode;
            TransactionType = transactionType;
        }

        public string Entity { get; }
        public PaymentMethod PaymentMethod { get; }
        public string[] CurrencyIsoCode { get; }
        public TransactionType TransactionType { get; }

        public enum TransactionStatus
        {
            DRAFT = 0, APPROVED = 3, DENIED = 4
        }
    }
}
