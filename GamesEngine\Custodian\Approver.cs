﻿using GamesEngine.Custodian.Operations;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Mail;
using System.Text;

namespace GamesEngine.Custodian
{
	[Puppet]
	public class Approvers : Objeto
	{
		private int sequence = 0;
		private List<Approver> approvers;
		internal int NextApproverId()
		{
			return sequence+1;
		}
		internal void Add(Approver approver)
		{
			if (approver == null) throw new GameEngineException($"The  {nameof(approver)} it's required.");
			if (Exists(approver)) throw new GameEngineException($"The {nameof(Approver)} {approver.EmailAddress} already exists.");

			if (approvers == null) approvers = new List<Approver>();

			approvers.Add(approver);
		}

		internal void Remove(Approver approver)
		{
			if (approver == null) throw new GameEngineException($"The  {nameof(approver)} it's required.");

			approvers.Remove(approver);
		}

		internal Approver Create(bool itsThePresent, int sequence, string email)
		{
			if (string.IsNullOrEmpty(email)) throw new GameEngineException($"The  {nameof(email)} it's required.");
			if ( ! Approver.ItsAValidEmailsAddress(email)) throw new GameEngineException($"The {email} it's not valid.");
			if (Exists(email)) throw new GameEngineException($"The {email} already exists.");

			if (approvers == null) approvers = new List<Approver>();

			this.sequence = sequence;
			Approver result = new Approver(sequence, email);
			approvers.Add(result);

			if(itsThePresent) Integration.Kafka.Send(itsThePresent, Integration.Kafka.TopicForGuardianInternalOperations, result.GenerateMessage());

			return result;
		}

		internal Approver Get(string email)
		{
			if (string.IsNullOrEmpty(email)) throw new GameEngineException($"The {nameof(email)} it's required.");
			if (!Approver.ItsAValidEmailsAddress(email)) throw new GameEngineException($"The {email} it's not valid.");

			Approver result;
			Try(email, out result);

			if (result == null) throw new GameEngineException($"The {nameof(Approver)} was not created.");

			return result;
		}

		internal Approver Search(string email)
		{
			if (approvers == null) throw new GameEngineException($"There is no  {nameof(Approver)} with the email {email}.");

			Approver approver = approvers.FirstOrDefault(approver => approver.EmailAddress == email);

			if (approver == null) throw new GameEngineException($"There is no  {nameof(Approver)} with the email {email}.");

			return approver;
		}
		internal bool Exists(Approver approverToSearch)
		{
			if (string.IsNullOrEmpty(approverToSearch.EmailAddress)) throw new GameEngineException($"{nameof(approverToSearch.EmailAddress)} it's required.");
			if (approvers == null) return false;

			return approvers.FirstOrDefault(approver => approver == approverToSearch) != null;
		}

		internal int Count()
		{
			if (approvers == null) return 0;

			return approvers.Count;
		}

		private bool Exists(string email)
		{
			if (approvers == null) return false;

			return approvers.FirstOrDefault(approver => approver.EmailAddress == email) != null;
		}
		private void Try(string email, out Approver approver)
		{
			approver = null;
			if (approvers == null) return;

			approver = approvers.FirstOrDefault(approver => approver.EmailAddress == email);
		}
		//internal void AddIfNotExists(Approver approverArg)
		//{
		//	if (Exists(approverArg)) return;
		//	if (approvers == null) approvers = new List<Approver>();

		//	approvers.Add(approverArg);
		//}
		//internal void AddIfNotExists(IEnumerable<Approver> approversArg)
		//{
		//	if (approversArg == null) throw new GameEngineException($"The {nameof(approvers)} it's required.");

		//	foreach (Approver approver in approversArg)
		//	{
		//		AddIfNotExists(approver);
		//	}
		//}
		//internal void AddIfNotExists(Approvers approversArg)
		//{
		//	if (approversArg == null) throw new GameEngineException($"The {nameof(approvers)} it's required.");

		//	foreach (Approver approver in approversArg.List())
		//	{
		//		AddIfNotExists(approver);
		//	}
		//}
		internal IEnumerable<Approver> List()
		{
			if (approvers == null) return new List<Approver>();

			return approvers;
		}
		internal IEnumerable<string> CheckUnExisting(List<string> emails)
		{
			List<string> result = new List<string>();
			foreach (string email in emails)
			{
				if (!Exists(email)) result.Add(email);
				//emails.Add(approver.EmailAddress);
			}

			return result;
		}
		internal IEnumerable<string> ListEmailAddressOnly()
		{
			if (approvers == null) return new List<string>();

			List<string> emails = new List<string>();
			foreach (Approver approver in approvers)
			{
				emails.Add(approver.EmailAddress);
			}

			return emails;
		}
	}
	public class UnExistingEmails
	{ 
		public List<UnExistingEmail> Emails { set; get; }
	}
	public class UnExistingEmail
	{
		public string Email { set; get; }
	}
	[Puppet]
	internal class Approver : Objeto
	{
		private readonly MailAddress email;
		internal int Id { get;}
		internal string EmailAddress
		{
			get
			{
				return email.Address;
			}
		}
		public Profiles ProfilesWhereWasAdded { get; private set; }
		internal Approver(int id, string emailAddress)
		{
			try
			{
				string[] emailsParts = emailAddress.Split("@");
				if (emailsParts.Length < 2) throw new GameEngineException("It's not a valid email format.");

				int indexOfLastPoint = emailsParts[1].LastIndexOf(".");
				bool hasAPoint = indexOfLastPoint >= 0;
				bool thereIsAtLeastOneLetterAfterLastPoint = emailsParts[1].Length-1 > indexOfLastPoint;
				if (!hasAPoint) throw new GameEngineException("It's not a valid email format.");
				if (!thereIsAtLeastOneLetterAfterLastPoint) throw new GameEngineException("It's not a valid email format.");

				email = new MailAddress(emailAddress);
				Id = id;
			}
			catch (Exception e)
			{
				throw new GameEngineException(e.Message);
			}
			
		}

		internal static bool ItsAValidEmailsAddress(string emailAddress)
		{
			try
			{
				new MailAddress(emailAddress);
				return true;
			}
			catch 
			{
				return false;
			}
		}

		internal void LinkTo(Profile profile)
		{
			if (profile == null) throw new ArgumentNullException(nameof(profile));
			if (ProfilesWhereWasAdded == null) ProfilesWhereWasAdded = new Profiles();

			ProfilesWhereWasAdded.Add(profile);
		}

		internal bool ItsAnOwner 
		{
			get 
			{
				return ProfilesWhereWasAdded.HasAnOwner;
			}
		}
		public override string ToString()
		{
			return EmailAddress;
		}

		internal KafkaMessage GenerateMessage()
		{
			return new ApproverCreationMessage(Id, EmailAddress);
		}
	}

	public class ApproverStored
	{

		public ApproverStored(int id, string name)
		{
			Id = id;
			Name = name;
		}
		public int Id { get; }
		public string Name { get; }
	}

}
