﻿using GamesEngine.Finance;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Threading.Tasks;

namespace ExchangeAPI.Model
{
	[DataContract(Name = "ExchangeRatesDTO")]
	public class ExchangeRatesDTO
	{
		[DataMember(Name = "exchangeRates")]
		public List<ExchangeRateDTO> ExchangeRates { get; set; } = new List<ExchangeRateDTO>();

		internal void Add(ExchangeRateDTO exchangeRateDTO)
		{
			ExchangeRates.Add(exchangeRateDTO);
		}
	}

	[DataContract(Name = "ExchangeRateDTO")]
	public class ExchangeRateDTO
	{
		[DataMember(Name = "date")]
		public string Date { get; set; }
		[DataMember(Name = "fromCurrencyCode")]
		public string FromCurrencyCode { get; set; }
		[DataMember(Name = "toCurrencyCode")]
		public string ToCurrencyCode { get; set; }
		[DataMember(Name = "purchasePrice")]
		public decimal PurchasePrice { get; set; }
		[DataMember(Name = "salePrice")]
		public decimal SalePrice { get; set; }
		[DataMember(Name = "purchasePriceFormatted")]
		public string PurchasePriceFormatted { get; set; }
		[DataMember(Name = "salePriceFormatted")]
		public string SalePriceFormatted { get; set; }
	}

}
