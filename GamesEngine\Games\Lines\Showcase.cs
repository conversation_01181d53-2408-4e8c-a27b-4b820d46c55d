﻿using GamesEngine.Bets;
using GamesEngine.Business;
using GamesEngine.Domains;
using GamesEngine.Gameboards;
using GamesEngine.Gameboards.Lines;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using GamesEngine.Resources;
using GamesEngine.Time;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GamesEngine.Games.Lines
{
	[Puppet]
	class Showcase : Objeto
	{
		private readonly List<Shelve> showcase;
		private readonly Game game;
		private readonly Team home;
		private readonly Team favoriteOrNullIfBoth;
		private Wagers wagers;
		private RiskAssestment riskAssestment;
		private readonly Matchday matchday;
		private readonly Pool pool;
		private readonly Workbench workbench;
		private readonly LineChangesOnGradingStatus changes;
		private readonly DomainsActivation activations = new DomainsActivation();

		private Showcase(Matchday matchday, Game game, Team home)
		{
			if (game.TeamA != home && game.TeamB != home) throw new GameEngineException($"Team {home.Name} can not be home of the match");

			this.matchday = matchday;
			this.game = game;
			this.showcase = new List<Shelve>();
			this.showcase.Add(new Shelve(this, game));
			this.home = home;
			this.wagers = new Wagers(this);
			this.riskAssestment = new RiskAssestment(game);
			this.pool = this.Company.Book.CreateNewPool($"Lines pool for {game.ToString()}");
			this.workbench = new Workbench(this);
			this.changes = new LineChangesOnGradingStatus(this);

            foreach (var domain in Company.Sales.CurrentStore.VisiblesDomains)
            {
				activations.IncludeDomain(domain);
			}

			backgrounds.GetOrCreateImage(game.Sport.DefaultBackground.Url);
			if (sideBySide == null) this.sideBySide = new ImagesResource();
			sideBySide.GetOrCreateImage(game.Sport.DefaultSideBySide.Url);
			SetChosenBackground(game.Sport.DefaultBackground.Url);
			SetChosenSideBySide(game.Sport.DefaultSideBySide.Url);
		}

		internal Showcase(Matchday matchday, Game game, Team home, Team favorite) : this(matchday, game, home)
		{
			if (game.TeamA != home && game.TeamB != home) throw new GameEngineException($"Team {home.Name} can not be home of the match");
			if (game.TeamA != favorite && game.TeamB != favorite) throw new GameEngineException($"Team {favorite.Name} can not be favorite of the match");

			this.favoriteOrNullIfBoth = favorite;
		}

		internal Showcase(Matchday matchday, Game game, Team home, Team favorite1, Team favorite2) : this(matchday, game, home)
		{
			if (game.TeamA != home && game.TeamB != home) throw new GameEngineException($"Team {home.Name} can not be home of the match");
			if (game.TeamA != favorite1 && game.TeamB != favorite1) throw new GameEngineException($"Team {favorite1.Name} can not be favorite of the match");
			if (game.TeamA != favorite2 && game.TeamB != favorite2) throw new GameEngineException($"Team {favorite2.Name} can not be favorite of the match");

			this.favoriteOrNullIfBoth = null;
		}

		internal Team Home
		{
			get
			{
				return this.home;
			}
		}

		internal Team Visitor
		{
			get
			{
				if (game.TeamA == home) return game.TeamB;
				return game.TeamA;
			}
		}

		internal Wagers Wagers
		{
			get
			{
				if (this.wagers == null) throw new GameEngineException("These wagers are already expired and wagers are not longer exist in memory");
				return this.wagers;
			}
		}

		internal Matchday Matchday
		{
			get
			{
				return this.matchday;
			}
		}

		internal Betboard Betboard
		{
			get
			{
				return this.matchday.Betboard;
			}
		}

		internal Game Game
		{
			get
			{
				return this.game;
			}
		}

		private Company Company
		{
			get
			{
				return this.matchday.Betboard.Company;
			}
		}

		internal Pool Pool
		{
			get
			{
				return this.pool;
			}
		}

		internal void AddWager(Wager wager)
		{
			if (wager == null) throw new ArgumentNullException(nameof(wager));
			if (wager.Showcase != this) throw new GameEngineException($"This wager {wager.Name} does not belong to this showcase {this.game.ToString()}");

			this.wagers.Add(wager);
		}

		internal bool IsFirstLineToBePublished()
		{
			var allLines = this.LastVersionLines();
			if (allLines.Count() == 0) return true;

			var thereAreLinesOtherThanDraft = allLines.Any(x => x.IsPublished || x.IsSuspended || x.IsCanceled);

			if (thereAreLinesOtherThanDraft)
			{
				return false;
			}
			return true;
		}

		internal IEnumerable<Line> Expired(Period gamePeriod)
		{
			if (gamePeriod == null) throw new ArgumentNullException(nameof(gamePeriod));
			if (gamePeriod >  this.game.CurrentPeriod) throw new GameEngineException($"This Period {gamePeriod.Name} has not started");

			var results = this.LastVersionLines().Where(
				line =>
					line.IsPublished && 
					(line.IsGraded() || line.IsNoAction()) &&
					(line.PublishedOn == gamePeriod)
			);
			return results.ToList();
		}

		internal IEnumerable<Line> Unexpired()
		{
			var gamePeriod = this.Game.CurrentPeriod;
			
			var results = this.LastVersionLines().Where(
				line => 
					line.IsPending() && 
					(
						line.ValidPeriodsToBePublished().Contains(gamePeriod) ||
						(line.IsPublished && line.PublishedOn < gamePeriod)
					)
			);
			return results.ToList();
		}

		internal IEnumerable<Line> RelevantsOn(Period period)
		{
			if (period == null) throw new ArgumentNullException(nameof(period));

			var currentPeriod = this.Game.CurrentPeriod;
			var lines = this.LastVersionLines().Where(x => x.IsPublished);
			IEnumerable<Line> result;

			if (currentPeriod == period)
			{
				result = lines.Where(
					x => 
					(x.Tier == Tier.TIER_TWO || x.Tier == Tier.PROP || x.Tier == Tier.TIER_ONE && (x.Game.IsPregame() || x.Game.IsGameOver())) &&
					((x.IsPublished && x.PublishedOn == period) || x.IsPublished) &&
					(x.IsPending() || (!x.IsPending() && x.GradedOn == period))
				);
			}
			else
			{
				result = lines.Where(
					x => (!x.IsPending() && x.PublishedOn == period) || (x.IsPending() && x.PublishedOn == period) || (!x.IsPending() && x.GradedOn == period)
				);
			}
			return result.ToList();
		}

		internal int PendingOn(Period period)
		{
			if (period == null) throw new ArgumentNullException(nameof(period));

			var currentPeriod = this.Game.CurrentPeriod;
			var lines = this.LastVersionLines().Where(x => x.IsPublished && x.IsPending());
			IEnumerable<Line> result;

			if (currentPeriod == period)
			{
				result = lines.Where(
					x =>
						(x.Tier == Tier.TIER_TWO || x.Tier == Tier.PROP || x.Tier == Tier.TIER_ONE && x.Game.IsPregame()) &&
						((x.IsPublished && x.PublishedOn == period) || x.IsPublished)
				);
			}
			else
			{
				result = lines.Where(
					x =>  x.PublishedOn == period
				);
			}
			return result.Count();
		}

		internal IEnumerable<Line> Unexpired(Tier tier)
		{
			if (tier == null) throw new ArgumentNullException(nameof(tier));

			var results = this.Unexpired().Where(
				line => line.Tier == tier
			);
			return results.ToList();
		}

		internal IEnumerable<Line> AvailableLines(Domain domain, DateTime now)
		{
			var result = this.LastVersionLines().Where(
				x => x.IsPublished && 
				x.IsEnabled(domain) &&
				x.IsPending() &&
				(x.RemainingAvailabilityTime(now) == -1 || x.RemainingAvailabilityTime(now) > 0) &&
				(
					(x.Tier == Tier.TIER_ONE && x.Game.IsPregame()) ||
					(x.Tier == Tier.TIER_TWO || x.Tier == Tier.PROP)
				)
			).ToList();

			return result;
		}

		internal IEnumerable<Line> AvailablePregameLines(Domain domain)
		{
			if (domain == null) throw new ArgumentNullException(nameof(domain));
			
			var result = this.LastVersionLines().Where(x => x.IsPublished && x.Game.IsPregame() && x.IsEnabled(domain)).ToList();
			return result;
		}

		internal IEnumerable<Line> AvailableInplayLines(Domain domain)
		{
			if (domain == null) throw new ArgumentNullException(nameof(domain));

			var result = this.LastVersionLines().Where(x => x.IsPublished && x.Tier != Tier.TIER_ONE && x.IsEnabled(domain)).ToList();
			return result;
		}

		internal bool CurrentlyHasInplayLinesOffer()
		{
			var result = this.LastVersionLines().Any(x => x.Tier != Tier.TIER_ONE && x.IsPublished);
			return result;
		}

		internal bool CurrentlyHasPregameLinesOffer()
		{
			var result = this.LastVersionLines().Any(x => x.Game.IsPregame() && x.IsPublished && !x.IsSuspended);
			return result;
		}

		internal IEnumerable<Line> LinesOffer()
		{
			var result = this.LastVersionLines().Where(x => x.IsPublished || x.IsSuspended).ToList();
			return result;
		}

		internal IEnumerable<Line> AllLinesOffered()
		{
			var result = this.LastVersionLines().Where(x => x.IsPending() && x.IsPublished || x.IsSuspended || x.IsDraft).ToList();
			return result;
		}

		internal IEnumerable<Line> DraftLinesOffered()
		{
			var result = this.LastVersionLines().Where(x => x.IsDraft && x.IsPending()).ToList();
			return result;
		}

		internal IEnumerable<Line> SuspendedLinesOffered()
		{
			var result = this.LastVersionLines().Where(x =>x.IsSuspended && x.IsPending()).ToList();
			return result;
		}

		internal IEnumerable<Line> PublishedLinesOffered()
		{
			var result = this.LastVersionLines().Where(x =>x.IsPublished && x.IsPending()).ToList();
			return result;
		}

		internal IEnumerable<Line> CanceledLines()
		{
			var result = this.LastVersionLines().Where(x => x.IsCanceled && x.IsGraded()).ToList();
			return result;
		}

		internal IEnumerable<Line> AllNonPendingLines()
		{
			var results = this.LastVersionLines().Where(
				line => !line.IsPending()
			);
			return results.ToList();
		}

		internal IEnumerable<Line> LastVersionLines()
		{
			IEnumerable<Line> result = Enumerable.Empty<Line>();
			foreach (Shelve shelve in this.showcase)
			{
				result = result.Concat(shelve.LastVersionLines);
			}

			return result.ToList();
		}

		internal IEnumerable<Line> PendingOrRegradedLines()
		{
			IEnumerable<Line> result = Enumerable.Empty<Line>();
			foreach (Shelve shelve in this.showcase)
			{
				result = result.Concat(shelve.PendingOrRegradedLines);
			}

			return result.ToList();
		}

		internal IEnumerable<Line> GradedLines()
		{
			IEnumerable<Line> result = Enumerable.Empty<Line>();
			foreach (Shelve shelve in this.showcase)
			{
				result = result.Concat(shelve.GradedLines);
			}

			return result.ToList();
		}

		internal IEnumerable<Line> OriginalLines()
		{
			IEnumerable<Line> result = Enumerable.Empty<Line>();
			foreach (Shelve shelve in this.showcase)
			{
				result = result.Concat(shelve.OriginalVersionLines);
			}

			return result;
		}

		internal IEnumerable<Line> LinesBasedOn(IEnumerable<Question> questions)
		{
			IEnumerable<Line> result = Enumerable.Empty<Line>();
			foreach (Shelve shelve in this.showcase)
			{
				var lines = shelve.LinesBasedOn(questions);
				result = result.Concat(lines);
			}

			return result.ToList();
		}

		//TODO cuando gradea linea tambien gradear versiones previas
		internal bool IsThereAnyPendingLine()
		{
			foreach (var line in this.LastVersionLines())
			{
				bool exit = false;
				var currLine = line;
				while (!exit)
				{
					if (currLine.IsPending()) return true;

					exit = currLine.IsOriginalVersion();
					if (!exit) currLine = currLine.PreviousVersion;
				}
			}

			return false;
		}

		internal bool Contains(Line line)
		{
			if (line == null) throw new ArgumentNullException(nameof(line));
			if (line.Showcase != this) throw new GameEngineException("Line does not belong to this showcase");

			bool result = this.OriginalLines().Contains(line.OriginalVersion);
			return result;
		}

		internal Line SuspendLine(bool itIsThePresent, int lineId, DateTime now)
		{
			if (lineId < 0) throw new GameEngineException("Line id must be greater than zero");
			var line = this.LastVersionLines().FirstOrDefault(x => x.LineId == lineId);
			if (line == null) throw new GameEngineException($"Showcase does not contains a line {lineId}");

			line.Suspend(itIsThePresent, now);
			return line;
		}

		internal Line CancelLine(bool itIsThePresent, int lineId, DateTime now)
		{
			if (lineId < 0) throw new GameEngineException("Line id must be greater than zero");
			var line = this.LastVersionLines().FirstOrDefault(x => x.LineId == lineId);
			if (line == null) throw new GameEngineException($"Showcase does not contains a line {lineId}");
			if (line.IsGraded() || line.IsRegraded()) throw new GameEngineException($"line {lineId} can not canceled when grading or regrading.");
			
			line.Cancel(itIsThePresent, now);
			return line;
		}

		internal Line ResumeLine(bool itIsThePresent, int lineId, DateTime now)
		{
			if (lineId < 0) throw new GameEngineException("Line id must be greater than zero");
			var line = this.LastVersionLines().FirstOrDefault(x => x.LineId == lineId);
			if (line == null) throw new GameEngineException($"Showcase does not contains a line {lineId}");

			line.Resume(itIsThePresent, now);
			return line;
		}

		internal void Confirm(bool itIsThePresent, DateTime now, string confirmedBy)
		{
			if (string.IsNullOrWhiteSpace(confirmedBy)) throw new ArgumentNullException(nameof(confirmedBy));
			if (!game.IsCancelled() && !game.IsGameOver()) throw new GameEngineException($"Game {game.Number} must be cancelled or ended in order to set line as no action.");

			var tierOneLines = this.LastVersionLines().Where(x => x.Tier == Tier.TIER_ONE);
			foreach (var line in tierOneLines)
			{
				switch (line)
				{
					case MoneyDrawLine mD:
						this.wagers.Confirm(itIsThePresent, mD, now, confirmedBy);
						break;
					case MoneyLine mL:
						this.wagers.Confirm(itIsThePresent, mL, now, confirmedBy);
						break;
					case SpreadLine sL:
						this.wagers.Confirm(itIsThePresent, sL, now, confirmedBy);
						break;
					case TotalPointsLine tpL:
						this.wagers.Confirm(itIsThePresent, tpL, now, confirmedBy);
						break;
					case YesNoLine _:
					case OverUnderLine _:
					case FixedLine _:
						break;
					default:
						if (line.Tier == Tier.TIER_ONE) throw new GameEngineException("This line is a Tier, grading is not being handled");
						break;
				}
			}
		}

		internal void Grade(bool itIsThePresent, ABLine line, DateTime now, Team teamA, int scoreA, Team teamB, int scoreB, string gradedBy)
		{
			if (line == null) throw new ArgumentNullException(nameof(line));
			if (string.IsNullOrWhiteSpace(gradedBy)) throw new ArgumentNullException(nameof(gradedBy));
			if (line.Tier == Tier.TIER_ONE && !line.Game.IsGameOver()) throw new GameEngineException($"Match has not ended therefore it can stored.");

			(line as ABLine).SetRealAnswer(teamA, scoreA, teamB, scoreB);
			this.wagers.Grade(itIsThePresent, line, now, gradedBy);

			WriteHistoricalGradingData(itIsThePresent, now, gradedBy);
		}

		internal void SetNoAction(bool itIsThePresent, Line line, DateTime now, string gradedBy)
		{
			if (line == null) throw new ArgumentNullException(nameof(line));
			if (string.IsNullOrWhiteSpace(gradedBy)) throw new ArgumentNullException(nameof(gradedBy));

			this.wagers.SetNoAction(itIsThePresent, line, now, gradedBy);
			WriteHistoricalGradingData(itIsThePresent, now, gradedBy);
		}

		internal void Confirm(bool itIsThePresent, Line line, DateTime now, string confirmedBy)
		{
			if (line == null) throw new ArgumentNullException(nameof(line));
			if (string.IsNullOrWhiteSpace(confirmedBy)) throw new ArgumentNullException(nameof(confirmedBy));
			if (line.Tier == Tier.TIER_ONE && !line.Game.IsGameOver()) throw new GameEngineException($"Match has not ended therefore it can stored.");

			this.wagers.Confirm(itIsThePresent, line, now, confirmedBy);
			DeleteExpiredWagers(now);
			line.GradingWasConfirmed = true;
		}

		private void DeleteExpiredWagers()
		{
			foreach(Player player in this.wagers.Players)
			{
				player.Preferences.GamesReminder.Remove(this.game);
			}
			this.wagers.DisposeAll();
			this.Company.RemoveDisposedGameboards();
			this.wagers = null;
		}

		private void DeleteExpiredWagers(DateTime date)
		{
			var expiredShowcases = ExpiredShowcases(date);
			foreach(Showcase s in expiredShowcases)
			{
				s.DeleteExpiredWagers();
			}
		}

		private IEnumerable<Showcase> ExpiredShowcases(DateTime date)
		{
			IEnumerable<Showcase> result = Enumerable.Empty<Showcase>();
			foreach(var b in this.Betboard.Book.BetBoards)
			{
				result = result.Concat(b.ExpiredShowcases(date));
			}

			return result;
		}

		internal IEnumerable<Line> LinesWithPendingGradingToConfirm()
		{
			var result = LastVersionLines().Where(l => !l.GradingWasConfirmed);
			return result;
		}

		internal void Regrade(bool itIsThePresent, Line line, DateTime now, string gradedBy)
		{
			if (line == null) throw new ArgumentNullException(nameof(line));
			if (string.IsNullOrWhiteSpace(gradedBy)) throw new ArgumentNullException(nameof(gradedBy));
			if (line.Tier == Tier.TIER_ONE && !line.Game.IsGameOver()) throw new GameEngineException($"Match has not ended therefore it can stored.");

			this.wagers.Regrade(itIsThePresent, line, now, gradedBy);
			WriteHistoricalGradingData(itIsThePresent, now, gradedBy);
		}

		internal void Grade(bool itIsThePresent, YesNoLine line, bool score, DateTime now, string gradedBy)
		{
			if (line == null) throw new ArgumentNullException(nameof(line));
			if (string.IsNullOrWhiteSpace(gradedBy)) throw new ArgumentNullException(nameof(gradedBy));
			if (line.Tier == Tier.TIER_ONE && !line.Game.IsGameOver()) throw new GameEngineException($"Match has not ended therefore it can stored.");

			(line as YesNoLine).SetRealAnswer(score);
			this.wagers.Grade(itIsThePresent, line, now, gradedBy);

			WriteHistoricalGradingData(itIsThePresent, now, gradedBy);
		}

		internal void Grade(bool itIsThePresent, OverUnderLine line, DateTime now, Team teamA, int scoreA, Team teamB, int scoreB, string gradedBy)
		{
			if (teamA == null) new ArgumentNullException(nameof(teamA));
			if (teamB == null) new ArgumentNullException(nameof(teamB));
			if (scoreA == scoreB && line.Tier == Tier.TIER_ONE && !Game.Sport.AllowsTieResults()) throw new GameEngineException($"Sport {Game.Sport.Name} does not allow tie results");
			if (scoreA < 0) throw new GameEngineException($"{teamA.Name}'s score can not be negative");
			if (scoreB < 0) throw new GameEngineException($"{teamB.Name}'s score can not be negative");
			if (!line.IsLastVersion()) throw new GameEngineException("Answer should be set only for the original line");
			if (!game.ScoreIncludes(teamA, scoreA, teamB, scoreB)) throw new GameEngineException($"Game score has never been set as {scoreA} / {scoreB}");

			Grade(itIsThePresent, line, scoreA + scoreB, now, gradedBy);
		}

		internal void Grade(bool itIsThePresent, OverUnderLine line, int score, DateTime now, string gradedBy)
		{
			if (line == null) throw new ArgumentNullException(nameof(line));
			if (string.IsNullOrWhiteSpace(gradedBy)) throw new ArgumentNullException(nameof(gradedBy));
			if (line.Tier == Tier.TIER_ONE && !line.Game.IsGameOver()) throw new GameEngineException($"Match has not ended therefore it can stored.");
			if (score < 0) throw new GameEngineException("Score should be greater than zero.");

			if (score > line.Score)
			{
				(line as OverUnderLine).SetRealAnswerAsOver();
			}
			else if (score < line.Score)
			{
				(line as OverUnderLine).SetRealAnswerAsUnder();
			}
			else
			{
				throw new GameEngineException("Probablemente haya que devolver la plata");//TODO xxxx probablemente haya que devolver la plata
			}
			var total = this.wagers.Grade(itIsThePresent, line, now, gradedBy);

			WriteHistoricalGradingData(itIsThePresent, now, gradedBy);
		}

		internal void Grade(bool itIsThePresent, FixedLine line, string score, DateTime now, string gradedBy)
		{
			if (line == null) throw new ArgumentNullException(nameof(line));
			if (string.IsNullOrWhiteSpace(score)) throw new ArgumentNullException(nameof(score));
			if (string.IsNullOrWhiteSpace(gradedBy)) throw new ArgumentNullException(nameof(gradedBy));
			if (line.Tier == Tier.TIER_ONE && !line.Game.IsGameOver()) throw new GameEngineException($"Match has not ended therefore it can stored.");

			(line as FixedLine).SetRealAnswer(score);
			var total = this.wagers.Grade(itIsThePresent, line, now, gradedBy);

			WriteHistoricalGradingData(itIsThePresent, now, gradedBy);
		}

		private class LineChangesOnGradingStatus
		{
			private readonly Dictionary<Line, GradingStatus> changes = new Dictionary<Line, GradingStatus>();
			private readonly Showcase showcase;

			internal LineChangesOnGradingStatus(Showcase showcase)
			{
				this.showcase = showcase;
			}

			internal void Record(Line line)
			{
				changes[line] = line.Grading;
			}

			internal IEnumerable<Line> AllChangedLines()
			{
				if (changes.Count == 0) return this.showcase.LastVersionLines();

				List<Line> result = new List<Line>();
				foreach (var keypair in changes)
				{
					var line = keypair.Key;
					if (line.Grading != keypair.Value)
					{
						result.Add(line);
					}
				}

				return result;
			}

			internal bool IsThereAnyLineChanged()
			{
				bool allLinesChangedWhenTheyMovedFromPendingToGraded = changes.Count == 0;
				if (allLinesChangedWhenTheyMovedFromPendingToGraded) return true;

				foreach (var keypair in changes)
				{
					var line = keypair.Key;
					if (line.Grading != keypair.Value)
					{
						return true;
					}
				}
				return false;
			}
		}

		private void WriteHistoricalGradingData(bool itIsThePresent, DateTime now, string gradedBy)
		{
			if (itIsThePresent && this.game.IsGameOver() && ! IsThereAnyPendingLine() && changes.IsThereAnyLineChanged())
			{
				using (SenderOfHistorical historical = new SenderOfHistorical(this, itIsThePresent))
				{
					var allChangedLines = changes.AllChangedLines();
					historical.WriteGeneralLinesData(allChangedLines, now, gradedBy);
					foreach (var line in allChangedLines)
					{
						this.wagers.WriteHistoricalGradingData(historical, line, now, gradedBy);
						changes.Record(line);
					}
				}
			}
		}

		internal void Punch(Wager wager)
		{
			if (wager == null) throw new ArgumentNullException(nameof(wager));

			this.Company.PunchBet(wager.Player, wager, this.pool);
		}

		internal void LockBetCanceled(Wager wager)
		{
			if (wager == null) throw new ArgumentNullException(nameof(wager));
			if (!wager.IsNoAction() && !wager.IsRegraded()) throw new GameEngineException($"Line should not be in {GameboardStatus.NOACTION} status to be cancelled");

			this.Company.LockBetCanceled(wager.Player, wager, this.pool);
		}

		internal void PayPrize(Wager wager)
		{
			if (!wager.IsWinner()) throw new GameEngineException($"Line should be in {GameboardStatus.WINNER} status but it is at {wager.Prizing}");
			var prize = wager.ToWin();
			var owner = wager.Player;

			Company.NewBetPayed(owner, wager, pool, prize);
		}

		internal void Cancel(Wager wager)
		{
			if (!wager.IsNoAction()) throw new GameEngineException($"Line should not be in {GameboardStatus.NOACTION} status to be cancelled");
			var owner = wager.Player;

			Company.CancelBet(owner, wager, pool);
		}

		internal bool HasPurchases(Line line)
		{
			return this.wagers.HasPurchases(line);
		}

		internal void LockBetPunched(Wager wager)
		{
			if (!wager.IsNoAction() && !wager.IsRegraded()) throw new GameEngineException($"Line should not be in {GameboardStatus.NOACTION} status to be cancelled");
			var owner = wager.Player;

			Company.LockBetPunched(owner, wager, pool);
		}

		internal void LockFromBetPayed(Wager wager)
		{
			if (!wager.IsNoAction() && !wager.IsRegraded()) throw new GameEngineException($"Line should not be in {GameboardStatus.NOACTION} status to be cancelled");
			var owner = wager.Player;

			Company.NewBetLockedFromBetPayed(owner, wager, pool);
		}

		internal Shelve AddShelve()
		{
			Shelve result = new Shelve(this, this.game);
			this.showcase.Add(result);

			return result;
		}

		internal void MoveUp(Shelve shelve)
		{
			if (shelve == null) throw new ArgumentNullException(nameof(shelve));
			if (!this.showcase.Contains(shelve)) throw new GameEngineException("This shelve does not belongs to this showcase");

			int index = this.showcase.IndexOf(shelve);
			if (index > 0)
            {
				this.showcase.RemoveAt(index);
				this.showcase.Insert(index - 1, shelve);
			}
		}

		internal void MoveDown(Shelve shelve)
		{
			if (shelve == null) throw new ArgumentNullException(nameof(shelve));
			if (!this.showcase.Contains(shelve)) throw new GameEngineException("This shelve does not belongs to this showcase");

			int index = this.showcase.IndexOf(shelve);
			if (index < showcase.Count - 1)
			{
				this.showcase.RemoveAt(index);
				this.showcase.Insert(index + 1, shelve);
			}
		}

		internal void MoveUp(Line line, DateTime now)
		{
			if (line == null) throw new ArgumentNullException(nameof(line));
			if (!line.IsPending()) throw new GameEngineException("The line can only be moved ");

			for (int row = 0; row < this.showcase.Count; row++)
			{
				Shelve shelve = this.showcase[row];
				int index = shelve.IndexOf(line);
				if (index != -1 && row != 0)
				{
					for (int i = index; i > 0; i--)
					{
						shelve.Swap(this.showcase[row - 1], index);
						if (line.IsPublished)
						{
							var movedUpLineEvent = new MoveUpLineEvent(now, line);
							PlatformMonitor.GetInstance().WhenNewEvent(movedUpLineEvent);
						}
					}
					return;
				}
			}
			throw new GameEngineException("This line does not exist in this showcase");
		}

		internal IEnumerable<Wager> WagersOfLastVersionLines()
		{
			IEnumerable<Wager> result = Enumerable.Empty<Wager>();
			foreach (var line in LastVersionLines())
			{
				var tempWagers = wagers.WagersOf(line);
				result = result.Concat(tempWagers).Cast<Wager>();
			}
			return result;
		}

		internal RiskAssestment RecalculateRisk()
		{
			RiskAssestment result = this.wagers.CalculateRisk(this.game);
			this.riskAssestment = result;
			return result;
		}

		internal Shelve InsertShelve(int shelveNumber)
		{
			Shelve shelve;
			if (this.showcase.Count == shelveNumber)
			{
				shelve = AddShelve();
			}
			else if (shelveNumber < this.showcase.Count)
			{
				shelve = new Shelve(this, this.game);
				this.showcase.Insert(shelveNumber, shelve);
			}
			else
			{
				throw new GameEngineException($"Invalid shelve number {shelveNumber} because showcas has {this.showcase.Count}");
			}

			return shelve;
		}

		internal RiskAssestment RiskAssestment
		{
			get
			{
				return this.riskAssestment;
			}
		}

		internal Workbench Workbench
		{
			get
			{
				return this.workbench;
			}
		}

		internal Shelve GetShelve(int shelveNumber)
		{
			return this[shelveNumber];
		}

		internal Shelve this[int shelveNumber]
		{
			get
			{
				if (shelveNumber < 0 || shelveNumber >= showcase.Count) throw new GameEngineException($"Index {shelveNumber} is out of range");

				var result = showcase[shelveNumber];
				return result;
			}
		}

		internal int IndexOf(Shelve shelve)
        {
			var result = this.showcase.IndexOf(shelve);
			return result;
        }

		internal int LastPosition()
        {
			var result = showcase.Max(shelve => shelve.MyIndex());
			return result;
		}

		internal void IncludeDomainForActivations(Domain domain)
		{
			if (domain == null) throw new ArgumentNullException(nameof(domain));

			activations.IncludeDomain(domain, false);
            foreach (var line in LastVersionLines())
            {
				line.IncludeDomainForActivations(domain);
            }
		}

		internal bool IsEnabled(Domain domain)
		{
			if (domain == null) throw new ArgumentNullException(nameof(domain));

			var result = activations.IsEnabled(domain);
			return result;
		}

		internal DomainsActivation EnableDomain(Domain domain)
		{
			if (domain == null) throw new ArgumentNullException(nameof(domain));

			activations.EnableDomain(domain);
			return activations;
		}

		internal DomainsActivation DisableDomain(Domain domain)
		{
			if (domain == null) throw new ArgumentNullException(nameof(domain));

			activations.DisableDomain(domain);
			return activations;
		}

		internal DomainsActivation ActivationsInDomains => activations;

		private ImagesResource backgrounds = new ImagesResource();
		internal IEnumerable<ImageResource> Backgrounds
		{
			get
			{
				return backgrounds.Images;
			}
		}
		internal void CreateBackgrounds(List<string> names, List<string> urls)
		{
			this.backgrounds.CreateImages(names, urls);
		}

		internal bool HasChosenBackground()
		{
			return this.backgrounds.HasDefaultImage();
		}

		internal ImageResource ChosenBackground
		{
			get
			{
				return this.backgrounds.DefaultImage();
			}
		}

		internal ImageResource SetChosenBackground(string url)
		{
			if (string.IsNullOrEmpty(url)) throw new ArgumentNullException(nameof(url));

			return this.backgrounds.SetDefaultImage(url);
		}

		private ImagesResource sideBySide;
		internal IEnumerable<ImageResource> SideBySide
		{
			get
			{
				if (sideBySide == null) this.sideBySide = new ImagesResource();
				return sideBySide.Images;
			}
		}

		internal void CreateSideBySide(List<string> names, List<string> urls)
		{
			if (names == null) throw new ArgumentNullException(nameof(names));
			if (urls == null) throw new ArgumentNullException(nameof(urls));

			if (sideBySide == null) this.sideBySide = new ImagesResource();
			this.sideBySide.CreateImages(names, urls);
		}

		internal bool HasChosenSideBySide()
		{
			if (sideBySide == null) this.sideBySide = new ImagesResource();
			return this.sideBySide.HasDefaultImage();
		}

		internal ImageResource ChosenSideBySide
		{
			get
			{
				if (sideBySide == null) this.sideBySide = new ImagesResource();
				return this.sideBySide.DefaultImage();
			}
		}

		internal ImageResource SetChosenSideBySide(string url)
		{
			if (string.IsNullOrEmpty(url)) throw new ArgumentNullException(nameof(url));
			
			if (sideBySide == null) this.sideBySide = new ImagesResource();
			return this.sideBySide.SetDefaultImage(url);
		}

		private HeaderOption headerOption = HeaderOption.OnlyBackground;
		internal string HeaderOptionAsText 
		{ 
			get 
			{ 
				return headerOption.ToString(); 
			} 
		}

		internal void SetHeaderOption(HeaderOption headerOption)
		{
			this.headerOption = headerOption;
		}

		internal int CountBackgrounds
        {
            get
            {
				return this.backgrounds == null ? 0 : backgrounds.Images.Count();
            }
        }

		internal int CountSideBySide
		{
			get
			{
				return this.sideBySide == null ? 0 : sideBySide.Images.Count();
			}
		}

		internal LinesCounter CountLines()
		{
			var result = new LinesCounter();
			foreach (var shelve in showcase)
            {
				var counter = shelve.CountLines();
				result.AddCounter(counter);
			}
			return result;
		}

	}

	internal enum HeaderOption
	{
		ShowShields,
		OnlyBackground
	}
}
