﻿using GamesEngine.Exchange;
using GamesEngine.Finance;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using town.connectors.drivers;
using static GamesEngine.Custodian.RiskRatingTypes;

namespace GamesEngine.Custodian
{
	internal class RiskAssignments : Objeto
	{
		private Dictionary<RiskRatingType, List<RiskAssignment>> riskAssignments;

		internal void StartupRiskAssignment(RiskRatingType riskRatingType)
		{
			if (riskRatingType == null) throw new ArgumentNullException(nameof(riskRatingType));
			if (riskAssignments == null) riskAssignments = new Dictionary<RiskRatingType, List<RiskAssignment>>();

			if (riskAssignments.ContainsKey(riskRatingType)) riskAssignments[riskRatingType] = new List<RiskAssignment>();
			else riskAssignments.Add(riskRatingType, new List<RiskAssignment>());
		}

		internal RiskAssignment NewRiskAssignment(RiskRatingType riskRatingType, AmountRangeByCurrencies amountRangeByCurrencies, Profiles profilesSelected)
		{
			if (riskRatingType == null) throw new ArgumentNullException(nameof(riskRatingType));
			if (amountRangeByCurrencies == null) throw new ArgumentNullException(nameof(amountRangeByCurrencies));
			if (profilesSelected == null) throw new ArgumentNullException(nameof(profilesSelected));
			if (riskAssignments == null) throw new GameEngineException($"{nameof(StartupRiskAssignment)} method must be called first");
			if (!riskAssignments.ContainsKey(riskRatingType)) throw new GameEngineException($"{nameof(StartupRiskAssignment)} method must be called first");

			RiskAssignment riskAssignment = new RiskAssignment(riskRatingType, amountRangeByCurrencies, profilesSelected);
			Add(riskAssignment);

			return riskAssignment;
		}

		private void Add(RiskAssignment riskAssignment)
		{
			if (riskAssignment == null) throw new ArgumentNullException(nameof(riskAssignment));

			var currentRiskAssignments = riskAssignments[riskAssignment.RiskRatingType];
			currentRiskAssignments.Add(riskAssignment);
		}

		internal int Count()
		{
			if (riskAssignments == null) return 0;
			return List().Count();
		}

        internal int CountRiskAssignments(RiskRatingType riskRatingType)
		{
			if (riskRatingType == null) throw new ArgumentNullException(nameof(riskRatingType));
			if (!riskAssignments.ContainsKey(riskRatingType)) return 0;

			return riskAssignments[riskRatingType].Count;
        }

        private IEnumerable<RiskAssignment> List()
		{
			if (riskAssignments == null) return new List<RiskAssignment>();
			return riskAssignments.Values.SelectMany(risk=>risk);
		}

		internal IEnumerable<RiskAssignment> SearchByRiskRatingType(RiskRatingType riskRatingType)
		{
			if (riskRatingType == null) throw new ArgumentNullException(nameof(riskRatingType));

			List<RiskAssignment> result = new List<RiskAssignment>();
			foreach (RiskAssignment riskAssignment in List())
			{
				if (riskAssignment.RiskRatingType == riskRatingType)
				{
					result.Add(riskAssignment);
				}
			}

			return result;
		}

        internal IEnumerable<RiskAssignment> SearchBy(Profile profile)
        {
            if (profile == null) throw new ArgumentNullException(nameof(profile));

            List<RiskAssignment> result = new List<RiskAssignment>();
            foreach (RiskAssignment riskAssignment in List())
            {
                if (riskAssignment.Profiles.Exists(profile.Id))
                {
                    result.Add(riskAssignment);
                }
            }

            return result;
        }

        internal Profiles SearchProfilesFor(Currency disbursementAmount)
		{
			if (disbursementAmount == null) throw new ArgumentNullException(nameof(disbursementAmount));

			Profiles result = new Profiles();
			if (riskAssignments == null) return result;
			foreach (RiskAssignment riskAssignment in List())
			{
				bool itsInRange = riskAssignment.AmountRangeByCurrencies.IsInRange(disbursementAmount);
				if (itsInRange)
				{
					result.AddIfNotExists(riskAssignment.Profiles);
				}
			}
			return result;
		}
	}

	internal class RiskAssignment : Objeto
	{
		private Profiles profiles;

		internal RiskRatingType RiskRatingType { get; private set; }
		internal AmountRangeByCurrencies AmountRangeByCurrencies { get; private set; }
		internal Profiles Profiles 
		{
			get 
			{
				return profiles;
			}
			set
			{
				if (value == null) throw new GameEngineException($"{nameof(Profiles)} it's required");
				profiles = value;
			}
		}
		internal RiskAssignment(RiskRatingType riskRatingType, AmountRangeByCurrencies amountRangeByCurrencies, Profiles profiles)
		{
			if (riskRatingType == null) throw new GameEngineException($"{nameof(riskRatingType)} it's required");
			if (amountRangeByCurrencies == null) throw new GameEngineException($"{nameof(amountRangeByCurrencies)} it's required");
			if (profiles == null) throw new GameEngineException($"{nameof(profiles)} it's required");

			RiskRatingType = riskRatingType;
			AmountRangeByCurrencies = amountRangeByCurrencies;
			this.profiles = profiles;
		}

		internal void Set(RiskRatingType riskRatingType, AmountRangeByCurrencies amountRangeByCurrencies, Profiles profiles)
		{
			if (riskRatingType == null) throw new GameEngineException($"{nameof(riskRatingType)} it's required");
			if (amountRangeByCurrencies == null) throw new GameEngineException($"{nameof(amountRangeByCurrencies)} it's required");
			if (profiles == null) throw new GameEngineException($"{nameof(profiles)} it's required");

			RiskRatingType = riskRatingType;
			AmountRangeByCurrencies = amountRangeByCurrencies;
			this.profiles = profiles;
		}
	}

	public class RiskAssignmentsModel
	{
		public List<RiskAssignmentModel> Items { get; set; } = new List<RiskAssignmentModel>();
		public bool AllRiskRatingTypesAreValid 
		{
			get 
			{
				foreach (RiskAssignmentModel item in Items)
				{
					if (string.IsNullOrEmpty(item.RiskRatingTypeName)) return false;
				}
				return true; 
			} 
		}
	}

	public class RiskAssignmentModel
	{
		private static List<string> transactionTypes;
		public int[] ProfilesIds { get; set; }
		public string RiskRatingTypeName { get; set; }
		public static List<string> TransactionTypes()
		{
			if (transactionTypes == null)
			{
				transactionTypes = new List<string>()
					{
						TransactionType.Deposit.ToString(),
						TransactionType.Withdrawal.ToString()
					};
			}

			return transactionTypes;
		}
		public static string TransactionTypesAsText()
		{
			TransactionTypes();

			return string.Join(",", transactionTypes.Select(type => $"'{type.ToString()}'").ToArray()); ;
		}
		public List<AmountRangeByCurrencyBody> AmountRangesByCurrencies { get; set; }
	}
}
