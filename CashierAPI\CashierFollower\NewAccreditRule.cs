﻿using GamesEngine.Bets;
using Microsoft.VisualStudio.Web.CodeGeneration.Design;
using MySql.Data.MySqlClient;
using Puppeteer;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Interprete.Libraries;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CashierAPI.CashierFollower
{
    public class NewAccreditRule : RuleWithoutActor
    {
        protected const string START_TEXT_LINE1 = "atAddress.GetOrCreateSource(";
        protected const string START_TEXT_LINE2 = ".Accredit(";
        protected const string START_TEXT_LINE3 = ", Currency('";
        protected const string END_TEXT_LINE3 = "',";
        protected const string TEXT_SetInitialBalance = "balance.SetInitialBalance(";
        protected const string END_TEXT_LINE = ");";

        
        public override void Then(<PERSON>ript script)
        {
            if (script.Text.IndexOf(TEXT_SetInitialBalance) != -1)
            {
                UtilStringRules.SetHeaderText(START_TEXT_LINE3, END_TEXT_LINE3);
                var currencyCode = UtilStringRules.GetBodyArgText(script.Text).Trim();
                if (InitialBalanceHandler.ExistsBalance(currencyCode)) DBHelperCashierFollower.ProceedToMarkSkipsForSpecificDairyId(InitialBalanceHandler.GetCurrentId(currencyCode));
                InitialBalanceHandler.SetCurrentId(currencyCode, script.DairyId);
            }
            else if (script.DairyId != LastIdRule.Id && InitialBalanceHandler.OldFPCommandsWithSkip)
            {
                DBHelperCashierFollower.ProceedToMarkSkipsForSpecificDairyId(script.DairyId);
            }
        }

        public override void When(Script script)
        {
            if (script.Text.IndexOf(START_TEXT_LINE1) != -1 && script.Text.IndexOf(START_TEXT_LINE2) != -1 && script.Text.IndexOf(START_TEXT_LINE3) != -1)
            {
                Then(script);
            }

        }

        public override void FinalizeRule()
        {
            return;
        }
    }
}

/*Accredit Nuevo
 {
	source2 = atAddress.GetOrCreateSource(itIsThePresent, now, 2, 'FP', 'Manual FP');
	atAddress.CreateAccountIfNotExists('FP', 'FP');
	store = company.Sales.StoreById(1);
	source2.Accredit(itIsThePresent, Now, Currency('FP',0.01), 'Dylan R', '57104', store, 'spins instead of lotto (********)', '2-*************', 'FP', 7);
	authorization = atAddress.CreateFakeAuthorizationWithoutLock('FP', 57104, 'FP', 09/17/2024 23:34:58);
	authorization.CreateFakeFragment('2-*************', 'spins instead of lotto (********)', 0.01);
	atAddress.PrepareFakePayment(authorization, Now, store, Winner).ApplyChanges(itIsThePresent, 'Dylan R');
}

 */
