﻿using GamesEngine.Gameboards;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamesEngine.Bets
{
    internal sealed class BetCanceled : Bet
    {
        internal BetCanceled(BetLocked bet): base(bet)
        {
            bet.Invalidate();
        }

        internal override BetState State
        {
            get
            {
                return BetState.CANCELED;
            }
        }

        internal Bet Lock()
        {
            if (Invalid) throw new GameEngineException("This bet was already locked.");
            
            return new BetLocked(this);
        }
    }
}
