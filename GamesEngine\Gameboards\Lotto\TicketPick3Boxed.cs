﻿using GamesEngine.Bets;
using GamesEngine.Business;
using GamesEngine.Games.Lotto;
using GamesEngine.Tools;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using static GamesEngine.Finance.PaymentChannels;

namespace GamesEngine.Gameboards.Lotto
{
	internal sealed class TicketPick3Boxed : TicketPick<Pick3>
	{

		internal TicketPick3Boxed(Player player, Lottery lottery, DateTime drawDate, string number1, string number2, string number3, Selection selectionMode, DateTime creationDate, decimal ticketCost, Prizes prizes) :
			base(player, lottery, drawDate, new Pick3(number1, number2, number3), selectionMode, creationDate, ticketCost, prizes)
		{
		}

		internal TicketPick3Boxed(Player player, Lottery lottery, DateTime drawDate, string [] numbers, Selection selectionMode, DateTime creationDate, decimal ticketCost, Prizes prizes) :
			base(player, lottery, drawDate, numbers, selectionMode, creationDate, ticketCost, prizes)
		{
		}

		protected sealed override void GradeNumber(Pick3 winnerNumber, int fireBallNumber = int.MinValue)
		{
            var withFireBall = BelongsToFireBallDraw;
            if (withFireBall && fireBallNumber == int.MinValue) throw new GameEngineException("FireBall number is required");
            if (withFireBall && (fireBallNumber < 0 || fireBallNumber > 9)) throw new GameEngineException($"FireBall number {fireBallNumber} must be between 1 and 2");
            if (winnerNumber.Count != 1) throw new GameEngineException($"Winner {winnerNumber.AsString()} must be a single number");
			int digit1 = winnerNumber[1].SingleDigit;
			int digit2 = winnerNumber[2].SingleDigit;
			int digit3 = winnerNumber[3].SingleDigit;
			bool won = false;
			payout = 0;
			PrizesPicks currPrizes = (PrizesPicks)base.Prizes;
			foreach (Pick3 pick in base.Numbers)
			{
				bool isExcluded1 = IsExcluded(digit1, digit2, digit3);
				bool isExcluded2 = IsExcluded(digit1, digit3, digit2);
				bool isExcluded3 = IsExcluded(digit3, digit1, digit2);
				bool isExcluded4 = IsExcluded(digit3, digit2, digit1);
				bool isExcluded5 = IsExcluded(digit2, digit3, digit1);
				bool isExcluded6 = IsExcluded(digit2, digit1, digit3);

				if (!withFireBall)
				{
                    if (
                    (pick.IsMarked(digit1, digit2, digit3) && !isExcluded1) ||
                    (pick.IsMarked(digit1, digit3, digit2) && !isExcluded2) ||
                    (pick.IsMarked(digit3, digit1, digit2) && !isExcluded3) ||
                    (pick.IsMarked(digit3, digit2, digit1) && !isExcluded4) ||
                    (pick.IsMarked(digit2, digit3, digit1) && !isExcluded5) ||
                    (pick.IsMarked(digit2, digit1, digit3) && !isExcluded6)
                    )
                    {
                        var prizeCriteria = PrizesPicks.WayOfSubticket(digit1, digit2, digit3);
                        decimal prize = currPrizes.Prize(TicketType.P3B, prizeCriteria);
                        var rawPrice = prize * BetAmount(PickPortion.NORMAL_LEVEL);
                        Commons.ValidateAmount(rawPrice);
                        payout = payout + rawPrice;

                        won = true;
                    }
                }
				else
                {
                    if (
                    (pick.IsMarked(digit1, digit2, digit3) && !isExcluded1) ||
                    (pick.IsMarked(digit1, digit3, digit2) && !isExcluded2) ||
                    (pick.IsMarked(digit3, digit1, digit2) && !isExcluded3) ||
                    (pick.IsMarked(digit3, digit2, digit1) && !isExcluded4) ||
                    (pick.IsMarked(digit2, digit3, digit1) && !isExcluded5) ||
                    (pick.IsMarked(digit2, digit1, digit3) && !isExcluded6)
                    )
                    {
                        var prizeCriteria = PrizesPicks.WayOfSubticket(digit1, digit2, digit3);
                        decimal prize = currPrizes.Prize(TicketType.P3B, prizeCriteria);
                        var rawPrice = RoundToNearestFiveCents(prize * BetAmount(PickPortion.NORMAL_LEVEL));
                        Commons.ValidateAmount(rawPrice);
                        payout = payout + rawPrice;

                        won = true;
                    }

                    if (
                    (pick.IsMarked(fireBallNumber, digit2, digit3) && !isExcluded1) ||
                    (pick.IsMarked(fireBallNumber, digit3, digit2) && !isExcluded2) ||
                    (pick.IsMarked(digit3, fireBallNumber, digit2) && !isExcluded3) ||
                    (pick.IsMarked(digit3, digit2, fireBallNumber) && !isExcluded4) ||
                    (pick.IsMarked(digit2, digit3, fireBallNumber) && !isExcluded5) ||
                    (pick.IsMarked(digit2, fireBallNumber, digit3) && !isExcluded6)
                    )
                    {
                        var prizeCriteria = PrizesPicks.WayOfSubticket(fireBallNumber, digit2, digit3);
                        decimal prize = currPrizes.Prize(TicketType.P3B, prizeCriteria);
                        var rawPrice = RoundToNearestFiveCents(prize * BetAmount(PickPortion.LEVEL1_PORTION));
                        Commons.ValidateAmount(rawPrice);
                        payout = payout + rawPrice;

                        won = true;
                    }

                    if (
                    (pick.IsMarked(digit1, fireBallNumber, digit3) && !isExcluded1) ||
                    (pick.IsMarked(digit1, digit3, fireBallNumber) && !isExcluded2) ||
                    (pick.IsMarked(digit3, digit1, fireBallNumber) && !isExcluded3) ||
                    (pick.IsMarked(digit3, fireBallNumber, digit1) && !isExcluded4) ||
                    (pick.IsMarked(fireBallNumber, digit3, digit1) && !isExcluded5) ||
                    (pick.IsMarked(fireBallNumber, digit1, digit3) && !isExcluded6)
                    )
                    {
                        var prizeCriteria = PrizesPicks.WayOfSubticket(digit1, fireBallNumber, digit3);
                        decimal prize = currPrizes.Prize(TicketType.P3B, prizeCriteria);
                        var rawPrice = RoundToNearestFiveCents(prize * BetAmount(PickPortion.LEVEL2_PORTION));
                        Commons.ValidateAmount(rawPrice);
                        payout = payout + rawPrice;

                        won = true;
                    }

                    if (
                    (pick.IsMarked(digit1, digit2, fireBallNumber) && !isExcluded1) ||
                    (pick.IsMarked(digit1, fireBallNumber, digit2) && !isExcluded2) ||
                    (pick.IsMarked(fireBallNumber, digit1, digit2) && !isExcluded3) ||
                    (pick.IsMarked(fireBallNumber, digit2, digit1) && !isExcluded4) ||
                    (pick.IsMarked(digit2, fireBallNumber, digit1) && !isExcluded5) ||
                    (pick.IsMarked(digit2, digit1, fireBallNumber) && !isExcluded6)
                    )
                    {
                        var prizeCriteria = PrizesPicks.WayOfSubticket(digit1, digit2, fireBallNumber);
                        decimal prize = currPrizes.Prize(TicketType.P3B, prizeCriteria);
                        var rawPrice = RoundToNearestFiveCents(prize * BetAmount(PickPortion.LEVEL3_PORTION));
                        Commons.ValidateAmount(rawPrice);
                        payout = payout + rawPrice;

                        won = true;
                    }
                }
            }
			if (won)
			{
				base.GradeAsWinner();
			}
			else
			{
				base.GradeAsLoser();
			}
		}

        protected override decimal BetAmount(PickPortion pickPortion)
        {
            var originalValue = TicketAmount() / Count;
            Commons.ValidateAmount(originalValue);
            if (!BelongsToFireBallDraw) return originalValue;

            decimal porcionSinFireBall = originalValue / 2;
            if (pickPortion == PickPortion.NORMAL_LEVEL) return porcionSinFireBall;

            decimal porcionCombinacion1 = originalValue - porcionSinFireBall;
            porcionCombinacion1 = porcionCombinacion1 / 3;
            if (pickPortion == PickPortion.LEVEL1_PORTION) return porcionCombinacion1;

            decimal porcionCombinacion2 = originalValue - porcionSinFireBall - porcionCombinacion1;
            porcionCombinacion2 = porcionCombinacion2 / 2;
            if (pickPortion == PickPortion.LEVEL2_PORTION) return porcionCombinacion2;

            decimal porcionCombinacion3 = originalValue - porcionSinFireBall - porcionCombinacion1 - porcionCombinacion2;
            if (pickPortion == PickPortion.LEVEL3_PORTION) return porcionCombinacion3;

            throw new GameEngineException("Invalid PickPortion");
        }

        internal override TicketType IdOfType()
		{
			return TicketType.P3B;
		}

		internal override string GameTypeForReports()
		{
			return QueryMakerOfHistoricalPicks.ID_PICK3;
		}

		internal override IEnumerable<TicketByPrize> TicketsByPrize()
		{
			var tickets = new TicketByPrize[3] {
				new TicketByPrizePick3Boxed(this, PrizesPicks.PRIZE_CRITERIA_ALL_NUMBERS_ARE_THE_SAME),
				new TicketByPrizePick3Boxed(this, PrizesPicks.PRIZE_CRITERIA_6_WAY),
				new TicketByPrizePick3Boxed(this, PrizesPicks.PRIZE_CRITERIA_3_WAY)
			};
			PrizesPicks currPrizes = (PrizesPicks)base.Prizes;
			foreach (SubTicket<IPick> subTicket in SubTickets())
			{
				var prizeCriteria = PrizesPicks.WayOfSubticket(subTicket[1], subTicket[2], subTicket[3]);
				switch (prizeCriteria)
				{
					case PrizesPicks.PRIZE_CRITERIA_ALL_NUMBERS_ARE_THE_SAME:
						tickets[0].Add(subTicket);
						break;
					case PrizesPicks.PRIZE_CRITERIA_6_WAY:
						tickets[1].Add(subTicket);
						break;
					case PrizesPicks.PRIZE_CRITERIA_3_WAY:
						tickets[2].Add(subTicket);
						break;
					default:
						throw new GameEngineException($"Prize {prizeCriteria} is not valid.");
				}
			}

			var result = tickets.Where(x => x.Count > 0);
			CheckIfItIsOnlyOnePrize(result);
			return result;
		}

		private void CheckIfItIsOnlyOnePrize(IEnumerable<TicketByPrize> ticketsByPrize)
		{
			if (ticketsByPrize.Count() == 1)
			{
				foreach (TicketByPrizePick3Boxed ticketByPrize in ticketsByPrize)
				{
					ticketByPrize.HasOnlyOnePrize = true;
				}
			}
		}

		internal override void GenerateWagers()
		{
			var betAmount = BetAmount();
			GenerateWagers(betAmount);
		}

		internal override void GenerateWagers(decimal betAmount)
		{
			PrizesPicks currPrizes = (PrizesPicks)base.Prizes;
			foreach (SubTicket<IPick> subticket in SubTickets())
			{
				var prizeCriteria = PrizesPicks.WayOfSubticket(subticket[1], subticket[2], subticket[3]);
				decimal prize = currPrizes.Prize(TicketType.P3B, prizeCriteria);

				prize *= betAmount;
				var prizeToWin = prize - betAmount;
				prizeToWin = prizeToWin < 0 ? 0 : prizeToWin;
				var strNumbers = subticket.AsStringForAccounting();
				AddWager(betAmount, prizeToWin, strNumbers, subticket);
			}
		}
	}

	internal class TicketByPrizePick3Boxed : TicketByPrize
	{
		internal bool HasOnlyOnePrize { get; set; }

		internal TicketByPrizePick3Boxed(Ticket ticket, int prizeCriteria) : base(ticket, prizeCriteria)
		{

		}

		internal override bool HasNumber(int[] numbers)
		{
			var result = Subtickets.Cast<SubTicket<IPick>>().Any(x => x.IsTheSameNumberWithDifferentOrder(numbers[0], numbers[1], numbers[2]));
			return result;
		}

		internal override string AsString()
		{
			if (HasOnlyOnePrize)
			{
				return ((TicketPick3Boxed)Ticket).AsString();
			}
			StringBuilder result = new StringBuilder();
			result.Append(Id);
			foreach (var subticket in Subtickets)
			{
				result.Append(subticket.AsString());
			}
			return result.ToString();
		}
	}
}
