﻿using Puppeteer.EventSourcing.Libraries;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GamesEngine.Logs;

namespace GamesEngine.Custodian
{
    internal class RiskRatingTypes : Objeto
    {
        List<RiskRatingType> types = new List<RiskRatingType>();
        internal IEnumerable<RiskRatingType> Types 
        {
            get
            {
                return types;
            }
        }

        internal RiskRatingType NewRiskRatingType(string name)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (ExistsName(name)) throw new GameEngineException($"There is a {nameof(RiskRatingType)} with the {nameof(name)} '{name}'");

            var riskRatingType = new RiskRatingType(this, name);
            types.Add(riskRatingType);

            return riskRatingType;
        }

        internal bool ExistsName(string name)
        {
            if (string.IsNullOrWhiteSpace(name)) return false;

            foreach (var riskRatingType in Types)
            {
                if (riskRatingType.Name.Equals(name, StringComparison.OrdinalIgnoreCase)) return true;
            }
            return false;
        }

        internal RiskRatingType FindRiskRatingType(string name)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

            foreach (var riskRatingType in types)
            {
                if (riskRatingType.Name.Equals(name, StringComparison.OrdinalIgnoreCase)) return riskRatingType;
            }
            throw new GameEngineException($"There is no {nameof(RiskRatingType)} with the {nameof(name)} '{name}'");
        }

        internal void AddAnnotation(string name, string message, string who, DateTime now)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (string.IsNullOrWhiteSpace(message)) throw new ArgumentNullException(nameof(message));
            if (string.IsNullOrWhiteSpace(who)) throw new ArgumentNullException(nameof(who));

            var riskRatingType = FindRiskRatingType(name);
            riskRatingType.AddAnnotation(message, who, now);
        }

        [Puppet]
        internal class RiskRatingType : Objeto
        {
            RiskRatingTypes riskRatingTypes;
            string name;
            internal string Name 
            {
                get
                {
                    return name;
                }
                set 
                { 
                    if (string.IsNullOrWhiteSpace(value)) throw new ArgumentNullException(nameof(value));
                    if (riskRatingTypes.ExistsName(value)) throw new GameEngineException($"There is a {nameof(RiskRatingType)} with the {nameof(Name)} '{value}'");

                    name = value;
                }
            }
            internal string Description { get; set; }
            internal Log Log { get; }
            const int MAX_NUMBER_OF_ENTRIES = 5;
            internal RiskRatingType(RiskRatingTypes riskRatingTypes, string name)
            {
                if (riskRatingTypes == null) throw new ArgumentNullException(nameof(riskRatingTypes));
                if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

                this.riskRatingTypes = riskRatingTypes;
                Name = name;
                Log = new Log(name, MAX_NUMBER_OF_ENTRIES);
            }

            public void AddAnnotation(string message, string who, DateTime now)
            {
                Log.AddEntry(now, who, $"{message} by {who} at {now.ToString("MM/dd/yyyy hh:mm tt")}.");
            }
        }
    }
}
