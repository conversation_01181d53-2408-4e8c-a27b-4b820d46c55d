﻿using GamesEngine.Finance;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Exchange
{
    public class TransactionFees
    {
        private List<TransactionFee> fees;

        internal bool AnyFee()
        {
            if (fees != null && fees.Any(fee => ! fee.NoFee)) return true;
            return false;
        }

        internal void Add(TransactionFee fee)
        {
            if (fee == null) throw new ArgumentNullException(nameof(fee));
            if (fees == null) fees = new List<TransactionFee>();
            if (fees.Count > 0)
            {
                var firstFee = fees.First();
                if (fee.CurrencyCode != firstFee.CurrencyCode) throw new GameEngineException($"{nameof(fee)} to add does not match with currency '{firstFee.CurrencyCode}'");
            }

            fees.Add(fee);
        }

        public Currency Total()
        {
            if (fees == null) throw new ArgumentNullException(nameof(fees));

            var firstFee = fees.First();
            var total = 0m;
            foreach (var fee in fees)
            {
                total += fee.Value;
            }
            return Currency.Factory(firstFee.CurrencyCode, total);
        }

        public IEnumerable<TransactionFee> List()
        {
            return fees;
        }
    }
}
