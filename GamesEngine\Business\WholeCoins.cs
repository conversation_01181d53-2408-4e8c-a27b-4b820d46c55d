﻿using GamesEngine.Custodian.Operations;
using GamesEngine.Finance;
using GamesEngine.MessageQueuing;
using GamesEngine.Settings;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static GamesEngine.PurchaseOrders.CustomerMessage;

namespace GamesEngine.Business
{
    class WholeCoins : WholeCatalog
    {
        internal WholeCoins(Company company) : base(company)
        {
        }
        internal CatalogMember Add(Coin coin)
        {
            var processorCoin = new ProcessorCoin(coin);
            members.Add(processorCoin);
            return processorCoin;
        }

        internal CatalogMember Add(int id, string iso4217Code, string sign, int decimalPrecision, string unicode, string name, CoinType type)
        {
            if (decimalPrecision < 0) throw new GameEngineException($"{nameof(decimalPrecision)} cannot be negative.");
            if (iso4217Code.Length < 2 || iso4217Code.Length > 5) throw new GameEngineException($"{nameof(iso4217Code)} length must be between 3 and 5.");
            if (string.IsNullOrEmpty(sign)) throw new ArgumentNullException(nameof(sign));
            if (string.IsNullOrEmpty(name)) throw new ArgumentNullException(nameof(name));
            if (Exists(name)) throw new GameEngineException($"{nameof(name)} '{name}' is already used");
            if (Exists(id)) throw new GameEngineException($"{nameof(id)} '{id}' is already used");
            if (ExistsIsoCode(iso4217Code)) throw new GameEngineException($"There is already a coin using {nameof(iso4217Code)} '{iso4217Code}'");

            var c = char.Parse(unicode);
            if (id > nextConsecutive) nextConsecutive = id;
            Coin coin;
            if (!Coinage.Exists(iso4217Code))
                coin = Coinage.Add(id, iso4217Code, sign, decimalPrecision, c, name, type);
            else
                coin = Coinage.Coin(iso4217Code);
            var processorCoin = new ProcessorCoin(coin);
            members.Add(processorCoin);
            company.Sales.AllowCurrency(iso4217Code);
            return processorCoin;
        }

        internal CatalogMember Add(bool itIsThePresent, int coinId, string iso4217Code, string sign, int decimalPrecision, string unicode, string name, CoinType type)
        {
            if (coinId < 0) throw new GameEngineException($"{nameof(coinId)} must be greater than 0.");
            if (decimalPrecision <= 0 || decimalPrecision > 28) throw new GameEngineException($"{nameof(decimalPrecision)} must be greater than 0 and less than 28.");
            if (iso4217Code.Length < 2 || iso4217Code.Length > 5) throw new GameEngineException($"{nameof(iso4217Code)} length must be between 2 and 5.");
            if (string.IsNullOrEmpty(sign)) throw new ArgumentNullException(nameof(sign));
            if (string.IsNullOrEmpty(name)) throw new ArgumentNullException(nameof(name));
            if (Exists(name)) throw new GameEngineException($"{nameof(name)} '{name}' is already used");
            if (Exists(coinId)) throw new GameEngineException($"{nameof(coinId)} '{coinId}' is already used");
            if (ExistsIsoCode(iso4217Code)) throw new GameEngineException($"There is already a coin using {nameof(iso4217Code)} '{iso4217Code}'");
            
            var c = char.Parse(unicode);
            if (coinId > nextConsecutive) nextConsecutive = coinId;
            Coin coin;
            if (!Coinage.Exists(iso4217Code))
                coin = Coinage.Add(coinId, iso4217Code, sign, decimalPrecision, c, name, type);
            else
                coin = Coinage.Coin(iso4217Code);
            var processorCoin = new ProcessorCoin(coin);
            Add(processorCoin);
            if (itIsThePresent && (Integration.UseKafka || Integration.UseKafkaForAuto))
            {
                var msg = new CoinAddMessage(coinId, iso4217Code, sign, decimalPrecision, c, name, type);
                Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForCatalog, msg);
                Integration.Kafka.Send(itIsThePresent, $"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForTransacctions}", new CoinMessage(coin));
                Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForGuardianInternalOperations, new CoinCreationMessage(coin));
                Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForCustomers, new CoinMessage(coin, CustomerMessageType.CURRENCY));
            }
            return processorCoin;
        }

        override internal CatalogMember Add(int id, string name)
        {
            throw new NotImplementedException();
        }
        internal bool ExistsIsoCode(string iso4217Code)
        {
            var result = members.Any(coin => ((ProcessorCoin)coin).Coin.Iso4217Code.Equals(iso4217Code, StringComparison.OrdinalIgnoreCase));
            return result;
        }

        internal ProcessorCoin SearchByIsoCode(string iso4217Code)
        {
            var result = (ProcessorCoin)members.Single(coin => ((ProcessorCoin)coin).Coin.Iso4217Code.Equals(iso4217Code, StringComparison.OrdinalIgnoreCase));
            return result;
        }
    }

    public class ProcessorCoin : CatalogMember
    {
        internal Coin Coin { get; }
        internal string Iso4217Code => Coin.Iso4217Code;
        internal string UnicodeAsText => Coin.UnicodeAsText;
        internal string TypeAsText => Coin.TypeAsText;
        internal int DecimalPrecision => Coin.DecimalPrecision;
        public ProcessorCoin(Coin coin) : base(coin.Id, coin.Name)
        {
            Coin = coin;
        }

    }

}