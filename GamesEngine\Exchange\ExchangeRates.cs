﻿using GamesEngine.Finance;
using System;
using System.Collections.Generic;
using static GamesEngine.Exchange.FloatingExchangeRate;

namespace GamesEngine.Exchange
{
	internal class ExchangeRates
	{
		private readonly Marketplace marketplace;
		private readonly ConversionSpreads rates;
		

		internal ExchangeRates(Marketplace marketplace)
		{
			this.marketplace = marketplace ?? throw new ArgumentNullException(nameof(marketplace));
			rates = new ConversionSpreads(this);
		}

		internal RegularConversionSpread Add(long id, SaleRate saleRate, PurchaseRate purchaseRate)
		{
			if (saleRate == null) throw new ArgumentNullException(nameof(saleRate));
			if (purchaseRate == null) throw new ArgumentNullException(nameof(purchaseRate));

			RegularConversionSpread conversionSpread = new RegularConversionSpread(id, this, saleRate, purchaseRate);

			rates.Add(conversionSpread);

			return conversionSpread;
		}

		internal SpecialConversionSpread Add(long id, String name, SaleRate saleRate, PurchaseRate purchaseRate)
		{
			if (string.IsNullOrEmpty(name)) throw new ArgumentNullException(nameof(name));
			if (saleRate == null) throw new ArgumentNullException(nameof(saleRate));
			if (purchaseRate == null) throw new ArgumentNullException(nameof(purchaseRate));

			SpecialConversionSpread conversionSpread = new SpecialConversionSpread(id, name, this, saleRate, purchaseRate);
			rates.Add(conversionSpread);

			return conversionSpread;
		}

		internal IConversionSpread At(DateTime date, Coin source, Coin target)
		{
			if (date == default(DateTime)) throw new ArgumentNullException(nameof(date));
			IConversionSpread rate =  rates.At(date, source, target);

			return rate;
		}

		internal IConversionSpread SearchSpreadFor(DateTime date, Coin source, Coin target)
		{
			if (date == default(DateTime)) throw new ArgumentNullException(nameof(date));
			return rates.SearchSpreadFor(date, source, target);
		}

		internal int AmountOfRatesInMemory()
		{
			return rates.AmountOfRatesInMemory();
		}

		internal void PruneUnUsedRates()
		{
			rates.PruneUnUsedRates();
		}
	}
}
