﻿using GamesEngine.Bets;
using GamesEngine.Business;
using GamesEngine.Finance;
using GamesEngine.Games.Lotto;
using GamesEngine.Tools;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using static GamesEngine.Finance.PaymentChannels;

namespace GamesEngine.Gameboards.Lotto
{
	internal sealed class TicketPick2Boxed : TicketPick<Pick2>
	{

		internal TicketPick2Boxed(Player player, Lottery lottery, DateTime drawDate, string number1, string number2, Selection selectionMode, DateTime creationDate, decimal ticketCost, Prizes prizes) :
			base(player, lottery, drawDate, new Pick2(number1, number2), selectionMode, creationDate, ticketCost, prizes)
		{

		}

		internal TicketPick2Boxed(Player player, Lottery lottery, DateTime drawDate, string [] numbers, Selection selectionMode, DateTime creationDate, decimal ticketCost, Prizes prizes) :
			base(player, lottery, drawDate, numbers, selectionMode, creationDate, ticketCost, prizes)
		{

		}

		protected sealed override void GradeNumber(Pick2 winnerNumber, int fireBallNumber = int.MinValue)
		{
            var withFireBall = BelongsToFireBallDraw;
            if (withFireBall && fireBallNumber == int.MinValue) throw new GameEngineException("FireBall number is required");
			if (withFireBall && (fireBallNumber < 0 || fireBallNumber > 9)) throw new GameEngineException($"FireBall number {fireBallNumber} must be between 1 and 2");
			if (winnerNumber.Count != 1) throw new GameEngineException($"Winner {winnerNumber.AsString()} must be a single number");
			int digit1 = winnerNumber[1].SingleDigit;
			int digit2 = winnerNumber[2].SingleDigit;
			bool won = false;
			payout = 0;
			PrizesPicks currPrizes = (PrizesPicks)base.Prizes;
            foreach (Pick2 pick in base.Numbers)
            {
				bool isExcluded1 = IsExcluded(digit1, digit2);
				bool isExcluded2 = IsExcluded(digit2, digit1);
                
				if (!withFireBall)
				{
                    if (
                    (pick.IsMarked(digit1, digit2) && !isExcluded1) ||
                    (pick.IsMarked(digit2, digit1) && !isExcluded2)
                    )
                    {
                        var prizeCriteria = PrizesPicks.WayOfSubticket(digit1, digit2);
                        decimal prize = currPrizes.Prize(TicketType.P2B, prizeCriteria);
                        var rawPrice = prize * BetAmount(PickPortion.NORMAL_LEVEL);
                        Commons.ValidateAmount(rawPrice);
                        payout = payout + rawPrice;

                        won = true;
                    }
                }
				else
				{
                    if (
                    (pick.IsMarked(digit1, digit2) && !isExcluded1) ||
                    (pick.IsMarked(digit2, digit1) && !isExcluded2)
                    )
                    {
                        var prizeCriteria = PrizesPicks.WayOfSubticket(digit1, digit2);
                        decimal prize = currPrizes.Prize(TicketType.P2B, prizeCriteria);
                        var rawPrice = RoundToNearestFiveCents(prize * BetAmount(PickPortion.NORMAL_LEVEL));
                        Commons.ValidateAmount(rawPrice);
                        payout = payout + rawPrice;

                        won = true;
                    }

                    if (
                    (pick.IsMarked(fireBallNumber, digit2) && !isExcluded1) ||
                    (pick.IsMarked(digit2, fireBallNumber) && !isExcluded2)
                    )
                    {
                        var prizeCriteria = PrizesPicks.WayOfSubticket(fireBallNumber, digit2);
                        decimal prize = currPrizes.Prize(TicketType.P2B, prizeCriteria);
                        var rawPrice = RoundToNearestFiveCents(prize * BetAmount(PickPortion.LEVEL1_PORTION));
                        Commons.ValidateAmount(rawPrice);
                        payout = payout + rawPrice;

                        won = true;
                    }

                    if (
                    (pick.IsMarked(digit1, fireBallNumber) && !isExcluded1) ||
                    (pick.IsMarked(fireBallNumber, digit1) && !isExcluded2)
                    )
                    {
                        var prizeCriteria = PrizesPicks.WayOfSubticket(digit1, fireBallNumber);
                        decimal prize = currPrizes.Prize(TicketType.P2B, prizeCriteria);
                        var rawPrice = RoundToNearestFiveCents(prize * BetAmount(PickPortion.LEVEL2_PORTION));
                        Commons.ValidateAmount(rawPrice);
                        payout = payout + rawPrice;

                        won = true;
                    }
                }
            }
            if (won)
			{
				base.GradeAsWinner();
			}
			else
			{
				base.GradeAsLoser();
			}
		}

        protected override decimal BetAmount(PickPortion pickPortion)
        {
            var originalValue = TicketAmount() / Count;
            Commons.ValidateAmount(originalValue);
            if (!BelongsToFireBallDraw) return originalValue;

            decimal porcionSinFireBall = originalValue / 2;
            if (pickPortion == PickPortion.NORMAL_LEVEL) return porcionSinFireBall;

            decimal porcionCombinacion1 = originalValue - porcionSinFireBall;
            porcionCombinacion1 = porcionCombinacion1 / 2;
            if (pickPortion == PickPortion.LEVEL1_PORTION) return porcionCombinacion1;

            decimal porcionCombinacion2 = originalValue - porcionSinFireBall - porcionCombinacion1;
            if (pickPortion == PickPortion.LEVEL2_PORTION) return porcionCombinacion2;

            throw new GameEngineException("Invalid PickPortion");
        }

        internal override TicketType IdOfType()
		{
			return TicketType.P2B;
		}

		internal override string GameTypeForReports()
		{
			return QueryMakerOfHistoricalPicks.ID_PICK2;
		}

		internal override IEnumerable<TicketByPrize> TicketsByPrize()
		{
			var tickets = new TicketByPrize[2] {
				new TicketByPrizePick2Boxed(this, PrizesPicks.PRIZE_CRITERIA_ALL_NUMBERS_ARE_THE_SAME),
				new TicketByPrizePick2Boxed(this, PrizesPicks.PRIZE_CRITERIA_2_WAY)
			};

			PrizesPicks currPrizes = (PrizesPicks)base.Prizes;
			foreach (SubTicket<IPick> subTicket in SubTickets())
			{
				var prizeCriteria = PrizesPicks.WayOfSubticket(subTicket[1], subTicket[2]);
				switch (prizeCriteria)
				{
					case PrizesPicks.PRIZE_CRITERIA_ALL_NUMBERS_ARE_THE_SAME:
						tickets[0].Add(subTicket);
						break;
					case PrizesPicks.PRIZE_CRITERIA_2_WAY:
						tickets[1].Add(subTicket);
						break;
					default:
						throw new GameEngineException($"Prize {prizeCriteria} is not valid.");
				}
			}

			var result = tickets.Where(x => x.Count > 0);
			CheckIfItIsOnlyOnePrize(result);
			return result;
		}

		private void CheckIfItIsOnlyOnePrize(IEnumerable<TicketByPrize> ticketsByPrize)
		{
			if (ticketsByPrize.Count() == 1)
			{
				foreach (TicketByPrizePick2Boxed ticketByPrize in ticketsByPrize)
				{
					ticketByPrize.HasOnlyOnePrize = true;
				}
			}
		}

		internal override void GenerateWagers()
		{
			var betAmount = BetAmount();
			GenerateWagers(betAmount);
		}

		internal override void GenerateWagers(decimal betAmount)
		{
			PrizesPicks currPrizes = (PrizesPicks)base.Prizes;
			foreach (SubTicket<IPick> subticket in SubTickets())
			{
				var prizeCriteria = PrizesPicks.WayOfSubticket(subticket[1], subticket[2]);
				decimal prize = currPrizes.Prize(TicketType.P2B, prizeCriteria);

				prize *= betAmount;
				var prizeToWin = prize - betAmount;
				prizeToWin = prizeToWin < 0 ? 0 : prizeToWin;
				var strNumbers = subticket.AsStringForAccounting();
				AddWager(betAmount, prizeToWin, strNumbers, subticket);
			}
		}
    }

	internal class TicketByPrizePick2Boxed : TicketByPrize
	{
		internal bool HasOnlyOnePrize { get; set; }

		internal TicketByPrizePick2Boxed(Ticket ticket, int prizeCriteria) : base(ticket, prizeCriteria)
		{

		}

		internal override bool HasNumber(int[] numbers)
		{
			var result = Subtickets.Cast<SubTicket<IPick>>().Any(x => x.IsTheSameNumberWithDifferentOrder(numbers[0], numbers[1]));
			return result;
		}

		internal override string AsString()
		{
			if (HasOnlyOnePrize)
			{
				return ((TicketPick2Boxed)Ticket).AsString();
			}
			StringBuilder result = new StringBuilder();
			result.Append(Id);
			foreach (var subticket in Subtickets)
			{
				result.Append(subticket.AsString());
			}
			return result.ToString();
		}
	}
}
