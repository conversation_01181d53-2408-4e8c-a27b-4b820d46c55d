FROM 5dimes:dotnetimage
ENV DEBIAN_FRONTEND noninteractive

ADD upgrades.ubuntu.18.04-x64.tar.gz /opt/upgrades
ADD ./appsettings_kycln.json /opt/upgrades
RUN mkdir /tmp/upgrades
RUN mv /opt/upgrades/appsettings_kycln.json /opt/upgrades/appsettings.json
RUN cp /opt/upgrades/appsettings.json /root/

WORKDIR /opt/upgrades

CMD dotnet Upgrades.dll /target:5.0 /actor:KNOWYOURCUSTOMER /Production > /tmp/upgrades/out_kycln.log