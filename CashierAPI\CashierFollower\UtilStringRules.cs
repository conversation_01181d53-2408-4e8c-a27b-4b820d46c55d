﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CashierAPI.CashierFollower
{

	public static class UtilStringRules
	{
		private static string startCaptureText;
		private static string endCaptureText;

		private static int lastPosition;

		public static void SetHeaderText(string startCaptureText, string endCaptureText)
		{
			UtilStringRules.startCaptureText = startCaptureText;
			UtilStringRules.endCaptureText = endCaptureText;
			lastPosition = 0;
		}

		public static string GetBodyArgText(string script)
		{
			int starPosition = script.IndexOf(startCaptureText, lastPosition);
			if (starPosition == -1) return string.Empty;
			starPosition += startCaptureText.Length;

			int endPosition = script.IndexOf(endCaptureText, starPosition);
			if (endPosition == -1) return string.Empty;
			lastPosition = endPosition;

			string bodyArgs = script.Substring(starPosition, endPosition - starPosition);
			return bodyArgs.Trim();
		}

        internal static List<string> GetArguments(string body)
        {
            List<string> arguments = new List<string>();
            int currentStart = 0;
            int currentEnd = 0;
            int curlyDepth = 0; // Keeps track of nesting depth within curly braces

            while (currentEnd < body.Length)
            {
                char currentChar = body[currentEnd];
                if (currentChar == ',' && curlyDepth == 0)
                {
                    arguments.Add(body.Substring(currentStart, currentEnd - currentStart));
                    currentStart = currentEnd + 1;
                }
                else if (currentChar == '{')
                {
                    curlyDepth++;
                }
                else if (currentChar == '}')
                {
                    curlyDepth--;
                }
                currentEnd++;
            }

            // Add the last element
            arguments.Add(body.Substring(currentStart));
			return arguments;
        }

        public static bool HasNextChangeInitialFragmentsStatus(string script)
		{
			int starPosition = script.IndexOf(startCaptureText, lastPosition);
			if (starPosition == -1)
			{
				return false;
			}
			else
			{
				return true;
			}
		}

	}
}
