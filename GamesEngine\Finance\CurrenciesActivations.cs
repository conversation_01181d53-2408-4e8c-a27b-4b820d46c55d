﻿using System;
using System.Collections.Generic;
using System.Text;

namespace GamesEngine.Finance
{
    class CurrenciesActivations<T>
    {
        private readonly Dictionary<T, CurrenciesActivation> activations = new Dictionary<T, CurrenciesActivation>();

        internal CurrenciesActivations()
        {

        }

        internal CurrenciesActivations(IEnumerable<T> elements) : this(elements, CurrenciesActivation.MAX_LOG_CAPACITY)
        {
        }

        internal CurrenciesActivations(IEnumerable<T> elements, byte maxLogCapacity)
        {
            if (maxLogCapacity <= 0) throw new GameEngineException($"Capacity {maxLogCapacity} must be greater than 0");

            foreach (var element in elements)
            {
                if (element == null) throw new ArgumentNullException(nameof(element));

                var activation = new CurrenciesActivation(maxLogCapacity);
                activations.Add(element, activation);
            }
        }

        internal void AddActivatableElement(T t)
        {
            if (t == null) throw new ArgumentNullException(nameof(t));

            activations.Add(t, new CurrenciesActivation());
        }

        internal bool IsEnabled(T t, Coin coin)
        {
            if (t == null) throw new ArgumentNullException(nameof(t));
            if (coin == null) throw new ArgumentNullException(nameof(coin));

            CurrenciesActivation activation;
            activations.TryGetValue(t, out activation);
            if (activation == null) throw new GameEngineException($"There is no '{t.GetType().Name}' initialized with domains activation");
            var result = activation.IsEnabled(coin);
            return result;
        }

        internal void IncludeNewCurrencyForAll(Coin coin, bool enabled = true)
        {
            if (coin == null) throw new ArgumentNullException(nameof(coin));

            foreach (var domainActivation in activations.Values)
            {
                domainActivation.Include(coin);
                if (!enabled) domainActivation.DisableDomain(coin);
            }
        }

        internal CurrenciesActivation EnableDomain(T t, Coin coin)
        {
            if (t == null) throw new ArgumentNullException(nameof(t));
            if (coin == null) throw new ArgumentNullException(nameof(coin));

            CurrenciesActivation activation;
            activations.TryGetValue(t, out activation);
            if (activation == null) throw new GameEngineException($"There is no '{t.GetType().Name}' initialized with domains activation");
            activation.EnableDomain(coin);
            return activation;
        }

        internal CurrenciesActivation DisableDomain(T t, Coin coin)
        {
            if (t == null) throw new ArgumentNullException(nameof(t));
            if (coin == null) throw new ArgumentNullException(nameof(coin));

            CurrenciesActivation activation;
            activations.TryGetValue(t, out activation);
            if (activation == null) throw new GameEngineException($"There is no '{t.GetType().Name}' initialized with domains activation");
            activation.DisableDomain(coin);
            return activation;
        }

        internal bool AnyEnabledDomain(T t)
        {
            if (t == null) throw new ArgumentNullException(nameof(t));
            if (!activations.ContainsKey(t)) throw new GameEngineException($"'{t.GetType().Name}' does not exist in domains activations");

            var result = activations[t].AnyEnabledDomain();
            return result;
        }

        internal Logs.Log SearchLog(T t)
        {
            CurrenciesActivation activation;
            activations.TryGetValue(t, out activation);
            if (activation == null) throw new GameEngineException($"There is no '{t.GetType().Name}' initialized with domains activation");
            return activation.Log;
        }
    }
}
