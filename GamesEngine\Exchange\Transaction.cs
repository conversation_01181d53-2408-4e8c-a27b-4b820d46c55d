﻿using GamesEngine.Accounting.JournalTemplates;
using GamesEngine.Business;
using GamesEngine.Exchange.Batch;
using GamesEngine.Finance;
using GamesEngine.MessageQueuing;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using town.connectors.drivers;
using static GamesEngine.Business.WholePaymentProcessor;
using static GamesEngine.Exchange.PaymentProcessorsAndActionsByDomains;
using static GamesEngine.Exchange.TransactionCompleted;
using static GamesEngine.Finance.Currencies;
using static GamesEngine.Finance.FragmentsCreationBody;
using static GamesEngine.Finance.PaymentChannels;
using static town.connectors.drivers.Result;

namespace GamesEngine.Exchange
{
	[Puppet]
	internal abstract class Transaction : Objeto
	{
		internal TransactionStatus Status { get; set; }
		private readonly int id;
		private readonly IConversionSpread conversionSpread;
		private readonly TransactionDefinition transactionDefinition;
		private readonly Currency amount;
		private readonly DateTime transactionDate;
		protected TransactionFees Fees { get; } = new TransactionFees();
		protected TransactionType Type { get; }

		public int Id { get { return id; } }
		internal IConversionSpread ConversionSpread { get { return this.conversionSpread; } }
		protected TransactionDefinition TransactionDefinition { get { return transactionDefinition; } }
		internal Currency Amount { get { return amount; } }
		internal DateTime TransactionDate { get { return this.transactionDate; } }
		internal string AccountNumberIdentificator { get { return TransactionDefinition.Account.Identificator; } }
		internal string CustomerIdentificationNumber { get { return TransactionDefinition.Account.CustomerIdentifier; } }
		internal Domains.Domain Domain { get { return TransactionDefinition.Domain; } }

		PaymentProcessor processor;
		internal PaymentProcessor Processor
		{
			get
			{
				if (processor == null) throw new GameEngineException($"{nameof(processor)} is not assigned");
				return processor;
			}
			set
			{
				if (processor != null) throw new GameEngineException($"{nameof(processor)} is already assigned");
				if (value == null) throw new ArgumentNullException(nameof(value));

				processor = value;
			}
		}

		int processorAccountId;
		internal int ProcessorAccountId
		{
			get
			{
				if (processorAccountId == 0) throw new GameEngineException($"{nameof(processorAccountId)} is not assigned");
				return processorAccountId;
			}
			set
			{
				if (processorAccountId != 0) throw new GameEngineException($"{nameof(processorAccountId)} is already assigned");
				if (value == 0) throw new ArgumentNullException(nameof(value));

				processorAccountId = value;
			}
		}
		
		internal void SaveTrackingInformation(string trackingCode, string reference)
		{
			if (string.IsNullOrEmpty(trackingCode)) throw new ArgumentNullException(nameof(trackingCode));
			if (string.IsNullOrEmpty(reference)) throw new ArgumentNullException(nameof(reference));

			TrackingKey = trackingCode + reference;
			BatchTransactions.IndexTransactionByTrackingInformation(TrackingKey, this);
		}


		internal BatchTransactions BatchTransactions { get { return this.transactionDefinition.Batch; } }

		internal string TrackingKey { get; private set; }
		internal bool HasTrackingInformation
		{
			get
			{
				return !string.IsNullOrEmpty(TrackingKey);
			}
		}

		protected abstract int TemplateNumber { get; }

		internal JournalEntryTemplate JournalEntryTemplate
		{
			get
			{
				var result = transactionDefinition.Marketplace.JournalEntryTemplates[TemplateNumber];
				return result;
			}
		}

		protected Transaction(TransactionDefinition transactionDefinition, IConversionSpread conversionSpread, Currency amount, DateTime transactionDate, TransactionType tranType)
		{
			if (conversionSpread == null) throw new ArgumentNullException(nameof(conversionSpread));
			if (conversionSpread is NoConversionSpread && transactionDefinition.Account.CurrencyCode != amount.CurrencyCode) throw new GameEngineException($"There is no a Rate defined for {transactionDefinition.Account.CurrencyCode}/{amount.CurrencyCode} ");
			if (transactionDefinition == null) throw new ArgumentNullException(nameof(transactionDefinition));
			if (amount == null) throw new ArgumentNullException(nameof(amount));
			if (transactionDate == default(DateTime)) throw new ArgumentNullException(nameof(transactionDate));

			Status = TransactionStatus.DRAFT;
			id = transactionDefinition.Id;
			this.conversionSpread = conversionSpread;
			this.transactionDefinition = transactionDefinition;
			this.amount = amount;
			this.transactionDate = transactionDate;
			this.Type = tranType;

			conversionSpread.IncreaseTheNumberOfTransactionsUsingIt();
		}

		protected Store Store
		{
			get
			{
				return this.transactionDefinition.Store;
			}
		}

		internal virtual TransactionCompleted Approve(DateTime date, bool itsThePresent, string employeeName, int journalEntryNumber)
		{
			Currency gross = null, comission = null, net = null, profit = null;
			Currency amountToCustomer = CalculateAmounts(out gross, out comission, out net, out profit);

			Status = TransactionStatus.APPROVED;

			var totalFee = Currency.ZeroFactory(amountToCustomer.CurrencyCode);
			TransactionResult result = new TransactionResult(net, gross, comission, profit, amountToCustomer, totalFee);
			TransactionCompleted transactionCompleted = new TransactionCompleted(result);

			transactionDefinition.SetJournalEntryId(journalEntryNumber);
			AfterApprove(date, itsThePresent, gross, comission, profit, net, amountToCustomer, employeeName, transactionCompleted, journalEntryNumber);
			TransactionDefinition.Batch.Remove(this);
			transactionDefinition.Batch.AddTransactionApproval(this, transactionCompleted);
			conversionSpread.DecreaseTheNumberOfTransactionsUsingIt();

			return transactionCompleted;
		}

		internal abstract bool isSameCurrencyTransaction();

		internal Currency CalculateAmounts(out Currency gross, out Currency comission, out Currency net, out Currency profit)
		{
			Currency amountToCustomer = null;
			Currency amount = Amount;
			bool thereIsARateDefined = !(ConversionSpread is NoConversionSpread);
			if (thereIsARateDefined)
			{
				amountToCustomer = ConversionSpread.Sale(amount, out gross, out comission, out net);
				profit = TransactionDefinition.Batch.CalculateProfit(ConversionSpread.SaleRate, amountToCustomer);
			}
			else if (!thereIsARateDefined && TransactionDefinition.Account.CurrencyCode == Amount.CurrencyCode)
			{
				gross = amount;
				comission = Currency.Factory(amount.CurrencyCode, 0);
				net = Currency.Factory(amount.CurrencyCode, amount.Value);
				amountToCustomer = amount;

				FixedExchangeRate noProfitRate = new FixedExchangeRate(amountToCustomer.Coin, amountToCustomer.Coin, 1);
				profit = TransactionDefinition.Batch.CalculateProfit(noProfitRate, amountToCustomer);
			}
			else
			{
				throw new GameEngineException($"There is no a Rate defined for {TransactionDefinition.Account.CurrencyCode}/{Amount.CurrencyCode} ");
			}

			return amountToCustomer;
		}

		protected abstract void AfterApprove(DateTime date, bool itsThePresent, Currency gross, Currency comission, Currency profit, Currency net, Currency amountToCustomer, string employeeName, TransactionCompleted transactionCompleted, int journalEntryNumber);

		internal virtual void Deny(DateTime date, bool itsThePresent, string employeeName, string reason)
		{
			AfterDeny(date, itsThePresent, employeeName, reason);

			Currency gross = null, comission = null, net = null, profit = null;
			Currency amountToCustomer = CalculateAmounts(out gross, out comission, out net, out profit);

			var totalFee = Currency.ZeroFactory(amountToCustomer.CurrencyCode);
			TransactionResult result = new TransactionResult(net, gross, comission, profit, amountToCustomer, totalFee);
			TransactionCompleted transactionCompleted = new TransactionCompleted(result);

			transactionDefinition.Batch.AddTransactionRejection(this, transactionCompleted);

			TransactionDefinition.Batch.Remove(this);

			conversionSpread.DecreaseTheNumberOfTransactionsUsingIt();
		}

		internal abstract void AfterDeny(DateTime date, bool itsThePresent, string employeeName, string reason);

		protected void SendDepositMessage(bool itsThePresent, string strToCurrencyCode, string toAddress, decimal amount, string description, int authorizationId, string who, string accountNumber, int processorId, Agents agent)
		{
			if (!itsThePresent) return;
			string reference = authorizationId.ToString();
			if (reference.Length > Movement.MAX_REFERENCE_LENGTH) reference = reference.Substring(0, Movement.MAX_REFERENCE_LENGTH);

			Coin toCurrencyCode = Coinage.Coin(strToCurrencyCode);
			using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itsThePresent, Integration.Kafka.TopicForDeposits))
			{
				DepositMessage message = new DepositMessage(
					toAddress,
					toCurrencyCode,
					this.Store.Id,
					amount,
					who,
					description,
					reference,
					accountNumber,
					processorId,
					string.Empty,
					agent
				);
				buffer.Send(message);
			}
		}

		protected void SendWithdrawMessage(bool itsThePresent, string strToCurrencyCode, string toAddress, decimal amount, string description, int authorizationId, string who, string accountNumber, int processorId, Agents agent)
		{
			if (!itsThePresent) return;
			string reference = authorizationId.ToString();
			if (reference.Length > Movement.MAX_REFERENCE_LENGTH) reference = reference.Substring(0, Movement.MAX_REFERENCE_LENGTH);

			Coin toCurrencyCode = Coinage.Coin(strToCurrencyCode);
			using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itsThePresent, Integration.Kafka.TopicForWithdrawals))
			{
				WithdrawMessage message = new WithdrawMessage(
					toAddress,
					toCurrencyCode,
					this.Store.Id,
					amount,
					who,
					description,
					reference,
					accountNumber,
					processorId,
					string.Empty,
					agent
				);
				buffer.Send(message);
			}
		}

		const int DaysToBecomeUseless = 7;
		protected void SendFragmentsCreationMessage(bool itsThePresent, string description, decimal amount, string fromAddress, Coin fromCurrency, int authorizationId, decimal total, Agents agent, DateTime now)
		{
			if (!itsThePresent) return;
			List<Fragment> listOfWagers = new List<Fragment>();
			listOfWagers.Add(new Fragment()
			{
				BetDescription = description,
				Risk = amount.ToString(),
				ReferenceNumber = authorizationId.ToString(),
				ToWin = amount.ToString(),
				Number = "1"
			});
			var useless = now.AddDays(DaysToBecomeUseless);
			FragmentCreationKafkaMessage message = new FragmentCreationKafkaMessage(
				this.Store.Id,
				fromAddress,
				authorizationId,
				0,
				1,
				1,
				fromCurrency,
				listOfWagers.ToArray(),
				total,
				(int)agent,
				now,
				useless,
				WholePaymentProcessor.NoPaymentProcessor.ToString()
			);

			Integration.Kafka.Send(itsThePresent, Integration.Kafka.TopicForFragmentsCreation, message);
		}

		protected void SendFragmentsCreationMessageWithFee(bool itsThePresent, string description, decimal amount, string fromAddress, string fromCurrency, int authorizationId, TransactionFee fee, decimal total, Agents agent, DateTime now)
		{
			if (!itsThePresent) return;
			if (fee.Value <= 0) throw new GameEngineException($"{nameof(fee)} must be greater than 0");

			List<Fragment> listOfWagers = new List<Fragment>();
			listOfWagers.Add(new Fragment()
			{
				BetDescription = description,
				Risk = amount.ToString(),
				ReferenceNumber = authorizationId.ToString(),
				ToWin = amount.ToString(),
				Number = "1"
			});

			listOfWagers.Add(new Fragment()
			{
				BetDescription = "Fee amount for withdrawal request",
				Risk = fee.Value.ToString(),
				ReferenceNumber = authorizationId.ToString(),
				ToWin = fee.Value.ToString(),
				Number = "2"
			});
			var useless = now.AddDays(DaysToBecomeUseless);
			FragmentCreationKafkaMessage message = new FragmentCreationKafkaMessage(
				this.Store.Id,
				fromAddress,
				authorizationId,
				0,
				1,
				1,
				Coinage.Coin(fromCurrency),
				listOfWagers.ToArray(),
				total,
				(int)agent,
				now,
				useless,
				WholePaymentProcessor.NoPaymentProcessor.ToString()
			);

			Integration.Kafka.Send(itsThePresent, Integration.Kafka.TopicForFragmentsCreation, message);
		}

		protected void SendFragmentPaymentMessage(bool itsThePresent, DateTime now, string strFromCurrencyCode, string fromAddress, int authorizationId, string who, WagerStatus status, string concept, Currency net, string thirdPartyInAccount, string thirdPartyOutAccount, TransactionType transactionType, TransactionStatus transactionStatus)
		{
			if (!itsThePresent) return;

			Coin fromCurrencyCode = Coinage.Coin(strFromCurrencyCode);

			using (FragmentPaymentCompressor buffer = new FragmentPaymentCompressor(itsThePresent, Integration.Kafka.TopicForFragmentPaymentsForAll))
			{
				buffer.BeforeSendTheFirstMessage = buffer.GenerateHeader(who, Store.Id, ProcessorAccountId, false, now);
				var gradedWagerMessage = new FragmentPaymentMessage(
					fromCurrencyCode,
					fromAddress,
					authorizationId,
					1,
					status,
					now,
					0,
					0,
					(int)Agents.INSIDER
				);
				buffer.Send(gradedWagerMessage);
			}
		}

		protected void SendFragmentPaymentMessageWithFee(bool itsThePresent, DateTime now, string strFromCurrencyCode, string fromAddress, int authorizationId, string who, WagerStatus status, string concept, Currency net, string thirdPartyInAccount, string thirdPartyOutAccount, TransactionType transactionType, TransactionStatus transactionStatus, decimal fee)
		{
			if (!itsThePresent) return;
			if (fee <= 0) throw new GameEngineException($"{nameof(fee)} must be greater than 0");

			Coin fromCurrencyCode = Coinage.Coin(strFromCurrencyCode);
			using (FragmentPaymentCompressor buffer = new FragmentPaymentCompressor(itsThePresent, Integration.Kafka.TopicForFragmentPaymentsForAll))
			{
				buffer.BeforeSendTheFirstMessage = buffer.GenerateHeader(who, Store.Id, ProcessorAccountId, false, now);
				var gradedWagerMessage = new FragmentPaymentMessage(
					fromCurrencyCode,
					fromAddress,
					authorizationId,
					1,
					status,
					now,
					0,
					0,
					(int)Agents.INSIDER
				);
				buffer.Send(gradedWagerMessage);

				gradedWagerMessage = new FragmentPaymentMessage(
						fromCurrencyCode,
						fromAddress,
						authorizationId,
						2,
						status,
						now,
						0,
						0,
					(int)Agents.INSIDER
					);
				buffer.Send(gradedWagerMessage);
			}
		}
	}

	public class HeaderExchangeTransactionMessage : TypedMessage
	{
		public TransactionType Type { get; private set; }
		public TransactionStatus Status { get; private set; }

		internal HeaderExchangeTransactionMessage(TransactionType type, TransactionStatus status) : base((char)(int)TransactionMessageType.Transaction)
		{
			Type = type;
			Status = status;
		}

		public HeaderExchangeTransactionMessage(string message) : base(message)
		{

		}

		public TransactionMessageType MessageType
		{
			get
			{
				return (TransactionMessageType)(int)base.MessageTypeSelector;
			}
		}

		protected override void InternalSerialize()
		{
			base.InternalSerialize();
			AddProperty((int)Type).
			AddProperty((int)Status);
		}

		protected override void Deserialize(string[] message, out int fieldOrder)
		{
			base.Deserialize(message, out fieldOrder);
			this.Type = (TransactionType)int.Parse(message[fieldOrder++]);
			this.Status = (TransactionStatus)int.Parse(message[fieldOrder++]);
		}
	}

	public class ApprovedTransactionMessage : HeaderExchangeTransactionMessage
	{
		public int Id { get; private set; }
		public DateTime ApprovalDate
		{
			get
			{
				return new DateTime(DateTicks);
			}
		}
		public long DateTicks { get; private set; }
		public decimal Profit { get; private set; }
		public decimal Purchase { get; private set; }
		public decimal Sale { get; private set; }
		public Coin FromCurrencyCode { get; private set; }
		public Coin ToCurrencyCode { get; private set; }
		public string EmployeeName { get; private set; }
		public int AuthorizationId { get; private set; }
		public int ProcessorId { get; private set; }

		public ApprovedTransactionMessage(TransactionType type, int id, DateTime approvalDate, decimal profit, decimal purchase, decimal sale, Coin fromCurrencyCode, Coin toCurrencyCode,
			string employeeName, int authorizationId, int processorId) :
			base(type, TransactionStatus.APPROVED)
		{
			Id = id;
			DateTicks = approvalDate.Ticks;
			Profit = profit;
			Purchase = purchase;
			Sale = sale;
			FromCurrencyCode = fromCurrencyCode;
			ToCurrencyCode = toCurrencyCode;
			EmployeeName = employeeName;
			AuthorizationId = authorizationId;
			ProcessorId = processorId;
		}

		public ApprovedTransactionMessage(string serialized) : base(serialized)
		{

		}

		protected override void InternalSerialize()
		{
			base.InternalSerialize();
			AddProperty(Id).
			AddProperty(DateTicks).
			AddProperty(Profit).
			AddProperty(Purchase).
			AddProperty(Sale).
			AddProperty(FromCurrencyCode.Iso4217Code).
			AddProperty(ToCurrencyCode.Iso4217Code).
			AddProperty(EmployeeName).
			AddProperty(AuthorizationId).
			AddProperty(ProcessorId);
		}

		protected override void Deserialize(string[] message, out int fieldOrder)
		{
			base.Deserialize(message, out fieldOrder);
			Id = int.Parse(message[fieldOrder++]);
			DateTicks = long.Parse(message[fieldOrder++]);
			Profit = decimal.Parse(message[fieldOrder++]);
			Purchase = decimal.Parse(message[fieldOrder++]);
			Sale = decimal.Parse(message[fieldOrder++]);
			FromCurrencyCode = Coinage.KafkaProperty2Coin(message[fieldOrder++]);
			ToCurrencyCode = Coinage.KafkaProperty2Coin(message[fieldOrder++]);
			EmployeeName = message[fieldOrder++];
			AuthorizationId = int.Parse(message[fieldOrder++]);
			ProcessorId = int.Parse(message[fieldOrder++]);
		}
	}

	public class DeniedTransactionMessage : HeaderExchangeTransactionMessage
	{
		public int Id { get; private set; }
		public DateTime RejectionDate
		{
			get
			{
				return new DateTime(DateTicks);
			}
		}
		public long DateTicks { get; private set; }
		public string Reason { get; private set; }
		public string EmployeeName { get; private set; }

		public DeniedTransactionMessage(TransactionType type, int id, DateTime rejectionDate, string reason, string employeeName) :
			base(type, TransactionStatus.DENIED)
		{
			Id = id;
			DateTicks = rejectionDate.Ticks;
			Reason = reason;
			EmployeeName = employeeName;
		}

		public DeniedTransactionMessage(string serialized) : base(serialized)
		{

		}

		protected override void InternalSerialize()
		{
			base.InternalSerialize();
			AddProperty(Id).
			AddProperty(DateTicks).
			AddProperty(Reason).
			AddProperty(EmployeeName);
		}

		protected override void Deserialize(string[] message, out int fieldOrder)
		{
			base.Deserialize(message, out fieldOrder);
			Id = int.Parse(message[fieldOrder++]);
			DateTicks = long.Parse(message[fieldOrder++]);
			Reason = message[fieldOrder++];
			EmployeeName = message[fieldOrder++];
		}
	}

	public class JournalEntryMessage : TypedMessage
	{
		public TransactionMessageType MessageType { get; private set; }
		public TransactionType Type { get; private set; }
		public int TransactionId { get; private set; }
		public int JournalEntryId { get; private set; }
		public string EmployeeName { get; private set; }
		public long DateTicks { get; private set; }
		public decimal SaleRate { get; private set; }
		public decimal PurchaseRate { get; private set; }

		public DateTime ApprovalDate
		{
			get
			{
				return new DateTime(DateTicks);
			}
		}
		public string ApprovalDateFormattedAsText
		{
			get
			{
				return ApprovalDate.ToString("MM/dd/yyyy HH:mm:ss");
			}
		}

		internal JournalEntryMessage(TransactionType type, int transactionId, DateTime date, int journalEntryId, string employeeName, decimal saleRate, decimal purchaseRate) : base((char)(int)TransactionMessageType.JournalEntry)
		{
			Type = type;
			TransactionId = transactionId;
			DateTicks = date.Ticks;
			JournalEntryId = journalEntryId;
			EmployeeName = employeeName;
			SaleRate = saleRate;
			PurchaseRate = purchaseRate;
		}

		public JournalEntryMessage(string message) : base(message)
		{

		}

		protected override void InternalSerialize()
		{
			base.InternalSerialize();
			AddProperty((int)Type).
			AddProperty(TransactionId).
			AddProperty(DateTicks).
			AddProperty(JournalEntryId).
			AddProperty(EmployeeName).
			AddProperty(SaleRate).
			AddProperty(PurchaseRate);
		}

		protected override void Deserialize(string[] message, out int fieldOrder)
		{
			base.Deserialize(message, out fieldOrder);
			Type = (TransactionType)int.Parse(message[fieldOrder++]);
			TransactionId = int.Parse(message[fieldOrder++]);
			DateTicks = long.Parse(message[fieldOrder++]);
			JournalEntryId = int.Parse(message[fieldOrder++]);
			EmployeeName = message[fieldOrder++];
			SaleRate = decimal.Parse(message[fieldOrder++]);
			PurchaseRate = decimal.Parse(message[fieldOrder++]);
		}
	}
	public class ApprovalForWithdrawalsMessage : TypedMessage
	{
		public TransactionType Type { get; private set; }
		public int TransactionId { get; private set; }
		public string AgentPath { get; private set; }
		public string EmployeeName { get; private set; }
		public string ProcessorKey { get; private set; }

		internal ApprovalForWithdrawalsMessage(int id, string agentPath, string employeeName, string processorKey) : base((char)(int)TransactionType.Withdrawal)
		{
			if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");
			if (string.IsNullOrWhiteSpace(agentPath)) throw new ArgumentNullException(nameof(agentPath));
			if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

			TransactionId = id;
			AgentPath = agentPath;
			EmployeeName = employeeName;
			ProcessorKey = processorKey;
		}

		public ApprovalForWithdrawalsMessage(string message) : base(message)
		{

		}

		protected override void InternalSerialize()
		{
			base.InternalSerialize();
			AddProperty(TransactionId).
			AddProperty(AgentPath).
			AddProperty(EmployeeName).
			AddProperty(ProcessorKey);
		}

		protected override void Deserialize(string[] message, out int fieldOrder)
		{
			base.Deserialize(message, out fieldOrder);
			TransactionId = int.Parse(message[fieldOrder++]);
			AgentPath = message[fieldOrder++];
			EmployeeName = message[fieldOrder++];
			ProcessorKey = message[fieldOrder++];
		}
	}
}