﻿using GamesEngine.Gameboards.Lines;
using GamesEngine.Games.Tournaments;
using GamesEngine.RealTime.Events;
using GamesEngine.Settings;
using GamesEngine.Time;
using System;
using System.Collections.Generic;

namespace GamesEngine.Games.Lines
{
	internal class TotalPointsLine : OverUnderLine
	{
		private readonly ScorePair realAnswer;
		internal const string TEXT = "Total Points Line";
		internal TotalPointsLine(int lineId, Question question, Tier tier, Game game, Shelve shelve, int index, double score, int overReward, int underReward, string who, DateTime now) : base(lineId, question, tier, game, shelve, index, score, overReward, underReward, question.Text, who, now)
		{
			ValidateSpread(score, game.Sport);
			this.realAnswer = new ScorePair(game);
		}

		private TotalPointsLine(TotalPointsLine previousLine, int overReward, int underReward, string who, DateTime now) : base(previousLine, overReward, underReward, who, now)
		{
			this.realAnswer = null;
		}

		internal new TotalPointsLine NewVersion(int overReward, int underReward, string who, DateTime now)
		{
			TotalPointsLine result = new TotalPointsLine(this, overReward, underReward, who, now);
			return result;
		}

		internal void SetRealAnswer(Team teamA, int scoreA, Team teamB, int scoreB)
		{
			if (teamA == null) new ArgumentNullException(nameof(teamA));
			if (teamB == null) new ArgumentNullException(nameof(teamB));
			if (this.Game.TeamA != teamA || this.Game.TeamB != teamB) throw new GameEngineException("The team is not valid for this line.");
			if (scoreA == scoreB && !Game.Sport.AllowsTieResults()) throw new GameEngineException($"Sport {Game.Sport.Name} does not allow tie results");
			if (scoreA < 0) throw new GameEngineException($"{teamA.Name}'s score can not be negative");
			if (scoreB < 0) throw new GameEngineException($"{teamB.Name}'s score can not be negative");
			if (this != this.OriginalVersion) throw new GameEngineException("Answer should be set only for the original line");
			if (!game.ScoreIncludes(teamA, scoreA, teamB, scoreB)) throw new GameEngineException($"Game score has never been set as {scoreA} / {scoreB}");

			this.realAnswer.ScoreTeamA = GamesEngine.Games.Score.WithPoints(scoreA);
			this.realAnswer.ScoreTeamB = GamesEngine.Games.Score.WithPoints(scoreB);

			var realTotalPoints = scoreA + scoreB;
			var score = base.Score;
			if (realTotalPoints > score)
			{
				this.SetRealAnswerAsOver();
			}
			else if (realTotalPoints < score)
			{
				this.SetRealAnswerAsUnder();
			}
			else
			{
				base.ChangeToNoAction();
				this.SetRealAnswerAsNoAction();
			}
		}

		internal override decimal Grade(Wager wager)
		{
			if (Tier == Tier.TIER_ONE && !this.game.IsGameOver()) throw new GameEngineException($"This game {this.game.ToString()} can not be graded because it is not ended yet.");

			decimal prize = base.Grade(wager);
			wager.Showcase.RiskAssestment.UpdateGrade(wager);

			return prize;
		}

		internal override string LineTypeAsString => LineType.TOTAL_POINTS_LINE.ToString();

		internal override void Serialize(KafkaMessage message)
		{
			if (!game.IsGameOver() && !Showcase.IsThereAnyPendingLine()) throw new GameEngineException("Line should be serialized only to send to BI and the end of grades");
			if (!IsOriginalVersion()) PreviousVersion.Serialize(message);

			message.AddProperty((char)LineType.TOTAL_POINTS_LINE);
			message.AddProperty(base.LineId);
			message.AddProperty(base.Version);
			message.AddProperty(base.Score);
			message.AddProperty(base.OverReward);
			message.AddProperty(base.UnderReward);
			message.AddProperty(base.RealAnswer == OverUnderAnswer.OVER ? 'O' : 'U');
			message.AddProperty((int)base.Grading);
			message.AddProperty((int)base.BasedOn.Id);
		}

		protected override LineEvent GetChangedLineEvent(DateTime timestamp)
		{
			var result = new ChangedTotalPointsLineEvent(timestamp, this);
			return result;
		}
	}
}
