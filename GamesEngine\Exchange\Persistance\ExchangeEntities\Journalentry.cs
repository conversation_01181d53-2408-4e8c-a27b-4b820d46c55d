﻿using System;
using System.Collections.Generic;

namespace ExchangeAPI.ExchangeEntities
{
    public partial class Journalentry
    {
        public Journalentry()
        {
            CreditNote = new HashSet<CreditNote>();
            DebitNote = new HashSet<DebitNote>();
            Deposit = new HashSet<Deposit>();
            Journalentrydetails = new HashSet<Journalentrydetails>();
            Transfer = new HashSet<Transfer>();
            Withdrawal = new HashSet<Withdrawal>();
        }

        public long Id { get; set; }
        public DateTime Date { get; set; }
        public string Title { get; set; }
        public string Reference { get; set; }
        public string Systemid { get; set; }
        public string Journalentrydetailid { get; set; }
        public long Whocreated { get; set; }

        public virtual Exchangeuser WhocreatedNavigation { get; set; }
        public virtual ICollection<CreditNote> CreditNote { get; set; }
        public virtual ICollection<DebitNote> DebitNote { get; set; }
        public virtual ICollection<Deposit> Deposit { get; set; }
        public virtual ICollection<Journalentrydetails> Journalentrydetails { get; set; }
        public virtual ICollection<Transfer> Transfer { get; set; }
        public virtual ICollection<Withdrawal> Withdrawal { get; set; }
    }
}
