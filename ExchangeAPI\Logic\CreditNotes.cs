﻿using ExchangeAPI.ExchangeEntities;
using ExchangeAPI.Model;
using ExchangeAPI.Service;
using GamesEngine;
using GamesEngine.Finance;
using GamesEngine.Settings;
using Microsoft.EntityFrameworkCore;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Currency = GamesEngine.Finance.Currency;

namespace ExchangeAPI.Logic
{
    public class CreditNotes
    {
        private const string ALL_SELECTED = "all";
        public const int MAXIMUM_AMOUNT_OF_ROWS_ALLOWED = 100;

        public static CreditNotesDTO FilteredBy(DateTime startDate, DateTime endDate, string state, string identificationNumber, string transactionId, string currencyCode, string cashierName, 
            string domain, int initialIndex, int amountOfRows)
        {
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate.Hour != 0 || startDate.Minute != 0 || startDate.Second != 0) throw new GameEngineException($"{nameof(startDate)} {startDate} is not valid");
            if (endDate.Hour != 0 || endDate.Minute != 0 || endDate.Second != 0) throw new GameEngineException($"{nameof(endDate)} {endDate} is not valid");
            if (string.IsNullOrWhiteSpace(state)) throw new ArgumentNullException(nameof(state));
            if (string.IsNullOrWhiteSpace(identificationNumber)) throw new ArgumentNullException(nameof(identificationNumber));
            if (string.IsNullOrWhiteSpace(transactionId)) throw new ArgumentNullException(nameof(transactionId));
			if (string.IsNullOrWhiteSpace(cashierName)) throw new ArgumentNullException(nameof(cashierName));
            if (string.IsNullOrWhiteSpace(domain)) throw new ArgumentNullException(nameof(domain));
            if (initialIndex < 0) throw new GameEngineException($"{nameof(initialIndex)} {initialIndex} is not valid");
            if (amountOfRows <= 0) throw new GameEngineException($"{nameof(amountOfRows)} {amountOfRows} is not valid");

            if (amountOfRows > MAXIMUM_AMOUNT_OF_ROWS_ALLOWED)
            {
                amountOfRows = MAXIMUM_AMOUNT_OF_ROWS_ALLOWED;
            }

            var allStatesSelected = state.ToLower().Trim() == ALL_SELECTED;
            var allIdentificationNumberSelected = identificationNumber.ToLower().Trim() == ALL_SELECTED;
            var allTransactionIdsSelected = transactionId.ToLower().Trim() == ALL_SELECTED;
            var allCurrencyCode = currencyCode.ToLower().Trim() == ALL_SELECTED;
			var allCashiersSelected = cashierName.ToLower().Trim() == ALL_SELECTED;
            var allDomainsSelected = domain.ToLower().Trim() == ALL_SELECTED;

            using (var context = new ExchangeContext())
            {
                var creditNotes = context.CreditNote
                    .Include(creditNote => creditNote.WhocreatedNavigation)
                    .Include(creditNote => creditNote.DomainNavigation)
                    .Include(creditNote => creditNote.CreditNoteAttachment)
                    .Include(creditNote => creditNote.Currency)
                    .Where(creditNote => ! creditNote.Deleted && creditNote.Datecreated.Date >= startDate && creditNote.Datecreated.Date <= endDate
                        && (allStatesSelected || (creditNote.Approvals == creditNote.Approvalsrequired && state == TransactionState.Approved.ToString()) || (creditNote.Rejections > 0 && state == TransactionState.Rejected.ToString()) || (creditNote.Approvals == 0 && creditNote.Rejections == 0 && state == TransactionState.Pending.ToString()))
                        && (allIdentificationNumberSelected || creditNote.Customer == identificationNumber)
                        && (allTransactionIdsSelected || creditNote.Id.ToString() == transactionId)
                        && (allCurrencyCode || creditNote.Currency.Isocode == currencyCode)
						&& (allCashiersSelected || creditNote.WhocreatedNavigation.Name == cashierName)
                        && (allDomainsSelected || creditNote.DomainNavigation.Domain == domain)
                        )
                    .OrderBy(creditNote => creditNote.Datecreated);
                var creditNotesPaged = creditNotes
                    .Skip(initialIndex)
                    .Take(amountOfRows);
                var creditNotesDTO = creditNotesPaged.MapToCreditNotesDTO();
                creditNotesDTO.TotalRecords = creditNotes.Count();
                creditNotesDTO.TotalAmount = creditNotes.Sum(creditNote => creditNote.Amount);
                creditNotesDTO.TotalAmountFormatted = allCurrencyCode ? 
                    Currency.ToDisplayFormatWithoutSign(GamesEngine.Finance.Currencies.CODES.USD.ToString(), creditNotesDTO.TotalAmount) : 
                    Currency.Factory(currencyCode, creditNotesDTO.TotalAmount).ToDisplayFormat();
                return creditNotesDTO;
            }
        }

		private static CreditNote CreditNoteFor(int id)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");

            using (var context = new ExchangeContext())
            {
                var creditNoteFound = context.CreditNote
                    .Where(creditNote => ! creditNote.Deleted && creditNote.Id == id)
                    .FirstOrDefault();
                return creditNoteFound;
            }
        }

        public static void Save(CreditNote creditNote)
        {
            if (creditNote == null) throw new ArgumentNullException(nameof(creditNote));

            using (var context = new ExchangeContext())
            {
                context.CreditNote.Add(creditNote);
                context.SaveChanges();
            }
        }

        public static void SaveWithAttachments(CreditNote creditNote, IEnumerable<string> urls)
        {
            if (creditNote == null) throw new ArgumentNullException(nameof(creditNote));

            using (var context = new ExchangeContext())
            {
                using (var transaction = context.Database.BeginTransaction())
                {
                    try
                    {
                        context.CreditNote.Add(creditNote);
                        context.SaveChanges();

                        var attachments = new List<CreditNoteAttachment>();
                        foreach (var url in urls)
                        {
                            attachments.Add(new CreditNoteAttachment()
                            {
                                Creditnoteid = creditNote.Id,
                                Url = url
                            });
                        }

                        context.CreditNoteAttachment.AddRange(attachments);
                        context.SaveChanges();
                        transaction.Commit();
                    }
                    catch (Exception e)
                    {
                        transaction.Rollback();
                        var message = $"{nameof(SaveWithAttachments)} method cannot add id:{creditNote.Id}";
                        Loggers.GetIntance().Db.Error(message, e);
                        ErrorsSender.Send(e, message);
                        throw;
                    }
                }
            }
        }

        public static CreditNote UpdateAsApproved(int id, DateTime date, string employeeName, int processorId)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");

            var user = Integration.ExchangeUsers.User(employeeName);
            var creditNote = CreditNoteFor(id);
            if (creditNote == null) throw new ArgumentNullException(nameof(creditNote));

            creditNote.Approvals = 1;
            creditNote.Whoapproved = user.Id;
            creditNote.Dateapproved = date;
            creditNote.Processorid = processorId;
            using (var context = new ExchangeContext())
            {
                context.CreditNote.Update(creditNote);
                context.SaveChanges();
            }
            return creditNote;
        }

        public static CreditNote UpdateJournalEntry(ExchangeContext context, int id, string journalEntryDetailId)
        {
            if (context == null) throw new ArgumentNullException(nameof(context));
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(journalEntryDetailId)) throw new ArgumentNullException(nameof(journalEntryDetailId));

            var creditNote = CreditNoteFor(id);
            if (creditNote == null) throw new ArgumentNullException(nameof(creditNote));

            creditNote.Journalentrydetailid = journalEntryDetailId;
            context.CreditNote.Update(creditNote);
            context.SaveChanges();
            return creditNote;
        }

        public static CreditNote UpdateAsRejected(int id, DateTime date, string rejectionReason, string employeeName)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");

            var user = Integration.ExchangeUsers.User(employeeName);
            var creditNote = CreditNoteFor(id);
            if (creditNote == null) throw new ArgumentNullException(nameof(creditNote));

            creditNote.Rejections = 1;
            creditNote.Whorejected = user.Id;
            creditNote.Daterejected = date;
            creditNote.Rejectionreason = rejectionReason;
            using (var context = new ExchangeContext())
            {
                context.CreditNote.Update(creditNote);
                context.SaveChanges();
            }
            return creditNote;
        }

        public static CreditNote UpdateVoucherUrl(int id, string voucherUrl)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(voucherUrl)) throw new ArgumentNullException(nameof(voucherUrl));

            var creditNote = CreditNoteFor(id);
            if (creditNote == null) throw new ArgumentNullException(nameof(creditNote));

            creditNote.Voucherurl = voucherUrl;
            using (var context = new ExchangeContext())
            {
                context.CreditNote.Update(creditNote);
                context.SaveChanges();
            }
            return creditNote;
        }
    }
}
