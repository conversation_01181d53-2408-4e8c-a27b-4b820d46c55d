﻿using ExchangeAPI.ExchangeEntities;
using GamesEngine.Exchange.Persistance;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ExchangeAPI.Logic
{
    public class ExchangeUsers : IExchangeUsers
    {
        public ExchangeUsers()
        {
        }
        public Exchangeuser User(string name)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

            var userOrNull = UserOrNull(name);
            var existsUser = userOrNull != null;
            if (!existsUser)
            {
                userOrNull = new Exchangeuser() { Name = name };
                Save(userOrNull);
            }
            return userOrNull;
        }

        private static Exchangeuser UserOrNull(string name)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

            using (var context = new ExchangeContext())
            {
                var user = context.Exchangeuser.FirstOrDefault(rate => rate.Name == name);
                return user;
            }
        }

        public static void Save(Exchangeuser exchangeUser)
        {
            if (exchangeUser == null) throw new ArgumentNullException(nameof(exchangeUser));

            using (var context = new ExchangeContext())
            {
                context.Exchangeuser.Add(exchangeUser);
                context.SaveChanges();
            }
        }
    }
}
