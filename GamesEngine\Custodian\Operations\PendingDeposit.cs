﻿using GamesEngine.Domains;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using GamesEngine.PurchaseOrders;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using town.connectors.drivers;
using static GamesEngine.Business.WholePaymentProcessor;
using static GamesEngine.Custodian.Operations.DisbursementExecutionResponse;
using static GamesEngine.Exchange.PaymentProcessorsAndActionsByDomains;
using static town.connectors.drivers.Result;

namespace GamesEngine.Custodian.Operations
{
	[Puppet]
	internal sealed class PendingDeposit : Deposit
	{
		public PendingDeposit(Guardian guardian, DateTime creationDate, int transactionId, int authorizationId, string description, Currency amount, int group, PaymentProcessor processor, Domain domain, 
			string identificationDocumentNumber, string accountNumber)
			: base(guardian, 
				  creationDate, 
				  transactionId, 
				  authorizationId,
				  description, 
				  amount, 
				  group, 
				  processor, 
				  domain,
				  StatusCodes.PENDING,
				  identificationDocumentNumber,
				  accountNumber)
		{
		}

		internal CompleteDeposit Execute(bool itsThePresent, DateTime now, TransactionStatus executionStatus, string detail, string who, int storeId)
		{
			List<PlatformEvent> monitorEvents = new List<PlatformEvent>();

			CompleteDeposit completeDeposit = new CompleteDeposit(Guardian,
				  CreationDate,
				  TransactionId,
				  AuthorizationId,
				  Description,
				  Amount,
				  Group,
				  Processor,
				  Domain,
				  Identifier,
				  AccountNumber);

			DepositExecution depositExecution = new DepositExecution(completeDeposit);

			Integration.Kafka.Send(itsThePresent, Integration.Kafka.TopicForGuardianInternalOperations, completeDeposit.GenerateMessage(now));

			Guardian.RemoveTransaction(this);

			using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itsThePresent, Integration.Kafka.TopicForDeposits))
			{
				using (KafkaMessagesBuffer buffer2 = new KafkaMessagesBuffer(itsThePresent, Integration.Kafka.TopicForWithdrawalsDisbursementExecution))
				{
					buffer2.Send(depositExecution.GenerateMessage(now, who, executionStatus));

					monitorEvents.Add(new DisbursmentEvent(
						now,
						depositExecution.Id,
						depositExecution.Account.Number,
						depositExecution.Account.Coin,
						depositExecution.Status,
						depositExecution.Amount,
						depositExecution.ScheduledDate,
						depositExecution.TransactionId,
						depositExecution.Processor.Driver.Id));
				}

				var processorAccount = Guardian.Accounts().SearchBy(Processor);
				DepositMessage message = new DepositMessage(
					processorAccount.Number,
					depositExecution.Account.Coin,
					storeId,
					depositExecution.Amount,
					who,
					$"Deposit Transaction: {TransactionId} ",//TODO cris cambiar esta descripción,
					""+AuthorizationId,
					depositExecution.Account.Number,
					processorAccount.Id,
					processorAccount.ProcessorKey,
					PaymentChannels.Agents.INSIDER
				);
				buffer.Send(message);
			}

			PlatformMonitor.GetInstance().WhenNewEvents(monitorEvents);

			return completeDeposit;
		}

		internal override InternalOperationUpdateMessage GenerateMessage(DateTime now)
		{
			throw new NotImplementedException();
		}
	}
}
