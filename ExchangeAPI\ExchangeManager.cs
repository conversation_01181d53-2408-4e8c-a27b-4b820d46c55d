using GamesEngine;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using System;

namespace ExchangeAPI
{
	internal static class ExchangeManagerAPI
    {
		private static ExchangeActor actor = new ExchangeActor();
		internal static RestAPIActorAsync ExchangeManager = new RestAPIActorAsync(actor);

		static internal void LoadFrom(ExchangeActor otherActor)
		{
			actor = otherActor;
			ExchangeManager = new RestAPIActorAsync(otherActor);
		}

	}
}
