﻿using Connectors.town.connectors.commons;
using Connectors.town.connectors.driver.transactions;
using Connectors.town.connectors.drivers.hades;
using GamesEngine.Business;
using GamesEngine.Custodian.Operations;
using GamesEngine.Domains;
using GamesEngine.Exchange;
using GamesEngine.Marketing;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using town.connectors.commons;
using town.connectors.drivers;
using town.connectors.drivers.artemis;
using town.connectors.drivers.fiero;
using town.connectors.drivers.hades;
using static Connectors.town.connectors.drivers.artemis.Fragment_an_Authorization;
using static GamesEngine.Business.WholePaymentMethods;
using static GamesEngine.Business.WholePaymentProcessor;
using static town.connectors.CustomSettings;
using static town.connectors.drivers.Result;
using DepositTransaction = Connectors.town.connectors.driver.transactions.DepositTransaction;
using DespositResponse = town.connectors.commons.DespositResponse;

namespace GamesEngine.Finance
{	
	public class PaymentChannels
	{
		public enum Agents
		{
			INSIDER = 0,
			TEST_BOOK = 1,
			TEST_BOOK_DGS = 2,
			ARTEMIS = 3,
			ZEUS = 4
		}

		private PaymentChannels(){}

		internal static async Task<DespositResponse> LockAsync(
			HttpContext context,
			Agents agent, 
			string atAddress,
			string account, 
			string decription, 
			DateTime date, 
			string employeeName, 
			Coin currencyCode, 
			decimal total, 
			int storeId,
			Domain domain,
            DateTime useless
            )
		{
			bool itIsThePresent = true;
			DespositResponse response = new DespositResponse(-1, TransactionStatus.DENIED, string.Empty);
			DateTime now = date;

			if (agent == Agents.INSIDER)
			{
				var paymentProcessor = WholePaymentProcessor.Instance().SearchSaleProcessorBy(currencyCode.Iso4217Code, typeof(town.connectors.drivers.fiero.Authorization));
				using (RecordSet recordSet = paymentProcessor.GetRecordSet())
                {
                    recordSet.SetParameter("accountNumber", string.Empty);
                    recordSet.SetParameter("atAddress", atAddress);
                    recordSet.SetParameter("purchaseTotal.Value", total);
					recordSet.SetParameter("purchaseTotal.CurrencyCode", currencyCode.Iso4217Code);
					recordSet.SetParameter("storeId", storeId);
					recordSet.SetParameter("concept", decription);
					recordSet.SetParameter("fragmentInformation", null);
					recordSet.SetParameter("referenceNumber", null);
                    recordSet.SetParameter("useless", useless);
                    recordSet.SetParameter("context", context);

					AuthorizationTransaction result = await paymentProcessor.ExecuteAsync<AuthorizationTransaction>(now, recordSet);
					response = new DespositResponse(result.AuthorizationId, result.Status, paymentProcessor.ProcessorKey);
				}
            }
			else if (agent == Agents.TEST_BOOK)
			{
				var paymentProcessor = WholePaymentProcessor.Instance().SearchSaleProcessorBy(currencyCode.Iso4217Code);
				using (RecordSet recordSet = paymentProcessor.GetRecordSet())
				{
					recordSet.SetParameter("accountNumber", account);
					recordSet.SetParameter("atAddress", atAddress);
					recordSet.SetParameter("purchaseTotal.Value", total);
					recordSet.SetParameter("purchaseTotal.CurrencyCode", currencyCode.Iso4217Code);
					recordSet.SetParameter("storeId", storeId);
					recordSet.SetParameter("concept", decription);
					recordSet.SetParameter("fragmentInformation", null);
					recordSet.SetParameter("referenceNumber", null);
                    recordSet.SetParameter("useless", useless);
                    recordSet.SetParameter("context", context);

					AuthorizationTransaction result = await paymentProcessor.ExecuteAsync<AuthorizationTransaction>(now, recordSet);
					response = new DespositResponse(result.AuthorizationId, result.Status, paymentProcessor.ProcessorKey);
				}
			}
			else if (agent == Agents.ZEUS)
			{
				var paymentProcessor = WholePaymentProcessor.Instance().SearchDepositProcessorBy(currencyCode.Iso4217Code);
				using (RecordSet recordSet = paymentProcessor.GetRecordSet())
				{
					recordSet.SetParameter("customerId", atAddress);
					recordSet.SetParameter("amount", total);
					recordSet.SetParameter("description", decription);

					var result = paymentProcessor.Execute<DepositTransaction>(now, recordSet);
					response = new DespositResponse(result.AuthorizationId, TransactionStatus.APPROVED, paymentProcessor.ProcessorKey);
					
					NotifyOperation(
					itIsThePresent,
					now,
					account,
					response.AuthorizationId,
					decription,
					Currency.Factory(currencyCode.Iso4217Code, total),
					atAddress,
					TransactionType.Deposit,
					domain.Id,
					domain.Url,
					new TransactionFees(),
					new Execution(result.Status, ""),
					paymentProcessor.Group,
					paymentProcessor.Name,
					storeId,
					employeeName
					);
				}

			}
			else
			{
				throw new GameEngineException($"Agent {agent} is not supported yet.");
			}
			return response;
		}

        public const int FAKE_DOCUMENT_NUMBER = -1;
        internal static async Task<FragmentResponse> LockWithExternalResponseAsync(
			HttpContext context,
			Agents agent,
			string atAddress,
			string account,
			string decription,
			DateTime date,
			string addicionalInfo,
			Coin currencyCode,
			decimal total,
			int storeId,
			Domain domain,
            DateTime useless,
			List<ToWinByDrawAndNumber> tickets
			)
		{
			FragmentResponse response;

			if (agent == Agents.ARTEMIS)
			{
				var artemisTickets = new List<ArtemisToWinByDrawAndNumber>();
				foreach (var ticket in tickets)
				{
					artemisTickets.Add(new ArtemisToWinByDrawAndNumber()
					{
						description = ticket.description,
						draw = ticket.draw,
						drawDate = ticket.drawDate,
						drawHour = ticket.drawHour,
						number = ticket.number,
						pick = ticket.pick,
						risk = ticket.risk,
						status = ticket.status,
						ticketId = ticket.ticketId,
						toWin = ticket.toWin,
						type = ticket.type,
						freePlay = currencyCode.Iso4217Code == "FP"
					});

				}
				PaymentProcessor paymentProcessor = WholePaymentProcessor.Instance().SearchFragmentProcessor(typeof(Connectors.town.connectors.drivers.artemis.Fragment_an_Authorization));
				using (RecordSet recordSet = paymentProcessor.GetRecordSet())
				{
					recordSet.SetParameter("customerId", atAddress);
					recordSet.SetParameter("amount", total);
					recordSet.SetParameter("description", decription);
					recordSet.SetParameter("toWinsByDrawAndNumber", artemisTickets);
					var result = paymentProcessor.Execute<InsertWagersResponse>(date, recordSet);

                    if (string.IsNullOrWhiteSpace(result.Response)) paymentProcessor.Driver.NotifyWarn(nameof(LockWithExternalResponseAsync), $"Url:{result.Url}\nResponse: {result.Response}", $"Response can not be empty");
                    switch (result.Code)
					{
						case AuthorizationResponseCode.OK:
                            response = new FragmentResponse(result.idTransaction, TransactionStatus.APPROVED, result, paymentProcessor.ProcessorKey);
                            break;
						case AuthorizationResponseCode.AuthorizationFail:
                            response = new FragmentResponse(FAKE_DOCUMENT_NUMBER, TransactionStatus.DENIED, AuthorizationResponseCode.AuthorizationFail, "Sorry, your purchase cannot be completed at this moment. Please try again. If the problem persists, please contact customer service.");
                            break;
                        case AuthorizationResponseCode.UnexpectedFormat:
							response = new FragmentResponse(FAKE_DOCUMENT_NUMBER, TransactionStatus.DENIED, AuthorizationResponseCode.UnexpectedFormat, "Sorry, your purchase cannot be completed at this moment. Please try again. If the problem persists, please contact customer service.");
                            break;
						case AuthorizationResponseCode.InsufficientFunds:
							response = new FragmentResponse(FAKE_DOCUMENT_NUMBER, TransactionStatus.DENIED, AuthorizationResponseCode.InsufficientFunds, "Sorry, your balance is not enough to purchase these tickets.");
                            break;
						default:
                            throw new GameEngineException($"Unknown error code {result.Code}");
					}
					if (result.Code != AuthorizationResponseCode.OK) paymentProcessor.Driver.NotifyWarn(nameof(LockWithExternalResponseAsync), $"Url:{result.Url}\nResponse: {result.Response}", result.Code.ToString());
                }

				if ((currencyCode.Iso4217Code == Currencies.CODES.FP.ToString() || currencyCode.Iso4217Code == Currencies.CODES.KRW.ToString() || currencyCode.Iso4217Code == Currencies.CODES.KRWFP.ToString()) && response.Status == TransactionStatus.APPROVED)
				{
					paymentProcessor = WholePaymentProcessor.Instance().SearchAuthorizationProcessorBy(typeof(AuthorizationMultiple));
					using (RecordSet recordSet = paymentProcessor.GetRecordSet())
					{
						recordSet.SetParameter("accountNumber", string.Empty);
						recordSet.SetParameter("atAddress", atAddress);
						recordSet.SetParameter("purchaseTotal.Value", total);
						recordSet.SetParameter("purchaseTotal.CurrencyCode", currencyCode.Iso4217Code);
						recordSet.SetParameter("storeId", storeId);
						recordSet.SetParameter("concept", decription);
						recordSet.SetParameter("fragmentInformation", null);
						recordSet.SetParameter("referenceNumber", null);
						recordSet.SetParameter("useless", useless);
						recordSet.SetParameter("context", context);
						recordSet.SetParameter("toWinsByDrawAndNumber", response.Response.tickets);
						AuthorizationTransaction result = await paymentProcessor.ExecuteAsync<AuthorizationTransaction>(date, recordSet);
                        if (result.Code == AuthorizationResponseCode.AuthorizationFail) response = new FragmentResponse(FAKE_DOCUMENT_NUMBER, TransactionStatus.DENIED, AuthorizationResponseCode.AuthorizationFail, "Sorry, your purchase cannot be completed at this moment. Please try again. If the problem persists, please contact customer service.");
						else if (result.Code == AuthorizationResponseCode.InsufficientFunds) response = new FragmentResponse(FAKE_DOCUMENT_NUMBER, TransactionStatus.DENIED, AuthorizationResponseCode.InsufficientFunds, "Sorry, your balance is not enough to purchase these tickets.");
                    }
				}
			}
			else
			{
				throw new GameEngineException($"Agent {agent} is not supported yet.");
			}
			return response;
		}

		private static void NotifyOperation(bool itsThePresent, DateTime creationDate, string accountNumber, int AuthorizationId, string description, Currency amount, string identificationDocumentNumber,
			TransactionType transactionType, int domainId, string domainUrl, TransactionFees fees, Execution execution, ProcessorPaymentMethod paymentMethod, string driverId, int storeId, string employeeName)
		{
			if (string.IsNullOrEmpty(description)) throw new ArgumentNullException(nameof(description));
			if (string.IsNullOrEmpty(domainUrl)) throw new ArgumentNullException(nameof(domainUrl));
			if (fees == null) throw new ArgumentNullException(nameof(fees));

			if (itsThePresent)
			{
				OperationMessage msn = new OperationMessage(
					accountNumber,
					transactionType,
					creationDate,
					AuthorizationId,
					description,
					amount,
					paymentMethod,
					driverId,
					domainId,
					domainUrl,
					fees,
					execution,
					identificationDocumentNumber,
					storeId,
					employeeName
					);

				Integration.Kafka.Send(itsThePresent, Integration.Kafka.TopicForGuardianOperations, msn);
			}
		}

		public struct ProcessorAccount
        {
            public int accountId { get; set; }
        }

		public static void Deposit(bool itsThePresent, DateTime now, RestAPISpawnerActor actor, DepositMessage message, out int authorization)
		{
			var description = Validator.StringEscape(message.Description);
			Currency amount = Currency.Factory(message.Currency, message.Amount);
            DepositTransaction result;
            if (message.Agent == Agents.INSIDER || message.Currency == Currencies.CODES.FP.ToString() || message.Currency == Currencies.CODES.KRW.ToString() || message.Currency == Currencies.CODES.KRWFP.ToString() || message.Currency == SpinKick.CURRENCY_CODE)//insider
			{
				PaymentProcessor paymentProcessor = WholePaymentProcessor.Instance().SearchDepositProcessorBy(message.Coin.Iso4217Code, typeof(town.connectors.drivers.fiero.Deposit));
				if (message.ProcessorId == 0)
                {
					var resultQry = actor.PerformCmd(RestAPISpawnerActor.GENERAL, $@"
					{{
						account = guardian.Accounts().SearchByProcessor('{paymentProcessor.ProcessorKey}');
						print account.Id accountId;
					}}
					");
					if (!(resultQry is OkObjectResult)) throw new GameEngineException($"No account for processor {paymentProcessor.ProcessorKey}");
					OkObjectResult o = (OkObjectResult)resultQry;
					string json = o.Value.ToString();
					var processorAccount = JsonConvert.DeserializeObject<ProcessorAccount>(json);

					message.ProcessorId = processorAccount.accountId;
				}
				
				using (RecordSet recordSet = paymentProcessor.GetRecordSet())
				{
					recordSet.SetParameter("message", message);
					recordSet.SetParameter("actor", actor);

					result = paymentProcessor.Execute<Connectors.town.connectors.driver.transactions.DepositTransaction>(now, recordSet);
					authorization = result.AuthorizationId;
				}
			}
			else 
			{
                var processorId = message.ProcessorId;
                if (message.ProcessorId == 0 && message.ProcessorKey != TransactionMessage.NoProcessorKey)
				{
					var resultQry = actor.PerformCmd(RestAPISpawnerActor.GENERAL, $@"
                    {{
                        account = guardian.Accounts().SearchByProcessor('{message.ProcessorKey}');
                        print account.Id accountId;
                    }}
                    ");
				    if (!(resultQry is OkObjectResult)) throw new GameEngineException($"No account for processor {message.ProcessorKey}");
				    OkObjectResult o = (OkObjectResult)resultQry;
				    string json = o.Value.ToString();
				    var processorAccount = JsonConvert.DeserializeObject<ProcessorAccount>(json);
				
				    processorId = processorAccount.accountId;
				}
				if (message.Agent == Agents.TEST_BOOK)
				{
					//authorization = (allAvailableDrivers[message.Agent].Driver as ASIdriver).AddAmount(message.AtAddress, message.Amount, message.Description, DateTime.Now, message.Reference);

					PaymentProcessor paymentProcessor = WholePaymentProcessor.Instance().SearchDepositProcessorBy(message.Coin.Iso4217Code, typeof(DepositNoReferenceId));
					using (RecordSet recordSet = paymentProcessor.GetRecordSet())
					{
						recordSet.SetParameter("customerId", message.AtAddress);
						recordSet.SetParameter("amount", message.Amount);
						recordSet.SetParameter("description", message.Description);
						recordSet.SetParameter("additionalInfo", message.Reference);

						result = paymentProcessor.Execute<Connectors.town.connectors.driver.transactions.DepositTransaction>(now, recordSet);
						authorization = result.AuthorizationId;
					}

				}
				else if (message.Agent == Agents.ARTEMIS)
				{
					var references = message.Reference.Split('-');
					if (references.Length < 2) throw new GameEngineException($"Reference does not have the right format.");
					authorization = int.Parse(references[0]);
				}
				else
				{
					//authorization = (allAvailableDrivers[message.Agent].Driver as DGSdriver).AddAmount(message.AtAddress, message.Amount, message.Description, message.Reference);

					PaymentProcessor paymentProcessor = WholePaymentProcessor.Instance().SearchDepositProcessorBy(message.Coin.Iso4217Code);
					using (RecordSet recordSet = paymentProcessor.GetRecordSet())
					{
						recordSet.SetParameter("customerId", message.AtAddress);
						recordSet.SetParameter("amount", message.Amount);
						recordSet.SetParameter("purchaseBody", message.Description);
						recordSet.SetParameter("description", message.Reference);
						recordSet.SetParameter("referenceID", message.Reference);

						result = paymentProcessor.Execute<Connectors.town.connectors.driver.transactions.DepositTransaction>(now, recordSet);
						authorization = result.AuthorizationId;
					}
				}
				string command = string.Empty;
				if (message.HasSource())
				{
					command = $@"
							{{
								source{message.SourceNumber} = atAddress.GetOrCreateSource(itIsThePresent, now, {message.SourceNumber}, {message.Currency}, '{message.SourceName}');
	                            atAddress.CreateAccountIfNotExists('{message.Currency}', '{message.AccountNumber}');
								store = company.Sales.StoreById({message.StoreId.ToString()});
								source{message.SourceNumber}.Accredit(itIsThePresent, Now, Currency('{message.Currency}', {message.Amount}), '{message.Who}', '{authorization}', store, '{description}', '{message.Reference}', '{message.AccountNumber}', {processorId});
								source{message.SourceNumber}.Withdraw(itIsThePresent, Now, Currency('{message.Currency}', {message.Amount}), '{message.Who}', '{authorization}', store, '{description}', '{message.Reference}','{message.AccountNumber}', {processorId});
							}}
						";
				}
				else if (!string.IsNullOrEmpty(message.AccountNumber))
				{
					command = $@"
							{{
								balance = atAddress.CreateAccountIfNotExists('{message.Currency}', '{message.AccountNumber}').Balance;
								store = company.Sales.StoreById({message.StoreId.ToString()});
								balance.Accredit(itIsThePresent, Now, Currency('{message.Currency}', {amount.Value}), '{message.Who}', '{authorization}', store, '{description}', '{message.Reference}', {processorId});
								balance.Withdraw(itIsThePresent, Now, Currency('{message.Currency}', {amount.Value}), '{message.Who}', '{authorization}', store, '{description}', '{message.Reference}', {processorId});
							}}";
				}
				else
				{
					command = $@"
							{{
								balance = atAddress.CreateBalanceIfNotExists('{message.Currency}');
								store = company.Sales.StoreById({message.StoreId.ToString()});
								balance.Accredit(itIsThePresent, Now, Currency('{message.Currency}', {amount.Value}), '{message.Who}', '{authorization}', store, '{description}', '{message.Reference}', {processorId});
								balance.Withdraw(itIsThePresent, Now, Currency('{message.Currency}', {amount.Value}), '{message.Who}', '{authorization}', store, '{description}', '{message.Reference}', {processorId});
							}}";
				}


				var result2 = actor.PerformCmd(message.AtAddress, command);
				if (!(result2 is OkObjectResult))
				{
					throw new GameEngineException($"Deposit was not saved because command fails on cashier.");
				}
			}
		}

        public static void WithDraw(DateTime now, RestAPISpawnerActor actor, WithdrawMessage message, out int authorization)
		{
			var description = Validator.StringEscape(message.Description);
			Currency amount = Currency.Factory(message.Currency, message.Amount);
            Connectors.town.connectors.driver.transactions.WithdrawalTransaction result;
			if (message.Agent == Agents.INSIDER || message.Currency == Currencies.CODES.FP.ToString() || message.Currency == Currencies.CODES.KRW.ToString() || message.Currency == Currencies.CODES.KRWFP.ToString() || message.Currency == SpinKick.CURRENCY_CODE)//insider
			{
				PaymentProcessor paymentProcessor = WholePaymentProcessor.Instance().SearchWithdrawalProcessorBy(message.Coin.Iso4217Code, typeof(town.connectors.drivers.fiero.Withdrawal));
				if (message.ProcessorId == 0)
				{
					var resultQry = actor.PerformCmd(RestAPISpawnerActor.GENERAL, $@"
					{{
						account = guardian.Accounts().SearchByProcessor('{paymentProcessor.ProcessorKey}');
						print account.Id accountId;
					}}
					");
					if (!(resultQry is OkObjectResult)) throw new GameEngineException($"No account for processor {paymentProcessor.ProcessorKey}");
					OkObjectResult o = (OkObjectResult)resultQry;
					string json = o.Value.ToString();
					var processorAccount = JsonConvert.DeserializeObject<ProcessorAccount>(json);

					message.ProcessorId = processorAccount.accountId;
				}

				using (RecordSet recordSet = paymentProcessor.GetRecordSet())
				{
					recordSet.SetParameter("message", message);
					recordSet.SetParameter("actor", actor);

					result = paymentProcessor.Execute<Connectors.town.connectors.driver.transactions.WithdrawalTransaction>(now, recordSet);
					authorization = result.AuthorizationId;
				}
			}
			else 
			{
                var processorId = message.ProcessorId;
                if (message.ProcessorId == 0 && message.ProcessorKey != TransactionMessage.NoProcessorKey)
                {
                    var resultQry = actor.PerformCmd(RestAPISpawnerActor.GENERAL, $@"
                    {{
                        account = guardian.Accounts().SearchByProcessor('{message.ProcessorKey}');
                        print account.Id accountId;
                    }}
                    ");
                    if (!(resultQry is OkObjectResult)) throw new GameEngineException($"No account for processor {message.ProcessorKey}");
                    OkObjectResult o = (OkObjectResult)resultQry;
                    string json = o.Value.ToString();
                    var processorAccount = JsonConvert.DeserializeObject<ProcessorAccount>(json);

                    processorId = processorAccount.accountId;
                }
                if (message.Agent == Agents.TEST_BOOK)
				{
					PaymentProcessor paymentProcessor = WholePaymentProcessor.Instance().SearchWithdrawalProcessorBy(message.Coin.Iso4217Code);
					using (RecordSet recordSet = paymentProcessor.GetRecordSet())
					{
						recordSet.SetParameter("customerId", message.AtAddress);
						recordSet.SetParameter("amount", message.Amount);
						recordSet.SetParameter("description", message.Description);
						recordSet.SetParameter("additionalInfo", message.Reference);

						result = paymentProcessor.Execute<Connectors.town.connectors.driver.transactions.WithdrawalTransaction>(now, recordSet);
						authorization = result.AuthorizationId;
					}
					//authorization = (allAvailableDrivers[message.Agent].Driver as ASIdriver).DeductAmount(message.AtAddress, message.Amount, message.Description, DateTime.Now, message.Reference);
				}
				else if (message.Agent == Agents.ARTEMIS)
				{
					var references = message.Reference.Split('-');
					if (references.Length < 2) throw new GameEngineException($"Reference does not have the right format.");
					authorization = int.Parse(references[0]);
				}
				else
				{
					PaymentProcessor paymentProcessor = WholePaymentProcessor.Instance().SearchWithdrawalProcessorBy(message.Coin.Iso4217Code);
					using (RecordSet recordSet = paymentProcessor.GetRecordSet())
					{
						recordSet.SetParameter("customerId", message.AtAddress);
						recordSet.SetParameter("amount", message.Amount);
						recordSet.SetParameter("purchaseBody", message.Description);
						recordSet.SetParameter("description", message.Reference);
						recordSet.SetParameter("referenceID", message.Reference);

						result = paymentProcessor.Execute<Connectors.town.connectors.driver.transactions.WithdrawalTransaction>(now, recordSet);
						authorization = result.AuthorizationId;
					}
					//authorization = (allAvailableDrivers[message.Agent].Driver as DGSdriver).DeductAmount(message.AtAddress, message.Amount, message.Description, message.Reference);
				}

				string command = string.Empty;
				if (message.HasSource())
				{
					command = $@"
							{{
								source{message.SourceNumber} = atAddress.GetOrCreateSource(itIsThePresent, now, {message.SourceNumber}, {message.Currency}, '{message.SourceName}');
	                            atAddress.CreateAccountIfNotExists('{message.Currency}', '{message.AccountNumber}');
								store = company.Sales.StoreById({message.StoreId.ToString()});
								source{message.SourceNumber}.Accredit(itIsThePresent, Now, Currency('{message.Currency}', {message.Amount}), '{message.Who}', '{authorization}', store, '{description}', '{message.Reference}', '{message.AccountNumber}', {processorId});
								source{message.SourceNumber}.Withdraw(itIsThePresent, Now, Currency('{message.Currency}', {message.Amount}), '{message.Who}', '{authorization}', store, '{description}', '{message.Reference}','{message.AccountNumber}', {processorId});
							}}
						";
				}
				else if (!string.IsNullOrEmpty(message.AccountNumber))
				{
					command = $@"
							{{
								balance = atAddress.CreateAccountIfNotExists('{message.Currency}', '{message.AccountNumber}').Balance;
								store = company.Sales.StoreById({message.StoreId.ToString()});
								balance.Accredit(itIsThePresent, Now, Currency('{message.Currency}', {amount.Value}), '{message.Who}', '{authorization}', store, '{description}', '{message.Reference}', {processorId});
								balance.Withdraw(itIsThePresent, Now, Currency('{message.Currency}', {amount.Value}), '{message.Who}', '{authorization}', store, '{description}', '{message.Reference}', {processorId});
							}}";
				}
				else
				{
					command = $@"
							{{
								balance = atAddress.CreateBalanceIfNotExists('{message.Currency}');
								store = company.Sales.StoreById({message.StoreId.ToString()});
								balance.Accredit(itIsThePresent, Now, Currency('{message.Currency}', {amount.Value}), '{message.Who}', '{authorization}', store, '{description}', '{message.Reference}', {processorId});
								balance.Withdraw(itIsThePresent, Now, Currency('{message.Currency}', {amount.Value}), '{message.Who}', '{authorization}', store, '{description}', '{message.Reference}', {processorId});
							}}";
				}

				var result2 = actor.PerformCmd(message.AtAddress, command);
				if (!(result2 is OkObjectResult))
				{
					throw new GameEngineException($"Withdraw was not saved coz command fails on cashier.");
				}
			}
		}

        public static void DepositAndLock(DateTime now, RestAPISpawnerActor actor, FragmentsCreationBody fragmentMessage)
		{
			if (fragmentMessage.Agent == Agents.INSIDER)//insider
			{

			}
			else
			{
				string Iso4217Code = fragmentMessage.CurrencyCode;
				PaymentProcessor paymentProcessor = WholePaymentProcessor.Instance().SearchDepositProcessorBy(Iso4217Code, typeof(DepositThenLock));
				using (RecordSet recordSet = paymentProcessor.GetRecordSet())
				{
					recordSet.SetParameter("actor", actor);
					recordSet.SetParameter("fragmentMessage", fragmentMessage);
                    recordSet.SetParameter("processorKey", fragmentMessage.ProcessorKey);
                    paymentProcessor.Execute<AuthorizationTransaction>(now, recordSet);
				}
			}
		}

        public static void DepositAndLockForMultipleAuthorizations(DateTime now, RestAPISpawnerActor actor, FragmentsCreationBody fragmentMessage)
        {
            if (fragmentMessage.Agent != Agents.INSIDER)
            {
                string Iso4217Code = fragmentMessage.CurrencyCode;
                PaymentProcessor paymentProcessor = WholePaymentProcessor.Instance().SearchDepositProcessorBy(Iso4217Code, typeof(DepositThenLockForMultipleAuthorizations));
                using (RecordSet recordSet = paymentProcessor.GetRecordSet())
                {
                    recordSet.SetParameter("actor", actor);
                    recordSet.SetParameter("fragmentMessage", fragmentMessage);
                    recordSet.SetParameter("processorKey", fragmentMessage.ProcessorKey);
                    paymentProcessor.Execute<AuthorizationTransaction>(now, recordSet);
                }
            }
        }

        internal static void UpdateWagers(List<PayFragmentsMessage> wagers)
		{
			var artemisWagers = wagers.Where(wager => wager.AgentId == (int)Agents.ARTEMIS);
			if (artemisWagers.Any())
			{
				PaymentProcessor paymentProcessor = WholePaymentProcessor.Instance().SearchGradeProcessorBy(typeof(Connectors.town.connectors.drivers.artemis.Grade));
				using (RecordSet recordSet = paymentProcessor.GetRecordSet())
				{
					recordSet.SetParameter("wagers", artemisWagers.ToList());

					paymentProcessor.Execute<object>(DateTime.Now, recordSet);
				}
			}
			else
            {
				throw new GameEngineException("No wagers to update in agent");
			}
		}

		internal static void UpdateUpdatedWagers(List<PayFragmentsMessage> wagers)
		{
			var artemisWagers = wagers.Where(wager => wager.AgentId == (int)Agents.ARTEMIS);
			if (artemisWagers.Any())
			{
				PaymentProcessor paymentProcessor = WholePaymentProcessor.Instance().SearchGradeProcessorBy(typeof(Connectors.town.connectors.drivers.artemis.ReGrade));
				using (RecordSet recordSet = paymentProcessor.GetRecordSet())
				{
					recordSet.SetParameter("wagers", artemisWagers.ToList());
					paymentProcessor.Execute<object>(DateTime.Now, recordSet);
				}
			}
			else
			{
				throw new GameEngineException("No wagers to update in agent");
			}
		}

		public static async Task<BalancesResponse> AllBalancesAsync(HttpContext httpContext, RestAPISpawnerActor actor, string atAddress, Agents agent)
		{
			BalancesResponse response = new BalancesResponse();

			string[] codes = new[] { Currencies.CODES.FP.ToString() };

			//remove, only for test users
			if (agent == Agents.INSIDER)
			{
                codes = new[] { Currencies.CODES.FP.ToString(), Currencies.CODES.USD.ToString() };
            }

            PaymentProcessor paymentProcessor = WholePaymentProcessor.Instance().SearchBalanceProcesorBy(typeof(town.connectors.drivers.fiero.Balance));
            foreach (var Iso4217Code in codes)
            {
				decimal balance;
				using (RecordSet recordSet = paymentProcessor.GetRecordSet())
				{
					recordSet.SetParameter("atAddress", atAddress);
					recordSet.SetParameter("HttpContext", httpContext);
					recordSet.SetParameter("currencyCode", Iso4217Code);
					recordSet.SetParameter("actor", actor);
					balance = await paymentProcessor.ExecuteAsync<decimal>(DateTime.Now, recordSet);
				}

				var balanceResponse = new BalanceResponse()
				{
					Balance = balance,
					CurrencyCodeAsText = Iso4217Code
				};
				response.Add(balanceResponse);
			}
			
			if (agent != Agents.INSIDER)
			{
				string Iso4217Code = Currencies.CODES.USD.ToString();
				paymentProcessor = WholePaymentProcessor.Instance().SearchBalanceProcesorBy(Iso4217Code);
				decimal balance;
				using (RecordSet recordSet = paymentProcessor.GetRecordSet())
				{
					recordSet.SetParameter("customerId", atAddress);
					recordSet.SetParameter("HttpContext", httpContext);
					
					balance = await paymentProcessor.ExecuteAsync<decimal>(DateTime.Now, recordSet);
				}

				var balanceResponse = new BalanceResponse()
				{
					Balance = balance,
					CurrencyCodeAsText = Iso4217Code
				};
				response.Add(balanceResponse);
			}

			return response;
		}

        public static async Task<BalancesResponse> SpecificBalancesAsync(HttpContext httpContext, RestAPISpawnerActor actor, string atAddress, Agents agent, string[] codes)
        {
            BalancesResponse response = new BalancesResponse();
            PaymentProcessor paymentProcessor = WholePaymentProcessor.Instance().SearchBalanceProcesorBy(typeof(town.connectors.drivers.fiero.Balance));
            foreach (var Iso4217Code in codes)
            {
                decimal balance;
				if (Iso4217Code == Currencies.CODES.USD.ToString())
				{
                    var paymentProcessorUSD = WholePaymentProcessor.Instance().SearchBalanceProcesorBy(Iso4217Code);
                    using (RecordSet recordSet = paymentProcessorUSD.GetRecordSet())
                    {
                        recordSet.SetParameter("customerId", atAddress);
                        recordSet.SetParameter("HttpContext", httpContext);

                        balance = await paymentProcessorUSD.ExecuteAsync<decimal>(DateTime.Now, recordSet);
                    }
                }
                else
                {
                    using (RecordSet recordSet = paymentProcessor.GetRecordSet())
                    {
                        recordSet.SetParameter("atAddress", atAddress);
                        recordSet.SetParameter("HttpContext", httpContext);
                        recordSet.SetParameter("currencyCode", Iso4217Code);
                        recordSet.SetParameter("actor", actor);
                        balance = await paymentProcessor.ExecuteAsync<decimal>(DateTime.Now, recordSet);
                    }
                }
                
				var balanceResponse = new BalanceResponse()
				{
					Balance = balance,
					CurrencyCodeAsText = Iso4217Code
				};
				response.Add(balanceResponse);
            }

            return response;
        }

        public static async Task<bool> ValidateAsync(int agentId, string accountNumber, string token)
		{
			string Iso4217Code = Currencies.CODES.USD.ToString();
			PaymentProcessor paymentProcessor = WholePaymentProcessor.Instance().SearchValidateProcesorBy(Iso4217Code);
			bool result;
			using (RecordSet recordSet = paymentProcessor.GetRecordSet())
			{
				recordSet.SetParameter("customerId", accountNumber);
				recordSet.SetParameter("token", token);

				result = await paymentProcessor.ExecuteAsync<bool>(DateTime.Now, recordSet);
			}
			return result;

			//BalancesResponse response = new BalancesResponse();
			//decimal balance = await (allAvailableDrivers[agent].Driver as ASIdriver).AvailableBalanceAsync(atAddress);
			//var balanceResponse = new BalanceResponse()
			//{
			//Balance = balance,
			//CurrencyCodeAsText = Iso4217Code
			//};
			//response.Add(balanceResponse);
			//return response;

			//Agents agent = (Agents)agentId;
			//if (agent == Agents.INSIDER)//insider
			//{
			//return true;
			//}
			//else if (agent == Agents.TEST_BOOK)
			//{
			//return await (allAvailableDrivers[agent].Driver as ASIdriver).PostValidateCustomerAsync(accountNumber, token);
			//}
			//else
			//{
			//return await (allAvailableDrivers[agent].Driver as DGSdriver).ValidatePlayerAsync(token);
			//}
		}


		internal static DespositResponse Deposit(bool itIsThePresent, Coin currencyCode, Domain domain, ProcessorTransaction transaction, DespositBody despositBody)
		{
			MultipleProcessors drivers = WholePaymentProcessor.Instance().PaymentProcessorsAndActionsByDomains.SearchFor(currencyCode, transaction);
			PaymentProcessor paymentProcessor = drivers.First();

			return Deposit(itIsThePresent, paymentProcessor, despositBody);
		}

		internal static DespositResponse Deposit(bool itIsThePresent, Coin coin, string domain, ProcessorTransaction transaction, DespositBody despositBody)
		{
			if (coin == null) throw new ArgumentNullException(nameof(coin));
			if (transaction == null) throw new ArgumentNullException(nameof(transaction));
			if (despositBody == null) throw new ArgumentNullException(nameof(despositBody));
			if (string.IsNullOrEmpty(domain)) throw new ArgumentNullException(nameof(domain));

			MultipleProcessors drivers = WholePaymentProcessor.Instance().PaymentProcessorsAndActionsByDomains.SearchFor(coin, transaction);
			PaymentProcessor paymentProcessor = drivers.SearchBy(transaction, coin, despositBody.PaymentMethod, despositBody.EntityId);

			return Deposit(itIsThePresent, paymentProcessor, despositBody);
		}

		internal static DespositResponse Deposit(bool itIsThePresent, string processorId, DespositBody despositBody)
		{
			PaymentProcessor paymentProcessor = WholePaymentProcessor.Instance().PaymentProcessorsAndActionsByDomains.SearchById(processorId);

			return Deposit(itIsThePresent, paymentProcessor, despositBody);

		}
		private static DespositResponse Deposit(bool itIsThePresent, PaymentProcessor paymentProcessor, DespositBody despositBody)
		{
			DespositResponse response;
			using (RecordSet recordSet = paymentProcessor.GetRecordSet())
			{
				recordSet.SetParameter("customerId", despositBody.AccountNumber);
				recordSet.SetParameter("amount", despositBody.Amount);
				recordSet.SetParameter("description", $"{nameof(TransactionType.Deposit)} {despositBody.Description}");
				recordSet.SetParameter("additionalInfo", string.Empty);
				recordSet.SetParameter("referenceID", despositBody.AccountNumber);
				
				recordSet.SetParameter("sendersName", despositBody.SendersName);
				recordSet.SetParameter("country", despositBody.Country);
				recordSet.SetParameter("state", despositBody.State);
				recordSet.SetParameter("city", despositBody.City);
				recordSet.SetParameter("controlNum", despositBody.ControlNum);
				recordSet.SetParameter("amount", despositBody.Amount);
				recordSet.SetParameter("readyForProcessing", "true");
				recordSet.SetParameter("Input$Provider", despositBody.ProviderId);
				var execResult = paymentProcessor.Execute<DepositTransaction>(DateTime.Now, recordSet);
				response = new DespositResponse(execResult.AuthorizationId, execResult.Status, paymentProcessor.ProcessorKey);
			}

			if (response.Status == TransactionStatus.APPROVED)
            {
				NotifyOperation(
					itIsThePresent,
					DateTime.Now,
					despositBody.AccountNumber,
					response.AuthorizationId,
					despositBody.Description,
					Currency.Factory(paymentProcessor.CurrencyIso4217Code, despositBody.Amount),
					despositBody.Identifier,
					TransactionType.Deposit,
					despositBody.Domain.Id,
					despositBody.Domain.Url,
					new TransactionFees(),
					new Execution(response.Status, ""),
					paymentProcessor.Group,
					paymentProcessor.Driver.Id,
					despositBody.StoreId,
					despositBody.EmployeeName
					);
			}
			return response;
		}

		internal static WithdrawReponse WithDraw(bool itIsThePresent, Coin coin, string domain, ProcessorTransaction transaction, WithdrawBody withdrawBody)
		{
			if (coin == null) throw new ArgumentNullException(nameof(coin));
			if (transaction == null) throw new ArgumentNullException(nameof(transaction));
			if (withdrawBody == null) throw new ArgumentNullException(nameof(withdrawBody));
			if (string.IsNullOrEmpty(domain)) throw new ArgumentNullException(nameof(domain));

			MultipleProcessors drivers = WholePaymentProcessor.Instance().PaymentProcessorsAndActionsByDomains.SearchFor(coin, transaction);
			PaymentProcessor paymentProcessor = drivers.SearchBy(transaction, coin, withdrawBody.PaymentMethod, withdrawBody.EntityId);

			return WithDraw(itIsThePresent, paymentProcessor, withdrawBody);
		}

		internal static WithdrawReponse WithDraw(bool itIsThePresent, string processorId, WithdrawBody withdrawBody)
		{
			PaymentProcessor paymentProcessor = WholePaymentProcessor.Instance().PaymentProcessorsAndActionsByDomains.SearchById(processorId);

			return WithDraw(itIsThePresent, paymentProcessor, withdrawBody);
		}

		static WithdrawReponse WithDraw(bool itIsThePresent, PaymentProcessor paymentProcessor, WithdrawBody withdrawBody)
        {
			WithdrawReponse response;

			using (RecordSet recordSet = paymentProcessor.GetRecordSet())
			{
				recordSet.SetParameter("customerId", withdrawBody.AccountNumber);
				recordSet.SetParameter("amount", withdrawBody.Amount);
				recordSet.SetParameter("description", $"{nameof(TransactionType.Withdrawal)} {withdrawBody.Description}");
				recordSet.SetParameter("additionalInfo", string.Empty);
				recordSet.SetParameter("referenceID", withdrawBody.AccountNumber);

				recordSet.SetParameter("identificationNumber", withdrawBody.Identifier);
				recordSet.SetParameter("receiversName", withdrawBody.ReceiversName);
				recordSet.SetParameter("country", withdrawBody.Country);
				recordSet.SetParameter("state", withdrawBody.State);
				recordSet.SetParameter("city", withdrawBody.City);
				recordSet.SetParameter("readyForProcessing", "true");
				var execResult = paymentProcessor.Execute<Connectors.town.connectors.driver.transactions.WithdrawalTransaction>(DateTime.Now, recordSet);
				response = new WithdrawReponse(execResult.AuthorizationId, execResult.Status, paymentProcessor.ProcessorKey);
			}

			return response;
		}
    }
}
