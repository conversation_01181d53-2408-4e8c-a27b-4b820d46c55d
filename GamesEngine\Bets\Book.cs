﻿using GamesEngine.Business;
using GamesEngine.Games;
using GamesEngine.Games.Lines;
using GamesEngine.Gameboards;
using GamesEngine.Gameboards.MarchMadness;
using GamesEngine.Tools;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using GamesEngine.Games.Tournaments;

namespace GamesEngine.Bets
{
	internal sealed class Book : Objeto
	{
		private readonly List<Bet> bets = new List<Bet>();
		private readonly List<Reward> pools = new List<Reward>();
		private readonly Dictionary<League, List<Betboard>> betBoards = new Dictionary<League, List<Betboard>>();
		private int betNumbersToSkip = 1;
		private RiskTags riskTags;
		private readonly Company company;

		internal Book(Company company) 
		{
			if (company == null) throw new ArgumentNullException(nameof(company));

			this.company = company;
		}

		[Obsolete("Do not use it is for legacy purposes")]
		internal Bet GetNewBet(Player player, Gameboard gameboard, Reward reward, DateTime creationDate)
		{
			if (player == null) throw new ArgumentNullException(nameof(player));
			if (gameboard == null) throw new ArgumentNullException(nameof(gameboard));
			if (reward == null) throw new ArgumentNullException(nameof(reward));
			if (!pools.Exists(x => x == reward)) throw new GameEngineException($"Reward '{reward.Name}' is unknown. Use Add to included it as a new Reward.");
			Bet bet = new BetAlive(player, gameboard, reward, creationDate);
			bets.Add(bet);
			player.Add(bet);
			return bet;
		}

		internal Bet NewBet(Player player, Gameboard gameboard, Reward reward, DateTime creationDate, int nextBetNumber)
		{
			if (player == null) throw new ArgumentNullException(nameof(player));
			if (gameboard == null) throw new ArgumentNullException(nameof(gameboard));
			if (reward == null) throw new ArgumentNullException(nameof(reward));
			if (!pools.Exists(x => x == reward)) throw new GameEngineException($"Reward '{reward.Name}' is unknown. Use Add to included it as a new Reward.");
			Bet bet = new BetAlive(player, gameboard, reward, creationDate, nextBetNumber);
			bets.Add(bet);
			player.Add(bet);
			return bet;
		}

		[Obsolete("Do not use it is for legacy purposes")]
		internal int NextBetNumber()
		{
			if (bets.Count == 0) return betNumbersToSkip;
			var bet = bets[bets.Count - 1];
			return bet.Number + betNumbersToSkip;
		}

		public const int NEW_CONSECUTIVE = 1000000;
		private int lastGeneratedNumber = NEW_CONSECUTIVE;

		internal int IdentitytBetNumber
		{
			get
			{
				if (bets.Count == 0)
				{
					return lastGeneratedNumber + 1;
				}
				else
				{
					var lastBet = bets[bets.Count - 1];
					var betNumber = lastBet.Number;
					lastGeneratedNumber = betNumber + 1;
					if (lastGeneratedNumber < NEW_CONSECUTIVE)
					{
						lastGeneratedNumber = NEW_CONSECUTIVE;
					}
				}

				return lastGeneratedNumber;
			}
		}

		internal void IncreaseBetNumbersToSkip()
		{
			betNumbersToSkip++;
		}

		internal void Add(Reward pool)
		{
			if (pool == null) throw new ArgumentNullException(nameof(pool));
			pools.Add(pool);
		}

		internal IEnumerable<Reward> Rewards()
		{
			return pools;
		}

		private Bet FindBet(Player player, Gameboard gameboard, Reward reward)
		{
			Bet betFound = player.FindBet(gameboard, reward);
			return betFound;
		}

		internal Betboard GetOrCreateBetboard(Tournament tournament, Players players)
		{
			if (tournament == null) throw new GameEngineException($"Tournament {tournament.Description} does not exist");
			if (players == null) throw new ArgumentNullException(nameof(players));

			var league = tournament.League;
			List<Betboard> tournamentBetBoards;
			Betboard result;
			if (!this.betBoards.TryGetValue(league, out tournamentBetBoards))
			{
				result = new Betboard(this, tournament, players);
				tournamentBetBoards = new List<Betboard>();
				tournamentBetBoards.Add(result);
				this.betBoards.Add(league, tournamentBetBoards);
			}
			else
			{
				result = tournamentBetBoards.FirstOrDefault(t => t.Tournament.Id == tournament.Id);
				if(result == null)
				{
					result = new Betboard(this, tournament, players);
					tournamentBetBoards.Add(result);
				}
			}
			return result;
		}

		internal IEnumerable<Betboard> BetBoards
		{
			get
			{
				return this.betBoards.Values.SelectMany(listOfBetBoards => listOfBetBoards);
			}
		}

		internal void Lock(BetAlive oldBet, BetLocked bet)
		{
			if (bet == null) throw new ArgumentNullException(nameof(bet));
			if (oldBet.Number != bet.Number) throw new GameEngineException("Bet Lock can not be done using different bet numbers");

			ReplaceAtTheEnd(oldBet, bet);
		}

		internal void Lock(BetPunched oldBet, BetLocked bet)
		{
			if (bet == null) throw new ArgumentNullException(nameof(bet));
			if (oldBet.Number != bet.Number) throw new GameEngineException("Bet Lock can not be done using different bet numbers");

			Replace(oldBet, bet);
		}

		internal void Lock(BetCanceled oldBet, BetLocked bet)
		{
			if (bet == null) throw new ArgumentNullException(nameof(bet));
			if (oldBet.Number != bet.Number) throw new GameEngineException("Bet Lock can not be done using different bet numbers");

			Replace(oldBet, bet);
		}

		private void PunchOf(BetLocked betLocked, BetPunched betPunched)
		{
			Replace(betLocked, betPunched);
		}

		private void PayOf(BetLocked betLocked, BetPayed betPayed)
		{
			Replace(betLocked, betPayed);
		}

		private void ReturnPrizeOf(BetPayed betPayed, BetLocked betLocked)
		{
			Replace(betPayed, betLocked);
		}

		private void Cancel(BetLocked betLocked, BetCanceled betCanceled)
		{
			Replace(betLocked, betCanceled);
		}

		private void Replace(Bet oldBet, Bet newBet)
		{
			int index = bets.BinarySearch(oldBet);
			if (index == -1)
			{
				throw new GameEngineException($"There is no a bet with the number {newBet.Number}");
			}
			bets[index] = newBet;
			newBet.Player.Replace(oldBet, newBet);
		}

		private void ReplaceAtTheEnd(Bet oldBet, Bet newBet)
		{
			int index = bets.LastIndexOf(oldBet);
			if (index == -1)
			{
				throw new GameEngineException($"There is no a bet with the number {newBet.Number}");
			}
			bets[index] = newBet;
			newBet.Player.ReplaceAtTheEnd(oldBet, newBet);
		}

		private int[] CountPerRanks(IEnumerable<Bet> betInThePool, int maxRanks)
		{
			if (maxRanks <= 0) throw new GameEngineException("Max ranks can not be zero");
			List<int> result = new List<int>();
			var count = 0;
			var remainingList = betInThePool;

			while (remainingList.Count() > 0)
			{
				var winnerBetsInCategory = WinnerBetsInPool(remainingList);
				remainingList = remainingList.Where(bet => !winnerBetsInCategory.Contains(bet));
				count = count + winnerBetsInCategory.Count;
				result.Add(winnerBetsInCategory.Count);
				if (count >= maxRanks) break;
			}

			return result.ToArray();
		}

		internal void CalculatePrize(MarchMadnessTournament tournament)
		{
			if (!tournament.IsEnded()) throw new GameEngineException("The tournament has not ended");

			var betsMatchingYear = bets.Where(b => b.Gameboard.GetType() == typeof(MarchMadnessBracket) && ((MarchMadnessBracket)b.Gameboard).Year == tournament.Year).ToList();
			var poolsMatchingYear = betsMatchingYear.Where(b => pools.Contains(b.Reward)).Select(b => b.Reward).Distinct().Cast<Pool>().ToList();
			var betsToPunch = new List<BetLocked>();
			const decimal ONE_HUNDRED_PERCENTAGE = 1m;

			foreach (var pool in poolsMatchingYear)
			{
				var betsInPool = betsMatchingYear.Where(b => b.Reward == pool).ToList();
				var awards = tournament.AwardsForPool(pool);
				var countPerRanks = CountPerRanks(betsInPool, awards.Count);
				awards = awards.RedistributeAwards(countPerRanks);
				List<BetLocked> allWinners = new List<BetLocked>();

				var pendingDistribution = ONE_HUNDRED_PERCENTAGE;
				var availableAwards = awards.Count;
				var remainingBets = betsInPool;
				var placedAwards = 0;
				while (availableAwards > 0)
				{
					var winnerBetsInCategory = WinnerBetsInPool(remainingBets);
					decimal percentage = awards.PrizesPercentageFor(placedAwards);

					SetAmountToPayToWinners(winnerBetsInCategory, pool, percentage);
					allWinners.AddRange(winnerBetsInCategory);

					availableAwards = availableAwards - 1;
					if (availableAwards != 0)
					{
						pendingDistribution = pendingDistribution - percentage;
						placedAwards = placedAwards + 1;
						remainingBets = remainingBets.Where(bet => !winnerBetsInCategory.Contains(bet)).ToList();
					}
				}

				var betsToPunchInPool = GetBetsToPunch(betsInPool, allWinners);
				betsToPunch.AddRange(betsToPunchInPool);
			}

			PunchBets(betsToPunch);
			tournament.ReadyToPay();
		}

		private List<BetLocked> WinnerBetsInPool(IEnumerable<Bet> betsInPool)
		{
			var maxGrade = betsInPool.Max(bet => bet.Gameboard.Grade());
			BigInteger minGradeToUntie = BigInteger.Zero;
			bool isTheFirst = true;
			List<BetLocked> winnerBets = new List<BetLocked>();
			foreach (var bet in betsInPool.Where(bet => bet.Gameboard.Grade() == maxGrade))
			{
				var gradeToUntie = ((MarchMadnessBracket)bet.Gameboard).GradeToUntie();
				if (isTheFirst || gradeToUntie < minGradeToUntie)
				{
					minGradeToUntie = gradeToUntie;
					winnerBets.Clear();
					winnerBets.Add((BetLocked)bet);
					isTheFirst = false;
				}
				else if (gradeToUntie == minGradeToUntie)
				{
					winnerBets.Add((BetLocked)bet);
				}
			}

			return winnerBets;
		}

		private void SetAmountToPayToWinners(List<BetLocked> winnerBets, Pool pool)
		{
			decimal prizeForEachWinner = pool.GrandPrice / winnerBets.Count;
			foreach (var winnerBet in winnerBets)
			{
				winnerBet.AmountToPay = prizeForEachWinner;
				winnerBet.AmountApproved = prizeForEachWinner;
			}
		}

		private void SetAmountToPayToWinners(List<BetLocked> winnerBets, Pool pool, decimal percentage)
		{
			if (percentage < 0 || percentage > 1) throw new GameEngineException($"Percentage of pool prize can not be less than 100%. {percentage} is invalid");

			decimal prizeForEachWinnerOriginal = (pool.GrandPrice * percentage) / (decimal)winnerBets.Count;
			decimal prizeForEachWinner = Math.Truncate(prizeForEachWinnerOriginal * 100m) / 100m;
			decimal remaining = pool.GrandPrice * percentage;

			for (int i = winnerBets.Count - 1; i >= 0; i--)
			{
				var winnerBet = winnerBets[i];
				decimal toBePayed = i == 0 ? remaining : prizeForEachWinner;
				Commons.ValidateAmount(toBePayed);
				winnerBet.AmountToPay = toBePayed;
				winnerBet.AmountApproved = toBePayed;
				remaining = remaining - toBePayed;
			}
		}

		private List<BetLocked> GetBetsToPunch(List<Bet> betsInPool, List<BetLocked> winnerBets)
		{
			var loserBets = betsInPool.Where(b => !winnerBets.Contains(b)).Cast<BetLocked>().ToList();
			var betsToPunch = new List<BetLocked>();
			for (int index = loserBets.Count - 1; index >= 0; index--)
			{
				var loserBet = loserBets[index];
				betsToPunch.Add(loserBet);
			}
			return betsToPunch;
		}

		private void PunchBets(List<BetLocked> betsLocked)
		{
			for (int index = betsLocked.Count - 1; index >= 0; index--)
			{
				var betLocked = betsLocked[index];
				var betPunched = (BetPunched)betLocked.Punch();
				PunchOf(betLocked, betPunched);
			}
		}

		internal SummaryOfBetsGroupedByPoolFee WinnerBetsInGeneralPools(List<Pool> generalPools)
		{
			var betsLocked = bets.Where(b => (b.State == Bet.BetState.LOCKED) && generalPools.Contains(b.Reward)).Select(b => new BetLockedOrPayed(b)).ToList();
			var betsPayed = bets.Where(b => (b.State == Bet.BetState.PAYED) && generalPools.Contains(b.Reward)).Select(b => new BetLockedOrPayed(b)).ToList();
			var payableBets = new List<BetLockedOrPayed>();
			payableBets.AddRange(betsLocked);
			payableBets.AddRange(betsPayed);
			var fees = payableBets.Where(b => b.Bet.Reward.GetType() == typeof(Pool)).Select(b => b.Bet.Reward).Cast<Pool>().Select(p => p.Fee).Distinct().OrderBy(fee => fee);
			var listBetsGroupedByPoolFee = new List<BetsGroupedByPoolFee>();
			foreach (var fee in fees)
			{
				var betsGrouped = payableBets.Where(b => ((Pool)b.Bet.Reward).Fee == fee).ToList();
				var betsGroupedByPoolFee = new BetsGroupedByPoolFee(betsGrouped, fee);
				listBetsGroupedByPoolFee.Add(betsGroupedByPoolFee);
			}

			return new SummaryOfBetsGroupedByPoolFee(listBetsGroupedByPoolFee, betsPayed.Count, betsLocked.Count);
		}

		internal SummaryOfBetsLockedOrPayed WinnerBetsInNonGeneralPools(List<Pool> generalPools)
		{
			var betsLocked = bets.Where(b => (b.State == Bet.BetState.LOCKED) && !generalPools.Contains(b.Reward)).Select(b => new BetLockedOrPayed(b)).ToList();
			var betsPayed = bets.Where(b => (b.State == Bet.BetState.PAYED) && !generalPools.Contains(b.Reward)).Select(b => new BetLockedOrPayed(b)).ToList();
			var payableBets = new List<BetLockedOrPayed>();
			payableBets.AddRange(betsLocked);
			payableBets.AddRange(betsPayed);
			return new SummaryOfBetsLockedOrPayed(payableBets, betsPayed.Count, betsLocked.Count);
		}

		internal int AuthorizationId(Bets.Player player, Gameboard gameboard, Pool pool)
		{
			if (gameboard is ISinglePool)
			{
				var g = (ISinglePool)gameboard;
				return g.AuthorizationId;
			}

			var bet = FindBet(player, gameboard, pool);
			if (bet == null) throw new ArgumentNullException(nameof(bet));
			return bet.AuthorizationId;
		}

		internal void SetDocumentNumber(Bets.Player player, Gameboard gameboard, Pool pool, int documentNumber)
		{
			var bet = FindBet(player, gameboard, pool);
			if (bet == null) throw new ArgumentNullException(nameof(bet));
			bet.AuthorizationId = documentNumber;
		}

		internal bool HasDocumentNumberAssigned(int documentNumber)
		{
			foreach (var bet in bets)
			{
				if (bet.AuthorizationId == documentNumber) return true;
			}
			return false;
		}

		internal BetPayed NewBetPayed(Bets.Player player, Gameboard gameboard, Pool pool, decimal prize)
		{
			var bet = FindBet(player, gameboard, pool);
			if (bet == null) throw new ArgumentNullException(nameof(bet));
			if (bet.State != Bet.BetState.LOCKED) throw new GameEngineException($"Bet in state {bet.State} cannot be {Bet.BetState.PAYED}");
			Commons.ValidateAmount(prize);

			var betLocked = ((BetLocked)bet);
			var betPayed = (BetPayed)betLocked.Pay(prize);
			PayOf(betLocked, betPayed);
			return betPayed;
		}

		internal BetLocked NewBetLockedFromBetPayed(Bets.Player player, Gameboard gameboard, Pool pool)
		{
			var bet = FindBet(player, gameboard, pool);
			if (bet == null) throw new ArgumentNullException(nameof(bet));
			if (bet.State != Bet.BetState.PAYED) throw new GameEngineException($"Bet in state {bet.State} cannot be {Bet.BetState.LOCKED}");

			var betPayed = ((BetPayed)bet);
			var betLocked = (BetLocked)betPayed.Lock();
			ReturnPrizeOf(betPayed, betLocked);
			return betLocked;
		}

		internal void CancelBet(Bets.Player player, Gameboard gameboard, Pool pool)
		{
			var bet = FindBet(player, gameboard, pool);
			if (bet == null) throw new ArgumentNullException(nameof(bet));
			if (bet.State != Bet.BetState.LOCKED) throw new GameEngineException($"Bet in state {bet.State} cannot be {Bet.BetState.CANCELED}");

			var betLocked = ((BetLocked)bet);
			var betCanceled = (BetCanceled)betLocked.Cancel();
			Cancel(betLocked, betCanceled);
		}

		internal void PunchBet(Bets.Player player, Gameboard gameboard, Pool pool)
		{
			var bet = FindBet(player, gameboard, pool);
			if (bet == null) throw new ArgumentNullException(nameof(bet));
			if (bet.State != Bet.BetState.LOCKED) throw new GameEngineException($"Bet in state {bet.State} cannot be {Bet.BetState.PUNCHED}");

			var betLocked = ((BetLocked)bet);
			var betPunched = (BetPunched)betLocked.Punch();
			PunchOf(betLocked, betPunched);
		}

		internal void LockBetPunched(Bets.Player player, Gameboard gameboard, Pool pool)
		{
			var bet = FindBet(player, gameboard, pool);
			if (bet == null) throw new ArgumentNullException(nameof(bet));
			if (bet.State != Bet.BetState.PUNCHED) throw new GameEngineException($"Bet in state {bet.State} cannot be {Bet.BetState.LOCKED}");

			var betPunched = ((BetPunched)bet);
			var betLocked = (BetLocked)betPunched.Lock();
			Lock(betPunched, betLocked);
		}

		internal void LockBetCanceled(Bets.Player player, Gameboard gameboard, Pool pool)
		{
			var bet = FindBet(player, gameboard, pool);
			if (bet == null) throw new ArgumentNullException(nameof(bet));
			if (bet.State != Bet.BetState.CANCELED) throw new GameEngineException($"Bet in state {bet.State} cannot be {Bet.BetState.LOCKED}");

			var betCanceled = ((BetCanceled)bet);
			var betLocked = (BetLocked)betCanceled.Lock();
			Lock(betCanceled, betLocked);
		}

		internal void RemoveBet(Bets.Player player, Gameboard gameboard, Reward pool)
		{
			var bet = FindBet(player, gameboard, pool);
			if (bet == null) throw new ArgumentNullException(nameof(bet));
			// if ((bet.State != Bet.BetState.PUNCHED) && (bet.State != Bet.BetState.PAYED) && (bet.State != Bet.BetState.CANCELED)) throw new GameEngineException($"Bet in state {bet.State} cannot be removed");

			bets.Remove(bet);
		}

		internal void RemoveDisposedGameboards()
		{
			var playersToUpdate = bets
				.Where(
					x => x.Gameboard.IsReadyToBeDisposed()
				)
				.Select(
					x => x.Player
				).Distinct();
			foreach (var player in playersToUpdate)
			{
				player.RemoveDisposedGameboards();
			}
			foreach (Bet b in bets.Where(x => x.Gameboard.IsReadyToBeDisposed()))
			{
				b.Reward.Uncontribute(b.Player, b.Contribution);
			}
			bets.RemoveAll(x => x.Gameboard.IsReadyToBeDisposed());
		}

		internal void UpdateAmountApproved(Bets.Player player, Gameboard gameboard, Pool pool, decimal amountApproved)
		{
			var bet = FindBet(player, gameboard, pool);
			if (bet == null) throw new ArgumentNullException(nameof(bet));
			if (bet.State != Bet.BetState.LOCKED) throw new GameEngineException($"Bet in state {bet.State} cannot be payed");
			var betLocked = ((BetLocked)bet);
			betLocked.AmountApproved = amountApproved;
		}

		internal List<Gameboard> GameboardsOnPoolDistinctOf(IEnumerable<Gameboard> gameboards, Pool pool)
		{
			var result = bets.Where(bet => bet.Reward == pool && !gameboards.Contains(bet.Gameboard)).Select(bet => bet.Gameboard).OrderByDescending(g => g.Grade()).ThenByDescending(g => g.Id);
			return result.ToList();
		}

		internal IEnumerable<Gameboard> GameboardsOf(Pool pool)
		{
			var result = bets.Where(bet => bet.Reward == pool).
				Select(bet => bet.Gameboard).ToList();
			return result;
		}

		internal IEnumerable<Gameboard> GameboardsOf(IEnumerable<Pool> pools)
		{
			var result = bets.Where(bet => pools.Contains(bet.Reward)).
				Select(bet => bet.Gameboard).ToList();
			return result;
		}

		internal IEnumerable<Gameboard> GameboardsWithSearchCriteriaSimilarThan(string searchCriteria)
		{
			var normalizedCriteria = searchCriteria.ToUpper();
			var gameboards = new HashSet<Gameboard>();
			foreach (var bet in bets)
			{
				var isMatchingGameboardName = bet.Gameboard.Name.ToUpper().IndexOf(normalizedCriteria) >= 0;
				if (isMatchingGameboardName)
				{
					gameboards.Add(bet.Gameboard);
					continue;
				}
				var isMatchingAccountNumber = bet.Player.AccountNumber.ToUpper().IndexOf(normalizedCriteria) >= 0;
				if (isMatchingAccountNumber)
				{
					gameboards.Add(bet.Gameboard);
					continue;
				}
				var isMatchingNickname = bet.Player.Nickname.ToUpper().IndexOf(normalizedCriteria) >= 0;
				if (isMatchingNickname)
				{
					gameboards.Add(bet.Gameboard);
					continue;
				}
				var isMatchingGameboardId = bet.Gameboard.Id.ToString() == searchCriteria;
				if (isMatchingGameboardId)
				{
					gameboards.Add(bet.Gameboard);
					continue;
				}
				var isMatchingAuthorizationId = bet.AuthorizationId.ToString() == searchCriteria;
				if (isMatchingAuthorizationId)
				{
					gameboards.Add(bet.Gameboard);
					continue;
				}
			}
			return gameboards;
		}

		internal Bet FindBetById(int betNumber)
		{
			if (betNumber <= 0) throw new GameEngineException($"{nameof(betNumber)} {betNumber} is not valid.");
			var index = BinarySearch(betNumber);
			if (index == -1) throw new GameEngineException($"Bet {betNumber} has not been found");
			Bet betFound = bets[index];
			return betFound;
		}

		internal bool ExistBetId(int betNumber)
		{
			if (betNumber <= 0) throw new GameEngineException($"{nameof(betNumber)} {betNumber} is not valid.");

			var index = BinarySearch(betNumber);
			return index != -1;
		}

		private int BinarySearch(int betNumber)
		{
			int L = 0;
			int R = bets.Count - 1;
			while (L <= R)
			{
				int M = (L + R) / 2;
				if (bets[M].Number < betNumber) L = M + 1;
				else if (bets[M].Number > betNumber) R = M - 1;
				else return M;
			}
			return -1;
		}

		internal Bet FindBetForTournamentBy(int authorizationId, int year)
		{
			if (authorizationId <= 0) throw new GameEngineException($"{nameof(authorizationId)} {authorizationId} is not valid.");

			Bet betFound = bets.Find(bet => bet.AuthorizationId == authorizationId &&
				bet.Gameboard.GetType().Equals(typeof(MarchMadnessBracket)) &&
				((MarchMadnessBracket)bet.Gameboard).Year == year);
			return betFound;
		}

		internal IEnumerable<Bet> FindBetsInRange(int theLowestBetId, int theHighestBetId)
		{
			if (theLowestBetId <= 0) throw new GameEngineException($"{nameof(theLowestBetId)} {theLowestBetId} is not valid.");
			if (theHighestBetId <= 0) throw new GameEngineException($"{nameof(theHighestBetId)} {theHighestBetId} is not valid.");

			var lowerIndex = BinarySearch(theLowestBetId);
			if (lowerIndex == -1) throw new GameEngineException($"{nameof(theLowestBetId)} {theLowestBetId} is not valid because it does not exist.");
			var upperIndex = BinarySearch(theHighestBetId);
			if (upperIndex == -1) throw new GameEngineException($"{nameof(theHighestBetId)} {theHighestBetId} is not valid because it does not exist.");
			if (upperIndex < lowerIndex) throw new GameEngineException($"Highest {theHighestBetId} and lowest {theLowestBetId} should be ordered, but they are not");

			var count = (theHighestBetId - theLowestBetId) + 1;
			Bet[] result = new Bet[count];
			bets.CopyTo(lowerIndex, result, 0, count);

			return result;
		}

		internal IEnumerable<Bet> BetsOfGameboardDistinctOfBet(Gameboard gameboard, Bet bet)
		{
			var betsFound = bets.Where(b => b.Gameboard == gameboard && b != bet);
			return betsFound;
		}

		internal IEnumerable<Bet> BetsFrom(IEnumerable<Reward> rewards)
		{
			var bets = this.bets.Where(bet => rewards.Contains(bet.Reward));
			return bets;
		}

		internal IEnumerable<Bet> BetsOf(Gameboard gameboard)
		{
			var result = bets.Where(bet => bet.Gameboard == gameboard);
			return result;
		}

		internal bool BetHasDefaultAuthorizationId(Bets.Player player, Gameboard gameboard, Reward pool)
		{
			var bet = FindBet(player, gameboard, pool);
			if (bet == null) throw new ArgumentNullException(nameof(bet));

			return bet.AuthorizationId == 0;
		}

		internal IEnumerable<Bet> ListAllBets()
		{
			return this.bets;
		}

		internal Pool CreateNewPool(string name, decimal fee)
		{
			if (String.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
			if (fee < 0) throw new GameEngineException("The fee of the pool can not be negative.");
			var id = company.Book.NextPoolId();
			if (this.pools.Exists(x => x.Id == id)) throw new GameEngineException($"Pool id {id} already exist in pools collection");

			Pool pool = new Pool(id, name, fee);
			this.Add(pool);
			lastPoolId = id;

			return pool;
		}

		internal Pool CreateNewPool(string name)
		{
			if (String.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
			var id = company.Book.NextPoolId();
			if (this.pools.Exists(x => x.Id == id)) throw new GameEngineException($"Pool id {id} already exist in pools collection");

			Pool pool = new Pool(id, name);
			this.Add(pool);
			lastPoolId = id;

			return pool;
		}

		private int lastPoolId;
		internal int NextPoolId()
		{
			return lastPoolId + 1;
		}

		internal RiskTags RiskTags
		{
			get
			{
				if (this.riskTags == null)
					this.riskTags = new RiskTags(this);

				return this.riskTags;
			}
		}

		internal Company Company
		{
			get
			{
				return this.company;
			}
		}
	}

	internal class SummaryOfBetsGroupedByPoolFee : Objeto
	{
		private readonly int countBetsPayed;
		private readonly int countBetsLocked;
		private readonly List<BetsGroupedByPoolFee> betsGroupedByPoolFee;

		internal int CountBetsPayed
		{
			get { return countBetsPayed; }
		}

		internal int CountBetsLocked
		{
			get { return countBetsLocked; }
		}

		internal List<BetsGroupedByPoolFee> BetsGroupedByPoolFee
		{
			get { return betsGroupedByPoolFee; }
		}

		public SummaryOfBetsGroupedByPoolFee(List<BetsGroupedByPoolFee> betsGroupedByPoolFee, int countBetsPayed, int countBetsLocked)
		{
			this.betsGroupedByPoolFee = betsGroupedByPoolFee;
			this.countBetsPayed = countBetsPayed;
			this.countBetsLocked = countBetsLocked;
		}
	}

	internal class BetsGroupedByPoolFee : Objeto
	{
		private readonly decimal fee;
		private readonly List<BetLockedOrPayed> betsLockedOrPayed;

		internal decimal Fee
		{
			get { return fee; }
		}

		internal List<BetLockedOrPayed> BetsLockedOrPayed
		{
			get { return betsLockedOrPayed; }
		}

		internal BetsGroupedByPoolFee(List<BetLockedOrPayed> bets, decimal fee)
		{
			this.betsLockedOrPayed = bets;
			this.fee = fee;
		}
	}

	internal class SummaryOfBetsLockedOrPayed : Objeto
	{
		private readonly int countBetsPayed;
		private readonly int countBetsLocked;
		private readonly List<BetLockedOrPayed> betsLockedOrPayed;

		internal int CountBetsPayed
		{
			get { return countBetsPayed; }
		}

		internal int CountBetsLocked
		{
			get { return countBetsLocked; }
		}

		internal List<BetLockedOrPayed> BetsLockedOrPayed
		{
			get { return betsLockedOrPayed; }
		}

		internal SummaryOfBetsLockedOrPayed(List<BetLockedOrPayed> bets, int countBetsPayed, int countBetsLocked)
		{
			this.betsLockedOrPayed = bets;
			this.countBetsPayed = countBetsPayed;
			this.countBetsLocked = countBetsLocked;
		}
	}

	internal class BetLockedOrPayed : Objeto
	{
		private readonly Bet bet;

		internal Bet Bet
		{
			get { return bet; }
		}

		internal bool IsLocked
		{
			get { return bet.GetType() == typeof(BetLocked); }
		}

		public BetLockedOrPayed(Bet bet)
		{
			this.bet = bet;
		}
	}
}
