using Bis;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Reflection;
using static Bis.RiskIndex;

namespace BisUT
{
	[TestClass]
	public class Linq_Test
	{
		private Dictionary<object, RiskExpression> isOPsbyId = new Dictionary<object, RiskExpression>();
		private Parameters parameters = new Parameters();

		internal void SetUp()
		{
			isOPsbyId = new Dictionary<object, RiskExpression>();
			parameters = new Parameters()
			{
				["1"] = "A",
				["2"] = "B",
				["3"] = "C",
				["4"] = "D"
			};

			isOPsbyId.Add("C", Allow_OP.ALLOW);
		}
		internal void SetUp_WithoutMatch()
		{
			isOPsbyId = new Dictionary<object, RiskExpression>();
			parameters = new Parameters()
			{
				["1"] = "A",
				["2"] = "B",
				["3"] = "C",
				["4"] = "D"
			};

			isOPsbyId.Add("E", Allow_OP.ALLOW);
		}

		[TestMethod]
		public void Iteration_Test()
		{
			SetUp();

			var isOPsbyIdExp = Expression.Constant(isOPsbyId);
			var parametersExp = Expression.Constant(parameters);

			ParameterExpression index = Expression.Parameter(typeof(int), "index");			
			ParameterExpression item = Expression.Parameter(typeof(Parameter), "item");

			LabelTarget label = Expression.Label(typeof(Parameter));
	
			var atExp = Expression.Call(
						parametersExp,
						typeof(Parameters).GetMethod("At", BindingFlags.Instance | BindingFlags.NonPublic, null, new Type[] { typeof(int) }, null),
						index
						);

			var itemGetValueExp = Expression.Call(
			item,
			typeof(Parameter).GetMethod("GetValue", BindingFlags.Instance | BindingFlags.NonPublic)
			);

			var containsExp = Expression.Call(
				isOPsbyIdExp,
				typeof(Dictionary<object, RiskExpression>).GetMethod("ContainsKey", new Type[] { typeof(object) }),
				itemGetValueExp
				);

			BlockExpression blockDelCiclo = Expression.Block(
				Expression.AddAssign(index, Expression.Constant(1)),
				Expression.Assign(item, atExp),
				item
			);

			BlockExpression block = Expression.Block(
				new[] { index, item },
				Expression.AddAssign(index, Expression.Constant(0)),
				Expression.Assign(item,  atExp),
				Expression.Loop(
					Expression.IfThenElse(
						containsExp,
						Expression.Break(label, item),
						blockDelCiclo
					),
				label
				)
			);

			var factorial = Expression.Lambda<Func<Parameter>>(block).Compile()();
			Assert.AreEqual("C", factorial.GetValue());
			Assert.AreEqual("3", factorial.Name);
		}



		internal RiskExpression GetValue(object obj)
		{
			return isOPsbyId[obj];
		}
		[TestMethod]
		public void Iteration_Test2()
		{
			SetUp();

			var isOPsbyIdExp = Expression.Constant(isOPsbyId);
			var parametersExp = Expression.Constant(parameters);

			ParameterExpression index = Expression.Parameter(typeof(int), "index");
			ParameterExpression item = Expression.Parameter(typeof(Parameter), "item");

			LabelTarget label = Expression.Label(typeof(RiskExpression));

			var atExp = Expression.Call(
						parametersExp,
					typeof(Parameters).GetMethod("At", BindingFlags.Instance | BindingFlags.NonPublic, null, new Type[] { typeof(int) }, null),
						index
						);

			var itemGetValueExp = Expression.Call(
			item,
			typeof(Parameter).GetMethod("GetValue", BindingFlags.Instance | BindingFlags.NonPublic)
			);

			var containsExp = Expression.Call(
				isOPsbyIdExp,
				typeof(Dictionary<object, RiskExpression>).GetMethod("ContainsKey",  new Type[] { typeof(object) }),
				itemGetValueExp
				);
			var getValueExp = Expression.Call(
				Expression.Constant(this),
				typeof(Linq_Test).GetMethod("GetValue", BindingFlags.Instance | BindingFlags.NonPublic, null, new Type[] { typeof(object) }, null),
				itemGetValueExp
				);


			BlockExpression blockDelCiclo = Expression.Block(
				Expression.AddAssign(index, Expression.Constant(1)),
				Expression.Assign(item, atExp),
				item
			);

			BlockExpression block = Expression.Block(
				new[] { index, item },
				Expression.AddAssign(index, Expression.Constant(0)),
				Expression.Assign(item, atExp),
				Expression.Loop(
					Expression.IfThenElse(
						containsExp,
						Expression.Break(label, getValueExp),
						blockDelCiclo
					),
				label
				)
			);

			var factorial = Expression.Lambda<Func<RiskExpression>>(block).Compile()();
			Assert.AreEqual(Allow_OP.ALLOW, factorial);
		}

		[TestMethod]
		public void Iteration_Test3()
		{
			SetUp_WithoutMatch();

			var isOPsbyIdExp = Expression.Constant(isOPsbyId);
			var parametersExp = Expression.Constant(parameters);

			ParameterExpression index = Expression.Parameter(typeof(int), "index");
			ParameterExpression item = Expression.Parameter(typeof(Parameter), "item");

			LabelTarget label = Expression.Label(typeof(RiskExpression));

			var parametersAt = Expression.Call(
						parametersExp,
						typeof(Parameters).GetMethod("At", BindingFlags.Instance | BindingFlags.NonPublic, null, new Type[] { typeof(int) }, null),
						index
						);
			var parametersCount = Expression.Call(
					parametersExp,
					typeof(Parameters).GetMethod("Count", BindingFlags.Instance | BindingFlags.NonPublic)
					);

			var itemGetValueExp = Expression.Call(
			item,
			typeof(Parameter).GetMethod("GetValue", BindingFlags.Instance | BindingFlags.NonPublic)
			);

			var containsExp = Expression.Call(
				isOPsbyIdExp,
				typeof(Dictionary<object, RiskExpression>).GetMethod("ContainsKey", new Type[] { typeof(object) }),
				itemGetValueExp
				);
			var getValueExp = Expression.Call(
				Expression.Constant(this),
				typeof(Linq_Test).GetMethod("GetValue", BindingFlags.Instance | BindingFlags.NonPublic, null, new Type[] { typeof(object) }, null),
				itemGetValueExp
				);


			var defaultRule = new RiskRule(666, new RiskPredicate[] { null}, RiskAction.Deny);
			BlockExpression blockDelCiclo = Expression.Block(
				Expression.IfThen(
					Expression.GreaterThanOrEqual(
						Expression.Add(index, Expression.Constant(1)),
						parametersCount),
					Expression.Break(label, Expression.Constant(defaultRule))
					),
				Expression.AddAssign(index, Expression.Constant(1)),
				Expression.Assign(item, parametersAt),
				item
			) ;

			BlockExpression block = Expression.Block(
				new[] { index, item },
				Expression.AddAssign(index, Expression.Constant(0)),
				Expression.Assign(item, parametersAt),
				Expression.Loop(
					Expression.IfThenElse(
						containsExp,
						Expression.Break(label, getValueExp),
						blockDelCiclo
					),
				label
				)
			);

			var factorial = Expression.Lambda<Func<RiskExpression>>(block).Compile()();
			Assert.AreEqual(defaultRule, factorial);
		}
	}
}