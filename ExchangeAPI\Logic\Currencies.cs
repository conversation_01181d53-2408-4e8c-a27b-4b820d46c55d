﻿using ExchangeAPI.ExchangeEntities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ExchangeAPI.Logic
{
    public class Currencies
    {
        public static void Save(Currency currency)
        {
            if (currency == null) throw new ArgumentNullException(nameof(currency));

            using (var context = new ExchangeContext())
            {
                context.Currency.Add(currency);
                context.SaveChanges();
            }
        }
    }
}
