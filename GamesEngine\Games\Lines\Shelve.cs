﻿using GamesEngine.Domains;
using GamesEngine.Gameboards.Lines;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using GamesEngine.Time;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;

namespace GamesEngine.Games.Lines
{
	class Shelve : Objeto
	{
		private readonly List<Line> lastVersionLines;
		private readonly Showcase showcase;
		private readonly Game game;
		internal Shelve(Showcase showcase, Game game)
		{
			if (showcase == null) throw new ArgumentNullException(nameof(showcase));

			this.lastVersionLines = new List<Line>();
			this.showcase = showcase;
			this.game = game;
		}

		private void AddLine(Line line)
		{
			if (line == null) throw new ArgumentNullException(nameof(line));
			if (line.Game != this.game) throw new GameEngineException($"Line {line.LineId} does not belong to this Shelve");
			if (line.Game.Tournament != this.game.Tournament) throw new GameEngineException($"Game associated to line {line.LineId} does not belong at the same Tournament");
			
			this.lastVersionLines.Add(line);
			this.showcase.Betboard.AddLine(line);
		}

		internal void UpdateLine(Line line)
		{
			if (line == null) throw new ArgumentNullException(nameof(line));

			var original = line.OriginalVersion;
			if (original == line) throw new GameEngineException("An original version could not be updated, use AddLine method instead");
			for (int i = 0; i < this.lastVersionLines.Count; i++)
			{
				if (this.lastVersionLines[i].OriginalVersion == original)
				{
					var thisIsTheFirstAddedLine = this.showcase.IsFirstLineToBePublished();
					if (thisIsTheFirstAddedLine && line.IsPublished)
					{
						var firstAvailableLineEvent = new FirstAvailableLineEvent(line.CreationDate, line);
						PlatformMonitor.GetInstance().WhenNewEvent(firstAvailableLineEvent);
					}

					this.lastVersionLines[i] = line;
					this.showcase.Betboard.AddLine(line);
					return;
				}
			}
			throw new GameEngineException($"Line {line.ToString()} does not belong to this shelve");
		}

		internal void RemoveLine(Line line)
		{
			if (line == null) throw new ArgumentNullException(nameof(line));
			if (!line.IsDraft && this.Showcase.Wagers.HasPurchases(line)) throw new GameEngineException($"Line {line.ToString()} can only be removed when is drafted or it does not have purchases");

			this.lastVersionLines.Remove(line);
			this.showcase.Betboard.RemoveLine(line);
		}

		private SpreadLine CreateSpreadLine(int lineID, Question question, Tier tier, double spread, int teamAReward, int teamBReward, string who, DateTime now)
		{
			if (tier == null) throw new ArgumentNullException(nameof(tier));
			if (question == null) throw new ArgumentNullException(nameof(question));
			if (tier != Tier.TIER_ONE && tier != Tier.TIER_TWO) throw new GameEngineException("Questions only can be created for Tier one or Tier two");
			if (game.IsInPlay() && tier != Tier.TIER_TWO) throw new GameEngineException("You can not add a spread line because game is already started");
			if (game.IsGameOver()) throw new GameEngineException("You can not add a spread line because game is already ended");
			Line.ValidateReward(teamAReward, teamBReward);

			var newIndex = this.lastVersionLines.Count;
			this.showcase.Betboard.SetLastLineId(lineID);
			SpreadLine result = new SpreadLine(lineID, question, tier, game, this, newIndex, spread, teamAReward, teamBReward, who, now);
			AddLine(result);
			return result;
		}

		internal SpreadLine CreateNewVersionForSpreadLine(int lineID, int teamAReward, int teamBReward, string who, DateTime now)
		{
			if (game.IsGameOver()) throw new GameEngineException("You can not add a spread line because game is already ended");
			Line.ValidateReward(teamAReward, teamBReward);

			var currentLine = this.showcase.Betboard.GetLine(lineID);
			if (game.IsInPlay() && currentLine.Tier != Tier.TIER_TWO) throw new GameEngineException("You can not add a spread line because game is already started");
			if (!currentLine.IsSpreadLine()) throw new GameEngineException($"This {nameof(currentLine)} is not a line of type {nameof(SpreadLine)}");
			
			SpreadLine result = (currentLine as SpreadLine).NewVersion(teamAReward, teamBReward, who, now);

			return result;
		}

		private MoneyLine CreateMoneyLine(int lineID, Question question, Tier tier, int teamAReward, int teamBReward, string who, DateTime now)
		{
			if (tier == null) throw new ArgumentNullException(nameof(tier));
			if (question == null) throw new ArgumentNullException(nameof(question));
			if (tier != Tier.TIER_ONE && tier != Tier.TIER_TWO) throw new GameEngineException("Questions only can be created for Tier one or Tier two");
			if (game.IsInPlay() && tier != Tier.TIER_TWO) throw new GameEngineException("You can not add a money line because game is already started");
			if (game.IsGameOver()) throw new GameEngineException("You can not add a money line because game is already ended");
			Line.ValidateReward(teamAReward, teamBReward);			


			var newIndex = this.lastVersionLines.Count;
			this.showcase.Betboard.SetLastLineId(lineID);
			MoneyLine result = new MoneyLine(lineID, question, tier, game, this, newIndex, teamAReward, teamBReward, who, now);
			AddLine(result);
			return result;
		}

		internal MoneyLine CreateNewVersionForMoneyLine(int lineID, int teamAReward, int teamBReward, string who, DateTime now)
		{
			if (game.IsGameOver()) throw new GameEngineException("You can not add a money line because game is already ended");
			Line.ValidateReward(teamAReward, teamBReward);

			var currentLine = this.showcase.Betboard.GetLine(lineID);
			if (game.IsInPlay() && currentLine.Tier != Tier.TIER_TWO) throw new GameEngineException("You can not add a money line because game is already started");
			if (!currentLine.IsMoneyLine()) throw new GameEngineException($"This {nameof(currentLine)} is not a line of type {nameof(MoneyLine)}");
			
			MoneyLine result = (currentLine as MoneyLine).NewVersion(teamAReward, teamBReward, who, now);

			return result;
		}

		private MoneyDrawLine CreateMoneyDrawLine(int lineID, Question question, Tier tier, int teamAReward, int tieReward, int teamBReward, string who, DateTime now)
		{
			if (tier == null) throw new ArgumentNullException(nameof(tier));
			if (question == null) throw new ArgumentNullException(nameof(question));
			if (tier != Tier.TIER_ONE && tier != Tier.TIER_TWO) throw new GameEngineException("Questions only can be created for Tier one or Tier two");
			if (game.IsInPlay() && tier != Tier.TIER_TWO) throw new GameEngineException("You can not add a money line because game is already started");
			if (game.IsGameOver()) throw new GameEngineException("You can not add a money line because game is already ended");
			Line.ValidateReward(teamAReward, teamBReward); Line.ValidateReward(tieReward);

			var newIndex = this.lastVersionLines.Count;
			this.showcase.Betboard.SetLastLineId(lineID);
			MoneyDrawLine result = new MoneyDrawLine(lineID, question, tier, game, this, newIndex, teamAReward, tieReward, teamBReward, who, now);
			AddLine(result);
			return result;
		}

		internal MoneyDrawLine CreateNewVersionForMoneyDrawLine(int lineID, int teamAReward, int tieReward, int teamBReward, string who, DateTime now)
		{
			if (game.IsGameOver()) throw new GameEngineException("You can not add a money line because game is already ended");
			Line.ValidateReward(teamAReward, teamBReward); Line.ValidateReward(tieReward);

			var currentLine = this.showcase.Betboard.GetLine(lineID);
			if (game.IsInPlay() && currentLine.Tier != Tier.TIER_TWO) throw new GameEngineException("You can not add a money line because game is already started");
			if (!currentLine.IsMoneyDrawLine()) throw new GameEngineException($"This {nameof(currentLine)} is not a line of type {nameof(MoneyLine)}");
			
			MoneyDrawLine result = (currentLine as MoneyDrawLine).NewVersion(teamAReward, tieReward, teamBReward, who, now);

			return result;
		}


		private TotalPointsLine CreateTotalPointsLine(int lineID, Question question, Tier tier, double score, int overReward, int underReward, string who, DateTime now)
		{
			if (tier == null) throw new ArgumentNullException(nameof(tier));
			if (question == null) throw new ArgumentNullException(nameof(question));
			if (tier != Tier.TIER_ONE && tier != Tier.TIER_TWO) throw new GameEngineException("Questions only can be created for Tier one or Tier two");
			if (game.IsInPlay() && tier != Tier.TIER_TWO) throw new GameEngineException("You can not add a money line because game is already started");
			if (game.IsGameOver()) throw new GameEngineException("You can not add a over/under line because game is already ended");
			if (score <= 0) throw new GameEngineException("Score must be greater than zero");
			Line.ValidateReward(overReward, underReward);

			var newIndex = this.lastVersionLines.Count;
			this.showcase.Betboard.SetLastLineId(lineID);
			TotalPointsLine result = new TotalPointsLine(lineID, question, tier, game, this, newIndex, score, overReward, underReward, who, now);
			AddLine(result);
			return result;
		}

		internal TotalPointsLine CreateNewVersionForTotalPointsLine(int lineID, int overReward, int underReward, string who, DateTime now)
		{
			if (game.IsGameOver()) throw new GameEngineException("You can not add a over/under line because game is already ended");
			Line.ValidateReward(overReward, underReward);

			var currentLine = this.showcase.Betboard.GetLine(lineID);
			if (game.IsInPlay() && currentLine.Tier != Tier.TIER_TWO) throw new GameEngineException("You can not add a money line because game is already started");
			if (!currentLine.IsTotalPointsLine()) throw new GameEngineException($"This {nameof(currentLine)} is not a line of type {nameof(TotalPointsLine)}");
			
			TotalPointsLine result = (currentLine as TotalPointsLine).NewVersion(overReward, underReward, who, now);

			return result;
		}

		private YesNoLine CreateYesNoLine(int lineID, Question question, Tier tier, int yesReward, int noReward, string text, string who, DateTime now)
		{
			if (game.IsGameOver()) throw new GameEngineException("You can not add a yes/no line because game is already ended");
			if (tier == null) throw new ArgumentNullException(nameof(tier));
			if (question == null) throw new ArgumentNullException(nameof(question));
			Line.ValidateReward(yesReward, noReward);
			if (string.IsNullOrWhiteSpace(text)) throw new ArgumentNullException(nameof(text));

			var newIndex = this.lastVersionLines.Count;
			this.showcase.Betboard.SetLastLineId(lineID);
			YesNoLine result = new YesNoLine(lineID, question, tier, game, this, newIndex, yesReward, noReward, text, who, now);
			AddLine(result);
			return result;
		}

		internal YesNoLine CreateNewVersionForYesNoLine(int lineID, int yesReward, int noReward, string who, DateTime now)
		{
			if (game.IsGameOver()) throw new GameEngineException("You can not add a yes/no line because game is already ended");
			Line.ValidateReward(yesReward, noReward);

			var currentLine = this.showcase.Betboard.GetLine(lineID);
			if (!currentLine.IsYesNoLine()) throw new GameEngineException($"This {nameof(currentLine)} is not a line of type {nameof(YesNoLine)}");
			YesNoLine result = ((YesNoLine)currentLine).NewVersion(yesReward, noReward, who, now);
			
			return result;
		}

		private OverUnderLine CreateOverUnderLine(int lineID, Question question, Tier tier, double score, int overReward, int underReward, string text, string who, DateTime now)
		{
			if (game.IsGameOver()) throw new GameEngineException("You can not add a over/under line because game is already ended");
			if (tier == null) throw new ArgumentNullException(nameof(tier));
			if (question == null) throw new ArgumentNullException(nameof(question));
			if (score <= 0) throw new GameEngineException("Score must be greater than zero");
			Line.ValidateReward(overReward, underReward);
			if (string.IsNullOrWhiteSpace(text)) throw new ArgumentNullException(nameof(text));

			var newIndex = this.lastVersionLines.Count;
			this.showcase.Betboard.SetLastLineId(lineID);
			OverUnderLine result = new OverUnderLine(lineID, question, tier, game, this, newIndex, score, overReward, underReward, text, who, now);
			AddLine(result);
			return result;
		}

		internal OverUnderLine CreateNewVersionForOverUnderLine(int lineID, int overReward, int underReward, string who, DateTime now)
		{
			if (game.IsGameOver()) throw new GameEngineException("You can not add a over/under line because game is already ended");
			Line.ValidateReward(overReward, underReward);

			var currentLine = this.showcase.Betboard.GetLine(lineID);
			if (!currentLine.IsOverUnderLine()) throw new GameEngineException($"This {nameof(currentLine)} is not a line of type {nameof(OverUnderLine)}");
			OverUnderLine result = ((OverUnderLine)currentLine).NewVersion(overReward, underReward, who, now);
			
			return result;
		}

		private FixedLine CreateFixedLine(int lineID, Question question, Tier tier, IEnumerable<string> options, IEnumerable<int> rewards, string text, string who, DateTime now)
		{
			if (game.IsGameOver()) throw new GameEngineException("You can not add a fixed line because game is already ended");
			if (tier == null) throw new ArgumentNullException(nameof(tier));
			if (question == null) throw new ArgumentNullException(nameof(question));
			if (options == null) throw new ArgumentNullException(nameof(options));
			if (rewards == null) throw new ArgumentNullException(nameof(rewards));
			if (options.Any(x => string.IsNullOrWhiteSpace(x))) throw new ArgumentNullException(nameof(options));
			if (options.Count() != rewards.Count()) throw new GameEngineException("Options and rewards must have the same length");
			FixedLine.ValidateRewards(rewards);
			if (string.IsNullOrWhiteSpace(text)) throw new ArgumentNullException(nameof(text));

			var newIndex = this.lastVersionLines.Count;
			this.showcase.Betboard.SetLastLineId(lineID);
			FixedLine result = new FixedLine(lineID, question, tier, game, this, newIndex, rewards, options, text, who, now);
			AddLine(result);
			return result;
		}

		internal FixedLine CreateNewVersionForFixedLine(int lineID, IEnumerable<int> rewards, string who, DateTime now)
		{
			if (game.IsGameOver()) throw new GameEngineException("You can not add a fixed line because game is already ended");
			if (rewards == null) throw new ArgumentNullException(nameof(rewards));
			FixedLine.ValidateRewards(rewards);
			var currentLine = this.showcase.Betboard.GetLine(lineID);
			if (!currentLine.IsFixedLine()) throw new GameEngineException($"This {nameof(currentLine)} is not a line of type {nameof(FixedLine)}");
						
			var currentFixedLine = (FixedLine)currentLine;
			if (currentFixedLine.Options.Count() != rewards.Count()) throw new GameEngineException("Options and rewards must have the same length");
			FixedLine result = currentFixedLine.NewVersion(rewards, who, now);

			return result;
		}

		internal int IndexOf(Line line)
		{
			if (line == null) throw new ArgumentNullException(nameof(line));

			int index = this.lastVersionLines.IndexOf(line);
			return index;
		}

		internal int MyIndex()
		{
			int index = showcase.IndexOf(this);
			return index;
		}

		internal void Swap(Shelve shelve, int index)
		{
			if (shelve == null) throw new ArgumentNullException(nameof(shelve));
			if (this.lastVersionLines.Count <= index) throw new GameEngineException("The index is out of bounds of the lines in this shelve");
			if (shelve.lastVersionLines.Count <= index) throw new GameEngineException("The index is out of bounds of the lines in parameters shelve");
			if (this == shelve) throw new GameEngineException("Invalid swap. It must be down with different shelve.");

			Line temp = this.lastVersionLines[index];
			this.lastVersionLines[index] = shelve.lastVersionLines[index];
			shelve.lastVersionLines[index] = temp;
		}

		internal void AddWager(Wager wager)
		{
			if (wager == null) throw new ArgumentNullException(nameof(wager));
			if (wager.Showcase != this.showcase) throw new GameEngineException($"This wager {wager.Name} does not belong to this showcase {this.showcase.Game.ToString()}");

			this.showcase.AddWager(wager);
		}
		internal Line CreateLine(int lineId, Question question, DateTime specificDateTime, string who, DateTime now) // llamar en el servicio para SpecificDateTime
		{
			if (question == null) throw new ArgumentNullException(nameof(question));
			if (question.TimeKillerMoment != AvailabilityTime.Milestone.SpecificDateTime) throw new GameEngineException($"This case is only for specific date time.");
			var seconds = (int)(specificDateTime - now).TotalSeconds;
			if (seconds < Question.MINIMUM_LIVE_TIME ) throw new GameEngineException($"Time in seconds must be greater than {Question.MINIMUM_LIVE_TIME} seconds");

			var oldTimeKiller = question.TimeKillerInSeconds;

			question.TimeKillerInSeconds = seconds;
			var result = this.CreateLine(lineId, question, who, now);
			
			if (oldTimeKiller != 0) question.TimeKillerInSeconds = oldTimeKiller;
			return result;
		}

		internal Line CreateLine(int lineId, Question question, string who, DateTime now)
		{
			if (question == null) throw new ArgumentNullException(nameof(question));
			if (question.Sport != this.game.Sport) throw new GameEngineException($"Question with shortcut {question.Shortcut} does not belong to this sport");

			var activations = showcase.Betboard.Catalog.DomainsActivationFrom(question);
			Line result;
			switch (question)
			{
				case SpreadQuestion sq:
					result = this.CreateSpreadLine(lineId, question, sq.Tier, sq.Spread, sq.TeamAReward, sq.TeamBReward, who, now);
					break;
				case MoneyQuestion mn:
					result = this.CreateMoneyLine(lineId, question, mn.Tier, mn.TeamAReward, mn.TeamBReward, who, now);
					break;
				case MoneyDrawQuestion md:
					result = this.CreateMoneyDrawLine(lineId, question, md.Tier, md.TeamAReward, md.TieReward, md.TeamBReward, who, now);
					break;
				case TotalPointsQuestion tP:
					result = this.CreateTotalPointsLine(lineId, question, tP.Tier, tP.Score, tP.OverReward, tP.UnderReward, who, now);
					break;
				case YesNoQuestion yN:
					result = this.CreateYesNoLine(lineId, question, yN.Tier, yN.YesReward, yN.NoReward, yN.Text, who, now);
					break;
				case OverUnderQuestion oU:
					result = this.CreateOverUnderLine(lineId, question, oU.Tier, oU.Score, oU.OverReward, oU.UnderReward, oU.Text, who, now);
					break;
				case FixedQuestion fQ:
					result = this.CreateFixedLine(lineId, question, fQ.Tier, fQ.Options, fQ.Rewards, fQ.Text, who, now);
					break;
				default:
					throw new GameEngineException($"Question {question.GetType().Name} is not been handled");
			}

			if (question.HasBackground()) result.Background = question.Background;
			result.CopyActivationsFrom(activations);
			foreach (Period period in question.ValidPeriodsToBePublished())
            {
				result.AllowToPublishOn(period);
			}

			switch (question.PublishTimeMoment)
			{
				case AvailabilityTime.Milestone.None:
					break;
				case AvailabilityTime.Milestone.BeforeGameStarts:
				case AvailabilityTime.Milestone.AfterGameStarts:
					result.PublishDate = this.game.GetAvailabilityTime(question.PublishTimeMoment, question.PublishTimeInSeconds);
					break;
				case AvailabilityTime.Milestone.AfterPeriodStarts:
					Period period = this.game.GetPeriod(question.PublishTimePeriodName);
					result.PublishDate = this.game.GetAvailabilityTime(question.PublishTimeMoment, period, question.PublishTimeInSeconds);
					break;
				default:
					throw new GameEngineException($"Question's moment {question.PublishTimeMoment.ToString()} is invalid");
			}
			switch (question.TimeKillerMoment)
			{
				case AvailabilityTime.Milestone.None:
					break;
				case AvailabilityTime.Milestone.BeforeGameStarts:
				case AvailabilityTime.Milestone.AfterGameStarts:
					result.DueDate = this.game.GetAvailabilityTime(question.TimeKillerMoment, question.TimeKillerInSeconds);
					break;
				case AvailabilityTime.Milestone.AfterPeriodStarts:
					Period period = this.game.GetPeriod(question.TimeKillerPeriodName);
					result.DueDate = this.game.GetAvailabilityTime(question.TimeKillerMoment, period, question.TimeKillerInSeconds);
					break;
				case AvailabilityTime.Milestone.AfterLinePublishing:
					result.DueDate = this.game.GetAvailabilityTime(question.TimeKillerMoment, result, question.TimeKillerInSeconds);
					break;
				case AvailabilityTime.Milestone.SpecificDateTime:
					result.DueDate = this.game.GetAvailabilityTime(question.TimeKillerMoment, result, question.TimeKillerInSeconds);
					break;
				default:
					throw new GameEngineException($"Question's moment {question.TimeKillerMoment.ToString()} is invalid");
			}

			return result;
		}

		internal Line CreateLine(int lineId, int questionId, string who, DateTime now)
		{
			var question = showcase.Betboard.Catalog.FindQuestionById(questionId);
			var result = CreateLine(lineId, question, who, now);
			return result;
		}

		internal IEnumerable<Line> LastVersionLines
		{
			get
			{
				return this.lastVersionLines;
			}
		}

		internal IEnumerable<Line> PendingOrRegradedLines
		{
			get
			{
				IEnumerable<Line> result = lastVersionLines.Where(line => !line.IsDraft && (line.IsPending() || line.IsRegraded()));
				return result;
			}
		}

		internal IEnumerable<Line> GradedLines
		{
			get
			{
				IEnumerable<Line> result = lastVersionLines.Where(line => line.IsGraded() || line.IsNoAction());
				return result;
			}
		}

		internal IEnumerable<Line> OriginalVersionLines
		{
			get
			{
				return this.lastVersionLines.Select(x => x.OriginalVersion);
			}
		}

		internal IEnumerable<Line> LinesBasedOn(IEnumerable<Question> questions)
		{
			IEnumerable<Line> result = Enumerable.Empty<Line>();
			foreach (var line in lastVersionLines)
            {
				if (questions.Contains(line.BasedOn)) result = result.Append(line);
            }
			return result;
		}

		internal Showcase Showcase
		{
			get
			{
				return this.showcase;
			}
		}

		internal Wagers Wagers
		{
			get
			{
				return this.showcase.Wagers;
			}
		}

		internal Line this[int lineIndex]
		{
			get
			{
				if (lineIndex < 0 || lineIndex >= lastVersionLines.Count) throw new GameEngineException($"Line index {lineIndex} is out of range");

				var result = lastVersionLines[lineIndex];
				return result;
			}
		}

		internal LinesCounter CountLines()
		{
			var result = new LinesCounter();
            foreach (var line in lastVersionLines)
            {
				if (line.IsGraded()) result.AddOneGradedLine();
				if (line.IsPublished) result.AddOnePublishedLine();
				else if (line.IsDraft) result.AddOneDraftedLine();
				else if (line.IsSuspended) result.AddOneSuspendedLine();
				else if (line.IsCanceled) result.AddOneCanceledLine();
			}
			return result;
		}

	}

	[Puppet]
	class LinesCounter : Objeto
	{
		internal int CountLines
		{
			get
			{
				return CountCanceledLines + CountPublishedLines + CountDraftedLines + CountSuspendedLines;
			}
		}
		internal int CountGradedLines { get; private set; }
		internal int CountPublishedLines { get; private set; }
		internal int CountDraftedLines { get; private set; }
		internal int CountSuspendedLines { get; private set; }
		internal int CountCanceledLines { get; private set; }

		internal void AddOneGradedLine()
		{
			CountGradedLines++;
		}
		internal void AddOnePublishedLine()
		{
			CountPublishedLines++;
		}
		internal void AddOneDraftedLine()
		{
			CountDraftedLines++;
		}
		internal void AddOneSuspendedLine()
		{
			CountSuspendedLines++;
		}
		internal void AddOneCanceledLine()
		{
			CountCanceledLines++;
		}

		internal void AddCounter(LinesCounter counter)
		{
			if (counter == null) throw new ArgumentNullException(nameof(counter));

			CountGradedLines += counter.CountGradedLines;
			CountPublishedLines += counter.CountPublishedLines;
			CountDraftedLines += counter.CountDraftedLines;
			CountSuspendedLines += counter.CountSuspendedLines;
			CountCanceledLines += counter.CountCanceledLines;
		}
	}
}
