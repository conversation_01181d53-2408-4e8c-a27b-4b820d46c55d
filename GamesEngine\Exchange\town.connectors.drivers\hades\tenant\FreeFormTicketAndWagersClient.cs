﻿using Puppeteer.EventSourcing;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.commons;
using town.connectors.drivers;
using town.connectors.drivers.hades;

namespace Connectors.town.connectors.drivers.hades
{
	public class FreeFormTicketAndWagersClient : ASITenantDriver
	{
		private HttpClient _postFreeFormTicketAndWagersClient;
		private string SystemId;
        private string SystemPassword;
        private string ClerkId;
		private string CompanyBaseUrlServices;

		public FreeFormTicketAndWagersClient() : base(Tenant_Actions.Others)
		{
		}
		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            throw new NotImplementedException();
        }

		public override void Prepare(DateTime now)
		{
			CustomSettings.AddVariableParameter("customerId");
			CustomSettings.AddVariableParameter("amount");
			CustomSettings.AddVariableParameter("wagers");

			//CustomSettings.Prepare();

			CompanyBaseUrlServices = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
			SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
			SystemPassword = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
			ClerkId = CustomSettings.Get(now, "CompanyClerkId").AsString;
		}

		public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
			if (_postFreeFormTicketAndWagersClient == null)
			{
				_postFreeFormTicketAndWagersClient = new HttpClient
				{
					BaseAddress = new Uri(CompanyBaseUrlServices)
				};
			}

			if (CustomSettings.ThereArePendingChanges)
			{
				bool changeApplied = false;
				CompanyBaseUrlServices = CustomSettings.Get(now, "CompanyBaseUrlServices", out changeApplied).AsString;
				if (changeApplied)
				{
					_postFreeFormTicketAndWagersClient = new HttpClient
					{
						BaseAddress = new Uri(CompanyBaseUrlServices)
					};
				}

				SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
				SystemPassword = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
				ClerkId = CustomSettings.Get(now, "CompanyClerkId").AsString;
			}

			var CustomerId = recordSet.Mappings["customerId"];
			var Amount = recordSet.Mappings["amount"];
			var Wagers = recordSet.Mappings["wagers"];

			string customerId = CustomerId.AsString;
			decimal amount = Amount.AsDecimal;
			List< PostFreeFormWager > wagers = Wagers.As<List<PostFreeFormWager>>();


			var result = await PostFreeFormTicketAndWagersAsync(now, customerId, amount, wagers);
			return (T)Convert.ChangeType(result, typeof(T));
		}

        [Obsolete]
		private async Task<PostFreeFormTicketAndWagersResponse> PostFreeFormTicketAndWagersAsync(DateTime now, string customerId, decimal amount, List<PostFreeFormWager> wagers)
		{
			if (String.IsNullOrWhiteSpace(customerId)) throw new ArgumentNullException(nameof(customerId));
			if (wagers == null) throw new ArgumentNullException(nameof(wagers));
			if (wagers.Count == 0) throw new Exception($"At least one wager is required to send request {nameof(PostFreeFormTicketAndWagersAsync)} CustomerId: {customerId}");
			if (amount <= 0) throw new Exception($"Amount {amount} is not valid to send request {nameof(PostFreeFormTicketAndWagersAsync)}");
			var totalRisk = wagers.Sum(x => decimal.Parse(x.Risk));
			if (amount != totalRisk) throw new Exception($"{nameof(amount)} {amount} does not match {nameof(totalRisk)} {totalRisk}");

			var strTotal = amount.ToString();
			var values = new PostFreeFormTicketAndWagersBody()
			{
				SystemId = SystemId,
				SystemPassword = SystemPassword,
				ClerkId = ClerkId,
				CustomerId = customerId,
				TotalRisk = strTotal,
				Wagers = wagers.ToArray()
			};
			const string url = "/v1/5dimesAPI/PostFreeFormTicketAndWagers";
			var jsonString = await ASIJsonUtils.ToJsonAsync(values);
			var httpContent = new StringContent(jsonString, Encoding.UTF8, "application/json");

			string responseString = "";
			int retryNumber = 0;

			string valuesWithHiddenFields = await ASIJsonUtils.HideSensitiveDataAsync(values, Loggers.GetIntance().AccountingServicesASIPostFreeFormTicketAndWagers);

			while (true)
			{
				try
				{
					Loggers.GetIntance().AccountingServicesASIPostFreeFormTicketAndWagers.Debug($@"url:{url} data:{valuesWithHiddenFields}");

					var response = await _postFreeFormTicketAndWagersClient.PostAsync(url, httpContent);
					responseString = await response.Content.ReadAsStringAsync();

					Loggers.GetIntance().AccountingServicesASIPostFreeFormTicketAndWagers.Debug($@"response:{responseString}");

					break;
				}
				catch (Exception e)
				{
					Loggers.GetIntance().AccountingServicesASIPostFreeFormTicketAndWagers.Error($@"url:{url} data:{valuesWithHiddenFields} type:{e.GetType()} error:{e.Message}", e);

					retryNumber++;
					var extraErrorMessage = string.Empty;
					if (e.InnerException is HttpRequestException) extraErrorMessage = "There was a problem getting the response from server";
					InternalOnError(nameof(PostFreeFormTicketAndWagersAsync), retryNumber, e, $"Url:{url}", $"Request: {valuesWithHiddenFields}", $"Response: {responseString}", extraErrorMessage);

					await Task.Delay(5000);

					if (retryNumber == MAX_RETRIES)
					{
						return CreateFakePostFreeFormTicketAndWagersResponse(wagers);
					}
				}
			}

			if (String.IsNullOrWhiteSpace(responseString))
			{
				NotifyWarn(nameof(PostFreeFormTicketAndWagersAsync), $"Url:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}", $"Response can not be empty");
				return CreateFakePostFreeFormTicketAndWagersResponse(wagers);
			}
			else
			{
				var objectResponse = Commons.FromJson<PostFreeFormTicketAndWagersResponse>(responseString);
				if (objectResponse.Error != null)
				{
					NotifyWarn(nameof(PostFreeFormTicketAndWagersAsync), $"Url:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}", $"Response threw an error");
					return CreateFakePostFreeFormTicketAndWagersResponse(wagers);
				}
				if (objectResponse.TicketNumber <= 0)
				{
					NotifyWarn(nameof(PostFreeFormTicketAndWagersAsync), $"Url:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}", $"{nameof(objectResponse.TicketNumber)} {objectResponse.TicketNumber} is not valid");
				}
				else if (objectResponse.Wagers == null)
				{
					NotifyWarn(nameof(PostFreeFormTicketAndWagersAsync), $"Url:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}", $"{nameof(objectResponse.Wagers)} are null");
				}
				else if (objectResponse.Wagers.Length != wagers.Count)
				{
					NotifyWarn(nameof(PostFreeFormTicketAndWagersAsync), $"Url:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}", $"Wagers count is not the same. Body has {wagers.Count} wagers and response has {objectResponse.Wagers.Length} wagers");
				}
				return objectResponse;
			}
		}

	}
}
