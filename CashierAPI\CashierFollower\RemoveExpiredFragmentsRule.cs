﻿using Puppeteer;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CashierAPI.CashierFollower
{
    public class RemoveExpiredFragmentsRule : RuleWithoutActor
    {
        protected const string START_TEXT_LINE = "atAddress.RemoveExpiredFragments(";
        protected const string END_TEXT_LINE = ");";

        public override void FinalizeRule()
        {
            return;
        }

        public override void Then(Script script)
        {
            if (script.DairyId != LastIdRule.Id) DBHelperCashierFollower.ProceedToMarkSkipsForSpecificDairyId(script.DairyId);
        }

        public override void When(Script script)
        {
            if (script.Text.IndexOf(START_TEXT_LINE) != -1 && script.Text.IndexOf(END_TEXT_LINE) != -1)
            {
                Then(script);
            }
        }
    }
}
