﻿using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Text;

namespace GamesEngine.Accounting
{
	internal class AccountingAccount : Objeto
	{
		private string account;
		private string description;

		internal AccountingAccount(string account, string description)
		{
			if (string.IsNullOrWhiteSpace(account)) throw new ArgumentNullException(nameof(account));
			if (string.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));

			this.account = account;
			this.description = description;
		}

		internal string Account
		{
			get
			{
				return this.account;
			}
		}

		internal string Description
		{
			get
			{
				return this.description;
			}
		}
	}
}
