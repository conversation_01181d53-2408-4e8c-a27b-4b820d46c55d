﻿using GamesEngine.Finance;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;
using town.connectors.drivers;
using static GamesEngine.Business.WholePaymentProcessor;
[assembly: InternalsVisibleTo("GamesEngineTests")]
namespace GamesEngine.Business
{
    internal interface IndexedProcessors
    {
        internal abstract void Addon(PaymentProcessor processor);
        internal abstract bool IsACollectionNeeded();
        internal abstract bool IsEmpty();
        internal abstract MultipleProcessors ChangeToMultipleIndexProcessor();
        internal abstract PaymentProcessor SearchBy(string iso4217Code);
        internal abstract PaymentProcessor SearchBy(TransactionType transactionType, string iso4217Code);
        internal abstract PaymentProcessor SearchBy(TransactionType transactionType, string iso4217Code, Type processorDriverType);
        internal abstract IEnumerable<PaymentProcessor> SearchBy(TransactionType transactionType, int entityId, int paymentMethodId, int coinId);
        internal abstract PaymentProcessor SearchBy(Tenant_Actions action);
        internal abstract IEnumerable<PaymentProcessor> SearchProcessorsBy(Tenant_Actions action);
        internal abstract PaymentProcessor SearchBy(Type processorDriverType);
        internal abstract PaymentProcessor FirstOne();
        internal abstract PaymentProcessor SearchBy(Func<PaymentProcessor, bool> filter);
    }

    internal sealed class SingleProcessor : IndexedProcessors
    {
        private PaymentProcessor procesor;

        void IndexedProcessors.Addon(PaymentProcessor processor)
        {
            if (this.procesor != null) throw new GameEngineException("Only one instance can be added.");

            this.procesor = processor;
        }

        MultipleProcessors IndexedProcessors.ChangeToMultipleIndexProcessor()
        {
            MultipleProcessors result = new MultipleProcessors();
            result.Addon(this.procesor);
            return result;
        }

        bool IndexedProcessors.IsACollectionNeeded()
        {
            return (this.procesor != null);
        }

        bool IndexedProcessors.IsEmpty()
        {
            return (this.procesor == null);
        }

        PaymentProcessor IndexedProcessors.SearchBy(string iso4217Code)
        {
            Coin coin = Coinage.Coin(iso4217Code);

            if (procesor.Visible && procesor.Enabled &&
                 procesor.Coin == coin)
                return procesor;

            throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured  for {iso4217Code}.");

        }

        PaymentProcessor IndexedProcessors.SearchBy(TransactionType transactionType, string iso4217Code)
        {
            Coin coin = Coinage.Coin(iso4217Code);

            if (procesor.Visible && procesor.Enabled &&
                procesor.Transactions.FirstOrDefault(x => x.Name == transactionType.ToString()) != null &&
                procesor.Coin == coin)
                return procesor;

            throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured  for {transactionType.ToString()} and {iso4217Code}.");

        }

        PaymentProcessor IndexedProcessors.SearchBy(TransactionType transactionType, string iso4217Code, Type processorDriverType)
        {

            Coin coin = Coinage.Coin(iso4217Code);

            if (procesor.UseDriver(processorDriverType) && procesor.Visible && procesor.Enabled &&
                procesor.Transactions.MatchWith(transactionType.ToString(), processorDriverType) &&
                procesor.Coin == coin)
                return procesor;

            throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured  for {transactionType.ToString()} and {iso4217Code}.");

        }

        IEnumerable<PaymentProcessor> IndexedProcessors.SearchBy(TransactionType transactionType, int entityId, int paymentMethodId, int coinId)
        {
            if (entityId <= 0) throw new GameEngineException($"{nameof(entityId)} '{entityId}' must be greater than 0");
            if (paymentMethodId <= 0) throw new GameEngineException($"{nameof(paymentMethodId)} '{paymentMethodId}' must be greater than 0");
            if (coinId < 0) throw new GameEngineException($"{nameof(coinId)} '{coinId}' must be greater than 0");

            if (procesor.Visible && procesor.Enabled &&
                procesor.Entity.Id == entityId &&
                procesor.Group.Id == paymentMethodId &&
                procesor.Transactions.FirstOrDefault(x => x.Name == transactionType.ToString()) != null &&
                procesor.ProcessorCoin.Id == coinId)
                return Enumerable.Empty<PaymentProcessor>().Append(procesor);

            throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured for {transactionType}, {nameof(entityId)} {entityId}, {nameof(paymentMethodId)} {paymentMethodId} and {nameof(coinId)} {coinId}.");
        }

        PaymentProcessor IndexedProcessors.SearchBy(Tenant_Actions action)
        {
            if (procesor.Driver is TenantDriver && (procesor.Driver as TenantDriver).TenantAction == action && procesor.Visible && procesor.Enabled)
                return procesor;

            throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured for {action}");
        }

        IEnumerable<PaymentProcessor> IndexedProcessors.SearchProcessorsBy(Tenant_Actions action)
        {
            if (procesor.Driver is TenantDriver && (procesor.Driver as TenantDriver).TenantAction == action && procesor.Visible && procesor.Enabled)
                return Enumerable.Empty<PaymentProcessor>().Append(procesor);

            throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured for {action}");
        }

        PaymentProcessor IndexedProcessors.SearchBy(Type processorDriverType)
        {
            if (procesor.UseDriver(processorDriverType) && procesor.Visible && procesor.Enabled)
                return procesor;

            throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured for {processorDriverType}");
        }

        PaymentProcessor IndexedProcessors.FirstOne()
        {
            if (procesor.Visible && procesor.Enabled)
                return procesor;

            throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured.");
        }

        PaymentProcessor IndexedProcessors.SearchBy(Func<PaymentProcessor, bool> filter)
        {
            var result = filter(procesor);
            if (result ) return procesor;

            throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured for that filter.");
        }
    }

    internal class EntityOrderComparer : IComparer<PaymentProcessor>
    {
        private List<int> order;

        public EntityOrderComparer(List<int> order)
        {
            this.order = order;
        }

        public int Compare(PaymentProcessor x, PaymentProcessor y)
        {
            int xIndex = order.IndexOf(x.Entity.Id);
            int yIndex = order.IndexOf(y.Entity.Id);

            return xIndex.CompareTo(yIndex);
        }
    }

    [Puppet]
    internal sealed class MultipleProcessors : Objeto, IEnumerable<PaymentProcessor>, IndexedProcessors
    {
        private readonly List<PaymentProcessor> processors = new List<PaymentProcessor>();

        internal void ReorderEntities(ProcessorTransaction transactionType, Coin coin, List<int> entityIdsToReorder)
        {
            var processorsToReorder = new List<PaymentProcessor>();
            var processorsToRemove = processors.Where(processor => processor.ContainsTransactionType(transactionType) && processor.Coin == coin && entityIdsToReorder.Contains(processor.Entity.Id));
            if (!processorsToRemove.Any()) throw new GameEngineException($"There are no processors matching with {transactionType} {coin} and entities {string.Join(',', entityIdsToReorder)}");
            if (processorsToRemove.Count() == 1) return;

            for (int index = processors.Count - 1; index >= 0; index--)
            {
                if (processorsToRemove.Contains(processors[index]))
                {
                    processorsToReorder.Add(processors[index]);
                    processors.Remove(processors[index]);
                }
            }
            processorsToReorder.Sort(new EntityOrderComparer(entityIdsToReorder));
            foreach (var processor in processorsToReorder)
            {
                processors.Add(processor);
            }
        }

        public IEnumerator<PaymentProcessor> GetEnumerator()
        {
            return this.processors.GetEnumerator();
        }

        IEnumerator IEnumerable.GetEnumerator()
        {
            return this.processors.GetEnumerator();
        }

        internal void Addon(PaymentProcessor processor)
        {
            this.processors.Add(processor);
        }

        void IndexedProcessors.Addon(PaymentProcessor processor)
        {
            this.processors.Add(processor);
        }

        bool IndexedProcessors.IsACollectionNeeded()
        {
            return false;
        }

        bool IndexedProcessors.IsEmpty()
        {
            return false;
        }

        MultipleProcessors IndexedProcessors.ChangeToMultipleIndexProcessor()
        {
            return this;
        }

        internal IEnumerable<PaymentProcessor> SearchByCoin(Coin coin)
        {
            var result = this.processors.Where(x => x.Visible && x.Enabled && x.Coin == coin);

            if (result == null) throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured  for {coin.Iso4217Code}.");

            return result;
        }

        PaymentProcessor IndexedProcessors.SearchBy(string iso4217Code)
        {
            Coin coin = Coinage.Coin(iso4217Code);

            var result = this.processors.FirstOrDefault(x => x.Visible && x.Enabled &&
            x.Coin == coin);

            if (result == null) throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured  for {iso4217Code}.");

            return result;
        }

        internal PaymentProcessor SearchByTransactionType(TransactionType transactionType, string iso4217Code)
        {
            return SearchBy(transactionType, iso4217Code);
        }

        PaymentProcessor SearchBy(TransactionType transactionType, string iso4217Code)
        {
            Coin coin = Coinage.Coin(iso4217Code);

            var result = this.processors.FirstOrDefault(x => x.Visible && x.Enabled &&
            x.Transactions.FirstOrDefault(x => x.Name == transactionType.ToString()) != null &&
            x.Coin == coin);

            if (result == null) throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured  for {transactionType.ToString()} and {iso4217Code}.");

            return result;
        }

        internal PaymentProcessor SearchBy(ProcessorTransaction transaction, Coin coin, PaymentMethod paymentMethod, int entityId)
        {
            if (coin == null) throw new ArgumentNullException(nameof(coin));
            if (transaction == null) throw new ArgumentNullException(nameof(transaction));
            if (entityId <= 0) throw new GameEngineException($"{nameof(entityId)} '{entityId}' must be greater than 0");

            var result = this.processors.FirstOrDefault(x => x.Visible && x.Enabled &&
            x.Transactions.Contains(transaction) &&
            x.Coin == coin && 
            x.PaymentMethodType == paymentMethod && 
            x.Entity.Id == entityId &&
            x.Driver is ProcessorDriver);

            if (result == null) throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured for {nameof(entityId)} {entityId}, {nameof(paymentMethod)} {paymentMethod}, {nameof(transaction)} {transaction.Name} and {nameof(coin)} {coin.Iso4217Code}.");

            return result;
        }

        internal IEnumerable<PaymentProcessor> SearchBy(ProcessorTransaction transaction, int entityId, int paymentMethodId, int coinId)
        {
            if (transaction == null) throw new ArgumentNullException(nameof(transaction));
            if (entityId <= 0) throw new GameEngineException($"{nameof(entityId)} '{entityId}' must be greater than 0");
            if (paymentMethodId <= 0) throw new GameEngineException($"{nameof(paymentMethodId)} '{paymentMethodId}' must be greater than 0");
            if (coinId < 0) throw new GameEngineException($"{nameof(coinId)} '{coinId}' must be greater than 0");

            var result = this.processors.Where(processor => processor.Visible && 
                                                            processor.Transactions.Contains(transaction) &&
                                                            processor.ProcessorCoin.Id == coinId &&
                                                            processor.Group.Id == paymentMethodId &&
                                                            processor.Entity.Id == entityId &&
                                                            processor.Driver is ProcessorDriver);

            if (result == null || !result.Any()) throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured for {nameof(entityId)} {entityId}, {nameof(paymentMethodId)} {paymentMethodId}, {nameof(transaction)} {transaction.Name} and {nameof(coinId)} {coinId}.");

            return result;
        }

        internal PaymentProcessor SearchBySpecificClass(TransactionType transactionType, string iso4217Code, Type processorDriverType)
        {
            return SearchBy(transactionType, iso4217Code, processorDriverType);
        }

        PaymentProcessor SearchBy(TransactionType transactionType, string iso4217Code, Type processorDriverType)
        {

            Coin coin = Coinage.Coin(iso4217Code);

            var result = this.processors.FirstOrDefault(x => x.UseDriver(processorDriverType) && x.Visible && x.Enabled &&
                                                            x.Transactions.MatchWith(transactionType.ToString(), processorDriverType) &&
                                                            x.Coin == coin);

            if (result == null) throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured  for {transactionType.ToString()} and {iso4217Code}.");

            return result;
        }

        IEnumerable<PaymentProcessor> IndexedProcessors.SearchBy(TransactionType transactionType, int entityId, int paymentMethodId, int coinId)
        {
            if (entityId <= 0) throw new GameEngineException($"{nameof(entityId)} '{entityId}' must be greater than 0");
            if (paymentMethodId <= 0) throw new GameEngineException($"{nameof(paymentMethodId)} '{paymentMethodId}' must be greater than 0");
            if (coinId < 0) throw new GameEngineException($"{nameof(coinId)} '{coinId}' must be greater than 0");

            var result = this.processors.Where(processor => processor.Visible &&
                                                            processor.Entity.Id == entityId &&
                                                            processor.Group.Id == paymentMethodId &&
                                                            processor.Transactions.FirstOrDefault(x => x.Name == transactionType.ToString()) != null &&
                                                            processor.ProcessorCoin.Id == coinId &&
                                                            processor.Driver is ProcessorDriver);

            if (result == null || !result.Any()) throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured for {transactionType}, {nameof(entityId)} {entityId}, {nameof(paymentMethodId)} {paymentMethodId} and {nameof(coinId)} {coinId}.");

            return result;
        }

        internal PaymentProcessor SearchBySpecificClass(Tenant_Actions actions)
        {
            return SearchBy(actions);
        }

        PaymentProcessor SearchBy(Tenant_Actions action)
        {
            var result = this.processors.FirstOrDefault(x => x.Driver is TenantDriver && (x.Driver as TenantDriver).TenantAction == action && x.Visible && x.Enabled);

            if (result == null) throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured for {action}");

            return result;
        }

        IEnumerable<PaymentProcessor> IndexedProcessors.SearchProcessorsBy(Tenant_Actions action)
        {
            var result = this.processors.Where(x => x.Driver is TenantDriver && (x.Driver as TenantDriver).TenantAction == action && x.Visible && x.Enabled);

            if (result == null) throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured for {action}");

            return result;
        }

        PaymentProcessor IndexedProcessors.SearchBy(Type processorDriverType)
        {
            var result = this.processors.FirstOrDefault(x => x.UseDriver(processorDriverType) && x.Visible && x.Enabled);

            if (result == null) throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured for {processorDriverType}");

            return result;
        }

        PaymentProcessor IndexedProcessors.FirstOne()
        {
            var result = this.processors.FirstOrDefault(x => x.Visible && x.Enabled);

            if (result == null) throw new GameEngineException($"There is no {nameof(PaymentProcessor)}.");

            return result;
        }

        internal PaymentProcessor FirstOne()
        {
            var result = this.processors.FirstOrDefault(x => x.Visible && x.Enabled);

            if (result == null) throw new GameEngineException($"There is no {nameof(PaymentProcessor)}.");

            return result;
        }

        internal bool ThereIsOnlyOne()
        {
            return processors.Count == 1;
        }

        PaymentProcessor IndexedProcessors.SearchBy(Func<PaymentProcessor, bool> filter)
        {
            foreach (var procesor in this.processors)
            {
                var result = filter(procesor);
                if (result) return procesor;
            }

            throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured for that filter.");
        }

        PaymentProcessor IndexedProcessors.SearchBy(TransactionType transactionType, string iso4217Code)
        {
            return SearchBy(transactionType, iso4217Code);
        }

        PaymentProcessor IndexedProcessors.SearchBy(TransactionType transactionType, string iso4217Code, Type processorDriverType)
        {
            return SearchBy(transactionType, iso4217Code, processorDriverType);
        }

        PaymentProcessor IndexedProcessors.SearchBy(Tenant_Actions action)
        {
            return SearchBy(action);
        }

        internal void Clear()
        {
            processors.Clear();
        }
    }
}
