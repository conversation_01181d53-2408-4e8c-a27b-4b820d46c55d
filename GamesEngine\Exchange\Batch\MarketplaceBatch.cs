﻿using GamesEngine.Finance;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Text;

namespace GamesEngine.Exchange.Batch
{
	[Puppet]
	internal class MarketplaceBatch : BatchSet
	{
		private readonly DateTime creationDate;
		private readonly DateTime scheduledDate;
		private readonly string name;
		private readonly string description;
		private string createdBy;
		private readonly int batchNumber;
		private int level;
		private DateTime openedDate;
		private readonly MarketplaceBatches marketplaceBatches;
		private Marketplace marketplace;

		internal MarketplaceBatch(MarketplaceBatches marketplaceBatches, Marketplace marketplace, int batchNumber, DateTime creationDate, DateTime scheduledDate, string description, string createdBy) : base(marketplace, creationDate)
		{
			if (marketplace == null) throw new ArgumentNullException(nameof(marketplace));
			if (batchNumber <= 0) throw new GameEngineException($"Batch number {batchNumber} is invalid to define a Marketplace batch.");
			if (creationDate == default(DateTime)) throw new ArgumentNullException(nameof(creationDate));
			if (scheduledDate == default(DateTime)) throw new ArgumentNullException(nameof(scheduledDate));
			if (string.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(description);
			if (string.IsNullOrWhiteSpace(createdBy)) throw new ArgumentNullException(createdBy);

			this.creationDate = creationDate;
			this.batchNumber = batchNumber;
			this.scheduledDate = scheduledDate;
			this.name = marketplace.Name;
			this.marketplace = marketplace;
			this.description = description;
			this.createdBy = createdBy;
			this.level = 1;
			this.consecutive++;
			this.marketplaceBatches = marketplaceBatches;
		}

		internal void InitialAmount(Currency amount, string employeeName)
		{
			if (this.marketplace.InitialAccountItsNotConfiguredFor(amount.Coin)) throw new GameEngineException($"There are no {nameof(SetOfBalances)} for the marketplace yet.");
			if (outTransaction.Available(amount.Coin).Value > 0) throw new GameEngineException("Marketplace batch already has funds.");
			if (amount.Value <= 0) throw new GameEngineException("Amount to receive can not be negative or zero");

			if (InitialAccountItsNotConfiguredFor()) LoadNewInitialAccounts( new SetOfBalances(this.Marketplace.RealAccounts));

			outTransaction.InitialAmount(amount, employeeName);
			outTransaction.Deposit(amount, employeeName);
		}

		internal IEnumerable<BatchAccount> InitialAccounts()
		{
			List<BatchAccount> result = new List<BatchAccount>();
			result.AddRange(outTransaction.Accounts());
			return result;
		}

		internal bool HasAvailableAmountInAnyCurrency
		{
			get
			{
				return this.outTransaction.HasAvailableAmountInAnyCurrency;
			}
		}

		internal override string FullName
		{
			get
			{
				return this.name;
			}
			
		}

		internal override int Level
		{
			get
			{
				return this.level;
			}

			set
			{
				this.level = value;
			}
		}

		internal int BatchNumber
		{
			get
			{
				return this.batchNumber;
			}
		}

		internal DateTime CreationDate
		{
			get
			{
				return this.creationDate;
			}
		}

		internal DateTime ScheduledDate
		{
			get
			{
				return this.scheduledDate;
			}
		}

		internal string CreatedBy
		{
			get
			{
				return this.createdBy;
			}
		}

		internal string Description
		{
			get
			{
				return this.description;
			}
		}

		internal OutBalances InitialTransaction
		{
			get
			{
				return this.outTransaction;
			}
		}

		internal override void ReceiveAmount(Currency amount, Currency cost, string employeeName)
		{
			throw new GameEngineException($"There is no possible to receive money for {nameof(MarketplaceBatch)}");
		}

		internal static void TailOFFullName(string fullName, out string current, out string tail)
		{
			int indexSeparator = fullName.IndexOf(AGENT_SEPARATOR);
			
			current = "";
			tail = "";

			if (indexSeparator == -1)
			{
				current = fullName;
			}
			else
			{
				tail = fullName.Substring(indexSeparator + 1);
				current = fullName.Substring(0, indexSeparator);
			}
		}

		internal override void ApplyBalancesModifications(BatchMovements draftMovements)
		{
			foreach (BatchMovement movement in draftMovements.List())
			{
				if (movement.BalanceType == Balances.Types.NONE)
				{
					continue;
				}
				else if (movement.BalanceType == Balances.Types.IN)
				{
					ApplyInBalancesModifications(movement);
				}
				else if (movement.BalanceType == Balances.Types.OUT)
				{
					ApplyOutBalancesModifications(movement);
				}
				else
				{
					throw new GameEngineException($"There is no valid implementation for {movement.BalanceType} in {nameof(ApplyBalancesModifications)}");
				}
			}
		}

		internal override void Open(bool itIsThePresent, DateTime openDate, string employee)
		{
			if (InitialAccountItsNotConfiguredFor()) throw new GameEngineException($"There are no {nameof(SetOfBalances)} for the marketplace yet.");

			base.OpenCurrentBatchSet(itIsThePresent, openDate);
			
			marketplaceBatches.OpenMarketplaceBatch(this);

			InitialAccount.TakeFirstSnap();
		}

		internal override void Close(bool itIsThePresent, DateTime closeDate, string employee)
		{
			base.CloseCurrentBatchSet(itIsThePresent, closeDate);
			marketplaceBatches.CloseMarketplaceBatch(this);

			InitialAccount.TakeSecondSnap();
		}

		internal override void Verify(bool itIsThePresent, DateTime verifyDate, string employee)
		{
			base.VerifyCurrentBatchSet(itIsThePresent, verifyDate);
			marketplaceBatches.ConfirmClosingMarketplaceBatch(this);
		}
	}
}
