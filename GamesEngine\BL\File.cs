﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;

namespace Resources.BL
{
    [Puppet]
    internal class File:Objeto
    {
        private readonly string extension;
        private string uniqueName;
        private readonly string originalFileName;
        private readonly string pathWithoutFileName;

        internal string Extension
        {
            get { return extension; }
        }

        internal string OriginalFileName
        {
            get { return originalFileName; }
        }

        internal string PathWithoutFileName
        {
            get { return pathWithoutFileName; }
        }

        internal string FileName
        {
            get { return this.originalFileName; }
        }

        internal string UniqueName
        {
            get { return uniqueName; }
            set { uniqueName=value; }
        }

        FileKey fileKey;
        internal string ResourceOwner => fileKey.ResourceOwner;

        internal File(FileKey fileKey)
        {
            if (fileKey == null) throw new ArgumentNullException(nameof(fileKey));
            if (String.IsNullOrWhiteSpace(fileKey.VirtualPath)) throw new ArgumentNullException(nameof(fileKey.VirtualPath));

            var extension = Path.GetExtension(fileKey.VirtualPath);
            var fileName = Path.GetFileName(fileKey.VirtualPath);
            pathWithoutFileName = fileKey.VirtualPath.Replace(fileName,"");

            if (String.IsNullOrWhiteSpace(extension)) throw new ArgumentNullException(nameof(extension));
            if (String.IsNullOrWhiteSpace(fileName)) throw new ArgumentNullException(nameof(fileName));

            this.fileKey = fileKey;
            this.extension = extension;
            this.originalFileName = fileName;
            this.uniqueName = fileKey.GetNameInDisk();
        }

        internal string FullPath()
        {
            var fullPath = pathWithoutFileName + uniqueName + extension;
            return fullPath;
        }

    }
}
