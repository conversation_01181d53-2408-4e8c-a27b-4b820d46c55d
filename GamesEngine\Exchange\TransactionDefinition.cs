﻿using GamesEngine.Exchange.Batch;
using GamesEngine.Finance;
using GamesEngine.Messaging;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using town.connectors.drivers;
using static GamesEngine.Business.WholePaymentProcessor;
using static GamesEngine.Finance.Currencies;
using static town.connectors.drivers.Result;

namespace GamesEngine.Exchange
{
	internal sealed class TransactionDefinition : Objeto
	{
		private readonly int id;
		private readonly Domains.Domain domain;
		private static int idsSequence = 0;
		private readonly CustomerAccount account;
		private readonly Marketplace marketplace;
		private BatchTransactions batch;

		public CustomerAccount Account { get { return account; } }
		public BatchTransactions Batch { get { return batch; } }

		internal Marketplace Marketplace
		{
			get
			{
				return this.marketplace;
			}
		}

		public int Id { get { return id; } }
		internal Domains.Domain Domain { get { return domain; } }
		internal Agent AssignedAgent { get; }

		internal TransactionDefinition(int id, Marketplace marketplace, CustomerAccount account, BatchTransactions batch, string agentPath, Domains.Domain domain)
		{
			if (account == null) throw new GameEngineException(nameof(account));
			if (marketplace == null) throw new GameEngineException(nameof(marketplace));
			if (batch == null) throw new GameEngineException(nameof(batch));

			this.account = account;
			this.marketplace = marketplace;
			this.id = id;
			idsSequence = id;
			this.batch = batch;
			this.domain = domain;

			batch.AddTemporaryDefinition(this);
			AssignedAgent = (Agent)marketplace.SearchAgent(agentPath);
		}

		internal Store Store
		{
			get
			{
				return this.marketplace.Company.Sales.CurrentStore;
			}
		}

		internal SaleTransaction Sale(DateTime date, bool itsThePresent, Currency amount, string employeeName)
		{
			if (this.batch.Available(amount.Coin).Value < amount.Value) throw new GameEngineException($"It's not possible to make a sale, because there's no exist funds in {amount.CurrencyCode}");

			IConversionSpread conversionSpreads = marketplace.SearchSpreadFor(date, amount.Coin, account.Coin);
			
			SaleTransaction result = new SaleTransaction(this, conversionSpreads, account, employeeName, amount, date);

			Batch.AddToDraftTransactions(result);
			batch.RemoveTemporaryDefinition(this);
			return result;
		}

		internal DepositTransaction Deposit(DateTime today, bool itsThePresent, Currency amount, string employeeName, PaymentProcessor processor, int processorAccountId)
		{
			string noDepositor = "";
			string noVoucher = "";
			string noVoucherurl = "";
			string noConcept = "";
			return Deposit(today, itsThePresent, amount, employeeName, noDepositor, noVoucher, noVoucherurl, noConcept, processor, processorAccountId);
		}

		internal DepositTransaction Deposit(DateTime today, bool itsThePresent, IConversionSpread conversionSpreads, Currency amount, string employeeName, PaymentProcessor processor, int processorAccountId)
		{
			string noDepositor = "";
			string noVoucher = "";
			string noVoucherurl = "";
			string noConcept = "";
			return Deposit(today, itsThePresent, amount, employeeName, noDepositor, noVoucher, noVoucherurl, noConcept, processor, processorAccountId, conversionSpreads);
		}

		internal string OutAccountFor(Coin currencyCode)
		{
			return Batch.OutAccountFor(currencyCode);
		}

		internal DepositTransaction Deposit(DateTime today, bool itsThePresent, Currency amount, string employeeName, string depositor, string voucher, string voucherurl, string concept, PaymentProcessor processor, int processorAccountId)
		{
			if (amount ==null) throw new ArgumentNullException(nameof(amount));
			if (string.IsNullOrEmpty(employeeName)) throw new ArgumentNullException(nameof(employeeName));
			if(today == default(DateTime)) throw new ArgumentNullException(nameof(today));
			if (amount.CurrencyCode != account.CurrencyCode && ! this.batch.HasAvailable(amount)) throw new GameEngineException($"It's not possible to make a deposit, because there's no exist funds in {amount.CurrencyCode}");
			if (processor == null) throw new ArgumentNullException(nameof(processor));
			if (processorAccountId <= 0) throw new GameEngineException($"{nameof(processorAccountId)} must be greater than 0");

			IConversionSpread conversionSpreads  = marketplace.SearchSpreadFor(today, amount.Coin, account.Coin);

			return Deposit(
				today, 
				itsThePresent,
				amount, 
				employeeName,
				depositor, 
				voucher, 
				voucherurl,
				concept, 
				processor, 
				processorAccountId,
				conversionSpreads);

		}

		private DepositTransaction Deposit(DateTime today, bool itsThePresent, Currency amount, string employeeName, string depositor, string voucher, string voucherurl, string concept, PaymentProcessor processor, 
			int processorAccountId, IConversionSpread conversionSpreads)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));
			if (string.IsNullOrEmpty(employeeName)) throw new ArgumentNullException(nameof(employeeName));
			if (today == default(DateTime)) throw new ArgumentNullException(nameof(today));
			if (amount.CurrencyCode != account.CurrencyCode && !this.batch.HasAvailable(amount)) throw new GameEngineException($"It's not possible to make a deposit, because there's no exist funds in {amount.CurrencyCode}");
			if (processor == null) throw new ArgumentNullException(nameof(processor));
			if (processorAccountId <= 0) throw new GameEngineException($"{nameof(processorAccountId)} must be greater than 0");

			DepositTransaction result = new DepositTransaction(this, conversionSpreads, employeeName, amount, today, processor);
			result.Processor = processor;
			result.ProcessorAccountId = processorAccountId;
			result.Depositor = depositor;
			result.Voucher = voucher;
			result.VoucherUrl = voucherurl;
			Batch.AddToDraftTransactions(result);
			batch.RemoveTemporaryDefinition(this);

			if (itsThePresent)
			{
				Currency gross = null, comission = null, net = null, profit = null;
				Currency amountToCustomer = result.CalculateAmounts(out gross, out comission, out net, out profit);
				if (Integration.UseKafka || Integration.UseKafkaForAuto)
				{
					Integration.Kafka.Send(
						itsThePresent,
						$"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForTransacctions}",
						new DraftDepositMessage(Id, today, amount.Coin, Account.CustomerIdentifier, amountToCustomer.Value, concept, employeeName, account.Identificator, Batch.TransactionsNumber, conversionSpreads.Id,
							Account.Coin, gross.Value, net.Value, profit.Value, depositor, voucher, voucherurl, Domain.Url, processorAccountId)
					);
				}
			}

			return result;
		}

		internal WithdrawalTransaction Withdraw(DateTime today, bool itsThePresent, Currency amount, int authorizationId, string employeeName, string realAccount, string concept, TransactionFee fee, 
			PaymentProcessor processor, int processorAccountId)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));
			if (string.IsNullOrEmpty(employeeName)) throw new ArgumentNullException(nameof(employeeName));
			if (realAccount.Length > 34) throw new GameEngineException("IBAN and Wallets accounts are 34 length caracters.");
			if (today == default(DateTime)) throw new ArgumentNullException(nameof(today));
			if (fee == null) throw new ArgumentNullException(nameof(fee));
			if (processor == null) throw new ArgumentNullException(nameof(processor));
			if (processorAccountId <= 0) throw new GameEngineException($"{nameof(processorAccountId)} must be greater than 0");

			IConversionSpread conversionSpreads = marketplace.SearchSpreadFor(today, amount.Coin, account.Coin);
			
			WithdrawalTransaction result = new WithdrawalTransaction(this, itsThePresent, conversionSpreads, authorizationId, employeeName, amount, today, realAccount, fee, marketplace.Company.Sales.CurrentStore.Id, processor);
			result.Processor = processor;
			result.ProcessorAccountId = processorAccountId;
			Batch.AddToDraftTransactions(result);
			batch.RemoveTemporaryDefinition(this);

			if (itsThePresent)
			{
				Currency gross = null, comission = null, net = null, profit = null, disbursement = null;
				Currency amountToCustomer = result.CalculateAmounts(out gross, out comission, out net, out profit, out disbursement);
				if (Integration.UseKafka || Integration.UseKafkaForAuto)
				{
					Integration.Kafka.Send(
						itsThePresent,
						$"{ KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX }_{Integration.Kafka.TopicForTransacctions}",
						new DraftWithdrawalMessage(Id, today, Account.Coin, Account.CustomerIdentifier, amountToCustomer.Value, concept, employeeName, account.Identificator, Batch.TransactionsNumber, conversionSpreads.Id, 
							amount.Coin, gross.Value, net.Value, profit.Value, authorizationId, realAccount, fee.Value, Domain.Url, processorAccountId)
					);
				}
			}


			return result;
		}

		internal TransferTransaction TransferTo(DateTime today, bool itsThePresent, Currency amount, int authorizationId, CustomerAccount toAccount, string employeeName, PaymentProcessor processor, int processorAccountId)
		{
			string noRealAccount = "";
			string noConcept = "";
			return TransferTo(today, itsThePresent, amount, authorizationId, toAccount, employeeName, noRealAccount, noConcept, processor, processorAccountId);
		}
		internal TransferTransaction TransferTo(DateTime today, bool itsThePresent, Currency amount, int authorizationId, CustomerAccount toAccount, string employeeName, string realAccount, string concept, 
			PaymentProcessor processor, int processorAccountId)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));
			if (toAccount == null) throw new ArgumentNullException(nameof(toAccount));
			if (string.IsNullOrEmpty(employeeName)) throw new ArgumentNullException(nameof(employeeName));
			if (realAccount.Length > 34) throw new GameEngineException("IBAN and Wallets accounts are 34 length caracters.");
			if (account.CurrencyCode != amount.CurrencyCode) throw new GameEngineException($"It's not possible take {account} from a {account.CurrencyCode} account.");
			if (today == default(DateTime)) throw new ArgumentNullException(nameof(today));
			if (! this.batch.HasAvailable(amount)) throw new GameEngineException($"It's not possible to make a transfer, because there's no exist funds in {amount.CurrencyCode}");
			if (processor == null) throw new ArgumentNullException(nameof(processor));
			if (processorAccountId <= 0) throw new GameEngineException($"{nameof(processorAccountId)} must be greater than 0");

			IConversionSpread conversionSpreads = marketplace.SearchSpreadFor(today, account.Coin, toAccount.Coin);
			
			TransferTransaction result = new TransferTransaction(this, itsThePresent, conversionSpreads, toAccount, authorizationId, employeeName, amount, today);
			result.Processor = processor;
			result.ProcessorAccountId = processorAccountId;
			Batch.AddToDraftTransactions(result);
			batch.RemoveTemporaryDefinition(this);

			if (itsThePresent)
			{
				if (Integration.UseKafka || Integration.UseKafkaForAuto)
				{
					Currency gross = null, comission = null, net = null, profit = null;
					Currency amountToCustomer = result.CalculateAmounts(out gross, out comission, out net, out profit);
					Integration.Kafka.Send(
						itsThePresent,
						$"{ KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX }_{Integration.Kafka.TopicForTransacctions}",
						new DraftTransferMessage(Id, today, Account.Coin, Account.CustomerIdentifier, amountToCustomer.Value, concept, employeeName, account.Identificator, Batch.TransactionsNumber, conversionSpreads.Id, 
							toAccount.Coin, gross.Value, net.Value, profit.Value, authorizationId, realAccount, toAccount.Identificator, toAccount.CustomerIdentifier, Domain.Url)
					);
				}
			}

			return result;
		}

		internal TransferTransaction TransferTo(DateTime today, bool itsThePresent, Currency amount, int authorizationId, Customer targetCustomer, string employeeName, PaymentProcessor processor, int processorAccountId)
		{
			CustomerAccount accountNumber = targetCustomer.FindAccountByCurrency(amount.Coin);

			return TransferTo(today, itsThePresent, amount, authorizationId, accountNumber, employeeName, processor, processorAccountId);
		}

		internal CreditNoteTransaction CreditNote(DateTime today, bool itsThePresent, Currency amount, string employeeName, string concept, int referenceId, int batchNumber, PaymentProcessor processor, int processorAccountId)
		{
			return CreditNote(today, itsThePresent, amount, employeeName, concept, referenceId, batchNumber, processor, processorAccountId, null);
		}

		internal CreditNoteTransaction CreditNote(DateTime today, bool itsThePresent, Currency amount, string employeeName, string concept, int referenceId, int batchNumber, PaymentProcessor processor, int processorAccountId, List<string> attachmentUrls)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));
			if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));
			if (string.IsNullOrEmpty(concept)) throw new ArgumentNullException(nameof(concept));
			if (referenceId <= 0) throw new GameEngineException($"{nameof(referenceId)} must be greater than 0");
			if (account.CurrencyCode != amount.CurrencyCode) throw new GameEngineException($"It's not possible take {amount.Value} from a {account.CurrencyCode} account.");
			if (processor == null) throw new ArgumentNullException(nameof(processor));
			if (processorAccountId <= 0) throw new GameEngineException($"{nameof(processorAccountId)} must be greater than 0");

			IConversionSpread conversionSpreads = marketplace.SearchSpreadFor(today, amount.Coin, account.Coin);
			var result = new CreditNoteTransaction(this, conversionSpreads, employeeName, amount, today);
			result.Processor = processor;
			result.ProcessorAccountId = processorAccountId;
			Batch.AddToDraftTransactions(result);
			batch.RemoveTemporaryDefinition(this);

			if ((itsThePresent && Integration.UseKafka) || Integration.UseKafkaForAuto)
			{
				var message = new DraftCreditNoteMessage(Id, today, Account.Coin, Account.CustomerIdentifier, amount.Value, concept, employeeName, account.Identificator, batchNumber, referenceId, Domain.Url);
				var topic = $"{ KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX }_{Integration.Kafka.TopicForTransacctions}";
				var noAttachments = attachmentUrls == null || attachmentUrls.Count == 0;
				if (noAttachments)
				{
					Integration.Kafka.Send(itsThePresent, topic, message);
				}
				else
				{
					Action<KafkaMessages> beforeSend = (KafkaMessages kafkaMessages) =>
					{
						kafkaMessages.Add(message.Serialize());
					};
					using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(true, topic))
					{
						buffer.BeforeSendTheFirstMessage = beforeSend;
						foreach (var url in attachmentUrls)
						{
							if (string.IsNullOrWhiteSpace(url)) throw new ArgumentNullException(nameof(url));
							if ( ! Uri.IsWellFormedUriString(url, UriKind.RelativeOrAbsolute)) throw new GameEngineException($"{nameof(url)} '{url}' is not valid");

							var attachmentMessage = new DraftAttachmentMessage(new UriBuilder(url).Uri);
							buffer.Send(attachmentMessage);
						}
					}
				}
			}

			return result;
		}

		internal DebitNoteTransaction DebitNote(DateTime today, bool itsThePresent, Currency amount, string employeeName, string concept, int referenceId, int batchNumber, PaymentProcessor processor, int processorAccountId)
		{
			return DebitNote(today, itsThePresent, amount, employeeName, concept, referenceId, batchNumber, processor, processorAccountId, null);
		}

		internal DebitNoteTransaction DebitNote(DateTime today, bool itsThePresent, Currency amount, string employeeName, string concept, int referenceId, int batchNumber, PaymentProcessor processor, int processorAccountId, List<string> attachmentUrls)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));
			if (string.IsNullOrEmpty(employeeName)) throw new ArgumentNullException(nameof(employeeName));
			if (string.IsNullOrEmpty(concept)) throw new ArgumentNullException(nameof(concept));
			if (referenceId <= 0) throw new GameEngineException($"{nameof(referenceId)} must be greater than 0");
			if (account.CurrencyCode != amount.CurrencyCode) throw new GameEngineException($"It's not possible take {amount.Value} from a {account.CurrencyCode} account.");
			if (processor == null) throw new ArgumentNullException(nameof(processor));
			if (processorAccountId <= 0) throw new GameEngineException($"{nameof(processorAccountId)} must be greater than 0");

			IConversionSpread conversionSpreads = marketplace.SearchSpreadFor(today, amount.Coin, account.Coin);
			var result = new DebitNoteTransaction(this, conversionSpreads, employeeName, amount, today);
			result.Processor = processor;
			result.ProcessorAccountId = processorAccountId;
			Batch.AddToDraftTransactions(result);
			batch.RemoveTemporaryDefinition(this);

			if ((itsThePresent && Integration.UseKafka) || Integration.UseKafkaForAuto)
			{
				var message = new DraftDebitNoteMessage(Id, today, Account.Coin, Account.CustomerIdentifier, amount.Value, concept, employeeName, account.Identificator, batchNumber, referenceId, Domain.Url);
				var topic = $"{ KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX }_{Integration.Kafka.TopicForTransacctions}";
				var noAttachments = attachmentUrls == null || attachmentUrls.Count == 0;
				if (noAttachments)
				{
					Integration.Kafka.Send(itsThePresent, topic, message);
				}
				else
				{
					Action<KafkaMessages> beforeSend = (KafkaMessages kafkaMessages) =>
					{
						kafkaMessages.Add(message.Serialize());
					};
					using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(true, topic))
					{
						buffer.BeforeSendTheFirstMessage = beforeSend;
						foreach (var url in attachmentUrls)
						{
							if (string.IsNullOrWhiteSpace(url)) throw new ArgumentNullException(nameof(url));
							if ( ! Uri.IsWellFormedUriString(url, UriKind.RelativeOrAbsolute)) throw new GameEngineException($"{nameof(url)} '{url}' is not valid");

							var attachmentMessage = new DraftAttachmentMessage(new UriBuilder(url).Uri);
							buffer.Send(attachmentMessage);
						}
					}
				}
			}

			return result;
		}

		internal void Deny(Transaction transaction)
		{
			Batch.Remove(transaction);
		}

		internal void Remove()
		{
			Batch.RemoveDefinition(this);
		}

		internal Customer FindOwner()
		{
			return marketplace.FindOwner(this);
		}

		internal void SetJournalEntryId(int consecutive)
		{
			marketplace.SetJournalEntryId(consecutive);
		}
	}

	public class DraftTransactionMessage : HeaderExchangeTransactionMessage
	{
		public const string EMPTY_STRING = "-";
		public int Id { get; private set; }
		public DateTime Date
		{
			get
			{
				return new DateTime(DateTicks);
			}
		}
		public long DateTicks { get; private set; }
		public Coin Coin { get; private set; }
		public String CurrencyCode { get { return Coin.Iso4217Code; } }
		public string Customer { get; private set; }
		public decimal Amount { get; private set; }
		public string Concept { get; private set; }
		public string EmployeeName { get; private set; }
		public string Account { get; private set; }
		public int BatchNumber { get; private set; }

		public DraftTransactionMessage(TransactionType type, int id, DateTime date, Coin coin, string customer, decimal amount, string concept, string employeeName, string account, 
			int batchNumber) :
			base(type, TransactionStatus.DRAFT)
		{
			Id = id;
			DateTicks = date.Ticks;
			Coin = coin;
			Customer = customer;
			Amount = amount;
			Concept = string.IsNullOrWhiteSpace(concept) ? EMPTY_STRING : concept;
			EmployeeName = employeeName;
			Account = account;
			BatchNumber = batchNumber;
		}

		public DraftTransactionMessage(string serialized) : base(serialized)
		{
			
		}

		protected override void InternalSerialize()
		{
			base.InternalSerialize();
			AddProperty(Id).
			AddProperty(DateTicks).
			AddProperty(CurrencyCode).
			AddProperty(Customer).
			AddProperty(Amount).
			AddProperty(Concept).
			AddProperty(EmployeeName).
			AddProperty(Account).
			AddProperty(BatchNumber);
		}

		protected override void Deserialize(string[] message, out int fieldOrder)
		{
			base.Deserialize(message, out fieldOrder);
			Id = int.Parse(message[fieldOrder++]);
			DateTicks = long.Parse(message[fieldOrder++]);
			Coin = Coinage.KafkaProperty2Coin(message[fieldOrder++]);
			Customer = message[fieldOrder++];
			Amount = decimal.Parse(message[fieldOrder++]);
			Concept = message[fieldOrder++];
			EmployeeName = message[fieldOrder++];
			Account = message[fieldOrder++];
			BatchNumber = int.Parse(message[fieldOrder++]);
		}
	}

	public class DraftDepositMessage : DraftTransactionMessage
	{
		public long RateId { get; set; }
		public string ToCurrencyCode { get { return ToCoin.Iso4217Code; } }
		public Coin ToCoin { get; set; }
		public decimal Gross { get; set; }
		public decimal Net { get; set; }
		public decimal Profit { get; set; }
		public string Depositor { get; set; }
		public string Voucher { get; set; }
		public string VoucherUrl { get; set; }
		public string Domain { get; set; }
		public int ProcessorId { get; private set; }

		public DraftDepositMessage(int id, DateTime date, Coin currencyCode, string customer, decimal amount, string concept, string employeeName, string account, int batchNumber, long rateId, 
			Coin toCoin, decimal gross, decimal net, decimal profit, string depositor, string voucher, string voucherUrl, string domain, int processorId) :
			base(TransactionType.Deposit, id, date, currencyCode, customer, amount, concept, employeeName, account, batchNumber)
		{
			RateId = rateId;
			ToCoin = toCoin;
			Gross = gross;
			Net = net;
			Profit = profit;
			Depositor = string.IsNullOrWhiteSpace(depositor) ? EMPTY_STRING : depositor;
			Voucher = string.IsNullOrWhiteSpace(voucher) ? EMPTY_STRING : voucher;
			VoucherUrl = string.IsNullOrWhiteSpace(voucherUrl) ? EMPTY_STRING : voucherUrl;
			Domain = domain;
			ProcessorId = processorId;
		}

		public DraftDepositMessage(string serialized) : base(serialized)
		{
			
		}

		protected override void InternalSerialize()
		{
			base.InternalSerialize();
			AddProperty(RateId).
			AddProperty(ToCurrencyCode).
			AddProperty(Gross).
			AddProperty(Net).
			AddProperty(Profit).
			AddProperty(Depositor).
			AddProperty(Voucher).
			AddProperty(VoucherUrl).
			AddProperty(Domain).
			AddProperty(ProcessorId);
		}

		protected override void Deserialize(string[] message, out int fieldOrder)
		{
			base.Deserialize(message, out fieldOrder);
			RateId = long.Parse(message[fieldOrder++]);
			ToCoin = Coinage.KafkaProperty2Coin(message[fieldOrder++]);
			Gross = decimal.Parse(message[fieldOrder++]);
			Net = decimal.Parse(message[fieldOrder++]);
			Profit = decimal.Parse(message[fieldOrder++]);
			Depositor = message[fieldOrder++];
			Voucher = message[fieldOrder++];
			VoucherUrl = message[fieldOrder++];
			Domain = message[fieldOrder++];
			ProcessorId = int.Parse(message[fieldOrder++]);
		}
	}

	public class DraftWithdrawalMessage : DraftTransactionMessage
	{
		public long RateId { get; set; }
		public Coin ToCoin { get; set; }
		public string ToCurrencyCode { get { return ToCoin.Iso4217Code; } }
		public decimal Gross { get; set; }
		public decimal Net { get; set; }
		public decimal Profit { get; set; }
		public decimal MinerFee { get; set; }
		public int AuthorizationId { get; set; }
		public string RealAccount { get; set; }
		public string Domain { get; set; }
		public int ProcessorId { get; private set; }

		public DraftWithdrawalMessage(int id, DateTime date, Coin currencyCode, string customer, decimal amount, string concept, string employeeName, string account, int batchNumber, long rateId, 
			Coin toCoin, decimal gross, decimal net, decimal profit, int authorizationId, string realAccount, decimal minerFee, string domain, int processorId) :
			base(TransactionType.Withdrawal, id, date, currencyCode, customer, amount, concept, employeeName, account, batchNumber)
		{
			RateId = rateId;
			ToCoin = toCoin;
			Gross = gross;
			Net = net;
			Profit = profit;
			AuthorizationId = authorizationId;
			RealAccount = string.IsNullOrWhiteSpace(realAccount) ? EMPTY_STRING : realAccount;
			MinerFee = minerFee;
			Domain = domain;
			ProcessorId = processorId;
		}

		public DraftWithdrawalMessage(string serialized) : base(serialized)
		{
			
		}

		protected override void InternalSerialize()
		{
			base.InternalSerialize();
			AddProperty(RateId).
			AddProperty(ToCurrencyCode).
			AddProperty(Gross).
			AddProperty(Net).
			AddProperty(Profit).
			AddProperty(AuthorizationId).
			AddProperty(RealAccount).
			AddProperty(MinerFee).
			AddProperty(Domain).
			AddProperty(ProcessorId);
		}

		protected override void Deserialize(string[] message, out int fieldOrder)
		{
			base.Deserialize(message, out fieldOrder);
			RateId = long.Parse(message[fieldOrder++]);
			ToCoin =  Coinage.KafkaProperty2Coin(message[fieldOrder++]);
			Gross = decimal.Parse(message[fieldOrder++]);
			Net = decimal.Parse(message[fieldOrder++]);
			Profit = decimal.Parse(message[fieldOrder++]);
			AuthorizationId = int.Parse(message[fieldOrder++]);
			RealAccount = message[fieldOrder++];
			MinerFee = decimal.Parse(message[fieldOrder++]);
			Domain = message[fieldOrder++];
			ProcessorId = int.Parse(message[fieldOrder++]);
		}
	}

	public class DraftTransferMessage : DraftTransactionMessage
	{
		public long RateId { get; set; }
		public string ToCurrencyCode { get { return ToCoin.Iso4217Code; } }
		public Coin ToCoin { get; set; }
		public decimal Gross { get; set; }
		public decimal Net { get; set; }
		public decimal Profit { get; set; }
		public int AuthorizationId { get; set; }
		public string RealAccount { get; set; }
		public string TargetAccount { get; set; }
		public string Domain { get; set; }

		public string ToCustomer { get; set; }

		public DraftTransferMessage(int id, DateTime date, Coin currencyCode, string customer, decimal amount, string concept, string employeeName, string account, int batchNumber, long rateId, 
			Coin toCoin, decimal gross, decimal net, decimal profit, int authorizationId, string realAccount, string targetAccount, string toCustomer, string domain) :
			base(TransactionType.Transfer, id, date, currencyCode, customer, amount, concept, employeeName, account, batchNumber)
		{
			RateId = rateId;
			ToCoin = toCoin;
			Gross = gross;
			Net = net;
			Profit = profit;
			AuthorizationId = authorizationId;
			RealAccount = string.IsNullOrWhiteSpace(realAccount) ? EMPTY_STRING : realAccount;
			TargetAccount = targetAccount;
			ToCustomer = toCustomer;
			Domain = domain;
		}

		public DraftTransferMessage(string serialized) : base(serialized)
		{
			
		}

		protected override void InternalSerialize()
		{
			base.InternalSerialize();
			AddProperty(RateId).
			AddProperty(ToCurrencyCode).
			AddProperty(Gross).
			AddProperty(Net).
			AddProperty(Profit).
			AddProperty(AuthorizationId).
			AddProperty(RealAccount).
			AddProperty(TargetAccount).
			AddProperty(ToCustomer).
			AddProperty(Domain);
		}

		protected override void Deserialize(string[] message, out int fieldOrder)
		{
			base.Deserialize(message, out fieldOrder);
			RateId = long.Parse(message[fieldOrder++]);
			ToCoin = Coinage.KafkaProperty2Coin(message[fieldOrder++]);
			Gross = decimal.Parse(message[fieldOrder++]);
			Net = decimal.Parse(message[fieldOrder++]);
			Profit = decimal.Parse(message[fieldOrder++]);
			AuthorizationId = int.Parse(message[fieldOrder++]);
			RealAccount = message[fieldOrder++];
			TargetAccount = message[fieldOrder++];
			ToCustomer = message[fieldOrder++];
			Domain = message[fieldOrder++];
		}
	}


	public sealed class DraftCreditNoteMessage : DraftTransactionMessage
	{
		public int ReferenceId { get; set; }
		public string Domain { get; set; }

		public DraftCreditNoteMessage(int id, DateTime date, Coin currencyCode, string customer, decimal amount, string concept, string employeeName, string account, int batchNumber, int referenceId, string domain) :
			base(TransactionType.CreditNote, id, date, currencyCode, customer, amount, concept, employeeName, account, batchNumber)
		{
			ReferenceId = referenceId;
			Domain = domain;
		}

		public DraftCreditNoteMessage(string serialized) : base(serialized)
		{
			
		}

		protected override void InternalSerialize()
		{
			base.InternalSerialize();
			AddProperty(ReferenceId).
			AddProperty(Domain);
		}

		protected override void Deserialize(string[] serializedMessage, out int fieldOrder)
		{
			base.Deserialize(serializedMessage, out fieldOrder);
			ReferenceId = int.Parse(serializedMessage[fieldOrder++]);
			Domain = serializedMessage[fieldOrder++];
		}
	}

	public sealed class DraftCreditNoteMessages
	{
		public DraftCreditNoteMessage DraftCreditNoteMessage { get; set; }
		public List<DraftAttachmentMessage> AttachmentMessages { get; set; } = new List<DraftAttachmentMessage>();

		public DraftCreditNoteMessages(string msg)
		{
			string[] messages = KafkaMessages.Split(msg);
			DraftCreditNoteMessage = new DraftCreditNoteMessage(messages[0]);
			for (int i = 1; i < messages.Length; i++)
			{
				string message = messages[i];
				var creditNoteAttachmentMessage = new DraftAttachmentMessage(message);
				AttachmentMessages.Add(creditNoteAttachmentMessage);
			}
		}

	}

	public sealed class DraftDebitNoteMessage : DraftTransactionMessage
	{
		public int ReferenceId { get; set; }
		public string Domain { get; set; }

		public DraftDebitNoteMessage(int id, DateTime date, Coin currencyCode, string customer, decimal amount, string concept, string employeeName, string account, int batchNumber, int referenceId, string domain) :
			base(TransactionType.DebitNote, id, date, currencyCode, customer, amount, concept, employeeName, account, batchNumber)
		{
			ReferenceId = referenceId;
			Domain = domain;
		}

		public DraftDebitNoteMessage(string serialized) : base(serialized)
		{
			
		}

		protected override void InternalSerialize()
		{
			base.InternalSerialize();
			AddProperty(ReferenceId).
			AddProperty(Domain);
		}

		protected override void Deserialize(string[] serializedMessage, out int fieldOrder)
		{
			base.Deserialize(serializedMessage, out fieldOrder);
			ReferenceId = int.Parse(serializedMessage[fieldOrder++]);
			Domain = serializedMessage[fieldOrder++];
		}
	}

	public sealed class DraftDebitNoteMessages
	{
		public DraftDebitNoteMessage DraftDebitNoteMessage { get; set; }
		public List<DraftAttachmentMessage> AttachmentMessages { get; set; } = new List<DraftAttachmentMessage>();

		public DraftDebitNoteMessages(string msg)
		{
			string[] messages = KafkaMessages.Split(msg);
			DraftDebitNoteMessage = new DraftDebitNoteMessage(messages[0]);
			for (int i = 1; i < messages.Length; i++)
			{
				string message = messages[i];

				var creditNoteAttachmentMessage = new DraftAttachmentMessage(message);
				AttachmentMessages.Add(creditNoteAttachmentMessage);
			}
		}
	}

	public class DraftAttachmentMessage : KafkaMessage
	{
		public string Url { get; set; }

		internal DraftAttachmentMessage(Uri uri)
		{
			Url = uri.AbsoluteUri;
		}

		public DraftAttachmentMessage(string message):base(message)
		{
			
		}

		protected override void Deserialize(string [] serializedMessage, out int fieldOrder)
		{
			base.Deserialize(serializedMessage, out fieldOrder);
			Url = serializedMessage[0];
		}

        protected override void InternalSerialize()
        {
			base.InternalSerialize();
			AddProperty(Url);
		}
    }
}
