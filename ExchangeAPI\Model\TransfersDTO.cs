﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Threading.Tasks;

namespace ExchangeAPI.Model
{
	public enum TransactionState
	{
		Pending,
		Approved,
		Rejected
	}

	[DataContract(Name = "TransfersDTO")]
	public class TransfersDTO
	{
		[DataMember(Name = "transfers")]
		public List<TransferDTO> Transfers { get; set; } = new List<TransferDTO>();

		[DataMember(Name = "totalRecords")]
		public int TotalRecords { get; set; }

		internal void Add(TransferDTO transferDTO)
		{
			Transfers.Add(transferDTO);
		}
	}

	[DataContract(Name = "TransferDTO")]
	public class TransferDTO
	{
		[DataMember(Name = "date")]
		public string Date { get; set; }
		[DataMember(Name = "domain")]
		public string Domain { get; set; }
		[DataMember(Name = "description")]
		public string Description { get; set; }
		[DataMember(Name = "fromCurrencyCode")]
		public string FromCurrencyCode { get; set; }
		[DataMember(Name = "toCurrencyCode")]
		public string ToCurrencyCode { get; set; }

		[DataMember(Name = "gross")]
		public decimal Gross { get; set; }
		[DataMember(Name = "grossCurrencyCode")]
		public string GrossCurrencyCode { get; set; }
		[DataMember(Name = "net")]
		public decimal Net { get; set; }
		[DataMember(Name = "netCurrencyCode")]
		public string NetCurrencyCode { get; set; }
		[DataMember(Name = "profit")]
		public decimal Profit { get; set; }
		[DataMember(Name = "profitCurrencyCode")]
		public string ProfitCurrencyCode { get; set; }
		[DataMember(Name = "amountReceived")]
		public decimal AmountReceived { get; set; }
		[DataMember(Name = "amountCurrencyCode")]
		public string AmountCurrencyCode { get; set; }

		[DataMember(Name = "approvals")]
		public int Approvals { get; set; }
		[DataMember(Name = "approvalsRequired")]
		public int ApprovalsRequired { get; set; }
		[DataMember(Name = "isApproved")]
		public bool IsApproved { get { return Approvals == ApprovalsRequired; } }
		public bool IsPending
		{
			get 
			{
				return State == TransactionState.Pending;
			}
		}

		[DataMember(Name = "id")]
		public string ID { get; set; }
		[DataMember(Name = "authorizationId")]
		public int AuthorizationId { get; set; }
		[DataMember(Name = "batchNumber")]
		public string BatchNumber { get; set; }
		[DataMember(Name = "state")]
		public string StateAsString
		{
			get
			{
				return State.ToString();
			}
		}

		internal TransactionState State 
		{ 
			get 
			{
				if (Approvals == ApprovalsRequired) return TransactionState.Approved;
				if (Rejections == ApprovalsRequired) return TransactionState.Rejected;
				return TransactionState.Pending;
			} 
		}

		[DataMember(Name = "purchasePrice")]
		public decimal PurchasePrice { get; set; }
		[DataMember(Name = "salePrice")]
		public decimal SalePrice { get; set; }

		[DataMember(Name = "rejections")]
		public int Rejections { get; set; }
		[DataMember(Name = "sourceAddress")]
		public string FromCustomer { get; set; }
		[DataMember(Name = "destinationAddress")]
		public string ToCustomer { get; set; }
		[DataMember(Name = "sourceAccount")]
		public string Account { get; set; }
		[DataMember(Name = "destinationAccount")]
		public string TargetAccount { get; set; }
		[DataMember(Name = "realAccount")]
		public string RealAccount { get; set; }
		[DataMember(Name = "voucherUrl")]
		public string VoucherUrl { get; set; }
		[DataMember(Name = "reason")]
		public string RejectionReason { get; set; }

		[DataMember(Name = "salePriceFormatted")]
		public string SalePriceFormatted { get; set; }
		[DataMember(Name = "purchasePriceFormatted")]
		public string PurchasePriceFormatted { get; set; }
		[DataMember(Name = "amountReceivedFormatted")]
		public string AmountReceivedFormatted { get; set; }
		[DataMember(Name = "grossFormatted")]
		public string GrossFormatted { get; set; }
		[DataMember(Name = "netFormatted")]
		public string NetFormatted { get; set; }
		[DataMember(Name = "profitFormatted")]
		public string ProfitFormatted { get; set; }
		[DataMember(Name = "whoCreated")]
		public string WhoCreated { get; set; }
		[DataMember(Name = "processorAccountId")]
		public int ProcessorAccountId { get; set; }
		[DataMember(Name = "journalDetailEntryId")]
		public string JournalDetailEntryId { get; set; }
	}
}
