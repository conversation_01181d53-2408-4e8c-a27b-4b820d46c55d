﻿using GamesEngine.Finance;
using GamesEngine.Settings;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Globalization;
using static GamesEngine.Exchange.CurrencyPair;

namespace GamesEngine.Exchange
{
	internal struct CurrencyPair
	{
		//https://www.investopedia.com/terms/b/basecurrency.asp
		private readonly Coin qouteCurrency;
		private readonly Coin baseCurrency;
		private readonly decimal price;
		internal decimal Price { get { return price; } }
		internal Coin QouteCurrency { get { return qouteCurrency; } }
		internal Coin BaseCurrency { get { return baseCurrency; } }

		internal CurrencyPair(Coin qouteCurrency, Coin baseCurrency, decimal price)
		{
			if (price <= 0) throw new GameEngineException("The price must be greater than 0");
			if (qouteCurrency == baseCurrency && price != 1) throw new GameEngineException($"The quote currency {qouteCurrency} must be distinct of the base currency {baseCurrency}. Or the price must be 1");

			this.qouteCurrency = qouteCurrency;
			this.baseCurrency = baseCurrency;
			this.price = price;
		}
		internal decimal InversedPrice()
		{
			return Math.Round(1 / Price, Currency.DecimalPrecision(baseCurrency.Iso4217Code));
		}

		internal T Comission<T>(Currency amount, RateMarkup rateMarkup) where T : Currency
		{
			if (!IsaValidCurrency(amount.Coin)) throw new GameEngineException($"The {amount} must be in {qouteCurrency} or {baseCurrency} currency code.");

			decimal markup = rateMarkup.Value;
			Currency comission = Currency.Factory(amount.CurrencyCode, markup);
			comission.MultipliedBy(amount.Value);

			return (T)comission;
		}
		internal bool IsaValidCurrency(Coin currencyCode)
		{
			return currencyCode == qouteCurrency || currencyCode == baseCurrency;
		}
		internal bool HasTheSameCurrenciesAs(CurrencyPair rate)
		{
			return rate.QouteCurrency == qouteCurrency && rate.BaseCurrency == baseCurrency;
		}
		internal RateMarkup CalculateMarkupWith(CurrencyPair rate)
		{
			if (!HasTheSameCurrenciesAs(rate)) throw new GameEngineException($"The {rate} must be a {baseCurrency}/{qouteCurrency} Rate.");

			decimal result = (Price - rate.Price )/ Price;
			//result = Math.Round(result, 2);

			if (result <= 0) throw new GameEngineException("The markup must be higher than 0");
			return new RateMarkup(result);
		}

		internal struct RateMarkup
		{
			internal RateMarkup(decimal value)
			{
				Value = value;
			}
			public decimal Value { get; }
		}

		internal string Summary()
		{
			return $"{BaseCurrency.Iso4217Code}/{QouteCurrency.Iso4217Code} {Currency.ToDisplayFormatWithoutSign(QouteCurrency.Iso4217Code, Price)}";
		}
	}

	internal abstract class FloatingExchangeRate : Objeto
	{
		private readonly ExchangeRates exchangeRates;
		private CurrencyPair rate;
		private readonly DateTime date;
		public DateTime Date { get { return date; } }

		internal decimal Price { get { return rate.Price; } }
		private static NumberFormatInfo customFormat;

		protected FloatingExchangeRate(ExchangeRates exchangeRates, DateTime date, Coin qouteCurrency, Coin baseCurrency, decimal price)
		{
			if (exchangeRates == null) throw new ArgumentNullException(nameof(exchangeRates));
			if (qouteCurrency == baseCurrency && price != 1) throw new GameEngineException($"The quote currency {qouteCurrency} must be distinct of the base currency {baseCurrency}. Or the price must be 1");

			customFormat = (NumberFormatInfo)Integration.CultureInfoEnUS.NumberFormat.Clone();
			customFormat.NumberDecimalSeparator = ".";

			this.exchangeRates = exchangeRates;
			this.date = date;
			this.rate = new CurrencyPair( qouteCurrency, baseCurrency, price);
		}

		internal decimal Minus(FixedExchangeRate rateCost)
		{
			return Price - rateCost.Price;
		}

		public override string ToString()
		{
			return $"{rate.BaseCurrency.Iso4217Code}/{rate.QouteCurrency.Iso4217Code} {Price.ToString(customFormat)}";
		}

		internal string Summary()
		{
			return rate.Summary();
		}

		internal Currency Convert(Currency amount)
		{
			if (!rate.IsaValidCurrency(amount.Coin)) throw new GameEngineException($"The {amount} must be in {rate.QouteCurrency} or {rate.BaseCurrency} currency code.");

			if (amount.Coin == rate.QouteCurrency)
			{
				return Currency.Factory(rate.BaseCurrency.Iso4217Code, amount.Value / Price);
			}
			else
			{
				return Currency.Factory(rate.QouteCurrency.Iso4217Code, amount.Value * rate.Price);
			}
		}

		internal static T CreateSaleRate<T>(ExchangeRates exchangeRates, DateTime date, Coin source, Coin target, decimal price) where T : SaleRate
		{
			if (exchangeRates == null) throw new ArgumentNullException(nameof(exchangeRates));
			if (source == target && price != 1) throw new GameEngineException($"The source currency {source} must be distinct of the target currency {target}. Or the price must be 1");

			return (T)SaleRate.CreateInstance<SaleRate>(exchangeRates, date, source, target, price);
		}

		internal static T CreatePurchaseRate<T>(ExchangeRates exchangeRates, DateTime date, Coin source, Coin target, decimal price) where T : PurchaseRate
		{
			if (exchangeRates == null) throw new ArgumentNullException(nameof(exchangeRates));
			if (source == target && price != 1) throw new GameEngineException($"The source currency {source} must be distinct of the target currency {target}. Or the price must be 1");

			return (T)PurchaseRate.CreateInstance<PurchaseRate>(exchangeRates, date, source, target, price);
		}

		internal class SaleRate : FloatingExchangeRate
		{
			internal SaleRate(ExchangeRates exchangeRates, DateTime date, Coin qouteCurrency, Coin baseCurrency, decimal price) : 
				base(
					exchangeRates, 
					date, 
					qouteCurrency, 
					baseCurrency, 
					price)
			{
			}

			internal static SaleRate CreateInstance<T>(ExchangeRates exchangeRates, DateTime date, Coin qouteCurrency, Coin baseCurrency, decimal price) where T : SaleRate
			{
				SaleRate result = new SaleRate(exchangeRates, date, qouteCurrency, baseCurrency, price);
				return result;
			}

			internal new PurchaseRate PurchaseRate()
            {
				var result = new PurchaseRate(exchangeRates, this.date, rate.BaseCurrency, rate.QouteCurrency, rate.InversedPrice());
				return result;
			}
		}

		internal class PurchaseRate : FloatingExchangeRate
		{
			internal PurchaseRate(ExchangeRates exchangeRates, DateTime date, Coin qouteCurrency, Coin baseCurrency, decimal price) : 
				base(
					exchangeRates, 
					date, 
					qouteCurrency, 
					baseCurrency, 
					price)
			{
			}

			internal static PurchaseRate CreateInstance<T>(ExchangeRates exchangeRates, DateTime date, Coin qouteCurrency, Coin baseCurrency, decimal price) where T : PurchaseRate
			{
				PurchaseRate result = new PurchaseRate(exchangeRates, date, qouteCurrency, baseCurrency, price);
				return result;
			}

			internal new SaleRate SaleRate() 
			{
				var result = new SaleRate(exchangeRates, this.date, rate.BaseCurrency, rate.QouteCurrency, rate.InversedPrice());
				return result;
			}
		}
		internal RateMarkup CalculateMarkupWith(FloatingExchangeRate exchangeRate)
		{
			return rate.CalculateMarkupWith(exchangeRate.rate);
		}

		internal T Comission<T>(T amount, RateMarkup rateMarkup) where T : Currency
		{
			return rate.Comission<T>(amount, rateMarkup);
		}

		internal bool CanCreateACoversionSpreadWith(FloatingExchangeRate exchangeRate)
		{
			return this.rate.HasTheSameCurrenciesAs(exchangeRate.rate)
				&& this.date == exchangeRate.date;
		}

		internal bool IsaValidCurrency(Coin currentCode)
		{
			return rate.IsaValidCurrency(currentCode);
		}

		public Coin Source { get { return rate.QouteCurrency; } }

		public Coin Target { get { return rate.BaseCurrency; } }

	}
}
