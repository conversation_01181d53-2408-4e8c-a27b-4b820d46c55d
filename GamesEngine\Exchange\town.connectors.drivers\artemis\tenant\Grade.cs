﻿using RestSharp;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.commons;
using town.connectors.drivers;
using town.connectors.drivers.artemis;
using Puppeteer.EventSourcing;

namespace Connectors.town.connectors.drivers.artemis
{
    public class Grade : DGSTenantDriver
    {
        private RestClient _postUpdateWagersClient;
        public Grade() : base(Tenant_Actions.Grade)
        {
        }
        public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            throw new NotImplementedException();
        }
        public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet) 
        {
			if (_postUpdateWagersClient == null)
			{
                if (string.IsNullOrWhiteSpace(SystemId)) throw new ArgumentNullException("Custom variable 'TokenSystemId' is required");
                if (string.IsNullOrWhiteSpace(Password)) throw new ArgumentNullException("Custom variable 'TokenSystemPassword' is required");
                if (string.IsNullOrWhiteSpace(ServicesUrl)) throw new ArgumentNullException("Custom variable 'CompanyBaseUrlServices' is required");

                if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken))
                {
                    _ = Task.Run(async () =>
                    {
                        await DGSTenantDriver.RequestAppTokenAsync(ServicesUrl, SystemId, Password);
                    });
                }

                _postUpdateWagersClient = new RestClient(ServicesUrl);
            }

            if (CustomSettings.ThereArePendingChanges)
            {
                bool changeApplied = false;
                ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices", out changeApplied).AsString;
                SystemId = CustomSettings.Get(now, "TokenSystemId").AsString;
                Password = CustomSettings.Get(now, "TokenSystemPassword").AsSecret.ToString();

                if (changeApplied)
                {
                    if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken))
                    {
                        _ = Task.Run(async () =>
                        {
                            await DGSTenantDriver.RequestAppTokenAsync(ServicesUrl, SystemId, Password);
                        });
                    }

                    _postUpdateWagersClient = new RestClient(ServicesUrl);
                }
            }

            UpdateWagers(now, recordSet);
			return (T)Convert.ChangeType(null, typeof(T));
		}

        public override void Prepare(DateTime now)
        {
            CustomSettings.AddVariableParameter("wagers");

            //CustomSettings.Prepare();

            SystemId = CustomSettings.Get(now, "TokenSystemId").AsString;
            Password = CustomSettings.Get(now, "TokenSystemPassword").AsSecret.ToString();
            ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
        }

        public void UpdateWagers(DateTime now, CustomSettings.RecordSet recordSet)
        {
            var Wagers = recordSet.Mappings["wagers"];
            var wagers = Wagers.As<List<PayFragmentsMessage>>();

            if (wagers == null) throw new ArgumentNullException(nameof(wagers));
            if (!wagers.Any()) throw new Exception($"At least one wager is required to send request {nameof(UpdateWagersColection)} on {nameof(UpdateWagers)}");

            Debug.WriteLine($"Accounting service {nameof(UpdateWagersColection)} received {nameof(wagers)}:{wagers.Count}");
            UpdateWagersColection(wagers);
        }

        void UpdateWagersColection(List<PayFragmentsMessage> wagers)
        {
            if (wagers == null) throw new ArgumentNullException(nameof(wagers));
            if (wagers.Count == 0) throw new Exception($"At least one wager is required to send request {nameof(UpdateWagersColection)}");
            if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken)) throw new Exception($"{nameof(DGSProcessorDriver.AppToken)} is empty");

            const string url = "/GradingBet";
            List<List<PayFragmentsMessage>> listOfGradesChunked = SplitList(wagers, GradedWagersPerChunk);
            foreach (List<PayFragmentsMessage> wagersChunck in listOfGradesChunked)
            {
                GradeFreeFormWagerCollectionChunked(url, wagersChunck);
            }
        }

        List<List<T>> SplitList<T>(List<T> items, int nSize = 30)
        {
            var list = new List<List<T>>();

            for (int i = 0; i < items.Count; i += nSize)
            {
                list.Add(items.GetRange(i, Math.Min(nSize, items.Count - i)));
            }

            return list;
        }

        void GradeFreeFormWagerCollectionChunked(string url, List<PayFragmentsMessage> wagers)
        {
            var gradeFreeFormWagers = new WagersUpdateBody();
            gradeFreeFormWagers.AddWagers(wagers);

            var jsonString = Commons.ToJson(gradeFreeFormWagers);
            string responseString = string.Empty;
            int retryNumber = 0;
            const int MAX_RETRIES_WAGER_COLLECTION = 5;
            string valuesWithHiddenFields = $"ticketNumbers:{string.Join(',', wagers.Select(wager => wager.TicketNumber))}";
            while (true)
            {
                try
                {
                    Loggers.GetIntance().AccountingServicesDGSUpdateWagers.Debug($@"url:{url} data:{valuesWithHiddenFields}");
                    var request = new RestRequest(url, Method.POST);
                    request.AddHeader("Authorization", $"Bearer {DGSProcessorDriver.AppToken}");
                    request.AddHeader("Content-Type", "application/json");
                    request.AddParameter("application/json", jsonString, ParameterType.RequestBody);

                    var response = _postUpdateWagersClient.Execute(request);
                    responseString = response.Content;
                    if ((int)response.StatusCode != 200)
                    {
                        Loggers.GetIntance().AccountingServicesDGSUpdateWagers.Debug($"{nameof(GradeFreeFormWagerCollectionChunked)}\nUrl:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}");
                        NotifyWarn(nameof(GradeFreeFormWagerCollectionChunked), $"Url:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}", $"Response threw an error");

                        if (responseString.Contains("Lotto wager not found.")) break;
                        retryNumber++;
                        Thread.Sleep(2000 + (1000 * (retryNumber - 1)));
                        if (retryNumber == MAX_RETRIES_WAGER_COLLECTION) break;
                    }
                    else
                    {
                        if (!string.IsNullOrWhiteSpace(responseString))
                            Loggers.GetIntance().AccountingServicesDGSUpdateWagers.Debug($"{nameof(GradeFreeFormWagerCollectionChunked)}\nUrl:{url}\nResponse: {responseString}");

                        break;
                    }
                }
                catch (Exception e)
                {
                    Loggers.GetIntance().AccountingServicesDGSUpdateWagers.Error($@"url:{url} data:{valuesWithHiddenFields} type:{e.GetType()} error:{e.Message}", e);
                    retryNumber++;
                    var extraErrorMessage = string.Empty;
                    if (e.InnerException is HttpRequestException) extraErrorMessage = "There was a problem getting the response from server.";
                    Thread.Sleep(2000 + (1000 * (retryNumber - 1)));
                    if (retryNumber == MAX_RETRIES_WAGER_COLLECTION)
                    {
                        InternalOnError(nameof(GradeFreeFormWagerCollectionChunked), retryNumber, e, $"Url:{url}", $"Request: {jsonString}", $"Response: {responseString}", extraErrorMessage);
                    }
                }
            }

        }
    }
}
