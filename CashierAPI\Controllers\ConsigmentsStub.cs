﻿using CashierAPI.Controllers;
using ExternalServices;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using town.connectors.drivers.artemis;

namespace ASIStub.Controllers
{
	public class ConsigmentsStub : AuthorizeController
	{
		static int authorization = 100;

		[HttpPost("")]
		[AllowAnonymous]
		public string ConsignmentsWebServices(string op)
		{
			authorization++;
			switch (op)
            {
				case "Add_deposit":
					StringBuilder builder = new StringBuilder();
					builder.AppendLine("<?xml version=\"1.0\" encoding=\"utf-8\"?>");
					builder.AppendLine("<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">");
					builder.AppendLine("<soap:Body>");
					builder.AppendLine("<Add_depositResponse xmlns=\"http://tempuri.org/\">");
					builder.AppendLine($"<Add_depositResult>{authorization}</Add_depositResult>");
					builder.AppendLine("</Add_depositResponse>");
					builder.AppendLine("</soap:Body>");
					builder.AppendLine("</soap:Envelope>");
					return builder.ToString();
				case "Add_payout":
					builder = new StringBuilder();
					builder.AppendLine("<?xml version=\"1.0\" encoding=\"utf-8\"?>");
					builder.AppendLine("<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">");
					builder.AppendLine("<soap:Body>");
					builder.AppendLine("<Add_payoutResponse xmlns=\"http://tempuri.org/\">");
					builder.AppendLine($"<Add_payoutResult>1,Payout Inserted,{authorization}</Add_payoutResult>");
					builder.AppendLine("</Add_payoutResponse>");
					builder.AppendLine("</soap:Body>");
					builder.AppendLine("</soap:Envelope>");
					return builder.ToString();
				default:
					return $"No service with op={op}";
			}
		}

	}
}
