﻿using GamesEngine.Business;
using GamesEngine.Games;
using GamesEngine.Games.Lines;
using GamesEngine.Games.Tournaments;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GamesEngine.Gameboards.Lines
{
	internal class Wagers : Gameboards
	{
		private readonly Showcase showcase;

		internal Wagers(Showcase showcase)
		{
			if (showcase == null) throw new ArgumentNullException(nameof(showcase));

			this.showcase = showcase;
		}

		internal decimal Grade(bool itIsThePresent, Line line, DateTime now, string gradedBy)
		{
			if (line == null) throw new ArgumentNullException(nameof(line));
			if ( line.Tier == Tier.TIER_ONE && ! line.Game.IsGameOver()) throw new GameEngineException($"Match has not ended therefore it can stored.");
			if (line.IsGraded()) throw new GameEngineException($"Line was already graded it can not be graded again");
			if (!this.showcase.Contains(line)) throw new GameEngineException($"This line {line.ToString()} does not belong to this showcase");
			if (string.IsNullOrWhiteSpace(gradedBy)) throw new ArgumentNullException(nameof(gradedBy));

			decimal result = 0;

			foreach (Wager wager in this.WagersOf(line))
			{
				result += wager.Grade();

				if (wager.WasPurchased)
				{
					switch (wager.Prizing)
					{
						case GameboardStatus.LOSER:
							//Referirme a un ID en especifico porque se que las descripciones ya estan puestas
							if (wager.PreviouslyWasNoAction()) this.showcase.LockBetCanceled(wager);
							this.showcase.Punch(wager);
							break;
						case GameboardStatus.WINNER:
							//Referirme a un ID en especifico porque se que las descripciones ya estan puestas
							if (wager.PreviouslyWasNoAction()) this.showcase.LockBetCanceled(wager);
							this.showcase.PayPrize(wager);
							break;
						case GameboardStatus.UNPRIZED:
							if (!wager.IsNoAction()) throw new GameEngineException("Gameboard should be a no action");
							if (wager.PreviouslyWasWinner()) this.showcase.LockFromBetPayed(wager);
							if (wager.PreviouslyWasLoser()) this.showcase.LockBetPunched(wager);
							this.showcase.Cancel(wager);
							break;
						default:
							throw new GameEngineException($"Status {wager.Prizing} is invalid for a wager just graded");
					}
				}
			}

			var previousGrading = line.Grading;
			line.OriginalVersion.ChangeToGraded();

			var onlythrowEventIfItIsTheFirstTime = previousGrading == Games.GradingStatus.PENDING;
			if (itIsThePresent && onlythrowEventIfItIsTheFirstTime && (line.IsPublished || line.IsSuspended))
			{
				var removedLineEvent = new CanceledLineEvent(now, line);
				PlatformMonitor.GetInstance().WhenNewEvent(removedLineEvent);
			}

			GradeStatusEvent gradedEvent;
			switch (line)
			{
				case SpreadLine sp:
					gradedEvent = new SpreadLineStatusEvent(now, sp);
					break;
				case MoneyLine ml:
					gradedEvent = new MoneyLineStatusEvent(now, ml);
					break;
				case MoneyDrawLine mdl:
					gradedEvent = new MoneyDrawLineStatusEvent(now, mdl);
					break;
				case TotalPointsLine tp:
					gradedEvent = new TotalPointsLineStatusEvent(now, tp);
					break;
				case OverUnderLine ou:
					gradedEvent = new OverUnderLineStatusEvent(now, ou);
					break;
				case YesNoLine yn:
					gradedEvent = new YesNoLineStatusEvent(now, yn);
					break;
				case FixedLine fixedLine:
					gradedEvent = new FixedLineLineStatusEvent(now, fixedLine);
					break;
				default:
					throw new GameEngineException($"Line {line} is not supported to launch the status event.");
			}

			if (itIsThePresent)
			{
				PlatformMonitor.GetInstance().WhenNewEvent(gradedEvent);
			}

			return result;
		}

		private int version = 1;
		bool sometimeWereTicketsSentToAccounting;
		internal void Confirm(bool itIsThePresent, Line line, DateTime now, string confirmedBy)
		{
			if (line == null) throw new ArgumentNullException(nameof(line));
			if (line.Tier == Tier.TIER_ONE && !line.Game.IsGameOver()) throw new GameEngineException($"Match has not ended therefore it can stored.");
			if (line.IsPending()) throw new GameEngineException($"Line is still pending, there is no changes to be confirmed");
			if (!this.showcase.Contains(line)) throw new GameEngineException($"This line {line.ToString()} does not belong to this showcase");
			if (string.IsNullOrWhiteSpace(confirmedBy)) throw new ArgumentNullException(nameof(confirmedBy));

			var wagersToUpdate = this.WagersOf(line).Where(x => x.NeedsAccountingSettle);
			if (!sometimeWereTicketsSentToAccounting) sometimeWereTicketsSentToAccounting = wagersToUpdate.Any();
			this.Company.Cashier.PayGameboards(itIsThePresent, wagersToUpdate, now, confirmedBy, sometimeWereTicketsSentToAccounting);
			foreach(var w in wagersToUpdate) w.MarkAccountingSettleAsDone();

			version++;
		}

		internal int Version
		{
			get
			{
				return this.version;
			}
		}

		internal void SetNoAction(bool itIsThePresent, Line line, DateTime now, string gradedBy)
		{
			if (line == null) throw new ArgumentNullException(nameof(line));
			if (!this.showcase.Contains(line)) throw new GameEngineException($"This line {line.ToString()} does not belong to this showcase");
			if (string.IsNullOrWhiteSpace(gradedBy)) throw new ArgumentNullException(nameof(gradedBy));
			if (line.IsNoAction()) throw new GameEngineException($"This line {line.ToString()} is already noAction.");

			foreach (Wager wager in this.WagersOf(line))
			{
				wager.ChangeToNoAction();

				if (wager.PreviouslyWasLoser())
				{
					this.showcase.LockBetPunched(wager);
				}
				else if (wager.PreviouslyWasWinner())
				{
					this.showcase.LockFromBetPayed(wager);
				}

				if (wager.WasPurchased)
				{
					this.showcase.Cancel(wager);
				}
			}

			var previousGrading = line.Grading;
			line.OriginalVersion.ChangeToNoAction();

			var onlythrowEventIfItIsTheFirstTime = previousGrading == Games.GradingStatus.PENDING;
			if (itIsThePresent && onlythrowEventIfItIsTheFirstTime && (line.IsPublished || line.IsSuspended))
			{
				var removedLineEvent = new CanceledLineEvent(now, line);
				PlatformMonitor.GetInstance().WhenNewEvent(removedLineEvent);
			}

			if (itIsThePresent)
			{
				var noActionEvent = new NoActionStatusEvent(now, line);
				PlatformMonitor.GetInstance().WhenNewEvent(noActionEvent);
			}
		}

		internal void Regrade(bool itIsThePresent, Line line, DateTime now, string gradedBy)
		{
			if (line == null) throw new ArgumentNullException(nameof(line));
			if (!line.IsGraded() && !line.IsNoAction()) throw new GameEngineException($"Line was not graded therefore it can not be regraded");
			if (!this.showcase.Contains(line)) throw new GameEngineException($"This line {line.ToString()} does not belong to this showcase");
			if (string.IsNullOrWhiteSpace(gradedBy)) throw new ArgumentNullException(nameof(gradedBy));

			foreach (Wager wager in this.WagersOf(line))
			{
				wager.ChangeToRegraded();
				if (wager.WasPurchased)
				{
					if (wager.PreviouslyWasWinner()) this.showcase.LockFromBetPayed(wager);
					if (wager.PreviouslyWasLoser()) this.showcase.LockBetPunched(wager);
					if (wager.PreviouslyWasNoAction()) this.showcase.LockBetCanceled(wager);
				}					
			}

			line.OriginalVersion.ChangeToRegraded();
			if (itIsThePresent)
			{
				var regradeEvent = new RegradeStatusEvent(now, line);
				PlatformMonitor.GetInstance().WhenNewEvent(regradeEvent);
			}
		}

		internal void WriteHistoricalGradingData(SenderOfHistorical historical, Line line, DateTime now, string gradedBy)
		{
			if (line == null) throw new ArgumentNullException(nameof(line));
			if (line.Tier == Tier.TIER_ONE && !line.Game.IsGameOver()) throw new GameEngineException($"Match has not ended therefore it can stored.");
			if (!this.showcase.Contains(line)) throw new GameEngineException($"This line {line.ToString()} does not belong to this showcase");
			if (string.IsNullOrWhiteSpace(gradedBy)) throw new ArgumentNullException(nameof(gradedBy));
			if (line.IsPending()) throw new GameEngineException("The line can not be pending.");
			
			historical.StartSending(line.Showcase.Betboard.Tournament.Id, line.Game.Number, line.LineId);
			foreach (Wager wager in this.WagersOf(line))
			{
				switch (wager.Prizing)
				{
					case GameboardStatus.LOSER:
						//Referirme a un ID en especifico porque se que las descripciones ya estan puestas
						historical.WriteLoserData(wager, now, gradedBy);
						break;
					case GameboardStatus.WINNER:
						//Referirme a un ID en especifico porque se que las descripciones ya estan puestas
						historical.WriteWinnerData(wager, now, gradedBy);
						break;
					case GameboardStatus.UNPRIZED:
						if (wager.IsRegraded())
						{
							historical.WriteLoserData(wager, now, gradedBy);
						}
						else
						{
							historical.WriteNoActionData(wager, now, gradedBy);
						}
						break;
					default:
						throw new GameEngineException($"Status {wager.Prizing} is invalid for a wager just graded");
				}
			}
			historical.EndSending();
		}

		internal IEnumerable<Gameboard> WagersOf(Line line)
		{
			var result = Enumerable.Empty<Gameboard>();
			bool exit = false;
			while (!exit)
			{
				var lineWagers = this.WagersOfLineVersion(line);
				result = result.Concat(lineWagers);
				exit = line.IsOriginalVersion();
				if (!exit) line = line.PreviousVersion;
			}

			return result;
		}

		internal void DisposeAll()
		{
			var allWagers = base.List();
			foreach(Wager wager in allWagers)
			{
				wager.Dispose();
			}
		}

		private IEnumerable<Gameboard> WagersOfLineVersion(Line line)
		{
			var lineWagers = base.List(x => (x as Wager).Line == line && ! x.IsRefunded());
			return lineWagers;
		}

		internal bool HasPurchases(Line line)
		{
			var result = base.Any(x => (x as Wager).Line == line && !x.IsRefunded());
			return result;
		}

		private void ValidateThatAllWagersArePendingOrRegraded(IEnumerable<Gameboard> allWagers)
		{
			bool areAllPendingWagers = true;
			bool areAllRegradedWagers = true;
			foreach (Wager wager in allWagers)
			{
				switch (wager.Grading)
				{
					case GameboardStatus.PENDING:
						areAllRegradedWagers = false;
						break;
					case GameboardStatus.REGRADED:
						areAllPendingWagers = false;
						break;
				}
			}
			if (!areAllRegradedWagers && !areAllPendingWagers) throw new GameEngineException($"The wagers are not valid grading status, all of them should be regraded or pending");
		}

		internal RiskAssestment CalculateRisk(Game game)
		{
			RiskAssestment result = new RiskAssestment(game);
			foreach (Gameboard gameboard in base.List())
			{
				var wager = (Wager)gameboard;
				wager.CalculateRisk(result);
			}

			return result;
		}

		internal IEnumerable<Wager> TicketsWithoutNoActionToUpdateInAccounting()
		{
			throw new NotImplementedException();
		}

		private Company Company
		{
			get
			{
				return this.showcase.Betboard.Company;
			}
		}
	}
}
