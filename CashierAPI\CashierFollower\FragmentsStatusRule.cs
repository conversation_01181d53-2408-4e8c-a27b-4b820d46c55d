﻿using Puppeteer;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CashierAPI.CashierFollower
{
    public class FragmentsStatusRule : RuleWithoutActor
    {
        protected const string START_TEXT_LINE1 = "authorization = atAddress.GetAuthorization(";
        protected const string END_TEXT_LINE1 = ");";
        protected const string START_TEXT_LINE2 = "authorization.ChangeInitialFragmentsStatus({";
        protected const string END_TEXT_LINE2 = "}, initialStatus);";

        public override void FinalizeRule()
        {
            return;
        }

        public override void Then(Script script)
        {
            const string splitBlockText = ");\r}\r";
            string[] scriptBlocks = script.Text.Split(splitBlockText);

            foreach (string scriptBlock in scriptBlocks)
            {
                if (string.IsNullOrEmpty(scriptBlock)) continue;

                string authorizationNumber = AuthorizationNumber(scriptBlock + splitBlockText);
                List<int> fragmentsStatus = FragmentsStatusArray(scriptBlock + splitBlockText);
                if (!string.IsNullOrEmpty(authorizationNumber) && fragmentsStatus.Count > 0)
                {
                    DBHelperCashierFollower.UpdateAuthorizationFragmentsStatus(authorizationNumber, fragmentsStatus, script.Now, script.DairyId);
                    DBHelperCashierFollower.CalculatePossibleSkips(authorizationNumber);
                }

            }
        }

        public override void When(Script script)
        {
            if (script.Text.IndexOf(START_TEXT_LINE1) != -1 && script.Text.IndexOf(START_TEXT_LINE2) != -1)
            {
                Then(script);
            }
        }

        private string AuthorizationNumber(string script)
        {
            UtilStringRules.SetHeaderText(START_TEXT_LINE1, END_TEXT_LINE1);
            return UtilStringRules.GetBodyArgText(script).Trim();
        }

        private List<int> FragmentsStatusArray(string script)
        {
            bool hasManyGroups = true;
            List<int> fragmentsStatusArray = new List<int>();

            UtilStringRules.SetHeaderText(START_TEXT_LINE2, END_TEXT_LINE2);

            while (hasManyGroups)
            {
                string bodyArgs = UtilStringRules.GetBodyArgText(script);

                string[] arguments = bodyArgs.Split(',');
                foreach (string argIndex in arguments)
                {
                    fragmentsStatusArray.Add(Convert.ToInt32(argIndex));
                }

                if (!UtilStringRules.HasNextChangeInitialFragmentsStatus(script))
                {
                    hasManyGroups = false;
                }

            }
            

            return fragmentsStatusArray;
        }

    }
}
