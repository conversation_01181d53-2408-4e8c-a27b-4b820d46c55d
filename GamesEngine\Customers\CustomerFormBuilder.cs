﻿using GamesEngine.PurchaseOrders;
using Newtonsoft.Json;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GamesEngine.Customers
{
	[Puppet]
    internal class CustomerFormBuilder:Objeto
    {
        internal CustomerForm Form { get;}

        public CustomerFormBuilder(Template template)
        {
            if (template == null) throw new ArgumentNullException(nameof(template));

            Form = new CustomerForm(template);
        }

        public CustomerFormBuilder(Customer customer, Template template)
        {
            if (customer == null) throw new ArgumentNullException(nameof(customer));
            if (template == null) throw new ArgumentNullException(nameof(template));

            Form = new CustomerForm(customer, template);
        }

        internal CustomerFormBuilder AddFields(string serializedFields)
        {
            if (string.IsNullOrWhiteSpace(serializedFields)) throw new ArgumentNullException(nameof(serializedFields));

            var fields = new List<FieldData>();
            string uniqueKey = null;
            var deserializedFields = JsonConvert.DeserializeObject<IDictionary<string, object>>(serializedFields);
            if (deserializedFields.ContainsKey(CustomerForm.PROPERTY_ID)) deserializedFields.Remove(CustomerForm.PROPERTY_ID);
            var verifiableKeys = new List<string>();
            foreach (var deserializedField in deserializedFields)
            {
                var key = deserializedField.Key;
                if (Form.IsVerifiableField(key)) 
                {
                    verifiableKeys.Add(Fields.PREFIX_VERIFIABLE + key);
                }
                if (Form.IsUniqueField(key))
                {
                    if (uniqueKey == null)
                    {
                        uniqueKey = key;
                    }
                    else
                    {
                        uniqueKey += $",{key}";
                    }
                }

                if (!Form.AnyFieldContainsKey(key) && !Form.IsVerifiableKey(key) && !Form.IsRequiredKey(key) && !Form.IsIgnoredKey(key)) throw new GameEngineException($"{nameof(key)} '{key}' does not exist in template fields");
                fields.Add(new FieldData(key, deserializedField.Value.ToString()));
            }

            if (uniqueKey == null) throw new GameEngineException($"Some field must be unique");
            if (!fields.Any(field => field.Name.Equals(CustomerForm.PROPERTY_KEY, StringComparison.OrdinalIgnoreCase))) fields.Add(new FieldData(CustomerForm.PROPERTY_KEY, uniqueKey));

            foreach (var verifiableKey in verifiableKeys)
            {
                if (! deserializedFields.ContainsKey(verifiableKey)) throw new GameEngineException($"{nameof(verifiableKey)} '{verifiableKey}' does not exist in serialized fields");
            }

            Form.AddFields(fields);
            return this;
        }

        internal CustomerFormBuilder AddExtraFields(string serializedFields)
        {
            if (string.IsNullOrWhiteSpace(serializedFields)) throw new ArgumentNullException(nameof(serializedFields));

            var fields = new List<FieldData>();
            string uniqueKey = null;
            var deserializedFields = JsonConvert.DeserializeObject<IDictionary<string, object>>(serializedFields);
            var verifiableKeys = new List<string>();
            foreach (var deserializedField in deserializedFields)
            {
                var key = deserializedField.Key;
                if (Form.IsVerifiableField(key))
                {
                    verifiableKeys.Add(Fields.PREFIX_VERIFIABLE + key);
                }
                if (Form.IsUniqueField(key))
                {
                    if (uniqueKey == null)
                    {
                        uniqueKey = key;
                    }
                    else
                    {
                        uniqueKey += $",{key}";
                    }
                }
                else if (Form.AnyFieldContainsKey(key) && !Form.IsVerifiableKey(key) && !Form.IsRequiredKey(key) && !Form.IsIgnoredKey(key)) throw new GameEngineException($"{nameof(key)} '{key}' belongs to template fields. It is not an extra field");
                fields.Add(new FieldData(key, deserializedField.Value.ToString()));
            }

            if (uniqueKey == null) throw new GameEngineException($"Some field must be unique");
            if (!fields.Any(field => field.Name.Equals(CustomerForm.PROPERTY_KEY, StringComparison.OrdinalIgnoreCase))) fields.Add(new FieldData(CustomerForm.PROPERTY_KEY, uniqueKey));

            foreach (var verifiableKey in verifiableKeys)
            {
                if (!deserializedFields.ContainsKey(verifiableKey)) throw new GameEngineException($"{nameof(verifiableKey)} '{verifiableKey}' does not exist in serialized fields");
            }

            Form.AddFields(fields);
            return this;
        }

        internal CustomerFormBuilder SetAsNew(DateTime creationDate, string whoCreated, int customerTypeId)
        {
            if (string.IsNullOrWhiteSpace(whoCreated)) throw new ArgumentNullException(nameof(whoCreated));

            Form.SetAsNew(creationDate, whoCreated, customerTypeId);
            return this;
        }

        internal CustomerFormBuilder SetAsUpdate(DateTime updateDate, string whoUpdated, CustomerForm customerFormWithModifiedFields)
        {
            if (string.IsNullOrWhiteSpace(whoUpdated)) throw new ArgumentNullException(nameof(whoUpdated));
            if (customerFormWithModifiedFields == null) throw new ArgumentNullException(nameof(customerFormWithModifiedFields));

            Form.SetAsUpdate(updateDate, whoUpdated, customerFormWithModifiedFields);
            return this;
        }

        internal CustomerFormBuilder SetAsApproved(DateTime approvalDate, string whoApproved)
        {
            if (string.IsNullOrWhiteSpace(whoApproved)) throw new ArgumentNullException(nameof(whoApproved));

            Form.SetAsApproved(approvalDate, whoApproved);
            return this;
        }

        internal CustomerFormBuilder SetAsRejected(DateTime rejectionDate, string whoRejected)
        {
            if (string.IsNullOrWhiteSpace(whoRejected)) throw new ArgumentNullException(nameof(whoRejected));

            Form.SetAsRejected(rejectionDate, whoRejected);
            return this;
        }

        internal CustomerForm CreateForm()
        {
            if (! Form.HasFields) throw new GameEngineException($"{nameof(CustomerForm)} must have some fields");

            //Form.VerifyIfCustomerIsAutoApproved(); TODO: uncomment this for autoapprove
            return Form;
        }
    }
}
