﻿using GamesEngine.Finance;
using GamesEngine.Domains;
using GamesEngine.Gameboards.Lotto;
using GamesEngine.Games.Lotto;
using GamesEngine.Location;
using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using System.Text;
using static Connectors.town.connectors.drivers.artemis.Fragment_an_Authorization;
using static GamesEngine.Business.Company;

namespace GamesEngine.Business
{
    public class TotalOrderByToWin : TotalOrderAndExclusionsByToWin
    {
        public TotalOrderByToWin() : base(1)
        {

        }

        static string GetRuleType(TicketType ticketType)
        {
            switch (ticketType)
            {
                case TicketType.P2S:
                case TicketType.P3S:
                case TicketType.P4S:
                case TicketType.P5S:
                    return "Straight";
                case TicketType.P2B:
                case TicketType.P3B:
                case TicketType.P4B:
                case TicketType.P5B:
                    return "Boxed";
                default:
                    throw new GameEngineException($"Unknown {nameof(ticketType)} '{ticketType}'");
            }
        }

        internal void AddToWinByDrawAndNumbers(Company company, Domain domain, IEnumerable<SubTicket<IPick>> subtickets, decimal betAmount, TicketType ticketType, int pickType, State state, DateTime drawDate, string currencyCode, bool withFireBall)
        {
            foreach (var subticket in subtickets)
            {
                var prizes = (PrizesPicks)company.Lotto900().RiskProfiles.GetRiskProfile(domain).Prizes;
                var prizeCriteria = prizes.WayOfSubticket(ticketType, subticket);
                decimal prize = prizes.Prize(ticketType, prizeCriteria) * betAmount;
                var type = Disincentives.PrizeCriteriaName(pickType, prizeCriteria);
                var number = subticket.Number.AsStringForAccounting().Replace("-", string.Empty);
                var code = currencyCode == "FP" ? " FP" : string.Empty;
                var fireballTag = withFireBall ? " FB" : string.Empty;
                Tickets.Add(new ToWinByDrawAndNumber()
                {
                    draw = state.Abbreviation,
                    drawDate = drawDate.ToString("MM/dd/yyyy"),
                    drawHour = drawDate.ToString("h:mm:ss tt"),
                    number = number,
                    pick = pickType,
                    risk = betAmount,
                    toWin = prize - betAmount,
                    type = type,
                    description = $"Lotto{fireballTag}{code} {state.Abbreviation} {drawDate.ToString("h:mm:ss tt")} Pick {pickType} {drawDate.ToString("MM/dd/yyyy")} {GetRuleType(ticketType)}",
                    freePlay = currencyCode == "FP"
                }
                );
            }
        }
    }

    [DataContract(Name = "excludeSubticket")]
    public class ExcludeSubticket
    {
        [DataMember(Name = "state")]
        public string State { get; set; }
        [DataMember(Name = "hour")]
        public string Hour { get; set; }
        [DataMember(Name = "date")]
        public string Date { get; set; }
        [DataMember(Name = "subticket")]
        public string Subticket { get; set; }

        public ExcludeSubticket(ChosenDrawBody drawSchedule, string date)
        {
            State = drawSchedule.State;
            Hour = drawSchedule.Hour;
            Date = date;
            Subticket = ExcludeSubtickets.INDICATOR_FOR_ALL_SUBTICKETS_EXCLUDED;
        }

        public ExcludeSubticket(ToWinByDrawAndNumberResponse drawSchedule)
        {
            State = drawSchedule.draw;
            Hour = drawSchedule.drawHour;
            Date = drawSchedule.date;
            Subticket = drawSchedule.number;
        }
    }

    [DataContract(Name = "OrderErrorResponseForSingleSelection")]
    public class OrderErrorResponseForSingleSelection
    {
        [DataMember(Name = "errorMessage")]
        public string ErrorMessage { get; set; }
        [DataMember(Name = "isValidToRetryPurchase")]
        public bool IsValidToRetryPurchase { get; set; }
        [DataMember(Name = "excludeSubtickets")]
        public ExcludeSubticket[] ExcludeSubtickets { get; set; }

        public OrderErrorResponseForSingleSelection()
        {

        }
    }

    public class ChosenDrawBody
    {
        public string State { get; set; }
        public string Hour { get; set; }

        public ChosenDrawBody(string state, string hour)
        {
            State = state;
            Hour = hour;
        }
    }

    public struct ExcludedSubticketKey
    {
        public string State { get; set; }
        public string Hour { get; set; }
        public string StrNumbers { get; set; }
    }

    public class GroupedExcludedSubticket
    {
        public string Dates => string.Join(",", DatesSet);
        public string Subtickets => string.Join(",", NumbersSet);
        public HashSet<string> DatesSet { get; set; } = new HashSet<string>();
        public HashSet<string> NumbersSet { get; set; } = new HashSet<string>();
    }
}
