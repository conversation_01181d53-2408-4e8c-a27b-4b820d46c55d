﻿using System;
using System.Runtime.Serialization;

namespace Bis
{
	[Serializable]
	internal class GameEngineException : Exception
	{
		public GameEngineException()
		{
		}

		public GameEngineException(string message) : base(message)
		{
		}

		public GameEngineException(string message, Exception innerException) : base(message, innerException)
		{
		}

		protected GameEngineException(SerializationInfo info, StreamingContext context) : base(info, context)
		{
		}
	}
}