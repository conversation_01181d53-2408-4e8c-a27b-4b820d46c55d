﻿using GamesEngine.Finance;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Text;

namespace GamesEngine.Exchange
{
    [Puppet]
    public abstract class TransactionFee:Objeto
    {
        internal enum FeeType
        {
            Free,
            Miner
        }

        private readonly Currency fee;
        internal string CurrencyCode => fee.CurrencyCode;
        public decimal Value => fee.Value;
        internal string Sign => fee.Sign;
        internal string ToDisplayFormat()
        {
            return fee.ToDisplayFormat();
        }

        internal bool NoFee
        {
            get
            {
                return Type == FeeType.Free;
            }
        }

        internal FeeType Type { get; }

        internal TransactionFee(Currency fee, FeeType type)
        {
            if (fee == null) throw new ArgumentNullException(nameof(fee));

            this.fee = fee;
            Type = type;
        }

        internal static TransactionFee Factory(Currency fee, FeeType feeType)
        {
            if (feeType == FeeType.Miner)
            {
                return new MinerFee(fee);
            }
            else if (feeType == FeeType.Free)
            {
                return new NoFee(fee.Coin);
            }
            else
            {
                throw new GameEngineException($"There is no {nameof(TransactionFee)} for type {feeType.ToString()} yet.");
            }
        }
    }

    public class MinerFee: TransactionFee
    {
        internal MinerFee(Currency fee):base(fee, FeeType.Miner)
        {

        }
    }

    public class NoFee : TransactionFee
    {
        internal NoFee(string currencyCode) : base(Currency.Factory(currencyCode, 0m), FeeType.Free)
        {

        }
        internal NoFee(Coin currencyCode) : base(Currency.Factory(currencyCode.Iso4217Code, 0m), FeeType.Free)
        {

        }
    }

    public class NoFeeUSD : NoFee
    {
        internal NoFeeUSD():base(Coinage.Coin(Currencies.CODES.USD) )
        {

        }
    }
}
