﻿using GamesEngine.Gameboards.Lines;
using GamesEngine.Games.Tournaments;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using GamesEngine.Settings;
using GamesEngine.Time;
using System;
using System.Collections.Generic;

namespace GamesEngine.Games.Lines
{
	internal class SpreadLine : ABLine
	{
		internal const string TEXT = "Spread Line";
		private readonly double spread;

		internal SpreadLine(int id, Question question, Tier tier, Game game, Shelve shelve, int index, double spread, int teamAReward, int teamBReward, string who, DateTime now) : base(id, question, tier, game, game.Favorite, shelve, index, teamAReward, teamBReward, who, now)
		{
			ValidateSpread(spread, base.game.Sport);
			if (string.IsNullOrWhiteSpace(question.Text)) throw new ArgumentNullException(nameof(question.Text));

			this.spread = spread;
			base.Text = ReplacePlaceholders(question.Text, game);
		}

		private SpreadLine(SpreadLine previousLine, int teamAReward, int teamBR<PERSON><PERSON>, string who, DateTime now) : base(previousL<PERSON>, team<PERSON><PERSON><PERSON>, team<PERSON><PERSON><PERSON>, who, now)
		{
			this.spread = previousLine.spread;

			if (!base.IsDraft && !base.IsCanceled)
			{
				var changedLineEvent = this.GetChangedLineEvent(now);
				PlatformMonitor.GetInstance().WhenNewEvent(changedLineEvent);
			}
		}

		protected override void ForcePublishedLineEvent(bool itIsThePresent, DateTime timestamp)
		{
			if (!base.IsPublished) throw new GameEngineException($"Line must be on publish visibility to send an event.");

			if (itIsThePresent)
			{
				var changedLineEvent = new PublishedSpreadLineEvent(timestamp, this);
				PlatformMonitor.GetInstance().WhenNewEvent(changedLineEvent);
			}
		}

		internal SpreadLine NewVersion(int teamAReward, int teamBReward, string who, DateTime now)
		{
			SpreadLine result = new SpreadLine(this, teamAReward, teamBReward, who, now);
			return result;
		}

		internal override decimal Grade(Wager wager)
		{
			if (Tier == Tier.TIER_ONE && !this.game.IsGameOver()) throw new GameEngineException($"This game {this.game.ToString()} can not be graded because it is not ended yet.");

			decimal prize = 0;

			var realSpread = Math.Abs(ScoreTeamA() - ScoreTeamB());
			if (realSpread == this.spread)
			{
				wager.ChangeToNoAction();
			}
			else
			{
				var answer = (ABAnswer)wager.ChosenAnswer;
				bool won = false;

				if (answer.ChosenTeam == base.TeamA)
				{
					if (base.RealAnswer.TeamAisWinner())
					{
						if (base.Favorite.IsTeamA() || base.Favorite.AreBoth())
						{
							int finalScoreTeamA = ScoreTeamA();
							if ((finalScoreTeamA - spread) > ScoreTeamB()) won = true;
						}
						else
						{
							won = true;
						}
					}
					else
					{
						int finalScoreTeamA = ScoreTeamA();
						if ((finalScoreTeamA + spread) < ScoreTeamB()) won = true;
					}
				}
				else if (answer.ChosenTeam == base.TeamB)
				{
					bool teamBIsWinner = !base.RealAnswer.TeamAisWinner();
					if (teamBIsWinner)
					{
						if (base.Favorite.IsTeamB() || base.Favorite.AreBoth())
						{
							int finalScoreTeamB = ScoreTeamB();
							if ((finalScoreTeamB - spread) > ScoreTeamA()) won = true;
						}
						else
						{
							won = true;
						}
					}
					else
					{
						int finalScoreTeamB = ScoreTeamB();
						if ((finalScoreTeamB + spread) < ScoreTeamA()) won = true;
					}
				}
				else
				{
					throw new GameEngineException($"Team {answer.ChosenTeam.Name} does not belong to game {this.game.ToString()}");
				}

				if (won)
				{
					prize = wager.ToWin();
					wager.GradeAsWinner();
				}
				else
				{
					wager.GradeAsLoser();
				}
			}
			wager.Showcase.RiskAssestment.UpdateGrade(wager);

			return prize;
		}

		public override string ToString()
		{
			string result = "";
			if (game.Favorite.IsTeamA()) result = $"-{spread} -{game.TeamA.Name} / +{spread} +{game.TeamB.Name}";
			if (game.Favorite.IsTeamB()) result = $"+{spread} +{game.TeamA.Name} / -{spread} -{game.TeamB.Name}";
			if (game.Favorite.AreBoth()) result = $"-{spread} -{game.TeamA.Name} / -{spread} -{game.TeamB.Name}";
			return result;
		}

		internal override string LineTypeAsString => LineType.SPREAD_LINE.ToString();

		internal override void Serialize(KafkaMessage message)
		{
			if (!game.IsGameOver() && !Showcase.IsThereAnyPendingLine()) throw new GameEngineException("Line should be serialized only to send to BI and the end of grades");
			if (!IsOriginalVersion()) PreviousVersion.Serialize(message);

			message.AddProperty((char)LineType.SPREAD_LINE);
			message.AddProperty(base.LineId);
			message.AddProperty(base.Version);
			message.AddProperty(spread);
			message.AddProperty(base.TeamAReward);
			message.AddProperty(base.TeamBReward);
			if (game.ScoreTeamA > game.ScoreTeamB)
			{
				message.AddProperty('A');
			}
			else
			{
				message.AddProperty('B');
			}
			message.AddProperty((int)base.Grading);
			message.AddProperty((int)base.BasedOn.Id);
		}

		internal double Spread
		{
			get
			{
				return this.spread;
			}
		}

		protected override LineEvent GetChangedLineEvent(DateTime timestamp)
		{
			var result = new ChangedSpreadLineEvent(timestamp, this);
			return result;
		}
	}
}
