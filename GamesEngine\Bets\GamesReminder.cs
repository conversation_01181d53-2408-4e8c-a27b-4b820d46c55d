﻿using GamesEngine.Games;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;

namespace GamesEngine.Bets
{
	internal class GamesReminder : Objeto
	{
		private GamesSet gamesSet = new GamesSet();

		internal void Remind(Game game)
		{
			if (game == null) throw new ArgumentNullException(nameof(game));

			gamesSet.Add(game);
		}

		internal void Remove(Game game)
		{
			if (game == null) throw new ArgumentNullException(nameof(game));

			gamesSet.Remove(game);
		}

		internal bool Contains(Game game)
		{
			if (game == null) throw new ArgumentNullException(nameof(game));

			return gamesSet.Contains(game);
		}

		internal IEnumerable<Game> NextTo(DateTime date)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");

			return gamesSet.NextTo(date);
		}

		internal IEnumerable<Game> FirstMatchInPreGame(DateTime date)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			
			List<Game> result = new List<Game>();
			var games = gamesSet.FirstMatchInPreGame(date);
			if ( games != null)
			{
				result.Add(games);
			}
			return result;
		}

		internal IEnumerable<Game> FirstMatchInPlay(DateTime date)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");

			List<Game> result = new List<Game>();
			var games = gamesSet.FirstMatchInPlay(date);
			if (games != null)
			{
				result.Add(games);
			}
			return result;
		}

	}
}
