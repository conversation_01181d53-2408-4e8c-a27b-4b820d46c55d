﻿using GamesEngine.Domains;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;

namespace GamesEngine.Bets
{
    [Puppet]
    internal class BetRange: Objeto
    {
        private decimal maxAmount;
        private decimal minAmount;
        private Domain domain;
        private string log;
        private string whoEnteredLastChange;
        private DateTime dateLastChange;

        internal decimal MaxAmount
        {
            get
            {
                return maxAmount;
            }
        }

        internal decimal MinAmount
        {
            get
            {
                return minAmount;
            }
        }

        internal Domain Domain
        {
            get
            {
                return domain;
            }
        }

        internal string Log
        {
            get
            {
                return log;
            }
        }

        internal string WhoEnteredLastChange
        {
            get
            {
                return whoEnteredLastChange;
            }
        }

        internal DateTime DateLastChange
        {
            get
            {
                return dateLastChange;
            }
        }

        internal BetRange(Domain domain, decimal maxAmount, decimal minAmount)
        {
            this.domain = domain;
            this.maxAmount = maxAmount;
            this.minAmount = minAmount;
        }

        internal void Update(Domain domain, decimal maxAmount, decimal minAmount)
        {
            this.domain = domain;
            this.maxAmount = maxAmount;
            this.minAmount = minAmount;
        }

        internal void AddLog(DateTime now, Domain domain, decimal maxAmount, decimal minAmount, string employeeName)
        {
            dateLastChange = now;
            whoEnteredLastChange = employeeName;
            log = $"{employeeName} entered maximum amount {maxAmount} and minimum amount {minAmount} for domain '{domain.Url}' at {now}<br>" + log;
        }
    }
}
