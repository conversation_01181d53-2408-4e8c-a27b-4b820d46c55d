﻿using ExternalServices;
using GamesEngine.Finance;
using GamesEngine.Gameboards.Lotto;
using GamesEngine.Games.Lotto;
using GamesEngine.Location;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using town.connectors.commons;
using town.connectors.drivers.artemis;
using static Connectors.town.connectors.drivers.artemis.Fragment_an_Authorization;
using FragmentResponse = Connectors.town.connectors.drivers.artemis.Fragment_an_Authorization.FragmentResponse;

namespace GamesEngine.Business
{
    public class ArtemisBehavior
    {
        public static string CommandsForPurchaseValidation(IdOfLotteryGame lotteryGameType, string gameType, int pickNumber, string strAmount, string dates, string states, string hours, string withFireBalls, 
            string strIsPresentPlayerBeforeToCloseStore, string strUseNextDate, string selectionMode, string includedSubticketsForInput, string strRuleType, string currencyCode, string gameTypeKey, bool applyToleranceFactor, string issued)
        {
            //TRIZ SOLUCIONAR el IsBetAmountValidFor
            var result = $@"
                    lotteryGame = company.LotteryGamesPool.GetLotteryGame({lotteryGameType});
                    //company.LotteryGamesPool.IsBetAmountValidFor({pickNumber}, player, domain, {strAmount});
                    nextDatesAccumulator = NextDatesAccumulator(lotteryGame, domain, Now, '{gameType}', '{dates}', '{states}', '{hours}', '{withFireBalls}', '{strIsPresentPlayerBeforeToCloseStore}', '{strUseNextDate}');
                    riskAccumulator = ToWinAccumulator();
                    totalOrderAndExclusionsByToWin = TotalOrderByToWin();
                    totalOrderAndExclusionsByToWin = company.TotalTicketOrderAndExclusionsByToWin(domain, '{states}', '{dates}', '{withFireBalls}', '{selectionMode}', {strAmount}, {{'{includedSubticketsForInput}'}}, '{strRuleType[0]}', excludedSubtickets, ticketPick{pickNumber}{strRuleType}Prod, nextDatesAccumulator, riskAccumulator, totalOrderAndExclusionsByToWin, '{currencyCode}', '{gameTypeKey}', {applyToleranceFactor}, '{issued}', Now);
                ";
            return result;
        }

        public static string CommandsForPurchaseValidationWithExclusions(char pickNumber, string strAmount, string dates, string states, string hours, string strIsPresentPlayerBeforeToCloseStore, string strUseNextDate, string selectionMode, string includedSubticketsForInput, string strRuleType, Dictionary<ExcludedSubticketKey, GroupedExcludedSubticket> dictExcludeSubticket, string currencyCode)
        {
            var scriptForExclusions = new StringBuilder();
            foreach (var groupedExcludedSubticket in dictExcludeSubticket)
            {
                var excludedNumbers = groupedExcludedSubticket.Value.Subtickets;
                scriptForExclusions.AppendLine($"excludedSubtickets.Add(state{groupedExcludedSubticket.Key.State},'{groupedExcludedSubticket.Key.Hour}','{groupedExcludedSubticket.Value.Dates}','{excludedNumbers}');");
            }

            var result = $@"
                    lotto900.IsBetAmountValidFor({pickNumber}, player, domain, {strAmount});
                    nextDatesAccumulator = NextDatesAccumulator(lotto900, domain, Now, '{pickNumber}', '{dates}','{states}','{hours}', '{strIsPresentPlayerBeforeToCloseStore}', '{strUseNextDate}');
                    riskAccumulator = ToWinAccumulator();
                    excludedSubtickets = ExcludeSubtickets();
                    {scriptForExclusions}
                    totalOrderAndExclusionsByToWin = TotalOrderByToWin();
                    totalOrderAndExclusionsByToWin = company.TotalTicketOrderAndExclusionsByToWin(domain, '{states}', '{dates}', '', '{selectionMode}', {strAmount}, '{includedSubticketsForInput}', '{strRuleType[0]}', excludedSubtickets, ticketPick{pickNumber}{strRuleType}Prod, nextDatesAccumulator, riskAccumulator, totalOrderAndExclusionsByToWin, '{currencyCode}');
                ";
            return result;
        }

        public static string CommandsForOrder(char pickNumber, string strAmount, string dates, string states, string hours, string strIsPresentPlayerBeforeToCloseStore, string strUseNextDate, string selectionMode, string includedSubticketsForInput, string strRuleType)
        {
            var result = $@"
                    lotto900.IsBetAmountValidFor({pickNumber}, player, domain, {strAmount});
                    nextDatesAccumulator = NextDatesAccumulator(lotto900, domain, Now, '{pickNumber}', '{dates}','{states}','{hours}', '{strIsPresentPlayerBeforeToCloseStore}', '{strUseNextDate}');
                    riskAccumulator = ToWinAccumulator();
                    myOrder = company.CreateTicketFullOrder(player, '{states}', '{dates}', '', '{selectionMode}', {strAmount}, '{includedSubticketsForInput}', '{strRuleType[0]}', myOrder, ticketPick{pickNumber}{strRuleType}Prod, nextDatesAccumulator, domain);
                ";
            return result;
        }

        public static string CommandsForOrderWithExclusions(char pickNumber, string strAmount, string dates, string states, string hours, string strIsPresentPlayerBeforeToCloseStore, string strUseNextDate, string selectionMode, string includedSubticketsForInput, string strRuleType, Dictionary<ExcludedSubticketKey, GroupedExcludedSubticket> dictExcludeSubticket)
        {
            var scriptForExclusions = new StringBuilder();
            foreach (var groupedExcludedSubticket in dictExcludeSubticket)
            {
                var excludedNumbers = groupedExcludedSubticket.Value.Subtickets;
                scriptForExclusions.AppendLine($"excludedSubtickets.Add(state{groupedExcludedSubticket.Key.State},'{groupedExcludedSubticket.Key.Hour}','{groupedExcludedSubticket.Value.Dates}','{excludedNumbers}');");
            }

            var result = $@"
                    lotto900.IsBetAmountValidFor({pickNumber}, player, domain, {strAmount});
                    nextDatesAccumulator = NextDatesAccumulator(lotto900, domain, Now, '{pickNumber}', '{dates}','{states}','{hours}', '{strIsPresentPlayerBeforeToCloseStore}', '{strUseNextDate}');
                    riskAccumulator = ToWinAccumulator();
                    excludedSubtickets = ExcludeSubtickets();
                    {scriptForExclusions}
                    myOrder = company.CreateTicketFullOrder(player, '{states}', '{dates}', '', '{selectionMode}', {strAmount}, '{includedSubticketsForInput}', excludedSubtickets, '{strRuleType[0]}', myOrder, ticketPick{pickNumber}{strRuleType}Prod, nextDatesAccumulator, domain);
                ";
            return result;
        }

        public static string CommandsToPrintExternalTicketData()
        {
            var result = $@"
                for (tickets : totalOrderAndExclusionsByToWin.Tickets)
                {{
                    ticket = tickets;
                    print ticket.drawDate drawDate;
                    print ticket.drawHour drawHour;
                    print ticket.toWin toWin;
                    print ticket.risk risk;
                    print ticket.pick pick;
                    print ticket.number number;
                    print ticket.draw draw;
                    print ticket.type type;
                    print ticket.description description;
                    print ticket.freePlay freePlay;
                }}
            ";
            return result;
        }

        public static void SendFragmentsToUpdate(IEnumerable<PayFragmentsMessage> payFragmentsMsg, bool sometimeWereTicketsSentToAccounting)
        {
            var message = new FragmentPaymentSendingToAgentMessages(payFragmentsMsg.ToList());
            if (sometimeWereTicketsSentToAccounting)
            {
                Integration.Kafka.Send(true, $"{Integration.Kafka.TopicForFragmentPaymentsForAll}{KafkaMessage.SENDING_UPDATED_FRAGMENTS_CONSUMER_SUFFIX}", message);
            }
            else
            {
                Integration.Kafka.Send(true, $"{Integration.Kafka.TopicForFragmentPaymentsForAll}{KafkaMessage.SENDING_FRAGMENTS_CONSUMER_SUFFIX}", message);
            }
        }

        internal static void ReplicateInOtherNodes(bool itIsThePresent, DateTime now, string defaultNickNameIfNotExists, string defaultAvatarIfNotExists, int affiliateId, string affiliateName, Customer customer)
        {
            if (Integration.UseKafka || Integration.UseKafkaForAuto)
            {
                if (customer.Company.Sales.HasCurrentStore())
                {
                    Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForCustomers, new NewArtemisCustomerMessage(
                        customer.Identifier,
                        customer.AccountNumber, 
                        defaultNickNameIfNotExists, 
                        defaultAvatarIfNotExists,
                        customer.Company.Sales.CurrentStore.Id, 
                        now, 
                        (int)customer.Player.Agent,
                        affiliateId,
                        affiliateName
                        )
                    );
                }
                else
                {
                    Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForCustomers, new NewArtemisCustomerMessage(
                        customer.Identifier,
                        customer.AccountNumber, 
                        defaultNickNameIfNotExists, 
                        defaultAvatarIfNotExists, 
                        now, 
                        (int)customer.Player.Agent,
                        affiliateId,
                        affiliateName
                        )
                    );
                }
            }
        }

        public class NewArtemisCustomerMessage : NewCustomerMessage
        {
            public int AffiliateId { get; private set; }

            public string AffiliateName { get; private set; }

            public NewArtemisCustomerMessage(string customerIdentifier, string accountNumber, string defaultNickNameIfNotExists, string defaultAvatarIfNotExists, DateTime visitDate, int agentId, int affiliateId, string affiliateName)
                : this(customerIdentifier, accountNumber, defaultNickNameIfNotExists, defaultAvatarIfNotExists, CustomerMessage.WITHOUT_STORE, visitDate, agentId, affiliateId, affiliateName)
            {
            }

            public NewArtemisCustomerMessage(string customerIdentifier, string accountNumber, string defaultNickNameIfNotExists, string defaultAvatarIfNotExists, int storeId, DateTime visitDate, int agentId, int affiliateId, string affiliateName)
                : base(customerIdentifier, accountNumber, defaultNickNameIfNotExists, defaultAvatarIfNotExists, storeId, visitDate, agentId)
            {
                AffiliateId = affiliateId;
                AffiliateName = affiliateName;
            }

            public NewArtemisCustomerMessage(string serialized) : base(serialized)
            {
            }

            protected override void InternalSerialize()
            {
                base.InternalSerialize();
                AddProperty(AffiliateId).
                AddProperty(AffiliateName);
            }

            protected override void Deserialize(string[] message, out int fieldOrder)
            {
                base.Deserialize(message, out fieldOrder);
                AffiliateId = int.Parse(message[fieldOrder++]);
                AffiliateName = message[fieldOrder++];
            }
        }
    }
}
