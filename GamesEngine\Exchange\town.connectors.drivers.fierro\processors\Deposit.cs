﻿using Connectors.town.connectors.driver.transactions;
using GamesEngine;
using GamesEngine.Finance;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

using static town.connectors.drivers.Result;

namespace town.connectors.drivers.fiero
{
    internal class Deposit : FierroProcessorDriver
	{
		private const float VERSION = 1.1F;
		public override string Description => $"Fiero {nameof(Deposit)} driver {VERSION}";

		public Deposit()
			: base(TransactionType.Deposit, VERSION)
        {
        }
		public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			throw new NotImplementedException();
		}
		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			var result = AddAmount(now, recordSet);
			DepositTransaction auth;
			if (result > 0)
			{
				auth = new DepositTransaction(result, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes);
			}
			else
			{
				auth = new DepositTransaction(result, TransactionStatus.DENIED, Entity, PaymentMethod, CurrencyIsoCodes);
			}
			return (T)Convert.ChangeType(auth, typeof(T));

		}

		public override void Prepare(DateTime now)
		{
			CustomSettings.AddVariableParameter("actor");
			CustomSettings.AddVariableParameter("message");

			//CustomSettings.Prepare();
		}

		int MakeADeposit(DepositMessage message)
		{
			if (message == null) throw new ArgumentNullException(nameof(message));

			var authorization = Movements.Storage.NextAuthorizationNumber();

			return authorization;
		}

        const int DaysToBecomeUseless = 7;
        private int AddAmount(DateTime now, CustomSettings.RecordSet recordSet)
		{
			var Actor = recordSet.Mappings["actor"];
			var Message = recordSet.Mappings["message"];
			var actor = Actor.As<RestAPISpawnerActor>();
			DepositMessage message = Message.As<DepositMessage>();

			var description = Validator.StringEscape(message.Description);
			Currency amount = Currency.Factory(message.Currency, message.Amount);
			string command = "";
			int authorization = MakeADeposit(message);
            var normalizedUseless = now.AddDays(DaysToBecomeUseless).ToString("MM/dd/yyyy HH:mm:ss");
            if (message.HasSource())
			{
				command = $@"
							{{
								source{message.SourceNumber} = atAddress.GetOrCreateSource(itIsThePresent, now, {message.SourceNumber}, '{message.Currency}', '{message.SourceName}');
	                            balance = atAddress.CreateAccountIfNotExists('{message.Currency}', '{message.AccountNumber}').Balance;
								Eval('available = '+balance.Available+';');
								balance.SetInitialBalance(itIsThePresent, available);
								store = company.Sales.StoreById({message.StoreId.ToString()});
								source{message.SourceNumber}.Accredit(itIsThePresent, Now, Currency('{message.Currency}',{message.Amount}), '{message.Who}', '{authorization}', store, '{description}', '{message.Reference}', '{message.AccountNumber}', {message.ProcessorId});
								authorization = atAddress.CreateFakeAuthorizationWithoutLock('{amount.CurrencyCode}', {authorization}, '{message.AccountNumber}', {normalizedUseless});
								authorization.CreateFakeFragment('{message.Reference}', '{description}', {amount.Value});
								atAddress.PrepareFakePayment(authorization, Now, store, {FragmentReason.Winner}).ApplyChanges(itIsThePresent, '{message.Who}');
							}}
						";
			}
			else if (!string.IsNullOrEmpty(message.AccountNumber))
			{
				command = $@"
							{{
								balance = atAddress.CreateAccountIfNotExists('{message.Currency}', '{message.AccountNumber}').Balance;
								Eval('available = '+balance.Available+';');
								balance.SetInitialBalance(itIsThePresent, available);
								store = company.Sales.StoreById({message.StoreId});
								balance.Accredit(itIsThePresent, Now, Currency('{amount.CurrencyCode}',{amount.Value}), '{message.Who}', '{authorization}', store, '{description}', '{message.Reference}', {message.ProcessorId});
								authorization = atAddress.CreateFakeAuthorizationWithoutLock('{amount.CurrencyCode}', {authorization}, '{message.AccountNumber}', {normalizedUseless});
								authorization.CreateFakeFragment('{message.Reference}', '{description}', {amount.Value});
								atAddress.PrepareFakePayment(authorization, Now, store, {FragmentReason.Winner}).ApplyChanges(itIsThePresent, '{message.Who}');
							}}";
			}
			else
			{
				command = $@"
							{{
								balance = atAddress.CreateBalanceIfNotExists('{message.Currency}');
								Eval('available = '+balance.Available+';');
								balance.SetInitialBalance(itIsThePresent, available);
								store = company.Sales.StoreById({message.StoreId});
								balance.Accredit(itIsThePresent, Now, Currency('{amount.CurrencyCode}',{amount.Value}), '{message.Who}', '{authorization}', store, '{description}', '{message.Reference}', {message.ProcessorId});
								authorization = atAddress.CreateFakeAuthorizationWithoutLock('{amount.CurrencyCode}', {authorization}, '{message.AccountNumber}', {normalizedUseless});
								authorization.CreateFakeFragment('{message.Reference}', '{description}', {amount.Value});
								atAddress.PrepareFakePayment(authorization, Now, store, {FragmentReason.Winner}).ApplyChanges(itIsThePresent, '{message.Who}');
							}}";
			}

			var result = actor.PerformCmd(message.AtAddress, command);
			if (!(result is OkObjectResult))
			{
				throw new GameEngineException($"Deposit was not saved because command fails on cashier.");
			}

			return authorization;
		}

	}
}
