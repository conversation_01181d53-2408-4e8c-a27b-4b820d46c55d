﻿using Connectors.town.connectors.driver.transactions;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;
using town.connectors.commons;
using static town.connectors.drivers.Result;

namespace town.connectors.drivers.fiero
{
    public class Authorization : FierroProcessorDriver
	{
		public const int FAKE_TICKET_NUMBER = -1;
		private const float VERSION = 1.0F;

		public string CashierUrl { get; private set; }
		public override string Description => $"Fiero {nameof(Authorization)} driver {VERSION}";

		public Authorization()
			: base(TransactionType.Sale, VERSION)
        {
        }

		public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			var result = await AuthorzationAsync(now, recordSet);
			AuthorizationTransaction auth;
			if (result > 0)
			{
				auth= new AuthorizationTransaction(result, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes);
				return (T)Convert.ChangeType(auth, typeof(T));
			}
			auth= new AuthorizationTransaction(result, TransactionStatus.DENIED, Entity, PaymentMethod, CurrencyIsoCodes);
			return (T)Convert.ChangeType(auth, typeof(T));
		}

		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			throw new NotImplementedException();
		}

		public override void Prepare(DateTime now)
		{
			CustomSettings.AddVariableParameter("accountNumber");
			CustomSettings.AddVariableParameter("atAddress");
			CustomSettings.AddVariableParameter("purchaseTotal.Value");
			CustomSettings.AddVariableParameter("purchaseTotal.CurrencyCode");
			CustomSettings.AddVariableParameter("storeId");
			CustomSettings.AddVariableParameter("concept");
			CustomSettings.AddVariableParameter("fragmentInformation");
			CustomSettings.AddVariableParameter("referenceNumber");
            CustomSettings.AddVariableParameter("useless");
            CustomSettings.AddVariableParameter("context");

			CashierUrl = CustomSettings.Get(now, "CashierDriver.url").AsString;
		}
		private async Task<int> AuthorzationAsync(DateTime now, CustomSettings.RecordSet recordSet)
		{

			var AccountNumber = recordSet.Mappings["accountNumber"];
			var AtAddress = recordSet.Mappings["atAddress"];
			var PurchaseTotalValue = recordSet.Mappings["purchaseTotal.Value"];
			var PurchaseTotalCurrencyCode = recordSet.Mappings["purchaseTotal.CurrencyCode"];
			var StoreId = recordSet.Mappings["storeId"];
			var Concept = recordSet.Mappings["concept"];
			var FragmentInformation = recordSet.Mappings["fragmentInformation"];
			var ReferenceNumber = recordSet.Mappings["referenceNumber"];
			var Useless = recordSet.Mappings["useless"];
			var Context = recordSet.Mappings["context"];

			string accountNumber = AccountNumber.AsString;
			string atAddress = AtAddress.AsString;
			decimal purchaseTotal = PurchaseTotalValue.AsDecimal;
			
			var lockBalanceData = new LockBalanceData();
			lockBalanceData.AtAddress = atAddress;
			lockBalanceData.AccountNumber = accountNumber;
			lockBalanceData.PurchaseTotal = purchaseTotal;
			lockBalanceData.CurrencyCode = PurchaseTotalCurrencyCode.AsString;
			lockBalanceData.StoreId = StoreId.AsInt;
			lockBalanceData.Concept = Concept.AsString;
			if (recordSet.ContainsKeyName("fragmentInformation"))
				lockBalanceData.FragmentInformation = FragmentInformation.As<FragmentInformation>();
			if (recordSet.ContainsKeyName("referenceNumber"))
				lockBalanceData.Reference = ReferenceNumber.AsString;
            lockBalanceData.Useless = Useless.AsDateTime;
            HttpContext context = Context.As<HttpContext>();

			GamesEngine.Finance.LockBalanceResponse lockBalanceResponse;
			try
			{
				var url = $"{CashierUrl}api/customers/{atAddress}/balance/lock";
				HttpRestClientConfiguration restClientToCashier = HttpRestClientConfiguration.GetInstance();
				var resultFromCashier = await restClientToCashier.PostAsync(context, url, lockBalanceData);
				if (!(resultFromCashier is OkObjectResult))
				{
					string body = string.Empty;
					if (resultFromCashier is ContentResult)
					{
						body = $@"error:{((ObjectResult)resultFromCashier).ToString()} \n url:{url} \n accountNumber:{accountNumber} \n purchaseTotal:{purchaseTotal}";
					}
					else
					{
						body = $@"url:{url} \n accountNumber:{accountNumber} \n purchaseTotal:{purchaseTotal}";
					}
					ErrorsSender.Send(body, $@"Lock amount to {accountNumber} fails.");
					//return new DespositResponse(ExternalServices.ServicesIntegration.FAKE_TICKET_NUMBER, ExecutionStatus.REJECTED);
					return FAKE_TICKET_NUMBER;
				}

				string resp = ((OkObjectResult)resultFromCashier).Value.ToString();
				lockBalanceResponse = JsonConvert.DeserializeObject<GamesEngine.Finance.LockBalanceResponse>(resp);
			}
			catch (Exception e)
			{
				ErrorsSender.Send(e);
				//return new DespositResponse(ExternalServices.ServicesIntegration.FAKE_TICKET_NUMBER, ExecutionStatus.REJECTED);
				return FAKE_TICKET_NUMBER;
			}

			return lockBalanceResponse.AuthorizationNumber;
		}

	}
}
