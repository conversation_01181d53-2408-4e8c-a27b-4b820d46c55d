﻿using ExchangeAPI.ExchangeEntities;
using GamesEngine;
using GamesEngine.Exchange.Persistance;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ExchangeAPI.Logic
{
    public class ExchangeRates : IExchangeRates
    {
        public static ExchangeContext ContextForTesting 
        { 
            get;
#if DEBUG
            set;
#endif
        }

        public static IEnumerable<Exchangerate> CurrentExchangeRates()
        {
            using (var context = ContextForTesting ?? new ExchangeContext())
            {
                var exchangeRates = context.Exchangerate
                    .Include(rate => rate.Fromcurrency)
                    .Include(rate => rate.Tocurrency)
                    .Where(rate => ! rate.Deleted)
                    .OrderByDescending(rate => rate.Date)
                    .AsEnumerable()
                    .GroupBy(rate => new { rate.Fromcurrencyid, rate.Tocurrencyid})
                    .Select(group => group.First())
                    .ToList();
                return exchangeRates;
            }
        }

        public static Exchangerate CurrentExchangeRateFor(int fromCurrencyid, int toCurrencyid)
        {
            if (fromCurrencyid < 0) throw new ArgumentNullException(nameof(fromCurrencyid));
            if (toCurrencyid < 0) throw new ArgumentNullException(nameof(toCurrencyid));

            using (var context = ContextForTesting ?? new ExchangeContext())
            {
                var exchangeRate = context.Exchangerate
                    .Include(rate => rate.Fromcurrency)
                    .Include(rate => rate.Tocurrency)
                    .Where(rate => ! rate.Deleted && rate.Fromcurrency.Id == fromCurrencyid && rate.Tocurrency.Id == toCurrencyid)
                    .OrderByDescending(rate => rate.Date)
                    .FirstOrDefault();
                return exchangeRate;
            }
        }

        public Exchangerate CurrentExchangeRateFor(string fromCurrencyCode, string toCurrencyCode, DateTime date)
        {
            if (string.IsNullOrWhiteSpace(fromCurrencyCode)) throw new ArgumentNullException(nameof(fromCurrencyCode));
            if (string.IsNullOrWhiteSpace(toCurrencyCode)) throw new ArgumentNullException(nameof(toCurrencyCode));

            using (var context = ContextForTesting ?? new ExchangeContext())
            {
                var exchangeRate = context.Exchangerate
                    .Include(rate => rate.Fromcurrency)
                    .Include(rate => rate.Tocurrency)
                    .Where(rate => !rate.Deleted && rate.Fromcurrency.Isocode == fromCurrencyCode && rate.Tocurrency.Isocode == toCurrencyCode && rate.Date >= date)
                    .OrderBy(rate => rate.Date)
                    .FirstOrDefault();
                return exchangeRate;
            }
        }
        public static decimal Convert(int fromCurrencyCode, int toCurrencyCode, decimal amount)
        {
            if (fromCurrencyCode < 0) throw new ArgumentNullException(nameof(fromCurrencyCode));
            if (toCurrencyCode < 0) throw new ArgumentNullException(nameof(toCurrencyCode));
            if (amount <= 0) throw new GameEngineException($"{nameof(amount)} must be greater than 0");

            using (var context = ContextForTesting ?? new ExchangeContext())
            {
                var exchangeRate = CurrentExchangeRateFor(fromCurrencyCode, toCurrencyCode);
                if (exchangeRate == null) throw new ArgumentNullException(nameof(exchangeRate));

                return amount * exchangeRate.Purchaseprice;
            }
        }

        public void Save(Exchangerate exchangeRate)
        {
            if (exchangeRate == null) throw new ArgumentNullException(nameof(exchangeRate));

            using (var context = ContextForTesting ?? new ExchangeContext())
            {
                var lastExchangeRate = CurrentExchangeRateFor(exchangeRate.Fromcurrencyid, exchangeRate.Tocurrencyid);
                if (lastExchangeRate != null)
                {
                    lastExchangeRate.Deleted = true;
                    context.Exchangerate.Update(lastExchangeRate);
                }

                context.Exchangerate.Add(exchangeRate);
                context.SaveChanges();
            }
        }
    }
}
