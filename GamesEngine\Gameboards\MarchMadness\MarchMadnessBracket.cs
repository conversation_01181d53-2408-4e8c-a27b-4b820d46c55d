﻿using GamesEngine.Games;
using GamesEngine.Games.Tournaments;
using GamesEngine.Messaging;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using System.Text;

namespace GamesEngine.Gameboards.MarchMadness
{
	[System.Diagnostics.DebuggerDisplay("{ToString()}")]
	internal sealed class MarchMadnessBracket : Bracket
	{
		private int grade;
		private BigInteger gradeToUntie;
		internal delegate void ChangeEvent(Gameboard board);
		internal event ChangeEvent OnScoreChange;

		/// <summary>
		/// This constructor is to create a Bracket for Brackets seasson.
		/// </summary>
		///<exception cref="tournament">
		/// The tournament does not have to be over 
		/// and the final round does not have started yet.
		/// <exception>
		/// <param name="tournament"></param>
		internal MarchMadnessBracket(Bets.Player player, string name, MarchMadnessTournament tournament, DateTime creationDate) : base(player, name, tournament, creationDate)
		{
			//if (tournament.IsEnded() || tournament.IsReadyToPay()) throw new GameEngineException($"The tournament {tournament.Year} is over.");
			//if (!tournament.IsStarted()) throw new GameEngineException("The tournament has not started");
			//var currentRound = tournament.CurrentRound();
			//if (currentRound.IsStarted() || currentRound.IsClosed()) throw new GameEngineException("New brackets can not be created if the final round already begins.");
			//var rules = tournament.Tournaments.Company.GetMarchMadnessEdition(tournament.Year);
			//if (!rules.HasProductsToSell(currentRound)) throw new GameEngineException("Predictions are only valid on Rounds of 64, 32 and 16.");
			//tournament.GamesFrom(currentRound).AddOnChangeToAllGames(OnScoreUpdate);
		}

		public override decimal Grade()
		{
			return this.grade;
		}

		public override BigInteger GradeToUntie()
		{
			return this.gradeToUntie;
		}

		public int Year
		{
			get
			{
				return ((MarchMadnessTournament)this.Tournament).Year;
			}
		}

		internal override String Name
		{
			get
			{
				return base.Name;
			}
			set
			{
				base.Name = value;
				if (OnScoreChange != null) OnScoreChange(this);
			}
		}

		internal override Predictions Predictions
		{
			get
			{
				return base.Predictions;
			}
		}

		internal bool CanChange()
		{
			bool isReadOnly = InitialRoundOfPredictions() != Tournament.CurrentRound() ||
				InitialRoundOfPredictions().IsStarted() ||
				InitialRoundOfPredictions().IsClosed();
			return !isReadOnly;
		}

		private BigInteger UntieCriteria2019()
		{
			Game game = this.Tournament.GetGameNumber(63);
			ScorePair prediction = this.Predictions.PredictionOf(63).Scores;

			if (game == null) throw new ArgumentNullException(nameof(game));
			if (!game.Scores.IsUsingPoints() || !prediction.IsUsingPoints()) throw new GameEngineException("Last game must use points.");

			var officialPoints = game.Scores.ScoreTeamA.Points + game.Scores.ScoreTeamB.Points;
			var predictionPoints = prediction.ScoreTeamA.Points + prediction.ScoreTeamB.Points;
			return Math.Abs(officialPoints - predictionPoints);
		}

		private BigInteger UntieCriteriaFrom2020()
		{
			Game game = this.Tournament.GetGameNumber(63);
			ScorePair prediction = this.Predictions.PredictionOf(63).Scores;

			if (game == null) throw new ArgumentNullException(nameof(game));
			if (!game.Scores.IsUsingPoints() || !prediction.IsUsingPoints()) throw new GameEngineException("Last game must use points.");

			var officialPoints = game.Scores.ScoreTeamA.Points + game.Scores.ScoreTeamB.Points;
			var predictionPoints = prediction.ScoreTeamA.Points + prediction.ScoreTeamB.Points;
			BigInteger dimensionOfDistance;
			if (predictionPoints > officialPoints)
				dimensionOfDistance = 1;
			else
				dimensionOfDistance = 0;

			dimensionOfDistance = dimensionOfDistance * 1000 + System.Math.Abs(officialPoints - predictionPoints);


			BigInteger dimensionWinnerPredictionOverOrUnder;
			int scoreOfWinner;
			int scoreOfWinnerPrediction;
			if (game.Scores.ScoreTeamA.Points >= game.Scores.ScoreTeamB.Points)
			{
				scoreOfWinner = game.Scores.ScoreTeamA.Points;
				scoreOfWinnerPrediction = prediction.ScoreTeamA.Points;
			}
			else
			{
				scoreOfWinner = game.Scores.ScoreTeamB.Points;
				scoreOfWinnerPrediction = prediction.ScoreTeamB.Points;
			}
			if (scoreOfWinnerPrediction > scoreOfWinner)
				dimensionWinnerPredictionOverOrUnder = 1;
			else
				dimensionWinnerPredictionOverOrUnder = 0;
			dimensionWinnerPredictionOverOrUnder = dimensionWinnerPredictionOverOrUnder * 1000 + System.Math.Abs(scoreOfWinner - scoreOfWinnerPrediction);

			BigInteger index = dimensionOfDistance * 10000 +
							dimensionWinnerPredictionOverOrUnder;
			return index;
		}

		private BigInteger UntieCriteriaForFinalFourChallenge()
		{
			Game game63 = this.Tournament.GetGameNumber(63);
			GameInThisPrediction prediction63 = this.Predictions.PredictionOf(63);

			ScorePair predictedScore63 = prediction63.Scores;

			//Criteria # 1
			var officialPoints = game63.Scores.ScoreTeamA.Points + game63.Scores.ScoreTeamB.Points;
			var predictionPoints = predictedScore63.ScoreTeamA.Points + predictedScore63.ScoreTeamB.Points;
			BigInteger dimensionOfDistance;
			if (predictionPoints > officialPoints)
				dimensionOfDistance = 1;
			else
				dimensionOfDistance = 0;

			dimensionOfDistance = dimensionOfDistance * 1000 + System.Math.Abs(officialPoints - predictionPoints);

			//Criteria # 2
			BigInteger dimensionWinnerPredictionOverOrUnder;
			int scoreOfWinner;
			int scoreOfWinnerPrediction;
			if (game63.Scores.ScoreTeamA.Points >= game63.Scores.ScoreTeamB.Points)
			{
				scoreOfWinner = game63.Scores.ScoreTeamA.Points;
				scoreOfWinnerPrediction = predictedScore63.ScoreTeamA.Points;
			}
			else
			{
				scoreOfWinner = game63.Scores.ScoreTeamB.Points;
				scoreOfWinnerPrediction = predictedScore63.ScoreTeamB.Points;
			}
			if (scoreOfWinnerPrediction > scoreOfWinner)
				dimensionWinnerPredictionOverOrUnder = 1;
			else
				dimensionWinnerPredictionOverOrUnder = 0;
			dimensionWinnerPredictionOverOrUnder = dimensionWinnerPredictionOverOrUnder * 1000 + System.Math.Abs(scoreOfWinner - scoreOfWinnerPrediction);

			Game game61 = this.Tournament.GetGameNumber(61);
			Game game62 = this.Tournament.GetGameNumber(62);
			ScorePair predictedScore61 = this.Predictions.PredictionOf(61).Scores;
			ScorePair predictedScore62 = this.Predictions.PredictionOf(62).Scores;

			if (game61 == null) throw new ArgumentNullException(nameof(game61));
			if (game62 == null) throw new ArgumentNullException(nameof(game62));
			if (!game61.Scores.IsUsingPoints() || !predictedScore61.IsUsingPoints()) throw new GameEngineException("Semifinal game 61 must use points.");
			if (!game62.Scores.IsUsingPoints() || !predictedScore62.IsUsingPoints()) throw new GameEngineException("Semifinal game 62 must use points.");


			//Criteria # 3
			officialPoints = game61.Scores.ScoreTeamA.Points + game61.Scores.ScoreTeamB.Points +
								game62.Scores.ScoreTeamA.Points + game62.Scores.ScoreTeamB.Points;
			predictionPoints = predictedScore61.ScoreTeamA.Points + predictedScore61.ScoreTeamB.Points +
								predictedScore62.ScoreTeamA.Points + predictedScore62.ScoreTeamB.Points;

			BigInteger dimensionOfDistanceRegions;
			if (predictionPoints > officialPoints)
				dimensionOfDistanceRegions = 1;
			else
				dimensionOfDistanceRegions = 0;

			dimensionOfDistanceRegions = dimensionOfDistanceRegions * 1000 + System.Math.Abs(officialPoints - predictionPoints);

			//Criteria # 4
			BigInteger dimensionMatchFinal;
			if (game63.TeamA == prediction63.TeamA && game63.TeamB == prediction63.TeamB)
			{
				dimensionMatchFinal = 0;
			}
			else
			{
				dimensionMatchFinal = 2;
			}

			//Criteria # 5
			BigInteger dimensionRightWinner;
			if (game63.Scores.ScoreTeamA.Points >= game63.Scores.ScoreTeamB.Points)
			{
				if (prediction63.Scores.ScoreTeamA.Points >= prediction63.Scores.ScoreTeamB.Points)
					dimensionRightWinner = 0;
				else
					dimensionRightWinner = game63.TeamA == prediction63.TeamA ? 0 : 1;
			}
			else
			{
				if (prediction63.Scores.ScoreTeamA.Points < prediction63.Scores.ScoreTeamB.Points)
					dimensionRightWinner = 0;
				else
					dimensionRightWinner = game63.TeamB == prediction63.TeamB ? 0 : 1;
			}

			BigInteger index = dimensionOfDistance * 10000 * 10000 * 10 * 10 +
							dimensionWinnerPredictionOverOrUnder * 10000 * 10 * 10 +
							dimensionOfDistanceRegions * 10 * 10 + dimensionMatchFinal * 10 +
							dimensionRightWinner;
			return index;
		}

		private int PointsCriteria(Game game, ScorePair officialScore, ScorePair predictedScore)
		{
			if (game == null) throw new ArgumentNullException(nameof(game));
			int points = 0;
			if (predictedScore.HasTheSameWinnerThan(officialScore))
			{
				points = ((MarchMadnessTournament)Tournament).PointsCriteria(game);
			}
			return points;
		}

		//TODO: Este debe usar
		[Obsolete("El MM19 recibe sin parametro y al MM20 hay que pasarle el parametro")]
		private const bool IT_NEEDS_TO_RECEIVE_ItIsThePresent_AS_PARAMETER = true;
		internal Bracket Replicate()
		{
			if (Tournament.IsEnded() || Tournament.IsReadyToPay()) throw new GameEngineException("Can not replicate a Bracket which tournament is over.");
			if (!CanChange()) throw new GameEngineException("Tournament has progressed to a phase where this bracket can be replicated");


			Bracket clone = ((MarchMadnessTournament)this.Tournament).NewMarchMadnessBracket(
				this.Name,
				this.Player,
				this.CreationDate
			);
			clone.Predictions.CopyFrom(this.Predictions);

			return clone;
		}

		internal MarchMadnessBracket TemporaryClone()
		{
			if (Tournament.IsEnded() || Tournament.IsReadyToPay()) throw new GameEngineException("Can not replicate a Bracket which tournament is over.");
			if (!CanChange()) throw new GameEngineException("Tournament has progressed to a phase where this bracket can be replicated");

			MarchMadnessBracket clone = ((MarchMadnessTournament)this.Tournament).TemporaryClone(
				this.Name,
				this.Player,
				this.CreationDate
			);
			clone.Predictions.CopyFrom(this.Predictions);

			return clone;
		}

		//TODO Se debe renombrar a FirstPredictedVersion, deberia coincidir con el CreationDate
		internal DateTime CreatedOn
		{
			get
			{
				return this.Predictions.CreatedOn;
			}
		}

		internal Team PredictedTournamentWinner
		{
			get
			{
				return Predictions.PredictedTournamentWinner();
			}
		}

		internal List<GamePredictionAffected> AffectedBy(List<GameInThisPrediction> newPredictions)
		{
			if (newPredictions == null) throw new ArgumentNullException(nameof(newPredictions));
			if (newPredictions.Count == 0) throw new GameEngineException("List of games must have at least one element");
			if (newPredictions.Count != Predictions.Count()) throw new GameEngineException("Number of games is different to number of predictions");
			if (state == State.LOCKED) throw new GameEngineException("Bracket is locked");

			var predictionsAffected = Predictions.AffectedByCheckingAllGames(newPredictions);
			return predictionsAffected;
		}

		internal List<GamePredictionAffected> GamesInThisPredictionAffected(List<GameInThisPrediction> newPredictions)
		{
			var predictionsAffected = AffectedBy(newPredictions);
			var allPredictionsWithAffected = new List<GamePredictionAffected>();
			foreach (var prediction in Predictions.ListAllPredictions())
			{
				var predictionAffected = new GamePredictionAffected(prediction, false);
				allPredictionsWithAffected.Add(predictionAffected);
			}
			foreach (var predictionAffected in predictionsAffected)
			{
				var currentGamePredictionAffectedToUpdate = allPredictionsWithAffected.FirstOrDefault(p => p.Game == predictionAffected.Game);
				var newWinnerOfPredecessorGame = newPredictions.FirstOrDefault(p => p.Game == predictionAffected.PredecessorGame).PredictedWinner();
				if (predictionAffected.PredecessorGame.IsNextPositionA())
				{
					currentGamePredictionAffectedToUpdate.TeamA = newWinnerOfPredecessorGame;
				}
				else
				{
					currentGamePredictionAffectedToUpdate.TeamB = newWinnerOfPredecessorGame;
				}

				var originalPrediction = Predictions.PredictionOf(currentGamePredictionAffectedToUpdate.Game.Number);
				currentGamePredictionAffectedToUpdate.IsPending = !originalPrediction.HasTheSamePlayers(currentGamePredictionAffectedToUpdate);
			}
			return allPredictionsWithAffected;
		}

		internal List<GamePredictionAffected> AffectedByGame(GameInThisPrediction game)
		{
			if (game == null) throw new ArgumentNullException(nameof(game));
			if (state == State.LOCKED) throw new GameEngineException("Bracket is locked");

			return Predictions.AffectedBy(new List<Game>() { game.Game });
		}
		private readonly Dictionary<Game, Notification> gradeNotifications = new Dictionary<Game, Notification>();
		public override void OnScoreUpdate(bool itIsThePresent, Game game)
		{
			if (game == null) throw new ArgumentNullException(nameof(game));
			if (!Tournament.IsStarted()) throw new GameEngineException("The tournament has no started.");
			if (Tournament.IsEnded() || Tournament.IsReadyToPay()) throw new GameEngineException("The tournament has ended. Changes on Score are not allowed.");

			int newGrade = Predictions.Grade(game, PointsCriteria);
			if (grade != newGrade)
			{
				grade = newGrade;
				if (OnScoreChange != null) OnScoreChange(this);
			}

			if (game.IsGameOver())
			{
				if (this.Tournament.Settings.AllowSendTournamentNotifications)
				{
					var oldNotification = gradeNotifications.FirstOrDefault(x => x.Key == game).Value;
					if (oldNotification != null)
					{
						gradeNotifications.Remove(game);
						oldNotification.Kill();
					}
					Subscriber me = (Subscriber) this.Player;
					var notification = new Notification((MarchMadnessTournament)this.Tournament,
						$"Game {game.TeamA.Name} vs {game.TeamB.Name} has ended<br>" +
						$"{game.Winner().Name} won!<br>" +
						$"The match result is {game.TeamA.ShortName} {game.ScoreTeamA} - {game.TeamB.ShortName} {game.ScoreTeamB}"
					);
					notification.CreationDate = game.EndedDate;
					notification.Importance = Importance.LOW;
					gradeNotifications.Add(game, notification);
					me.Messages.SendMessageTo(itIsThePresent, me, notification);
				}

				if (game.CurrentRound is FinalRound)
				{
					if (this.Year == 2019)
						gradeToUntie = Predictions.GradeUntie(game, UntieCriteria2019);
					else
					{
						var isFinalFourChallenge = this.Predictions.InitialRoundOfPredictions().Number == 4;
						if (isFinalFourChallenge)
							gradeToUntie = Predictions.GradeUntie(game, UntieCriteriaForFinalFourChallenge);
						else
							gradeToUntie = Predictions.GradeUntie(game, UntieCriteriaFrom2020);
					}
				}
			}
		}

		internal new string ToString()
		{
			StringBuilder result = new StringBuilder();
			result.Append($"{nameof(MarchMadnessBracket)}({Name})=>[Score:{Grade()}]==");
			result.Append(base.ToString());
			return result.ToString();
		}

		internal void AddOnScoreChange(MarchMadnessBracket.ChangeEvent onScoreChange)
		{
			this.OnScoreChange += onScoreChange;
		}
	}
	[Puppet]
	internal class GamePredictionAffected : GameInThisPrediction
	{
		public bool IsPending { get; set; }
		private Game predecessorGame;

		internal Game PredecessorGame
		{
			get
			{
				return predecessorGame;
			}
		}

		internal GamePredictionAffected(GameInThisPrediction gameInThisPrediction, bool isPending) : base(gameInThisPrediction)
		{
			IsPending = isPending;
		}

		internal void SetPredecessorGame(Game predecessorGame)
		{
			if (predecessorGame == null) throw new ArgumentNullException(nameof(predecessorGame));

			this.predecessorGame = predecessorGame;
		}
	}
}
