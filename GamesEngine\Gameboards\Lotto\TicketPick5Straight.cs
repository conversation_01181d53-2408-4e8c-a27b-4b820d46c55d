﻿using GamesEngine.Bets;
using GamesEngine.Business;
using GamesEngine.Games.Lotto;
using GamesEngine.Tools;
using System;
using System.Collections.Generic;
using System.Linq;
using static GamesEngine.Finance.PaymentChannels;

namespace GamesEngine.Gameboards.Lotto
{
	internal sealed class TicketPick5Straight : TicketPick<Pick5>
	{

		internal TicketPick5Straight(Player player, Lottery lottery, DateTime drawDate, string number1, string number2, string number3, string number4, string number5, Selection selectionMode, DateTime creationDate, decimal ticketCost, Prizes prizes) :
			base(player, lottery, drawDate, new Pick5(number1, number2, number3, number4, number5), selectionMode, creationDate, ticketCost, prizes)
		{
		}

		internal TicketPick5Straight(Player player, Lottery lottery, DateTime drawDate, string [] numbers, Selection selectionMode, DateTime creationDate, decimal ticketCost, Prizes prizes) :
			base(player, lottery, drawDate, numbers, selectionMode, creationDate, ticketCost, prizes)
		{
		}

		protected sealed override void GradeNumber(Pick5 winnerNumber, int fireBallNumber = int.MinValue)
		{
			var withFireBall = BelongsToFireBallDraw;
            if (withFireBall && fireBallNumber == int.MinValue) throw new GameEngineException("FireBall number is required");
            if (withFireBall && (fireBallNumber < 0 || fireBallNumber > 9)) throw new GameEngineException($"FireBall number {fireBallNumber} must be between 1 and 2");
            if (winnerNumber.Count != 1) throw new GameEngineException($"Winner {winnerNumber.AsString()} must be a single number");
			int digit1 = winnerNumber[1].SingleDigit;
			int digit2 = winnerNumber[2].SingleDigit;
			int digit3 = winnerNumber[3].SingleDigit;
			int digit4 = winnerNumber[4].SingleDigit;
			int digit5 = winnerNumber[5].SingleDigit;
			bool won = false;
			payout = 0;
			foreach (Pick5 pick in base.Numbers)
			{
                bool isExcluded = IsExcluded(digit1, digit2, digit3, digit4, digit5);
                if (!isExcluded)
                {
                    int prize = Prize();

					if (!withFireBall)
					{
                        if (pick.IsMarked(digit1, digit2, digit3, digit4, digit5))
                        {
                            won = true;
                            var rawPrice = prize * BetAmount(PickPortion.NORMAL_LEVEL);
                            Commons.ValidateAmount(rawPrice);
                            payout = payout + rawPrice;
                        }
                    }
					else
                    {
						if (pick.IsMarked(digit1, digit2, digit3, digit4, digit5))
						{
							won = true;
                            var rawPrice = RoundToNearestFiveCents(prize * BetAmount(PickPortion.NORMAL_LEVEL));
                            Commons.ValidateAmount(rawPrice);
                            payout = payout + rawPrice;
                        }
                        if (pick.IsMarked(fireBallNumber, digit2, digit3, digit4, digit5))
                        {
                            won = true;
                            var rawPrice = RoundToNearestFiveCents(prize * BetAmount(PickPortion.LEVEL1_PORTION));
                            Commons.ValidateAmount(rawPrice);
                            payout = payout + rawPrice;
                        }
                        
						if (pick.IsMarked(digit1, fireBallNumber, digit3, digit4, digit5))
                        {
                            won = true;
                            var rawPrice = RoundToNearestFiveCents(prize * BetAmount(PickPortion.LEVEL2_PORTION));
                            Commons.ValidateAmount(rawPrice);
                            payout = payout + rawPrice;
                        }
                        
						if (pick.IsMarked(digit1, digit2, fireBallNumber, digit4, digit5))
                        {
                            won = true;
                            var rawPrice = RoundToNearestFiveCents(prize * BetAmount(PickPortion.LEVEL3_PORTION));
                            Commons.ValidateAmount(rawPrice);
                            payout = payout + rawPrice;
                        }
                        
						if (pick.IsMarked(digit1, digit2, digit3, fireBallNumber, digit5))
                        {
                            won = true;
                            var rawPrice = RoundToNearestFiveCents(prize * BetAmount(PickPortion.LEVEL4_PORTION));
                            Commons.ValidateAmount(rawPrice);
                            payout = payout + rawPrice;
                        }
                        
						if (pick.IsMarked(digit1, digit2, digit3, digit4, fireBallNumber))
                        {
                            won = true;
                            var rawPrice = RoundToNearestFiveCents(prize * BetAmount(PickPortion.LEVEL5_PORTION));
                            Commons.ValidateAmount(rawPrice);
                            payout = payout + rawPrice;
                        }
                    }
                }
            }

			if (won)
			{
				base.GradeAsWinner();
			}
			else
			{
				base.GradeAsLoser();
			}
		}

        protected override decimal BetAmount(PickPortion pickPortion)
        {
            var originalValue = TicketAmount() / Count;
            Commons.ValidateAmount(originalValue);
            if (!BelongsToFireBallDraw) return originalValue;

            decimal porcionSinFireBall = originalValue / 2;
            if (pickPortion == PickPortion.NORMAL_LEVEL) return porcionSinFireBall;

            decimal porcionCombinacion1 = originalValue - porcionSinFireBall;
            porcionCombinacion1 = porcionCombinacion1 / 5;
            if (pickPortion == PickPortion.LEVEL1_PORTION) return porcionCombinacion1;

            decimal porcionCombinacion2 = originalValue - porcionSinFireBall - porcionCombinacion1;
            porcionCombinacion2 = porcionCombinacion2 / 4;
            if (pickPortion == PickPortion.LEVEL2_PORTION) return porcionCombinacion2;

            decimal porcionCombinacion3 = originalValue - porcionSinFireBall - porcionCombinacion1 - porcionCombinacion2;
            porcionCombinacion3 = porcionCombinacion3 / 3;
            if (pickPortion == PickPortion.LEVEL3_PORTION) return porcionCombinacion3;

            decimal porcionCombinacion4 = originalValue - porcionSinFireBall - porcionCombinacion1 - porcionCombinacion2 - porcionCombinacion3;
			porcionCombinacion4 = porcionCombinacion4 / 2;
            if (pickPortion == PickPortion.LEVEL4_PORTION) return porcionCombinacion4;

            decimal porcionCombinacion5 = originalValue - porcionSinFireBall - porcionCombinacion1 - porcionCombinacion2 - porcionCombinacion3 - porcionCombinacion4;
            if (pickPortion == PickPortion.LEVEL5_PORTION) return porcionCombinacion5;

            throw new GameEngineException("Invalid PickPortion");
        }

        internal override TicketType IdOfType()
		{
			return TicketType.P5S;
		}

		internal override string GameTypeForReports()
		{
			return QueryMakerOfHistoricalPicks.ID_PICK5;
		}

		internal int Prize()
		{
			return Prizes.Prize(IdOfType(), PrizesPicks.PRIZE_CRITERIA);
		}

		internal override IEnumerable<TicketByPrize> TicketsByPrize()
		{
			var tickets = new TicketByPrize[] {
				new TicketByPrizePick5Straight(this, PrizesPicks.PRIZE_CRITERIA)
			};
			foreach (SubTicket<IPick> subTicket in SubTickets())
			{
				tickets[0].Add(subTicket);
			}
			return tickets;
		}

		internal override void GenerateWagers()
		{
			var betAmount = BetAmount();
			GenerateWagers(betAmount);
		}

		internal override void GenerateWagers(decimal betAmount)
		{
			decimal prize = Prize();

			prize *= betAmount;
			var prizeToWin = prize - betAmount;
			prizeToWin = prizeToWin < 0 ? 0 : prizeToWin;

			if (!NumbersFollowPattern())
			{
				foreach (SubTicket<IPick> subticket in SubTickets())
				{
					var strNumbers = subticket.AsStringForAccounting();
					AddWager(betAmount, prizeToWin, strNumbers, subticket);
				}
			}
			else
			{
				ValidateThereIsOnlyOneSubticket();
				var ticketCost = TicketAmount();
				var strNumbers = Numbers.ElementAt(0).AsStringForAccounting();
				AddWager(ticketCost, prizeToWin, strNumbers, SubTickets().ToArray());
			}
		}
	}

	internal class TicketByPrizePick5Straight : TicketByPrize
	{
		internal TicketByPrizePick5Straight(Ticket ticket, int prizeCriteria) : base(ticket, prizeCriteria)
		{

		}

		internal override bool HasNumber(int[] numbers)
		{
			var result = Subtickets.Cast<SubTicket<IPick>>().Any(x => x.IsTheSameNumberWithTheSameOrder(numbers[0], numbers[1], numbers[2], numbers[3], numbers[4]));
			return result;
		}

		internal override string AsString()
		{
			var result = Ticket.AsString();
			return result;
		}
	}
}
