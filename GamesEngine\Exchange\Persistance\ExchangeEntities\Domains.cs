﻿using System;
using System.Collections.Generic;

namespace ExchangeAPI.ExchangeEntities
{
    public partial class Domains
    {
        public Domains()
        {
            CreditNote = new HashSet<CreditNote>();
            DebitNote = new HashSet<DebitNote>();
            Deposit = new HashSet<Deposit>();
            Transfer = new HashSet<Transfer>();
            Withdrawal = new HashSet<Withdrawal>();
        }

        public int Id { get; set; }
        public string Domain { get; set; }

        public virtual ICollection<CreditNote> CreditNote { get; set; }
        public virtual ICollection<DebitNote> DebitNote { get; set; }
        public virtual ICollection<Deposit> Deposit { get; set; }
        public virtual ICollection<Transfer> Transfer { get; set; }
        public virtual ICollection<Withdrawal> Withdrawal { get; set; }
    }
}
