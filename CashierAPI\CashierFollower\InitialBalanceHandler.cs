﻿using MySql.Data.MySqlClient;
using Puppeteer.EventSourcing;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
[assembly: InternalsVisibleTo("GamesEngineTests")]
namespace CashierAPI.CashierFollower
{
    internal class InitialBalanceHandler
    {
        private static Dictionary<string, BalanceData> BalancesInfo { get; } = new Dictionary<string, BalanceData>();
        private static string[] CurrenciesSupported = { "USD", "FP", "SPR" };
        internal static bool OldFPCommandsWithSkip { get; set; } = true;

        internal static int GetCurrentId(string currency)
        {
            if (!BalancesInfo.TryGetValue(currency, out var data))
            {
                data = new BalanceData();
                BalancesInfo[currency] = data;
            }
            return data.CurrentId;
        }

        internal static int GetLastId(string currency)
        {
            if (!BalancesInfo.TryGetValue(currency, out var data))
            {
                data = new BalanceData();
                BalancesInfo[currency] = data;
            }
            return data.LastId;
        }

        internal static void SetCurrentId(string currency, int id)
        {
            if (!BalancesInfo.TryGetValue(currency, out var data))
            {
                data = new BalanceData();
                BalancesInfo[currency] = data;
            }
            data.CurrentId = id;
        }

        internal static void SetLastAuthorizationNumbers(string currency, string[] authorizationNumbers)
        {
            if (!BalancesInfo.TryGetValue(currency, out var data))
            {
                data = new BalanceData();
                BalancesInfo[currency] = data;
            }
            data.LastAuthorizationNumbers = authorizationNumbers;
        }

        internal static bool ExistsBalance(string currency)
        {
            return BalancesInfo.ContainsKey(currency) && BalancesInfo[currency].CurrentId != 0;
        }

        internal static bool ExistsAuthorizationNumbers(string currency)
        {
            return BalancesInfo.TryGetValue(currency, out var data) && data.LastAuthorizationNumbers != null && data.LastAuthorizationNumbers.Length > 0;
        }

        internal static bool ContainsAuthorizationNumber(string currency, string authorizationNumber)
        {
            if (!ExistsAuthorizationNumbers(currency))
                return false;
            return BalancesInfo[currency].LastAuthorizationNumbers.Contains(authorizationNumber);
        }

        internal static void Clear()
        {
            BalancesInfo.Clear();
            OldFPCommandsWithSkip = true;
        }

        private static readonly StringBuilder caseBuilder = new StringBuilder();
        private static readonly StringBuilder filterBuilder = new StringBuilder();
        private static string firstPartQueryMysql;
        private static string secondPartQueryMysql;
        private static string firstPartQuerySqlServer;
        private static string secondPartQuerySqlServer;

        internal static void SetupCurrencyQueryParts()
        {
            foreach (var currency in CurrenciesSupported)
            {
                // Add to CASE expression
                if (caseBuilder.Length > 0) caseBuilder.AppendLine();
                caseBuilder.Append("WHEN Script LIKE '%").Append(currency).Append("%' THEN '").Append(currency).Append("'");

                // Add to filter conditions
                if (filterBuilder.Length > 0) filterBuilder.Append(" OR ");
                filterBuilder.Append("Script LIKE '%").Append(currency).Append("%'");
            }

            firstPartQueryMysql = $@"SELECT t.Currency, t.id
                        FROM (
                            SELECT 
                                CASE 
                                {caseBuilder}
                                END AS Currency,
                                id,
                                FechaHora,
                                ROW_NUMBER() OVER (
                                    PARTITION BY 
                                        CASE 
                                        {caseBuilder}
                                        END
                                    ORDER BY FechaHora DESC
                                ) AS rn
                            FROM";

            secondPartQueryMysql = $@"WHERE Skip = 0 AND Script LIKE '%SetInitialBalance%' 
                            AND ({filterBuilder})
                        ) AS t
                        WHERE t.rn = 1 AND t.Currency IS NOT NULL";

            firstPartQuerySqlServer = $@"SELECT t.Currency, t.id
                        FROM (
                            SELECT 
                                CASE 
                                {caseBuilder}
                                END AS Currency,
                                id,
                                ROW_NUMBER() OVER (
                                    PARTITION BY 
                                        CASE 
                                        {caseBuilder}
                                        END
                                    ORDER BY FechaHora DESC
                                ) AS rn
                            FROM ";

            secondPartQuerySqlServer = $@" WHERE Skip = 0 AND Script LIKE '%SetInitialBalance%' 
                            AND ({filterBuilder})
                        ) AS t
                        WHERE t.rn = 1 AND t.Currency IS NOT NULL";
        }

        internal static void FetchLastIds(string connectionString, DatabaseType dbtype, string actorName)
        {
            if (dbtype == DatabaseType.MySQL)
            {
                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    string query = $"{firstPartQueryMysql} `{actorName}` {secondPartQueryMysql}";

                    ExecuteQuery(connection, query);
                }
            }
            else if (dbtype == DatabaseType.SQLServer)
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    string query = $"{firstPartQuerySqlServer} {actorName} {secondPartQuerySqlServer}";

                    ExecuteQuery(connection, query);
                }
            }
            else
            {
                throw new System.Exception("Database type not supported");
            }
        }

        internal static void ExecuteQuery(IDbConnection connection, string sql)
        {
            try
            {
                connection.Open();
                using (IDbCommand command = connection.CreateCommand())
                {
                    command.CommandText = sql;
                    using (IDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            string currency = reader.GetString(0);
                            int id = reader.GetInt32(1);
                            BalanceData data = new BalanceData
                            {
                                CurrentId = 0,
                                LastId = id
                            };
                            BalancesInfo[currency] = data;
                        }
                    }
                }
            }
            finally
            {
                connection.Close();
            }
        }

        internal class BalanceData
        {
            public int LastId { get; set; }
            public int CurrentId { get; set; }
            public string[] LastAuthorizationNumbers { get; set; }
        }
    }
}
