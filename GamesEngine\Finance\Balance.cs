﻿using GamesEngine.Business;
using GamesEngine.PurchaseOrders;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;

namespace GamesEngine.Finance
{
	[Puppet]
	internal class Balance : Objeto
	{
		private Currency available;
		internal Coin Coin { get;}
		internal string CurrencyCode { get { return Coin.Iso4217Code; } }
		private Currency locked;
		internal AtAddress Owner { get;}
		internal AccountWithBalance Account { get;}
		internal Movements Movements { get;}
        
        internal Balance(Coin isoCodes, AccountWithBalance account)
		{
			Coin = isoCodes;
			Account = account;
			Owner = account.Owner;
			available = Currency.Factory(this.CurrencyCode, 0);
			locked = Currency.Factory(this.CurrencyCode, 0);
			Movements = new Movements(this);
        }

        internal void Lock(bool itIsThePresent, DateTime date, Currency amount, Source source, string who, string documentNumber, Store store, string concept, string reference)
		{
			Lock(itIsThePresent, date, amount, source, who, documentNumber, store, concept, reference, WholePaymentProcessor.NoPaymentProcessor);
		}

		internal void Lock(bool itIsThePresent, DateTime date, Currency amount, Source source, string who, string documentNumber, Store store, string concept, string reference, int processorId)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));
			if (amount.ItsZero()) throw new GameEngineException($@"Amount must be bigger than 0.");
			if (amount.Value < 0) throw new GameEngineException($@"Invalid amount.Must be bigger than 0.");
			if (processorId < 0) throw new GameEngineException($"{nameof(processorId)} {processorId} must be greater than 0.");
			if (available == null) throw new GameEngineException($@"There is no enough available in {amount.CurrencyCode} for {Owner.Number} ");
			if (amount.Value > available.Value) throw new GameEngineException($@"There is no enough available in {amount.CurrencyCode} for {Owner.Number} ");
			if (CurrencyCode != amount.CurrencyCode) throw new GameEngineException($@"A balance in {CurrencyCode } cannot process operations in {amount.CurrencyCode}");
			if (reference == null) reference = string.Empty;
			if (reference.Length > Movement.MAX_REFERENCE_LENGTH) throw new GameEngineException($"Reference {reference} its longer than 30 characters.");

			available.Subtract(amount);
			locked.Add(amount);

			Movements.Add(itIsThePresent,
				new LockMovement(
					source.Number,
					Owner.Number,
					date,
					amount,
					available.Value,
					who,
					documentNumber,
					store.Id,
					concept,
					reference,
					locked.Value,
					Account.Number,
					processorId
					)
				);
		}

		internal void Lock(bool itIsThePresent, DateTime date, Currency amount, string who, string documentNumber, Store store, string concept, string reference)
		{
			Lock(itIsThePresent, date, amount, who, documentNumber, store, concept, reference, WholePaymentProcessor.NoPaymentProcessor);
		}

		internal void Lock(bool itIsThePresent, DateTime date, Currency amount, string who, string documentNumber, Store store, string concept, string reference, int processorId)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));
			if (amount.ItsZero()) throw new GameEngineException($@"Amount must be bigger than 0.");
			if (amount.Value < 0) throw new GameEngineException($@"Invalid amount.Must be bigger than 0.");
			if (processorId < 0) throw new GameEngineException($"{nameof(processorId)} {processorId} must be greater than 0.");
			if (available == null) throw new GameEngineException($@"There is no enough available in {amount.CurrencyCode} for {Owner.Number} ");
			if (amount.Value > available.Value) throw new GameEngineException($@"There is no enough available in {amount.GetType()}.");
			if (CurrencyCode != amount.CurrencyCode) throw new GameEngineException($@"A balance in {CurrencyCode } cannot process operations in {amount.CurrencyCode}");
			if (reference == null) reference = string.Empty;
			if (reference.Length > Movement.MAX_REFERENCE_LENGTH) throw new GameEngineException($"Reference {reference} its longer than 30 characters.");

			available.Subtract(amount);
			locked.Add(amount);
            reference = string.IsNullOrWhiteSpace(reference) ? documentNumber.ToString() : reference;
            Movements.Add(itIsThePresent,
				new LockWithOutSourceMovement(
					Owner.Number,
					date,
					amount,
					available.Value,
					who,
					documentNumber,
					store.Id,
					concept,
					reference,
					locked.Value,
					Account.Number,
					processorId
					)
				);
		}

		internal bool HasEnoughFor(Currency amount)
		{
			return (available.Value >= amount.Value);
		}

		internal void UnLock(bool itIsThePresent, DateTime date, Currency amount, Source source, string who, string documentNumber, Store store, string concept, string reference, int processorId)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));
			if (amount.ItsZero()) throw new GameEngineException($@"Amount must be bigger than 0.");
			if (amount.Value < 0) throw new GameEngineException($@"Invalid amount.Must be bigger than 0.");
			if (processorId < 0) throw new GameEngineException($"{nameof(processorId)} {processorId} must be greater than 0.");
			if (available == null) throw new GameEngineException($@"There is no enough available in {amount.CurrencyCode} for {Owner.Number} ");
			if (locked == null) throw new GameEngineException($@"There is no locked funds in {amount.GetType()} ");
			if (amount.Value > locked.Value) throw new GameEngineException($@"There is no enough available in {amount.CurrencyCode} for {Owner.Number} ");
			if (CurrencyCode != amount.CurrencyCode) throw new GameEngineException($@"A balance in {CurrencyCode} cannot process operations in {amount.CurrencyCode}");
			if (reference == null) reference = string.Empty;
			if (reference.Length > Movement.MAX_REFERENCE_LENGTH) throw new GameEngineException($"Reference {reference} its longer than 30 characters.");

			available.Add(amount);
			locked.Subtract(amount);

			Movements.Add(itIsThePresent,
				new UnLockMovement(
					source.Number,
					Owner.Number,
					date,
					amount,
					available.Value,
					who,
					documentNumber,
					store.Id,
					concept, 
					reference,
					locked.Value,
					Account.Number,
					processorId
					)
				);
		}

		internal void UnLock(bool itIsThePresent, DateTime date, Currency amount, string who, string documentNumber, Store store, string concept, string reference, int processorId)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));
			if (amount.ItsZero()) throw new GameEngineException($@"Amount must be bigger than 0.");
			if (amount.Value < 0) throw new GameEngineException($@"Invalid amount.Must be bigger than 0.");
			if (processorId < 0) throw new GameEngineException($"{nameof(processorId)} {processorId} must be greater than 0.");
			if (available == null) throw new GameEngineException($@"There is no enough available in {amount.CurrencyCode} for {Owner.Number} ");
			if (locked == null) throw new GameEngineException($@"There is no locked funds in {amount.GetType()} ");
			if (amount.Value > locked.Value) throw new GameEngineException($@"There is no enough available in {amount.CurrencyCode} for {Owner.Number} ");
			if (CurrencyCode != amount.CurrencyCode) throw new GameEngineException($@"A balance in {CurrencyCode} cannot process operations in {amount.CurrencyCode}");
			if (reference == null) reference = string.Empty;
			if (reference.Length > Movement.MAX_REFERENCE_LENGTH) throw new GameEngineException($"Reference {reference} its longer than 30 characters.");

			available.Add(amount);
			locked.Subtract(amount);

            Movements.Add(itIsThePresent,
				new UnLockWithOutSourceMovement(
					Owner.Number,
					date,
					amount,
					available.Value,
					who,
					documentNumber,
					store.Id,
					concept,
					reference,
					locked.Value,
					Account.Number,
					processorId
					)
				);
		}

		internal void UnLock(MovementsCollector movements, Currency amount, Authorization authorization, string concept, string reference, int processorId)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));
			if (amount.ItsZero()) throw new GameEngineException($@"Amount must be bigger than 0.");
			if (amount.Value < 0) throw new GameEngineException($@"Invalid amount.Must be bigger than 0.");
			if (processorId < 0) throw new GameEngineException($"{nameof(processorId)} {processorId} must be greater than 0.");
			if (available == null) throw new GameEngineException($@"There is no enough available in {amount.CurrencyCode} for {Owner.Number} ");
			if (locked == null) throw new GameEngineException($@"There is no locked funds in {amount.GetType()} ");
			if (amount.Value > locked.Value) throw new GameEngineException($@"There is no enough available in {amount.CurrencyCode} for {Owner.Number} ");
			if (CurrencyCode != amount.CurrencyCode) throw new GameEngineException($@"A balance in {CurrencyCode} cannot process operations in {amount.CurrencyCode}");
			if (reference == null) reference = string.Empty;
			if (reference.Length > Movement.MAX_REFERENCE_LENGTH) throw new GameEngineException($"Reference {reference} its longer than 30 characters.");

			available.Add(amount);
			locked.Subtract(amount);
            var newRef = string.IsNullOrWhiteSpace(reference) ? authorization.Number.ToString() : reference;
            movements.AddUnlockMovement(
				amount,
				available.Value,
				authorization,
				concept,
                newRef,
				locked.Value,
				Account.Number,
				processorId
				);
		}

		internal void SetAvailable(Currency amount)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));
			if (amount.Value < 0) throw new GameEngineException($@"Invalid amount.Must be bigger than 0.");
			if (CurrencyCode != amount.CurrencyCode) throw new GameEngineException($@"A balance in {CurrencyCode } cannot process operations in {amount.CurrencyCode}");

			available = Currency.Factory(amount.CurrencyCode, amount.Value); // Debe ser un nuevo objecto para evitar malos cálculos por referencia.
		}

		internal void Debit(bool itIsThePresent, DateTime date, Currency amount, Source source, string who, string documentNumber, Store store, string concept, string reference, int processorId)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));
			if (amount.ItsZero()) throw new GameEngineException($@"Amount must be bigger than 0.");
			if (amount.Value < 0) throw new GameEngineException($@"Invalid amount.Must be bigger than 0.");
			if (processorId < 0) throw new GameEngineException($"{nameof(processorId)} {processorId} must be greater than 0.");
			if (available == null) throw new GameEngineException($@"There is no enough available in {amount.CurrencyCode} for {Owner.Number} ");
			if (amount.Value > available.Value) throw new GameEngineException($@"There is no enough available in {amount.CurrencyCode} for {Owner.Number} ");
			if (CurrencyCode != amount.CurrencyCode) throw new GameEngineException($@"A balance in {CurrencyCode} cannot process operations in {amount.CurrencyCode}");
			if (reference == null) reference = string.Empty;
			if (reference.Length > Movement.MAX_REFERENCE_LENGTH) throw new GameEngineException($"Reference {reference} its longer than 30 characters.");

			available.Subtract(amount);

			Movements.Add(itIsThePresent,
				new WithDrawMovement(
					 source.Number,
					Owner.Number,
					date,
					amount,
					available.Value,
					who,
					documentNumber,
					store.Id,
					concept, 
					reference,
					locked.Value,
					Account.Number,
					processorId
					)
				);
		}

		internal void Withdraw(bool itIsThePresent, DateTime date, Currency amount, string who, string documentNumber, Store store, string concept, string reference)
		{
			Withdraw(itIsThePresent, date, amount, who, documentNumber, store, concept, reference, WholePaymentProcessor.NoPaymentProcessor);
		}

		internal void Withdraw(bool itIsThePresent, DateTime date, Currency amount, string who, string documentNumber, Store store, string concept, string reference, int processorId)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));
			if (amount.ItsZero()) throw new GameEngineException($@"Amount must be bigger than 0.");
			if (amount.Value < 0) throw new GameEngineException($@"Invalid amount.Must be bigger than 0.");
			if (processorId < 0) throw new GameEngineException($"{nameof(processorId)} {processorId} must be greater than 0.");
			if (CurrencyCode != amount.CurrencyCode) throw new GameEngineException($@"A balance in {CurrencyCode} cannot process operations in {amount.CurrencyCode}");
			if (concept == null) concept = string.Empty;
			if (reference == null) reference = string.Empty;
			if (reference.Length > Movement.MAX_REFERENCE_LENGTH) throw new GameEngineException($"Reference {reference} its longer than 30 characters.");

			if (available == null) throw new GameEngineException($@"There is no enough available in {amount.CurrencyCode} for {Owner.Number} ");
			if (amount.Value > available.Value) throw new GameEngineException($@"There is no enough available in {amount.CurrencyCode} for {Owner.Number} ");


			available.Subtract(amount);

			Movements.Add(itIsThePresent,
				new WithDrawWithOutSourceMovement(
					Owner.Number,
					date,
					amount,
					available.Value,
					who,
					documentNumber,
					store.Id,
					concept,
					reference,
					locked.Value,
					Account.Number,
					processorId
					)
				);
		}

		internal void Withdraw(bool itIsThePresent, DateTime date, Currency amount, Source source, string who, string documentNumber, Store store, string concept, string reference, int processorId)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));
			if (source == null) throw new ArgumentNullException(nameof(source));
			if (amount.ItsZero()) throw new GameEngineException($@"Amount must be bigger than 0.");
			if (amount.Value <0) throw new GameEngineException($@"Invalid amount.Must be bigger than 0.");
			if (processorId < 0) throw new GameEngineException($"{nameof(processorId)} {processorId} must be greater than 0.");
			if (CurrencyCode != amount.CurrencyCode) throw new GameEngineException($@"A balance in {CurrencyCode} cannot process operations in {amount.CurrencyCode}");
			if (concept == null) concept = string.Empty;
			if (reference == null) reference = string.Empty;
			if (reference.Length > Movement.MAX_REFERENCE_LENGTH) throw new GameEngineException($"Reference {reference} its longer than 30 characters.");

			if (available == null) throw new GameEngineException($@"There is no enough available in {amount.CurrencyCode} for {Owner.Number} ");
			if (amount.Value > available.Value) throw new GameEngineException($@"There is no enough available in {amount.CurrencyCode} for {Owner.Number} ");


			available.Subtract(amount);

			Movements.Add(itIsThePresent,
				new WithDrawMovement(
					source.Number,
					Owner.Number,
					date,
					amount,
					available.Value, 
					who,
					documentNumber,
					store.Id,
					concept, 
					reference,
					locked.Value,
					Account.Number,
					processorId
					)
				);
		}

		internal void Credit(bool itIsThePresent, DateTime date, Currency amount, Source source, string who, string documentNumber, Store store, string concept, string reference)
		{
			Credit(itIsThePresent, date, amount, source, who, documentNumber, store, concept, reference, WholePaymentProcessor.NoPaymentProcessor);
		}

		internal void Credit(bool itIsThePresent, DateTime date, Currency amount, Source source, string who, string documentNumber, Store store, string concept, string reference, int processorId)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));
			if (amount.Value == 0) throw new GameEngineException($@"Amount must be bigger than 0.");
			if (amount.Value < 0) throw new GameEngineException($@"Invalid amount.Must be bigger than 0.");
			if (processorId < 0) throw new GameEngineException($"{nameof(processorId)} {processorId} must be greater than 0.");
			if (CurrencyCode != amount.CurrencyCode) throw new GameEngineException($@"A balance in {CurrencyCode } cannot process operations in {amount.CurrencyCode}");
			if (reference == null) reference = string.Empty;
			if (reference.Length > Movement.MAX_REFERENCE_LENGTH) throw new GameEngineException($"Reference {reference} its longer than 30 characters.");

			available.Add(amount);

			Movements.Add(itIsThePresent, 
				new AccreditMovement(
					source.Number,
					Owner.Number,
					date,
					amount,
					available.Value,
					who, 
					documentNumber,
					store.Id,
					concept, 
					reference,
					locked.Value,
					Account.Number,
					processorId
					)
				);
		}

		internal void Accredit(bool itIsThePresent, DateTime date, Currency amount, Source source, string who, string documentNumber, Store store, string concept, string reference)
		{
			Accredit(itIsThePresent, date, amount, source, who, documentNumber, store, concept, reference, WholePaymentProcessor.NoPaymentProcessor);
		}

		internal void Accredit(bool itIsThePresent, DateTime date, Currency amount, Source source, string who, string documentNumber, Store store, string concept, string reference, int processorId)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));
			if (source == null) throw new ArgumentNullException(nameof(source));
			if (amount.Value == 0) throw new GameEngineException($@"Amount must be bigger than 0.");
			if (amount.Value < 0) throw new GameEngineException($@"Invalid amount.Must be bigger than 0.");
			if (processorId < 0) throw new GameEngineException($"{nameof(processorId)} {processorId} must be greater than 0.");
			if (CurrencyCode != amount.CurrencyCode) throw new GameEngineException($@"A balance in {CurrencyCode } cannot process operations in {amount.CurrencyCode}");
			if (concept == null) concept = string.Empty;
			if (reference == null) reference = string.Empty;
			if (reference.Length > Movement.MAX_REFERENCE_LENGTH) throw new GameEngineException($"Reference {reference} its longer than 30 characters.");

			available.Add(amount);

			Movements.Add(itIsThePresent,
				new AccreditMovement(
					source.Number,
					Owner.Number,
					date,
					amount,
					available.Value,
					who,
					documentNumber,
					store.Id,
					concept,
					reference,
					locked.Value,
					Account.Number,
					processorId
					)
				);
		}

		internal void Accredit(bool itIsThePresent, DateTime date, Currency amount, string who, string documentNumber, Store store, string concept, string reference)
		{
			Accredit(itIsThePresent, date, amount, who, documentNumber, store, concept, reference, WholePaymentProcessor.NoPaymentProcessor);
		}

		internal void Accredit(bool itIsThePresent, DateTime date, Currency amount, string who, string documentNumber, Store store, string concept, string reference, int processorId)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));
			if (amount.Value == 0) throw new GameEngineException($@"Amount must be bigger than 0.");
			if (amount.Value < 0) throw new GameEngineException($@"Invalid amount.Must be bigger than 0.");
			if (processorId < 0) throw new GameEngineException($"{nameof(processorId)} {processorId} must be greater than 0.");
			if (CurrencyCode != amount.CurrencyCode) throw new GameEngineException($@"A balance in {CurrencyCode } cannot process operations in {amount.CurrencyCode}");
			if (concept == null) concept = string.Empty;
			if (reference == null) reference = string.Empty;
			if (reference.Length > Movement.MAX_REFERENCE_LENGTH) throw new GameEngineException($"Reference {reference} its longer than 30 characters.");

			available.Add(amount);

			Movements.Add(itIsThePresent,
				new AccreditWithOutSourceMovement(
					Owner.Number,
					date,
					amount,
					available.Value,
					who,
					documentNumber,
					store.Id,
					concept,
					reference,
					locked.Value,
					Account.Number,
					processorId
					)
				);
		}

		[Obsolete]
		internal void Accredit(bool itIsThePresent, DateTime date, Currency amount, string who, List<int> documentNumbers, Store store, string concept, string reference)
		{
			Accredit(itIsThePresent, date, amount, who, documentNumbers, store, concept, reference, WholePaymentProcessor.NoPaymentProcessor);
		}

        internal void Accredit(bool itIsThePresent, DateTime date, Currency amount, string who, List<int> documentNumbers, Store store, string concept, string reference, int processorId)
        {
            if (amount == null) throw new ArgumentNullException(nameof(amount));
            if (amount.Value == 0) throw new GameEngineException($@"Amount must be bigger than 0.");
            if (amount.Value < 0) throw new GameEngineException($@"Invalid amount.Must be bigger than 0.");
			if (processorId < 0) throw new GameEngineException($"{nameof(processorId)} {processorId} must be greater than 0.");
            if (CurrencyCode != amount.CurrencyCode) throw new GameEngineException($@"A balance in {CurrencyCode} cannot process operations in {amount.CurrencyCode}");
            if (concept == null) concept = string.Empty;
            if (reference == null) reference = string.Empty;
            if (reference.Length > Movement.MAX_REFERENCE_LENGTH) throw new GameEngineException($"Reference {reference} its longer than 30 characters.");

            available.Add(amount);
			var amountPerDocument = Currency.Factory(amount.CurrencyCode, amount.Value / documentNumbers.Count);
			foreach (var documentNumber in documentNumbers) 
			{
				var newRef = string.IsNullOrWhiteSpace(reference) ? documentNumber.ToString() : reference;
				Movements.Add(itIsThePresent,
					new AccreditWithOutSourceMovement(
						Owner.Number,
						date,
                        amountPerDocument,
						available.Value,
						who,
						documentNumber.ToString(),
						store.Id,
						concept,
                        newRef,
						locked.Value,
						Account.Number,
						processorId
						)
					);
            }
        }

        internal void Accredit(bool itIsThePresent, DateTime date, List<string> amounts, string who, List<int> documentNumbers, Store store, string concept, string reference, int processorId)
        {
            if (amounts.Count == 0) throw new GameEngineException($"The amount of amounts and document numbers must be bigger than 0.");
            if (amounts.Count != documentNumbers.Count) throw new GameEngineException($"The amount of amounts and document numbers must be the same.");
			if (store == null) throw new ArgumentNullException(nameof(store));
            if (concept == null) concept = string.Empty;
            if (reference == null) reference = string.Empty;
            if (reference.Length > Movement.MAX_REFERENCE_LENGTH) throw new GameEngineException($"Reference {reference} its longer than 30 characters.");

			var index = 0;
            foreach (var documentNumber in documentNumbers)
            {
                var betAmount = decimal.Parse(amounts[index]);
                var amount = Currency.Factory(CurrencyCode, betAmount);
                if (amount == null) throw new ArgumentNullException(nameof(amount));
                if (amount.Value == 0) throw new GameEngineException($@"Amount must be bigger than 0.");
                if (amount.Value < 0) throw new GameEngineException($@"Invalid amount.Must be bigger than 0.");

                available.Add(amount);
                var newRef = string.IsNullOrWhiteSpace(reference) ? documentNumber.ToString() : reference;
                Movements.Add(itIsThePresent,
                    new AccreditWithOutSourceMovement(
                        Owner.Number,
                        date,
                        amount,
                        available.Value,
                        who,
                        documentNumber.ToString(),
                        store.Id,
                        concept,
                        newRef,
                        locked.Value,
                        Account.Number,
                        processorId
                        )
					);
				index++;
            }
        }

        internal void Credit(MovementsCollector movements, Currency amount, Authorization authorization, string concept, string reference)
		{
			Credit(movements, amount, authorization, concept, reference, WholePaymentProcessor.NoPaymentProcessor);
		}

		internal void Credit(MovementsCollector movements, Currency amount, Authorization authorization, string concept, string reference, int processorId)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));
			if (amount.Value == 0) throw new GameEngineException($@"Amount must be bigger than 0.");
			if (amount.Value < 0) throw new GameEngineException($@"Invalid amount.Must be bigger than 0.");
			if (processorId < 0) throw new GameEngineException($"{nameof(processorId)} {processorId} must be greater than 0.");
			if (CurrencyCode != amount.CurrencyCode) throw new GameEngineException($@"A balance in {CurrencyCode } cannot process operations in {amount.CurrencyCode}");
			if (reference == null) reference = string.Empty;
			if (reference.Length > Movement.MAX_REFERENCE_LENGTH) throw new GameEngineException($"Reference {reference} its longer than 30 characters.");

			available.Add(amount);
            var newRef = string.IsNullOrWhiteSpace(reference) ? authorization.Number.ToString() : reference;
            movements.AddAccreditMovement(
			amount,
			available.Value,
			authorization,  
			concept,
            newRef,
			locked.Value,
			Account.Number,
			processorId
			);
		}

		internal void Debit(MovementsCollector movements, Currency amount, Authorization authorization, string concept, string reference, int processorId)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));
			if (amount.ItsZero()) throw new GameEngineException($@"Amount must be bigger than 0.");
			if (amount.Value < 0) throw new GameEngineException($@"Invalid amount.Must be bigger than 0.");
			if (processorId < 0) throw new GameEngineException($"{nameof(processorId)} {processorId} must be greater than 0.");
			if (available == null) throw new GameEngineException($@"There is no enough available in {amount.CurrencyCode} for {Owner.Number} ");
			if (amount.Value > available.Value) throw new GameEngineException($@"There is no enough available in {amount.CurrencyCode} for {Owner.Number} ");
			if (CurrencyCode != amount.CurrencyCode) throw new GameEngineException($@"A balance in {CurrencyCode} cannot process operations in {amount.CurrencyCode}");
			if (reference == null) reference = string.Empty;
			if (reference.Length > Movement.MAX_REFERENCE_LENGTH) throw new GameEngineException($"Reference {reference} its longer than 30 characters.");

			available.Subtract(amount);

			movements.AddDebitMovement(
				amount,
				available.Value,
				authorization,
				concept,
				reference,
				locked.Value,
				Account.Number,
				processorId
				);

		}

		internal decimal Available
		{
			get
			{
				return this.available.Value;
			}
		 
		}

		internal decimal Locked
		{
			get
			{
				return  this.locked.Value;
			}
		}


		internal Currency AvailableAmount
		{
			get
			{
				return this.available;
			}

		}

		internal Currency LockedAmount
		{
			get
			{
				return this.locked;
			}
		}

		internal string Type
		{
			get
			{
				return this.available.GetType().Name.ToString();
			}
		}

        internal bool IsInitialized { get; private set; }

        internal void SetInitialBalance(bool itIsThePresent, decimal newAvailable)
        {
            if (newAvailable < 0) throw new GameEngineException("Invalid amount. Must be bigger than 0.");

			if (!IsInitialized)
			{
				this.available = Currency.Factory(this.available.CurrencyCode, newAvailable);
                IsInitialized = true;
			}
			else if (newAvailable != available.Value)
			{
                Loggers.GetIntance().Emails.Debug($"{nameof(available)} {available.CurrencyCodeAsText} {available.Value} is not matching {nameof(newAvailable)} {newAvailable} for {nameof(Owner)} {Owner.Number}");

                this.available = Currency.Factory(this.available.CurrencyCode, newAvailable);
            }
        }

    }

}
