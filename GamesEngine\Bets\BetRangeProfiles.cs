﻿using GamesEngine.Domains;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GamesEngine.Bets
{
    internal class BetRangeProfiles: Objeto
    {
        private readonly List<BetRangeProfile> betRangeProfiles = new List<BetRangeProfile>();

        internal IEnumerable<BetRangeProfile> GetAll
        {
            get
            {
                return betRangeProfiles;
            }
        }

        internal void Add(Profile profile, Domain domain, decimal maxAmount, decimal minAmount, DateTime now, string employeeName)
        {
            if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (profile == null) throw new ArgumentNullException(nameof(profile));
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (maxAmount <= 0) throw new GameEngineException($"{nameof(maxAmount)} must be greater than 0");
            if (minAmount <= 0) throw new GameEngineException($"{nameof(minAmount)} must be greater than 0");
            if (minAmount > maxAmount) throw new GameEngineException($"{nameof(maxAmount)} must be greater than {nameof(minAmount)}");
            if (String.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));
            if (Exists(profile, domain)) throw new GameEngineException($"Profile {profile.Name} with domain {domain.Url} already exists");

            var betRangeProfile = new BetRangeProfile(profile, domain, maxAmount, minAmount, now, employeeName);
            betRangeProfiles.Add(betRangeProfile);
        }

        internal void Delete(Profile profile, Domain domain)
        {
            if (profile == null) throw new ArgumentNullException(nameof(profile));
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (!Exists(profile, domain)) throw new GameEngineException($"Profile {profile.Name} with domain {domain.Url} does not exist");

            var betRangeProfile = BetRangeProfile(profile, domain);
            betRangeProfiles.Remove(betRangeProfile);
        }

        internal void Add(BetRangeProfile profile)
        {
            if (profile == null) throw new ArgumentNullException(nameof(profile));
            betRangeProfiles.Add(profile);
        }

        private bool Exists(Profile profile, Domain domain)
        {
            var result = betRangeProfiles.Exists(x => x.Profile == profile && x.Domain==domain);
            return result;
        }

        internal BetRangeProfile BetRangeProfile(Profile profile, Domain domain)
        {
            if (profile == null) throw new ArgumentNullException(nameof(profile));
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (!Exists(profile, domain)) throw new GameEngineException($"Profile {profile.Name} with domain {domain.Url} does not exist");

            var betRange = betRangeProfiles.Single(x => x.Profile == profile && x.Domain == domain);
            return betRange;
        }

        private BetRangeProfile ProfileBy(Domain domain)
        {
            var betRange = betRangeProfiles.SingleOrDefault(x => x.Domain == domain);
            return betRange;
        }
    }
}
