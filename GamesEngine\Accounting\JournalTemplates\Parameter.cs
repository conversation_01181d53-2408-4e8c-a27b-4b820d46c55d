﻿using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Text;

namespace GamesEngine.Accounting.JournalTemplates
{
	internal enum ParameterType
	{
		Text = 1,
		Amount = 2,
	}

	internal abstract class Parameter:Objeto
	{
		private readonly string identifier;
		private readonly ParameterType type;
		private readonly Argument argument;
		public string Formula { get; protected set; }
		public string Description { get; protected set; }
		internal string Type => GetType().Name;

		protected Parameter(string identifier, ParameterType type)
		{
			this.identifier = identifier;
			this.type = type;
			this.argument = new Argument(type);
		}


		internal Argument Argument
		{
			get
			{
				return this.argument;
			}
		}

		internal string Identifier
		{
			get
			{
				return this.identifier;
			}
		}

		internal bool HasDescription
		{
			get
			{
				return ! string.IsNullOrWhiteSpace(Description);
			}
		}
	}

	internal class TextParameter : Parameter
	{
		internal TextParameter(string identifier) : base(identifier, ParameterType.Text)
		{}

		internal TextParameter(string identifier, string description) : base(identifier, ParameterType.Text)
		{
			Description = description;
		}

		internal void Update(string description)
        {
			Description = description;
		}
	}

	internal class AmountParameter : Parameter
	{
		internal AmountParameter(string identifier) : base(identifier, ParameterType.Amount)
		{}

		internal AmountParameter(string identifier, string description, string formula) : base(identifier, ParameterType.Amount)
		{
			Formula = formula;
			Description = description;
		}

		internal void Update(string description, string formula)
		{
			Description = description;
			Formula = formula;
		}
	}
}
