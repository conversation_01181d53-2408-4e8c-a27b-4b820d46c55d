﻿using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GamesEngine.Finance
{
	internal class MovementsCollector
	{
		private List<Movement> movements = new List<Movement>();
		private enum FragmentChangesType { ADD = 1, UPDATE = 2 };
		private struct FragmentChanges
		{
			private readonly FragmentChangesType type;
			private readonly AuthorizationFragment fragment;

			internal FragmentChanges(FragmentChangesType type, AuthorizationFragment fragment)
			{
				this.type = type;
				this.fragment = fragment;
			}

			internal FragmentChangesType Type
			{
				get
				{
					return this.type;
				}
			}

			internal AuthorizationFragment Fragment
			{
				get
				{
					return this.fragment;
				}
			}
		}
		private List<FragmentChanges> fragments;
		private string who;
		private Store store;
		private AtAddress atAddressWhereBelongs;
		private int number;
		private DateTime day;

		public MovementsCollector(DateTime day, Store store, AtAddress atAddressWhereBelongs, int number)
		{
			this.store = store;
			this.atAddressWhereBelongs = atAddressWhereBelongs;
			this.number = number;
			this.day = day;
			this.who = Users.None;
		}

		internal string Who
		{
			set
			{
				if (value == null) throw new ArgumentNullException(nameof(value));
				this.who = value;
			}
		}

		internal IEnumerable<AuthorizationFragment> FragmentsToAdd()
		{
			if (fragments == null) return new List<AuthorizationFragment>();

			var result = fragments.Where(x => x.Type == FragmentChangesType.ADD).Select(x=>x.Fragment);
			return result;
		}

		internal IEnumerable<AuthorizationFragment> FragmentsToUpdate()
		{
			if (fragments == null) return new List<AuthorizationFragment>();

			var result = fragments.Where(x => x.Type == FragmentChangesType.UPDATE).Select(x => x.Fragment);
			return result;
		}

		internal void AddFragmentToPayed(AuthorizationFragment fragment)
		{
			if (fragment == null) throw new ArgumentNullException(nameof(fragment));
			if (fragment.Status == FragmentStatus.Pending || fragment.Status == FragmentStatus.Refunded) throw new GameEngineException($"Authorization {fragment.Authorization.Number}, Fragment {fragment.Number} can not be pending or refunded");
			
			if (fragments == null) fragments = new List<FragmentChanges>();
			fragments.Add(new FragmentChanges(FragmentChangesType.ADD,fragment));
		}

		internal void AddFragmentToRefunded(AuthorizationFragment fragment)
		{
			if (fragment == null) throw new ArgumentNullException(nameof(fragment));
			if (fragment.Status == FragmentStatus.Pending || fragment.Status == FragmentStatus.Payed) throw new GameEngineException($"Authorization {fragment.Authorization.Number}, Fragment {fragment.Number} can not be pending or payed");

			if (fragments == null) fragments = new List<FragmentChanges>();
			fragments.Add(new FragmentChanges(FragmentChangesType.ADD, fragment));
		}

		internal void ChangeFragmentToRefunded(AuthorizationFragment fragment)
		{
			if (fragment == null) throw new ArgumentNullException(nameof(fragment));

			if (fragments == null) fragments = new List<FragmentChanges>();
			fragments.Add(new FragmentChanges(FragmentChangesType.UPDATE, fragment));
		}

		internal void ChangeFragmentToPayed(AuthorizationFragment fragment)
		{
			if (fragment == null) throw new ArgumentNullException(nameof(fragment));

			if (fragments == null) fragments = new List<FragmentChanges>();
			fragments.Add(new FragmentChanges(FragmentChangesType.UPDATE, fragment));
		}

		internal IEnumerable<Movement> List()
		{
			return movements;
		}

		internal void AddAccreditMovement(Currency amount, decimal newBalance, Authorization authorization, string concept, string reference, decimal newLock, string accountNumber, int processorId)
		{
			movements.Add(new AccreditWithOutSourceMovement(
				atAddressWhereBelongs.Number,
				day, 
				amount,
				newBalance,
				who,
				authorization.Number.ToString(),
				this.store.Id,
				concept,
				reference, 
				newLock, 
				accountNumber,
				processorId));
		}

		internal void AddDebitMovement(Currency amount, decimal newBalance, Authorization authorization, string concept, string reference, decimal newLock, string accountNumber, int processorId)
		{
			movements.Add(new WithDrawWithOutSourceMovement(
				atAddressWhereBelongs.Number,
				day,
				amount,
				newBalance,
				who,
				authorization.Number.ToString(),
				store.Id,
				concept, 
				reference, 
				newLock, 
				accountNumber,
				processorId));
		}

		internal void AddUnlockMovement(Currency amount, decimal newBalance, Authorization authorization, string concept, string reference, decimal newLock, string accountNumber, int processorId)
		{
			movements.Add(new UnLockWithOutSourceMovement(
				atAddressWhereBelongs.Number,
				day,
				amount,
				newBalance,
				who,
				authorization.Number.ToString(),
				store.Id,
				concept,
				reference, 
				newLock, 
				accountNumber,
				processorId));
		}

		internal void Save(int bufferId, bool itIsThePresent)
		{
			if (!itIsThePresent) return;
			if (movements == null || movements.Count() <= 0) return;

			var messages = new NewMovementMessage(movements, who);
			if (bufferId != MovementsBuffers.NoBuffer)
			{
				MovementsBuffers.Get(bufferId).Add(messages);
			}
			else
			{
				Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForMovements, messages);
			}
		}
	}

	[Puppet]
	public class Movements : Objeto
	{
		private readonly Balance balance;
		public static MovementStorage Storage { get; set; }
		internal static StorageAsync StorageAsync { get; set; }

		internal Movements(Balance balance)
		{
			if (Storage == null)
			{
				throw new GameEngineException("MovementStorage configuration its required before use Movements class.");
			}
			this.balance = balance;
		}

		internal void Add(bool itIsThePresent, Movement m)
		{
			if (!itIsThePresent) return;
			if (m == null) return;

			var messages = new NewMovementMessage(m);
			var lastDigit = ((int)m.AtAddress.Last()) % 10;
			Integration.Kafka.Send(itIsThePresent, $"{Integration.Kafka.TopicForMovements}_{lastDigit}", messages);
		}

		internal MovementsFiltered FilterBy(int storeId, int inicialIndex, int amount, DateTime initDate, DateTime endDate, Coin currencyCode)
		{
			return Storage.ListMovement(
					inicialIndex,
					amount,
					balance.Owner.Number,
					storeId,
					initDate,
					endDate,
					currencyCode
					);
		}


        internal MovementDetailReport MovementDetailReportBy(AtAddress owner, string currencyCode, int number, int initialIndex, int amountOfRows)
        {
            if (number <= 0) throw new GameEngineException($"{nameof(number)} must be greater than 0");
            if (initialIndex < 0) throw new GameEngineException($"{nameof(initialIndex)} must be greater or equal than 0");
            if (amountOfRows <= 0) throw new GameEngineException($"{nameof(amountOfRows)} must be greater than 0");

            var result = Storage.MovementDetailReportBy(owner, number, Coinage.Coin(currencyCode), initialIndex, amountOfRows);
            return result;
        }

        private const int MAXIMUM_AMOUNT_OF_ROWS_ALLOWED = 100;
        internal MovementsReport MovementsFullDetailReportBy(string atAddress, int storeId, DateTime paramStartDate, DateTime paramEndDate, int initialIndex, int amountOfRows)
        {
            if (string.IsNullOrEmpty(atAddress)) throw new ArgumentException(nameof(atAddress));
            if (paramStartDate == default(DateTime)) throw new ArgumentNullException(nameof(paramStartDate));
            if (paramEndDate == default(DateTime)) throw new ArgumentNullException(nameof(paramEndDate));
            if (initialIndex < 0) throw new GameEngineException($"{nameof(initialIndex)} must be greater or equal than 0");
            if (amountOfRows <= 0) throw new GameEngineException($"{nameof(amountOfRows)} must be greater than 0");

            if (amountOfRows > MAXIMUM_AMOUNT_OF_ROWS_ALLOWED)
            {
                amountOfRows = MAXIMUM_AMOUNT_OF_ROWS_ALLOWED;
            }
            var startDate = paramStartDate.Date;
            var endDate = paramEndDate.Date;

            var report = Storage.MovementsFullDetailReportBy(
                balance.Owner, 
				balance.Coin,
				storeId,
				startDate,
                endDate,
                initialIndex,
                amountOfRows
                );
            return report;
        }

    }

	[Puppet]
	public abstract class Movement :Objeto
	{
		public const int MAX_REFERENCE_LENGTH = 30;
		public const int ALL_MOVEMENT_TYPE = -1;

		public Movement(int scope, string atAddress, DateTime date, type type, Coin iSO_CODE, decimal currentAmount, decimal newBalance, string who, string documentNumber, int storeId, string concept, 
			string reference, decimal newLock, string accountNumber, int processorId)
		{
			if (string.IsNullOrEmpty(documentNumber)) throw new ArgumentException(nameof(documentNumber));
			if (string.IsNullOrEmpty(atAddress)) throw new ArgumentException(nameof(atAddress));
			if (date == null) throw new ArgumentException(nameof(date));
			if (concept == null) concept = string.Empty;
			if (reference == null) reference = string.Empty;
			if (reference.Length > Movement.MAX_REFERENCE_LENGTH) throw new GameEngineException($"Reference {reference} its longer than 30 characters.");
			if (string.IsNullOrEmpty(who)) throw new ArgumentException(nameof(who));

			DocumentNumber = documentNumber;
			AtAddress = atAddress;
			Day = date;
			Type = type;
			Currency = iSO_CODE;
			NewBalance = newBalance;
			CurrentAmount = currentAmount;
			Source = scope;
			TypeAsText = type.ToString();
			CurrencyAsText = iSO_CODE.Iso4217Code.ToString();
			StoreId = storeId;
			this.concept = concept;
			Reference = reference;
			Who = who;
			NewLock = newLock;
			AccountNumber = accountNumber;
			ProcessorId = processorId;
		}

		public enum type { Debit=0, Credit=1, Lock=2, Unlock=3};
		internal DateTime Day { get;}
		internal type Type { get;}
		internal string AtAddress { get;}
		internal Coin Currency { get;}
		internal decimal NewBalance { get;}
		internal decimal CurrentAmount { get;}
		internal int Source { get;}
		internal string CurrencyAsText { get;}
		internal string TypeAsText { get;}

		internal string DocumentNumber { get;}
		public int StoreId { get;}
		private string concept;
		internal string Concept 
		{
            get
            {
				return string.IsNullOrWhiteSpace(concept) ? "Wager(s) placed" : concept;

			}
		}
		internal string Reference { get;}
		internal string Who { get;}
		internal decimal NewLock { get; }
		internal string AccountNumber { get; }
		internal int ProcessorId { get; }
		internal string CurrentAmountFormatted
		{
			get
			{
				return Finance.Currency.Factory(Currency.Iso4217Code, CurrentAmount).ToDisplayFormat();
			}
		}
		internal string NewLockFormatted
		{
			get
			{
				return Finance.Currency.Factory(Currency.Iso4217Code, NewLock).ToDisplayFormat();
			}
		}
		
		internal string ZeroFormatted
		{
			get
			{
				return Finance.Currency.Factory(Currency.Iso4217Code, 0).ToDisplayFormat();
			}
		}
		internal string NewBalanceFormatted
		{
			get
			{
				return Finance.Currency.Factory(Currency.Iso4217Code, NewBalance).ToDisplayFormat();
			}
		}

		internal static DebitMovement GenerateADebitMovement(int source, string atAddress, DateTime day, type type, Currency currency, decimal newBalance, string who, string documentNumber, int storeId, string concept, string reference, decimal newLock, string accountNumber, int processorId)
		{
			DebitMovement m;
			switch (type)
			{
				case type.Debit:
					if (source == Sources.NO_SOURCE_ID)
					{
						m = new WithDrawWithOutSourceMovement(atAddress, day, currency, newBalance, who, documentNumber, storeId, concept, reference, newLock, accountNumber, processorId);
					}
					else
					{
						m = new WithDrawMovement(source, atAddress, day, currency, newBalance, who, documentNumber, storeId, concept, reference, newLock, accountNumber, processorId);
					}
				break;
				case type.Lock:

					if (source == Sources.NO_SOURCE_ID)
					{
						m = new LockWithOutSourceMovement(atAddress, day, currency, newBalance, who, documentNumber, storeId, concept, reference, newLock, accountNumber, processorId);
					}
					else
					{
						m = new LockMovement(source, atAddress, day, currency, newBalance, who, documentNumber, storeId, concept, reference, newLock, accountNumber, processorId);
					}
					
					break;
				default:
					throw new GameEngineException($@"There is no implementation for type {type}");
			}

			return m;
		}

		internal static CreditMovement GenerateACreditMovement(int source, string atAddress, DateTime day, type type, Currency currency, decimal newBalance, string who, string documentNumber, int storeId, string concept, string reference, decimal newLock, string accountNumber, int processorId)
		{
			CreditMovement m;
			switch (type)
			{
				case type.Credit:
					m = new AccreditMovement(source, atAddress, day, currency, newBalance, who,documentNumber, storeId, concept, reference, newLock, accountNumber, processorId);
					break;
				case type.Unlock:
					m = new UnLockMovement(source, atAddress, day, currency, newBalance, who, documentNumber, storeId, concept, reference, newLock, accountNumber, processorId);
					break;
				default:
					throw new GameEngineException($@"There is no implementation for type {type}");
			}

			return m;
		}
	}

	internal abstract class CreditMovement : Movement
	{
		internal CreditMovement(int scope, string atAddress, DateTime date, type type, Coin iSO_CODE, decimal currentAmount, decimal newBalance, string who, string documentNumber, int storeId, string concept, string reference, decimal newLock, string accountNumber, int processorId) : 
			base(scope, atAddress, date, type, iSO_CODE, currentAmount, newBalance, who, documentNumber, storeId, concept, reference, newLock, accountNumber, processorId)
		{
		}

		internal CreditMovement(string atAddress, DateTime date, type type, Coin iSO_CODE, decimal currentAmount, decimal newBalance, string who, string documentNumber, int storeId, string concept, string reference, decimal newLock, string accountNumber, int processorId) :
		this(Sources.NO_SOURCE_ID, atAddress, date, type, iSO_CODE, currentAmount, newBalance, who, documentNumber, storeId, concept, reference, newLock, accountNumber, processorId)
		{

		}

	}

	internal abstract class DebitMovement : Movement
	{
		internal DebitMovement(int source, string atAddress, DateTime date, type type, Coin iSO_CODE, decimal currentAmount, decimal newBalance, string who, string documentNumber, int storeId, string concept, string reference, decimal newLock, string accountNumber, int processorId) : 
			base(source, atAddress, date, type, iSO_CODE, currentAmount, newBalance, who, documentNumber, storeId, concept, reference, newLock, accountNumber, processorId)
		{

		}

		internal DebitMovement(string atAddress, DateTime date, type type, Coin iSO_CODE, decimal currentAmount, decimal newBalance, string who, string documentNumber, int storeId, string concept, string reference, decimal newLock, string accountNumber, int processorId) : 
			this(Sources.NO_SOURCE_ID, atAddress, date, type, iSO_CODE, currentAmount, newBalance, who, documentNumber, storeId, concept, reference, newLock, accountNumber, processorId)
		{

		}
	}

	internal sealed class AccreditMovement : CreditMovement
	{
		internal AccreditMovement(int source, string atAddress, DateTime date, Currency currentAmount, decimal newBalance, string who, string documentNumber, int storeId, string concept, string reference, decimal newLock, string accountNumber, int processorId)
			: base(source, atAddress, date, type.Credit, currentAmount.Coin, currentAmount.Value, newBalance, who, documentNumber, storeId, concept, reference, newLock, accountNumber, processorId)
		{
		}
	}

	internal sealed class AccreditWithOutSourceMovement : CreditMovement
	{
		internal AccreditWithOutSourceMovement(string atAddress, DateTime date, Currency currentAmount, decimal newBalance, string who, string documentNumber, int storeId, string concept, string reference, decimal newLock, string accountNumber, int processorId)
			: base(atAddress, date, type.Credit, currentAmount.Coin, currentAmount.Value, newBalance, who, documentNumber, storeId, concept, reference, newLock, accountNumber, processorId)
		{
		}
	}

	internal sealed class WithDrawMovement : DebitMovement
	{
		internal WithDrawMovement(int source, string atAddress, DateTime date, Currency currentAmount, decimal newBalance, string who, string documentNumber, int storeId, string concept, string reference, decimal newLock, string accountNumber, int processorId)
			: base(source, atAddress, date, type.Debit, currentAmount.Coin, currentAmount.Value, newBalance, who, documentNumber, storeId, concept, reference, newLock, accountNumber, processorId)
		{
		}
	}

	internal sealed class WithDrawWithOutSourceMovement : DebitMovement
	{
		internal WithDrawWithOutSourceMovement(string atAddress, DateTime date, Currency currentAmount, decimal newBalance, string who, string documentNumber, int storeId, string concept, string reference, decimal newLock, string accountNumber, int processorId)
			: base(atAddress, date, type.Debit, currentAmount.Coin, currentAmount.Value, newBalance, who, documentNumber, storeId, concept, reference, newLock, accountNumber, processorId)
		{
		}
	}

	internal sealed class LockMovement : DebitMovement
	{
		public LockMovement(int source, string atAddress, DateTime date, Currency currentAmount, decimal newBalance, string who, string documentNumber, int storeId, string concept, string reference, decimal newLock, string accountNumber, int processorId)
			: base(source, atAddress, date, type.Lock, currentAmount.Coin, currentAmount.Value, newBalance, who, documentNumber, storeId, concept, reference, newLock, accountNumber, processorId)
		{
		}

	}

	internal sealed class LockWithOutSourceMovement : DebitMovement
	{
		internal LockWithOutSourceMovement(string atAddress, DateTime date, Currency currentAmount, decimal newBalance, string who, string documentNumber, int storeId, string concept, string reference, decimal newLock, string accountNumber, int processorId)
		   : base(atAddress, date, type.Lock, currentAmount.Coin, currentAmount.Value, newBalance, who, documentNumber, storeId, concept, reference, newLock, accountNumber, processorId)
		{
		}
	}

	internal sealed class UnLockMovement : CreditMovement
	{
		internal UnLockMovement(int source, string atAddress, DateTime date, Currency currentAmount, decimal newBalance, string who, string documentNumber, int storeId, string concept, string reference, decimal newLock, string accountNumber, int processorId)
			: base(source, atAddress, date, type.Unlock, currentAmount.Coin, currentAmount.Value, newBalance, who, documentNumber, storeId, concept, reference, newLock, accountNumber, processorId)
		{
		}
	}

	internal sealed class UnLockWithOutSourceMovement : CreditMovement
	{
		internal UnLockWithOutSourceMovement(string atAddress, DateTime date, Currency currentAmount, decimal newBalance, string who, string documentNumber, int storeId, string concept, string reference, decimal newLock, string accountNumber, int processorId)
			: base(atAddress, date, type.Unlock, currentAmount.Coin, currentAmount.Value, newBalance, who, documentNumber, storeId, concept, reference, newLock, accountNumber, processorId)
		{
		}
	}

	[Puppet]
	public sealed class Users : Objeto
	{
		public const string ME = "ME";
		public const string None = "N/A";
		public const string LADYBET = "Ladybet";
		public const int NO_USER = 0;

		internal IEnumerable<User> List(Store store)
		{
			return Movements.Storage.ListUsers(store.Id);
		}
	}
	[Puppet]
	internal sealed class User : Objeto
	{
		internal User(int storeId, int id, string name)
		{
			if (id <= 0) throw new GameEngineException($@"{id} its not valid.");
			if (string.IsNullOrEmpty(name)) throw new GameEngineException($@" {name} its required.");

			Id = id;
			Name = name;
			StoreId = storeId;
		}

		internal int Id { get;}
  
		internal string Name { get;}

		internal int StoreId { get;}
	}
}