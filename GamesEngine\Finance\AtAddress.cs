﻿using GamesEngine.Business;
using GamesEngine.Custodian;
using GamesEngine.PurchaseOrders;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using static GamesEngine.Finance.Authorizations;
[assembly: InternalsVisibleTo("CashierAPI")]

namespace GamesEngine.Finance
{
	[Puppet]
	internal class AtAddress : Objeto
	{
		private Sources sources;
		private string number;
		//private Balances balances;
		private Authorizations authorizations;
		private AccountWithBalances accounts;

		public AtAddress(string number)
		{
			if (string.IsNullOrEmpty(number)) throw new ArgumentException(nameof(number));

			this.number = number;
			NumberForTables = number.ToLower();
			sources = new Sources(this);
			//this.balances = new Balances(this);
			this.authorizations = new Authorizations(this);
			accounts = new AccountWithBalances(this);
		}

		internal Source GetSource(int number)
		{
			return sources.SearchByNumber(number);
		}

		[Obsolete]
		internal Source GetOrCreateSource(bool itIsThePresent, DateTime creationDate, int sourceNumber, Currencies.CODES isoCode)
		{
			return GetOrCreateSource(itIsThePresent, creationDate, sourceNumber, isoCode.ToString());
		}

		internal Source GetOrCreateSource(bool itIsThePresent, DateTime creationDate, int sourceNumber, string isoCode)
		{
			if (sourceNumber <= 0) throw new GameEngineException($"{nameof(sourceNumber)} must be greater than zero");

			return GetOrCreateSource(itIsThePresent, creationDate, sourceNumber, isoCode, $"Source {isoCode}");
		}

		internal Source GetOrCreateSource(bool itIsThePresent, DateTime creationDate, int sourceNumber, Currencies.CODES isoCode, string sourceName)
		{
			if (sourceNumber <= 0) throw new GameEngineException($"{nameof(sourceNumber)} must be greater than zero");
			if (string.IsNullOrWhiteSpace(sourceName)) throw new ArgumentNullException(nameof(sourceName));

			return GetOrCreateSource(itIsThePresent, creationDate, sourceNumber, isoCode.ToString(), $"Source {isoCode}");
		}

		internal Source GetOrCreateSource(bool itIsThePresent, DateTime creationDate, int sourceNumber, string isoCode, string sourceName)
		{
			if (sourceNumber <= 0) throw new GameEngineException($"{nameof(sourceNumber)} must be greater than zero");
			if (string.IsNullOrWhiteSpace(sourceName)) throw new ArgumentNullException(nameof(sourceName));

			Source result = GetSource(sourceNumber);
			if (result != null) return result;
			return CreateSource(itIsThePresent, creationDate, sourceNumber, isoCode, sourceName);
		}

        internal bool NeedsToRemoveExpiredFragments(DateTime now)
        {
            return Movements.Storage.NeedsToRemoveExpiredFragments(this.number, now);
        }

        internal void RemoveExpiredFragments(bool itIsThePresent, DateTime now)
        {
            if (itIsThePresent) Movements.Storage.RemoveExpiredFragments(this.number, now);
        }

        [Obsolete]
		internal Source CreateSource(bool itIsThePresent, DateTime creationDate, int sourceNumber,  Currencies.CODES isoCode)
		{
			return CreateSource(itIsThePresent, creationDate, sourceNumber, isoCode.ToString());
		}
		internal Source CreateSource(bool itIsThePresent, DateTime creationDate, int sourceNumber, string isoCode)
		{
			return CreateSource(itIsThePresent, creationDate, sourceNumber, Coinage.Coin(isoCode));
		}
		internal Source CreateSource(bool itIsThePresent, DateTime creationDate, int sourceNumber, Coin coin)
		{
			return CreateSource(itIsThePresent, creationDate, sourceNumber, coin.Iso4217Code, $"Source {coin.Iso4217Code}");
		}
		internal Source CreateSource(bool itIsThePresent, DateTime creationDate, int sourceNumber, string isoCode, string sourceName)
		{
			if (sourceNumber <= 0) throw new GameEngineException($"{nameof(sourceNumber)} must be greater than zero");
			if (string.IsNullOrWhiteSpace(sourceName)) throw new ArgumentNullException(nameof(sourceName));
			if (!GeneralSources.Any(sourceNumber)) GeneralSources.CreateSource(sourceNumber, creationDate, sourceName, isoCode);

			return sources.CreateNewSource(itIsThePresent, creationDate, sourceNumber, Coinage.Coin(isoCode));
		}

		internal IEnumerable<Source> ListSources()
		{
			return sources.List();
		}

		internal string Number
		{
			get
			{
				return this.number;
			}
		}

        internal string NumberForTables { get; }

        internal AccountWithBalances Accounts 
		{ 
			get 
			{ 
				return accounts; 
			}
		}

		internal bool CheckIfExistsAccountIn(string currencyCode)
		{
			return this.accounts.CheckIfExistsAccountIn(Coinage.Coin(currencyCode));
		}

		internal bool CheckIfExistsAccountIn(Coin isoCode)
		{
			return this.accounts.CheckIfExistsAccountIn(isoCode);
		}

		internal bool CheckIfExistsAccount(string accountNumber)
		{
			return this.accounts.CheckIfExistsAccount(accountNumber);
		}

        [Obsolete]
        internal AccountWithBalance CreateAccountIfNotExists(Currencies.CODES currencyCode, string accountNumber)
        {
            return accounts.CreateNewAccountIfNotExists(Coinage.Coin(currencyCode), accountNumber);
        }

        internal AccountWithBalance CreateAccountIfNotExists(string currencyCode, string accountNumber)
		{
			return CreateAccountIfNotExists(Coinage.Coin(currencyCode), accountNumber);
		}

		internal AccountWithBalance CreateAccountIfNotExists(Coin currencyCode, string accountNumber)
		{
			return accounts.CreateNewAccountIfNotExists(currencyCode, accountNumber);
		}

		[Obsolete]
		internal Balance GetBalance(Currencies.CODES isoCode)
		{
			return this.accounts.SearchByNumber(isoCode);
		}

		internal Balance GetBalance(string isoCode)
		{
			return this.accounts.SearchByCoin(isoCode);
		}

		internal AccountWithBalance SearchAccountByNumber(string accountNumber)
		{
			return this.accounts.SearchByNumber(accountNumber);
		}

		internal AccountWithBalance SearchAccountWithBalanceGreaterThan(Currency currency)
		{
			return this.accounts.SearchAccountWithBalanceGreaterThan(currency);
		}

		internal Balance CreateBalanceIfNotExists(Coin isoCode)
		{
			return this.accounts.CreateNewAccountIfNotExists(isoCode);
		}
		internal Balance CreateBalanceIfNotExists(string isoCode)
		{
			return this.accounts.CreateNewAccountIfNotExists(Coinage.Coin(isoCode));
		}

		internal AccumulatedBalances GetAccumulatedBalances(List<string> isoCodes)
		{
			if (isoCodes.Count == 0) throw new GameEngineException($"{nameof(isoCodes)} cannot be empty");

			var accumulatedBalances = new AccumulatedBalances();
            foreach (var isoCode in isoCodes)
            {
				var currencyCode = Coinage.Coin(isoCode);
				var accumulatedBalance = accounts.GetAccumulatedBalance(currencyCode);
				var isThereBalance = accumulatedBalance.Available != 0;
				if (isThereBalance)
                {
					accumulatedBalances.Add(accumulatedBalance);
				}
			}
			
			return accumulatedBalances;
		}

		internal AccumulatedBalanceWithAccounts GetAccumulatedBalanceWithAccounts(string currencyCode)
		{
			if (string.IsNullOrWhiteSpace(currencyCode)) throw new ArgumentNullException(nameof(currencyCode));

			return GetAccumulatedBalanceWithAccounts(Coinage.Coin(currencyCode));
		}

		internal AccumulatedBalanceWithAccounts GetAccumulatedBalanceWithAccounts(Coin currencyCode)
		{
			var accumulatedBalance = accounts.GetAccumulatedBalance(currencyCode);
			return accumulatedBalance;
		}

		internal Authorization GetAuthorizationForUpdate(int number)
		{
			Authorization result = authorizations.Search(number, Purpose.ForUpdate);
			return result;
		}

		internal Balance BalanceForAccount(string accountNumber)
		{
			return accounts.SearchByNumber(accountNumber).Balance;
		}

		internal Authorization GetAuthorization(int number)
		{
			Authorization result = authorizations.Search(number, Purpose.ForDisplay);
			return result;
		}

        internal AuthorizationsMultiple GetAuthorizations(List<int> numbers)
        {
            var result = authorizations.Search(numbers, Purpose.ForDisplay);
            return result;
        }

		internal bool AnyAccountHasEnoughFor(Currency amount)
		{
			return accounts.AnyAccountHasEnoughFor(amount);
		}

		internal bool HasEnoughFor(string accountNumber, Currency amount)
		{
			Balance balance;
			if (this.accounts.SearchByNumber(accountNumber, out balance))
			{
				return balance.HasEnoughFor(amount);
			}
			return false;
		}

		const int DaysToBecomeUseless = 40;
		internal Authorization CreateAuthorization(bool itIsThePresent, DateTime date, Currency amount, int authorizationNumber, Store store, string concept, string reference)
		{
			var uselessForOldCommand = date.AddDays(DaysToBecomeUseless);
			return CreateAuthorization(itIsThePresent, date, amount, authorizationNumber, store, concept, reference, uselessForOldCommand, WholePaymentProcessor.NoPaymentProcessor);
		}

		internal Authorization CreateAuthorization(bool itIsThePresent, DateTime date, Currency amount, int authorizationNumber, Store store, string concept, string reference, DateTime useless, int processorId)
		{
			Balance balance = CreateBalanceIfNotExists(amount.Coin);
			string accountNumber = $"{amount.CurrencyCode}";
			AccountWithBalance account = SearchAccountByNumber(accountNumber);

			balance.Lock(itIsThePresent, date, amount, store.Name, authorizationNumber.ToString(), store, concept, reference, processorId);
			Authorization auth = new Authorization(authorizationNumber, amount, account, useless);

			return authorizations.Add(auth);
		}

		internal Authorization CreateAuthorization(bool itIsThePresent, DateTime date, Currency amount, int authorizationNumber, Store store, string concept, string reference, string accountNumber)
		{
			var uselessForOldCommand = date.AddDays(DaysToBecomeUseless);
			return CreateAuthorization(itIsThePresent, date, amount, authorizationNumber, store, concept, reference, accountNumber, uselessForOldCommand, WholePaymentProcessor.NoPaymentProcessor, Users.None);
		}
		internal Authorization CreateAuthorization(bool itIsThePresent, DateTime date, Currency amount, int authorizationNumber, Store store, string concept, string reference, string accountNumber, DateTime useless, int processorId)
		{
			return CreateAuthorization(itIsThePresent, date, amount, authorizationNumber, store, concept, reference, accountNumber, useless, processorId, Users.None);
		}
		internal Authorization CreateAuthorization(bool itIsThePresent, DateTime date, Currency amount, int authorizationNumber, Store store, string concept, string reference, string accountNumber, DateTime useless, int processorId, string who)
		{
			AccountWithBalance account = SearchAccountByNumber(accountNumber);
			return CreateAuthorization(itIsThePresent, date, amount, authorizationNumber, store, concept, reference, account, useless, processorId, who);
		}
		internal Authorization CreateAuthorization(bool itIsThePresent, DateTime date, Currency amount, int authorizationNumber, Store store, string concept, string reference, AccountWithBalance account, DateTime useless, int processorId, string who)
		{
			Balance balance = account.Balance;

			balance.Lock(itIsThePresent, date, amount, store.Name, authorizationNumber.ToString(), store, concept, reference, processorId);
			Authorization auth = new Authorization(authorizationNumber, amount, account, useless);

			return authorizations.Add(auth);
		}
		[Obsolete]
        internal void CreateAuthorization(bool itIsThePresent, DateTime date, Currency amount, List<int> authorizationNumbers, Store store, string concept, string reference, string accountNumber, DateTime useless)
        {
            CreateAuthorization(itIsThePresent, date, amount, authorizationNumbers, store, concept, reference, accountNumber, useless, WholePaymentProcessor.NoPaymentProcessor);
        }
        internal void CreateAuthorization(bool itIsThePresent, DateTime date, Currency amount, List<int> authorizationNumbers, Store store, string concept, string reference, string accountNumber, DateTime useless, int processorId)
        {
            AccountWithBalance account = SearchAccountByNumber(accountNumber);
            CreateAuthorization(itIsThePresent, date, amount, authorizationNumbers, store, concept, reference, account, useless, processorId, Users.None);
        }
        internal void CreateAuthorization(bool itIsThePresent, DateTime date, Currency amount, List<int> authorizationNumbers, Store store, string concept, string reference, AccountWithBalance account, DateTime useless, int processorId, string who)
        {
            Balance balance = account.Balance;
			foreach (var authorizationNumber in authorizationNumbers)
			{
				balance.Lock(itIsThePresent, date, amount, store.Name, authorizationNumber.ToString(), store, concept, reference, processorId);
				Authorization auth = new Authorization(authorizationNumber, amount, account, useless);
				authorizations.Add(auth);
			}
        }
		internal Authorization CreateFakeAuthorizationWithoutLock(string currencyCode, int authorizationNumber, string accountNumber, DateTime useless)
		{
			AccountWithBalance account = SearchAccountByNumber(accountNumber);
			Authorization auth = new Authorization(authorizationNumber, Currency.ZeroFactory(currencyCode), account, useless);
			return authorizations.Add(auth);
		}

		internal void CreateAuthorization(bool itIsThePresent, DateTime date, string currencyCode, List<string> amounts, List<int> authorizationNumbers, Store store, string concept, string reference, string accountNumber, DateTime useless, int processorId)
		{
			AccountWithBalance account = SearchAccountByNumber(accountNumber);
			CreateAuthorization(itIsThePresent, date, currencyCode, amounts, authorizationNumbers, store, concept, reference, account, useless, processorId, Users.None);
		}
        internal void CreateAuthorization(bool itIsThePresent, DateTime date, string currencyCode, List<string> amounts, List<int> authorizationNumbers, Store store, string concept, string reference, AccountWithBalance account, DateTime useless, int processorId, string who)
        {
            Balance balance = account.Balance;
			var index = 0;
            foreach (var authorizationNumber in authorizationNumbers)
            {
				var betAmount = decimal.Parse(amounts[index]);
				var amount = Currency.Factory(currencyCode, betAmount);
                balance.Lock(itIsThePresent, date, amount, store.Name, authorizationNumber.ToString(), store, concept, reference, processorId);
                Authorization auth = new Authorization(authorizationNumber, amount, account, useless);
                authorizations.Add(auth);
				index++;
            }
        }

        internal Authorization AddAuthorization(Authorization authorization)
		{
			return authorizations.Add(authorization);
		}

		public override string ToString()
		{
			return number;
		}

		internal void Forget()
		{
			if (discartedAuthorizations == null) return;

			foreach (Authorization authorization in discartedAuthorizations)
			{
				authorizations.Remove(authorization);
			}

			discartedAuthorizations = null;
		}

		internal Balance BalanceOrNull(Currencies.CODES isoCode)
		{
			return this.accounts.BalanceOrNull(isoCode);
		}

		internal void Withdraw(bool itIsThePresent, DateTime date, Currency amount, Source source, string who, string documentNumber, Store store, string concept, string reference, int processorId)
		{
			CreateBalanceIfNotExists(amount.Coin).Withdraw(itIsThePresent, date, amount, source, who, documentNumber, store, concept, reference, processorId);
		}

		internal void Withdraw(bool itIsThePresent, DateTime date, Currency amount, Source source, string who, string documentNumber, Store store, string concept, string reference, string accountNumber, int processorId)
		{
			SearchAccountByNumber(accountNumber).Withdraw(itIsThePresent, date, amount, source, who, documentNumber, store, concept, reference, processorId);
		}

		internal void Lock(bool itIsThePresent, DateTime date, Currency amount, Source source, string who, string documentNumber, Store store, string concept, string reference, int processorId)
		{
			CreateBalanceIfNotExists(amount.Coin).Lock(itIsThePresent, date, amount, who, documentNumber, store, concept, reference, processorId);
		}

		internal void UnLock(bool itIsThePresent, DateTime date, Currency amount, Source source, string who, string documentNumber, Store store, string concept, string reference, int processorId)
		{
			CreateBalanceIfNotExists(amount.Coin).UnLock(itIsThePresent, date, amount, who, documentNumber, store, concept, reference, processorId);
		}

		internal void Accredit(bool itIsThePresent, DateTime date, Currency amount, Source source, string who, string documentNumber, Store store, string concept, string reference, int processorId)
		{
			CreateBalanceIfNotExists(amount.Coin).Accredit(itIsThePresent, date, amount, source, who, documentNumber, store, concept, reference, processorId);
		}

		internal void Accredit(bool itIsThePresent, DateTime date, Currency amount, Source source, string who, string documentNumber, Store store, string concept, string reference, string accountNumber, int processorId)
		{
			SearchAccountByNumber(accountNumber).Accredit(itIsThePresent, date, amount, source, who, documentNumber, store, concept, reference, processorId);
		}

		internal void Lock(bool itIsThePresent, DateTime date, Currency amount, string documentNumber, Store store, string concept, string reference, int processorId)
		{
			CreateBalanceIfNotExists(amount.Coin).Lock(itIsThePresent, date, amount, Users.LADYBET, documentNumber, store, concept, reference, processorId);
		}

		private List<Authorization> discartedAuthorizations;

		internal void AddToDiscartedAuthorizations(Authorization authorization)
		{
			if (discartedAuthorizations == null) discartedAuthorizations = new List<Authorization>();
			discartedAuthorizations.Add(authorization);
		}

		internal AtAddress PrepareAdjustments(Authorization authorization, int authorizationFragments, decimal AdjustedWinAmount, decimal AdjustedLossAmount)
		{
			if (AdjustedWinAmount == 0 && AdjustedLossAmount == 0) throw new GameEngineException($"Both adjustments can not 0.");

			var atAddress = this;
			authorization.PrepareAdjustments(authorizationFragments, AdjustedWinAmount, AdjustedLossAmount);
			return this;
		}

		internal AtAddress PreparePayments(bool itIsThePresent, Authorization authorization, IEnumerable<int> authorizationFragments, DateTime now, Store store, string concept, FragmentReason reason)
		{
			return PreparePayments(itIsThePresent, authorization, authorizationFragments, now, store, concept, reason, WholePaymentProcessor.NoPaymentProcessor);
		}

		internal AtAddress PreparePayments(bool itIsThePresent, Authorization authorization, IEnumerable<int> authorizationFragments, DateTime now, Store store, string concept, FragmentReason reason, int processorId)
		{
			if (authorizationFragments == null) throw new ArgumentNullException(nameof(authorizationFragments));
			if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (concept == null) throw new ArgumentNullException(nameof(concept));
			if (processorId < 0) throw new GameEngineException($"{nameof(processorId)} must be greater than 0");

			MovementsCollector movements = accounts.TakeMovementCollector(now, store, authorization.Number);
			authorization.PayFragments(itIsThePresent, authorizationFragments, concept, movements, reason, processorId);
			return this;
		}

		internal AtAddress PrepareFakePayment(Authorization authorization, DateTime now, Store store, FragmentReason reason)
		{
			if (authorization == null) throw new ArgumentNullException(nameof(authorization));
			if (store == null) throw new ArgumentNullException(nameof(store));
			if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (reason != FragmentReason.Winner) throw new GameEngineException($"Fake payment only work for {FragmentReason.Winner}");

			MovementsCollector movements = accounts.TakeMovementCollector(now, store, authorization.Number);
			authorization.PayFakeFragment(movements, reason);
			return this;
		}

		internal AtAddress PrepareRefunds(bool itIsThePresent, Authorization authorization, IEnumerable<int> authorizationFragments, DateTime now, Store store, string concept, FragmentReason reason)
		{
			return PrepareRefunds(itIsThePresent, authorization, authorizationFragments, now, store, concept, reason, WholePaymentProcessor.NoPaymentProcessor);
		}

		internal AtAddress PrepareRefunds(bool itIsThePresent, Authorization authorization, IEnumerable<int> authorizationFragments, DateTime now, Store store, string concept, FragmentReason reason, int processorId)
		{
			if (authorizationFragments == null) throw new ArgumentNullException(nameof(authorizationFragments));
			if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (concept == null) throw new ArgumentNullException(nameof(concept));
			if (processorId < 0) throw new GameEngineException($"{nameof(processorId)} must be greater than 0");

			var atAddress = this;
			MovementsCollector movements = accounts.TakeMovementCollector(now, store, authorization.Number);
			authorization.RefundFragments(itIsThePresent, authorizationFragments, concept, movements, reason, processorId);
			return this;
		}

		internal AtAddress ApplyChanges(bool itIsThePresent, string who)
		{
			accounts.ApplyChanges(MovementsBuffers.NoBuffer, itIsThePresent, who);
			return this;
		}

		internal AtAddress ApplyChanges(int bufferId, bool itIsThePresent, string who)
		{
			accounts.ApplyChanges(bufferId, itIsThePresent, who);
			return this;
		}

		internal MovementDetailReport MovementDetailReportBy(string currencyCode, int number, int initialIndex, int amountOfRows)
		{
			if (number <= 0) throw new GameEngineException($"{nameof(number)} must be greater than 0");
			if (initialIndex < 0) throw new GameEngineException($"{nameof(initialIndex)} must be greater or equal than 0");
			if (amountOfRows <= 0) throw new GameEngineException($"{nameof(amountOfRows)} must be greater than 0");

			var result = Movements.Storage.MovementDetailReportBy(this, number, Coinage.Coin(currencyCode), initialIndex, amountOfRows);
			return result;
		}

	}
}
