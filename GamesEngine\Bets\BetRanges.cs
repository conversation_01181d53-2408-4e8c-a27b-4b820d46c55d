﻿using GamesEngine.Domains;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Linq;
using System.Collections.Generic;
using System.Text;

namespace GamesEngine.Bets
{
    internal class BetRanges:Objeto
    {
        private readonly List<BetRange> betRanges = new List<BetRange>();

        internal int Count
        {
            get
            {
                return betRanges.Count;
            }
        }

        internal IEnumerable<BetRange> GetAll
        {
            get
            {
                return betRanges;
            }
        }

        internal void Add(BetRange betRange)
        {
            if (betRange == null) throw new ArgumentNullException(nameof(betRange));
            if (Exists(betRange.Domain)) throw new GameEngineException($"Bet range for domain {betRange.Domain.Url} already exist");

            betRanges.Add(betRange);
        }

        internal void Add(DateTime now, Domain domain, decimal maxAmount, decimal minAmount, string employeeName)
        {
            if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (maxAmount <= 0) throw new GameEngineException($"{nameof(maxAmount)} must be greater than 0");
            if (minAmount <= 0) throw new GameEngineException($"{nameof(minAmount)} must be greater than 0");
            if (minAmount > maxAmount) throw new GameEngineException($"{nameof(maxAmount)} must be greater than {nameof(minAmount)}");
            if (Exists(domain)) throw new GameEngineException($"Bet range for domain {domain.Url} already exist");

            var betRange = new BetRange(domain, maxAmount, minAmount);
            betRanges.Add(betRange);
            betRange.AddLog(now, domain, maxAmount, minAmount, employeeName);
        }

        internal void Update(DateTime now, Domain currentDomain, Domain newDomain, decimal maxAmount, decimal minAmount, string employeeName)
        {
            if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (currentDomain == null) throw new ArgumentNullException(nameof(currentDomain));
            if (maxAmount <= 0) throw new GameEngineException($"{nameof(maxAmount)} must be greater than 0");
            if (minAmount <= 0) throw new GameEngineException($"{nameof(minAmount)} must be greater than 0");
            if (minAmount > maxAmount) throw new GameEngineException($"{nameof(maxAmount)} must be greater than {nameof(minAmount)}");
            if (!Exists(currentDomain)) throw new GameEngineException($"Bet range for domain {currentDomain.Url} does not exist");

            var betRange = BetRange(currentDomain);
            betRange.Update(newDomain, maxAmount, minAmount);
            betRange.AddLog(now, newDomain, maxAmount, minAmount, employeeName);
        }

        internal void Remove(BetRange betRange)
        {
            if (betRange == null) throw new ArgumentNullException(nameof(betRange));
            if (!Exists(betRange.Domain)) throw new GameEngineException($"Bet range for domain {betRange.Domain.Url} does not exist");
            if (!betRanges.Contains(betRange)) throw new GameEngineException("This bet range does not belong to this collection");

            betRanges.Remove(betRange);
        }

        internal void Clear()
        {
            betRanges.Clear();
        }

        private BetRange BetRange(Domain domain)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (!Exists(domain)) throw new GameEngineException($"Bet range for domain {domain.Url} does not exist");

            var betRange = betRanges.Single(x => x.Domain == domain);
            return betRange;
        }

        internal bool Exists(Domain domain)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            var result = betRanges.Exists(x => x.Domain == domain);
            return result;
        }

        internal decimal MaxAmount(Domain domain)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            var betRange = betRanges.SingleOrDefault(x => x.Domain == domain);
            if (betRange==null) throw new GameEngineException($"Bet range for domain {domain.Url} does not exist");
            return betRange.MaxAmount;
        }

        internal decimal MinAmount(Domain domain)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            var betRange = betRanges.SingleOrDefault(x => x.Domain == domain);
            if (betRange == null) throw new GameEngineException($"Bet range for domain {domain.Url} does not exist");
            return betRange.MinAmount;
        }
    }
}
