﻿using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Collections;
using System.Text;
using System.Threading.Tasks;

namespace GamesEngine.Exchange
{
    class UserTypes : Objeto, IEnumerable<UserType>
    {
        List<UserType> Types { get; } = new List<UserType>();

        int nextConsecutive = 0;
        internal int NextConsecutive()
        {
            return nextConsecutive + 1;
        }

        internal void Add(int id, string name)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

            nextConsecutive = id;
            var type = new UserType(id, name);
            Types.Add(type);
        }

        public IEnumerator<UserType> GetEnumerator()
        {
            return Types.GetEnumerator();
        }

        IEnumerator IEnumerable.GetEnumerator()
        {
            return Types.GetEnumerator();
        }
    }

    class UserType : Objeto
    {
        internal int Id { get; }
        internal string Name { get; }

        public UserType(int id, string name)
        {
            Id = id;
            Name = name;
        }
    }
}
