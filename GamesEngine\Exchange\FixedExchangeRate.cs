﻿using GamesEngine.Finance;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Text;

namespace GamesEngine.Exchange
{
	internal sealed class FixedExchangeRate
	{
		private readonly CurrencyPair rate;
		internal decimal Price { get { return rate.Price; } }

		public Coin BaseCurrency { get { return rate.BaseCurrency; } }

		internal FixedExchangeRate(Coin qouteCurrency, Coin baseCurrency, decimal price)
		{
			this.rate = new CurrencyPair(qouteCurrency, baseCurrency, price);
		}

		internal FixedExchangeRate(Currency qouteCurrency, Currency baseCurrency)
		{
			decimal dividend = baseCurrency.Value;
			decimal divisor = qouteCurrency.Value;
			decimal quotient = dividend / divisor;

			this.rate = new CurrencyPair(qouteCurrency.Coin, baseCurrency.Coin, quotient);
		}

		internal decimal Minus(FixedExchangeRate rateCost)
		{
			return Price - rateCost.Price;
		}

		public override string ToString()
		{
			return $"{rate.QouteCurrency}/{rate.BaseCurrency} {rate.Price}";
		}
	}
}
