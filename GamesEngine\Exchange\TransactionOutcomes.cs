﻿using GamesEngine.Accounting.JournalTemplates;
using GamesEngine.Business;
using GamesEngine.Customers;
using GamesEngine.Domains;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using town.connectors.drivers;

namespace GamesEngine.Exchange
{
    internal class TransactionOutcomes: Objeto
    {
        private readonly Dictionary<TransactionOutcomeKey, TransactionOutcome> outcomes = new Dictionary<TransactionOutcomeKey, TransactionOutcome>();
        private readonly Marketplace marketplace;
        private readonly JournalEntryTemplates journalEntryTemplates;

        internal static TransactionType[] AvailableTransactionType = new TransactionType[]
        {
            TransactionType.Deposit,
            TransactionType.Deposit_Then_Lock,
            TransactionType.Withdrawal,
            TransactionType.Transfer,
            TransactionType.CreditNote,
            TransactionType.DebitNote,
            TransactionType.Sale
        };

        internal TransactionOutcomes(Marketplace marketplace, JournalEntryTemplates journalEntryTemplates)
        {
            if (marketplace == null) throw new ArgumentNullException(nameof(marketplace));
            if (journalEntryTemplates == null) throw new ArgumentNullException(nameof(journalEntryTemplates));

            this.marketplace = marketplace;
            this.journalEntryTemplates = journalEntryTemplates;
        }

		internal Parameters ListDefaultParametersFor(TransactionType transactionType)
        {
            switch (transactionType)
            {
                case TransactionType.Deposit:
                case TransactionType.Deposit_Then_Lock:
                    return DefaultTemplateFor(transactionType).Parameters;
                case TransactionType.Withdrawal:
                    return DefaultTemplateFor(transactionType).Parameters;
                case TransactionType.Transfer:
                    return DefaultTemplateFor(transactionType).Parameters;
                case TransactionType.CreditNote:
                    return DefaultTemplateFor(transactionType).Parameters;
                case TransactionType.DebitNote:
                    return DefaultTemplateFor(transactionType).Parameters;
                case TransactionType.Sale:
                    return DefaultTemplateFor(transactionType).Parameters;
                default:
                    throw new GameEngineException($"There is no {nameof(Parameters)} for {nameof(transactionType)} '{transactionType}'");
            }
        }

        private JournalEntryTemplate DefaultTemplateFor(TransactionType transactionType)
        {
            switch (transactionType)
            {
                case TransactionType.Deposit:
                case TransactionType.Deposit_Then_Lock:
                    return DepositTransaction.Template;
                case TransactionType.Withdrawal:
                    return WithdrawalTransaction.Template;
                case TransactionType.Transfer:
                    return TransferTransaction.Template;
                case TransactionType.CreditNote:
                    return CreditNoteTransaction.Template;
                case TransactionType.DebitNote:
                    return DebitNoteTransaction.Template;
                case TransactionType.Sale:
                    return SaleTransaction.Template;
                default:
                    throw new GameEngineException($"There is no {nameof(JournalEntryTemplate)} for {nameof(transactionType)} '{transactionType}'");
            }
        }

        internal void CreatesOutcomesFor(Domain domain)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            foreach (var currencyCode in Coinage.All)
            {
                foreach (var transactionType in AvailableTransactionType)
                {
                    templateConsecutive = NewTemplateNumber();
                    var key = new TransactionOutcomeKey(domain, transactionType, currencyCode);
                    var outcome = new TransactionOutcome(this, journalEntryTemplates, key, templateConsecutive);
                    outcomes.Add(key, outcome);
                }
            }
            
        }

        private int templateConsecutive = 100;
        private int NewTemplateNumber()
        {
            templateConsecutive += 1;
            return templateConsecutive;
        }

        internal JournalEntryTemplate TemplateFor(Domain domain, TransactionType transactionType, string currencyCode)
        {
            return TemplateFor(domain, transactionType, Coinage.Coin(currencyCode));
        }
        internal JournalEntryTemplate TemplateFor(Domain domain, TransactionType transactionType, Coin coin)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (! AvailableTransactionType.Contains(transactionType)) throw new GameEngineException($"{nameof(transactionType)} '{transactionType}' is not valid");
            if (!Coinage.Exists(coin.Iso4217Code)) throw new GameEngineException($"{nameof(coin)} '{coin.Iso4217Code}' is not valid");

            var outcome = FindOutcome(domain, transactionType, coin);
            return outcome.CurrentTemplate;
        }

        internal Parameters CustomParametersFor(Domain domain, TransactionType transactionType, string currencyCode)
        {
            return CustomParametersFor(domain, transactionType, Coinage.Coin(currencyCode));
        }
        internal Parameters CustomParametersFor(Domain domain, TransactionType transactionType, Coin coin)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (! AvailableTransactionType.Contains(transactionType)) throw new GameEngineException($"{nameof(transactionType)} '{transactionType}' is not valid");
            if (! Coinage.Exists(coin.Iso4217Code)) throw new GameEngineException($"{nameof(coin)} '{coin.Iso4217Code}' is not valid");

            var outcome = FindOutcome(domain, transactionType, coin);
            return outcome.CustomParameters;
        }

        private TransactionOutcome FindOutcome(Domain domain, TransactionType transactionType, Coin coin)
        {
            var key = new TransactionOutcomeKey(domain, transactionType, coin);
            TransactionOutcome outcome = null;
            outcomes.TryGetValue(key, out outcome);
            if (outcome == null) throw new GameEngineException($"There is no {nameof(TransactionOutcome)} registered for '{domain.Url}', '{transactionType}', '{coin.Iso4217Code}'");
            return outcome;
        }

        internal void EnableTemplate(Domain domain, TransactionType transactionType, string currencyCode)
        {
            EnableTemplate(domain, transactionType, Coinage.Coin(currencyCode));
        }
        internal void EnableTemplate(Domain domain, TransactionType transactionType, Coin coin)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (! AvailableTransactionType.Contains(transactionType)) throw new GameEngineException($"{nameof(transactionType)} '{transactionType}' is not valid");
            if (! Coinage.Exists(coin.Iso4217Code)) throw new GameEngineException($"{nameof(coin)} '{coin.Iso4217Code}' is not valid");

            var outcome = FindOutcome(domain, transactionType, coin);
            outcome.EnableTemplate();
        }

        internal void DisableTemplate(Domain domain, TransactionType transactionType, string currencyCode)
        {
            DisableTemplate(domain, transactionType, Coinage.Coin(currencyCode));
        }
        internal void DisableTemplate(Domain domain, TransactionType transactionType, Coin coin)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (! AvailableTransactionType.Contains(transactionType)) throw new GameEngineException($"{nameof(transactionType)} '{transactionType}' is not valid");
            if (! Coinage.Exists(coin.Iso4217Code)) throw new GameEngineException($"{nameof(coin)} '{coin.Iso4217Code}' is not valid");

            var outcome = FindOutcome(domain, transactionType, coin);
            outcome.DisableTemplate();
        }

        internal void EnableFee(Domain domain, TransactionType transactionType, string currencyCode)
        {
            EnableFee(domain, transactionType, Coinage.Coin(currencyCode));
        }
        internal void EnableFee(Domain domain, TransactionType transactionType, Coin coin)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (! AvailableTransactionType.Contains(transactionType)) throw new GameEngineException($"{nameof(transactionType)} '{transactionType}' is not valid");
            if (! Coinage.Exists(coin.Iso4217Code)) throw new GameEngineException($"{nameof(coin)} '{coin.Iso4217Code}' is not valid");

            var outcome = FindOutcome(domain, transactionType, coin);
            outcome.EnableFee();
        }

        internal void DisableFee(Domain domain, TransactionType transactionType, string currencyCode)
        {
            DisableFee(domain, transactionType, Coinage.Coin(currencyCode));
        }
        internal void DisableFee(Domain domain, TransactionType transactionType, Coin coin)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (! AvailableTransactionType.Contains(transactionType)) throw new GameEngineException($"{nameof(transactionType)} '{transactionType}' is not valid");
            if (! Coinage.Exists(coin.Iso4217Code)) throw new GameEngineException($"{nameof(coin)} '{coin.Iso4217Code}' is not valid");

            var outcome = FindOutcome(domain, transactionType, coin);
            outcome.DisableFee();
        }

        internal bool AreFeesEnabled(Domain domain, TransactionType transactionType, string currencyCode)
        {
            return AreFeesEnabled(domain, transactionType, Coinage.Coin(currencyCode));
        }
        internal bool AreFeesEnabled(Domain domain, TransactionType transactionType, Coin coin)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (! AvailableTransactionType.Contains(transactionType)) throw new GameEngineException($"{nameof(transactionType)} '{transactionType}' is not valid");
            if (! Coinage.Exists(coin.Iso4217Code)) throw new GameEngineException($"{nameof(coin)} '{coin.Iso4217Code}' is not valid");

            var outcome = FindOutcome(domain, transactionType, coin);
            return outcome.AreFeesEnabled;
        }

        private const int MAX_LENGTH_FOR_DESCRIPTION = 200;
        internal void AddCustomTextParameter(Domain domain, TransactionType transactionType, string currencyCode, string name, string description)
        {
            AddCustomTextParameter(domain, transactionType, Coinage.Coin(currencyCode), name, description);
        }
        internal void AddCustomTextParameter(Domain domain, TransactionType transactionType, Coin coin, string name, string description)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (! AvailableTransactionType.Contains(transactionType)) throw new GameEngineException($"{nameof(transactionType)} '{transactionType}' is not valid");
            if (! Coinage.Exists(coin.Iso4217Code)) throw new GameEngineException($"{nameof(coin)} '{coin.Iso4217Code}' is not valid");
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (description.Length > MAX_LENGTH_FOR_DESCRIPTION) throw new GameEngineException($"{nameof(description)} is greater than {MAX_LENGTH_FOR_DESCRIPTION} characters");

            var outcome = FindOutcome(domain, transactionType, coin);
            outcome.AddCustomTextParameter(name, description);
        }

        internal void AddCustomAmountParameter(Domain domain, TransactionType transactionType, string currencyCode, string name, string description, string formula)
        {
            AddCustomAmountParameter(domain, transactionType, Coinage.Coin(currencyCode), name, description, formula);
        }
        internal void AddCustomAmountParameter(Domain domain, TransactionType transactionType, Coin coin, string name, string description, string formula)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (! AvailableTransactionType.Contains(transactionType)) throw new GameEngineException($"{nameof(transactionType)} '{transactionType}' is not valid");
            if (! Coinage.Exists(coin.Iso4217Code)) throw new GameEngineException($"{nameof(coin)} '{coin.Iso4217Code}' is not valid");
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (description.Length > MAX_LENGTH_FOR_DESCRIPTION) throw new GameEngineException($"{nameof(description)} is greater than {MAX_LENGTH_FOR_DESCRIPTION} characters");

            var outcome = FindOutcome(domain, transactionType, coin);
            outcome.AddCustomAmountParameter(name, description, formula);
        }

        internal void UpdateCustomTextParameter(Domain domain, TransactionType transactionType, string currencyCode, string name, string description)
        {
            UpdateCustomTextParameter(domain, transactionType, Coinage.Coin(currencyCode), name, description);
        }
        internal void UpdateCustomTextParameter(Domain domain, TransactionType transactionType, Coin coin, string name, string description)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (! AvailableTransactionType.Contains(transactionType)) throw new GameEngineException($"{nameof(transactionType)} '{transactionType}' is not valid");
            if (! Coinage.Exists(coin.Iso4217Code)) throw new GameEngineException($"{nameof(coin)} '{coin.Iso4217Code}' is not valid");
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (description.Length > MAX_LENGTH_FOR_DESCRIPTION) throw new GameEngineException($"{nameof(description)} is greater than {MAX_LENGTH_FOR_DESCRIPTION} characters");

            var outcome = FindOutcome(domain, transactionType, coin);
            outcome.UpdateCustomTextParameter(name, description);
        }

        internal void UpdateCustomAmountParameter(Domain domain, TransactionType transactionType, string currencyCode, string name, string description, string formula)
        {
            UpdateCustomAmountParameter(domain, transactionType, Coinage.Coin(currencyCode), name, description, formula);
        }
        internal void UpdateCustomAmountParameter(Domain domain, TransactionType transactionType, Coin coin, string name, string description, string formula)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (! AvailableTransactionType.Contains(transactionType)) throw new GameEngineException($"{nameof(transactionType)} '{transactionType}' is not valid");
            if (! Coinage.Exists(coin.Iso4217Code)) throw new GameEngineException($"{nameof(coin)} '{coin.Iso4217Code}' is not valid");
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (description.Length > MAX_LENGTH_FOR_DESCRIPTION) throw new GameEngineException($"{nameof(description)} is greater than {MAX_LENGTH_FOR_DESCRIPTION} characters");

            var outcome = FindOutcome(domain, transactionType, coin);
            outcome.UpdateCustomAmountParameter(name, description, formula);
        }

        internal bool ExistsCustomParameter(Domain domain, TransactionType transactionType, string currencyCode, string name)
        {
            return ExistsCustomParameter(domain, transactionType, Coinage.Coin(currencyCode), name);
        }
        internal bool ExistsCustomParameter(Domain domain, TransactionType transactionType, Coin coin, string name)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (! AvailableTransactionType.Contains(transactionType)) throw new GameEngineException($"{nameof(transactionType)} '{transactionType}' is not valid");
            if (! Coinage.Exists(coin.Iso4217Code)) throw new GameEngineException($"{nameof(coin)} '{coin.Iso4217Code}' is not valid");
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

            var outcome = FindOutcome(domain, transactionType, coin);
            var exists = outcome.CustomParameters.Exists(name);
            return exists;
        }

        internal void AddCustomParameterAsFee(Domain domain, TransactionType transactionType, Coin coin, string name)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (! AvailableTransactionType.Contains(transactionType)) throw new GameEngineException($"{nameof(transactionType)} '{transactionType}' is not valid");
            if (! Coinage.Exists(coin.Iso4217Code)) throw new GameEngineException($"{nameof(coin)} '{coin.Iso4217Code}' is not valid");
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

            var outcome = FindOutcome(domain, transactionType, coin);
            outcome.AddCustomParameterAsFee(name);
        }

        internal void RemoveCustomParameterAsFee(Domain domain, TransactionType transactionType, Coin coin, string name)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (! AvailableTransactionType.Contains(transactionType)) throw new GameEngineException($"{nameof(transactionType)} '{transactionType}' is not valid");
            if (! Coinage.Exists(coin.Iso4217Code)) throw new GameEngineException($"{nameof(coin)} '{coin.Iso4217Code}' is not valid");
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

            var outcome = FindOutcome(domain, transactionType, coin);
            outcome.RemoveCustomParameterAsFee(name);
        }

        internal bool IsParameterAddedAsFee(Domain domain, TransactionType transactionType, string currencyCode, string name)
        {
            return IsParameterAddedAsFee(domain, transactionType, Coinage.Coin(currencyCode), name);
        }
        internal bool IsParameterAddedAsFee(Domain domain, TransactionType transactionType, Coin coin, string name)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (! AvailableTransactionType.Contains(transactionType)) throw new GameEngineException($"{nameof(transactionType)} '{transactionType}' is not valid");
            if (! Coinage.Exists(coin.Iso4217Code)) throw new GameEngineException($"{nameof(coin)} '{coin.Iso4217Code}' is not valid");
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

            var outcome = FindOutcome(domain, transactionType, coin);
            var exists = outcome.ContainsCustomParameterAsFee(name);
            return exists;
        }
        internal bool IsParameterAddedAsFee(Domain domain, TransactionType transactionType, string currencyCode, Parameter parameter)
        {
            return IsParameterAddedAsFee(domain, transactionType, Coinage.Coin(currencyCode), parameter);
        }
        internal bool IsParameterAddedAsFee(Domain domain, TransactionType transactionType, Coin coin, Parameter parameter)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (! AvailableTransactionType.Contains(transactionType)) throw new GameEngineException($"{nameof(transactionType)} '{transactionType}' is not valid");
            if (! Coinage.Exists(coin.Iso4217Code)) throw new GameEngineException($"{nameof(coin)} '{coin.Iso4217Code}' is not valid");
            if (parameter == null) throw new ArgumentNullException(nameof(parameter));

            var outcome = FindOutcome(domain, transactionType, coin);
            var exists = outcome.ContainsCustomParameterAsFee(parameter);
            return exists;
        }

        struct TransactionOutcomeKey
        {
            internal Domain Domain { get; }
            internal TransactionType TransactionType { get; }
            internal Coin Coin { get; }
            internal string CurrencyCode { get { return Coin.Iso4217Code; } }
            internal TransactionOutcomeKey(Domain domain, TransactionType transactionType, Coin coin)
            {
                Domain = domain;
                TransactionType = transactionType;
                Coin = coin;
            }
        }

        class TransactionOutcome
        {
            private readonly TransactionOutcomes outcomes;
            private readonly JournalEntryTemplates journalEntryTemplates;
            private readonly TransactionOutcomeKey key;
            internal Domain Domain => key.Domain;
            internal TransactionType TransactionType => key.TransactionType;
            internal string CurrencyCode => key.CurrencyCode;
            internal Coin Coin => key.Coin;
            internal JournalEntryTemplate CurrentTemplate { get; private set; }
            internal Parameters CustomParameters { get;} = new Parameters();
            internal FeeParameters FeeParameters { get;} = new FeeParameters();
            internal bool AreFeesEnabled { get; private set; } = true;

            internal TransactionOutcome(TransactionOutcomes outcomes, JournalEntryTemplates journalEntryTemplates, TransactionOutcomeKey key, int templateId)
            {
                this.outcomes = outcomes;
                this.journalEntryTemplates = journalEntryTemplates;
                this.key = key;

                LoadDefaultTemplate(templateId);
            }

            private void LoadDefaultTemplate(int templateId)
            {
                CurrentTemplate = journalEntryTemplates.CreateTemplate(templateId, $"Template for '{Domain.Url}'");
                var defaultTemplate = outcomes.DefaultTemplateFor(TransactionType);
                CurrentTemplate.Parameters.Add(defaultTemplate.Parameters);
                CurrentTemplate.CopyLinesFrom(defaultTemplate);
            }

            internal void EnableTemplate()
            {
                CurrentTemplate.IsActive = true;
            }

            internal void DisableTemplate()
            {
                CurrentTemplate.IsActive = false;
            }

            internal void EnableFee()
            {
                AreFeesEnabled = true;
            }

            internal void DisableFee()
            {
                AreFeesEnabled = false;
            }

            internal void AddCustomTextParameter(string name, string description)
            {
                CustomParameters.AddTextParameter(name, description);
                var parameter = CustomParameters[name];
                CurrentTemplate.Parameters.AddTextParameter((TextParameter)parameter);
            }

            internal void AddCustomAmountParameter(string name, string description, string formula)
            {
                CustomParameters.AddAmountParameter(name, description, formula);
                var parameter = CustomParameters[name];
                CurrentTemplate.Parameters.AddAmountParameter((AmountParameter)parameter);
            }

            internal void UpdateCustomTextParameter(string name, string description)
            {
                CustomParameters.UpdateTextParameter(name, description);
            }

            internal void UpdateCustomAmountParameter(string name, string description, string formula)
            {
                CustomParameters.UpdateAmountParameter(name, description, formula);
            }

            internal void AddCustomParameterAsFee(string name)
            {
                if (!CustomParameters.Exists(name)) throw new GameEngineException($"{nameof(CustomParameters)} '{name}' was not added for '{Domain.Url}', '{TransactionType}' and '{CurrencyCode}'");
                var parameter = CustomParameters[name];
                if (!(parameter is AmountParameter)) throw new GameEngineException($"{nameof(parameter)} is not a {nameof(AmountParameter)} type");
                FeeParameters.Add((AmountParameter)parameter);
            }

            internal void RemoveCustomParameterAsFee(string name)
            {
                if (!CustomParameters.Exists(name)) throw new GameEngineException($"{nameof(CustomParameters)} '{name}' was not added for '{Domain.Url}', '{TransactionType}' and '{CurrencyCode}'");
                var parameter = CustomParameters[name];
                if (!(parameter is AmountParameter)) throw new GameEngineException($"{nameof(parameter)} is not a {nameof(AmountParameter)} type");
                FeeParameters.Remove((AmountParameter)parameter);
            }

            internal bool ContainsCustomParameterAsFee(string name)
            {
                if (!CustomParameters.Exists(name)) return false;
                var parameter = CustomParameters[name];
                if (!(parameter is AmountParameter)) return false;
                return FeeParameters.Contains((AmountParameter)parameter);
            }

            internal bool ContainsCustomParameterAsFee(Parameter parameter)
            {
                if (!(parameter is AmountParameter)) return false;
                return FeeParameters.Contains((AmountParameter)parameter);
            }

        }
    }

}
