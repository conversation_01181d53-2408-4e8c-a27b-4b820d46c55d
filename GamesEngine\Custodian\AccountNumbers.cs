﻿using GamesEngine.Business;
using GamesEngine.Custodian.Operations;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using town.connectors.drivers;
using static GamesEngine.Business.WholePaymentProcessor;

namespace GamesEngine.Custodian
{
	[Puppet]
	internal class AccountNumbers : Objeto
	{
		private int accountIdConsecutive = 0;
		private Dictionary<string, AccountNumber> accounts;

		internal int NextAccountId()
		{
			accountIdConsecutive += 1;
			return accountIdConsecutive;
		}

		internal void Create(bool itsThePresent, int accountId, string processorKey, string number, string currencyCode)
		{
			if (accountId <= 0) throw new GameEngineException($"{nameof(accountId)} must be greater than 0");
			if (string.IsNullOrWhiteSpace(processorKey)) throw new ArgumentNullException(nameof(processorKey));
			if (string.IsNullOrWhiteSpace(number)) throw new ArgumentNullException(nameof(number));
			if (string.IsNullOrWhiteSpace(currencyCode)) throw new ArgumentNullException(nameof(currencyCode));

			Coin currencyToSearch = Coinage.Coin(currencyCode);
			Create(itsThePresent, accountId, processorKey, number, currencyToSearch);
		}

		internal void Create(bool itsThePresent, int accountId, string processorKey, string number, Coin coin)
		{
			if (coin == null) throw new ArgumentNullException(nameof(coin));
			if (accountId <= 0) throw new GameEngineException($"{nameof(accountId)} must be greater than 0");
			if (string.IsNullOrWhiteSpace(processorKey)) throw new ArgumentNullException(nameof(processorKey));
			if (string.IsNullOrWhiteSpace(number)) throw new ArgumentNullException(nameof(number));
			if (Exists(number)) throw new GameEngineException($"{number} already exists.");

			if (accounts == null) accounts = new Dictionary<string, AccountNumber>();

			accountIdConsecutive = accountId;

			AccountNumber accountNumber = new AccountNumber(accountId, number, processorKey, coin);
			accounts.Add(processorKey, accountNumber);

			if (itsThePresent) Integration.Kafka.Send(itsThePresent, Integration.Kafka.TopicForGuardianInternalOperations, accountNumber.GenerateMessage());
		}

		internal AccountNumber Search(string accountNumber)
		{
			if (accounts == null) throw new GameEngineException($"There is no {nameof(AccountNumber)} with the accountNumber {accountNumber}.");

			AccountNumber result = accounts.Values.FirstOrDefault(account => account.Number == accountNumber);
			if (result == null) throw new GameEngineException($"There is no {nameof(AccountNumber)} with the accountNumber {accountNumber}.");

			return result;
		}

		internal AccountNumber SearchBy(PaymentProcessor processor)
		{
			if (processor == null) throw new ArgumentNullException(nameof(processor));
			if (accounts == null) throw new GameEngineException($"There is no {nameof(AccountNumber)} with the processorKey {processor.ProcessorKey}.");

			accounts.TryGetValue(processor.ProcessorKey, out AccountNumber result);
			if (result == null) throw new GameEngineException($"There is no {nameof(AccountNumber)} with the processorKey {processor.ProcessorKey}.");

			return result;
		}

		internal AccountNumber SearchByProcessor(string processorKey)
		{
			if (string.IsNullOrWhiteSpace(processorKey)) throw new ArgumentNullException(nameof(processorKey));
			if (accounts == null) throw new GameEngineException($"There is no {nameof(AccountNumber)} with the processorKey {processorKey}.");

			accounts.TryGetValue(processorKey, out AccountNumber result);
			if (result == null) throw new GameEngineException($"There is no {nameof(AccountNumber)} with the processorKey {processorKey}.");

			return result;
		}

		private bool Exists(string number)
		{
			if (accounts == null) return false;

			return accounts.Values.Any(account => account.Number == number);
		}

		internal bool ContainsProcessor(string processorKey)
		{
			if (string.IsNullOrWhiteSpace(processorKey) || accounts == null) return false;
			return accounts.ContainsKey(processorKey);
		}

		internal int Count()
		{
			if (accounts == null) return 0;

			return accounts.Count;
		}

		internal IEnumerable<AccountNumber> List()
		{
			return accounts.Values.ToList();
		}
		internal IEnumerable<AccountNumber> List(string currencyCode)
		{
			return List(Coinage.Coin(currencyCode)).ToList();
		}

		internal IEnumerable<AccountNumber> List(Coin currencyCode)
		{
			return accounts.Values.Where(account => account.Coin == currencyCode);
		}

		internal AccountNumberAndAccumulates ListAccumulates(DateTime initialDateToSearch, DateTime finalDateToSearch, int inicialIndex, int amountOfRows, string processorId, string accountNumber)
		{
			return ListAccumulates(initialDateToSearch, finalDateToSearch, inicialIndex, amountOfRows, processorId, accountNumber, null, decimal.MinValue, decimal.MaxValue);
		}

		internal AccountNumberAndAccumulates ListAccumulates(DateTime initialDateToSearch, DateTime finalDateToSearch, int inicialIndex, int amountOfRows, string processorId)
		{
			return ListAccumulates(initialDateToSearch, finalDateToSearch, inicialIndex, amountOfRows, processorId, null, null, decimal.MinValue, decimal.MaxValue);
		}
		internal AccountNumberAndAccumulates ListAccumulates(DateTime initialDateToSearch, DateTime finalDateToSearch, int inicialIndex, int amountOfRows, string processorId, string accountNumer, string currencyCode)
		{
			return ListAccumulates(initialDateToSearch, finalDateToSearch, inicialIndex, amountOfRows, processorId, accountNumer, currencyCode, decimal.MinValue, decimal.MaxValue);
		}

		internal AccountNumberAndAccumulates ListAccumulates(DateTime initialDateToSearch, DateTime finalDateToSearch, int inicialIndex, int amountOfRows, string processorId, string accountNumer, string currencyCode, decimal minAmount, decimal maxAmount)
		{
			return ListAccumulates(initialDateToSearch, finalDateToSearch, inicialIndex, amountOfRows, processorId, accountNumer, currencyCode, minAmount, maxAmount, null);
		}
		
		internal AccountNumberAndAccumulates ListAccumulates(DateTime initialDateToSearch, DateTime finalDateToSearch, int inicialIndex, int amountOfRows, string processorId, string accountNumer, string currencyCode, decimal minAmount, decimal maxAmount, string transactionType)
		{
			AccountNumberAndAccumulates collector = new AccountNumberAndAccumulates();

			int stopIndex = inicialIndex + amountOfRows;
			int index = 0;
			bool thereIsAProccessorToSearch = ! string.IsNullOrEmpty(processorId);
			bool thereIsAnAccountNumberToSearch= !string.IsNullOrEmpty(accountNumer);
			bool thereIsACurrencyCodeToSearch = !string.IsNullOrEmpty(currencyCode);
			Coin currencyToSearch = Coinage.Coin(Currencies.CODES.USD);
			if (thereIsACurrencyCodeToSearch) currencyToSearch = Coinage.Coin(currencyCode);
			bool thereIsATransactionTypeToSearch = !string.IsNullOrEmpty(transactionType);
			TransactionType transactionTypeToSearch = TransactionType.Withdrawal;
			if (thereIsATransactionTypeToSearch) transactionTypeToSearch = (TransactionType)Enum.Parse(typeof(TransactionType), transactionType);
			bool stopTheIteration = false;
			if (maxAmount <= 0) maxAmount = Decimal.MaxValue;
			this.IterateAccounts((AccountNumber account) => {

				bool achieveAccountNumberCriteria = !thereIsAnAccountNumberToSearch
							|| (thereIsAnAccountNumberToSearch
							&& account.Number == accountNumer);

				if (achieveAccountNumberCriteria)
				{
					account.IterateAccumulates(initialDateToSearch, finalDateToSearch, (AccumulatesByCurrency accumulatesByCurrency) =>
					{
						accumulatesByCurrency.IterateAccumulate((Accumulate accumulate) =>
						{
							bool theIndexItBetweenTheSearchedFrame = (index >= inicialIndex
								&& stopIndex > index);
							bool achieveProcessorCriteria = !thereIsAProccessorToSearch
								|| (thereIsAProccessorToSearch
								&& accumulate.ContainsProcessor(processorId));
							bool achieveCurrencyCriteria = !thereIsACurrencyCodeToSearch
								|| (thereIsACurrencyCodeToSearch
								&& accumulate.ItsIn(currencyToSearch));
							bool achieveAmountCriteria = accumulate.Value >= minAmount &&
								maxAmount >= accumulate.Value;
							bool achieveTransactionTypeCriteria = !thereIsATransactionTypeToSearch
								|| (thereIsATransactionTypeToSearch
								&& accumulate.TransactionType == transactionTypeToSearch);

							if (theIndexItBetweenTheSearchedFrame
								&& achieveProcessorCriteria
								&& achieveCurrencyCriteria
								&& achieveAmountCriteria
								&& achieveTransactionTypeCriteria)
							{
								collector.Add(new AccountNumberAndAccumulate(account, accumulate.Amount, accumulate.ScheduledDate));
							}

							index++;

							stopTheIteration = (index >= stopIndex);
							return stopTheIteration;
						});
						stopTheIteration = (index >= stopIndex);
						return stopTheIteration;
					});
				}
				
				stopTheIteration = (index >= stopIndex);
				return stopTheIteration;
			});

			return collector;
		}

		private void IterateAccounts(Func<AccountNumber, bool> p)
		{
			if (p == null) return;
			foreach (AccountNumber account in accounts.Values)
			{
				bool stopTheIteration = p.Invoke(account);
				if (stopTheIteration) return;
			}
		}
	}
	[Puppet]
	internal class AccountNumberAndAccumulates : Objeto
	{
		private List<AccountNumberAndAccumulate> accountAndAccumulates = new List<AccountNumberAndAccumulate>();
		internal IEnumerable<AccountNumberAndAccumulate> List()
		{
			return accountAndAccumulates;
		}
		internal void Add(AccountNumberAndAccumulate accountAndAccumulate)
		{
			accountAndAccumulates.Add(accountAndAccumulate);
		}
	}
	[Puppet]
	internal class AccountNumberAndAccumulate : Objeto
	{
		internal AccountNumberAndAccumulate(AccountNumber account, Currency accumulate, DateTime scheduledDate)
		{
			Account = account;
			ScheduledDate = scheduledDate;
			Accumulate = accumulate;
		}

		internal AccountNumber Account { get; }
		internal Currency Accumulate { get; }
		internal string AccumulateCurrencyCodeAsText { get { return Accumulate.CurrencyCodeAsText;  } }
		internal DateTime ScheduledDate { get; }
	}
	[Puppet]
	public class AccountNumber : Objeto
	{
		internal int Id { get; }
		internal string Number { get;}
		internal string ProcessorKey { get; }
		internal Coin Coin { get; }
		internal string CurrencyCode { get { return Coin.Iso4217Code ; } }
		private AccumulastesByDate accumulatesByDate;
		internal string CurrencyCodeAsText
		{
			get
			{
				return CurrencyCode.ToString();
			}
		}

		internal AccountNumber(int id, string number, string processorKey, Coin coin)
		{
			if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");
			if (string.IsNullOrWhiteSpace(number)) throw new GameEngineException($"{nameof(number)} it's required.");
			if (string.IsNullOrWhiteSpace(processorKey)) throw new ArgumentNullException(nameof(processorKey));
			if (coin == null) throw new ArgumentNullException(nameof(coin));

			Id = id;
			Number = number;
			ProcessorKey = processorKey;
			Coin = coin;
		}

		internal void UpdateAccumulate(Coin operationCurrencyCode, DateTime scheduledDate, decimal amount, string processorId, TransactionType transactionType)
		{
			if (accumulatesByDate == null) accumulatesByDate = new AccumulastesByDate();
			accumulatesByDate.UpdateAccumulate(scheduledDate, Currency.Factory(operationCurrencyCode.Iso4217Code, amount), processorId, transactionType);
		}

		internal Currency GetAccumulate(DateTime scheduledDate, string currencyCode)
		{
			return GetAccumulate(scheduledDate, Coinage.Coin(currencyCode));
		}
		internal Currency GetAccumulate(DateTime scheduledDate, Coin coin)
		{
			Accumulate accumulate;
			bool found = GetAccumulate(scheduledDate, coin, out accumulate);
			if (found)
			{
				return accumulate.Amount;
			}

			return Currency.ZeroFactory(coin.Iso4217Code);
		}

		internal bool GetAccumulate(DateTime scheduledDate, Coin coin, out Accumulate accumulate)
		{
			accumulate = null;
			if (accumulatesByDate == null) return false;

			AccumulatesByCurrency amountStored;
			bool found = accumulatesByDate.TryGetValue(scheduledDate, out amountStored);
			if (found)
			{

				return amountStored.Amount(coin, out accumulate);
			}

			return false;
		}
		//internal List<AccumulatesByCurrency> GetAccumulates(DateTime initialDateToSearch, DateTime finalDateToSearch)
		//{
		//	if (accumulatesByDate == null) return new List<AccumulatesByCurrency>();

		//	return accumulatesByDate.List(initialDateToSearch, finalDateToSearch);
		//}

		internal void IterateAccumulates(DateTime initialDateToSearch, DateTime finalDateToSearch, Func<AccumulatesByCurrency, bool> p)
		{
			if (accumulatesByDate == null) return;

			accumulatesByDate.Iterate(initialDateToSearch, finalDateToSearch, p);
		}

		internal KafkaMessage GenerateMessage()
		{
			return new AccountNumberCreationMessage(Id, Number, ProcessorKey, Coin);
		}
	}
	public class AccountNumberStored 
	{
		public AccountNumberStored(int accountNumberId, string accountNumber, Currencies.CODES codes)
		{
			Id = accountNumberId;
			Number = accountNumber;
			CurrencyCode = codes;
		}

		public int Id { get; set; }
		public string Number { get; set; }
		public Currencies.CODES CurrencyCode { get; set; }
		public string CurrencyCodeTxt { get { return CurrencyCode.ToString(); } }
	}
}
