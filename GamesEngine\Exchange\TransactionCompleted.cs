﻿using GamesEngine.Finance;
using Puppeteer.EventSourcing.Libraries;
using System;

namespace GamesEngine.Exchange
{
	internal class TransactionCompleted : Objeto
	{
		internal TransactionResult Result { get; }
		internal TransactionCompleted(TransactionResult transactionResult)
		{
			if (transactionResult == null) throw new ArgumentNullException(nameof(transactionResult));

			Result = transactionResult;
		}

		internal class TransactionResult : Objeto
		{
			internal TransactionResult(Currency net, Currency gross, Currency comission, Currency profit, Currency value, Currency totalFee)
			{
				if (net == null) throw new ArgumentNullException(nameof(net));
				if (gross == null) throw new ArgumentNullException(nameof(gross));
				if (profit == null) throw new ArgumentNullException(nameof(profit));
				if (comission == null) throw new ArgumentNullException(nameof(comission));
				if (value == null) throw new ArgumentNullException(nameof(value));
				if (totalFee == null) throw new ArgumentNullException(nameof(totalFee));

				Net = net;
				Gross = gross;
				Profit = profit;
				Comission = comission;
				Amount = value;
				TotalFee = totalFee;
			}

			internal Currency Net { get; }
			internal Currency Gross { get; }
			internal Currency Comission { get; }
			internal Currency Profit { get; }
			internal Currency Amount { get; }
			internal Currency TotalFee { get; }
		}

		internal class WithdrawalTransactionResult : TransactionResult
		{
			internal WithdrawalTransactionResult(Currency net, Currency gross, Currency comission, Currency profit, Currency value, Currency totalFee, Currency disbursement):
				base(net, gross, comission, profit, value, totalFee)
			{
				if (disbursement == null) throw new ArgumentNullException(nameof(disbursement));

				Disbursement = disbursement;
			}

			internal Currency Disbursement { get; }
		}
	}
}