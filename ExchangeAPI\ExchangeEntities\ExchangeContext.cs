﻿using GamesEngine.Settings;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ExchangeAPI.ExchangeEntities
{
    public class ExchangeContext:freshContext
    {
        public ExchangeContext() : base()
        {
        }
        public ExchangeContext(DbContextOptions<freshContext> options) : base(options)
        {
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (Integration.Db != null && (String.IsNullOrEmpty(Integration.Db.DBSelected) || String.IsNullOrEmpty(Integration.Db.MySQL)))
            {
                throw new Exception("Mysql Db configuration its required.");
            }
            if ( ! optionsBuilder.IsConfigured)
            {
                optionsBuilder.UseMySql(Integration.Db.MySQL);
            }
        }
    }
}
