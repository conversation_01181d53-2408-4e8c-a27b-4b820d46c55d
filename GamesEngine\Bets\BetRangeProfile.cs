﻿using GamesEngine.Domains;
using Puppeteer.EventSourcing.Libraries;
using System;

namespace GamesEngine.Bets
{
    internal class BetRangeProfile:Objeto
    {
        private decimal maxAmount;
        private decimal minAmount;
        private Profile profile;
        private Domain domain;
        private string log;
        private string whoEnteredFirstChange;
        private DateTime dateFirstChange;

        internal decimal MaxAmount
        {
            get
            {
                return maxAmount;
            }
        }

        internal decimal MinAmount
        {
            get
            {
                return minAmount;
            }
        }

        internal Profile Profile
        {
            get
            {
                return profile;
            }
        }

        internal Domain Domain
        {
            get
            {
                return domain;
            }
        }

        internal string Log
        {
            get
            {
                return log;
            }
        }

        internal string WhoEnteredFirstChange
        {
            get
            {
                return whoEnteredFirstChange;
            }
        }

        internal DateTime DateFirstChange
        {
            get
            {
                return dateFirstChange;
            }
        }

        internal BetRangeProfile(Profile profile, Domain domain, decimal maxAmount, decimal minAmount, DateTime now, string employeeName)
        {
            this.profile = profile;
            this.domain = domain;
            this.maxAmount = maxAmount;
            this.minAmount = minAmount;
            dateFirstChange = now;
            whoEnteredFirstChange = employeeName;

            log = $"{employeeName} entered maximum amount {maxAmount} minimum amount {minAmount} for domain '{domain.Url}' at {now}<br>";
        }

        internal void Update(Profile profile, Domain domain, decimal maxAmount, decimal minAmount, DateTime now, string employeeName)
        {
            this.profile = profile;
            this.domain = domain;
            this.maxAmount = maxAmount;
            this.minAmount = minAmount;

            log = $"{employeeName} entered maximum amount {maxAmount} minimum amount {minAmount} for domain '{domain.Url}' at {now}<br>" +log;
        }
    }
}
