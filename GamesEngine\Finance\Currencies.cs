﻿using GamesEngine.Business;
using GamesEngine.MessageQueuing;
using GamesEngine.Settings;
using GamesEngine.Tools;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using town.connectors.drivers;
using static GamesEngine.Finance.Currencies;
using static GamesEngine.PurchaseOrders.CustomerMessage;

namespace GamesEngine.Finance
{
	
	[Puppet]
	public class Currencies : Objeto
	{
		public enum CODES { FP = 0, LR = 1, USD = 2, BTC = 3, USDT = 4, BCH = 5, PAX = 6, ETH = 7, KRW = 8, KRWFP = 9 };
	}

	public class Coin
	{
		internal Coin(int id, string iso4217Code, string sign, int decimalPrecision, char unicode, string name, CoinType type)
		{
			if (id < 0) throw new GameEngineException($"{nameof(id)} must be greater than 0.");
			iso4217Code = iso4217Code.Trim().ToUpper();
			if (decimalPrecision < 0 || decimalPrecision > 28) throw new GameEngineException($"{nameof(decimalPrecision)} cannot be negative and less than 28.");
			if (iso4217Code.Length < 2 || iso4217Code.Length > 5) throw new GameEngineException($"{nameof(iso4217Code)} length must be between 2 and 5.");
			if (string.IsNullOrWhiteSpace(sign)) throw new ArgumentNullException(nameof(sign));
			if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

			Id = id;
			Name = name;
			DecimalPrecision = decimalPrecision;
			Iso4217Code = iso4217Code;
			Sign = sign;
			Unicode = unicode;
			Type = type;

			StringBuilder patternBuilder = new StringBuilder();
			patternBuilder.Append("{0:#,0.");
			patternBuilder.Append('0', decimalPrecision);
			patternBuilder.Append("}");

			Pattern = patternBuilder.ToString();
		}

		internal int Id { get; private set; }
		internal string Name { get; private set; }
		internal string Sign { get; private set; }
        internal int DecimalPrecision { private set; get; }
		internal string Iso4217Code { get; private set; }
		internal string Pattern { get; private set; }
		internal char Unicode { get; private set; }
		internal string UnicodeAsText => Unicode.ToString();
		internal CoinType Type { get; private set; }
		internal string TypeAsText => Type.ToString();
	}


	public class CoinMessage : TypedMessage
	{
		public Coin Coin { get; private set; }

		internal CoinMessage(Coin coin) : base((char)(int)TransactionMessageType.Coin)
		{
			if (coin == null) throw new ArgumentException(nameof(coin));

			Coin = coin;
		}
		internal CoinMessage(Coin coin, CustomerMessageType customerMessageType) : base((char)(int)customerMessageType)
		{
			if (coin == null) throw new ArgumentException(nameof(coin));

			Coin = coin;
		}

		public CoinMessage(string message) : base(message)
		{
		}

		protected override void Deserialize(string[] message, out int fieldOrder)
		{
			base.Deserialize(message, out fieldOrder);
			var id = int.Parse(message[fieldOrder++]);
			var isocode = message[fieldOrder++];
			var sign = message[fieldOrder++];
			var precision = int.Parse(message[fieldOrder++]);
			var unicode = char.Parse(message[fieldOrder++]);
			var name = message[fieldOrder++];
			var type = (CoinType)int.Parse(message[fieldOrder++]);
			Coin = new Coin(id, isocode, sign, precision, unicode, name, type);
		}

        protected override void InternalSerialize()
        {
			base.InternalSerialize();
			AddProperty(Coin.Id).
			AddProperty(Coin.Iso4217Code).
			AddProperty(Coin.Sign).
			AddProperty(Coin.DecimalPrecision).
			AddProperty(Coin.Unicode).
			AddProperty(Coin.Name).
			AddProperty((int)Coin.Type)
			;
		}
    }

	public enum CoinType
    {
		Fiat,
		Crypt,
		Digital
	}

	public static class Coinage
	{
		private static Dictionary<string, Coin> coins;

        public static Coin[] All { get { return coins.Values.ToArray();  } }

        static Coinage()
		{
			var comparer = StringComparer.OrdinalIgnoreCase;
			coins = new Dictionary<string, Coin>(comparer);
		}

		internal static Coin Add(int id, string iso4217Code, string sign, int decimalPrecision, char unicode, string name, CoinType type)
		{
			Coin coin = new Coin(id, iso4217Code, sign, decimalPrecision, unicode, name, type);
			coins.TryAdd(coin.Iso4217Code, coin);

			return coin;
		}

		internal static void Add(Coin coin)
		{
			coins.TryAdd(coin.Iso4217Code, coin);
		}

		internal static int decimalPrecision(string iso4217Code)
		{
            return coins[iso4217Code].DecimalPrecision;
		}
		internal static Coin Coin(string iso4217Code)
		{
			if (string.IsNullOrWhiteSpace(iso4217Code)) throw new ArgumentException(nameof(iso4217Code));
			if (!coins.ContainsKey(iso4217Code)) throw new GameEngineException($@"There is no coin for isoCode {iso4217Code}");
			return coins[iso4217Code];
		}
        internal static char Unicode(string iso4217Code)
        {
			return coins[iso4217Code].Unicode;
		}
		[Obsolete("Do not use this, it is for legacy purposes.")]
		internal static string Type(string iso4217Code)
		{
            switch (iso4217Code.ToLower().Trim())
            {
				case "usd":
					return "Fiat";
				case "fp":
				case "lr":
					return "Digital";
				case "btc":
					return "Crypt";
                case "eth":
                    return "Crypt";
				case "krw":
                    return "Fiat";
				case "krwfp":
                    return "Digital";
                default:
					throw new GameEngineException($@"There is no implementation for isoCode {iso4217Code}");
			}
        }
		[Obsolete("Do not use this, it is for legacy purposes.")]
		public static Coin Coin(CODES currencyCode)
        {
			return coins[currencyCode.ToString()];
		}

        internal static Coin KafkaProperty2Coin(string property)
        {
			int currencyEnumId;
			string currencyCode = int.TryParse(property, out currencyEnumId) ?
			((Currencies.CODES)currencyEnumId).ToString() :
			property;
			return Coinage.Coin(currencyCode);
		}

        internal static Coin GetById(int id)
        {
			var result = coins.Values.Single(coin => coin.Id == id);
			return result;
		}

		internal static Coin GetByName(string name)
		{
			name= name.Trim().ToLower();
			var result = coins.Values.Single(coin => coin.Name.Trim().ToLower() == name);
			return result;
		}
		internal static bool Exists(string iso4217Code)
		{
			var result = coins.Keys.Any(iso => iso == iso4217Code);
			return result;
		}
	}

	public static class CurrencyExtensions
	{
		public static string ToLowercaseString(this Currencies.CODES isoCode)
		{
			switch (isoCode)
			{
				case CODES.FP:
					return "fp";
				case CODES.LR:
					return "lr";
				case CODES.USD:
					return "usd";
				case CODES.KRW:
					return "krw";
				case CODES.KRWFP:
					return "krwfp";
				default:
					throw new GameEngineException($@"There is no implementation for isoCode {isoCode}");
			}
		}
	}

	[Puppet]
	public class Currency : Objeto
	{
		private decimal value;
		//private string sign;
		private string currencyCode;
		private static NumberFormatInfo customFormat;
		private Coin coinInformation;
		//protected enum CODES { FP = 0, LR = 1, USD = 2, BTC = 3, USDT = 4, BCH = 5, PAX = 6, ETH = 7 };

		internal Currency(string iso4217Code, decimal amount)
		{
			if (string.IsNullOrEmpty(iso4217Code)) throw new ArgumentException(nameof(iso4217Code));

			coinInformation = Coinage.Coin(iso4217Code);
			customFormat = (NumberFormatInfo)Integration.CultureInfoEnUS.NumberFormat.Clone();
			customFormat.NumberDecimalSeparator = ".";

			this.value = Decimal.Round(amount, coinInformation.DecimalPrecision);
			this.currencyCode = iso4217Code;
		}

		internal Currency(Coin coin, decimal amount)
		{
			if (coin==null) throw new ArgumentException(nameof(coin));

			coinInformation = coin;
			customFormat = (NumberFormatInfo)Integration.CultureInfoEnUS.NumberFormat.Clone();
			customFormat.NumberDecimalSeparator = ".";

			this.value = Decimal.Round(amount, coinInformation.DecimalPrecision);
			this.currencyCode = coin.Iso4217Code;
		}
		internal Currency(int id, decimal amount):this(Coinage.GetById(id), amount)
		{
		}

		internal void Add(Currency addend)
		{
			if (coinInformation != addend.Coin) throw new GameEngineException($@"Can not add {addend.Sign}{addend.value} to a {coinInformation.Iso4217Code}");

			Add(addend.Value);
		}

		internal void Add(decimal addend)
		{
			decimal storedAddend = this.value;
			this.value = Decimal.Round(storedAddend + addend, coinInformation.DecimalPrecision);
		}

		internal bool ItsZero()
		{
			return value == 0;
		}

		internal void Subtract(Currency substrahend)
		{
			if (coinInformation != substrahend.Coin) throw new GameEngineException($@"Can not add {substrahend.Sign}{substrahend.value} to a {coinInformation.Iso4217Code}");

			Subtract(substrahend.Value);
		}

		internal void Subtract(decimal substrahend)
		{
			decimal minied = this.value;

			this.value = Decimal.Round(minied - substrahend, coinInformation.DecimalPrecision);
		}

		public decimal Value
		{
			get
			{
				return this.value;
			}

		}
		internal string Sign
		{
			get
			{
				return this.coinInformation.Sign;
			}

		}

		internal Coin Coin
		{
			get
			{
				return coinInformation;
			}

		}

		public string CurrencyCode
		{
			get
			{
				return Coin.Iso4217Code;
			}

		}

		public string CurrencyCodeAsText
		{
			get
			{
				return $"{this.currencyCode}";
			}

		}

		internal string MemberTypeName
		{
			get
			{
				return this.GetType().Name.ToString();
			}
		}

		public static string Unicode(string currencyCode)
		{
			return Coinage.Unicode(currencyCode).ToString();
		}

		public static string ToDisplayFormat(string currencyCode, decimal value)
		{
			Coin coin = Coinage.Coin(currencyCode);
			string pattern = coin.Pattern;
            var formattedValue = value.ToString($"F{DecimalPrecision(currencyCode)}", customFormat);
            return $"{Unicode(currencyCode)} {string.Format(pattern, formattedValue) }";
		}

		public string ToDisplayFormat()
		{
			return ToDisplayFormat(this.currencyCode, this.value);
		}

		public static string ToDisplayFormatWithoutSign(string currencyCode, decimal value)
		{
			Coin coin = Coinage.Coin(currencyCode);
			string pattern = coin.Pattern;
	
			return $"{string.Format(pattern, value)}";
		}

		internal static int DecimalPrecision(string currencyCode)
		{
			Coin coin = Coinage.Coin(currencyCode);
			return coin.DecimalPrecision;
		}

		public static Currency ZeroFactory(string currencyCode)
		{
			return new Currency(currencyCode, 0);
		}

		public static Currency Factory(string iso4217Code, decimal amount)
		{
			Currency result = new Currency(iso4217Code, amount);
			return result;
		}
		public static Currency Factory(Coin coin, decimal amount)
		{
			Currency result = new Currency(coin, amount);
			return result;
		}
		public override string ToString()
		{
			return $"{currencyCode}{value}";
		}

		internal void DividedBy(Currency divider)
		{
			if (this.CurrencyCode != divider.CurrencyCode) throw new GameEngineException($" {this}/{divider} it's not valid. Only currencies with the same code can be divided.");
			
			DividedBy(divider.Value);
		}
		internal void DividedBy(decimal divider)
		{
			decimal divisor = this.Value;
			decimal quotient = divisor / divider;
			this.value = Decimal.Round(quotient, Currency.DecimalPrecision(CurrencyCode));
		}

		internal void MultipliedBy(Currency multiplier)
		{
			if (this.CurrencyCode != multiplier.CurrencyCode) throw new GameEngineException($" {this}/{multiplier} it's not valid. Only currencies with the same code can be divided.");

			MultipliedBy(multiplier.Value);
		}
		internal void MultipliedBy(decimal multiplier)
		{
			decimal mutiplicand = this.Value;
			decimal product = mutiplicand * multiplier;
			this.value = Decimal.Round(product, Currency.DecimalPrecision(CurrencyCode));
		}

		public override bool Equals(object o)
		{
			if (o == null) return false;
			if (o == this) return true;
			// If parameter cannot be cast to Point return false.
			Currency p = o as Currency;
			if ((System.Object)p == null) return false;

			return value == p.value && currencyCode == p.CurrencyCode;
		}
	}
	public class CryptCurrency : Currency
	{
		internal CryptCurrency(string isoCode, decimal amount) : base(isoCode, amount)
		{
		}
	}
	public class DigitalCurrency : Currency
	{
		internal DigitalCurrency(string isoCode, decimal amount) : base(isoCode, amount)
		{
		}
	}
	public class FiatCurrency : Currency
	{
		internal FiatCurrency(string isoCode, decimal amount) : base(isoCode, amount)
		{
		}
	}

    internal class Dollar : FiatCurrency
    {
        internal Dollar(decimal amount) : base(CODES.USD.ToString(), amount)
        {
        }
    }

    internal class LottoReward : DigitalCurrency
    {
        internal LottoReward(decimal amount) : base(CODES.LR.ToString(), amount)
        {
        }
    }

    internal class FreePlay : DigitalCurrency
    {
        internal FreePlay(decimal amount) : base(CODES.FP.ToString(), amount)
        {
        }
    }

    internal class Btc : CryptCurrency
    {
        internal Btc(decimal amount) : base(CODES.BTC.ToString(), amount)
        {
        }
    }
    internal class Usdt : CryptCurrency
    {
        internal Usdt(decimal amount) : base(CODES.USDT.ToString(), amount)
        {
        }
    }
    internal class Bch : CryptCurrency
    {
        internal Bch(decimal amount) : base(CODES.BCH.ToString(), amount)
        {
        }
    }
    internal class Pax : CryptCurrency
    {
        internal Pax(decimal amount) : base(CODES.PAX.ToString(), amount)
        {
        }
    }

    internal class Eth : CryptCurrency
    {
        internal Eth(decimal amount) : base(CODES.ETH.ToString(), amount)
        {
        }
    }

	public class CoinExistence
	{
		public bool existsCoin { get; set; }
	}

    internal class Won : FiatCurrency
    {
        internal Won(decimal amount) : base(CODES.KRW.ToString(), amount)
        {
        }
    }

    internal class WonFreePlay : FiatCurrency
    {
        internal WonFreePlay(decimal amount) : base(CODES.KRWFP.ToString(), amount)
        {
        }
    }
}
