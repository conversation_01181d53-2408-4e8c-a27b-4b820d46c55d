﻿using Connectors.town.connectors.commons;
using Connectors.town.connectors.driver.transactions;
using GamesEngine.Finance;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;
using town.connectors.commons;
using static town.connectors.drivers.Result;

namespace town.connectors.drivers.fiero
{
    public class AuthorizationInternal : FieroTenantDriver
	{
		public AuthorizationInternal()
			: base(Tenant_Actions.AuthorizationInternal)
        {
        }

		public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			var result = await AuthorzationAsync(now, recordSet);
			
			return (T)Convert.ChangeType(result, typeof(T));
		}

		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			throw new NotImplementedException();
		}

		public override void Prepare(DateTime now)
		{
			CustomSettings.AddVariableParameter("actor");
			CustomSettings.AddVariableParameter("context");
			CustomSettings.AddVariableParameter("atAddress");
			CustomSettings.AddVariableParameter("purchaseTotal");
			CustomSettings.AddVariableParameter("storeId");
			CustomSettings.AddVariableParameter("concept");
			CustomSettings.AddVariableParameter("referenceNumber");
			CustomSettings.AddVariableParameter("accountNumber");
			CustomSettings.AddVariableParameter("useless");
			CustomSettings.AddVariableParameter("processorId");
			CustomSettings.AddVariableParameter("who");
			CustomSettings.AddVariableParameter("fragmentInformation");
		}

		private async Task<AuthorizationTransaction> AuthorzationAsync(DateTime now, CustomSettings.RecordSet recordSet)
		{
			var Actor = recordSet.Mappings["actor"];
			var Context = recordSet.Mappings["context"];
			var AtAddress = recordSet.Mappings["atAddress"];
			var PurchaseTotalValue = recordSet.Mappings["purchaseTotal"];
			var StoreId = recordSet.Mappings["storeId"];
			var Concept = recordSet.Mappings["concept"];
			var ReferenceNumber = recordSet.Mappings["referenceNumber"];
			var AccountNumber = recordSet.Mappings["accountNumber"];
            var Useless = recordSet.Mappings["useless"];
            var ProcessorId = recordSet.Mappings["processorId"];
			var Who = recordSet.Mappings["who"];
			var FragmentInformation = recordSet.Mappings["fragmentInformation"];

			var actor = Actor.As<RestAPISpawnerActor>();
			var context = Context.As<HttpContext>();
			string atAddress = AtAddress.AsString;
			var purchaseTotal = PurchaseTotalValue.As<Currency>();
			int storeId = StoreId.AsInt;
			string concept = Concept.AsString;
			string reference = ReferenceNumber.AsString;
			string accountNumber = AccountNumber.AsString;
            var useless = Useless.AsDateTime;
            int processorId = ProcessorId.AsInt;
			string who = Who.AsString;
			var fragmentInformation = FragmentInformation.As<FragmentInformation>();
			var result = await PurchaseAsync(actor, context, atAddress, purchaseTotal, storeId, concept, reference, accountNumber, useless, processorId, who, fragmentInformation);
			return result;
		}

		async Task<int> NextAuthorizationNumberAsync()
		{
			return await Movements.StorageAsync.NextValueAuthorizationNumberAsync();
		}

		async Task<AuthorizationTransaction> PurchaseAsync(RestAPISpawnerActor actor, HttpContext context, string atAddress, Currency purchaseTotal, int storeId, string concept, string reference, 
			string accountNumber, DateTime useless, int processorId, string who, FragmentInformation fragmentInformation)
		{
			try
			{
				var hasAccountNumber = !string.IsNullOrWhiteSpace(accountNumber);
				string command = hasAccountNumber ? $@"
					{{
						print atAddress.HasEnoughFor('{accountNumber}', Currency('{purchaseTotal.CurrencyCode}',{purchaseTotal.Value})) hasEnough;
					}}" : 
					$@"
					{{
						print atAddress.AnyAccountHasEnoughFor(Currency('{purchaseTotal.CurrencyCode}',{purchaseTotal.Value})) hasEnough;
					}}";
				var result = await actor.PerformQryAsync(atAddress, context, command);
				if (!(result is OkObjectResult))
				{
					return new AuthorizationTransaction(Authorization.FAKE_TICKET_NUMBER, TransactionStatus.DENIED, Entity, PaymentMethod, CurrencyIsoCodes, AuthorizationResponseCode.AuthorizationFail);
				}

				string jsonResult = (result as ObjectResult).Value.ToString();
				HasEnoughResponse response = JsonConvert.DeserializeObject<HasEnoughResponse>(jsonResult);

				string fragmentCommand = "";
				if (fragmentInformation != null && fragmentInformation.ItsConfigured)
					fragmentCommand = $"authorization.CreateFragments(1, 1, {{'{reference}'}}, {{'{concept}'}}, {purchaseTotal.Value}, {fragmentInformation.Towin});";

				if (response.HasEnough)
				{
					var authorization = await NextAuthorizationNumberAsync();
					if (authorization != Authorization.FAKE_TICKET_NUMBER && authorization > 0)
					{
                        var normalizedUseless = useless.ToString("MM/dd/yyyy HH:mm:ss");
                        command = hasAccountNumber ? $@"
						{{
							store = company.Sales.StoreById( {storeId});
							authorization = atAddress.CreateAuthorization(itIsThePresent,Now,Currency('{purchaseTotal.CurrencyCode}',{purchaseTotal.Value}), {authorization}, store, '{concept}', '{reference}', '{accountNumber}', {normalizedUseless}, {processorId}, '{who}');
							{fragmentCommand}
						}}" : $@"
						{{
							balance = atAddress.CreateBalanceIfNotExists('{purchaseTotal.CurrencyCode}');
							Eval('available = '+balance.Available+';');
							balance.SetInitialBalance(itIsThePresent, available);
							store = company.Sales.StoreById({storeId});
							account = atAddress.SearchAccountWithBalanceGreaterThan(Currency('{purchaseTotal.CurrencyCode}',{purchaseTotal.Value}));
							authorization = atAddress.CreateAuthorization(itIsThePresent,Now,Currency('{purchaseTotal.CurrencyCode}',{purchaseTotal.Value}), {authorization}, store, '{concept}', '{reference}', account, {normalizedUseless}, {processorId}, '{who}');
							{fragmentCommand}
						}}";
						result = await actor.PerformCmdAsync(atAddress, context, command);
						if (!(result is OkObjectResult))
						{
							return new AuthorizationTransaction(Authorization.FAKE_TICKET_NUMBER, TransactionStatus.DENIED, Entity, PaymentMethod, CurrencyIsoCodes, AuthorizationResponseCode.AuthorizationFail);
						}
						return new AuthorizationTransaction(authorization, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes, AuthorizationResponseCode.OK);
					}
					else
					{
						return new AuthorizationTransaction(Authorization.FAKE_TICKET_NUMBER, TransactionStatus.DENIED, Entity, PaymentMethod, CurrencyIsoCodes, AuthorizationResponseCode.AuthorizationFail);
					}
				}
				else
                {
					return new AuthorizationTransaction(Authorization.FAKE_TICKET_NUMBER, TransactionStatus.DENIED, Entity, PaymentMethod, CurrencyIsoCodes, AuthorizationResponseCode.InsufficientFunds);
				}
			}
			catch (Exception e)
			{
				ErrorsSender.Send(e, $"atAddress:{atAddress}", $"purchaseTotal:{purchaseTotal.CurrencyCode.ToString()}{purchaseTotal.Value}", $"storeSequence:{storeId.ToString()}", $"concept:{concept}", $"reference:{reference}");
				return new AuthorizationTransaction(Authorization.FAKE_TICKET_NUMBER, TransactionStatus.DENIED, Entity, PaymentMethod, CurrencyIsoCodes, AuthorizationResponseCode.AuthorizationFail);
			}
		}

		class HasEnoughResponse
		{
			public bool HasEnough { get; set; }
		}
	}
}
