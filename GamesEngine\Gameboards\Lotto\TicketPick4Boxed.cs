﻿using GamesEngine.Bets;
using GamesEngine.Business;
using GamesEngine.Games.Lotto;
using GamesEngine.Tools;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using static GamesEngine.Finance.PaymentChannels;

namespace GamesEngine.Gameboards.Lotto
{
    internal sealed class TicketPick4Boxed : TicketPick<Pick4>
    {
        
        internal TicketPick4Boxed(Player player, Lottery lottery, DateTime drawDate, string number1, string number2, string number3, string number4, Selection selectionMode, DateTime creationDate, decimal ticketCost, Prizes prizes) : 
            base(player, lottery, drawDate, new Pick4(number1, number2, number3, number4), selectionMode, creationDate, ticketCost, prizes)
        {
        }

		internal TicketPick4Boxed(Player player, Lottery lottery, DateTime drawDate, string [] numbers, Selection selectionMode, DateTime creationDate, decimal ticketCost, Prizes prizes) :
			base(player, lottery, drawDate, numbers, selectionMode, creationDate, ticketCost, prizes)
		{
		}

        protected sealed override void GradeNumber(Pick4 winnerNumber, int fireBallNumber = int.MinValue)
        {
            var withFireBall = BelongsToFireBallDraw;
            if (withFireBall && fireBallNumber == int.MinValue) throw new GameEngineException("FireBall number is required");
            if (withFireBall && (fireBallNumber < 0 || fireBallNumber > 9)) throw new GameEngineException($"FireBall number {fireBallNumber} must be between 1 and 2");
            if (winnerNumber.Count != 1) throw new GameEngineException($"Winner {winnerNumber.AsString()} must be a single number");
            int digit1 = winnerNumber[1].SingleDigit;
            int digit2 = winnerNumber[2].SingleDigit;
            int digit3 = winnerNumber[3].SingleDigit;
            int digit4 = winnerNumber[4].SingleDigit;
            bool won = false;
            payout = 0;

            PrizesPicks currPrizes = (PrizesPicks)base.Prizes;
            foreach (Pick4 pick in base.Numbers)
            {
                bool isExcluded1 = IsExcluded(digit1, digit2, digit3, digit4);
                bool isExcluded2 = IsExcluded(digit1, digit2, digit4, digit3);
                bool isExcluded3 = IsExcluded(digit1, digit3, digit2, digit4);
                bool isExcluded4 = IsExcluded(digit1, digit3, digit4, digit2);
                bool isExcluded5 = IsExcluded(digit1, digit4, digit2, digit3);
                bool isExcluded6 = IsExcluded(digit1, digit4, digit3, digit2);
                bool isExcluded7 = IsExcluded(digit2, digit1, digit3, digit4);
                bool isExcluded8 = IsExcluded(digit2, digit1, digit4, digit3);
                bool isExcluded9 = IsExcluded(digit2, digit3, digit1, digit4);
                bool isExcluded10 = IsExcluded(digit2, digit3, digit4, digit1);
                bool isExcluded11 = IsExcluded(digit2, digit4, digit1, digit3);
                bool isExcluded12 = IsExcluded(digit2, digit4, digit3, digit1);
                bool isExcluded13 = IsExcluded(digit3, digit1, digit2, digit4);
                bool isExcluded14 = IsExcluded(digit3, digit1, digit4, digit2);
                bool isExcluded15 = IsExcluded(digit3, digit2, digit1, digit4);
                bool isExcluded16 = IsExcluded(digit3, digit2, digit4, digit1);
                bool isExcluded17 = IsExcluded(digit3, digit4, digit1, digit2);
                bool isExcluded18 = IsExcluded(digit3, digit4, digit2, digit1);
                bool isExcluded19 = IsExcluded(digit4, digit1, digit2, digit3);
                bool isExcluded20 = IsExcluded(digit4, digit1, digit3, digit2);
                bool isExcluded21 = IsExcluded(digit4, digit2, digit1, digit3);
                bool isExcluded22 = IsExcluded(digit4, digit2, digit3, digit1);
                bool isExcluded23 = IsExcluded(digit4, digit3, digit1, digit2);
                bool isExcluded24 = IsExcluded(digit4, digit3, digit2, digit1);

                if (!withFireBall)
                {
                    if (
                    (pick.IsMarked(digit1, digit2, digit3, digit4) && !isExcluded1) ||
                    (pick.IsMarked(digit1, digit2, digit4, digit3) && !isExcluded2) ||
                    (pick.IsMarked(digit1, digit3, digit2, digit4) && !isExcluded3) ||
                    (pick.IsMarked(digit1, digit3, digit4, digit2) && !isExcluded4) ||
                    (pick.IsMarked(digit1, digit4, digit2, digit3) && !isExcluded5) ||
                    (pick.IsMarked(digit1, digit4, digit3, digit2) && !isExcluded6) ||
                    (pick.IsMarked(digit2, digit1, digit3, digit4) && !isExcluded7) ||
                    (pick.IsMarked(digit2, digit1, digit4, digit3) && !isExcluded8) ||
                    (pick.IsMarked(digit2, digit3, digit1, digit4) && !isExcluded9) ||
                    (pick.IsMarked(digit2, digit3, digit4, digit1) && !isExcluded10) ||
                    (pick.IsMarked(digit2, digit4, digit1, digit3) && !isExcluded11) ||
                    (pick.IsMarked(digit2, digit4, digit3, digit1) && !isExcluded12) ||
                    (pick.IsMarked(digit3, digit1, digit2, digit4) && !isExcluded13) ||
                    (pick.IsMarked(digit3, digit1, digit4, digit2) && !isExcluded14) ||
                    (pick.IsMarked(digit3, digit2, digit1, digit4) && !isExcluded15) ||
                    (pick.IsMarked(digit3, digit2, digit4, digit1) && !isExcluded16) ||
                    (pick.IsMarked(digit3, digit4, digit1, digit2) && !isExcluded17) ||
                    (pick.IsMarked(digit3, digit4, digit2, digit1) && !isExcluded18) ||
                    (pick.IsMarked(digit4, digit1, digit2, digit3) && !isExcluded19) ||
                    (pick.IsMarked(digit4, digit1, digit3, digit2) && !isExcluded20) ||
                    (pick.IsMarked(digit4, digit2, digit1, digit3) && !isExcluded21) ||
                    (pick.IsMarked(digit4, digit2, digit3, digit1) && !isExcluded22) ||
                    (pick.IsMarked(digit4, digit3, digit1, digit2) && !isExcluded23) ||
                    (pick.IsMarked(digit4, digit3, digit2, digit1) && !isExcluded24)
                    )
                    {
                        var prizeCriteria = PrizesPicks.WayOfSubticket(digit1, digit2, digit3, digit4);
                        decimal prize = currPrizes.Prize(TicketType.P4B, prizeCriteria);
                        var rawPrice = prize * BetAmount(PickPortion.NORMAL_LEVEL);
                        Commons.ValidateAmount(rawPrice);
                        payout = payout + rawPrice;

                        won = true;
                    }
                }
                else
                {
                    if (
                    (pick.IsMarked(digit1, digit2, digit3, digit4) && !isExcluded1) ||
                    (pick.IsMarked(digit1, digit2, digit4, digit3) && !isExcluded2) ||
                    (pick.IsMarked(digit1, digit3, digit2, digit4) && !isExcluded3) ||
                    (pick.IsMarked(digit1, digit3, digit4, digit2) && !isExcluded4) ||
                    (pick.IsMarked(digit1, digit4, digit2, digit3) && !isExcluded5) ||
                    (pick.IsMarked(digit1, digit4, digit3, digit2) && !isExcluded6) ||
                    (pick.IsMarked(digit2, digit1, digit3, digit4) && !isExcluded7) ||
                    (pick.IsMarked(digit2, digit1, digit4, digit3) && !isExcluded8) ||
                    (pick.IsMarked(digit2, digit3, digit1, digit4) && !isExcluded9) ||
                    (pick.IsMarked(digit2, digit3, digit4, digit1) && !isExcluded10) ||
                    (pick.IsMarked(digit2, digit4, digit1, digit3) && !isExcluded11) ||
                    (pick.IsMarked(digit2, digit4, digit3, digit1) && !isExcluded12) ||
                    (pick.IsMarked(digit3, digit1, digit2, digit4) && !isExcluded13) ||
                    (pick.IsMarked(digit3, digit1, digit4, digit2) && !isExcluded14) ||
                    (pick.IsMarked(digit3, digit2, digit1, digit4) && !isExcluded15) ||
                    (pick.IsMarked(digit3, digit2, digit4, digit1) && !isExcluded16) ||
                    (pick.IsMarked(digit3, digit4, digit1, digit2) && !isExcluded17) ||
                    (pick.IsMarked(digit3, digit4, digit2, digit1) && !isExcluded18) ||
                    (pick.IsMarked(digit4, digit1, digit2, digit3) && !isExcluded19) ||
                    (pick.IsMarked(digit4, digit1, digit3, digit2) && !isExcluded20) ||
                    (pick.IsMarked(digit4, digit2, digit1, digit3) && !isExcluded21) ||
                    (pick.IsMarked(digit4, digit2, digit3, digit1) && !isExcluded22) ||
                    (pick.IsMarked(digit4, digit3, digit1, digit2) && !isExcluded23) ||
                    (pick.IsMarked(digit4, digit3, digit2, digit1) && !isExcluded24)
                    )
                    {
                        var prizeCriteria = PrizesPicks.WayOfSubticket(digit1, digit2, digit3, digit4);
                        decimal prize = currPrizes.Prize(TicketType.P4B, prizeCriteria);
                        var rawPrice = RoundToNearestFiveCents(prize * BetAmount(PickPortion.NORMAL_LEVEL));
                        Commons.ValidateAmount(rawPrice);
                        payout = payout + rawPrice;

                        won = true;
                    }

                    if (
                    (pick.IsMarked(fireBallNumber, digit2, digit3, digit4) && !isExcluded1) ||
                    (pick.IsMarked(fireBallNumber, digit2, digit4, digit3) && !isExcluded2) ||
                    (pick.IsMarked(fireBallNumber, digit3, digit2, digit4) && !isExcluded3) ||
                    (pick.IsMarked(fireBallNumber, digit3, digit4, digit2) && !isExcluded4) ||
                    (pick.IsMarked(fireBallNumber, digit4, digit2, digit3) && !isExcluded5) ||
                    (pick.IsMarked(fireBallNumber, digit4, digit3, digit2) && !isExcluded6) ||
                    (pick.IsMarked(digit2, fireBallNumber, digit3, digit4) && !isExcluded7) ||
                    (pick.IsMarked(digit2, fireBallNumber, digit4, digit3) && !isExcluded8) ||
                    (pick.IsMarked(digit2, digit3, fireBallNumber, digit4) && !isExcluded9) ||
                    (pick.IsMarked(digit2, digit3, digit4, fireBallNumber) && !isExcluded10) ||
                    (pick.IsMarked(digit2, digit4, fireBallNumber, digit3) && !isExcluded11) ||
                    (pick.IsMarked(digit2, digit4, digit3, fireBallNumber) && !isExcluded12) ||
                    (pick.IsMarked(digit3, fireBallNumber, digit2, digit4) && !isExcluded13) ||
                    (pick.IsMarked(digit3, fireBallNumber, digit4, digit2) && !isExcluded14) ||
                    (pick.IsMarked(digit3, digit2, fireBallNumber, digit4) && !isExcluded15) ||
                    (pick.IsMarked(digit3, digit2, digit4, fireBallNumber) && !isExcluded16) ||
                    (pick.IsMarked(digit3, digit4, fireBallNumber, digit2) && !isExcluded17) ||
                    (pick.IsMarked(digit3, digit4, digit2, fireBallNumber) && !isExcluded18) ||
                    (pick.IsMarked(digit4, fireBallNumber, digit2, digit3) && !isExcluded19) ||
                    (pick.IsMarked(digit4, fireBallNumber, digit3, digit2) && !isExcluded20) ||
                    (pick.IsMarked(digit4, digit2, fireBallNumber, digit3) && !isExcluded21) ||
                    (pick.IsMarked(digit4, digit2, digit3, fireBallNumber) && !isExcluded22) ||
                    (pick.IsMarked(digit4, digit3, fireBallNumber, digit2) && !isExcluded23) ||
                    (pick.IsMarked(digit4, digit3, digit2, fireBallNumber) && !isExcluded24)
                    )
                    {
                        var prizeCriteria = PrizesPicks.WayOfSubticket(fireBallNumber, digit2, digit3, digit4);
                        decimal prize = currPrizes.Prize(TicketType.P4B, prizeCriteria);
                        var rawPrice = RoundToNearestFiveCents(prize * BetAmount(PickPortion.LEVEL1_PORTION));
                        Commons.ValidateAmount(rawPrice);
                        payout = payout + rawPrice;

                        won = true;
                    }

                    if (
                    (pick.IsMarked(digit1, fireBallNumber, digit3, digit4) && !isExcluded1) ||
                    (pick.IsMarked(digit1, fireBallNumber, digit4, digit3) && !isExcluded2) ||
                    (pick.IsMarked(digit1, digit3, fireBallNumber, digit4) && !isExcluded3) ||
                    (pick.IsMarked(digit1, digit3, digit4, fireBallNumber) && !isExcluded4) ||
                    (pick.IsMarked(digit1, digit4, fireBallNumber, digit3) && !isExcluded5) ||
                    (pick.IsMarked(digit1, digit4, digit3, fireBallNumber) && !isExcluded6) ||
                    (pick.IsMarked(fireBallNumber, digit1, digit3, digit4) && !isExcluded7) ||
                    (pick.IsMarked(fireBallNumber, digit1, digit4, digit3) && !isExcluded8) ||
                    (pick.IsMarked(fireBallNumber, digit3, digit1, digit4) && !isExcluded9) ||
                    (pick.IsMarked(fireBallNumber, digit3, digit4, digit1) && !isExcluded10) ||
                    (pick.IsMarked(fireBallNumber, digit4, digit1, digit3) && !isExcluded11) ||
                    (pick.IsMarked(fireBallNumber, digit4, digit3, digit1) && !isExcluded12) ||
                    (pick.IsMarked(digit3, digit1, fireBallNumber, digit4) && !isExcluded13) ||
                    (pick.IsMarked(digit3, digit1, digit4, fireBallNumber) && !isExcluded14) ||
                    (pick.IsMarked(digit3, fireBallNumber, digit1, digit4) && !isExcluded15) ||
                    (pick.IsMarked(digit3, fireBallNumber, digit4, digit1) && !isExcluded16) ||
                    (pick.IsMarked(digit3, digit4, digit1, fireBallNumber) && !isExcluded17) ||
                    (pick.IsMarked(digit3, digit4, fireBallNumber, digit1) && !isExcluded18) ||
                    (pick.IsMarked(digit4, digit1, fireBallNumber, digit3) && !isExcluded19) ||
                    (pick.IsMarked(digit4, digit1, digit3, fireBallNumber) && !isExcluded20) ||
                    (pick.IsMarked(digit4, fireBallNumber, digit1, digit3) && !isExcluded21) ||
                    (pick.IsMarked(digit4, fireBallNumber, digit3, digit1) && !isExcluded22) ||
                    (pick.IsMarked(digit4, digit3, digit1, fireBallNumber) && !isExcluded23) ||
                    (pick.IsMarked(digit4, digit3, fireBallNumber, digit1) && !isExcluded24)
                    )
                    {
                        var prizeCriteria = PrizesPicks.WayOfSubticket(digit1, fireBallNumber, digit3, digit4);
                        decimal prize = currPrizes.Prize(TicketType.P4B, prizeCriteria);
                        var rawPrice = RoundToNearestFiveCents(prize * BetAmount(PickPortion.LEVEL2_PORTION));
                        Commons.ValidateAmount(rawPrice);
                        payout = payout + rawPrice;

                        won = true;
                    }

                    if (
                    (pick.IsMarked(digit1, digit2, fireBallNumber, digit4) && !isExcluded1) ||
                    (pick.IsMarked(digit1, digit2, digit4, fireBallNumber) && !isExcluded2) ||
                    (pick.IsMarked(digit1, fireBallNumber, digit2, digit4) && !isExcluded3) ||
                    (pick.IsMarked(digit1, fireBallNumber, digit4, digit2) && !isExcluded4) ||
                    (pick.IsMarked(digit1, digit4, digit2, fireBallNumber) && !isExcluded5) ||
                    (pick.IsMarked(digit1, digit4, fireBallNumber, digit2) && !isExcluded6) ||
                    (pick.IsMarked(digit2, digit1, fireBallNumber, digit4) && !isExcluded7) ||
                    (pick.IsMarked(digit2, digit1, digit4, fireBallNumber) && !isExcluded8) ||
                    (pick.IsMarked(digit2, fireBallNumber, digit1, digit4) && !isExcluded9) ||
                    (pick.IsMarked(digit2, fireBallNumber, digit4, digit1) && !isExcluded10) ||
                    (pick.IsMarked(digit2, digit4, digit1, fireBallNumber) && !isExcluded11) ||
                    (pick.IsMarked(digit2, digit4, fireBallNumber, digit1) && !isExcluded12) ||
                    (pick.IsMarked(fireBallNumber, digit1, digit2, digit4) && !isExcluded13) ||
                    (pick.IsMarked(fireBallNumber, digit1, digit4, digit2) && !isExcluded14) ||
                    (pick.IsMarked(fireBallNumber, digit2, digit1, digit4) && !isExcluded15) ||
                    (pick.IsMarked(fireBallNumber, digit2, digit4, digit1) && !isExcluded16) ||
                    (pick.IsMarked(fireBallNumber, digit4, digit1, digit2) && !isExcluded17) ||
                    (pick.IsMarked(fireBallNumber, digit4, digit2, digit1) && !isExcluded18) ||
                    (pick.IsMarked(digit4, digit1, digit2, fireBallNumber) && !isExcluded19) ||
                    (pick.IsMarked(digit4, digit1, fireBallNumber, digit2) && !isExcluded20) ||
                    (pick.IsMarked(digit4, digit2, digit1, fireBallNumber) && !isExcluded21) ||
                    (pick.IsMarked(digit4, digit2, fireBallNumber, digit1) && !isExcluded22) ||
                    (pick.IsMarked(digit4, fireBallNumber, digit1, digit2) && !isExcluded23) ||
                    (pick.IsMarked(digit4, fireBallNumber, digit2, digit1) && !isExcluded24)
                    )
                    {
                        var prizeCriteria = PrizesPicks.WayOfSubticket(digit1, digit2, fireBallNumber, digit4);
                        decimal prize = currPrizes.Prize(TicketType.P4B, prizeCriteria);
                        var rawPrice = RoundToNearestFiveCents(prize * BetAmount(PickPortion.LEVEL3_PORTION));
                        Commons.ValidateAmount(rawPrice);
                        payout = payout + rawPrice;

                        won = true;
                    }

                    if (
                    (pick.IsMarked(digit1, digit2, digit3, fireBallNumber) && !isExcluded1) ||
                    (pick.IsMarked(digit1, digit2, fireBallNumber, digit3) && !isExcluded2) ||
                    (pick.IsMarked(digit1, digit3, digit2, fireBallNumber) && !isExcluded3) ||
                    (pick.IsMarked(digit1, digit3, fireBallNumber, digit2) && !isExcluded4) ||
                    (pick.IsMarked(digit1, fireBallNumber, digit2, digit3) && !isExcluded5) ||
                    (pick.IsMarked(digit1, fireBallNumber, digit3, digit2) && !isExcluded6) ||
                    (pick.IsMarked(digit2, digit1, digit3, fireBallNumber) && !isExcluded7) ||
                    (pick.IsMarked(digit2, digit1, fireBallNumber, digit3) && !isExcluded8) ||
                    (pick.IsMarked(digit2, digit3, digit1, fireBallNumber) && !isExcluded9) ||
                    (pick.IsMarked(digit2, digit3, fireBallNumber, digit1) && !isExcluded10) ||
                    (pick.IsMarked(digit2, fireBallNumber, digit1, digit3) && !isExcluded11) ||
                    (pick.IsMarked(digit2, fireBallNumber, digit3, digit1) && !isExcluded12) ||
                    (pick.IsMarked(digit3, digit1, digit2, fireBallNumber) && !isExcluded13) ||
                    (pick.IsMarked(digit3, digit1, fireBallNumber, digit2) && !isExcluded14) ||
                    (pick.IsMarked(digit3, digit2, digit1, fireBallNumber) && !isExcluded15) ||
                    (pick.IsMarked(digit3, digit2, fireBallNumber, digit1) && !isExcluded16) ||
                    (pick.IsMarked(digit3, fireBallNumber, digit1, digit2) && !isExcluded17) ||
                    (pick.IsMarked(digit3, fireBallNumber, digit2, digit1) && !isExcluded18) ||
                    (pick.IsMarked(fireBallNumber, digit1, digit2, digit3) && !isExcluded19) ||
                    (pick.IsMarked(fireBallNumber, digit1, digit3, digit2) && !isExcluded20) ||
                    (pick.IsMarked(fireBallNumber, digit2, digit1, digit3) && !isExcluded21) ||
                    (pick.IsMarked(fireBallNumber, digit2, digit3, digit1) && !isExcluded22) ||
                    (pick.IsMarked(fireBallNumber, digit3, digit1, digit2) && !isExcluded23) ||
                    (pick.IsMarked(fireBallNumber, digit3, digit2, digit1) && !isExcluded24)
                    )
                    {
                        var prizeCriteria = PrizesPicks.WayOfSubticket(digit1, digit2, digit3, fireBallNumber);
                        decimal prize = currPrizes.Prize(TicketType.P4B, prizeCriteria);
                        var rawPrice = RoundToNearestFiveCents(prize * BetAmount(PickPortion.LEVEL4_PORTION));
                        Commons.ValidateAmount(rawPrice);
                        payout = payout + rawPrice;

                        won = true;
                    }
                }
			}

			if (won)
			{
                base.GradeAsWinner();
			}
			else
			{
                base.GradeAsLoser();
			}
        }

        protected override decimal BetAmount(PickPortion pickPortion)
        {
            var originalValue = TicketAmount() / Count;
            Commons.ValidateAmount(originalValue);
            if (!BelongsToFireBallDraw) return originalValue;

            decimal porcionSinFireBall = originalValue / 2;
            if (pickPortion == PickPortion.NORMAL_LEVEL) return porcionSinFireBall;

            decimal porcionCombinacion1 = originalValue - porcionSinFireBall;
            porcionCombinacion1 = porcionCombinacion1 / 4;
            if (pickPortion == PickPortion.LEVEL1_PORTION) return porcionCombinacion1;

            decimal porcionCombinacion2 = originalValue - porcionSinFireBall - porcionCombinacion1;
            porcionCombinacion2 = porcionCombinacion2 / 3;
            if (pickPortion == PickPortion.LEVEL2_PORTION) return porcionCombinacion2;

            decimal porcionCombinacion3 = originalValue - porcionSinFireBall - porcionCombinacion1 - porcionCombinacion2;
            porcionCombinacion3 = porcionCombinacion3 / 2;
            if (pickPortion == PickPortion.LEVEL3_PORTION) return porcionCombinacion3;

            decimal porcionCombinacion4 = originalValue - porcionSinFireBall - porcionCombinacion1 - porcionCombinacion2 - porcionCombinacion3;
            if (pickPortion == PickPortion.LEVEL4_PORTION) return porcionCombinacion4;

            throw new GameEngineException("Invalid PickPortion");
        }

        internal override TicketType IdOfType()
        {
            return TicketType.P4B;
        }

        internal override string GameTypeForReports()
        {
            return QueryMakerOfHistoricalPicks.ID_PICK4;
        }

        internal override IEnumerable<TicketByPrize> TicketsByPrize()
        {
            var tickets = new TicketByPrize[5] {
                new TicketByPrizePick4Boxed(this, PrizesPicks.PRIZE_CRITERIA_ALL_NUMBERS_ARE_THE_SAME),
                new TicketByPrizePick4Boxed(this, PrizesPicks.PRIZE_CRITERIA_4_WAY),
                new TicketByPrizePick4Boxed(this, PrizesPicks.PRIZE_CRITERIA_6_WAY),
                new TicketByPrizePick4Boxed(this, PrizesPicks.PRIZE_CRITERIA_12_WAY),
                new TicketByPrizePick4Boxed(this, PrizesPicks.PRIZE_CRITERIA_24_WAY)
            };
            PrizesPicks currPrizes = (PrizesPicks)base.Prizes;
            foreach (SubTicket<IPick> subTicket in SubTickets())
            {
                var prizeCriteria = PrizesPicks.WayOfSubticket(subTicket[1], subTicket[2], subTicket[3], subTicket[4]);
                switch (prizeCriteria)
                {
                    case PrizesPicks.PRIZE_CRITERIA_ALL_NUMBERS_ARE_THE_SAME:
                        tickets[0].Add(subTicket);
                        break;
                    case PrizesPicks.PRIZE_CRITERIA_4_WAY:
                        tickets[1].Add(subTicket);
                        break;
                    case PrizesPicks.PRIZE_CRITERIA_6_WAY:
                        tickets[2].Add(subTicket);
                        break;
                    case PrizesPicks.PRIZE_CRITERIA_12_WAY:
                        tickets[3].Add(subTicket);
                        break;
                    case PrizesPicks.PRIZE_CRITERIA_24_WAY:
                        tickets[4].Add(subTicket);
                        break;
                    default:
                        throw new GameEngineException($"Prize {prizeCriteria} is not valid.");
                }
            }

            var result = tickets.Where(x => x.Count > 0);
            CheckIfItIsOnlyOnePrize(result);
            return result;
        }

        private void CheckIfItIsOnlyOnePrize(IEnumerable<TicketByPrize> ticketsByPrize)
        {
            if (ticketsByPrize.Count() == 1)
            {
                foreach (TicketByPrizePick4Boxed ticketByPrize in ticketsByPrize)
                {
                    ticketByPrize.HasOnlyOnePrize = true;
                }
            }
        }

        internal override void GenerateWagers()
        {
            var betAmount = BetAmount();
            GenerateWagers(betAmount);
        }

        internal override void GenerateWagers(decimal betAmount)
        {
            PrizesPicks currPrizes = (PrizesPicks)base.Prizes;
            foreach (SubTicket<IPick> subticket in SubTickets())
            {
                var prizeCriteria = PrizesPicks.WayOfSubticket(subticket[1], subticket[2], subticket[3], subticket[4]);
                decimal prize = currPrizes.Prize(TicketType.P4B, prizeCriteria);

                prize *= betAmount;
                var prizeToWin = prize - betAmount;
                prizeToWin = prizeToWin < 0 ? 0 : prizeToWin;
                var strNumbers = subticket.AsStringForAccounting();
                AddWager(betAmount, prizeToWin, strNumbers, subticket);
            }
        }
    }

    internal class TicketByPrizePick4Boxed : TicketByPrize
    {
        internal bool HasOnlyOnePrize { get; set; }

        internal TicketByPrizePick4Boxed(Ticket ticket, int prizeCriteria) : base(ticket, prizeCriteria)
        {

        }

        internal override bool HasNumber(int[] numbers)
        {
            var result = Subtickets.Cast<SubTicket<IPick>>().Any(x => x.IsTheSameNumberWithDifferentOrder(numbers[0], numbers[1], numbers[2], numbers[3]));
            return result;
        }

        internal override string AsString()
        {
            if (HasOnlyOnePrize)
            {
                return ((TicketPick4Boxed)Ticket).AsString();
            }

            StringBuilder result = new StringBuilder();
            result.Append(Id);
            foreach (var subticket in Subtickets)
            {
                result.Append(subticket.AsString());
            }
            return result.ToString();
        }
    }
}
