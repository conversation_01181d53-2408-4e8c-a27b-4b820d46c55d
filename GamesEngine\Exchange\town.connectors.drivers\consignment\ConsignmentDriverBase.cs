﻿using log4net;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using town.connectors.drivers;

namespace Connectors.town.connectors.drivers.consignment
{
    public abstract class ConsignmentTenantDriver : TenantDriver
    {
        private const float VERSION = 1.0F;
        protected const int FAKE_DOCUMENT_NUMBER = -1;

        protected string SystemId { get; set; }
        protected string Password { get; set; }
        protected string ServicesUrl { get; set; }

        protected HttpClient httpClient = new HttpClient();

        public ConsignmentTenantDriver(Tenant_Actions tenantAction)
            : base(tenantAction, "Consignment", PaymentMethod.P2PTransfer, "USD", VERSION)
        {
        }

        public override string Description => "Consignment driver";
        public override string Fabricator => "Ncubo";
        public override DateTime ReleaseDate => new DateTime(2019, 01, 01);
    }

    public abstract class ConsignmentProcessorDriver : ProcessorDriver
    {
        private const float VERSION = 1.0F;
        protected const int FAKE_DOCUMENT_NUMBER = -1;

        protected string SystemId { get; set; }
        protected string Password { get; set; }
        protected string ServicesUrl { get; set; }

        protected HttpClient httpClient = new HttpClient();

        public ConsignmentProcessorDriver(TransactionType transactionType)
        : base("Consignment", PaymentMethod.P2PTransfer, "USD", transactionType, VERSION)
        {
        }

        public override string Description => "Consignment driver";
        public override string Fabricator => "Ncubo";
        public override DateTime ReleaseDate => new DateTime(2019, 01, 01);
    }
}