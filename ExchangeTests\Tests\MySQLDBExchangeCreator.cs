﻿using ExchangeAPI.ExchangeEntities;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ExchangeTests.Tests
{
    public class MySQLDBExchangeCreator: DBExchangeCreator
    {
        
        public MySQLDBExchangeCreator(string strConnection)
        : base(
            new DbContextOptionsBuilder<freshContext>()
                .UseMySql(strConnection)
                .Options)
        {
        }
    }
}
