﻿using ExchangeAPI.ExchangeEntities;
using GamesEngine.Exchange.Persistance;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ExchangeAPI.Logic
{
    public class Domains : IDomains
    {
        public Domains()
        {
        }
        public ExchangeEntities.Domains Domain(string url)
        {
            if (string.IsNullOrWhiteSpace(url)) throw new ArgumentNullException(nameof(url));

            var domainOrNull = DomainOrNull(url);
            var existsDomain = domainOrNull != null;
            if (!existsDomain)
            {
                domainOrNull = new ExchangeEntities.Domains() { Domain = url };
                Save(domainOrNull);
            }
            return domainOrNull;
        }

        private static ExchangeEntities.Domains DomainOrNull(string url)
        {
            if (string.IsNullOrWhiteSpace(url)) throw new ArgumentNullException(nameof(url));

            using (var context = new ExchangeContext())
            {
                var user = context.Domains.FirstOrDefault(rate => rate.Domain == url);
                return user;
            }
        }

        public static void Save(ExchangeEntities.Domains domain)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            using (var context = new ExchangeContext())
            {
                context.Domains.Add(domain);
                context.SaveChanges();
            }
        }
    }
}
