﻿using GamesEngine.Bets;
using GamesEngine.Games.Lotto;
using GamesEngine.Tools;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Gameboards.Lotto
{
    internal abstract class TicketKeno : Ticket
    {
        private const int CRITERIA_HAS_NOT_BEEN_ASSIGNED = int.MinValue;
        private int prizeCriteria = CRITERIA_HAS_NOT_BEEN_ASSIGNED;
        private Keno number;
        internal TicketKeno(Player player, decimal baseCost, Lottery lottery, DateTime drawDate, string drawId, int[] numbers, bool multiplier, bool bulleye, DateTime creationDate, decimal ticketCost, Prizes prizes) :
              base(player, lottery, drawDate, Selection.MultipleInputSingleAmount, creationDate, ticketCost, prizes)
        {
            if (string.IsNullOrEmpty(drawId)) throw new ArgumentNullException(nameof(drawId));

            if (numbers.Length == 10)
            {
                number = new Keno(numbers[0], numbers[1], numbers[2], numbers[3], numbers[4], numbers[5], numbers[6], numbers[7], numbers[8], numbers[9], multiplier, bulleye);
            }
            else if (numbers.Length == 12)
            {
                number = new Keno(numbers[0], numbers[1], numbers[2], numbers[3], numbers[4], numbers[5], numbers[6], numbers[7], numbers[8], numbers[9], numbers[10], numbers[11], multiplier, bulleye);
            }
            else
            {
                throw new GameEngineException($"{numbers.Length} is not supported yet.");
            }
            DrawId = drawId;
            Multiplier = multiplier;
            Bulleye = bulleye;
            BaseCost = baseCost;
        }

        internal string AsStringForAccounting()
        {
            return number.AsStringForAccounting();
        }

        internal override void GenerateWagers()
        {
            var betAmount = BetAmount();
            GenerateWagers(betAmount);
        }

        internal override void GenerateWagers(decimal betAmount)
        {
            //decimal prize = 1;TODO Keno
            decimal prize = base.Prizes.MaxPrizeFor(IdOfType());
            prize *= betAmount;

            var prizeToWin = prize - betAmount;
            prizeToWin = prizeToWin < 0 ? 0 : prizeToWin;

            var strNumbers = AsStringForAccounting();
            AddWager(betAmount, prizeToWin, strNumbers, SubTickets().ElementAt(0));
        }

        internal override int Count => 1;

        internal string DrawId { get; }
        public bool Multiplier { get; }
        public bool Bulleye { get; }
        public decimal BaseCost { get; }
        private MultiplierDraw GradedMultiplier { get; set; }
        private BulleyeDraw GradedBulleye { get; set; }

        internal override string AsString()
        {
            StringBuilder result = new StringBuilder();
            result.Append(IdOfType().ToString());
            result.Append(number.AsString());
            return result.ToString();
        }

        internal override bool IsPowerball()
        {
            return false;
        }

        internal override void LotteryDraw(LotteryDraw lotteryDraw)
        {
            if (!(lotteryDraw is LotteryDrawKeno)) throw new GameEngineException($"{lotteryDraw.GetType().Name} is not valid for {nameof(TicketKeno)}.");
            if (!base.IsUnprized()) throw new GameEngineException($"Ticket should be in {GameboardStatus.UNPRIZED} status but it is at {Prizing}");
            if (lotteryDraw == null) throw new ArgumentNullException(nameof(lotteryDraw));

            var draw = (LotteryDrawKeno)lotteryDraw;

            int[] sequenceOfNumbers = draw.Numbers;
            var winnerNumber = new KenoDraw(sequenceOfNumbers[0], sequenceOfNumbers[1], sequenceOfNumbers[2], sequenceOfNumbers[3], sequenceOfNumbers[4], sequenceOfNumbers[5], sequenceOfNumbers[6], sequenceOfNumbers[7], sequenceOfNumbers[8], sequenceOfNumbers[9], sequenceOfNumbers[10], sequenceOfNumbers[11], sequenceOfNumbers[12], sequenceOfNumbers[13], sequenceOfNumbers[14], sequenceOfNumbers[15], sequenceOfNumbers[16], sequenceOfNumbers[17], sequenceOfNumbers[18], sequenceOfNumbers[19],
                draw.MultiplierNumber, draw.BulleyeNumber);
            GradeNumber(winnerNumber);
            base.Draw = draw;
        }

        private void GradeNumber(KenoDraw winnerNumber)
        {
            if (winnerNumber.Count != 20) throw new GameEngineException($"Winner {winnerNumber.AsString()} must be a 20 numbers");

            this.prizeCriteria = CalculatePrizeCriteria(winnerNumber);
            if (this.prizeCriteria != 0)
            {
                base.GradeAsWinner();
            }
            else
            {
                base.GradeAsLoser();
            }

            payout = this.prizeCriteria;
            GradedMultiplier = winnerNumber.Multiplier;
            GradedBulleye = winnerNumber.Bulleye;

        }

        internal int NumbersMatches { get; private set; } = 0;
        protected int CalculatePrizeCriteria(KenoDraw winnerNumber)
        {
            var criteria = 0;
            PrizesKeno currPrizes = (PrizesKeno)base.Prizes;
            NumbersMatches = number.WhiteMatches(winnerNumber);

            TicketType type;
            if (number.Length == 10)
            {
                type = TicketType.K10;
            }
            else if (number.Length == 12)
            {
                type = TicketType.K12;
            }
            else
            {
                throw new GameEngineException($"Numbers with length {number.Length} are not supported yet.");
            }

            switch (NumbersMatches)
            {
                case 0:
                    criteria = currPrizes.Prize(type, PrizesKeno.ZERO_BALL);
                    break;
                case 1:
                    criteria = currPrizes.Prize(type, PrizesKeno.ONE_BALL);
                    break;
                case 2:
                    criteria = currPrizes.Prize(type, PrizesKeno.TWO_BALL);
                    break;
                case 3:
                    criteria = currPrizes.Prize(type, PrizesKeno.THREE_BALL);
                    break;
                case 4:
                    criteria = currPrizes.Prize(type, PrizesKeno.FOUR_BALL);
                    break;
                case 5:
                    criteria = currPrizes.Prize(type, PrizesKeno.FIVE_BALL);
                    break;
                case 6:
                    criteria = currPrizes.Prize(type, PrizesKeno.SIX_BALL);
                    break;
                case 7:
                    criteria = currPrizes.Prize(type, PrizesKeno.SEVEN_BALL);
                    break;
                case 8:
                    criteria = currPrizes.Prize(type, PrizesKeno.EIGHT_BALL);
                    break;
                case 9:
                    criteria = currPrizes.Prize(type, PrizesKeno.NINE_BALL);
                    break;
                case 10:
                    criteria = currPrizes.Prize(type, PrizesKeno.TEN_BALL);
                    break;
                case 11:
                    criteria = currPrizes.Prize(type, PrizesKeno.ELEVEN_BALL);
                    break;
                case 12:
                    criteria = currPrizes.Prize(type, PrizesKeno.TWELVE_BALL);
                    break;
                default:
                    throw new GameEngineException("Not supported yet.");
            }
            return criteria;
        }
        internal int CalculateBulleyePrizeCriteria()
        {
            var criteria = 0;
            Prizes currPrizes = base.Prizes;

            TicketType type;
            if (number.Length == 10)
            {
                type = TicketType.K10;
            }
            else if (number.Length == 12)
            {
                type = TicketType.K12;
            }
            else
            {
                throw new GameEngineException($"Numbers with length {number.Length} are not supported yet.");
            }

            if (number.Bulleye.Exists && number.HasNumber(GradedBulleye.ToInt()))
            {
                switch (NumbersMatches)
                {
                    case 0:
                        criteria = 0;
                        break;
                    case 1:
                        criteria = currPrizes.Prize(type, PrizesKeno.ONE_BALL_BULLEYE);
                        break;
                    case 2:
                        criteria = currPrizes.Prize(type, PrizesKeno.TWO_BALL_BULLEYE);
                        break;
                    case 3:
                        criteria = currPrizes.Prize(type, PrizesKeno.THREE_BALL_BULLEYE);
                        break;
                    case 4:
                        criteria = currPrizes.Prize(type, PrizesKeno.FOUR_BALL_BULLEYE);
                        break;
                    case 5:
                        criteria = currPrizes.Prize(type, PrizesKeno.FIVE_BALL_BULLEYE);
                        break;
                    case 6:
                        criteria = currPrizes.Prize(type, PrizesKeno.SIX_BALL_BULLEYE);
                        break;
                    case 7:
                        criteria = currPrizes.Prize(type, PrizesKeno.SEVEN_BALL_BULLEYE);
                        break;
                    case 8:
                        criteria = currPrizes.Prize(type, PrizesKeno.EIGHT_BALL_BULLEYE);
                        break;
                    case 9:
                        criteria = currPrizes.Prize(type, PrizesKeno.NINE_BALL_BULLEYE);
                        break;
                    case 10:
                        criteria = currPrizes.Prize(type, PrizesKeno.TEN_BALL_BULLEYE);
                        break;
                    case 11:
                        criteria = currPrizes.Prize(type, PrizesKeno.ELEVEN_BALL_BULLEYE);
                        break;
                    case 12:
                        criteria = currPrizes.Prize(type, PrizesKeno.TWELVE_BALL_BULLEYE);
                        break;
                    default:
                        throw new GameEngineException("Not supported yet.");
                }
            }
            return criteria;
        }
        internal override bool NumbersFollowPattern()
        {
            return false;
        }

        internal override void RemoveWager(TicketWager wager)
        {
            if (CountWagers == 1) throw new GameEngineException("Cannot remove the last wager. Please remove the ticket.");

            Bet bet = this.Player.FindBet(this);
            foreach (var subticket in wager.Subtickets)
            {
                bet.Contribution -= BetAmount();
            }

            RemoveThisWager(wager);
        }

        internal override IEnumerable<SubTicket<IPick>> SubTickets()
        {
            return number.SubTickets();
        }

        internal override IEnumerable<SubTicket<IPick>> Permute()
        {
            throw new GameEngineException($"Powerball cannot be permuted");
        }

        internal override IEnumerable<TicketByPrize> TicketsByPrize()
        {
            var tickets = new TicketByPrize[] {
                new TicketByPrizeKeno(this, PrizesKeno.ZERO_BALL)
            };
            foreach (SubTicket<IPick> subTicket in SubTickets())
            {
                tickets[0].Add(subTicket);
            }
            return tickets;
        }

        internal override bool WasCreatedByPattern()
        {
            return false;
        }

        internal override decimal CalculatedPrize()
        {
            decimal payout = Grade() * BaseCost * CountOfWinners();
            if (GradedMultiplier != null)
            {
                payout = (number.Multiplier.Enabled) ? GradedMultiplier.Multiply(payout) : payout;
            }
            if (number.Bulleye.Exists)
            {
                payout = payout + CalculateBulleyePrizeCriteria();
            }

            return payout;
        }

        internal string SelectedNumbers => number.SelectedNumbers;
        internal string DrawIdPrefix => KenoNextDraw.Date2PrefixId(DrawDate);

        internal override decimal BetAmount()
        {
            Commons.ValidateAmount(BaseCost);
            return BaseCost;
        }

        internal override string WinnerNumbers()
        {
            var tempWinnerNumbers = string.IsNullOrWhiteSpace(Draw.SequenceOfNumbers) ? string.Empty : Draw.SequenceOfNumbers.Substring(0, Draw.SequenceOfNumbers.Length - 4);
            var listWinnerNumbers = Enumerable.Range(0, tempWinnerNumbers.Length / 2).Select(i => tempWinnerNumbers.Substring(i * 2, 2).TrimStart('0'));
            var winnerNumbers = new List<int>();
            foreach (var winnerAsText in listWinnerNumbers)
            {
                var winner = int.Parse(winnerAsText);
                if (number.HasNumber(winner)) winnerNumbers.Add(winner);
            }

            var result = string.Join(',', winnerNumbers);
            return result;
        }

        internal decimal CalculateMultiplierPrize()
        {
            decimal payout = Grade() * BaseCost;
            var result = 0m;
            if (number.Multiplier.Enabled) result = GradedMultiplier.Multiply(payout);
            return result;
        }
    }

    internal class TicketByPrizeKeno : TicketByPrize
    {
        internal TicketByPrizeKeno(Ticket ticket, int prizeCriteria) : base(ticket, prizeCriteria)
        {

        }

        internal override string AsString()
        {
            var result = Ticket.AsString();
            return result;
        }

        internal override decimal TicketAmount()
        {
            var ticket = (TicketKeno)Ticket;
            var amount = ticket.TicketAmount();
            return amount;
        }

        internal override bool HasNumber(int[] numbers)
        {
            var ticket = (TicketKeno)Ticket;
            var result = ticket.IdOfType() == TicketType.K10 ? 
                ticket.AsString() == $"{Ticket.IdOfType().ToString()}[{numbers[0]},{numbers[1]},{numbers[2]},{numbers[3]},{numbers[4]},{numbers[5]},{numbers[6]},{numbers[7]},{numbers[8]},{numbers[9]},{numbers[10]},{numbers[11]}]":
                ticket.AsString() == $"{Ticket.IdOfType().ToString()}[{numbers[0]},{numbers[1]},{numbers[2]},{numbers[3]},{numbers[4]},{numbers[5]},{numbers[6]},{numbers[7]},{numbers[8]},{numbers[9]},{numbers[10]},{numbers[11]},{numbers[12]},{numbers[13]}]";
            return result;
        }

        internal override IEnumerable<TicketWager> Wagers()
        {
            return base.Wagers();
        }
    }
}
