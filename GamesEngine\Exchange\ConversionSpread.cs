﻿using GamesEngine.Finance;
using Puppeteer.EventSourcing.Libraries;
using System;
using static GamesEngine.Exchange.CurrencyPair;
using static GamesEngine.Exchange.FloatingExchangeRate;

namespace GamesEngine.Exchange
{
	internal interface IConversionSpread
	{
		Currency Sale(Currency amount, out Currency gross, out Currency profit, out Currency net);

		FloatingExchangeRate PurchaseRate { get; }
		long Id { get; }
		FloatingExchangeRate SaleRate { get; }

		string SaleRateSummary();

		Coin SaleRateBaseCurrency();

		Coin SaleRateQouteCurrency();
		decimal SaleRatePrice();
		int TransactionsUsingIt();
		void IncreaseTheNumberOfTransactionsUsingIt();
		void DecreaseTheNumberOfTransactionsUsingIt();
		RateResult GenerateRateResult();
	}
	internal abstract class ConversionSpread : Objeto, IConversionSpread
	{
		enum ConversionSpreadStatus
		{
			Created = 1,
			Active = 2,
			NoActive = 3
		}

		private ConversionSpreadStatus status = ConversionSpreadStatus.Created;
		private readonly CurrencyPair.RateMarkup markup;
		internal ConversionSpread(long id, ExchangeRates exchangeRates, FloatingExchangeRate.SaleRate saleRate, FloatingExchangeRate.PurchaseRate purchaseRate)
		{
			if (exchangeRates == null) throw new ArgumentNullException(nameof(exchangeRates));
			if (saleRate == null) throw new ArgumentNullException(nameof(exchangeRates));
			if (purchaseRate == null) throw new ArgumentNullException(nameof(exchangeRates));
			if (!saleRate.CanCreateACoversionSpreadWith(purchaseRate)) throw new GameEngineException($"The {purchaseRate} must be a {saleRate.Target}/{saleRate.Source} Rate and sale date {saleRate.Date} must be the same than purchase date{purchaseRate.Date}");

			SaleRate = saleRate;
			PurchaseRate = purchaseRate;

			Date = saleRate.Date;
			this.id = id;
			this.markup = saleRate.CalculateMarkupWith(purchaseRate);

		}

		internal bool IsaValidCurrency(Coin currentCode)
		{
			return SaleRate.IsaValidCurrency(currentCode);
		}

		public Currency Sale(Currency amount, out Currency gross, out Currency comission, out Currency net)
		{
			RateMarkup markup = this.markup;
			bool itsMarkupExactlyZero = markup.Value == 0;

			comission = SaleRate.Comission(amount, markup);
			bool decimalPrecisionLostForaTinyMarkup = !itsMarkupExactlyZero && comission.Value == 0;

			if (decimalPrecisionLostForaTinyMarkup)
			{
				decimal powedNumber = (decimal)Math.Pow(10, Currency.DecimalPrecision(comission.CurrencyCode) );
				decimal minPossibleValue = 1 / powedNumber;
				comission.Add(minPossibleValue);
			}

			gross = amount;
			net = Currency.Factory(amount.CurrencyCode, gross.Value - comission.Value);
			
			return SaleRate.Convert(net);
		}

		public FloatingExchangeRate PurchaseRate { get; }
		public FloatingExchangeRate SaleRate { get; }
		internal DateTime Date { get; }

		private readonly long id;
		public long Id { get { return this.id; } }

		internal void MarkAsNoActive()
		{
			status = ConversionSpreadStatus.NoActive;
		}
		internal void MarkAsActive()
		{
			status = ConversionSpreadStatus.Active;
		}
		public override string ToString()
		{
			return $"{SaleRate.Summary()} | {PurchaseRate.Summary()}";
		}

		public string SaleRateSummary()
		{
			return $"{SaleRate.Summary()}";
		}

		public Coin SaleRateBaseCurrency()
		{
			return SaleRate.Target;
		}

		public Coin SaleRateQouteCurrency()
		{
			return SaleRate.Source;
		}
		public decimal SaleRatePrice()
		{
			return SaleRate.Price;
		}

		private int transactionsUsingIt=0;
		public int TransactionsUsingIt()
		{
			return transactionsUsingIt;
		}
		public void IncreaseTheNumberOfTransactionsUsingIt()
		{
			transactionsUsingIt++;
		}
		public void DecreaseTheNumberOfTransactionsUsingIt()
		{
			transactionsUsingIt--;
		}

		public RateResult GenerateRateResult()
		{
			return new RateResult(true, this.Id, $"{this.SaleRateSummary()}", $"{this.SaleRateBaseCurrency().Iso4217Code}", $"{this.SaleRateQouteCurrency().Iso4217Code}", this.SaleRatePrice());
		}
	}

	internal sealed class NoConversionSpread : IConversionSpread
	{
		private static NoConversionSpread noConversionSpread;
		private NoConversionSpread(){ }

		public FloatingExchangeRate PurchaseRate => throw new GameEngineException($"There is no a valid {nameof(PurchaseRate)} in {nameof(NoConversionSpread)}.");

		public Currency Sale(Currency amount, out Currency gross, out Currency profit, out Currency net)
		{
			throw new GameEngineException($"There is no a valid {nameof(Sale)} in {nameof(NoConversionSpread)}.");
		}

		internal static NoConversionSpread GetInstance() 
		{
			if(noConversionSpread == null) noConversionSpread = new NoConversionSpread();
			return noConversionSpread;
		}
		public long Id { get { return 0; } }

		public FloatingExchangeRate SaleRate => throw new GameEngineException($"There is no a valid {nameof(PurchaseRate)} in {nameof(NoConversionSpread)}.");

		public string SaleRateSummary()
		{
			return "No rate.";
		}

		private int transactionsUsingIt = 0;
		public int TransactionsUsingIt()
		{
			return transactionsUsingIt;
		}
		public void IncreaseTheNumberOfTransactionsUsingIt()
		{
			transactionsUsingIt++;
		}
		public void DecreaseTheNumberOfTransactionsUsingIt()
		{
			transactionsUsingIt--;
		}

		public Coin SaleRateBaseCurrency()
		{
			return SaleRate.Target;
		}

		public Coin SaleRateQouteCurrency()
		{
			return SaleRate.Source;
		}

		public decimal SaleRatePrice()
		{
			return SaleRate.Price;
		}

		public RateResult GenerateRateResult()
		{
			return new RateResult(false, this.Id, this.SaleRateSummary(), $"{this.SaleRateBaseCurrency()}", $"{this.SaleRateQouteCurrency()}", this.SaleRatePrice());
		}
	}

	internal sealed class RegularConversionSpread : ConversionSpread
	{
		internal RegularConversionSpread(long id, ExchangeRates exchangeRates, FloatingExchangeRate.SaleRate saleRate, FloatingExchangeRate.PurchaseRate purchaseRate) 
			: base(id, exchangeRates, saleRate, purchaseRate)
		{
		}
	}
	internal sealed class SpecialConversionSpread : ConversionSpread
	{
		internal SpecialConversionSpread(long id, string name, ExchangeRates exchangeRates, FloatingExchangeRate.SaleRate saleRate, FloatingExchangeRate.PurchaseRate purchaseRate) : 
			base(id, exchangeRates, saleRate, purchaseRate)
		{
			if (string.IsNullOrEmpty(name)) throw new ArgumentNullException(nameof(name));
			Name = name.Trim().ToLower();
		}

		internal string Name { get; }

	}

}