﻿using GamesEngine.Finance;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using static GamesEngine.Exchange.Batch.BatchSet;
using static GamesEngine.Finance.Currencies;

namespace GamesEngine.Exchange.Batch
{
	[Puppet]
	internal class BatchTransactions : Objeto
	{
		private readonly int batchNumber;
		private readonly BatchSet batch;
		private readonly DateTime creationDate;
		private readonly InBalances inAccounts;
		private readonly OutBalances outAccounts;
		private readonly BatchMovements batchMovements;
		private readonly Dictionary<int, TransactionDefinition> temporaryDefinitions;
		private Dictionary<int, Transaction> draftTransactions;
		private Dictionary<string, Transaction> transactionsByTrackingInformation;
		private Marketplace marketplace;

		internal BatchTransactions(Marketplace marketplace, int batchNumber, BatchSet batch, DateTime now, OutBalances outAccounts, InBalances inAccounts)
		{
			if (marketplace == null) throw new ArgumentNullException(nameof(marketplace));
			if (batchNumber <= 0) throw new GameEngineException("Invalid batch number");
			if (now == default(DateTime)) throw new ArgumentNullException(nameof(now));
			if (outAccounts == null) throw new ArgumentNullException(nameof(outAccounts));
			if (inAccounts == null) throw new ArgumentNullException(nameof(inAccounts));

			this.batchNumber = batchNumber;
			this.outAccounts = outAccounts;
			this.inAccounts = inAccounts;
			this.creationDate = now;
			this.marketplace = marketplace;
			this.batch = batch;
			this.temporaryDefinitions = new Dictionary<int, TransactionDefinition> ();
			this.draftTransactions = new Dictionary<int, Transaction>();
			this.transactionsByTrackingInformation = new Dictionary<string, Transaction>();
			
			this.batchMovements = new BatchMovements();
		}

		internal void Deposit(Currency amount, string employeeName, DateTime now)
		{
			if (now == default(DateTime)) throw new ArgumentNullException(nameof(now));
			if (amount == null) throw new ArgumentNullException(nameof(amount));
			if (batch.Status != BatchStatus.READY) throw new GameEngineException($"Initial deposit is not allowed when batch status is different to {BatchStatus.READY.ToString()}");

			BatchAccount sourceAccount = outAccounts.FindAccountByCurrency(amount.Coin);
			sourceAccount.Credit(amount);
		}

		internal void Initial(Currency amount, string employeeName, DateTime now)
		{
			if (now == default(DateTime)) throw new ArgumentNullException(nameof(now));
			if(amount == null) throw new ArgumentNullException(nameof(amount));
			if (batch.Status != BatchStatus.READY) throw new GameEngineException($"Initial deposit is not allowed when batch status is different to {BatchStatus.READY.ToString()}");

			BatchAccount sourceAccount = outAccounts.FindAccountByCurrency(amount.Coin);
			sourceAccount.InitialAmount(amount);
		}

		internal string OutAccountFor(Coin currencyCode)
		{
			return Batch().OutAccountFor(currencyCode);
		}

		internal void DebitInitialAmount(Currency amount, string employeeName, DateTime now)
		{
			if (now == default(DateTime)) throw new ArgumentNullException(nameof(now));
			if (amount == null) throw new ArgumentNullException(nameof(amount));
			if (batch.Status != BatchStatus.READY) throw new GameEngineException($"Initial deposit is not allowed when batch status is different to {BatchStatus.READY.ToString()}");

			BatchAccount sourceAccount = outAccounts.FindAccountByCurrency(amount.Coin);
			sourceAccount.DebitInitialAmount(amount);
		}

		internal BatchSet Batch()
		{
			return batch;
		}

		internal void IndexTransactionByTrackingInformation(string key, Transaction transaction)
		{
			transactionsByTrackingInformation.Add(key, transaction);
		}

		internal void AddTransactionDenifition(Transaction transaction)
		{
			if (batch.Status != BatchStatus.OPEN) throw new GameEngineException($"Transactions are not allowed when batch status is different to {BatchStatus.OPEN.ToString()}");

			Currency gross = null, comission = null, net = null, profit = null;
			Currency amountToCustomer = transaction.CalculateAmounts(out gross, out comission, out net, out profit);

			bool isSameCurrencyTransaction = transaction.isSameCurrencyTransaction();
			BatchMovements draftMovements = new BatchMovements();
			BatchMovements parentDraftMovements = new BatchMovements();

			if (!isSameCurrencyTransaction)
			{
				this.outAccounts.
					Lock(amountToCustomer, transaction, draftMovements);

				parentDraftMovements.AccumulateInLock(transaction.Id, amountToCustomer, transaction.TransactionDate, Balances.Types.OUT);

				if (! (transaction is WithdrawalTransaction))
				{
					this.inAccounts.
						Accredit(gross, transaction, draftMovements, parentDraftMovements).
						Lock(gross, transaction, draftMovements, parentDraftMovements);
				}
			}
			else
			{
				if (transaction is SaleTransaction)
				{
					this.outAccounts.
						Lock(gross, transaction, draftMovements);

					parentDraftMovements.AccumulateInLock(transaction.Id, amountToCustomer, transaction.TransactionDate, Balances.Types.OUT);
				}
				else if (transaction is DepositTransaction)
				{
					this.inAccounts.
							Accredit(gross, transaction, draftMovements, parentDraftMovements).
							Lock(gross, transaction, draftMovements, parentDraftMovements);
				}
				else if (transaction is WithdrawalTransaction)
				{
					this.outAccounts.
						Lock(gross, transaction, draftMovements);

					parentDraftMovements.AccumulateInLock(transaction.Id, amountToCustomer, transaction.TransactionDate, Balances.Types.OUT);
				}
				else if (transaction is TransferTransaction)
				{
					draftMovements.CreateNewUnProfitMovement(transaction.Id, amountToCustomer, transaction.TransactionDate, Balances.Types.NONE);
				}
				else if (transaction is CreditNoteTransaction)
				{
					this.outAccounts.
						Lock(amountToCustomer, transaction, draftMovements);

					parentDraftMovements.AccumulateInLock(transaction.Id, amountToCustomer, transaction.TransactionDate, Balances.Types.OUT);
				}
				else if (transaction is DebitNoteTransaction)
				{
					this.inAccounts.
						Accredit(amountToCustomer, transaction, draftMovements, parentDraftMovements).
						Lock(amountToCustomer, transaction, draftMovements, parentDraftMovements);
				}
				else
				{
					throw new GameEngineException($"There is no a {nameof(AddTransactionDenifition)}  implementation for transaction type {transaction.GetType()}.");
				}
			}

			batch.ApplyBalancesModifications(parentDraftMovements);
			batchMovements.Add(draftMovements);
		}

		internal void AddTransactionApproval(Transaction transaction, TransactionCompleted transactionCompleted)
		{
			if (batch.Status != BatchStatus.OPEN) throw new GameEngineException($"Transactions are not allowed when batch status is different to {BatchStatus.OPEN.ToString()}");

			Currency gross = transactionCompleted.Result.Gross,
				 net = transactionCompleted.Result.Net,
				 profit = transactionCompleted.Result.Profit,
				 amountToCustomer = transactionCompleted.Result.Amount;
			bool isSameCurrencyTransaction = transaction.isSameCurrencyTransaction();
			BatchMovements draftMovements = new BatchMovements();
			BatchMovements parentDraftMovements = new BatchMovements();

			if (!isSameCurrencyTransaction)
			{
				this.outAccounts.
					UnLock(amountToCustomer, transaction, draftMovements);

				parentDraftMovements.
					SubstractInLock(transaction.Id, amountToCustomer, transaction.TransactionDate, Balances.Types.OUT).
					AccumulateInSpend(transaction.Id, amountToCustomer, transaction.TransactionDate, Balances.Types.OUT);

				if (!(transaction is WithdrawalTransaction))
				{
					this.inAccounts.
						UnLock(gross, transaction, draftMovements, parentDraftMovements).
						Accredit(gross, transaction, draftMovements, parentDraftMovements);
				}

				this.inAccounts.InitialAccount.IncreaseInBalances(gross);
				this.outAccounts.InitialAccount.IncreaseOutBalances(amountToCustomer);
			}
			else
			{
				if (transaction is SaleTransaction)
				{
					this.outAccounts.
						UnLock(amountToCustomer, transaction, draftMovements);

					parentDraftMovements.
						SubstractInLock(transaction.Id, amountToCustomer, transaction.TransactionDate, Balances.Types.OUT).
						AccumulateInSpend(transaction.Id, amountToCustomer, transaction.TransactionDate, Balances.Types.OUT);

					this.inAccounts.InitialAccount.IncreaseInBalances(gross);
					this.outAccounts.InitialAccount.IncreaseOutBalances(amountToCustomer);
				}
				else if (transaction is DepositTransaction)
				{
					this.inAccounts.
						UnLock(gross, transaction, draftMovements, parentDraftMovements).
						Accredit(gross, transaction, draftMovements, parentDraftMovements);

					this.inAccounts.InitialAccount.IncreaseInBalances(gross);
				}
				else if (transaction is WithdrawalTransaction)
				{
					this.outAccounts.
						UnLock(amountToCustomer, transaction, draftMovements);

					parentDraftMovements.
						SubstractInLock(transaction.Id, amountToCustomer, transaction.TransactionDate, Balances.Types.OUT).
						AccumulateInSpend(transaction.Id, amountToCustomer, transaction.TransactionDate, Balances.Types.OUT);

					this.outAccounts.InitialAccount.IncreaseOutBalances(amountToCustomer);
				}
				else if (transaction is TransferTransaction)
				{
					draftMovements.CreateNewUnProfitApprovalMovement(transaction.Id, amountToCustomer, transaction.TransactionDate, Balances.Types.NONE);
				}
				else if (transaction is CreditNoteTransaction)
				{
					this.outAccounts.
						UnLock(amountToCustomer, transaction, draftMovements);

					parentDraftMovements.
						SubstractInLock(transaction.Id, amountToCustomer, transaction.TransactionDate, Balances.Types.OUT).
						AccumulateInSpend(transaction.Id, amountToCustomer, transaction.TransactionDate, Balances.Types.OUT);
					
					this.outAccounts.InitialAccount.IncreaseOutBalances(amountToCustomer);
				}
				else if (transaction is DebitNoteTransaction)
				{
					this.inAccounts.
						UnLock(amountToCustomer, transaction, draftMovements, parentDraftMovements).
						Accredit(amountToCustomer, transaction, draftMovements, parentDraftMovements);

					this.inAccounts.InitialAccount.IncreaseInBalances(gross);
				}
				else
				{
					throw new GameEngineException($"There is no a {nameof(AddTransactionDenifition)}  implementation for transaction type {transaction.GetType()}.");
				}
			}

			batch.ApplyBalancesModifications(parentDraftMovements);
			batchMovements.Add(draftMovements);
		}

		internal TransactionDefinition SearchTemporaryDefinition(int transactionNumber)
		{
			return temporaryDefinitions[transactionNumber];
		}

		internal void AddTemporaryDefinition(TransactionDefinition transaction)
		{
			if (batch.Status != BatchStatus.OPEN) throw new GameEngineException($"Transactions are not allowed when batch status is different to {BatchStatus.OPEN.ToString()}");

			temporaryDefinitions.Add(transaction.Id, transaction);
		}

		internal void RemoveTemporaryDefinition(TransactionDefinition transaction)
		{
			if (batch.Status != BatchStatus.OPEN) throw new GameEngineException($"Transactions are not allowed when batch status is different to {BatchStatus.OPEN.ToString()}");

			temporaryDefinitions.Remove(transaction.Id);
		}

		internal void AddTransactionRejection(Transaction transaction, TransactionCompleted transactionCompleted)
		{
			if (batch.Status != BatchStatus.OPEN) throw new GameEngineException($"Transactions are not allowed when batch status is different to {BatchStatus.OPEN.ToString()}");

			Currency gross = transactionCompleted.Result.Gross,
				 net = transactionCompleted.Result.Net,
				 profit = transactionCompleted.Result.Profit,
				 amountToCustomer = transactionCompleted.Result.Amount;
			bool isSameCurrencyTransaction = transaction.isSameCurrencyTransaction();
			BatchMovements draftMovements = new BatchMovements();
			BatchMovements parentDraftMovements = new BatchMovements();

			if (!isSameCurrencyTransaction)
			{
				this.outAccounts.
					UnLock(amountToCustomer, transaction, draftMovements).
					Accredit(amountToCustomer, transaction, draftMovements);

				parentDraftMovements.
					SubstractInLock(transaction.Id, amountToCustomer, transaction.TransactionDate, Balances.Types.OUT);

				if (!(transaction is WithdrawalTransaction))
				{
					this.inAccounts.
						UnLock(gross, transaction, draftMovements, parentDraftMovements);
				}

			}
			else
			{
				if (transaction is SaleTransaction)
				{
					this.outAccounts.
						UnLock(amountToCustomer, transaction, draftMovements).
						Accredit(amountToCustomer, transaction, draftMovements);

					parentDraftMovements.
						SubstractInLock(transaction.Id, amountToCustomer, transaction.TransactionDate, Balances.Types.OUT);
				}
				else if (transaction is DepositTransaction)
				{
					this.inAccounts.
						UnLock(gross, transaction, draftMovements, parentDraftMovements).
						Debit(gross, transaction, draftMovements, parentDraftMovements);
				}
				else if (transaction is WithdrawalTransaction)
				{
					this.outAccounts.
						UnLock(amountToCustomer, transaction, draftMovements).
						Accredit(amountToCustomer, transaction, draftMovements);

					parentDraftMovements.
						SubstractInLock(transaction.Id, amountToCustomer, transaction.TransactionDate, Balances.Types.OUT);
				}
				else if (transaction is TransferTransaction)
				{
					draftMovements.CreateNewUnProfitRejectionMovement(transaction.Id, amountToCustomer, transaction.TransactionDate, Balances.Types.NONE);
				}
				else if (transaction is CreditNoteTransaction)
				{
					this.outAccounts.
						UnLock(amountToCustomer, transaction, draftMovements).
						Accredit(amountToCustomer, transaction, draftMovements);

					parentDraftMovements.
						SubstractInLock(transaction.Id, amountToCustomer, transaction.TransactionDate, Balances.Types.OUT);

				}
				else if (transaction is DebitNoteTransaction)
				{
					this.inAccounts.
						UnLock(amountToCustomer, transaction, draftMovements, parentDraftMovements);
				}
				else
				{
					throw new GameEngineException($"There is no a {nameof(AddTransactionDenifition)}  implementation for transaction type {transaction.GetType()}.");
				}
			}

			batch.ApplyBalancesModifications(parentDraftMovements);
			batchMovements.Add(draftMovements); 
		}

		internal void RemoveDefinition(TransactionDefinition transactionDefinition)
		{
			temporaryDefinitions.Remove(transactionDefinition.Id);
		}

		internal Currency CalculateProfit(FloatingExchangeRate saleRate, Currency amountToCustomer)
		{
			return outAccounts.CalculateProfit(saleRate, amountToCustomer);
		}
		internal Currency CalculateProfit(FixedExchangeRate purchaseRate, Currency amountToCustomer)
		{
			return outAccounts.CalculateProfit(purchaseRate, amountToCustomer);
		}
		internal Currency CalculateCost(Currency amountToCustomer)
		{
			return outAccounts.CalculateCost(amountToCustomer);
		}

		//El Batch tiene un Available, solo se puede pedir si esta OPENED
		internal Currency Available(Coin currencyCode)
		{
			BatchAccount result = this.FindSaleAccountByCurrency(currencyCode);
			return result.Available;
		}

		internal Currency AvailableReception(Coin currencyCode)
		{
			BatchAccount result = this.FindReceptionAccountByCurrency(currencyCode);
			return result.Available;
		}

		internal Currency Locked(Coin currencyCode)
		{
			BatchAccount result = this.FindSaleAccountByCurrency(currencyCode);
			return result.Locked;
		}

		internal Currency LockedReception(Coin currencyCode)
		{
			BatchAccount result = this.FindReceptionAccountByCurrency(currencyCode);
			return result.Locked;
		}

		internal void Withdraw(Currency amount, string employeeName, DateTime now)
		{
			if (now == default(DateTime)) throw new ArgumentNullException(nameof(now));
			if (amount == null) throw new ArgumentNullException(nameof(amount));
			//Solo se pueden hacer cuando esta OPENED
			if (batch.Status != BatchStatus.OPEN) throw new GameEngineException($"Withdraws are not allowed when batch status is different to {BatchStatus.OPEN.ToString()}");

			BatchAccount sourceAccount = outAccounts.FindAccountByCurrency(amount.Coin);
			sourceAccount.Lock(amount);
		}

		internal bool HasPendingTransactions()
		{
			return (temporaryDefinitions.Count > 0 || draftTransactions.Count > 0);
		}

		internal bool HasPendingDraftTransactions()
		{
			return (draftTransactions.Count > 0);
		}

		internal void DropTemporaryTransactionsIfExists()
		{
			if (temporaryDefinitions.Count > 0)
			{
				temporaryDefinitions.Clear();
			}
		}

		internal bool HasAvailable(Currency amount)
		{
			bool result = this.batch.Available(amount.Coin).Value >= amount.Value;
			return result;
		}

		internal void UnLock(Currency amount, string employeeName, DateTime now)
		{
			if (now == default(DateTime)) throw new ArgumentNullException(nameof(now));
			if (amount == null) throw new ArgumentNullException(nameof(amount));
			//Solo se pueden hacer cuando esta OPENED
			if (batch.Status != BatchStatus.OPEN) throw new GameEngineException($"Unlock are not allowed when batch status is different to {BatchStatus.OPEN.ToString()}");

			BatchAccount sourceAccount = outAccounts.FindAccountByCurrency(amount.Coin);
			sourceAccount.Unlock(amount);
		}

		internal BatchAccount FindSaleAccountByCurrency(Coin currencyCode)
		{
			return outAccounts.FindAccountByCurrency(currencyCode);
		}

		internal BatchAccount FindReceptionAccountByCurrency(Coin currencyCode)
		{
			return inAccounts.FindAccountByCurrency(currencyCode);
		}

		internal IEnumerable<BatchAccount> SaleAccounts()
		{
			return this.outAccounts.Accounts();
		}

		internal IEnumerable<BatchAccount> ReceptionAccounts()
		{
			return this.inAccounts.Accounts();
		}

		internal int TransactionsNumber
		{
			get
			{
				return this.batchNumber;
			}
		}

		internal BatchStatus CurrentStatus
		{
			get
			{
				return batch.Status;
			}
		}

		internal bool IsReady
		{
			get
			{
				return batch.Status == BatchStatus.READY;
			}
		}

		internal bool IsOpen
		{
			get
			{
				return batch.Status == BatchStatus.OPEN;
			}
		}

		internal string CurrentStatusAsStr
		{
			get
			{
				return batch.Status.ToString();
			}
		}

		internal DateTime CreationDate
		{
			get
			{
				return this.creationDate;
			}
		}

		internal bool ItsClose { get { return batch.Status == BatchStatus.CLOSE; } }
		internal bool ItsDone { get { return batch.Status == BatchStatus.DONE; } }
		
		internal IEnumerable<BatchMovement> Movements()
		{
			return this.batchMovements.List();
		}

		internal void AddToDraftTransactions(Transaction transaction)
		{
			draftTransactions.Add(transaction.Id, transaction);
		}

		internal void Remove(Transaction transaction)
		{
			draftTransactions.Remove(transaction.Id);
			if ( transaction.HasTrackingInformation )
			{
				transactionsByTrackingInformation.Remove(transaction.TrackingKey);
			}
		}

		internal Transaction FindDraftTransaction(int id)
		{
			return draftTransactions[id];
		}

		internal bool ExistsDraftTransaction(int id)
		{
			return draftTransactions.ContainsKey(id);
		}

		//internal void Remove(Transaction transaction)
		//{
		//	draftTransactions.Remove(transaction.Id);
		//	transactionsByTrackingInformation.Remove(transaction.TrackingKey);
		//}

		internal Transaction SearchTransactionByTrackinginformation(string trackingCode, string reference)
		{
			Transaction result;

			if (transactionsByTrackingInformation.TryGetValue(trackingCode+ reference, out result))
			{
				return result;
			}
			throw new GameEngineException($"{nameof(Transaction)} doesn't exists.");
		}
	}
}
