﻿using GamesEngine.Gameboards;
using GamesEngine.Gameboards.Lines;
using GamesEngine.Games.Tournaments;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Games.Lines
{
    internal class Reports : Objeto
    {
        private QueryMakerOfHistorical queryMaker;
        internal QueryMakerOfHistorical QueryMaker
        {
            get
            {
                if (queryMaker == null) queryMaker = new QueryMakerOfHistorical();
                return queryMaker;
            }
        }

        readonly Tournaments.Tournaments tournaments;
        internal Reports(Tournaments.Tournaments tournaments)
        {
            if (tournaments == null) throw new ArgumentNullException(nameof(tournaments));

            this.tournaments = tournaments;
        }

        public const string SELECTION_ALL = "all";
        public const int SELECTION_ALL_INT = 0;

        internal PendingMatches GeneratePendingMatchesReport(DateTime startDate, DateTime endDate, string sportNameOrAll, string leagueNameOrAll, string tournamentIdOrAll, string gameIdOrAll, string lineTypeOrAll, 
            string gradingStatusOrAll, string accountNumberOrAll, string authorizationIdOrAll, string domainIdsOrAll)
        {
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate > endDate) throw new GameEngineException($"{nameof(startDate)} cannot be greater than {nameof(endDate)}");
            if (startDate.Hour != 0 || startDate.Minute != 0 || startDate.Second != 0 || startDate.Millisecond != 0) throw new GameEngineException($"{nameof(startDate)} '{startDate}' must be an exact day");
            if (endDate.Hour != 0 || endDate.Minute != 0 || endDate.Second != 0 || endDate.Millisecond != 0) throw new GameEngineException($"{nameof(endDate)} '{endDate}' must be an exact day");
            if (string.IsNullOrWhiteSpace(sportNameOrAll)) throw new ArgumentNullException(nameof(sportNameOrAll));
            if (string.IsNullOrWhiteSpace(leagueNameOrAll)) throw new ArgumentNullException(nameof(leagueNameOrAll));
            if (string.IsNullOrWhiteSpace(tournamentIdOrAll)) throw new ArgumentNullException(nameof(tournamentIdOrAll));
            if (string.IsNullOrWhiteSpace(gameIdOrAll)) throw new ArgumentNullException(nameof(gameIdOrAll));
            if (string.IsNullOrWhiteSpace(lineTypeOrAll)) throw new ArgumentNullException(nameof(lineTypeOrAll));
            if (string.IsNullOrWhiteSpace(gradingStatusOrAll)) throw new ArgumentNullException(nameof(gradingStatusOrAll));
            if (string.IsNullOrWhiteSpace(accountNumberOrAll)) throw new ArgumentNullException(nameof(accountNumberOrAll));
            if (string.IsNullOrWhiteSpace(authorizationIdOrAll)) throw new ArgumentNullException(nameof(authorizationIdOrAll));
            if (string.IsNullOrWhiteSpace(domainIdsOrAll)) throw new ArgumentNullException(nameof(domainIdsOrAll));

            var allSports = sportNameOrAll == SELECTION_ALL;
            var allLeagues = leagueNameOrAll == SELECTION_ALL;
            var allTournaments = tournamentIdOrAll == SELECTION_ALL;

            PendingMatchesBuilder reportBuilder;
            if (!allSports && allLeagues && allTournaments)
            {
                var sport = tournaments.Sports.FindBy(sportNameOrAll);
                var tournamentsToSearch = tournaments.FindTournamentsBy(sport);
                reportBuilder = new PendingMatchesBuilder(tournamentsToSearch, startDate, endDate);
            }
            else if (!allSports && !allLeagues && allTournaments)
            {
                var league = tournaments.Leagues.FindBy(leagueNameOrAll);
                var tournamentsToSearch = tournaments.FindTournamentsBy(league);
                reportBuilder = new PendingMatchesBuilder(tournamentsToSearch, startDate, endDate);
            }
            else if (!allSports && !allLeagues && !allTournaments)
            {
                var tournamentId = Convert.ToInt32(tournamentIdOrAll);
                var tournament = tournaments.FindById(tournamentId);
                reportBuilder = new PendingMatchesBuilder(tournament, startDate, endDate);
            }
            else
            {
                throw new GameEngineException($"Search by {nameof(Sport)} '{sportNameOrAll}', {nameof(League)} '{leagueNameOrAll}', {nameof(Tournament)} '{tournamentIdOrAll}' is not valid");
            }

            var allGameId = gameIdOrAll == SELECTION_ALL;
            if (!allGameId)
            {
                var gameId = Convert.ToInt32(gameIdOrAll);
                reportBuilder.FilterByGame(gameId);
            }

            var allLineType = lineTypeOrAll == SELECTION_ALL;
            if (!allLineType)
            {
                LineType lineType;
                if (!Enum.TryParse(lineTypeOrAll, out lineType)) throw new GameEngineException($"{nameof(lineTypeOrAll)} '{lineTypeOrAll}' is not valid");
                reportBuilder.FilterBy(lineType);
            }

            var allGradingStatus = gradingStatusOrAll == SELECTION_ALL;
            if (!allGradingStatus)
            {
                GameboardStatus gameboardStatus;
                if (!Enum.TryParse(gradingStatusOrAll, out gameboardStatus)) throw new GameEngineException($"{nameof(gradingStatusOrAll)} '{gradingStatusOrAll}' is not valid");
                reportBuilder.FilterBy(gameboardStatus);
            }

            var allDomains = domainIdsOrAll == SELECTION_ALL;
            if (!allDomains)
            {
                var domainIds = domainIdsOrAll.Split(',');
                reportBuilder.FilterBy(domainIds);
            }

            var allAccountNumber = accountNumberOrAll == SELECTION_ALL;
            if (!allAccountNumber)
            {
                reportBuilder.FilterBy(accountNumberOrAll);
            }

            var allAuthorizationId = authorizationIdOrAll == SELECTION_ALL;
            if (!allAuthorizationId)
            {
                var authorizationId = Convert.ToInt32(authorizationIdOrAll);
                reportBuilder.FilterBy(authorizationId);
            }

            return reportBuilder.Build();
        }

        internal PendingMatchesPerPlayers GeneratePendingMatchesPerPlayersReport(DateTime startDate, DateTime endDate, string sportNameOrAll, string leagueNameOrAll, string tournamentIdOrAll, string gameIdOrAll, 
            string lineTypeOrAll, string gradingStatusOrAll, string accountNumberOrAll, string authorizationIdOrAll, string domainIdsOrAll)
        {
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate > endDate) throw new GameEngineException($"{nameof(startDate)} cannot be greater than {nameof(endDate)}");
            if (startDate.Hour != 0 || startDate.Minute != 0 || startDate.Second != 0 || startDate.Millisecond != 0) throw new GameEngineException($"{nameof(startDate)} '{startDate}' must be an exact day");
            if (endDate.Hour != 0 || endDate.Minute != 0 || endDate.Second != 0 || endDate.Millisecond != 0) throw new GameEngineException($"{nameof(endDate)} '{endDate}' must be an exact day");
            if (string.IsNullOrWhiteSpace(sportNameOrAll)) throw new ArgumentNullException(nameof(sportNameOrAll));
            if (string.IsNullOrWhiteSpace(leagueNameOrAll)) throw new ArgumentNullException(nameof(leagueNameOrAll));
            if (string.IsNullOrWhiteSpace(tournamentIdOrAll)) throw new ArgumentNullException(nameof(tournamentIdOrAll));
            if (string.IsNullOrWhiteSpace(gameIdOrAll)) throw new ArgumentNullException(nameof(gameIdOrAll));
            if (string.IsNullOrWhiteSpace(lineTypeOrAll)) throw new ArgumentNullException(nameof(lineTypeOrAll));
            if (string.IsNullOrWhiteSpace(gradingStatusOrAll)) throw new ArgumentNullException(nameof(gradingStatusOrAll));
            if (string.IsNullOrWhiteSpace(accountNumberOrAll)) throw new ArgumentNullException(nameof(accountNumberOrAll));
            if (string.IsNullOrWhiteSpace(authorizationIdOrAll)) throw new ArgumentNullException(nameof(authorizationIdOrAll));
            if (string.IsNullOrWhiteSpace(domainIdsOrAll)) throw new ArgumentNullException(nameof(domainIdsOrAll));

            var allSports = sportNameOrAll == SELECTION_ALL;
            var allLeagues = leagueNameOrAll == SELECTION_ALL;
            var allTournaments = tournamentIdOrAll == SELECTION_ALL;

            PendingMatchesPerPlayersBuilder reportBuilder;
            if (!allSports && allLeagues && allTournaments)
            {
                var sport = tournaments.Sports.FindBy(sportNameOrAll);
                var tournamentsToSearch = tournaments.FindTournamentsBy(sport);
                reportBuilder = new PendingMatchesPerPlayersBuilder(tournamentsToSearch, startDate, endDate);
            }
            else if (!allSports && !allLeagues && allTournaments)
            {
                var league = tournaments.Leagues.FindBy(leagueNameOrAll);
                var tournamentsToSearch = tournaments.FindTournamentsBy(league);
                reportBuilder = new PendingMatchesPerPlayersBuilder(tournamentsToSearch, startDate, endDate);
            }
            else if (!allSports && !allLeagues && !allTournaments)
            {
                var tournamentId = Convert.ToInt32(tournamentIdOrAll);
                var tournament = tournaments.FindById(tournamentId);
                reportBuilder = new PendingMatchesPerPlayersBuilder(tournament, startDate, endDate);
            }
            else
            {
                throw new GameEngineException($"Search by {nameof(Sport)} '{sportNameOrAll}', {nameof(League)} '{leagueNameOrAll}', {nameof(Tournament)} '{tournamentIdOrAll}' is not valid");
            }

            var allGameId = gameIdOrAll == SELECTION_ALL;
            if (!allGameId)
            {
                var gameId = Convert.ToInt32(gameIdOrAll);
                reportBuilder.FilterByGame(gameId);
            }

            var allLineType = lineTypeOrAll == SELECTION_ALL;
            if (!allLineType)
            {
                LineType lineType;
                if (!Enum.TryParse(lineTypeOrAll, out lineType)) throw new GameEngineException($"{nameof(lineTypeOrAll)} '{lineTypeOrAll}' is not valid");
                reportBuilder.FilterBy(lineType);
            }

            var allGradingStatus = gradingStatusOrAll == SELECTION_ALL;
            if (!allGradingStatus)
            {
                GameboardStatus gameboardStatus;
                if (!Enum.TryParse(gradingStatusOrAll, out gameboardStatus)) throw new GameEngineException($"{nameof(gradingStatusOrAll)} '{gradingStatusOrAll}' is not valid");
                reportBuilder.FilterBy(gameboardStatus);
            }

            var allDomains = domainIdsOrAll == SELECTION_ALL;
            if (! allDomains)
            {
                var domainIds = domainIdsOrAll.Split(',');
                reportBuilder.FilterBy(domainIds);
            }

            var allAccountNumber = accountNumberOrAll == SELECTION_ALL;
            if (!allAccountNumber)
            {
                reportBuilder.FilterBy(accountNumberOrAll);
            }

            var allAuthorizationId = authorizationIdOrAll == SELECTION_ALL;
            if (!allAuthorizationId)
            {
                var authorizationId = Convert.ToInt32(authorizationIdOrAll);
                reportBuilder.FilterBy(authorizationId);
            }

            return reportBuilder.Build();
        }

        internal PendingWagers GeneratePendingWagersPerMatchesReport(DateTime startDate, DateTime endDate, string sportNameOrAll, string leagueNameOrAll, string tournamentIdOrAll, string gameIdOrAll, 
            string lineTypeOrAll, string gradingStatusOrAll, string accountNumberOrAll, string authorizationIdOrAll, string domainIdsOrAll)
        {
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate > endDate) throw new GameEngineException($"{nameof(startDate)} cannot be greater than {nameof(endDate)}");
            if (startDate.Hour != 0 || startDate.Minute != 0 || startDate.Second != 0 || startDate.Millisecond != 0) throw new GameEngineException($"{nameof(startDate)} '{startDate}' must be an exact day");
            if (endDate.Hour != 0 || endDate.Minute != 0 || endDate.Second != 0 || endDate.Millisecond != 0) throw new GameEngineException($"{nameof(endDate)} '{endDate}' must be an exact day");
            if (string.IsNullOrWhiteSpace(sportNameOrAll)) throw new ArgumentNullException(nameof(sportNameOrAll));
            if (string.IsNullOrWhiteSpace(leagueNameOrAll)) throw new ArgumentNullException(nameof(leagueNameOrAll));
            if (string.IsNullOrWhiteSpace(tournamentIdOrAll)) throw new ArgumentNullException(nameof(tournamentIdOrAll));
            if (string.IsNullOrWhiteSpace(gameIdOrAll)) throw new ArgumentNullException(nameof(gameIdOrAll));
            if (string.IsNullOrWhiteSpace(lineTypeOrAll)) throw new ArgumentNullException(nameof(lineTypeOrAll));
            if (string.IsNullOrWhiteSpace(gradingStatusOrAll)) throw new ArgumentNullException(nameof(gradingStatusOrAll));
            if (string.IsNullOrWhiteSpace(accountNumberOrAll)) throw new ArgumentNullException(nameof(accountNumberOrAll));
            if (string.IsNullOrWhiteSpace(authorizationIdOrAll)) throw new ArgumentNullException(nameof(authorizationIdOrAll));
            if (string.IsNullOrWhiteSpace(domainIdsOrAll)) throw new ArgumentNullException(nameof(domainIdsOrAll));

            var allSports = sportNameOrAll == SELECTION_ALL;
            var allLeagues = leagueNameOrAll == SELECTION_ALL;
            var allTournaments = tournamentIdOrAll == SELECTION_ALL;

            PendingWagersBuilder reportBuilder;
            if (!allSports && allLeagues && allTournaments)
            {
                var sport = tournaments.Sports.FindBy(sportNameOrAll);
                var tournamentsToSearch = tournaments.FindTournamentsBy(sport);
                reportBuilder = new PendingWagersBuilder(tournamentsToSearch, startDate, endDate);
            }
            else if (!allSports && !allLeagues && allTournaments)
            {
                var league = tournaments.Leagues.FindBy(leagueNameOrAll);
                var tournamentsToSearch = tournaments.FindTournamentsBy(league);
                reportBuilder = new PendingWagersBuilder(tournamentsToSearch, startDate, endDate);
            }
            else if (!allSports && !allLeagues && !allTournaments)
            {
                var tournamentId = Convert.ToInt32(tournamentIdOrAll);
                var tournament = tournaments.FindById(tournamentId);
                reportBuilder = new PendingWagersBuilder(tournament, startDate, endDate);
            }
            else
            {
                throw new GameEngineException($"Search by {nameof(Sport)} '{sportNameOrAll}', {nameof(League)} '{leagueNameOrAll}', {nameof(Tournament)} '{tournamentIdOrAll}' is not valid");
            }

            var allGameId = gameIdOrAll == SELECTION_ALL;
            if (!allGameId)
            {
                var gameId = Convert.ToInt32(gameIdOrAll);
                reportBuilder.FilterByGame(gameId);
            }

            var allLineType = lineTypeOrAll == SELECTION_ALL;
            if (!allLineType)
            {
                LineType lineType;
                if (!Enum.TryParse(lineTypeOrAll, out lineType)) throw new GameEngineException($"{nameof(lineTypeOrAll)} '{lineTypeOrAll}' is not valid");
                reportBuilder.FilterBy(lineType);
            }

            var allGradingStatus = gradingStatusOrAll == SELECTION_ALL;
            if (!allGradingStatus)
            {
                GameboardStatus gameboardStatus;
                if (!Enum.TryParse(gradingStatusOrAll, out gameboardStatus)) throw new GameEngineException($"{nameof(gradingStatusOrAll)} '{gradingStatusOrAll}' is not valid");
                reportBuilder.FilterBy(gameboardStatus);
            }

            var allDomains = domainIdsOrAll == SELECTION_ALL;
            if (!allDomains)
            {
                var domainIds = domainIdsOrAll.Split(',');
                reportBuilder.FilterBy(domainIds);
            }

            var allAccountNumber = accountNumberOrAll == SELECTION_ALL;
            if (!allAccountNumber)
            {
                reportBuilder.FilterBy(accountNumberOrAll);
            }

            var allAuthorizationId = authorizationIdOrAll == SELECTION_ALL;
            if (!allAuthorizationId)
            {
                var authorizationId = Convert.ToInt32(authorizationIdOrAll);
                reportBuilder.FilterBy(authorizationId);
            }

            return reportBuilder.Build();
        }

        internal CompletedMatches GenerateCompletedMatchesReport(DateTime startDate, DateTime endDate, string sportOrAll, string leagueOrAll, string tournamentIdOrAll, string gameIdOrAll, string lineTypeOrAll, 
            string gradingStatusOrAll, string accountNumberOrAll, string authorizationIdOrAll, string domainIdsOrAll)
        {
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate > endDate) throw new GameEngineException($"{nameof(startDate)} cannot be greater than {nameof(endDate)}");
            if (startDate.Hour != 0 || startDate.Minute != 0 || startDate.Second != 0 || startDate.Millisecond != 0) throw new GameEngineException($"{nameof(startDate)} '{startDate}' must be an exact day");
            if (endDate.Hour != 0 || endDate.Minute != 0 || endDate.Second != 0 || endDate.Millisecond != 0) throw new GameEngineException($"{nameof(endDate)} '{endDate}' must be an exact day");
            if (string.IsNullOrWhiteSpace(sportOrAll)) throw new ArgumentNullException(nameof(sportOrAll));
            if (string.IsNullOrWhiteSpace(leagueOrAll)) throw new ArgumentNullException(nameof(leagueOrAll));
            if (string.IsNullOrWhiteSpace(tournamentIdOrAll)) throw new ArgumentNullException(nameof(tournamentIdOrAll));
            if (string.IsNullOrWhiteSpace(gameIdOrAll)) throw new ArgumentNullException(nameof(gameIdOrAll));
            if (string.IsNullOrWhiteSpace(lineTypeOrAll)) throw new ArgumentNullException(nameof(lineTypeOrAll));
            if (string.IsNullOrWhiteSpace(gradingStatusOrAll)) throw new ArgumentNullException(nameof(gradingStatusOrAll));
            if (string.IsNullOrWhiteSpace(accountNumberOrAll)) throw new ArgumentNullException(nameof(accountNumberOrAll));
            if (string.IsNullOrWhiteSpace(authorizationIdOrAll)) throw new ArgumentNullException(nameof(authorizationIdOrAll));
            if (string.IsNullOrWhiteSpace(domainIdsOrAll)) throw new ArgumentNullException(nameof(domainIdsOrAll));

            var lineTypeIdToSearch = 0;
            if (lineTypeOrAll != SELECTION_ALL)
            {
                LineType lineTypeToSearch;
                if (! Enum.TryParse(lineTypeOrAll, out lineTypeToSearch)) throw new GameEngineException($"{nameof(lineTypeOrAll)} '{lineTypeOrAll}' is not valid");
                lineTypeIdToSearch = (int)lineTypeToSearch;
            }
            char gradingStatusAsChar = char.MinValue;
            if (gradingStatusOrAll != SELECTION_ALL)
            {
                GradingStatus gradingStatusToSearch;
                if (!Enum.TryParse(gradingStatusOrAll, out gradingStatusToSearch)) throw new GameEngineException($"{nameof(gradingStatusOrAll)} '{gradingStatusOrAll}' is not valid");
                gradingStatusAsChar = gradingStatusToSearch.GradingAsChar();
            }

            var sportToSearch = sportOrAll == SELECTION_ALL ? string.Empty : sportOrAll;
            var leagueToSearch = leagueOrAll == SELECTION_ALL ? string.Empty : leagueOrAll;
            var tournamentIdToSearch = tournamentIdOrAll == SELECTION_ALL ? 0 : Convert.ToInt32(tournamentIdOrAll);
            var gameIdToSearch = gameIdOrAll == SELECTION_ALL ? 0 : Convert.ToInt32(gameIdOrAll);
            var accountNumberToSearch = accountNumberOrAll == SELECTION_ALL ? string.Empty : accountNumberOrAll;
            var authorizationIdToSearch = authorizationIdOrAll == SELECTION_ALL ? string.Empty : authorizationIdOrAll;
            var domainIdsToSearch = domainIdsOrAll == SELECTION_ALL ? string.Empty : domainIdsOrAll;
            
            var result = QueryMaker.GenerateCompletedMatchesReport(startDate, endDate, sportToSearch, leagueToSearch, tournamentIdToSearch, gameIdToSearch, lineTypeIdToSearch, gradingStatusAsChar, accountNumberToSearch, authorizationIdToSearch, domainIdsToSearch);
            return result;
        }

        internal CompletedMatchesPerPlayers GenerateCompletedMatchesPerPlayersReport(DateTime startDate, DateTime endDate, string sportOrAll, string leagueOrAll, string tournamentIdOrAll, string gameIdOrAll, 
            string lineTypeOrAll, string gradingStatusOrAll, string accountNumberOrAll, string authorizationIdOrAll, string domainIdsOrAll)
        {
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate > endDate) throw new GameEngineException($"{nameof(startDate)} cannot be greater than {nameof(endDate)}");
            if (startDate.Hour != 0 || startDate.Minute != 0 || startDate.Second != 0 || startDate.Millisecond != 0) throw new GameEngineException($"{nameof(startDate)} '{startDate}' must be an exact day");
            if (endDate.Hour != 0 || endDate.Minute != 0 || endDate.Second != 0 || endDate.Millisecond != 0) throw new GameEngineException($"{nameof(endDate)} '{endDate}' must be an exact day");
            if (string.IsNullOrWhiteSpace(sportOrAll)) throw new ArgumentNullException(nameof(sportOrAll));
            if (string.IsNullOrWhiteSpace(leagueOrAll)) throw new ArgumentNullException(nameof(leagueOrAll));
            if (string.IsNullOrWhiteSpace(tournamentIdOrAll)) throw new ArgumentNullException(nameof(tournamentIdOrAll));
            if (string.IsNullOrWhiteSpace(gameIdOrAll)) throw new ArgumentNullException(nameof(gameIdOrAll));
            if (string.IsNullOrWhiteSpace(lineTypeOrAll)) throw new ArgumentNullException(nameof(lineTypeOrAll));
            if (string.IsNullOrWhiteSpace(gradingStatusOrAll)) throw new ArgumentNullException(nameof(gradingStatusOrAll));
            if (string.IsNullOrWhiteSpace(accountNumberOrAll)) throw new ArgumentNullException(nameof(accountNumberOrAll));
            if (string.IsNullOrWhiteSpace(authorizationIdOrAll)) throw new ArgumentNullException(nameof(authorizationIdOrAll));
            if (string.IsNullOrWhiteSpace(domainIdsOrAll)) throw new ArgumentNullException(nameof(domainIdsOrAll));

            var lineTypeIdToSearch = 0;
            if (lineTypeOrAll != SELECTION_ALL)
            {
                LineType lineTypeToSearch;
                if (!Enum.TryParse(lineTypeOrAll, out lineTypeToSearch)) throw new GameEngineException($"{nameof(lineTypeOrAll)} '{lineTypeOrAll}' is not valid");
                lineTypeIdToSearch = (int)lineTypeToSearch;
            }
            char gradingStatusAsChar = char.MinValue;
            if (gradingStatusOrAll != SELECTION_ALL)
            {
                GradingStatus gradingStatusToSearch;
                if (!Enum.TryParse(gradingStatusOrAll, out gradingStatusToSearch)) throw new GameEngineException($"{nameof(gradingStatusOrAll)} '{gradingStatusOrAll}' is not valid");
                gradingStatusAsChar = gradingStatusToSearch.GradingAsChar();
            }

            var sportToSearch = sportOrAll == SELECTION_ALL ? string.Empty : sportOrAll;
            var leagueToSearch = leagueOrAll == SELECTION_ALL ? string.Empty : leagueOrAll;
            var tournamentIdToSearch = tournamentIdOrAll == SELECTION_ALL ? 0 : Convert.ToInt32(tournamentIdOrAll);
            var gameIdToSearch = gameIdOrAll == SELECTION_ALL ? 0 : Convert.ToInt32(gameIdOrAll);
            var accountNumberToSearch = accountNumberOrAll == SELECTION_ALL ? string.Empty : accountNumberOrAll;
            var authorizationIdToSearch = authorizationIdOrAll == SELECTION_ALL ? string.Empty : authorizationIdOrAll;
            var domainIdsToSearch = domainIdsOrAll == SELECTION_ALL ? string.Empty : domainIdsOrAll;

            var result = QueryMaker.GenerateCompletedMatchesPerPlayersReport(startDate, endDate, sportToSearch, leagueToSearch, tournamentIdToSearch, gameIdToSearch, lineTypeIdToSearch, gradingStatusAsChar, accountNumberToSearch, authorizationIdToSearch, domainIdsToSearch);
            return result;
        }

        internal CompletedWagers GenerateCompletedWagersPerMatchesReport(DateTime startDate, DateTime endDate, string sportOrAll, string leagueOrAll, string tournamentIdOrAll, string gameIdOrAll, 
            string lineTypeOrAll, string gradingStatusOrAll, string accountNumberOrAll, string authorizationIdOrAll, string domainIdsOrAll)
        {
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate > endDate) throw new GameEngineException($"{nameof(startDate)} cannot be greater than {nameof(endDate)}");
            if (startDate.Hour != 0 || startDate.Minute != 0 || startDate.Second != 0 || startDate.Millisecond != 0) throw new GameEngineException($"{nameof(startDate)} '{startDate}' must be an exact day");
            if (endDate.Hour != 0 || endDate.Minute != 0 || endDate.Second != 0 || endDate.Millisecond != 0) throw new GameEngineException($"{nameof(endDate)} '{endDate}' must be an exact day");
            if (string.IsNullOrWhiteSpace(sportOrAll)) throw new ArgumentNullException(nameof(sportOrAll));
            if (string.IsNullOrWhiteSpace(leagueOrAll)) throw new ArgumentNullException(nameof(leagueOrAll));
            if (string.IsNullOrWhiteSpace(tournamentIdOrAll)) throw new ArgumentNullException(nameof(tournamentIdOrAll));
            if (string.IsNullOrWhiteSpace(gameIdOrAll)) throw new ArgumentNullException(nameof(gameIdOrAll));
            if (string.IsNullOrWhiteSpace(lineTypeOrAll)) throw new ArgumentNullException(nameof(lineTypeOrAll));
            if (string.IsNullOrWhiteSpace(gradingStatusOrAll)) throw new ArgumentNullException(nameof(gradingStatusOrAll));
            if (string.IsNullOrWhiteSpace(accountNumberOrAll)) throw new ArgumentNullException(nameof(accountNumberOrAll));
            if (string.IsNullOrWhiteSpace(authorizationIdOrAll)) throw new ArgumentNullException(nameof(authorizationIdOrAll));
            if (string.IsNullOrWhiteSpace(domainIdsOrAll)) throw new ArgumentNullException(nameof(domainIdsOrAll));

            var lineTypeIdToSearch = 0;
            if (lineTypeOrAll != SELECTION_ALL)
            {
                LineType lineTypeToSearch;
                if (!Enum.TryParse(lineTypeOrAll, out lineTypeToSearch)) throw new GameEngineException($"{nameof(lineTypeOrAll)} '{lineTypeOrAll}' is not valid");
                lineTypeIdToSearch = (int)lineTypeToSearch;
            }
            char gradingStatusAsChar = char.MinValue;
            if (gradingStatusOrAll != SELECTION_ALL)
            {
                GradingStatus gradingStatusToSearch;
                if (!Enum.TryParse(gradingStatusOrAll, out gradingStatusToSearch)) throw new GameEngineException($"{nameof(gradingStatusOrAll)} '{gradingStatusOrAll}' is not valid");
                gradingStatusAsChar = gradingStatusToSearch.GradingAsChar();
            }

            var sportToSearch = sportOrAll == SELECTION_ALL ? string.Empty : sportOrAll;
            var leagueToSearch = leagueOrAll == SELECTION_ALL ? string.Empty : leagueOrAll;
            var tournamentIdToSearch = tournamentIdOrAll == SELECTION_ALL ? 0 : Convert.ToInt32(tournamentIdOrAll);
            var gameIdToSearch = gameIdOrAll == SELECTION_ALL ? 0 : Convert.ToInt32(gameIdOrAll);
            var accountNumberToSearch = accountNumberOrAll == SELECTION_ALL ? string.Empty : accountNumberOrAll;
            var authorizationIdToSearch = authorizationIdOrAll == SELECTION_ALL ? string.Empty : authorizationIdOrAll;
            var domainIdsToSearch = domainIdsOrAll == SELECTION_ALL ? string.Empty : domainIdsOrAll;

            var result = QueryMaker.GenerateCompletedWagersPerMatchesReport(startDate, endDate, sportToSearch, leagueToSearch, tournamentIdToSearch, gameIdToSearch, lineTypeIdToSearch, gradingStatusAsChar, accountNumberToSearch, authorizationIdToSearch, domainIdsToSearch);
            return result;
        }

        internal CompletedWagers GenerateCompletedWinnerWagersReport(DateTime startDate, DateTime endDate, string sportOrAll, string leagueOrAll, string tournamentIdOrAll, string gameIdOrAll, string accountNumberOrAll, string domainIdsOrAll)
        {
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate > endDate) throw new GameEngineException($"{nameof(startDate)} cannot be greater than {nameof(endDate)}");
            if (startDate.Hour != 0 || startDate.Minute != 0 || startDate.Second != 0 || startDate.Millisecond != 0) throw new GameEngineException($"{nameof(startDate)} '{startDate}' must be an exact day");
            if (endDate.Hour != 0 || endDate.Minute != 0 || endDate.Second != 0 || endDate.Millisecond != 0) throw new GameEngineException($"{nameof(endDate)} '{endDate}' must be an exact day");
            if (string.IsNullOrWhiteSpace(sportOrAll)) throw new ArgumentNullException(nameof(sportOrAll));
            if (string.IsNullOrWhiteSpace(leagueOrAll)) throw new ArgumentNullException(nameof(leagueOrAll));
            if (string.IsNullOrWhiteSpace(tournamentIdOrAll)) throw new ArgumentNullException(nameof(tournamentIdOrAll));
            if (string.IsNullOrWhiteSpace(gameIdOrAll)) throw new ArgumentNullException(nameof(gameIdOrAll));
            if (string.IsNullOrWhiteSpace(accountNumberOrAll)) throw new ArgumentNullException(nameof(accountNumberOrAll));
            if (string.IsNullOrWhiteSpace(domainIdsOrAll)) throw new ArgumentNullException(nameof(domainIdsOrAll));

            var sportToSearch = sportOrAll == SELECTION_ALL ? string.Empty : sportOrAll;
            var leagueToSearch = leagueOrAll == SELECTION_ALL ? string.Empty : leagueOrAll;
            var tournamentIdToSearch = tournamentIdOrAll == SELECTION_ALL ? 0 : Convert.ToInt32(tournamentIdOrAll);
            var gameIdToSearch = gameIdOrAll == SELECTION_ALL ? 0 : Convert.ToInt32(gameIdOrAll);
            var accountNumberToSearch = accountNumberOrAll == SELECTION_ALL ? string.Empty : accountNumberOrAll;
            var domainIdsToSearch = domainIdsOrAll == SELECTION_ALL ? string.Empty : domainIdsOrAll;

            var result = QueryMaker.GenerateCompletedWinnerWagersReport(startDate, endDate, sportToSearch, leagueToSearch, tournamentIdToSearch, gameIdToSearch, accountNumberToSearch, domainIdsToSearch);
            return result;
        }

        internal CompletedLines GenerateCompletedLinesReport(DateTime startDate, DateTime endDate, string sportOrAll, string leagueOrAll, string tournamentIdOrAll, string gameIdOrAll)
        {
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate > endDate) throw new GameEngineException($"{nameof(startDate)} cannot be greater than {nameof(endDate)}");
            if (startDate.Hour != 0 || startDate.Minute != 0 || startDate.Second != 0 || startDate.Millisecond != 0) throw new GameEngineException($"{nameof(startDate)} '{startDate}' must be an exact day");
            if (endDate.Hour != 0 || endDate.Minute != 0 || endDate.Second != 0 || endDate.Millisecond != 0) throw new GameEngineException($"{nameof(endDate)} '{endDate}' must be an exact day");
            if (string.IsNullOrWhiteSpace(sportOrAll)) throw new ArgumentNullException(nameof(sportOrAll));
            if (string.IsNullOrWhiteSpace(leagueOrAll)) throw new ArgumentNullException(nameof(leagueOrAll));
            if (string.IsNullOrWhiteSpace(tournamentIdOrAll)) throw new ArgumentNullException(nameof(tournamentIdOrAll));
            if (string.IsNullOrWhiteSpace(gameIdOrAll)) throw new ArgumentNullException(nameof(gameIdOrAll));

            var sportToSearch = sportOrAll == SELECTION_ALL ? string.Empty : sportOrAll;
            var leagueToSearch = leagueOrAll == SELECTION_ALL ? string.Empty : leagueOrAll;
            var tournamentIdToSearch = tournamentIdOrAll == SELECTION_ALL ? 0 : Convert.ToInt32(tournamentIdOrAll);
            var gameIdToSearch = gameIdOrAll == SELECTION_ALL ? 0 : Convert.ToInt32(gameIdOrAll);

            var result = QueryMaker.GenerateCompletedLinesReport(startDate, endDate, sportToSearch, leagueToSearch, tournamentIdToSearch, gameIdToSearch);
            return result;
        }

        internal DailyProfitReport GenerateDailyProfitReport(DateTime startDate, DateTime endDate, string sportOrAll, string leagueOrAll, string tournamentIdOrAll, string gameIdOrAll, string lineTypeOrAll, string domainIdsOrAll)
        {
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate > endDate) throw new GameEngineException($"{nameof(startDate)} cannot be greater than {nameof(endDate)}");
            if (startDate.Hour != 0 || startDate.Minute != 0 || startDate.Second != 0 || startDate.Millisecond != 0) throw new GameEngineException($"{nameof(startDate)} '{startDate}' must be an exact day");
            if (endDate.Hour != 0 || endDate.Minute != 0 || endDate.Second != 0 || endDate.Millisecond != 0) throw new GameEngineException($"{nameof(endDate)} '{endDate}' must be an exact day");
            if (string.IsNullOrWhiteSpace(sportOrAll)) throw new ArgumentNullException(nameof(sportOrAll));
            if (string.IsNullOrWhiteSpace(leagueOrAll)) throw new ArgumentNullException(nameof(leagueOrAll));
            if (string.IsNullOrWhiteSpace(tournamentIdOrAll)) throw new ArgumentNullException(nameof(tournamentIdOrAll));
            if (string.IsNullOrWhiteSpace(gameIdOrAll)) throw new ArgumentNullException(nameof(gameIdOrAll));
            if (string.IsNullOrWhiteSpace(lineTypeOrAll)) throw new ArgumentNullException(nameof(lineTypeOrAll));
            if (string.IsNullOrWhiteSpace(domainIdsOrAll)) throw new ArgumentNullException(nameof(domainIdsOrAll));

            var lineTypeIdToSearch = 0;
            if (lineTypeOrAll != SELECTION_ALL)
            {
                LineType lineTypeToSearch;
                if (!Enum.TryParse(lineTypeOrAll, out lineTypeToSearch)) throw new GameEngineException($"{nameof(lineTypeOrAll)} '{lineTypeOrAll}' is not valid");
                lineTypeIdToSearch = (int)lineTypeToSearch;
            }

            var sportToSearch = sportOrAll == SELECTION_ALL ? string.Empty : sportOrAll;
            var leagueToSearch = leagueOrAll == SELECTION_ALL ? string.Empty : leagueOrAll;
            var tournamentIdToSearch = tournamentIdOrAll == SELECTION_ALL ? 0 : Convert.ToInt32(tournamentIdOrAll);
            var gameIdToSearch = gameIdOrAll == SELECTION_ALL ? 0 : Convert.ToInt32(gameIdOrAll);
            var domainIdsToSearch = domainIdsOrAll == SELECTION_ALL ? string.Empty : domainIdsOrAll;

            var report = new DailyProfitReport();
            report.Generate(startDate, endDate, sportToSearch, leagueToSearch, tournamentIdToSearch, gameIdToSearch, lineTypeIdToSearch, domainIdsToSearch);
            return report;
        }
    }

    internal class ScoresRecords : Objeto
    {
        public string TeamA { get; set; }
        public string TeamB { get; set; }
        public int ScoreTeamA { get; set; }
        public int ScoreTeamB { get; set; }
        public int GameNumber { get; set; }
    }
}