﻿using GamesEngine.Exchange;
using GamesEngine.Games.Lotto;
using GamesEngine.Logs;
using GamesEngine.MessageQueuing;
using GamesEngine.Settings;
using GamesEngine.Time;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using static GamesEngine.Exchange.PaymentProcessorsAndActionsByDomains;
using static GamesEngine.Finance.PaymentChannels;

namespace GamesEngine.Domains
{
	[Puppet]
    public class Domain : Objeto, ILog
    {
        private Uri mainUrl;
        private Dictionary<string, ResourceUrlStruct> resourceUrls = new Dictionary<string, ResourceUrlStruct>();
        internal AllowedTransactions AllowedTransactions { get; } = new AllowedTransactions();

        internal struct ResourceUrlStruct
        {
            private readonly string key;

            private Uri uri;
            internal Uri Uri
            {
                get
                {
                    return uri;
                }
            }

            private readonly bool isFlexible;
            internal bool IsFlexible
            {
                get
                {
                    return isFlexible;
                }
            }

            internal ResourceUrlStruct(string key, string url, bool isFlexible)
            {
                if (string.IsNullOrWhiteSpace(key)) throw new ArgumentNullException(nameof(key));
                if (string.IsNullOrWhiteSpace(url)) throw new ArgumentNullException(nameof(url));

                this.key = key;
                this.uri = new UriBuilder(url).Uri;
                this.isFlexible = isFlexible;
            }

            internal string Url()
            {
                if (isFlexible)
                {
                    return uri.Host;
                }
                return uri.AbsoluteUri;
            }
        }

        internal IEnumerable<string> ResourceKeys
        {
            get
            {
                return resourceUrls.Keys;
            }
        }

        internal string Url
        {
            get
            {
                if (mainUrl == null) throw new ArgumentNullException(nameof(mainUrl));
                return mainUrl.Host;
            }
        }

        internal int Id { get; private set; }

        internal Agents Agent { get; }

        internal int AgentId => (int)Agent;

        internal bool Visible { get; set; } = true;

        public Domain(bool itIsThePresent, int id, string url, Agents agent)
        {
            if (String.IsNullOrWhiteSpace(url)) throw new ArgumentNullException(nameof(url));
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");
            if (!Uri.IsWellFormedUriString(url, UriKind.RelativeOrAbsolute)) throw new GameEngineException($"URL {url} is not well formed");

            Id = id;
            mainUrl = new UriBuilder(url).Uri;
            Agent = agent;
            if (itIsThePresent && (Integration.UseKafka || Integration.UseKafkaForAuto))
            {
                Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForDomains, new DomainBIMessage(id, url));
                Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForCatalog, new DomainAddMessage(id, url));
            }
        }

        internal Domain(bool itIsThePresent, int id, string url, Agents agent, Logs.Log log):this(itIsThePresent, id, url, agent)
        {
            if (log == null) throw new ArgumentNullException(nameof(log));

            this.log = log;
        }

        internal bool HasTheSame(string url)
        {
            return mainUrl.Host == url;
        }

        internal bool HasResourceUrl(string key)
        {
            if (string.IsNullOrWhiteSpace(key)) throw new ArgumentNullException(nameof(key));

            var result = resourceUrls.ContainsKey(key);
            return result;
        }

        internal void AddResourceUrl(string key, string url)
        {
            if (string.IsNullOrWhiteSpace(key)) throw new ArgumentNullException(nameof(key));
            if (string.IsNullOrWhiteSpace(url)) throw new ArgumentNullException(nameof(url));
            if (resourceUrls.ContainsKey(key)) throw new GameEngineException($"{nameof(url)} {url} already exist");

            var normalizedUrl = url.ToLower().Trim();
            var resourceUrl = new ResourceUrlStruct(key, normalizedUrl, false);
            resourceUrls.Add(key, resourceUrl);
        }

        internal void AddFlexibleResourceUrl(string key, string url)
        {
            if (string.IsNullOrWhiteSpace(key)) throw new ArgumentNullException(nameof(key));
            if (string.IsNullOrWhiteSpace(url)) throw new ArgumentNullException(nameof(url));
            if (resourceUrls.ContainsKey(key)) throw new GameEngineException($"{nameof(url)} {url} already exist");

            var normalizedUrl = url.ToLower().Trim();
            var resourceUrl = new ResourceUrlStruct(key, normalizedUrl, true);
            resourceUrls.Add(key, resourceUrl);
        }

        private void RemoveResourceUrl(string key)
        {
            if (string.IsNullOrWhiteSpace(key)) throw new ArgumentNullException(nameof(key));
            if (!resourceUrls.ContainsKey(key)) throw new GameEngineException($"{nameof(key)} '{key}' does not exist");

            resourceUrls.Remove(key);
        }

        internal void UpdateResourceUrl(string key, string newUrl)
        {
            if (string.IsNullOrWhiteSpace(key)) throw new ArgumentNullException(nameof(key));
            if (string.IsNullOrWhiteSpace(newUrl)) throw new ArgumentNullException(nameof(newUrl));

            var currentResourceUrl = resourceUrls.GetValueOrDefault(key);
            if (currentResourceUrl.Equals(default(ResourceUrlStruct))) throw new GameEngineException($"{nameof(currentResourceUrl)} with key '{key}' does not exist");

            var isFlexibleUrl = IsFlexibleResourceUrl(key);
            var normalizedUrl = newUrl.ToLower().Trim();
            var newResourceUrl = new ResourceUrlStruct(key, normalizedUrl, isFlexibleUrl);
            resourceUrls[key] = newResourceUrl;
        }

        internal void AddOrUpdateResourceUrl(string key, string newUrl)
        {
            if (string.IsNullOrWhiteSpace(key)) throw new ArgumentNullException(nameof(key));

            var resourceUrl = resourceUrls.GetValueOrDefault(key);
            if (resourceUrl.Equals(default(ResourceUrlStruct)))
            {
                if (!string.IsNullOrWhiteSpace(newUrl))
                {
                    AddResourceUrl(key, newUrl);
                }
            }
            else
            {
                if (string.IsNullOrWhiteSpace(newUrl))
                {
                    RemoveResourceUrl(key);
                }
                else
                {
                    UpdateResourceUrl(key, newUrl);
                }
            }
        }

        internal void AddOrUpdateFlexibleResourceUrl(string key, string newUrl)
        {
            if (string.IsNullOrWhiteSpace(key)) throw new ArgumentNullException(nameof(key));

            var resourceUrl = resourceUrls.GetValueOrDefault(key);
            if (resourceUrl.Equals(default(ResourceUrlStruct)))
            {
                if (!string.IsNullOrWhiteSpace(newUrl))
                {
                    AddFlexibleResourceUrl(key, newUrl);
                }
            }
            else
            {
                if (string.IsNullOrWhiteSpace(newUrl))
                {
                    RemoveResourceUrl(key);
                }
                else
                {
                    UpdateResourceUrl(key, newUrl);
                }
            }
        }

        internal string ResourceUrl(string key)
        {
            var resourceUrl = resourceUrls.GetValueOrDefault(key);
            if (resourceUrl.Equals(default(ResourceUrlStruct))) throw new GameEngineException($"{nameof(resourceUrl)} with key '{key}' does not exist");
            return resourceUrl.Url();
        }

        internal bool IsFlexibleResourceUrl(string key)
        {
            var resourceUrl = resourceUrls.GetValueOrDefault(key);
            if (resourceUrl.Equals(default(ResourceUrlStruct))) throw new GameEngineException($"{nameof(resourceUrl)} with key '{key}' does not exist");
            return resourceUrl.IsFlexible;
        }

        internal void UpdateMainUrl(bool itIsThePresent, string newUrl)
        {
            if (string.IsNullOrWhiteSpace(newUrl)) throw new ArgumentNullException(nameof(newUrl));

            mainUrl = new UriBuilder(newUrl).Uri;
            if (Integration.UseKafka) Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForDomains, new DomainBIMessage(Id, newUrl));
        }

        public static string NormalizeUrl(string url)
        {
            if (string.IsNullOrWhiteSpace(url)) throw new ArgumentNullException(nameof(url));

            var urlNormalized = url.Trim();
            if (urlNormalized.StartsWith("https://", StringComparison.OrdinalIgnoreCase))
            {
                urlNormalized = urlNormalized.Substring("https://".Length);
            }
            else if (urlNormalized.StartsWith("http://", StringComparison.OrdinalIgnoreCase))
            {
                urlNormalized = urlNormalized.Substring("http://".Length);
            }
            int index = urlNormalized.IndexOf(':');
            if (index != -1)
            {
                urlNormalized = urlNormalized.Substring(0, index);
            }
            var result = new UriBuilder(urlNormalized).Uri;
            return result.Host;
        }

        private readonly Logs.Log log;
        public void AddAnnotation(string message, string who, DateTime now)
        {
            if (log == null) throw new ArgumentNullException(nameof(log));

            log.AddEntry(now, who, $"{message} by {who} at {now.ToString("MM/dd/yyyy hh:mm tt")}.");
        }

        public IEnumerable<LogEntry> LastEntries(int number)
        {
            if (number <= 0) throw new GameEngineException($"{nameof(number)} must be greater than 0");

            var result = log.LastEntries(number);
            return result;
        }
    }

    public class DomainBIMessage : GradeMessage
    {
        public int Id { get; private set; }
        public string Url { get; private set; }

        internal DomainBIMessage(int id, string url) : base(GradeTicketType.DOMAINS)
        {
            if (Id < 0) throw new ArgumentException(nameof(Id));
            if (string.IsNullOrWhiteSpace(url)) throw new ArgumentException(nameof(url));

            Id = id;
            Url = url;
        }

        public DomainBIMessage(string message) : base(message)
        {
        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty(Id).
            AddProperty(Url);
        }

        protected override void Deserialize(string[] message, out int fieldOrder)
        {
            base.Deserialize(message, out fieldOrder);
            Id = int.Parse(message[fieldOrder++]);
            Url = message[fieldOrder++];
        }
    }
}
