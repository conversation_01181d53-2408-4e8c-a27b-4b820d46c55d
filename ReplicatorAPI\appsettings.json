{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"CashierAPI": "persistsecurityinfo=True;port=3306;Server=localhost;Database=sync2cashier;user id=root;password=*********;SslMode=none", "ExchangeAPI": "persistsecurityinfo=True;port=3306;Server=localhost;Database=sync2_exchange;user id=root;password=*********;SslMode=none", "GuardianAPI": "persistsecurityinfo=True;port=3306;Server=localhost;Database=guardianapi_db;user id=root;password=*********;SslMode=none", "KnowYourCustomerAPI": "persistsecurityinfo=True;port=3306;Server=localhost;Database=sync2_kyc;user id=root;password=*********;SslMode=none", "KenoBIAPI": "persistsecurityinfo=True;port=3306;Server=localhost;Database=kenobi_db;user id=root;password=*********;SslMode=none", "LinesAPI": "persistsecurityinfo=True;port=3306;Server=localhost;Database=linesapi_db;user id=root;password=*********;SslMode=none", "LottoAPI": "persistsecurityinfo=True;port=3306;Server=localhost;Database=kor_lotto;user id=root;password=*********;SslMode=none", "LottoBIAPI": "persistsecurityinfo=True;port=3306;Server=localhost;Database=kor_lottobi;user id=root;password=*********;SslMode=none", "LoyaltyAPI": "persistsecurityinfo=True;port=3306;Server=localhost;Database=upgrade_loyaltyln;user id=root;password=*********;SslMode=none", "ResourcesAPI": "persistsecurityinfo=True;port=3306;Server=localhost;Database=resources_db;user id=root;password=*********;SslMode=none"}}