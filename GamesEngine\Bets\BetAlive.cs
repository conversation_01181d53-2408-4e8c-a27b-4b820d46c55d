﻿using GamesEngine.Gameboards;
using GamesEngine.Tools;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamesEngine.Bets
{
    [Puppet]
    internal sealed class BetAlive : Bet
    {
		[Obsolete("Do not use it is for legacy purposes")]
        internal BetAlive(Player player, Gameboard gameboard, Reward reward, DateTime creationDate) : base(player, gameboard, reward, creationDate)
        {

        }

		internal BetAlive(Player player, Gameboard gameboard, Reward reward, DateTime creationDate, int nextBetNumber) : base(player, gameboard, reward, creationDate, nextBetNumber)
		{

		}

		internal void Contribute(decimal amount)
        {
            if (amount <= 0) throw new GameEngineException($"Bet amount can not be {amount}. Amount must be greater than zero");
            Commons.ValidateAmount(amount);

            Reward.Contribute(Player, amount);
            this.SetContribution (amount);
        }

        internal override BetState State
        {
            get
            {
                return BetState.ALIVE;
            }
        }

        internal Bet Lock()
        {
            if (Invalid) throw new GameEngineException("This bet was already locked.");
            return new BetLocked(this);
        }

    }
}
