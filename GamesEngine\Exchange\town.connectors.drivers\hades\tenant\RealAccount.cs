﻿using Puppeteer.EventSourcing;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.commons;
using town.connectors.drivers;
using town.connectors.drivers.hades;

namespace Connectors.town.connectors.drivers.hades.tenant
{
	public class RealAccount: ASITenantDriver
    {
		private HttpClient _getLottoCustomerClient;
		private string SystemId;
		private string SystemPassword;
		public RealAccount() : base(Tenant_Actions.Others)
		{
		}

		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			throw new NotImplementedException();
		}

		public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			if (_getLottoCustomerClient == null)
			{
				string companyBaseUrlServices = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
				_getLottoCustomerClient = new HttpClient
				{
					BaseAddress = new Uri(companyBaseUrlServices)
				};
			}

			if (CustomSettings.ThereArePendingChanges)
			{
				bool changeApplied = false;
				string companyBaseUrlServices = CustomSettings.Get(now, "CompanyBaseUrlServices", out changeApplied).AsString;
				if (changeApplied)
				{
					_getLottoCustomerClient = new HttpClient
					{
						BaseAddress = new Uri(companyBaseUrlServices)
					};
				}

				SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
				SystemPassword = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
			}

			var CustomerId = recordSet.Mappings["customerId"];

			var result = await IsARealAccountAsync(now, CustomerId.AsString);
			return (T)Convert.ChangeType(result, typeof(T));
		}
		public override void Prepare(DateTime now)
		{
			CustomSettings.AddVariableParameter("customerId");

			//CustomSettings.Prepare();

			SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
			SystemPassword = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
		}

		async Task<bool> IsARealAccountAsync(DateTime now, string customerId)
		{
			if (String.IsNullOrWhiteSpace(customerId)) throw new ArgumentNullException(nameof(customerId));
			const int BALANCE_FOR_NO_REAL_USER = -9999999;

			var values = new CustomerBalance()
			{
				SystemId = SystemId,
				SystemPassword = SystemPassword,
				CustomerId = customerId
			};

			var jsonString = await ASIJsonUtils.ToJsonAsync(values);
			var httpContent = new StringContent(jsonString, Encoding.UTF8, "application/json");
			const string url = "/v1/5dimesAPI/GetCustomerBalance";

			string responseString = "";

			string valuesWithHiddenFields = await ASIJsonUtils.HideSensitiveDataAsync(values, Loggers.GetIntance().AccountingServicesASI);

			try
			{
				var response = await _getLottoCustomerClient.PostAsync(url, httpContent);
				responseString = await response.Content.ReadAsStringAsync();
				decimal amount = Convert.ToDecimal(responseString);

				if (amount == BALANCE_FOR_NO_REAL_USER) return false;

				return true;
			}
			catch (Exception e)
			{
				Loggers.GetIntance().AccountingServicesASI.Error($@"url:{url} data:{valuesWithHiddenFields} type:{e.GetType()} error:{e.Message}", e);
				return false;
			}
		}
	}
}
