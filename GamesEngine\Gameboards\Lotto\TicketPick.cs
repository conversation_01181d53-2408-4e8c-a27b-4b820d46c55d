﻿using GamesEngine.Bets;
using GamesEngine.Games.Lotto;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;

namespace GamesEngine.Gameboards.Lotto
{
    public enum TicketType { P2S, P2B, P3S, P3B, P4S, P4B, P5S, P5B, PBS, PBP, K10, K12 }

    internal abstract class TicketPick<Pick>: Ticket where Pick:IPick
    {
		private Pick[] numbers;
		private Pick[] excludes;
        internal override Prizes Prizes
        {
            get
            {
                var todaysPrizesOfLottery_Or_VersionOfDrawing = (draw == null || draw is LotteryNoAction) ? Lottery.PicksLotteryGame.RiskProfiles.GetRiskProfile(Order.Domain).Prizes : prizes;
                return todaysPrizesOfLottery_Or_VersionOfDrawing;
            }
        }

        internal TicketPick(Player player, Lottery lottery, DateTime drawDate, Pick pick, Selection selectionMode, DateTime creationDate, decimal ticketCost, Prizes prizes) : 
            base(player, lottery, drawDate, selectionMode, creationDate, ticketCost, prizes)
        {
			if (base.SelectionMode != Selection.BALLS) throw new GameEngineException("Input numbers only can be set later in the case of balls.");
			numbers = new Pick[] { pick };
        }

		internal TicketPick(Player player, Lottery lottery, DateTime drawDate, string[] numbers, Selection selectionMode, DateTime creationDate, decimal ticketCost, Prizes prizes) :
			base(player, lottery, drawDate, selectionMode, creationDate, ticketCost, prizes)
		{
			if (base.SelectionMode != Selection.MultipleInputSingleAmount && SelectionMode != Selection.SingleInputMultipleAmount) throw new GameEngineException("Input numbers only can be set later in the case of inputs.");
			if (numbers == null) throw new ArgumentNullException(nameof(numbers));
            if (numbers.Any(x => string.IsNullOrEmpty(x))) throw new ArgumentNullException(nameof(numbers));

			IncludeGroupOfNumbers(numbers);
		}
        
        protected abstract void GradeNumber(Pick winnerNumber, int fireBallNumber = int.MinValue);

        protected decimal RoundToNearestFiveCents(decimal amount)
        {
            const decimal roundTo = 0.05m;
            decimal remainder = amount % roundTo;

            if (remainder == 0)
            {
                return amount;
            }

            if (remainder < roundTo / 2)
            {
                return amount - remainder;
            }
            else
            {
                return amount + (roundTo - remainder);
            }
        }

        internal override void LotteryDraw(LotteryDraw lotteryDraw)
        {
            if (! base.IsUnprized()) throw new GameEngineException($"Ticket should be in {GameboardStatus.UNPRIZED} status but it is at {Prizing}");
            if (lotteryDraw == null) throw new ArgumentNullException(nameof(lotteryDraw));

            if (BelongsToFireBallDraw)
            {
                GradeNumber((Pick)lotteryDraw.WinnerNumber, lotteryDraw.FireBallNumber);
            }
            else
            {
                GradeNumber((Pick)lotteryDraw.WinnerNumber);
            }

            prizes = Lottery.PicksLotteryGame.RiskProfiles.GetRiskProfile(Order.Domain).Prizes;
            base.Draw = lotteryDraw;
        }

        internal void ExcludeGroupOfNumbers(string excludesList)
        {
            if (!String.IsNullOrWhiteSpace(excludesList))
            {
                var countNumbers = this.Count;
                var excludesArr = excludesList.Split(",");
                var countExcluded = excludesArr.Length;
                //if (countNumbers <= countExcluded) throw new GameEngineException("Ticket can not be exclude because this would generate an empty ticket");
                excludes = new Pick[countExcluded];
                if (excludes[0] is Pick2)
                {
                    for (int i = 0; i < countExcluded; i++)
                    {
                        var numbers = excludesArr[i];
                        if (numbers.Length != 2) throw new GameEngineException($"Number [{numbers}] is not a valid {nameof(Pick2)} ticket");
                        excludes[i] = (Pick)(IPick)new Pick2(numbers);
                    }
                }
                else if (excludes[0] is Pick3)
                {
                    for (int i = 0; i < countExcluded; i++)
                    {
                        var numbers = excludesArr[i];
                        if (numbers.Length != 3) throw new GameEngineException($"Number [{numbers}] is not a valid {nameof(Pick3)} ticket");
                        excludes[i] = (Pick)(IPick)new Pick3(numbers);
                        //for (int p = i - 1; p >= 0; p--)
                        //if (excludes[p].Equals(exclude))
                        //throw new GameEngineException($"Number [{digit1},{digit2},{digit3}] is already excluded on ticket {AsString()}");
                    }
                }
                else if (excludes[0] is Pick4)
                {
                    for (int i = 0; i < countExcluded; i++)
                    {
                        var numbers = excludesArr[i];
                        if (numbers.Length != 4) throw new GameEngineException($"Number [{numbers}] is not a valid {nameof(Pick4)} ticket");
                        excludes[i] = (Pick)(IPick)new Pick4(numbers);
                    }
                }
                else if (excludes[0] is Pick5)
                {
                    for (int i = 0; i < countExcluded; i++)
                    {
                        var numbers = excludesArr[i];
                        if (numbers.Length != 5) throw new GameEngineException($"Number [{numbers}] is not a valid {nameof(Pick5)} ticket");
                        excludes[i] = (Pick)(IPick)new Pick5(numbers);
                    }
                }
                else
                {
                    throw new GameEngineException("Unknown Pick type");
                }
            }
        }

        private void IncludeGroupOfNumbers(string [] includesArr)
		{
			if (base.SelectionMode != Selection.MultipleInputSingleAmount && SelectionMode != Selection.SingleInputMultipleAmount) throw new GameEngineException("Input numbers only can be included for input selection.");
            if (includesArr == null) throw new ArgumentNullException(nameof(includesArr));
            var countToInclude = includesArr.Length;
            numbers = new Pick[countToInclude];
            if (numbers[0] is Pick2)
            {
                for (int i = 0; i < countToInclude; i++)
                {
                    var pattern = includesArr[i];
                    if (pattern.Length != 2) throw new GameEngineException($"Number [{pattern}] is not a valid {nameof(Pick2)} ticket");
                    numbers[i] = (Pick)(IPick)new Pick2(pattern);
                    for (int p = i - 1; p >= 0; p--)
                        if (numbers[p].Equals(numbers[i]))
                            throw new GameEngineException($"Number [{pattern}] is already included on ticket {AsString()}");
                }
            }
            else if (numbers[0] is Pick3)
            {
                for (int i = 0; i < countToInclude; i++)
                {
                    var pattern = includesArr[i];
                    if (pattern.Length != 3) throw new GameEngineException($"Number [{pattern}] is not a valid {nameof(Pick3)} ticket");
                    numbers[i] = (Pick)(IPick)new Pick3(pattern);
                    for (int p = i - 1; p >= 0; p--)
                        if (numbers[p].Equals(numbers[i]))
                            throw new GameEngineException($"Number [{pattern}] is already included on ticket {AsString()}");
                }
            }
            else if (numbers[0] is Pick4)
            {
                for (int i = 0; i < countToInclude; i++)
                {
                    var pattern = includesArr[i];
                    if (pattern.Length != 4) throw new GameEngineException($"Number [{pattern}] is not a valid {nameof(Pick4)} ticket");
                    numbers[i] = (Pick)(IPick)new Pick4(pattern);
                    for (int p = i - 1; p >= 0; p--)
                        if (numbers[p].Equals(numbers[i]))
                            throw new GameEngineException($"Number [{pattern}] is already included on ticket {AsString()}");
                }
            }
            else if (numbers[0] is Pick5)
            {
                for (int i = 0; i < countToInclude; i++)
                {
                    var pattern = includesArr[i];
                    if (pattern.Length != 5) throw new GameEngineException($"Number [{pattern}] is not a valid {nameof(Pick5)} ticket");
                    numbers[i] = (Pick)(IPick)new Pick5(pattern);
                    for (int p = i - 1; p >= 0; p--)
                        if (numbers[p].Equals(numbers[i]))
                            throw new GameEngineException($"Number [{pattern}] is already included on ticket {AsString()}");
                }
            }
            else
            {
                throw new GameEngineException("Unknown Pick type");
            }
        }

        internal override int Count
        {
            get
            {
                var count = numbers.Sum(x => x.Count) - (excludes == null ? 0 : excludes.Length);
                return count;
            }
        }

        [Conditional("DEBUG")]
        protected void ValidateThereIsOnlyOneSubticket()
        {
            if (Numbers.Count() != 1) throw new GameEngineException($"{nameof(Numbers)} must contain only one element if selection was {Ticket.Selection.BALLS}");
        }

        internal override IEnumerable<SubTicket<IPick>> SubTickets()
        {
			var result = new List<SubTicket<IPick>>();
			foreach (Pick p in numbers)
			{
                foreach (Pick subTicket in p.Split())
                {
                    if (excludes == null || ! excludes.Contains(subTicket))
                    {
                        result.AddRange(subTicket.SubTickets());
                    }
                }
			}
            return result;
        }

        internal override IEnumerable<SubTicket<IPick>> Permute()
        {
            var result = new List<SubTicket<IPick>>();
            foreach (Pick p in numbers)
            {
                foreach (Pick subTicket in p.Permute())
                {
                    result.AddRange(subTicket.SubTickets());
                }
            }
            return result;
        }

        protected IEnumerable<Pick> Numbers
		{
			get
			{
				return numbers;
			}
		}

        internal IEnumerable<Pick> NumbersWithoutExcludes
        {
            get
            {
                return numbers.Where(x => excludes == null || !excludes.Contains(x));
            }
        }

        internal sealed override string AsString()
        {
            StringBuilder result = new StringBuilder();
            result.Append(IdOfType().ToString());
            if (HasExcluded() || SelectionWasInputs)
            {
                foreach (var subticket in SubTickets())
                {
                    result.Append(subticket.AsString());
                }
            }
            else
            {
                result.Append(SingleAsString());
            }

            return result.ToString();
        }

        protected internal string SingleAsString()
		{
			switch (numbers.Length)
			{
				case 1:
					return numbers[0].AsString();
				case 2:
					return numbers[0].AsString() + " and " + numbers[1].AsString();
				case 3:
					return numbers[0].AsString() + ", " + numbers[1].AsString() + " and " + numbers[2].AsString();
			}
			return numbers[0].AsString() + ", " + numbers[1].AsString() + " and " + (numbers.Length - 2) + " more";
		}

        protected internal bool HasOnlyOnePrize()
        {
            var result = TicketsByPrize().Count() == 1;
            return result;
        }

        protected internal bool IsExcluded(ref Pick aNumber)
        {
            if (excludes == null) return false;
            bool result = excludes.Contains(aNumber);
            return result;
        }

        protected bool IsExcluded(int digit1, int digit2)
        {
            Pick currentPick = default(Pick);
            if (!(currentPick is Pick2)) throw new GameEngineException($"Variable type must be {nameof(Pick2)} to use this method");
            if (excludes == null) return false;

            var result = excludes.Cast<Pick2>().Any(x => x.IsMarked(digit1, digit2));
            return result;
        }

        protected bool IsExcluded(Pick2 pick2)
        {
			if (excludes == null) return false;
			foreach (var subticket in pick2.SubTickets())
			{
				if (IsExcluded(subticket[1], subticket[2])) return true;
			}
			return false;
        }


        protected bool IsExcluded(Pick3 pick3)
        {
			if (excludes == null) return false;
			foreach (var subticket in pick3.SubTickets())
			{
				if (IsExcluded(subticket[1], subticket[2], subticket[3])) return true;
			}
            return false;
        }

        protected bool IsExcluded(Pick4 pick4)
        {
			if (excludes == null) return false;
			foreach (var subticket in pick4.SubTickets())
			{
				if (IsExcluded(subticket[1], subticket[2], subticket[3], subticket[4])) return true;
			}
			return false;
        }

        protected bool IsExcluded(Pick5 pick5)
        {
			if (excludes == null) return false;
			foreach (var subticket in pick5.SubTickets())
			{
				if (IsExcluded(subticket[1], subticket[2], subticket[3], subticket[4], subticket[5])) return true;
			}
			return false;
        }

        protected bool IsExcluded(int digit1, int digit2, int digit3)
        {
            Pick currentPick = default(Pick);
            if (!(currentPick is Pick3)) throw new GameEngineException($"Variable type must be {nameof(Pick3)} to use this method");
            if (excludes == null) return false;

            var result = excludes.Cast<Pick3>().Any(x => x.IsMarked(digit1, digit2, digit3));
            return result;
        }

        protected bool IsExcluded(int digit1, int digit2, int digit3, int digit4)
        {
            Pick currentPick = default(Pick);
            if (!(currentPick is Pick4)) throw new GameEngineException($"Variable type must be {nameof(Pick4)} to use this method");
            if (excludes == null) return false;

            var result = excludes.Cast<Pick4>().Any(x => x.IsMarked(digit1, digit2, digit3, digit4));
            return result;
        }

        protected bool IsExcluded(int digit1, int digit2, int digit3, int digit4, int digit5)
        {
            Pick currentPick = default(Pick);
            if (!(currentPick is Pick5)) throw new GameEngineException($"Variable type must be {nameof(Pick5)} to use this method");
            if (excludes == null) return false;

            var result = excludes.Cast<Pick5>().Any(x => x.IsMarked(digit1, digit2, digit3, digit4, digit5));
            return result;
        }

        internal static List<Pick> RandomOf(int numberOfSubtickets)
        {
            if (numberOfSubtickets < 1) throw new GameEngineException($"Cannot take {numberOfSubtickets} numbers");
            var random = new Random();
            var picks = new List<Pick>();
            Pick currentPick = default(Pick);
            if (currentPick is Pick2)
            {
                while (numberOfSubtickets > 0)
                {
                    int digit1 = random.Next(10);
                    int digit2 = random.Next(10);
                    var pick = (Pick)(IPick)new Pick2($"{digit1}{digit2}");
                    if (! picks.Contains(pick))
                    {
                        picks.Add(pick);
                        numberOfSubtickets--;
                    }
                }

                return picks;
            }
            else if (currentPick is Pick3)
            {
                while (numberOfSubtickets > 0)
                {
                    int digit1 = random.Next(10);
                    int digit2 = random.Next(10);
                    int digit3 = random.Next(10);
                    var pick = (Pick)(IPick)new Pick3($"{digit1}{digit2}{digit3}");
                    if (!picks.Contains(pick))
                    {
                        picks.Add(pick);
                        numberOfSubtickets--;
                    }
                }

                return picks;
            }
            else if (currentPick is Pick4)
            {
                while (numberOfSubtickets > 0)
                {
                    int digit1 = random.Next(10);
                    int digit2 = random.Next(10);
                    int digit3 = random.Next(10);
                    int digit4 = random.Next(10);
                    var pick = (Pick)(IPick)new Pick4($"{digit1}{digit2}{digit3}{digit4}");
                    if (!picks.Contains(pick))
                    {
                        picks.Add(pick);
                        numberOfSubtickets--;
                    }
                }

                return picks;
            }
            else if (currentPick is Pick5)
            {
                while (numberOfSubtickets > 0)
                {
                    int digit1 = random.Next(10);
                    int digit2 = random.Next(10);
                    int digit3 = random.Next(10);
                    int digit4 = random.Next(10);
                    int digit5 = random.Next(10);
                    var pick = (Pick)(IPick)new Pick5($"{digit1}{digit2}{digit3}{digit4}{digit5}");
                    if (!picks.Contains(pick))
                    {
                        picks.Add(pick);
                        numberOfSubtickets--;
                    }
                }

                return picks;
            }
            else
            {
                throw new GameEngineException("Unknown Pick type");
            }
        }

        internal static List<Pick> RandomNumbersExceptOf(List<IPick> originalNumbers, int numberOfSubtickets)
        {
            if (originalNumbers.Count == 0) throw new GameEngineException($"{nameof(originalNumbers)} {originalNumbers} must contain any number");
            if (numberOfSubtickets < 1) throw new GameEngineException($"Cannot take {numberOfSubtickets} numbers");

            var random = new Random();
            var picks = new List<Pick>();
            Pick currentPick = default(Pick);
            if (currentPick is Pick2)
            {
                while (numberOfSubtickets > 0)
                {
                    int digit1 = random.Next(10);
                    int digit2 = random.Next(10);
                    var pick = (Pick)(IPick)new Pick2($"{digit1}{digit2}");
                    var isNewPick = ! picks.Contains(pick) && ! originalNumbers.Contains(pick);
                    if (isNewPick)
                    {
                        picks.Add(pick);
                        numberOfSubtickets--;
                    }
                }

                return picks;
            }
            else if (currentPick is Pick3)
            {
                while (numberOfSubtickets > 0)
                {
                    int digit1 = random.Next(10);
                    int digit2 = random.Next(10);
                    int digit3 = random.Next(10);
                    var pick = (Pick)(IPick)new Pick3($"{digit1}{digit2}{digit3}");
                    var isNewPick = ! picks.Contains(pick) && ! originalNumbers.Contains(pick);
                    if (isNewPick)
                    {
                        picks.Add(pick);
                        numberOfSubtickets--;
                    }
                }

                return picks;
            }
            else if (currentPick is Pick4)
            {
                while (numberOfSubtickets > 0)
                {
                    int digit1 = random.Next(10);
                    int digit2 = random.Next(10);
                    int digit3 = random.Next(10);
                    int digit4 = random.Next(10);
                    var pick = (Pick)(IPick)new Pick4($"{digit1}{digit2}{digit3}{digit4}");
                    var isNewPick = ! picks.Contains(pick) && ! originalNumbers.Contains(pick);
                    if (isNewPick)
                    {
                        picks.Add(pick);
                        numberOfSubtickets--;
                    }
                }

                return picks;
            }
            else if (currentPick is Pick5)
            {
                while (numberOfSubtickets > 0)
                {
                    int digit1 = random.Next(10);
                    int digit2 = random.Next(10);
                    int digit3 = random.Next(10);
                    int digit4 = random.Next(10);
                    int digit5 = random.Next(10);
                    var pick = (Pick)(IPick)new Pick5($"{digit1}{digit2}{digit3}{digit4}{digit5}");
                    var isNewPick = ! picks.Contains(pick) && ! originalNumbers.Contains(pick);
                    if (isNewPick)
                    {
                        picks.Add(pick);
                        numberOfSubtickets--;
                    }
                }

                return picks;
            }
            else
            {
                throw new GameEngineException("Unknown Pick type");
            }
        }

        protected internal bool HasExcluded()
        {
            return excludes != null && excludes.Length > 0;
        }

        internal override void RemoveWager(TicketWager wager)
        {
            if (CountWagers==1) throw new GameEngineException("Cannot remove the last wager. Please remove the ticket.");

            var subticketAsString = "";
            foreach (var subticket in wager.Subtickets)
            {
                var subtickets = subticket.AsString().Replace("[", "").Replace(",", "").Split("]", StringSplitOptions.RemoveEmptyEntries);
                var excludes = HasExcluded() ? this.excludes.SelectMany(x => x.SubTickets().SelectMany(y => y.AsString().Replace("[", "").Replace(",", "").Split("]", StringSplitOptions.RemoveEmptyEntries))).ToArray():new string []{ };
                var arrayToExclude = new string[subtickets.Length+ excludes.Length];

                Array.Copy(subtickets, 0, arrayToExclude, 0, subtickets.Length);
                Array.Copy(excludes, 0, arrayToExclude, subtickets.Length, excludes.Length);
                subticketAsString = string.Join(",", arrayToExclude);

                ExcludeGroupOfNumbers(subticketAsString);
            }
            
            RemoveThisWager(wager);
        }

        internal override bool WasCreatedByPattern()
        {
            bool result = this.numbers.Length == 1;
            return result;
        }

        internal override bool NumbersFollowPattern()
        {
            var result = !(HasExcluded() || SelectionWasInputs);
            return result;
        }

        internal override bool IsPowerball()
        {
            return false;
        }

        internal override decimal CalculatedPrize()
        {
            return Grade() * CountOfWinners();
        }

        internal override string WinnerNumbers()
        {
            var result = ((LotteryDraw)Draw).SequenceOfNumbers;
            return result;
        }

        protected enum PickPortion
        {
            NORMAL_LEVEL,
            LEVEL1_PORTION,
            LEVEL2_PORTION,
            LEVEL3_PORTION,
            LEVEL4_PORTION,
            LEVEL5_PORTION
        }

        protected abstract decimal BetAmount(PickPortion pickPortion);
    }
}
