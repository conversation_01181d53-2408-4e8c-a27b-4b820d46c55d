﻿using ExchangeAPI.ExchangeEntities;
using ExchangeAPI.Model;
using ExchangeAPI.Service;
using GamesEngine;
using GamesEngine.Accounting.JournalTemplates;
using GamesEngine.Settings;
using Microsoft.EntityFrameworkCore;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using town.connectors.drivers;

namespace ExchangeAPI.Logic
{
    public class JournalEntries
    {
        private const string ALL_SELECTED = "all";
        private const int MAXIMUM_AMOUNT_OF_ROWS_ALLOWED = 100;

        public static JournalEntriesDTO FilteredBy(DateTime startDate, DateTime endDate, string entryId, string cashierName, int initialIndex, int amountOfRows)
        {
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate.Hour != 0 || startDate.Minute != 0 || startDate.Second != 0) throw new GameEngineException($"{nameof(startDate)} {startDate} is not valid");
            if (endDate.Hour != 0 || endDate.Minute != 0 || endDate.Second != 0) throw new GameEngineException($"{nameof(endDate)} {endDate} is not valid");
            if (string.IsNullOrWhiteSpace(entryId)) throw new ArgumentNullException(nameof(entryId));
            if (string.IsNullOrWhiteSpace(cashierName)) throw new ArgumentNullException(nameof(cashierName));
            if (initialIndex < 0) throw new GameEngineException($"{nameof(initialIndex)} {initialIndex} is not valid");
            if (amountOfRows <= 0) throw new GameEngineException($"{nameof(amountOfRows)} {amountOfRows} is not valid");

            if (amountOfRows > MAXIMUM_AMOUNT_OF_ROWS_ALLOWED)
            {
                amountOfRows = MAXIMUM_AMOUNT_OF_ROWS_ALLOWED;
            }

            var allEntriesIdSelected = entryId.ToLower().Trim() == ALL_SELECTED;
            var allCashiersSelected = cashierName.ToLower().Trim() == ALL_SELECTED;

            using (var context = new ExchangeContext())
            {
                var journalEntries = context.Journalentry
                    .Include(journalEntry => journalEntry.WhocreatedNavigation)
                    .Include(journalEntry => journalEntry.Journalentrydetails)
                    .Where(journalEntry => journalEntry.Date.Date >= startDate && journalEntry.Date.Date <= endDate
                        && (allEntriesIdSelected || journalEntry.Journalentrydetailid == entryId)
                        && (allCashiersSelected || journalEntry.WhocreatedNavigation.Name == cashierName)
                        )
                    .OrderBy(journalEntry => journalEntry.Date);
                var journalEntriesPaged = journalEntries
                    .Skip(initialIndex)
                    .Take(amountOfRows);
                var journalEntriesDTO = journalEntriesPaged.MapToJournalEntriesDTO();
                journalEntriesDTO.TotalRecords = journalEntries.Count();
                return journalEntriesDTO;
            }
        }

        public static JournalEntriesDTO DetailFilteredBy(DateTime startDate, DateTime endDate, string entryId)
        {
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate.Hour != 0 || startDate.Minute != 0 || startDate.Second != 0) throw new GameEngineException($"{nameof(startDate)} {startDate} is not valid");
            if (endDate.Hour != 0 || endDate.Minute != 0 || endDate.Second != 0) throw new GameEngineException($"{nameof(endDate)} {endDate} is not valid");
            if (string.IsNullOrWhiteSpace(entryId)) throw new ArgumentNullException(nameof(entryId));
            if (entryId == ALL_SELECTED) throw new GameEngineException($"{nameof(entryId)} '{entryId}' is not valid");

            using (var context = new ExchangeContext())
            {
                var journalEntries = context.Journalentry
                    .Include(journalEntry => journalEntry.WhocreatedNavigation)
                    .Include(journalEntry => journalEntry.Journalentrydetails)
                    .Where(journalEntry => journalEntry.Date.Date >= startDate && journalEntry.Date.Date <= endDate
                        && (journalEntry.Journalentrydetailid == entryId)
                        )
                    .OrderBy(journalEntry => journalEntry.Date);
                var journalEntriesDTO = journalEntries.MapToJournalEntriesDTO();
                journalEntriesDTO.TotalRecords = journalEntries.Count();
                return journalEntriesDTO;
            }
        }

        public static void SaveWithDetails(Journalentry entry, IEnumerable<Journalentrydetails> details, TransactionType transactionType, int transactionId)
        {
            if (entry == null) throw new ArgumentNullException(nameof(entry));

            using (var context = new ExchangeContext())
            {
                using (var transaction = context.Database.BeginTransaction())
                {
                    try
                    {
                        context.Journalentry.Add(entry);
                        context.SaveChanges();

                        context.Journalentrydetails.AddRange(details);
                        context.SaveChanges();

                        switch (transactionType)
                        {
                            case TransactionType.Deposit:
                                Deposits.UpdateJournalEntry(context, transactionId, entry.Journalentrydetailid);
                                break;
                            case TransactionType.Withdrawal:
                                Withdrawals.UpdateJournalEntry(context, transactionId, entry.Journalentrydetailid);
                                break;
                            case TransactionType.Transfer:
                                Transfers.UpdateJournalEntry(context, transactionId, entry.Journalentrydetailid);
                                break;
                            case TransactionType.CreditNote:
                                CreditNotes.UpdateJournalEntry(context, transactionId, entry.Journalentrydetailid);
                                break;
                            case TransactionType.DebitNote:
                                DebitNotes.UpdateJournalEntry(context, transactionId, entry.Journalentrydetailid);
                                break;
                            default:
                                break;
                        }
                        context.SaveChanges();
                        transaction.Commit();
                    }
                    catch (Exception e)
                    {
                        transaction.Rollback();
                        var message = $"{nameof(SaveWithDetails)} method cannot add id:{entry.Id}";
                        Loggers.GetIntance().Db.Error(message, e);
                        ErrorsSender.Send(e, message);
                        throw;
                    }
                }
            }
        }
    }
}
