﻿using ExchangeAPI.ExchangeEntities;
using ExchangeAPI.Model;
using GamesEngine.Exchange.Persistance;
using GamesEngine.Finance;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static GamesEngine.Finance.Currencies;
using Currency = GamesEngine.Finance.Currency;

namespace ExchangeAPI.Service
{
    public static class EntitiesMapperToDTO
    {
        public static VerifiedBatchesDTO MapToVerifiedbatchesDTO(this IEnumerable<VerifiedBatch> batches)
        {
            var verifiedDTO = new VerifiedBatchesDTO();
            foreach (var batch in batches)
            {
                var verifiedBatchDTO = batch.MapToVerifiedbatchDTO();
                verifiedDTO.Add(verifiedBatchDTO);
            }
            return verifiedDTO;
        }

        public static VerifyBatchDTO MapToVerifiedbatchDTO(this VerifiedBatch batch)
        {
            var verifiedBatchDTO = new VerifyBatchDTO()
            {
                Date = batch.Date.ToString("MM/dd/yyyy HH:mm:ss"),
                CurrencyCode = batch.CurrencyCode,
                Initial = Currency.Factory(batch.CurrencyCode, batch.Initial).ToDisplayFormat(),
                Available = Currency.Factory(batch.CurrencyCode, batch.Available).ToDisplayFormat(),
                Locked = Currency.Factory(batch.CurrencyCode, batch.Locked).ToDisplayFormat(),
                LockedAccumulated = Currency.Factory(batch.CurrencyCode, batch.LockedAccumulated).ToDisplayFormat(),
                SpendAccumulated = Currency.Factory(batch.CurrencyCode, batch.SpendAccumulated).ToDisplayFormat(),
                BatchNumber = batch.BatchNumber
            };

            return verifiedBatchDTO;
        }

        public static ExchangeRatesDTO MapToExchangeRatesDTO(this IEnumerable<Exchangerate> transfers)
        {
            var transfersDTO = new ExchangeRatesDTO();
            foreach (var transfer in transfers)
            {
                var transferDTO = transfer.MapToExchangeRateDTO();
                transfersDTO.Add(transferDTO);
            }
            return transfersDTO;
        }

        public static ExchangeRateDTO MapToExchangeRateDTO(this Exchangerate transfer)
        {
            Currency sale = Currency.Factory(transfer.Fromcurrency.Isocode, transfer.Saleprice);
            Currency purchase = Currency.Factory(transfer.Fromcurrency.Isocode, transfer.Purchaseprice);

            var transferDTO = new ExchangeRateDTO()
            {
                Date = transfer.Date.ToString("MM/dd/yyyy HH:mm:ss"),
                FromCurrencyCode = transfer.Fromcurrency.Isocode,
                ToCurrencyCode = transfer.Tocurrency.Isocode,
                PurchasePrice = purchase.Value,
                SalePrice = sale.Value,
                PurchasePriceFormatted = purchase.ToDisplayFormat(),
                SalePriceFormatted = sale.ToDisplayFormat(),
            };

            return transferDTO;
        }

        public static TransfersDTO MapToTransfersDTO(this IEnumerable<Transfer> transfers)
        {
            var transfersDTO = new TransfersDTO();
            foreach (var transfer in transfers)
            {
                var transferDTO = transfer.MapToTransferDTO();
                transfersDTO.Add(transferDTO);
            }
            return transfersDTO;
        }

        public static TransferDTO MapToTransferDTO(this Transfer transfer)
        {
            var salePrice = transfer.Exchangerate == null ? 0 : transfer.Exchangerate.Saleprice;
            Currency sale = Currency.Factory(transfer.Fromcurrency.Isocode, salePrice);

            var purchaseprice = transfer.Exchangerate == null ? 0 : transfer.Exchangerate.Purchaseprice;
            Currency purchase = Currency.Factory(transfer.Tocurrency.Isocode, purchaseprice);

            Currency amountReceived = Currency.Factory(transfer.Tocurrency.Isocode, transfer.Amount);
            Currency gross = Currency.Factory(transfer.Fromcurrency.Isocode, transfer.Gross);
            Currency net = Currency.Factory(transfer.Fromcurrency.Isocode, transfer.Net);
            Currency profit = Currency.Factory(transfer.Fromcurrency.Isocode, transfer.Profit);

            var transferDTO = new TransferDTO()
            {
                Date = transfer.Datecreated.ToString("MM/dd/yyyy HH:mm:ss"),
                Domain = transfer.DomainNavigation.Domain,
                Description = transfer.Description,
                FromCurrencyCode = transfer.Fromcurrency.Isocode,
                ToCurrencyCode = transfer.Tocurrency.Isocode,
                PurchasePrice = purchase.Value,
                SalePrice = sale.Value,
                PurchasePriceFormatted = purchase.ToDisplayFormat(),
                SalePriceFormatted = sale.ToDisplayFormat(),

                AmountReceived = amountReceived.Value,
                AmountCurrencyCode = transfer.Tocurrency.Isocode,
                AmountReceivedFormatted = amountReceived.ToDisplayFormat(),
                Gross = gross.Value,
                GrossCurrencyCode = transfer.Fromcurrency.Isocode,
                GrossFormatted = gross.ToDisplayFormat(),
                Net = net.Value,
                NetCurrencyCode = transfer.Fromcurrency.Isocode,
                NetFormatted = net.ToDisplayFormat(),
                Profit = profit.Value,
                ProfitCurrencyCode = transfer.Fromcurrency.Isocode,
                ProfitFormatted = profit.ToDisplayFormat(),

                Approvals = transfer.Approvals,
                ApprovalsRequired = transfer.Approvalsrequired,
                Rejections = transfer.Rejections,

                ID = transfer.Id.ToString(),
                AuthorizationId = transfer.Authorizationid,
                BatchNumber = transfer.Batchnumber.ToString(),
                FromCustomer = transfer.Fromcustomer,
                ToCustomer = transfer.Tocustomer,

                Account= transfer.Account,
                TargetAccount = transfer.Targetaccount,
                RealAccount = transfer.Realaccount,
                VoucherUrl = transfer.Voucherurl,
                RejectionReason = transfer.Rejectionreason == null ? string.Empty : transfer.Rejectionreason,
                WhoCreated = transfer.WhocreatedNavigation.Name,
                ProcessorAccountId = transfer.Processorid == null ? 0 : (int)transfer.Processorid.Value,
                JournalDetailEntryId = string.IsNullOrWhiteSpace(transfer.Journalentrydetailid) ? string.Empty : transfer.Journalentrydetailid
            };

            return transferDTO;
        }

        public static DepositsDTO MapToDepositsDTO(this IEnumerable<Deposit> deposits)
        {
            var depositsDTO = new DepositsDTO();
            foreach (var deposit in deposits)
            {
                var depositDTO = deposit.MapToDepositDTO();
                depositsDTO.Add(depositDTO);
            }
            return depositsDTO;
        }

        public static DepositDTO MapToDepositDTO(this Deposit deposit)
        {
            Currency amountReceived = Currency.Factory(deposit.Tocurrency.Isocode, deposit.Amount);
            Currency gross = Currency.Factory(deposit.Fromcurrency.Isocode, deposit.Gross);
            Currency net = Currency.Factory(deposit.Fromcurrency.Isocode, deposit.Net);
            Currency profit = Currency.Factory(deposit.Fromcurrency.Isocode, deposit.Profit);

            var depositDTO = new DepositDTO()
            {
                Date = deposit.Datecreated.ToString("MM/dd/yyyy HH:mm:ss"),
                Domain = deposit.DomainNavigation.Domain,
                Description = deposit.Description,
                ToCurrencyCode = deposit.Tocurrency.Isocode,

                AmountReceived = amountReceived.Value,
                AmountCurrencyCode = deposit.Tocurrency.Isocode,
                Gross = gross.Value,
                GrossCurrencyCode = deposit.Fromcurrency.Isocode,
                Net = net.Value,
                NetCurrencyCode = deposit.Fromcurrency.Isocode,
                Profit = profit.Value,
                ProfitCurrencyCode = deposit.Fromcurrency.Isocode,

                Approvals = deposit.Approvals,
                ApprovalsRequired = deposit.Approvalsrequired,
                Rejections = deposit.Rejections,

                ID = deposit.Id.ToString(),
                AuthorizationId = deposit.Authorizationid == null ? 0 : deposit.Authorizationid.Value,
                BatchNumber = deposit.Batchnumber.ToString(),
                ToCustomer = deposit.Tocustomer,

                Account = deposit.Account,
                VoucherUrl = deposit.Voucherurl,
                Voucher = deposit.Voucher,
                Depositor = deposit.Depositor,
                RejectionReason = deposit.Rejectionreason == null ? string.Empty : deposit.Rejectionreason,
                WhoCreated = deposit.WhocreatedNavigation.Name,
                ProcessorAccountId = deposit.Processorid == null ? 0 : (int)deposit.Processorid.Value,
                JournalDetailEntryId = string.IsNullOrWhiteSpace(deposit.Journalentrydetailid) ? string.Empty : deposit.Journalentrydetailid
            };

            return depositDTO;
        }

        public static WithdrawalsDTO MapToWithdrawalsDTO(this IEnumerable<Withdrawal> withdrawals)
        {
            var withdrawalsDTO = new WithdrawalsDTO();
            foreach (var withdrawal in withdrawals)
            {
                var withdrawalDTO = withdrawal.MapToWithdrawalDTO();
                withdrawalsDTO.Add(withdrawalDTO);
            }
            return withdrawalsDTO;
        }

        public static WithdrawalDTO MapToWithdrawalDTO(this Withdrawal withdrawal)
        {
            Currency amountReceived = Currency.Factory(withdrawal.Tocurrency.Isocode, withdrawal.Amount);
            Currency gross = Currency.Factory(withdrawal.Fromcurrency.Isocode, withdrawal.Gross);
            Currency net = Currency.Factory(withdrawal.Fromcurrency.Isocode, withdrawal.Net);
            Currency profit = Currency.Factory(withdrawal.Fromcurrency.Isocode, withdrawal.Profit);

            var withdrawalDTO = new WithdrawalDTO()
            {
                Date = withdrawal.Datecreated.ToString("MM/dd/yyyy HH:mm:ss"),
                Domain = withdrawal.DomainNavigation.Domain,
                Description = withdrawal.Description,
                FromCurrencyCode = withdrawal.Fromcurrency.Isocode,

                AmountReceived = amountReceived.Value,
                AmountCurrencyCode = withdrawal.Tocurrency.Isocode,
                Gross = gross.Value,
                GrossCurrencyCode = withdrawal.Fromcurrency.Isocode,
                Net = net.Value,
                NetCurrencyCode = withdrawal.Fromcurrency.Isocode,
                Profit = profit.Value,
                ProfitCurrencyCode = withdrawal.Fromcurrency.Isocode,
                MinerFee = withdrawal.Minerfee,

                Approvals = withdrawal.Approvals,
                ApprovalsRequired = withdrawal.Approvalsrequired,
                Rejections = withdrawal.Rejections,

                ID = withdrawal.Id.ToString(),
                AuthorizationId = withdrawal.Authorizationid,
                BatchNumber = withdrawal.Batchnumber.ToString(),
                FromCustomer = withdrawal.Fromcustomer,

                Account = withdrawal.Account,
                RealAccount = withdrawal.Realaccount,
                VoucherUrl = withdrawal.Voucherurl,
                RejectionReason = withdrawal.Rejectionreason == null ? string.Empty : withdrawal.Rejectionreason,
                WhoCreated = withdrawal.WhocreatedNavigation.Name,
                ProcessorAccountId = withdrawal.Processorid == null ? 0 : (int)withdrawal.Processorid.Value,
                JournalDetailEntryId = string.IsNullOrWhiteSpace(withdrawal.Journalentrydetailid) ? string.Empty : withdrawal.Journalentrydetailid
            };

            return withdrawalDTO;
        }

        public static DailyProfitsDTO MapToDailyProfitsDTO(this IEnumerable<DailyProfitDTO> dailyProfits, string currencyCode)
        {
            var dailyProfitsDTO = new DailyProfitsDTO(currencyCode);
            foreach (var dailyProfit in dailyProfits)
            {
                dailyProfitsDTO.Add(dailyProfit);
            }
            return dailyProfitsDTO;
        }

        public static ProfitableTransactionsDTO MapToProfitableTransactionsDTO(this IEnumerable<Profitabletransaction> transactions)
        {
            var transactionsDTO = new ProfitableTransactionsDTO();
            foreach (var transaction in transactions)
            {
                var transferDTO = transaction.MapToProfitableTransactionDTO();
                transactionsDTO.Add(transferDTO);
            }
            return transactionsDTO;
        }

        public static ProfitableTransactionDTO MapToProfitableTransactionDTO(this Profitabletransaction transaction)
        {
            Currency amountReceived = Currency.Factory(transaction.Tocurrency.Isocode, transaction.Amount);
            Currency gross = Currency.Factory(transaction.Fromcurrency.Isocode, transaction.Gross);
            Currency net = Currency.Factory(transaction.Fromcurrency.Isocode, transaction.Net);
            Currency profit = Currency.Factory(transaction.Fromcurrency.Isocode, transaction.Profit);

            var transactionDTO = new ProfitableTransactionDTO()
            {
                Date = transaction.Date.ToString("MM/dd/yyyy HH:mm:ss"),
                FromCurrencyCode = transaction.Fromcurrency.Isocode,
                ToCurrencyCode = transaction.Tocurrency.Isocode,

                AmountReceived = amountReceived.Value,
                AmountCurrencyCode = transaction.Tocurrency.Isocode,
                Gross = gross.Value,
                GrossCurrencyCode = transaction.Fromcurrency.Isocode,
                Net = net.Value,
                NetCurrencyCode = transaction.Fromcurrency.Isocode,
                Profit = profit.Value,
                ProfitCurrencyCode = transaction.Fromcurrency.Isocode,

                ID = transaction.Id.ToString(),
                BatchNumber = transaction.Batchnumber.ToString(),
                Type = transaction.Type,
                FromCustomer = transaction.Fromcustomer,
                ToCustomer = transaction.Tocustomer,
                Account = transaction.Account,
                TargetAccount = transaction.Targetaccount,
                WhoApproved = transaction.WhoapprovedNavigation.Name
            };

            return transactionDTO;
        }

        public static CreditNotesDTO MapToCreditNotesDTO(this IEnumerable<CreditNote> creditNotes)
        {
            var creditNotesDTO = new CreditNotesDTO();
            foreach (var creditNote in creditNotes)
            {
                var creditNoteDTO = creditNote.MapToCreditNoteDTO();
                creditNotesDTO.Add(creditNoteDTO);
            }
            return creditNotesDTO;
        }

        public static CreditNoteDTO MapToCreditNoteDTO(this CreditNote creditNote)
        {
            Currency amount = Currency.Factory(creditNote.Currency.Isocode, creditNote.Amount);

            var creditNoteDTO = new CreditNoteDTO()
            {
                Date = creditNote.Datecreated.ToString("MM/dd/yyyy HH:mm:ss"),
                Domain = creditNote.DomainNavigation.Domain,
                Description = creditNote.Description,
                CurrencyCode = creditNote.Currency.Isocode,
                AmountReceived = amount.Value,
                Approvals = creditNote.Approvals,
                ApprovalsRequired = creditNote.Approvalsrequired,
                Rejections = creditNote.Rejections,

                ReferenceId = creditNote.Referenceid.ToString(),
                ID = creditNote.Id.ToString(),
                BatchNumber = creditNote.Batchnumber.ToString(),
                Customer = creditNote.Customer,
                Account = creditNote.Account,
                VoucherUrl = creditNote.Voucherurl,
                RejectionReason = creditNote.Rejectionreason == null ? string.Empty : creditNote.Rejectionreason,
                WhoCreated = creditNote.WhocreatedNavigation.Name,
                ProcessorAccountId = creditNote.Processorid == null ? 0 : (int)creditNote.Processorid.Value,
                JournalDetailEntryId = string.IsNullOrWhiteSpace(creditNote.Journalentrydetailid) ? string.Empty : creditNote.Journalentrydetailid
            };

            foreach (var attachment in creditNote.CreditNoteAttachment)
            {
                creditNoteDTO.Attachments.Add(attachment.Url);
            }
            
            return creditNoteDTO;
        }

        public static DebitNotesDTO MapToDebitNotesDTO(this IEnumerable<DebitNote> debitNotes)
        {
            var debitNotesDTO = new DebitNotesDTO();
            foreach (var debitNote in debitNotes)
            {
                var debitNoteDTO = debitNote.MapToDebitNoteDTO();
                debitNotesDTO.Add(debitNoteDTO);
            }
            return debitNotesDTO;
        }

        public static DebitNoteDTO MapToDebitNoteDTO(this DebitNote debitNote)
        {
            Currency amount = Currency.Factory(debitNote.Currency.Isocode, debitNote.Amount);

            var debitNoteDTO = new DebitNoteDTO()
            {
                Date = debitNote.Datecreated.ToString("MM/dd/yyyy HH:mm:ss"),
                Domain = debitNote.DomainNavigation.Domain,
                Description = debitNote.Description,
                CurrencyCode = debitNote.Currency.Isocode,
                AmountReceived = amount.Value,
                Approvals = debitNote.Approvals,
                ApprovalsRequired = debitNote.Approvalsrequired,
                Rejections = debitNote.Rejections,

                ReferenceId = debitNote.Referenceid.ToString(),
                ID = debitNote.Id.ToString(),
                BatchNumber = debitNote.Batchnumber.ToString(),
                Customer = debitNote.Customer,
                Account = debitNote.Account,
                VoucherUrl = debitNote.Voucherurl,
                RejectionReason = debitNote.Rejectionreason == null ? string.Empty : debitNote.Rejectionreason,
                WhoCreated = debitNote.WhocreatedNavigation.Name,
                ProcessorAccountId = debitNote.Processorid == null ? 0 : (int)debitNote.Processorid.Value,
                JournalDetailEntryId = string.IsNullOrWhiteSpace(debitNote.Journalentrydetailid) ? string.Empty : debitNote.Journalentrydetailid
            };

            foreach (var attachment in debitNote.DebitNoteAttachment)
            {
                debitNoteDTO.Attachments.Add(attachment.Url);
            }

            return debitNoteDTO;
        }

        public static TransactionDTO MapToTransactionDTO(this Deposit deposit)
        {
            var transaction = new TransactionDTO()
            {
                Date = deposit.Datecreated,
                Description = deposit.Description,
                IdentificationNumber = deposit.Tocustomer,
                TransactionId = deposit.Id.ToString(),
                Approvals = deposit.Approvals,
                ApprovalsRequired = deposit.Approvalsrequired,
                Rejections = deposit.Rejections,
                VoucherUrl = deposit.Voucherurl,
                WhoCreated = deposit.WhocreatedNavigation.Name
            };
            return transaction;
        }

        public static TransactionDTO MapToTransactionDTO(this Withdrawal withdrawal)
        {
            var transaction = new TransactionDTO()
            {
                Date = withdrawal.Datecreated,
                Description = withdrawal.Description,
                IdentificationNumber = withdrawal.Fromcustomer,
                TransactionId = withdrawal.Id.ToString(),
                Approvals = withdrawal.Approvals,
                ApprovalsRequired = withdrawal.Approvalsrequired,
                Rejections = withdrawal.Rejections,
                VoucherUrl = withdrawal.Voucherurl,
                WhoCreated = withdrawal.WhocreatedNavigation.Name
            };
            return transaction;
        }

        public static TransactionDTO MapToTransactionDTO(this Transfer transfer)
        {
            var transaction = new TransactionDTO()
            {
                Date = transfer.Datecreated,
                Description = transfer.Description,
                IdentificationNumber = transfer.Fromcustomer,
                TransactionId = transfer.Id.ToString(),
                Approvals = transfer.Approvals,
                ApprovalsRequired = transfer.Approvalsrequired,
                Rejections = transfer.Rejections,
                VoucherUrl = transfer.Voucherurl,
                WhoCreated = transfer.WhocreatedNavigation.Name
            };
            return transaction;
        }

        public static HistoricalTransactionDTO MapToHistoricalTransactionDTO(this Deposit deposit)
        {
            var depositCoin = Coinage.GetById(deposit.Tocurrencyid);
            var transaction = new HistoricalTransactionDTO()
            {
                Date = deposit.Datecreated,
                Description = deposit.Description,
                TransactionId = deposit.Id.ToString(),
                Approvals = deposit.Approvals,
                ApprovalsRequired = deposit.Approvalsrequired,
                Rejections = deposit.Rejections,

                AmountToDebitCurrencyCode = depositCoin.Iso4217Code,
                AmountToCreditCurrencyCode = depositCoin.Iso4217Code,
                AmountToCredit = deposit.Amount,
                AccountToCredit = deposit.Account,
                Type = deposit.GetType().Name,
                IsCreditForFilteredCurrency = true
            };
            return transaction;
        }

        public static HistoricalTransactionDTO MapToHistoricalTransactionDTO(this Withdrawal withdrawal)
        {
            var withdrawalCoin = Coinage.GetById(withdrawal.Fromcurrencyid);
            var transaction = new HistoricalTransactionDTO()
            {
                Date = withdrawal.Datecreated,
                Description = withdrawal.Description,
                TransactionId = withdrawal.Id.ToString(),
                Approvals = withdrawal.Approvals,
                ApprovalsRequired = withdrawal.Approvalsrequired,
                Rejections = withdrawal.Rejections,

                AmountToDebitCurrencyCode = withdrawalCoin.Iso4217Code,
                AmountToDebit = withdrawal.Amount,
                AmountToCreditCurrencyCode = withdrawalCoin.Iso4217Code,
                AccountToDebit = withdrawal.Account,
                Type = withdrawal.GetType().Name,
                IsCreditForFilteredCurrency = false
            };
            return transaction;
        }

        public static HistoricalTransactionDTO MapToHistoricalTransactionDTO(this Transfer transfer, string identificationNumber, string currencyCode)
        {
            var creditCoin = Coinage.GetById(transfer.Tocurrencyid);
            var debitCoin = Coinage.GetById(transfer.Fromcurrencyid);
            var transaction = new HistoricalTransactionDTO()
            {
                Date = transfer.Datecreated,
                Description = transfer.Description,
                TransactionId = transfer.Id.ToString(),
                Approvals = transfer.Approvals,
                ApprovalsRequired = transfer.Approvalsrequired,
                Rejections = transfer.Rejections,

                AmountToDebitCurrencyCode = debitCoin.Iso4217Code,
                AmountToDebit = transfer.Gross,
                AccountToDebit = transfer.Account,
                AmountToCreditCurrencyCode = creditCoin.Iso4217Code,
                AmountToCredit = transfer.Amount,
                AccountToCredit = transfer.Targetaccount,
                Type = transfer.GetType().Name,
                IsCreditForFilteredCurrency = currencyCode == "all" ? (bool?)null : transfer.Tocustomer == identificationNumber && creditCoin.Iso4217Code == currencyCode
            };
            return transaction;
        }

        public static HistoricalTransactionDTO MapToHistoricalTransactionDTO(this CreditNote creditNote)
        {
            var creditCoin = Coinage.GetById(creditNote.Currencyid);
            var transaction = new HistoricalTransactionDTO()
            {
                Date = creditNote.Datecreated,
                Description = creditNote.Description,
                TransactionId = creditNote.Id.ToString(),
                Approvals = creditNote.Approvals,
                ApprovalsRequired = creditNote.Approvalsrequired,
                Rejections = creditNote.Rejections,

                AmountToDebitCurrencyCode = creditCoin.Iso4217Code,
                AmountToCreditCurrencyCode = creditCoin.Iso4217Code,
                AmountToCredit = creditNote.Amount,
                AccountToCredit = creditNote.Account,
                Type = creditNote.GetType().Name,
                IsCreditForFilteredCurrency = true
            };
            return transaction;
        }

        public static HistoricalTransactionDTO MapToHistoricalTransactionDTO(this DebitNote debitNote)
        {
            var debitCoin = Coinage.GetById(debitNote.Currencyid);
            var transaction = new HistoricalTransactionDTO()
            {
                Date = debitNote.Datecreated,
                Description = debitNote.Description,
                TransactionId = debitNote.Id.ToString(),
                Approvals = debitNote.Approvals,
                ApprovalsRequired = debitNote.Approvalsrequired,
                Rejections = debitNote.Rejections,

                AmountToDebitCurrencyCode = debitCoin.Iso4217Code,
                AmountToDebit = debitNote.Amount,
                AmountToCreditCurrencyCode = debitCoin.Iso4217Code,
                AccountToDebit = debitNote.Account,
                Type = debitNote.GetType().Name,
                IsCreditForFilteredCurrency = false
            };
            return transaction;
        }

        public static HistoricalTransactionsDTO MapToHistoricalTransactionsDTO(this IEnumerable<HistoricalTransactionDTO> creditNotes)
        {
            var creditNotesDTO = new HistoricalTransactionsDTO();
            foreach (var creditNote in creditNotes)
            {
                creditNotesDTO.Add(creditNote);
            }
            return creditNotesDTO;
        }

        public static JournalEntriesDTO MapToJournalEntriesDTO(this IEnumerable<Journalentry> journalEntries)
        {
            var journalEntriesDTO = new JournalEntriesDTO();
            foreach (var journalEntry in journalEntries)
            {
                var journalEntryDTO = journalEntry.MapToJournalEntryDTO();
                journalEntriesDTO.Add(journalEntryDTO);
            }
            return journalEntriesDTO;
        }

        public static JournalEntryDTO MapToJournalEntryDTO(this Journalentry journalEntry)
        {
            if (journalEntry.WhocreatedNavigation == null) throw new ArgumentNullException(nameof(journalEntry.WhocreatedNavigation));

            var journalEntryDTO = new JournalEntryDTO()
            {
                Date = journalEntry.Date.ToString("MM/dd/yyyy HH:mm:ss"),
                Title = journalEntry.Title,
                EntryId = journalEntry.Journalentrydetailid,
                Reference = journalEntry.Reference,
                WhoCreated = journalEntry.WhocreatedNavigation.Name
            };

            foreach (var JournalEntryDetails in journalEntry.Journalentrydetails)
            {
                var detail = JournalEntryDetails.MapToJournalEntryDetailDTO();
                journalEntryDTO.Add(detail);
            }
            return journalEntryDTO;
        }

        public static JournalEntryDetailDTO MapToJournalEntryDetailDTO(this Journalentrydetails detail)
        {
            var journalEntryDetailDTO = new JournalEntryDetailDTO()
            {
                Account = detail.Account,
                Description = detail.Description,
                Sequence = detail.Sequence,
                Credit = detail.Credit ?? 0m,
                Debit = detail.Debit ?? 0m,
            };

            return journalEntryDetailDTO;
        }
    }
}
