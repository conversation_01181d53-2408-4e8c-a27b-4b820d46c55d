﻿using GamesEngine.Finance;
using System;
using System.Collections.Generic;
using System.Linq;
using static GamesEngine.Exchange.FloatingExchangeRate;

namespace GamesEngine.Exchange
{
	internal class ConversionSpreads
	{
		private readonly ExchangeRates exchangeRates;
		private readonly Dictionary<string, ConversionSpreadVersions> conversionSpreadByVersion = new Dictionary<string, ConversionSpreadVersions>();
		private readonly Dictionary<string, SpecialConversionSpread> specialConversionSpreadByName = new Dictionary<string, SpecialConversionSpread>();

		public ConversionSpreads(ExchangeRates exchangeRates)
		{
			if (exchangeRates == null) throw new ArgumentNullException(nameof(exchangeRates));
			this.exchangeRates = exchangeRates;
		}

		internal void Add(RegularConversionSpread conversionSpread)
		{
			if (conversionSpread == null) throw new ArgumentNullException(nameof(conversionSpread));

			string defaultCurrencyDirectionKey = ConversionSpreadVersions.CalculateKey(conversionSpread);
			ConversionSpreadVersions conversionSpreadVersions = null;

			if (!conversionSpreadByVersion.TryGetValue(defaultCurrencyDirectionKey, out conversionSpreadVersions))
			{
				conversionSpreadVersions = new ConversionSpreadVersions();
				conversionSpreadByVersion.Add(defaultCurrencyDirectionKey, conversionSpreadVersions);
			}

			conversionSpreadVersions.Add(conversionSpread);

			PruneUnUsedConversionSpreadRates();
		}

		internal void Add(SpecialConversionSpread conversionSpread)
		{
			string key = ConversionSpreadVersions.GenerateIdentationKey(conversionSpread);

			specialConversionSpreadByName.Add(key, conversionSpread);
		}

		internal IConversionSpread SearchSpreadFor(DateTime date, Coin source, Coin target)
		{
			string defaultCurrencyDirectionKey = ConversionSpreadVersions.CalculateKey(source, target);

			ConversionSpreadVersions conversionSpreadVersion = null;
			conversionSpreadByVersion.TryGetValue(defaultCurrencyDirectionKey, out conversionSpreadVersion);

			if (conversionSpreadVersion == null)
			{
				return NoConversionSpread.GetInstance();
			}

			IConversionSpread result = conversionSpreadVersion.At(date);
			return result;
		}

		internal void PruneUnUsedRates()
		{
			PruneUnUsedConversionSpreadRates();
			PruneUnUsedSpecialConversionSpreadRates();
		}

		private void PruneUnUsedConversionSpreadRates()
		{
			foreach (ConversionSpreadVersions spread in conversionSpreadByVersion.Values)
			{
				spread.PruneUnUsedRates();
			}
		}

		internal void PruneUnUsedSpecialConversionSpreadRates()
		{
			var unModifiedCollection = specialConversionSpreadByName.Values.ToArray();
			foreach (SpecialConversionSpread spread in unModifiedCollection)
			{
				if (spread.TransactionsUsingIt() == 0)
				{
					string key = ConversionSpreadVersions.GenerateIdentationKey(spread);
					specialConversionSpreadByName.Remove(key);
				}

			}
		}

		internal int AmountOfRatesInMemory()
		{
			int result = 0;

			foreach (ConversionSpreadVersions spread in conversionSpreadByVersion.Values.ToArray() )
			{
				result += spread.AmountOfRatesInMemory();
			}
			
			result += specialConversionSpreadByName.Count();
			
			return result;
		}

		internal IConversionSpread At(DateTime date, Coin source, Coin target)
		{
			IConversionSpread conversionSpread= SearchSpreadFor(date, source, target);

			if (conversionSpread == null)
			{
				throw new GameEngineException($"{nameof(conversionSpreadByVersion)} must have key for ( {source} / {target} ) and ( {target} / {source} ) first.");
			}

			return conversionSpread;
		}

		private class ConversionSpreadVersions
		{
			private List<ConversionSpread> conversionSpreadsDescendingbyDate;
			internal static string CalculateKey(ConversionSpread conversionSpread)
			{
				return CalculateKey(conversionSpread.PurchaseRate.Source, conversionSpread.PurchaseRate.Target);
			}

			internal static string GenerateIdentationKey(SpecialConversionSpread conversionSpread)
			{
				return conversionSpread.Name;
			}
			internal static string CalculateKey(Coin quotedCurrencyCode, Coin baseCurrencyCode)
			{
				string defaultCurrencyDirectionKey = quotedCurrencyCode.Iso4217Code + baseCurrencyCode.Iso4217Code;
				return defaultCurrencyDirectionKey;
			}

			internal void Add(ConversionSpread conversionSpread)
			{
				if (conversionSpreadsDescendingbyDate == null)
				{
					conversionSpreadsDescendingbyDate = new List<ConversionSpread>();
					conversionSpreadsDescendingbyDate.Add(conversionSpread);
				}
				else
				{
					ConversionSpread lastConversionSpread = conversionSpreadsDescendingbyDate[conversionSpreadsDescendingbyDate.Count - 1];

					if (lastConversionSpread.Date > conversionSpread.Date) throw new GameEngineException($"It's not possible to insert a conversion spread before the last one registered in {lastConversionSpread.Date} which it's  already active.");

					lastConversionSpread.MarkAsNoActive();
					conversionSpreadsDescendingbyDate.Add(conversionSpread);
				}

				conversionSpread.MarkAsActive();
			}

			internal ConversionSpread At(DateTime date)
			{
				if (conversionSpreadsDescendingbyDate == null) throw new GameEngineException("There is no any conversion spread already registered.");

				for (int i = conversionSpreadsDescendingbyDate.Count-1; i >= 0; i-- )
				{
					ConversionSpread currentSpread = conversionSpreadsDescendingbyDate[i];
					if (date >= currentSpread.Date) return currentSpread;
				}

				throw new GameEngineException($"Date {date} is not later than any conversion spread already registered.");
			}

			internal int AmountOfRatesInMemory()
			{
				int result = conversionSpreadsDescendingbyDate.Count();

				return result;
			}

			internal void PruneUnUsedRates()
			{
				for (int i = conversionSpreadsDescendingbyDate.Count - 1; i >= 0; i--)
				{
					ConversionSpread currentSpread = conversionSpreadsDescendingbyDate[i];
					bool itsTheLastOne = i == conversionSpreadsDescendingbyDate.Count - 1;

					if (!itsTheLastOne && currentSpread.TransactionsUsingIt() == 0)
					{
						conversionSpreadsDescendingbyDate.RemoveAt(i);
					}
				}
			}
		}

	}
}