﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Linq.Expressions;
using System.Text;
using System.Runtime.CompilerServices;
using System.Linq;
using System.Collections;
using System.Reflection;
using static Bis.RiskIndex;

[assembly: InternalsVisibleTo("BisUT")]
namespace Bis
{

	class Program
	{
		static void Main(String[] arguments)
		{

			//Total(Amount) < 150 * 2 && Amount < 10 && Account is "abc" 
			//Customer.Account is 'abc'
			//Customer.Account in ('abc', '123', '5D343245')


			RiskIndex onPurchase = new RiskIndex("onPurchase");

			//Definicion de los campos del indice
			onPurchase
				.IndexOn("account", typeof(string))
				.IndexOn("amount", typeof(decimal));

			//Se crea una regla. When account is 'abc123' AND amount < 100 then Allow
			RiskRule aRule = new RiskRule
			(
				1,
				new RiskPredicate[]
				{
					new RiskPredicate(
						new IsOp(
							onPurchase.Variable("account"),
							new StringLiteral("abc123")
						)
					),
					new RiskPredicate(
						new LowerOrEqualOp(
							onPurchase.Variable("amount"),
							new IntLiteral(100)
						)
					)
				},
				RiskAction.Allow
			);

			//Agregar reglas
			onPurchase.Include(aRule);

			// Buscar algo en el indice

			//Quitar reglas
		//	onPurchase.Reset(aRule.RuleId);
		}
	}

	//Indice y los nodos del arbol 
	internal class RiskIndex
	{
		private RiskExpression root;
		private Func<Result> executable;
		private Dictionary<string, Type> fields;
		private Dictionary<int, RiskRule> rules;
		private Dictionary<string, Variable> variables;

		internal int AmountOfIndexes { get { return (fields == null) ? 0 : fields.Count; } }
		internal string Name { get; }
		internal int AmountOfRules { get; private set; }

		internal Dictionary<IEnumerable, Accumulator> IndexedAccumulators 
		{
			get
			{
				return (root as HashOp).IndexedAccumulators;
			} 
		}

		internal IEnumerable[] IndexedAccumulatorsKeys
		{
			get
			{
				return IndexedAccumulators.Keys.ToArray();
			}
		}

		internal Accumulator IndexedAccumulatorsByKey(IEnumerable[] enumerables)
		{
			Accumulator result;
			(root as HashOp).TryToSearchADictionaryWithThisDimension(enumerables, out result);
			return result;
		}

		private Dictionary<string, Accumulator> definedAccumulators;

		private Parameters parameters;
		internal RiskIndex(string name)
		{
			if (string.IsNullOrEmpty(name)) throw new ArgumentException(nameof(name));

			Name = name;
			root = Allow_OP.ALLOW;
			fields = new Dictionary<string, Type>();
			rules = new Dictionary<int, RiskRule>();
			parameters = new Parameters();
			variables = new Dictionary<string, Variable>();
			definedAccumulators = new Dictionary<string, Accumulator>();
		}

		internal RiskIndex IndexOn(string fieldName, Type fieldType)
		{
			if (string.IsNullOrEmpty(fieldName)) throw new ArgumentException(nameof(fieldName));
			if (fieldType == null) throw new ArgumentException(nameof(fieldType));

			fieldName = fieldName.Trim().ToLower();
			if (fields.ContainsKey(fieldName)) throw new GameEngineException($"{fieldName} already exists.");

			fields.Add(fieldName, fieldType);
			
			var variable = new Variable(parameters, fieldType, fieldName);
			variables.Add(fieldName, variable);

			if (fieldType == typeof(int) || fieldType == typeof(decimal))
			{
				variable.Accumulator = new Accumulator(parameters, fieldType, fieldName);
				definedAccumulators.Add(variable.Name, variable.Accumulator);
			}

			return this;
		}

		internal void Include(RiskRule rule)
		{
			if (rule == null) throw new ArgumentException(nameof(rule));
			if (rules.ContainsKey(rule.RuleId)) throw new GameEngineException($"The rule id {rule.RuleId} it's already defined.");

			IEnumerable<Variable> variables = rule.Variables();
			foreach (Variable variable in variables)
			{
				string name = variable.Name.Trim().ToLower();

				Type indexType;
				bool exists = fields.TryGetValue(name, out indexType);
				if (!exists) throw new GameEngineException($"The {nameof(RiskRule)} with {nameof(RiskPredicate)} named {variable.Name} it's no valid  for {nameof(RiskIndex)} named {Name}.");

				variable.Type = indexType;
			}

			rule.ValidateTypes();

			rules.Add(rule.RuleId, rule);

			if (!(root is HashOp) && !rule.HasIsOp())
			{
				root = new IterateRuleOp(root, rule); //TODO cris preguntar a alvaro donde poner estos acumulator para quitar los del indice
			}
			else if (root is HashOp && !rule.HasIsOp())
			{
				root = (root as HashOp).AddRuleWithoutIsOp(rule);
				(root as HashOp).ProcessRule(rule);
			}
			else if (!(root is HashOp) && rule.HasIsOp())
			{
				root = new HashOp(parameters, root, rule);
				(root as HashOp).ProcessRule(rule);
			}
			else if (root is HashOp && rule.HasIsOp())
			{
				root = (root as HashOp).AddRulewithIsOp(rule);
				(root as HashOp).ProcessRule(rule);
			}
			else
			{
				throw new GameEngineException($"Type {root.GetType().Name} and rule has IsOperator {rule.HasIsOp()} is not supported yet.");
			}

			AmountOfRules++;
			executable = null;
		}

		internal void Reset()
		{
			root = Allow_OP.ALLOW;
			AmountOfRules = 0;
			rules = new Dictionary<int, RiskRule>();
			executable = null;
		}

		internal void LoadParameters(Parameters userParameters)
		{
			if (userParameters == null) throw new ArgumentException(nameof(userParameters));

			foreach (Parameter userParameter in userParameters)
			{
				Type filed;
				if (!fields.TryGetValue(userParameter.Name, out filed)) continue;

				bool hasTheSameType = filed == userParameter.ValueType;
				if (!hasTheSameType)
				{
					if (fields[userParameter.Name] == typeof(int) &&
								userParameter.ValueType == typeof(decimal) &&
								(decimal)userParameter.GetValue() % 1 == 0)
						continue;
					else if (fields[userParameter.Name] == typeof(decimal) &&
						userParameter.ValueType == typeof(int))
						continue;
					else
						throw new GameEngineException($"The parameter {userParameter.Name} is a {userParameter.ValueType} when {fields[userParameter.Name]} is expected.");
				}
			}

			foreach (Parameter userParameter in userParameters)
			{
				parameters[userParameter.Name] = userParameter.GetValue();
			}
		}
		internal Result Evaluate(Parameters userParameters)
		{
			LoadParameters(userParameters);

			if (executable == null)
			{
				var program = root.Evaluate;
				executable = Expression.Lambda<Func<Result>>(program).Compile();
			}

			Result result = executable();

			if (result != Result.ALLOW)
			{
				bool ValidateIfAllDimensionesArePresentInTheParameterRequired = true;

				IEnumerable<Accumulator> accumulatorsToAccumulate = Enumerable.Empty<Accumulator>();
				IEnumerable<object> dimensions = null;
				if (root is HashOp)
				{
					accumulatorsToAccumulate = (root as HashOp).IndexedAccumulators.Values;
					dimensions = (root as HashOp).IndexedAccumulators.Keys;

				}
				else if (root is IterateRuleOp)
				{
					accumulatorsToAccumulate = definedAccumulators.Values.ToArray();
					ValidateIfAllDimensionesArePresentInTheParameterRequired = false;
				}
				else throw new GameEngineException($"{root.GetType()} is not supported yet.");

				if (!ValidateIfAllDimensionesArePresentInTheParameterRequired)
				{
					foreach (Accumulator accumulator in accumulatorsToAccumulate)
					{
						accumulator.Accumule();
					}
				}
				else
				{
					foreach (KeyValuePair<IEnumerable, Accumulator> keyPair in (root as HashOp).IndexedAccumulators)
					{
						var dimension = keyPair.Key;
						var accumulator = keyPair.Value;
						if (parameters.HaveAllValues(dimension)) 
						{
							accumulator.Accumule();
						}
					}
				}
				
			}

			parameters.Clear();


			return result;
		}

		internal RiskExpression Variable(string name)
		{
			name = name.Trim().ToLower();
			return variables[name];
		}

		internal Accumulator Total(string name)
		{
			return definedAccumulators[name];
		}

		internal class Result
		{

			internal static Result ALLOW = new Result(0);
			internal static Expression ALLOW_EXP=  System.Linq.Expressions.Expression.Constant(Result.ALLOW);

			internal static Result UNMATCHED = new Result(-1);
			internal static Expression UNMATCHED_EXP = System.Linq.Expressions.Expression.Constant(Result.UNMATCHED);

			private Result(int ruleId)
			{
				RuleId = ruleId;
				Action = RiskAction.Allow;
			}

			public Result(int ruleId, RiskAction action)
			{
				RuleId = ruleId;
				Action = action;
			}

			internal int RuleId { get; }
			internal RiskAction Action { get; }

			public override string ToString()
			{
				return $"{RuleId}-{Action.ToString()}";
			}
		}
	}

	public sealed class Parameters : IEnumerable<Parameter>
	{
		private readonly List<Parameter> parameters = new List<Parameter>();

		internal bool HaveAllValues(object values)
		{
			if (values.GetType().IsArray)
			{
				IEnumerable<object> ivalues = (IEnumerable<object>)values;

				foreach (object value in ivalues)
				{
					var result = HaveParameterValue(value);
					if (!result) return result;
				}

				return true;
			}
			else 
			{
				return HaveParameterValue(values);
			}
		}

		internal bool HaveParameter(string parameterName)
		{
			if (String.IsNullOrWhiteSpace(parameterName)) throw new ArgumentNullException(nameof(parameterName));
			var nameStd = parameterName.Trim().ToLower();
			var result = parameters.Exists(p => p.Name == nameStd);
			return result;
		}

		internal bool HaveParameterValue(object parameterValue)
		{
			if (parameterValue==null) throw new ArgumentNullException(nameof(parameterValue));

			var result = parameters.Exists(p => p.GetValue()==parameterValue);
			return result;
		}

		internal bool AreAllSet()
		{
			var result = !this.parameters.Any(p => p.IsEmpty);
			return result;
		}
		internal int Count()
		{
			return parameters.Count();
		}
		public IEnumerator<Parameter> GetEnumerator() => parameters.GetEnumerator();

		IEnumerator IEnumerable.GetEnumerator() => parameters.GetEnumerator();

		public object this[string parameterName]
		{
			set
			{
				if (String.IsNullOrWhiteSpace(parameterName)) throw new ArgumentNullException(nameof(parameterName));
				var nameStd = parameterName.Trim().ToLower();
				var parameter = parameters.FirstOrDefault(p => p.Name == nameStd);
				if (parameter == null)
				{
					parameter = new Parameter(nameStd);
					parameters.Add(parameter);
				}
				parameter.Value = value;
			}
		}

		internal void Clear()
		{
			parameters.ForEach(p => p.Clear());
		}

		public int FieldAsInt(string variableName)
		{
			foreach (Parameter p in parameters)
			{
				if (p.Name == variableName)
				{
					if (p.GetValue() is decimal)
					{
						return Convert.ToInt32(p.GetValue());
					}
					else
					{
						return (int)p.GetValue();
					}
				}
			}
			throw new GameEngineException($"There is no variable named '{variableName}' with type {typeof(int)}.");
		}
		public string FieldAsString(string variableName)
		{
			foreach (Parameter p in parameters)
			{
				if (p.Name == variableName)
				{
					return (string)p.GetValue();
				}
			}
			throw new GameEngineException($"There is no variable named '{variableName}' with type {typeof(string)}.");
		}
		public DateTime FieldAsDateTime(string variableName)
		{
			foreach (Parameter p in parameters)
			{
				if (p.Name == variableName)
				{
					return (DateTime)p.GetValue();
				}
			}
			throw new GameEngineException($"There is no variable named '{variableName}' with type {typeof(DateTime)}.");
		}
		public bool FieldAsBoolean(string variableName)
		{
			foreach (Parameter p in parameters)
			{
				if (p.Name == variableName )
				{
					return (bool)p.GetValue();
				}
			}
			throw new GameEngineException($"There is no variable named '{variableName}' with type {typeof(bool)}.");
		}
		public decimal FieldAsDecimal(string variableName)
		{
			foreach (Parameter p in parameters)
			{
				if (p.Name == variableName)
				{
					if (p.GetValue() is int)
					{
						return (int)p.GetValue();
					}
					else 
					{
						return (decimal)p.GetValue();
					}
				}
			}
			throw new GameEngineException($"There is no variable named '{variableName}' with type {typeof(decimal)}.");
		}

		internal Parameter At(int position)
		{
			return parameters[position];
		}
	}

	public sealed class Parameter
	{
		private object instance = null;
		private readonly string name;

		internal Parameter(string name)
		{
			if (name == null) throw new ArgumentNullException(nameof(name));
			this.name = name.Trim().ToLower();
		}

		internal string Name => this.name;

		internal bool IsEmpty => this.instance == null;

		public object Value
		{
			set
			{
				Type tipoArgumento = value.GetType();

				if (tipoArgumento.IsArray)
				{
					var elementType = tipoArgumento.GetElementType();
					var CastArregloType = typeof(IEnumerable<>).MakeGenericType(new[] { elementType });
					instance = Convert.ChangeType(value, CastArregloType);
				}
				else
				{
					instance = value;
				}
			}
		}

		internal Type ValueType { get { return GetValue().GetType(); } }

		public bool IsNumeric { get { return instance is int || instance is decimal; } }

		internal object GetValue()
		{
			if (instance == null) throw new ArgumentException($"Parameter {this.name} has not been set");
			return instance;
		}

		internal void Clear()
		{
			instance = null;
		}
	}

	internal class RiskRule : RiskExpression
	{
		private readonly int ruleId;
		private readonly RiskPredicate[] predicates;
		private RiskAction action;

		internal static RiskRule ALLOW_RULE = new RiskRule();
		private RiskRule()
		{
			this.ruleId = 0;
			this.predicates = new RiskPredicate[]
				{
					new RiskPredicate(BoolLiteral.TRUE)
				};
			this.action = RiskAction.Allow;
		}

		internal RiskRule(int ruleId, RiskPredicate[] predicates, RiskAction action)
		{
			if (ruleId <=0 ) throw new GameEngineException($"{nameof(ruleId)} must be greater than 0");
			if (predicates == null) throw new ArgumentException(nameof(predicates));
			if (predicates.Length == 0) throw new ArgumentException(nameof(predicates));

			this.ruleId = ruleId;
			this.predicates = predicates;
			this.action = action;
		}

		internal int AmountOfPredicates { get { return predicates.Length; } }

		internal int RuleId
		{
			get
			{
				return this.ruleId;
			}
		}

		internal RiskAction Action
		{
			get
			{
				return this.action;
			}
		}
		
		internal bool HasIsOp()
		{
			IEnumerable<IsOp> isOps = AllIsOps();
			return isOps.Count() != 0;
		}

		internal override void ValidateTypes()
		{
			foreach (RiskPredicate predicate in predicates)
			{
				predicate.ValidateTypes();
			}
		}
		private bool isEnabled=true;
		internal void Disable()
		{
			isEnabled = false;
		}

		internal void Enable()
		{
			isEnabled = true;
		}

		internal override IEnumerable<RiskExpression> Children
		{
			get
			{
				IEnumerable<RiskExpression> result = new[] { this };
				foreach (RiskPredicate p in predicates)
				{
					result = result.Concat(p.Children);
				}
				return result;
			}
		}

		internal RiskPredicate[] Predicates 
		{
			get 
			{
				return predicates;
			}
		}
		private Func<Result> executable;
		internal Result Value
		{
			get
			{
				//if (executable == null)
				//{
					RiskPredicate[] predicatesArr = predicates.ToArray();
					RiskExpression predicatesExp;
					if (predicates.Length == 1)
					{
						predicatesExp = predicatesArr[0];
					}
					else
					{
						predicatesExp = new AndPredicateOp(predicatesArr[0], predicatesArr[1]);
						for (int i = 2; i < predicates.Length; i++)
						{
							predicatesExp = new AndPredicateOp(predicatesExp, predicatesArr[i]);
						}
					}

					predicatesExp = new AndPredicateOp((isEnabled)? BoolLiteral.TRUE: BoolLiteral.FALSE, predicatesExp);

					var ruleIdExpression = Expression.Constant(RuleId);
					var ruleaActionExpression = Expression.Constant(Action);
					var resultCtor = typeof(Result).GetConstructor(new Type[] { typeof(int), typeof(RiskAction) });
					var resultValueExpression = Expression.New(resultCtor, ruleIdExpression, ruleaActionExpression);
					var block = Expression.Condition(
						predicatesExp.Evaluate,
						resultValueExpression,
						Result.UNMATCHED_EXP
					);

					var lambda = Expression.Lambda(block);
					var result = Expression.Invoke(lambda);
					

					var programa = Expression.Block(lambda, result);
					var programaWithLogs = Accumulator.Traza(programa, typeof(RiskRule), predicatesExp.Evaluate, resultValueExpression);

					executable = Expression.Lambda<Func<Result>>(programaWithLogs).Compile();
				//}

				return executable();
			}
		
		}
		internal override Expression Evaluate
		{
			get
			{
				return Expression.Property(
						Expression.Constant(this),
						typeof(RiskRule).GetProperty("Value", BindingFlags.Instance | BindingFlags.NonPublic)
						);
			}
		}
	}

	enum RiskAction
	{
		Allow,
		Deny
	}

	internal class RiskPredicate : RiskExpression
	{
		private RiskExpression expression;

		internal RiskPredicate(RiskExpression expression)
		{
			if (expression == null) throw new ArgumentException(nameof(expression));
			this.expression = expression;
		}

		internal override Expression Evaluate
		{
			get
			{
				return expression.Evaluate;
			}
		}

		internal override IEnumerable<RiskExpression> Children
		{
			get
			{
				IEnumerable<RiskExpression> result = new[] { this };
				result = result.Concat(expression.Children);
				return result;
			}
		}

		internal override void ValidateTypes()
		{
			expression.ValidateTypes();
		}
	}

	abstract class RiskExpression
	{
		internal abstract IEnumerable<RiskExpression> Children { get; }
		internal abstract void ValidateTypes();
		internal Type Type { get; set; }
		internal abstract Expression Evaluate  { get; }
		internal IEnumerable<IsOp> AllIsOps()
		{
			var result = Children.Where(child => child is IsOp).Cast<IsOp>();

			return result;
		}
		internal IEnumerable<Accumulator> AllAccumulators()
		{
			var result = Children.Where(child => child is Accumulator).Cast<Accumulator>();

			return result;
		}
		internal IEnumerable<Variable> Variables()
		{
			var result = Children.Where(child => child is Variable).Cast<Variable>();
			return result;
		}
	}

	class AndPredicateOp : RiskExpression
	{
		private readonly RiskExpression leftExp;
		private readonly RiskExpression rightExp;
		internal AndPredicateOp(RiskExpression leftExp, RiskExpression rightExp)
		{
			if (rightExp == null) throw new ArgumentException(nameof(rightExp));
			if (leftExp == null) throw new ArgumentException(nameof(leftExp));

			this.leftExp = leftExp;
			this.rightExp = rightExp;
		}

		internal override IEnumerable<RiskExpression> Children
		{
			get
			{
				IEnumerable<RiskExpression> result = new[] { this };
				result = result.Concat(leftExp.Children.Concat(rightExp.Children));
				return result;
			}
		}

		internal override Expression Evaluate
		{
			get
			{
				Expression result;
				var leftExpEvaluated = leftExp.Evaluate;
				var rightExpEvaluated = rightExp.Evaluate;

				result = Expression.And(leftExpEvaluated, rightExpEvaluated);
				return result;
			}
		}

		internal override void ValidateTypes()
		{
			if (leftExp.Type == typeof(bool) && rightExp.Type == typeof(bool))
			{
				this.Type = typeof(bool);
			}
			else
			{
				throw new GameEngineException($"{leftExp.GetType().Name} it's a no valid {rightExp.GetType().Name}");
			}
		}
	}
	internal class Allow_OP : RiskExpression
	{
		internal readonly static RiskExpression ALLOW = new Allow_OP();
		
		internal override Expression Evaluate => Result.ALLOW_EXP;
		internal override IEnumerable<RiskExpression> Children => new[] { this };
		internal override void ValidateTypes() { }
	}
	internal class IterateRuleOp: RiskExpression
	{
		private readonly RiskExpression leftExp;
		private readonly RiskExpression rightExp;
		internal IterateRuleOp(RiskExpression leftExp, RiskExpression rightExp)
		{
			if (rightExp == null) throw new ArgumentException(nameof(rightExp));
			if (leftExp == null) throw new ArgumentException(nameof(leftExp));
			if( ! (leftExp is IterateRuleOp || leftExp == Allow_OP.ALLOW || leftExp is RiskRule) ) throw new GameEngineException($"Invalid left parameter. {nameof(IterateRuleOp)} was expected.");

			this.leftExp = leftExp;
			this.rightExp = rightExp;
		}
		
		internal Result Value
		{
			get
			{
				Result leftEvaluated=null;
				Result rightEvaluated = null;

				if (leftExp is RiskRule) leftEvaluated = ((RiskRule)leftExp).Value;
				else if (leftExp is HashOp) leftEvaluated = ((HashOp)leftExp).Value;
				else if (leftExp is IterateRuleOp) leftEvaluated = ((IterateRuleOp)leftExp).Value;
				else if (leftExp == Allow_OP.ALLOW) leftEvaluated =Result.ALLOW;

				if (rightExp is RiskRule) rightEvaluated = ((RiskRule)rightExp).Value;
				else if (rightExp is HashOp) rightEvaluated = ((HashOp)rightExp).Value;
				else if (rightExp is IterateRuleOp) rightEvaluated = ((IterateRuleOp)rightExp).Value;

				var isLeftTheLastElement = leftEvaluated == Result.ALLOW;
				if (isLeftTheLastElement)
				{
					var isRightUnmatched = rightEvaluated == Result.UNMATCHED;

					return isRightUnmatched ? Result.ALLOW : rightEvaluated;
				}
				else
				{
					var isNotLeftUnmatched = leftEvaluated != Result.UNMATCHED;
					return isNotLeftUnmatched ? leftEvaluated : rightEvaluated;
				}
			}
		}
		internal override Expression Evaluate
		{
			get
			{
				return Expression.Property(
					Expression.Constant(this),
					typeof(IterateRuleOp).GetProperty("Value", BindingFlags.Instance | BindingFlags.NonPublic)
					);
			}
		}

		internal override IEnumerable<RiskExpression> Children
		{
			get
			{
				IEnumerable<RiskExpression> result = new[] { this };
				result = result.Concat(leftExp.Children.Concat(rightExp.Children));
				return result;
			}
		}

		internal override void ValidateTypes()
		{
			if (leftExp == RiskRule.ALLOW_RULE && rightExp is RiskRule)
			{
				this.Type = typeof(RiskRule);
			}
			else if (leftExp is RiskRule && rightExp is RiskRule)
			{
				this.Type = typeof(RiskRule);
			}
			else
			{
				throw new GameEngineException($"{leftExp.GetType().Name} it's a no valid {rightExp.GetType().Name}");
			}
		}
	}
	internal class HashOp : RiskExpression
	{
		private Dictionary<object, RiskExpression> isOPsbyId;
		private RiskExpression leftExp;
		private RiskExpression rightExp;
		private readonly Parameters parameters;
		//private List<IDictionary> indexer = new List<IDictionary>();
		private Dictionary<IEnumerable, Accumulator> indexer = new Dictionary<IEnumerable, Accumulator>();

		internal HashOp(Parameters parameters, RiskExpression leftExp, RiskExpression rightExp)
		{
			if (parameters == null) throw new ArgumentException(nameof(parameters));
			if (rightExp == null) throw new ArgumentException(nameof(rightExp));
			if (leftExp == null) throw new ArgumentException(nameof(leftExp));
			if ((!(leftExp is RiskRule) && !(rightExp is RiskRule)) ||
				(leftExp != Allow_OP.ALLOW && !(rightExp is RiskRule)))
				throw new GameEngineException($"Invalid {nameof(leftExp)} as {leftExp.GetType().Name} {nameof(rightExp)} as {rightExp.GetType().Name}  parameters.");

			this.leftExp = leftExp;
			this.rightExp = rightExp;
			this.parameters = parameters;

			IndexRuleByValue((RiskRule)rightExp);
		}

		internal Dictionary<IEnumerable, Accumulator> IndexedAccumulators
		{
			get
			{
				return indexer;
			}
		}

		internal override IEnumerable<RiskExpression> Children
		{
			get
			{
				IEnumerable<RiskExpression> result = new[] { this };
				result = result.Concat(leftExp.Children.Concat(rightExp.Children));
				return result;
			}
		}
		internal Result Value
		{
			get
			{
				Debug.WriteLine($"{typeof(HashOp)}.{nameof(Value)}");
#if DEBUG
				foreach (KeyValuePair<object, RiskExpression> kvp in isOPsbyId)
				{
					Debug.WriteLine($"	{nameof(isOPsbyId)}.key:{kvp.Key}");
				}
#endif
				RiskExpression rule = null;
				foreach (Parameter parameter in this.parameters)
				{
					var parameterValue = parameter.GetValue();
					bool exists = isOPsbyId.TryGetValue(parameterValue, out rule);

					if (!exists && parameterValue is int)
					{
						parameterValue = Convert.ToDecimal(parameterValue);
						exists = isOPsbyId.TryGetValue(parameterValue, out rule);
					}
					else if (!exists && parameterValue is decimal)
					{
						parameterValue = Convert.ToInt32(parameterValue);
						exists = isOPsbyId.TryGetValue(parameterValue, out rule);
					}

					Debug.WriteLine($"	parameter.Name:{parameter.Name} parameter.Value:{parameter.GetValue()} exists:{exists}");
					if (exists)
					{
						Result result = null;
						if (rule is RiskRule)
						{
							result = ((RiskRule)rule).Value;
						}
						else if (rule is IterateRuleOp)
						{
							result = ((IterateRuleOp)rule).Value;
						}

						if (result == null) throw new GameEngineException($"{ rule.GetType().Name} is not supported yet.");

						if (Result.UNMATCHED == result) break;

						return result;
					}
				}

				if (leftExp is RiskRule) return ((RiskRule)leftExp).Value;
				if (leftExp is IterateRuleOp) return ((IterateRuleOp)leftExp).Value;
				
				return Result.ALLOW;
			}
		}
		internal override Expression Evaluate 
		{
			get
			{
				return Expression.Property(
					Expression.Constant(this),
					typeof(HashOp).GetProperty("Value", BindingFlags.Instance | BindingFlags.NonPublic)
					);
			}
		}

		internal RiskExpression AddRuleWithoutIsOp(RiskRule rule)
		{
			if (rule == null) throw new ArgumentException(nameof(rule));
			if(rule.HasIsOp()) throw new GameEngineException($"Rule has {nameof(IsOp)} in the predicates.");
			
			leftExp = new IterateRuleOp(leftExp, rule);

			return this;
		}

		internal RiskExpression AddRulewithIsOp(RiskRule rule)
		{
			if (rule == null) throw new ArgumentException(nameof(rule));
			if (!rule.HasIsOp()) throw new GameEngineException($"Rule has not {nameof(IsOp)} in the predicates.");
			if (!rule.HasIsOp()) throw new GameEngineException($"Rule has not {nameof(IsOp)} in the predicates.");

			IndexRuleByValue(rule);
			
			rightExp = new IterateRuleOp(rightExp, rule);

			return this;
		}
		private void IndexRuleByValue(RiskRule rule)
		{
			if (isOPsbyId == null) isOPsbyId = new Dictionary<object, RiskExpression>();

			foreach (IsOp ops in rule.AllIsOps())
			{
				object key;
				if (ops.Value is IntLiteral)
				{
					key = (ops.Value as IntLiteral).Value;
				}
				else if (ops.Value is StringLiteral)
				{
					key = (ops.Value as StringLiteral).Value;
				}
				else if (ops.Value is DateTimeLiteral)
				{
					key = (ops.Value as DateTimeLiteral).Value;
				}
				else if (ops.Value is BoolLiteral)
				{
					key = (ops.Value as BoolLiteral).Value;
				}
				else if (ops.Value is DecimalLiteral)
				{
					key = (ops.Value as DecimalLiteral).Value;
				}
				else
				{
					throw new GameEngineException($"{ops.Value.GetType().Name} it's not supported yet.");
				}

				RiskExpression oldRule;
				bool exists = isOPsbyId.TryGetValue(key, out oldRule);
				if (!exists)
				{
					isOPsbyId.Add(key, rule);
				}
				else
				{
					var newRule = new IterateRuleOp(oldRule, rule);
					isOPsbyId[key] = newRule;
				}
				
			}
		}
		internal override void ValidateTypes()
		{
			throw new NotImplementedException();
		}

		internal object[] GroupBy(IEnumerable<IsOp> dimensions)
		{
			object[] keys = new object[dimensions.Count()];
			int index = 0;
			foreach (IsOp dimension in dimensions)
			{
				if (dimension.Value is StringLiteral)
				{
					keys[index] = ((StringLiteral)dimension.Value).Value;
				}
				else if (dimension.Value is DecimalLiteral)
				{
					keys[index] = ((DecimalLiteral)dimension.Value).Value;
				}
				else if (dimension.Value is IntLiteral)
				{
					keys[index] = ((IntLiteral)dimension.Value).Value;
				}
				else if (dimension.Value is BoolLiteral)
				{
					keys[index] = ((BoolLiteral)dimension.Value).Value;
				}
				else if (dimension.Value is DateTimeLiteral)
				{
					keys[index] = ((DateTimeLiteral)dimension.Value).Value;
				}
				else
				{
					throw new GameEngineException($"{dimension.GetType().Name} is not supported yet.");
				}
				index++;
			}

			return keys;
		}

		internal void ProcessRule(RiskRule rule)
		{
			IEnumerable<IsOp> isOps = Enumerable.Empty<IsOp>();
			IEnumerable<Accumulator> accumulators = Enumerable.Empty<Accumulator>();
			foreach (RiskPredicate predicate in rule.Predicates)
			{
				isOps = isOps.Concat<IsOp>(predicate.AllIsOps());
				accumulators = accumulators.Concat<Accumulator>(predicate.AllAccumulators());
			}
			if (isOps.Any() && accumulators.Any())
			{
				foreach (Accumulator accumulator in accumulators)
				{
					var dimensiones = (IEnumerable<object>)GroupBy(isOps);

					//IDictionary dictionaryWithTheDimension = null;
					//if (!TryToSearchADictionaryWithThisDimension(accumulator.DimensionsName, out dictionaryWithTheDimension))
					//{
					//	Type genericDicctionary = typeof(Dictionary<,>);
					//	Type[] typeArgs = { accumulator.DimensionsName.GetType(), typeof(Accumulator) };
					//	Type constructed = genericDicctionary.MakeGenericType(typeArgs);
					//	dictionaryWithTheDimension = (IDictionary)Activator.CreateInstance(constructed);

					//	indexer.Add(dictionaryWithTheDimension);
					//}

					if (!HasADictionaryWithThisDimension(dimensiones))
					{
						//indexer.Add(dimensiones, accumulator);
						indexer.Add(dimensiones, new Accumulator(parameters, accumulator.Type, accumulator.VariableName));
					}
				}
			}
		}
		private bool HasADictionaryWithThisDimension(IEnumerable dimesionNames)
		{
			Accumulator result;
			return TryToSearchADictionaryWithThisDimension(dimesionNames, out result);
		}

		internal bool TryToSearchADictionaryWithThisDimension(IEnumerable dimesionNames, out Accumulator result)
		{
			result = null;
			foreach (KeyValuePair<IEnumerable, Accumulator> keyValue in indexer)
			{
				if (Enumerable.SequenceEqual((IEnumerable<object>)dimesionNames, (IEnumerable<object>)keyValue.Key))
				{
					result = keyValue.Value;
					return true;
				}
			}
			return false;
		}

		//private bool TryToSearchADictionaryWithThisDimension(object dimensionsName, out IDictionary result)
		//{
		//	result = null;
		//	if (dimensionsName.GetType().IsArray)
		//	{
		//		IEnumerable<object> dimesionNames = (IEnumerable<object>)dimensionsName;
		//		foreach (IDictionary dictionary in indexer)
		//		{
		//			foreach (IEnumerable<object> key in dictionary.Keys)
		//			{
		//				if (Enumerable.SequenceEqual(dimesionNames, key))
		//				{
		//					result = dictionary;
		//					return true;
		//				}
		//			}
		//		}
		//		return false;
		//	}
		//	else
		//	{
		//		result = indexer.FirstOrDefault(value => value.Contains(dimensionsName));
		//		return result != null;
		//	}
		//}
	}

	internal class Accumulator : RiskExpression
	{
		private Parameters parameters;
		internal string VariableName { get; }

		internal Accumulator(Parameters parameters, Type fieldType, string variableName)
		{
			Type = fieldType;
			this.parameters = parameters;
			VariableName = variableName;
		}

		internal override IEnumerable<RiskExpression> Children => new[]{this};

		internal override Expression Evaluate
		{
			get 
			{

				MethodCallExpression value = null;
				MethodCallExpression accumulateExp = null;
				if (Type == typeof(int))
				{
					accumulateExp = Expression.Call(
						Expression.Constant(this),
						typeof(Accumulator).GetMethod("FieldAsInt", BindingFlags.Instance | BindingFlags.NonPublic)
						);

					value = Expression.Call(
						Expression.Constant(parameters),
						typeof(Parameters).GetMethod("FieldAsInt", new Type[] { typeof(string) }),
						Expression.Constant(VariableName));
				}
				else if (Type == typeof(decimal))
				{
					accumulateExp = Expression.Call(
						Expression.Constant(this),
						typeof(Accumulator).GetMethod("FieldAsDecimal", BindingFlags.Instance | BindingFlags.NonPublic)
						);

					value = Expression.Call(
						Expression.Constant(parameters),
						typeof(Parameters).GetMethod("FieldAsDecimal", new Type[] { typeof(string) }),
						Expression.Constant(VariableName));
				}
				else
				{
					throw new GameEngineException($"Type {Type} is not supported yet.");
				}

				var accumulatedExpEvaluated = Expression.Add(value, accumulateExp);

				return Accumulator.Traza(accumulatedExpEvaluated, this.GetType(), value, accumulateExp, Expression.Constant(this.GetHashCode()));
			}
			
		}

		public static Expression Traza(Expression e, Type callClaseType, params Expression[] trazas)
		{
			int index = 1;
			Expression[] logs = new Expression[trazas.Count()+1];
			logs[0] = Expression.Call(
					null,
					typeof(Debug).GetMethod("Print", new Type[] { typeof(string) }),
					Expression.Constant($"TRACE {callClaseType} {e.GetType()}")
					);
			foreach (Expression traza in trazas)
			{
				object value = null;
				if (traza.Type == typeof(int))
				{
					value = $"	{ Expression.Lambda<Func<int>>(traza).Compile()()}";
				}
				else if (traza.Type == typeof(decimal))
				{
					value = $"	{ Expression.Lambda<Func<decimal>>(traza).Compile()()}";
				}
				else if (traza.Type == typeof(bool))
				{
					value = $"	{ Expression.Lambda<Func<bool>>(traza).Compile()()}";
				}
				else if (traza.Type == typeof(DateTime))
				{
					value = $"	{ Expression.Lambda<Func<DateTime>>(traza).Compile()()}";
				}
				else if (traza.Type == typeof(string))
				{
					value = $"	{ Expression.Lambda<Func<string>>(traza).Compile()()}";
				}
				else if (traza.Type == typeof(Result))
				{
					var result = Expression.Lambda<Func<Result>>(traza).Compile()();
					value = $"	{nameof(Result)}:{result.RuleId}{result.Action}";
				}
				else 
				{
					throw new NotImplementedException();
				}
				var itemGetValueExp = Expression.Call(
					null,
					typeof(Debug).GetMethod("Print", new Type[] { typeof(string) }),
					Expression.Constant(value)
					);
				logs[index] = itemGetValueExp;
				index++;
			}


			var logExp = Expression.Block(logs);
			return Expression.Block(logExp, e);
		}

		private object accumulate = null;
		internal void Accumule( )
		{
			object newValue = parameters.FirstOrDefault(value => value.Name == VariableName);

			if (newValue == null) throw new ArgumentNullException(nameof(newValue));
			newValue = ((Parameter)newValue).GetValue();
			if (!(newValue is int || newValue is decimal)) throw new GameEngineException($"Type {newValue.GetType().Name} is not valid yet.");

			if (accumulate == null)
			{
				accumulate = newValue;
				return;
			}

			if (accumulate is int && newValue is int)
			{
				accumulate = ((int)accumulate) + ((int)newValue);
			}
			else if (accumulate is decimal && newValue is decimal)
			{
				accumulate = ((decimal)accumulate) + ((decimal)newValue);
			}
			else if (accumulate is int && newValue is decimal)
			{
				accumulate = ((int)accumulate) + Convert.ToInt32(newValue);
			}
			else if (accumulate is decimal && newValue is int)
			{
				accumulate = ((decimal)accumulate) + Convert.ToDecimal(newValue);
			}
			else
			{
				throw new GameEngineException($"Type: {newValue.GetType().Name} is not supported yet.");
			}
		}

		internal int FieldAsInt()
		{
			if (accumulate!=null)
			{
				var result = accumulate;
				if (result is decimal && ((decimal)result % 1) == 0) result = Convert.ToInt32(result);
				return (int)result;
			}
			return 0;
		}
		internal decimal FieldAsDecimal()
		{
			if (accumulate != null)
			{
				return (decimal)accumulate;
			}
			return 0;
		}

		internal override void ValidateTypes()
		{
			throw new NotImplementedException();
		}
	}

	class Variable : RiskExpression
	{
		private readonly Parameters parameters;
		private Type fieldType;
		internal Variable(Parameters parameters, Type fieldType, string variableName)
		{
			if (string.IsNullOrEmpty(variableName)) throw new ArgumentException(nameof(variableName));
			if (parameters == null) throw new ArgumentException(nameof(parameters));

			Name = variableName.Trim().ToLower();
			this.parameters = parameters;
			this.fieldType = fieldType;
		}
		internal string Name { get; }

		internal Parameters Parameters { get { return parameters; } }

		internal override IEnumerable<RiskExpression> Children => new[] { this };

		internal override void ValidateTypes()
		{
		}
		internal override Expression Evaluate 
		{
			get
			{
				
				if (fieldType == typeof(int))
				{
					var result = Expression.Call(
						Expression.Constant(parameters),
						typeof(Parameters).GetMethod("FieldAsInt", new Type[] { typeof(string) }),
						Expression.Constant(Name)
					);
					return result;
				}
				else if (fieldType == typeof(bool))
				{
					var result = Expression.Call(
						Expression.Constant(parameters),
						typeof(Parameters).GetMethod("FieldAsBoolean", new Type[] { typeof(string) }),
						Expression.Constant(Name)
					);
					return result;
				}
				else if (fieldType == typeof(decimal))
				{
					var result = Expression.Call(
						Expression.Constant(parameters),
						typeof(Parameters).GetMethod("FieldAsDecimal", new Type[] { typeof(string) }),
						Expression.Constant(Name)
					);
					return result;
				}
				else if (fieldType == typeof(string))
				{
					var result = Expression.Call(
						Expression.Constant(parameters),
						typeof(Parameters).GetMethod("FieldAsString", new Type[] { typeof(string) }),
						Expression.Constant(Name)
					);
					return result;
				}
				else if (fieldType == typeof(DateTime))
				{
					var result = Expression.Call(
						Expression.Constant(parameters),
						typeof(Parameters).GetMethod("FieldAsDateTime", new Type[] { typeof(string) }),
						Expression.Constant(Name)
					);
					return result;
				}

				throw new GameEngineException($"Variable {Name} has the type {fieldType} whichis not supported.");
			}
		}

		internal bool IsNumeric 
		{
			get 
			{ 
				return (fieldType == typeof(int)) || (fieldType == typeof(decimal)); 
			}
		}
		internal Type FieldType
		{
			get
			{
				return fieldType;
			}
		}
		internal Accumulator Accumulator { get; set; }
	}
	class BoolLiteral : RiskExpression
	{
		private BoolLiteral(bool value)
		{
			Value = value;
			base.Type = typeof(bool);
		}
		internal bool Value { get; }
		internal override IEnumerable<RiskExpression> Children => new[] { this };
		internal override void ValidateTypes() { }
		internal override Expression Evaluate
		{
			get
			{
				return Expression.Constant(Value);
			}
		}

		internal readonly static RiskExpression TRUE = new BoolLiteral(true);
		internal readonly static RiskExpression FALSE = new BoolLiteral(false);
	}

	class IntLiteral : RiskExpression
	{
		private readonly int value;
		internal IntLiteral(int value)
		{
			this.value = value;
			base.Type = typeof(int);
		}

		internal override IEnumerable<RiskExpression> Children => new[] { this };
		internal override void ValidateTypes() { }

		internal override Expression Evaluate
		{
			get
			{
				return Expression.Constant(value);
			}
		}
		internal int Value
		{
			get
			{
				return this.value;
			}
		}

	}

	class StringLiteral : RiskExpression
	{
		internal StringLiteral(string value)
		{
			if (string.IsNullOrEmpty(value)) throw new ArgumentException(nameof(value));
			Value = value;
			base.Type = typeof(string);
		}
		internal string Value { get; }

		internal override IEnumerable<RiskExpression> Children => new[] { this };
		internal override void ValidateTypes()
		{

		}
		internal override Expression Evaluate
		{
			get
			{
				return Expression.Constant(Value);
			}
		}
	}
	class DecimalLiteral : RiskExpression
	{
		internal DecimalLiteral(decimal value)
		{
			Value = value;
			base.Type = typeof(decimal);
		}
		internal decimal Value { get; }

		internal override IEnumerable<RiskExpression> Children => new[] { this };

		internal override void ValidateTypes()
		{

		}
		internal override Expression Evaluate
		{
			get
			{
				return Expression.Constant(Value);
			}
		}
	}

	class DateTimeLiteral : RiskExpression
	{
		internal DateTimeLiteral(DateTime value)
		{
			Value = value;
			base.Type = typeof(DateTime);
		}
		internal DateTime Value { get; }

		internal override IEnumerable<RiskExpression> Children => new[] { this };
		internal override void ValidateTypes()
		{

		}
		internal override Expression Evaluate
		{
			get
			{
				return Expression.Constant(Value);
			}
		}
	}

	class IsOp : RiskExpression
	{
		private readonly RiskExpression leftExp;
		private readonly RiskExpression rightExp;

		internal IsOp(RiskExpression leftExp, RiskExpression rightExp)
		{
			if (!(leftExp is Variable)) throw new GameEngineException($"{nameof(leftExp)} must be a valid variable name.");
			if (!(rightExp is IntLiteral ||
				rightExp is StringLiteral ||
				rightExp is DecimalLiteral ||
				rightExp is BoolLiteral ||
				rightExp is DateTimeLiteral))
				throw new GameEngineException($"Invalid {nameof(rightExp)} as {rightExp.GetType().Name}  parameters.");

			this.leftExp = leftExp;
			this.rightExp = rightExp;
		}
		internal RiskExpression Value
		{
			get
			{
				return rightExp;
			}
		}
		internal Variable Variable
		{
			get
			{
				return (Variable)leftExp;
			}
		}
		internal override IEnumerable<RiskExpression> Children
		{
			get
			{
				IEnumerable<RiskExpression> result = new[] { this };
				result = result.Concat(leftExp.Children.Concat(rightExp.Children));
				return result;
			}
		}

		internal override void ValidateTypes()
		{
			if (leftExp.Type == typeof(int) && rightExp.Type == typeof(int))
			{
				this.Type = typeof(bool);
			}
			else if (leftExp.Type == typeof(decimal) && rightExp.Type == typeof(decimal))
			{
				this.Type = typeof(bool);
			}
			else if (leftExp.Type == typeof(decimal) && rightExp.Type == typeof(int))
			{
				this.Type = typeof(bool);
			}
			else if (leftExp.Type == typeof(int) && rightExp.Type == typeof(decimal))
			{
				this.Type = typeof(bool);
			}
			else if (leftExp.Type == typeof(DateTime) && rightExp.Type == typeof(DateTime))
			{
				this.Type = typeof(bool);
			}
			else if (leftExp.Type == typeof(string) && rightExp.Type == typeof(string))
			{
				this.Type = typeof(bool);
			}
			else if (leftExp.Type == typeof(bool) && rightExp.Type == typeof(bool))
			{
				this.Type = typeof(bool);
			}
			else
			{
				throw new GameEngineException($"{leftExp.GetType().Name} it's a no valid {rightExp.GetType().Name}");
			}
		}

		internal override Expression Evaluate
		{
			get
			{
				Expression result;
				var leftExpEvaluated = leftExp.Evaluate;
				var rightExpEvaluated = rightExp.Evaluate;
				if (leftExpEvaluated.Type == typeof(decimal) && rightExpEvaluated.Type == typeof(int))
				{
					rightExpEvaluated = Expression.Convert(rightExpEvaluated, typeof(decimal));
				}
				else if (leftExpEvaluated.Type == typeof(int) && rightExpEvaluated.Type == typeof(decimal))
				{
					leftExpEvaluated = Expression.Convert(leftExpEvaluated, typeof(decimal));
				}

				result = Expression.Equal(leftExpEvaluated, rightExpEvaluated);
				return Accumulator.Traza(result, this.GetType(), leftExpEvaluated, rightExpEvaluated) ;
			}
		}
	}

	class GreatherOp : RiskExpression
	{
		private readonly RiskExpression leftExp;
		private readonly RiskExpression rightExp;
		internal GreatherOp(RiskExpression leftExp, RiskExpression rightExp)
		{
			if (leftExp == null) throw new ArgumentNullException(nameof(leftExp));
			if (rightExp == null) throw new ArgumentNullException(nameof(rightExp));

			this.leftExp = leftExp;
			this.rightExp = rightExp;
		}
		internal override IEnumerable<RiskExpression> Children
		{
			get
			{
				IEnumerable<RiskExpression> result = new[] { this };
				result = result.Concat(leftExp.Children.Concat(rightExp.Children));
				return result;
			}
		}
		internal override void ValidateTypes()
		{
			if (leftExp.Type == typeof(int) && rightExp.Type == typeof(int))
			{
				this.Type = typeof(bool);
			}
			else if (leftExp.Type == typeof(decimal) && rightExp.Type == typeof(decimal))
			{
				this.Type = typeof(bool);
			}
			else if (leftExp.Type == typeof(decimal) && rightExp.Type == typeof(int))
			{
				this.Type = typeof(bool);
			}
			else if (leftExp.Type == typeof(int) && rightExp.Type == typeof(decimal))
			{
				this.Type = typeof(bool);
			}
			else if (leftExp.Type == typeof(DateTime) && rightExp.Type == typeof(DateTime))
			{
				this.Type = typeof(bool);
			}
			else
			{
				throw new GameEngineException($"{leftExp.GetType().Name} it's a no valid {rightExp.GetType().Name}");
			}
		}

		internal override Expression Evaluate
		{
			get
			{
				Expression result;
				var leftExpEvaluated = leftExp.Evaluate;
				var rightExpEvaluated = rightExp.Evaluate;
				if (leftExpEvaluated.Type == typeof(decimal) && rightExpEvaluated.Type == typeof(int))
				{
					rightExpEvaluated = Expression.Convert(rightExpEvaluated, typeof(decimal));
				}
				else if (leftExpEvaluated.Type == typeof(int) && rightExpEvaluated.Type == typeof(decimal))
				{
					leftExpEvaluated = Expression.Convert(leftExpEvaluated, typeof(decimal));
				}

				result = Expression.GreaterThan(leftExpEvaluated, rightExpEvaluated);
				return result;
			}
		}
	}

	class GreatherOrEqualOp : RiskExpression
	{
		private readonly RiskExpression leftExp;
		private readonly RiskExpression rightExp;
		internal GreatherOrEqualOp(RiskExpression leftExp, RiskExpression rightExp)
		{
			if (leftExp == null) throw new ArgumentNullException(nameof(leftExp));
			if (rightExp == null) throw new ArgumentNullException(nameof(rightExp));

			this.leftExp = leftExp;
			this.rightExp = rightExp;
		}
		internal override IEnumerable<RiskExpression> Children
		{
			get
			{
				IEnumerable<RiskExpression> result = new[] { this };
				result = result.Concat(leftExp.Children.Concat(rightExp.Children));
				return result;
			}
		}
		internal override void ValidateTypes()
		{
			if (leftExp.Type == typeof(int) && rightExp.Type == typeof(int))
			{
				this.Type = typeof(bool);
			}
			else if (leftExp.Type == typeof(decimal) && rightExp.Type == typeof(decimal))
			{
				this.Type = typeof(bool);
			}
			else if (leftExp.Type == typeof(decimal) && rightExp.Type == typeof(int))
			{
				this.Type = typeof(bool);
			}
			else if (leftExp.Type == typeof(int) && rightExp.Type == typeof(decimal))
			{
				this.Type = typeof(bool);
			}
			else if (leftExp.Type == typeof(DateTime) && rightExp.Type == typeof(DateTime))
			{
				this.Type = typeof(bool);
			}
			else
			{
				throw new GameEngineException($"{leftExp.GetType().Name} it's a no valid {rightExp.GetType().Name}");
			}
		}

		internal override Expression Evaluate
		{
			get
			{
				Expression result;
				var leftExpEvaluated = leftExp.Evaluate;
				var rightExpEvaluated = rightExp.Evaluate;
				if (leftExpEvaluated.Type == typeof(decimal) && rightExpEvaluated.Type == typeof(int))
				{
					rightExpEvaluated = Expression.Convert(rightExpEvaluated, typeof(decimal));
				}
				else if (leftExpEvaluated.Type == typeof(int) && rightExpEvaluated.Type == typeof(decimal))
				{
					leftExpEvaluated = Expression.Convert(leftExpEvaluated, typeof(decimal));
				}

				result = Expression.GreaterThanOrEqual(leftExpEvaluated, rightExpEvaluated);
				return result;
			}
		}
	}

	class LowerOp : RiskExpression
	{
		private readonly RiskExpression leftExp;
		private readonly RiskExpression rightExp;
		internal LowerOp(RiskExpression leftExp, RiskExpression rightExp)
		{
			if (leftExp == null) throw new ArgumentNullException(nameof(leftExp));
			if (rightExp == null) throw new ArgumentNullException(nameof(rightExp));

			this.leftExp = leftExp;
			this.rightExp = rightExp;
		}
		internal override IEnumerable<RiskExpression> Children
		{
			get
			{
				IEnumerable<RiskExpression> result = new[] { this };
				result = result.Concat(leftExp.Children.Concat(rightExp.Children));
				return result;
			}
		}
		internal override void ValidateTypes()
		{
			if (leftExp.Type == typeof(int) && rightExp.Type == typeof(int))
			{
				this.Type = typeof(bool);
			}
			else if (leftExp.Type == typeof(decimal) && rightExp.Type == typeof(decimal))
			{
				this.Type = typeof(bool);
			}
			else if (leftExp.Type == typeof(decimal) && rightExp.Type == typeof(int))
			{
				this.Type = typeof(bool);
			}
			else if (leftExp.Type == typeof(int) && rightExp.Type == typeof(decimal))
			{
				this.Type = typeof(bool);
			}
			else if (leftExp.Type == typeof(DateTime) && rightExp.Type == typeof(DateTime))
			{
				this.Type = typeof(bool);
			}
			else
			{
				throw new GameEngineException($"{leftExp.GetType().Name} it's a no valid {rightExp.GetType().Name}");
			}
		}
		internal override Expression Evaluate
		{
			get
			{
				Expression result;
				var leftExpEvaluated = leftExp.Evaluate;
				var rightExpEvaluated = rightExp.Evaluate;
				if (leftExpEvaluated.Type == typeof(decimal) && rightExpEvaluated.Type == typeof(int))
				{
					rightExpEvaluated = Expression.Convert(rightExpEvaluated, typeof(decimal));
				}
				else if (leftExpEvaluated.Type == typeof(int) && rightExpEvaluated.Type == typeof(decimal))
				{
					leftExpEvaluated = Expression.Convert(leftExpEvaluated, typeof(decimal));
				}
				
				result = Expression.LessThan(leftExpEvaluated, rightExpEvaluated);
				return result;
			}
		}
	}

	class LowerOrEqualOp : RiskExpression
	{
		private readonly RiskExpression leftExp;
		private readonly RiskExpression rightExp;
		internal LowerOrEqualOp(RiskExpression leftExp, RiskExpression rightExp)
		{
			if (leftExp == null) throw new ArgumentNullException(nameof(leftExp));
			if (rightExp == null) throw new ArgumentNullException(nameof(rightExp));

			this.leftExp = leftExp;
			this.rightExp = rightExp;
		}
		internal override IEnumerable<RiskExpression> Children
		{
			get
			{
				IEnumerable<RiskExpression> result = new[] { this };
				result = result.Concat(leftExp.Children.Concat(rightExp.Children));
				return result;
			}
		}
		internal override void ValidateTypes()
		{
			//monto <= 10
			if (leftExp.Type == typeof(int) && rightExp.Type == typeof(int))
			{
				this.Type = typeof(bool);
			}
			else if (leftExp.Type == typeof(decimal) && rightExp.Type == typeof(decimal))
			{
				this.Type = typeof(bool);
			}
			else if (leftExp.Type == typeof(decimal) && rightExp.Type == typeof(int))
			{
				this.Type = typeof(bool);
			}
			else if (leftExp.Type == typeof(int) && rightExp.Type == typeof(decimal))
			{
				this.Type = typeof(bool);
			}
			else if (leftExp.Type == typeof(DateTime) && rightExp.Type == typeof(DateTime))
			{
				this.Type = typeof(bool);
			}
			else
			{
				throw new GameEngineException($"{leftExp.GetType().Name} it's a no valid {rightExp.GetType().Name}");
			}
		}

		internal override Expression Evaluate
		{
			get
			{
				Expression result;
				var leftExpEvaluated = leftExp.Evaluate;
				var rightExpEvaluated = rightExp.Evaluate;
				if (leftExpEvaluated.Type == typeof(decimal) && rightExpEvaluated.Type == typeof(int))
				{
					rightExpEvaluated = Expression.Convert(rightExpEvaluated, typeof(decimal));
				}
				else if (leftExpEvaluated.Type == typeof(int) && rightExpEvaluated.Type == typeof(decimal))
				{
					leftExpEvaluated = Expression.Convert(leftExpEvaluated, typeof(decimal));
				}

				result = Expression.LessThanOrEqual(leftExpEvaluated, rightExpEvaluated);
				return Accumulator.Traza(result, this.GetType(), leftExpEvaluated, rightExpEvaluated); ;
			}
		}
	}

	class NotEqualOp : RiskExpression
	{
		private readonly RiskExpression leftExp;
		private readonly RiskExpression rightExp;
		internal NotEqualOp(RiskExpression leftExp, RiskExpression rightExp)
		{
			if (leftExp == null) throw new ArgumentNullException(nameof(leftExp));
			if (rightExp == null) throw new ArgumentNullException(nameof(rightExp));

			this.leftExp = leftExp;
			this.rightExp = rightExp;
		}
		internal override IEnumerable<RiskExpression> Children
		{
			get
			{
				IEnumerable<RiskExpression> result = new[] { this };
				result = result.Concat(leftExp.Children.Concat(rightExp.Children));
				return result;
			}
		}
		internal override void ValidateTypes()
		{
			//monto != 10
			if (leftExp.Type == typeof(int) && rightExp.Type == typeof(int))
			{
				this.Type = typeof(bool);
			}
			else if (leftExp.Type == typeof(decimal) && rightExp.Type == typeof(decimal))
			{
				this.Type = typeof(bool);
			}
			else if (leftExp.Type == typeof(decimal) && rightExp.Type == typeof(int))
			{
				this.Type = typeof(bool);
			}
			else if (leftExp.Type == typeof(int) && rightExp.Type == typeof(decimal))
			{
				this.Type = typeof(bool);
			}
			else if (leftExp.Type == typeof(DateTime) && rightExp.Type == typeof(DateTime))
			{
				this.Type = typeof(bool);
			}
			else if (leftExp.Type == typeof(string) && rightExp.Type == typeof(string))
			{
				this.Type = typeof(bool);
			}
			else if (leftExp.Type == typeof(bool) && rightExp.Type == typeof(bool))
			{
				this.Type = typeof(bool);
			}
			else
			{
				throw new GameEngineException($"{leftExp.GetType().Name} it's a no valid {rightExp.GetType().Name}");
			}
		}

		internal override Expression Evaluate
		{
			get
			{
				Expression result;
				var leftExpEvaluated = leftExp.Evaluate;
				var rightExpEvaluated = rightExp.Evaluate;
				if (leftExpEvaluated.Type == typeof(decimal) && rightExpEvaluated.Type == typeof(int))
				{
					rightExpEvaluated = Expression.Convert(rightExpEvaluated, typeof(decimal));
				}
				else if (leftExp.Type == typeof(int) && rightExp.Type == typeof(decimal))
				{
					leftExpEvaluated = Expression.Convert(leftExpEvaluated, typeof(decimal));
				}

				result = Expression.NotEqual(leftExpEvaluated, rightExpEvaluated);
				return result;
			}
		}
	}

	class EqualOp : RiskExpression
	{
		private readonly RiskExpression leftExp;
		private readonly RiskExpression rightExp;
		internal EqualOp(RiskExpression leftExp, RiskExpression rightExp)
		{
			if (leftExp == null) throw new ArgumentNullException(nameof(leftExp));
			if (rightExp == null) throw new ArgumentNullException(nameof(rightExp));

			this.leftExp = leftExp;
			this.rightExp = rightExp;
		}
		internal override IEnumerable<RiskExpression> Children
		{
			get
			{
				IEnumerable<RiskExpression> result = new[] { this };
				result = result.Concat(leftExp.Children.Concat(rightExp.Children));
				return result;
			}
		}
		internal override void ValidateTypes()
		{
			//monto == 10
			if (leftExp.Type == typeof(int) && rightExp.Type == typeof(int))
			{
				this.Type = typeof(bool);
			}
			else if (leftExp.Type == typeof(decimal) && rightExp.Type == typeof(decimal))
			{
				this.Type = typeof(bool);
			}
			else if (leftExp.Type == typeof(decimal) && rightExp.Type == typeof(int))
			{
				this.Type = typeof(bool);
			}
			else if (leftExp.Type == typeof(int) && rightExp.Type == typeof(decimal))
			{
				this.Type = typeof(bool);
			}
			else if (leftExp.Type == typeof(DateTime) && rightExp.Type == typeof(DateTime))
			{
				this.Type = typeof(bool);
			}
			else if (leftExp.Type == typeof(string) && rightExp.Type == typeof(string))
			{
				this.Type = typeof(bool);
			}
			else if (leftExp.Type == typeof(bool) && rightExp.Type == typeof(bool))
			{
				this.Type = typeof(bool);
			}
			else
			{
				throw new GameEngineException($"{leftExp.GetType().Name} it's a no valid {rightExp.GetType().Name}");
			}
		}

		internal override Expression Evaluate
		{
			get
			{
				Expression result;
				var leftExpEvaluated = leftExp.Evaluate;
				var rightExpEvaluated = rightExp.Evaluate;
				if (leftExpEvaluated.Type == typeof(decimal) && rightExpEvaluated.Type == typeof(int))
				{
					rightExpEvaluated = Expression.Convert(rightExpEvaluated, typeof(decimal));
				}
				else if (leftExpEvaluated.Type == typeof(int) && rightExpEvaluated.Type == typeof(decimal))
				{
					leftExpEvaluated = Expression.Convert(leftExpEvaluated, typeof(decimal));
				}

				result = Expression.Equal(leftExpEvaluated, rightExpEvaluated);
				return result;
			}
		}
	}

	class A
	{
		public int x = 1;
	}

	class B : A
	{
		public int x = 1;
	}

	class Program2
	{
		static void Main2(string[] args)
		{
			List<A> la = new List<A>();
			la.Add(new A());
			la.Add(new A());

			IEnumerable<A> ea = la;

			List<B> lb = new List<B>();
			lb.Add(new B());
			lb.Add(new B());
			IEnumerable<A> eb = lb;

			string s = "";
			foreach (var i in eb)
			{
				s += i.x;
			}
			int o = 1;

			Customer customer = new Customer();

			Expression<Func<Customer, string>> customerAccount2 = (c) => c.account;

			Line line = new Line();
			Expression<Func<Line, decimal>> lineRisk2 = (l) => l.risk;


			var customerAccount = Expression.PropertyOrField(Expression.Constant(customer), "account");

			var lineRisk = Expression.PropertyOrField(Expression.Constant(line), "risk");


			var s123 = Expression.Constant("123");
			var s456 = Expression.Constant("456");
			var s789 = Expression.Constant("789");


			var c160 = Expression.Constant(160m, typeof(decimal));
			var cond1 = Expression.And(Expression.Equal(customerAccount2, s123), Expression.GreaterThan(lineRisk2, c160));


			var c150 = Expression.Constant(150m, typeof(decimal));
			var cond2 = Expression.And(Expression.Equal(customerAccount, s456), Expression.GreaterThan(lineRisk, c150));

			var c100 = Expression.Constant(100m, typeof(decimal));
			var cond3 = Expression.And(Expression.Equal(customerAccount, s789), Expression.GreaterThan(lineRisk, c100));

			var programa = Expression.Lambda<Func<bool>>(cond3).Compile();
			Console.WriteLine(programa());

			/*RiskLimitFinder f = new RiskLimitFinder();
			f.Visit(cond1);

			RulesEngine rules = new RulesEngine();
			var rule1 = new Rule(cond1);
			var rule2 = new Rule(cond2);
			var rule3 = new Rule(cond3);

			rules.Add(rule1);
			rules.Add(rule2);
			rules.Add(rule3);

			List<Expression> reglas = new List<Expression>();
			reglas.Add(cond1);
			reglas.Add(cond2);
			reglas.Add(cond3);
			*/
		}
	}

	class RiskLimitFinder : ExpressionVisitor
	{
		List<Expression> limits = new List<Expression>();
		public override Expression Visit(Expression node)
		{
			if (node.NodeType == ExpressionType.GreaterThanOrEqual)
			{
				limits.Add((node as BinaryExpression).Right);
			}
			else if (node.NodeType == ExpressionType.GreaterThan)
			{
				limits.Add((node as BinaryExpression).Right);
			}
			else if (node.NodeType == ExpressionType.LessThan)
			{

			}
			else if (node.NodeType == ExpressionType.LessThanOrEqual)
			{
				limits.Add((node as BinaryExpression).Right);
			}
			return base.Visit(node);
		}

	}

	internal class RulesEngine
	{
		private readonly List<Rule> rules = new List<Rule>();

		internal void Add(Rule rule)
		{
			rules.Add(rule);

			//para toda regla que tenga un GreatherThan y Risk a alguno de los lados...
			//=> debo ir a construir el if{} else if{}
			// y agregar la regla en el bloque correspondiente.
		}

	}

	internal class Rule
	{
		internal Expression Expression { get; }
		internal Rule(Expression expression)
		{
			Expression = expression;
		}
	}

	internal class RiskFilter
	{

	}

	internal class Customer
	{
		internal string account = "123";
	}

	internal class Line
	{
		internal decimal risk = 145.0m;
	}
}
