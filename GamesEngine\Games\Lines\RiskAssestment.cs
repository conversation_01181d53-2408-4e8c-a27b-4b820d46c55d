﻿using GamesEngine.Gameboards.Lines;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Games.Lines
{
	[Puppet]
	internal class RiskAssestment : Objeto
	{
		private readonly Game game;
		private readonly Dictionary<Line, RiskStatistics> detailStatistics;
		private decimal totalToWin, totalToRisk, amountToWon, amountToRisk, amountToPending;
		private int numberOfBets;

		internal RiskAssestment(Game game)
		{
			if (game == null) throw new ArgumentNullException(nameof(game));

			this.game = game;
			this.detailStatistics = new Dictionary<Line, RiskStatistics>();
			numberOfBets = 0;
		}

		internal double Juice(Line line, WagerAnswer answer)
		{
			if (line == null) throw new ArgumentNullException(nameof(line));
			if (answer == null) throw new ArgumentNullException(nameof(answer));

			decimal juice = 0;
			var originalLine = line.OriginalVersion;
			RiskStatistics stats;
			if (!this.detailStatistics.TryGetValue(originalLine, out stats))
			{
				throw new GameEngineException("Line does not exist.");
			}

			switch (answer)
			{
				case DrawAnswer ab:
					if (!(line is MoneyDrawLine)) throw new GameEngineException("Line does not correspond to answer type");

					var placeForOtherThanDraw = (stats as ABRiskStatistics).PlaceForTeamA + (stats as ABRiskStatistics).PlaceForTeamB;
					var toPayIfDrawWon = (stats as ABRiskStatistics).ToPayIfDrawWon;

					if (placeForOtherThanDraw < toPayIfDrawWon)
					{
						juice = -(1m - (placeForOtherThanDraw / toPayIfDrawWon));
					}
					else
					{
						juice = +(placeForOtherThanDraw - toPayIfDrawWon) / toPayIfDrawWon;
					}
					break;
				case ABAnswer ab:
					if (!(line is ABLine)) throw new GameEngineException("Line does not correspond to answer type");

					if (line is MoneyDrawLine) throw new NotImplementedException();

					if (game.TeamA == ab.ChosenTeam)
					{
						if((stats as ABRiskStatistics).PlaceForTeamB < (stats as ABRiskStatistics).ToPayIfTeamAWon)
						{
							juice = - (1m - ((stats as ABRiskStatistics).PlaceForTeamB / (stats as ABRiskStatistics).ToPayIfTeamAWon));
						}
						else
						{
							juice = + ((stats as ABRiskStatistics).PlaceForTeamB - (stats as ABRiskStatistics).ToPayIfTeamAWon) / (stats as ABRiskStatistics).ToPayIfTeamAWon;
						}
					}
					else
					{
						if ((stats as ABRiskStatistics).PlaceForTeamA < (stats as ABRiskStatistics).ToPayIfTeamBWon)
						{
							juice = -(1m - ((stats as ABRiskStatistics).PlaceForTeamA / (stats as ABRiskStatistics).ToPayIfTeamBWon));
						}
						else
						{
							juice = +((stats as ABRiskStatistics).PlaceForTeamA - (stats as ABRiskStatistics).ToPayIfTeamBWon) / (stats as ABRiskStatistics).ToPayIfTeamBWon;
						}
					}
					break;
				case YesNoAnswer yN:
					if (!(line is YesNoLine)) throw new GameEngineException("Line does not correspond to answer type");

					if (yN == YesNoAnswer.YES)
					{
						if ((stats as YesNoRiskStatistics).PlaceForNo < (stats as YesNoRiskStatistics).ToPayIfYesWon)
						{
							juice = -(1m - ((stats as YesNoRiskStatistics).PlaceForNo / (stats as YesNoRiskStatistics).ToPayIfYesWon));
						}
						else
						{
							juice = +((stats as YesNoRiskStatistics).PlaceForNo - (stats as YesNoRiskStatistics).ToPayIfYesWon) / (stats as YesNoRiskStatistics).ToPayIfYesWon;
						}
					}
					else
					{
						if ((stats as YesNoRiskStatistics).PlaceForYes < (stats as YesNoRiskStatistics).ToPayIfNoWon)
						{
							juice = -(1m - ((stats as YesNoRiskStatistics).PlaceForYes / (stats as YesNoRiskStatistics).ToPayIfNoWon));
						}
						else
						{
							juice = +((stats as YesNoRiskStatistics).PlaceForYes - (stats as YesNoRiskStatistics).ToPayIfNoWon) / (stats as YesNoRiskStatistics).ToPayIfNoWon;
						}
					}
					break;
				case OverUnderAnswer oU:
					if (!(line is OverUnderLine)) throw new GameEngineException("Line does not correspond to answer type");

					if (oU == OverUnderAnswer.OVER)
					{
						if ((stats as OverUnderRiskStatistics).PlaceForUnder < (stats as OverUnderRiskStatistics).ToPayIfOverWon)
						{
							juice = -(1m - ((stats as OverUnderRiskStatistics).PlaceForUnder / (stats as OverUnderRiskStatistics).ToPayIfOverWon));
						}
						else
						{
							juice = +((stats as OverUnderRiskStatistics).PlaceForUnder - (stats as OverUnderRiskStatistics).ToPayIfOverWon) / (stats as OverUnderRiskStatistics).ToPayIfOverWon;
						}
					}
					else
					{
						if ((stats as OverUnderRiskStatistics).PlaceForOver < (stats as OverUnderRiskStatistics).ToPayIfUnderWon)
						{
							juice = -(1m - ((stats as OverUnderRiskStatistics).PlaceForOver / (stats as OverUnderRiskStatistics).ToPayIfUnderWon));
						}
						else
						{
							juice = +((stats as OverUnderRiskStatistics).PlaceForOver - (stats as OverUnderRiskStatistics).ToPayIfUnderWon) / (stats as OverUnderRiskStatistics).ToPayIfUnderWon;
						}
					}
					break;
				case FixedAnswer fA:
					if (!(line is FixedLine)) throw new GameEngineException("Line does not correspond to answer type");

					var placeForOtherThan = (stats as FixedRiskStatistics).PlaceForOtherThan(fA.Ordinal);
					var toPayIfWon = (stats as FixedRiskStatistics).ToPayIfWon(fA.Ordinal);

					if (placeForOtherThan < toPayIfWon)
					{
						juice = -(1m - (placeForOtherThan / toPayIfWon));
					}
					else
					{
						juice = +(placeForOtherThan - toPayIfWon) / toPayIfWon;
					}
					break;
				default:
					throw new GameEngineException("Answer type is not being handled");
			}

			juice = juice * 100m;
			return (double)juice;
		}

		internal void Initialize(Line line)
		{
			if (line == null) throw new ArgumentNullException(nameof(line));
			if (!line.IsOriginalVersion()) throw new GameEngineException("Line is not a original version.");
			if (this.detailStatistics.ContainsKey(line)) throw new GameEngineException($"This line {line} is already added.");

			RiskStatistics stats;
			switch (line)
			{
				case ABLine ab:
					stats = new ABRiskStatistics();
					this.detailStatistics.Add(line, stats);
					break;

				case YesNoLine yN:
					stats = new YesNoRiskStatistics();
					this.detailStatistics.Add(line, stats);
					break;
				case OverUnderLine oU:
					stats = new OverUnderRiskStatistics();
					this.detailStatistics.Add(line, stats);
					break;
				case FixedLine fA:
					stats = new FixedRiskStatistics();
					this.detailStatistics.Add(line, stats);
					break;
				default:
					throw new GameEngineException("line type is not being handled");
			}
		}

		internal void UpdateGrade(Wager wager)
		{
			if (wager == null) throw new ArgumentNullException(nameof(wager));

			var placedAmount = wager.Risk;
			var toWin = wager.ToWin();
			this.totalToRisk = totalToRisk - placedAmount;
			this.totalToWin = totalToWin - toWin;

			if (wager.IsWinner())
			{
				this.amountToWon += toWin;
				this.amountToPending = this.amountToPending - placedAmount;
			}
			else
			{
				this.amountToRisk += placedAmount;
				this.amountToPending = this.amountToPending - placedAmount;
			}
		}

		internal int NumberOfBet
		{
			get
			{
				return this.numberOfBets;
			}
		}
		internal decimal TotalToRisk
		{
			get
			{
				return this.totalToRisk;
			}
		}

		internal decimal TotalToWin
		{
			get
			{
				return this.totalToWin;
			}
		}

		internal decimal AmountToWon
		{
			get
			{
				return this.amountToWon;
			}
		}

		internal decimal AmountToRisk
		{
			get
			{
				return this.amountToRisk;
			}
		}

		internal decimal AmountToPending
		{
			get
			{
				return this.amountToPending;
			}
		}

		internal void Increment(Line line, Wager wager)
		{
			if (line == null) throw new ArgumentNullException(nameof(line));
			if (wager == null) throw new ArgumentNullException(nameof(wager));

			WagerAnswer answer = wager.ChosenAnswer;
			var originalLine = line.OriginalVersion;
			var placedAmount = wager.Risk;
			var toWin = wager.ToWin();
			this.amountToPending += placedAmount;
			this.totalToRisk += placedAmount;
			this.totalToWin += toWin;
			this.numberOfBets++;

			RiskStatistics stats;
			switch (answer)
			{
				case DrawAnswer da:
					if (!(line is MoneyDrawLine)) throw new GameEngineException("Line and wager does not correspond to Wager type");
					if (!this.detailStatistics.TryGetValue(originalLine, out stats))
					{
						stats = new ABRiskStatistics();
						this.detailStatistics.Add(originalLine, stats);
					}
					(stats as ABRiskStatistics).IncrementDraw(line, placedAmount, toWin);
					break;
				case ABAnswer ab:
					if (!(line is ABLine)) throw new GameEngineException("Line and wager does not correspond to Wager type");

					if (!this.detailStatistics.TryGetValue(originalLine, out stats))
					{
						stats = new ABRiskStatistics();
						this.detailStatistics.Add(originalLine, stats);
					}
					if (game.TeamA == ab.ChosenTeam)
					{
						(stats as ABRiskStatistics).IncrementTeamA(line, placedAmount, toWin);
					}
					else
					{
						(stats as ABRiskStatistics).IncrementTeamB(line, placedAmount, toWin);
					}
					break;
				case YesNoAnswer yN:
					if (!(line is YesNoLine)) throw new GameEngineException("Line and wager does not correspond to Wager type");

					if (!this.detailStatistics.TryGetValue(originalLine, out stats))
					{
						stats = new YesNoRiskStatistics();
						this.detailStatistics.Add(originalLine, stats);
					}
					if (yN == YesNoAnswer.YES)
					{
						(stats as YesNoRiskStatistics).IncrementYes(line, placedAmount, toWin);
					}
					else
					{
						(stats as YesNoRiskStatistics).IncrementNo(line, placedAmount, toWin);
					}
					break;
				case OverUnderAnswer oU:
					if (!(line is OverUnderLine)) throw new GameEngineException("Line and wager does not correspond to Wager type");

					if (!this.detailStatistics.TryGetValue(originalLine, out stats))
					{
						stats = new OverUnderRiskStatistics();
						this.detailStatistics.Add(originalLine, stats);
					}
					if (oU == OverUnderAnswer.OVER)
					{
						(stats as OverUnderRiskStatistics).IncrementOver(line, placedAmount, toWin);
					}
					else
					{
						(stats as OverUnderRiskStatistics).IncrementUnder(line, placedAmount, toWin);
					}
					break;
				case FixedAnswer fA:
					if (!(line is FixedLine)) throw new GameEngineException("Line and wager does not correspond to Wager type");

					if (!this.detailStatistics.TryGetValue(originalLine, out stats))
					{
						stats = new FixedRiskStatistics();
						this.detailStatistics.Add(originalLine, stats);
					}
					(stats as FixedRiskStatistics).Increment(line, fA.Ordinal, placedAmount, toWin);
					break;
				default:
					throw new GameEngineException("Answer type is not being handled");
			}
			stats.count++;
		}

		internal decimal PlacedFor(Line line, WagerAnswer answer)
		{
			if (line == null) throw new ArgumentNullException(nameof(line));
			if (answer == null) throw new ArgumentNullException(nameof(answer));

			RiskStatistics stats;
			var originalLine = line.OriginalVersion;
			if (!this.detailStatistics.TryGetValue(originalLine, out stats))
			{
				throw new GameEngineException($"This line {originalLine.ToString()} does not exist.");
			}
			switch (answer)
			{
				case DrawAnswer da:
					if (!(line is MoneyDrawLine)) throw new GameEngineException("Line and wager does not correspond to Wager type");

					return (stats as ABRiskStatistics).PlaceForDraw;
				case ABAnswer ab:
					if (!(line is ABLine)) throw new GameEngineException("Line and wager does not correspond to Wager type");

					if (game.TeamA == ab.ChosenTeam)
						return (stats as ABRiskStatistics).PlaceForTeamA;
					else
						return (stats as ABRiskStatistics).PlaceForTeamB;
				case YesNoAnswer yN:
					if (!(line is YesNoLine)) throw new GameEngineException("Line and wager does not correspond to Wager type");

					if (yN == YesNoAnswer.YES)
						return (stats as YesNoRiskStatistics).PlaceForYes;
					else
						return (stats as YesNoRiskStatistics).PlaceForNo;
				case OverUnderAnswer oU:
					if (!(line is OverUnderLine)) throw new GameEngineException("Line and wager does not correspond to Wager type");

					if (oU == OverUnderAnswer.OVER)
						return (stats as OverUnderRiskStatistics).PlaceForOver;
					else
						return (stats as OverUnderRiskStatistics).PlaceForUnder;
				case FixedAnswer fA:
					if (!(line is FixedLine)) throw new GameEngineException("Line and wager does not correspond to Wager type");

					return (stats as FixedRiskStatistics).PlaceFor(fA.Ordinal);
				default:
					break;
			}

			throw new GameEngineException($"Answer does not belong to this line {originalLine.ToString()}");
		}

		internal decimal PlacedFor(WagerAnswer answer)
		{
			if (answer == null) throw new ArgumentNullException(nameof(answer));

			decimal result = 0;
			foreach (var line in this.detailStatistics.Keys)
			{
				switch (answer)
				{
					case DrawAnswer da:
						if (line is MoneyDrawLine)
						{
							RiskStatistics stats = this.detailStatistics[line];

							result += (stats as ABRiskStatistics).PlaceForDraw;
						}
						break;
					case ABAnswer ab:
						if (line is ABLine)
						{
							RiskStatistics stats = this.detailStatistics[line];
							if (game.TeamA == (answer as ABAnswer).ChosenTeam)
								result += (stats as ABRiskStatistics).PlaceForTeamA;
							else
								result += (stats as ABRiskStatistics).PlaceForTeamB;
						}
						break;
					case YesNoAnswer yN:
						if (line is YesNoLine)
						{
							RiskStatistics stats = this.detailStatistics[line];
							if (yN == YesNoAnswer.YES)
								result += (stats as YesNoRiskStatistics).PlaceForYes;
							else
								result += (stats as YesNoRiskStatistics).PlaceForNo;
						}
						break;
					case OverUnderAnswer oU:
						if (line is OverUnderLine)
						{
							RiskStatistics stats = this.detailStatistics[line];
							if (oU == OverUnderAnswer.OVER)
								result += (stats as OverUnderRiskStatistics).PlaceForOver;
							else
								result += (stats as OverUnderRiskStatistics).PlaceForUnder;
						}
						break;
					case FixedAnswer fA:
						if (line is FixedLine)
						{
							RiskStatistics stats = this.detailStatistics[line];
							result += (stats as FixedRiskStatistics).PlaceFor(fA.Ordinal);
						}
						break;
					default:
						throw new GameEngineException("Unknown answer type");
				}
				
			}
			return result;
		}

		internal decimal ToPayFor(Line line, WagerAnswer answer)
		{
			if (line == null) throw new ArgumentNullException(nameof(line));
			if (answer == null) throw new ArgumentNullException(nameof(answer));

			RiskStatistics stats;
			var originalLine = line.OriginalVersion;
			if (!this.detailStatistics.TryGetValue(originalLine, out stats))
			{
				throw new GameEngineException($"This line {originalLine.ToString()} does not exist.");
			}

			switch (answer)
			{
				case DrawAnswer da:
					if (!(line is MoneyDrawLine)) throw new GameEngineException("Line and wager does not correspond to Wager type");

					return (stats as ABRiskStatistics).ToPayIfDrawWon;
				case ABAnswer ab:
					if (!(line is ABLine)) throw new GameEngineException("Line and wager does not correspond to Wager type");

					if (game.TeamA == ab.ChosenTeam)
						return (stats as ABRiskStatistics).ToPayIfTeamAWon;
					else
						return (stats as ABRiskStatistics).ToPayIfTeamBWon;
				case YesNoAnswer yN:
					if (!(line is YesNoLine)) throw new GameEngineException("Line and wager does not correspond to Wager type");

					if (yN == YesNoAnswer.YES)
						return (stats as YesNoRiskStatistics).ToPayIfYesWon;
					else
						return (stats as YesNoRiskStatistics).ToPayIfNoWon;
				case OverUnderAnswer oU:
					if (!(line is OverUnderLine)) throw new GameEngineException("Line and wager does not correspond to Wager type");

					if (oU == OverUnderAnswer.OVER)
						return (stats as OverUnderRiskStatistics).ToPayIfOverWon;
					else
						return (stats as OverUnderRiskStatistics).ToPayIfUnderWon;
				case FixedAnswer fA:
					if (!(line is FixedLine)) throw new GameEngineException("Line and wager does not correspond to Wager type");

					return (stats as FixedRiskStatistics).ToPayIfWon(fA.Ordinal);
				default:
					throw new GameEngineException("Unknown answer type");
			}

			throw new GameEngineException($"Answer does not belong to this line {originalLine.ToString()}");
		}

		internal decimal ToPayFor(WagerAnswer answer)
		{
			if (answer == null) throw new ArgumentNullException(nameof(answer));

			decimal result = 0;
			foreach (var line in this.detailStatistics.Keys)
			{
				switch (answer)
				{
					case DrawAnswer da:
						if (line is MoneyDrawLine)
						{
							RiskStatistics stats = this.detailStatistics[line];
							result += (stats as ABRiskStatistics).ToPayIfDrawWon;
						}
						break;
					case ABAnswer ab:
						if (line is ABLine)
						{
							RiskStatistics stats = this.detailStatistics[line];
							if (game.TeamA == ab.ChosenTeam)
								result += (stats as ABRiskStatistics).ToPayIfTeamAWon;
							else
								result += (stats as ABRiskStatistics).ToPayIfTeamBWon;
						}
						break;
					case YesNoAnswer yN:
						if (line is YesNoLine)
						{
							RiskStatistics stats = this.detailStatistics[line];
							if (yN == YesNoAnswer.YES)
								result += (stats as YesNoRiskStatistics).ToPayIfYesWon;
							else
								result += (stats as YesNoRiskStatistics).ToPayIfNoWon;
						}
						break;
					case OverUnderAnswer oU:
						if (line is OverUnderLine)
						{
							RiskStatistics stats = this.detailStatistics[line];
							if (oU == OverUnderAnswer.OVER)
								result += (stats as OverUnderRiskStatistics).ToPayIfOverWon;
							else
								result += (stats as OverUnderRiskStatistics).ToPayIfUnderWon;
						}
						break;
					case FixedAnswer fA:
						if (line is FixedLine)
						{
							RiskStatistics stats = this.detailStatistics[line];
							result += (stats as FixedRiskStatistics).ToPayIfWon(fA.Ordinal);
						}
						break;
				}
			}
			return result;
		}

		internal int CountFor(Line line)
		{
			if (line == null) throw new ArgumentNullException(nameof(line));

			RiskStatistics stats;
			var originalLine = line.OriginalVersion;
			if (!this.detailStatistics.TryGetValue(originalLine, out stats))
			{
				throw new GameEngineException($"This line {originalLine.ToString()} does not exist.");
			}

			return stats.Count;
		}

		internal int CountForTeamA(Line line)
		{
			if (line == null) throw new ArgumentNullException(nameof(line));

			RiskStatistics stats;
			var originalLine = line.OriginalVersion;
			if (!this.detailStatistics.TryGetValue(originalLine, out stats))
			{
				throw new GameEngineException($"This line {originalLine.ToString()} does not exist.");
			}

			return (stats as ABRiskStatistics).CountForTeamA;
		}
		internal int CountForTeamB(Line line)
		{
			if (line == null) throw new ArgumentNullException(nameof(line));

			RiskStatistics stats;
			var originalLine = line.OriginalVersion;
			if (!this.detailStatistics.TryGetValue(originalLine, out stats))
			{
				throw new GameEngineException($"This line {originalLine.ToString()} does not exist.");
			}

			return (stats as ABRiskStatistics).CountForTeamB;
		}
		internal int CountForDraw(Line line)
		{
			if (line == null) throw new ArgumentNullException(nameof(line));

			RiskStatistics stats;
			var originalLine = line.OriginalVersion;
			if (!this.detailStatistics.TryGetValue(originalLine, out stats))
			{
				throw new GameEngineException($"This line {originalLine.ToString()} does not exist.");
			}

			return (stats as ABRiskStatistics).CountForDraw;
		}

		internal int CountForYes(Line line)
		{
			if (line == null) throw new ArgumentNullException(nameof(line));

			RiskStatistics stats;
			var originalLine = line.OriginalVersion;
			if (!this.detailStatistics.TryGetValue(originalLine, out stats))
			{
				throw new GameEngineException($"This line {originalLine.ToString()} does not exist.");
			}
			return (stats as YesNoRiskStatistics).CountForYes;
		}

		internal int CountForNo(Line line)
		{
			if (line == null) throw new ArgumentNullException(nameof(line));

			RiskStatistics stats;
			var originalLine = line.OriginalVersion;
			if (!this.detailStatistics.TryGetValue(originalLine, out stats))
			{
				throw new GameEngineException($"This line {originalLine.ToString()} does not exist.");
			}
			return (stats as YesNoRiskStatistics).CountForNo;
		}

		internal int CountForOver(Line line)
		{
			if (line == null) throw new ArgumentNullException(nameof(line));

			RiskStatistics stats;
			var originalLine = line.OriginalVersion;
			if (!this.detailStatistics.TryGetValue(originalLine, out stats))
			{
				throw new GameEngineException($"This line {originalLine.ToString()} does not exist.");
			}
			return (stats as OverUnderRiskStatistics).CountForOver;
		}

		internal int CountForUnder(Line line)
		{
			if (line == null) throw new ArgumentNullException(nameof(line));

			RiskStatistics stats;
			var originalLine = line.OriginalVersion;
			if (!this.detailStatistics.TryGetValue(originalLine, out stats))
			{
				throw new GameEngineException($"This line {originalLine.ToString()} does not exist.");
			}
			return (stats as OverUnderRiskStatistics).CountForUnder;
		}

		internal int CountForFixed(Line line, string option)
		{
			if (line == null) throw new ArgumentNullException(nameof(line));

			RiskStatistics stats;
			var originalLine = line.OriginalVersion;
			if (!this.detailStatistics.TryGetValue(originalLine, out stats))
			{
				throw new GameEngineException($"This line {originalLine.ToString()} does not exist.");
			}
			var fl = (FixedLine)line;
			var answer = (FixedAnswer)fl.GetAnAnswer(option);
			return (stats as FixedRiskStatistics).Count(answer.Ordinal);
		}

		abstract class RiskStatistics
		{
			internal int count;
			protected List<Data> history;

			protected abstract class Data
			{
				internal Line lineVersion;
			}

			protected abstract Data GetNewDataInstance(Line line);

			internal int Count
			{
				get
				{
					return this.count;
				}
			}

			protected Data GetOrCreateData(Line line)
			{
				Data data;

				if (this.history == null)
				{
					data = GetNewDataInstance(line);
					this.history = new List<Data>();
					this.history.Add(data);
					data.lineVersion = line;
				}
				else
				{
					var last = this.history.Last();
					if (last.lineVersion == line)
					{
						data = last;
					}
					else
					{
						if (last.lineVersion.Version > line.Version)
						{
							throw new GameEngineException("Historical data must receive lines in timeline order");
						}
						data = GetNewDataInstance(line);
						data.lineVersion = line;
						this.history.Add(data);
					}
				}
				return data;
			}
		}

		class ABRiskStatistics : RiskStatistics
		{
			class ABData : Data
			{
				internal decimal toPayIfTeamAWon;
				internal decimal toPayIfTeamBWon;
				internal decimal placeForTeamA;
				internal decimal placeForTeamB;
				internal int countTeamA;
				internal int countTeamB;
				internal decimal toPayIfDrawWon;
				internal decimal placeForDraw;
				internal int countDraw;
			}

			protected override Data GetNewDataInstance(Line line)
			{
				return new ABData();
			}

			internal void IncrementTeamA(Line line, decimal placeForTeamA, decimal toPayIfTeamAWon)
			{
				if (line == null) throw new ArgumentNullException(nameof(line));
				if (!(line is ABLine)) throw new GameEngineException($"Line does not correspond to {nameof(ABRiskStatistics)} type");
				if (placeForTeamA <= 0) throw new GameEngineException($"Amount {placeForTeamA} must be greater than zero");

				var data = (ABData)base.GetOrCreateData(line);
				data.toPayIfTeamAWon += toPayIfTeamAWon;
				data.placeForTeamA += placeForTeamA;
				data.countTeamA++;
			}

			internal void IncrementTeamB(Line line, decimal placeForTeamB, decimal toPayIfTeamBWon)
			{
				if (line == null) throw new ArgumentNullException(nameof(line));
				if (!(line is ABLine)) throw new GameEngineException($"Line does not correspond to {nameof(ABRiskStatistics)} type");
				if (placeForTeamB <= 0) throw new GameEngineException($"Amount {placeForTeamB} must be greater than zero");

				var data = (ABData)base.GetOrCreateData(line);
				data.toPayIfTeamBWon += toPayIfTeamBWon;
				data.placeForTeamB += placeForTeamB;
				data.countTeamB++;
			}

			internal void IncrementDraw(Line line, decimal placeForDraw, decimal toPayIfDrawWon)
			{
				if (line == null) throw new ArgumentNullException(nameof(line));
				if (!(line is MoneyDrawLine)) throw new GameEngineException($"Line does not correspond to {nameof(ABRiskStatistics)} type");
				if (placeForDraw <= 0) throw new GameEngineException($"Amount {placeForDraw} must be greater than zero");

				var data = (ABData)base.GetOrCreateData(line);
				data.toPayIfDrawWon += toPayIfDrawWon;
				data.placeForDraw += placeForDraw;
				data.countDraw++;
			}

			internal int CountForTeamA
			{
				get
				{
					return this.history == null ? 0: this.history.Sum(x => (x as ABData).countTeamA);
				}
			}

			internal int CountForTeamB
			{
				get
				{
					return this.history == null ? 0 : this.history.Sum(x => (x as ABData).countTeamB);
				}
			}

			internal decimal PlaceForTeamA
			{
				get
				{
					return this.history == null ? 0: this.history.Sum(x => (x as ABData).placeForTeamA);
				}
			}

			internal decimal PlaceForTeamB
			{
				get
				{
					return this.history == null ? 0 : this.history.Sum(x => (x as ABData).placeForTeamB);
				}
			}

			internal decimal ToPayIfTeamAWon
			{
				get
				{
					return this.history == null ? 0 : this.history.Sum(x => (x as ABData).toPayIfTeamAWon);
				}
			}

			internal decimal ToPayIfTeamBWon
			{
				get
				{
					return this.history == null ? 0 : this.history.Sum(x => (x as ABData).toPayIfTeamBWon);
				}
			}

			internal int CountForDraw
			{
				get
				{
					return this.history == null ? 0 : this.history.Sum(x => (x as ABData).countDraw);
				}
			}


			internal decimal PlaceForDraw
			{
				get
				{
					return this.history == null ? 0 : this.history.Sum(x => (x as ABData).placeForDraw);
				}
			}


			internal decimal ToPayIfDrawWon
			{
				get
				{
					return this.history == null ? 0 : this.history.Sum(x => (x as ABData).toPayIfDrawWon);
				}
			}
		}

		class YesNoRiskStatistics : RiskStatistics
		{
			class YesNoData : Data
			{
				internal decimal toPayIfYesWon;
				internal decimal toPayIfNoWon;
				internal decimal placeForYes;
				internal decimal placeForNo;
				internal int countYes;
				internal int countNo;
			}

			protected override Data GetNewDataInstance(Line line)
			{
				return new YesNoData();
			}

			internal void IncrementYes(Line line, decimal placeForYes, decimal toPayIfYesWon)
			{
				if (line == null) throw new ArgumentNullException(nameof(line));
				if (!(line is YesNoLine)) throw new GameEngineException($"Line does not correspond to {nameof(YesNoRiskStatistics)} type");
				if (placeForYes <= 0) throw new GameEngineException($"Amount {placeForYes} must be greater than zero");

				var data = (YesNoData)base.GetOrCreateData(line);
				data.toPayIfYesWon += toPayIfYesWon;
				data.placeForYes += placeForYes;
				data.countYes++;
			}

			internal void IncrementNo(Line line, decimal placeForNo, decimal toPayIfNoWon)
			{
				if (line == null) throw new ArgumentNullException(nameof(line));
				if (!(line is YesNoLine)) throw new GameEngineException($"Line does not correspond to {nameof(YesNoRiskStatistics)} type");
				if (placeForNo <= 0) throw new GameEngineException($"Amount {placeForNo} must be greater than zero");

				var data = (YesNoData)base.GetOrCreateData(line);
				data.toPayIfNoWon += toPayIfNoWon;
				data.placeForNo += placeForNo;
				data.countNo++;
			}

			internal decimal PlaceForYes
			{
				get
				{
					return this.history == null ? 0 : this.history.Sum(x => (x as YesNoData).placeForYes);
				}
			}

			internal decimal PlaceForNo
			{
				get
				{
					return this.history == null ? 0 : this.history.Sum(x => (x as YesNoData).placeForNo);
				}
			}

			internal decimal ToPayIfYesWon
			{
				get
				{
					return this.history == null ? 0 : this.history.Sum(x => (x as YesNoData).toPayIfYesWon);
				}
			}

			internal decimal ToPayIfNoWon
			{
				get
				{
					return this.history == null ? 0 : this.history.Sum(x => (x as YesNoData).toPayIfNoWon);
				}
			}

			internal int CountForYes
			{
				get
				{
					return this.history == null ? 0 : this.history.Sum(x => (x as YesNoData).countYes);
				}
			}

			internal int CountForNo
			{
				get
				{
					return this.history == null ? 0 : this.history.Sum(x => (x as YesNoData).countNo);
				}
			}
		}

		class OverUnderRiskStatistics : RiskStatistics
		{
			class OverUnderData : Data
			{
				internal decimal toPayIfOverWon;
				internal decimal toPayIfUnderWon;
				internal decimal placeForOver;
				internal decimal placeForUnder;
				internal int countOver;
				internal int countUnder;
			}

			protected override Data GetNewDataInstance(Line line) 
			{
				return new OverUnderData();
			}

			internal void IncrementOver(Line line, decimal placeForOver, decimal toPayIfOverWon)
			{
				if (line == null) throw new ArgumentNullException(nameof(line));
				if (!(line is OverUnderLine)) throw new GameEngineException($"Line does not correspond to {nameof(OverUnderRiskStatistics)} type");
				if (placeForOver <= 0) throw new GameEngineException($"Amount {placeForOver} must be greater than zero");

				var data = (OverUnderData)base.GetOrCreateData(line);
				data.toPayIfOverWon += toPayIfOverWon;
				data.placeForOver += placeForOver;
				data.countOver++;
			}

			internal void IncrementUnder(Line line, decimal placeForUnder, decimal toPayIfUnderWon)
			{
				if (line == null) throw new ArgumentNullException(nameof(line));
				if (!(line is OverUnderLine)) throw new GameEngineException($"Line does not correspond to {nameof(OverUnderRiskStatistics)} type");
				if (placeForUnder <= 0) throw new GameEngineException($"Amount {placeForUnder} must be greater than zero");

				var data = (OverUnderData)base.GetOrCreateData(line);
				data.toPayIfUnderWon += toPayIfUnderWon;
				data.placeForUnder += placeForUnder;
				data.countUnder++;
			}

			internal decimal PlaceForOver
			{
				get
				{
					return this.history == null ? 0 : this.history.Sum(x => (x as OverUnderData).placeForOver);
				}
			}

			internal decimal PlaceForUnder
			{
				get
				{
					return this.history == null ? 0 : this.history.Sum(x => (x as OverUnderData).placeForUnder);
				}
			}

			internal decimal ToPayIfOverWon
			{
				get
				{
					return this.history == null ? 0 : this.history.Sum(x => (x as OverUnderData).toPayIfOverWon);
				}
			}

			internal decimal ToPayIfUnderWon
			{
				get
				{
					return this.history == null ? 0 : this.history.Sum(x => (x as OverUnderData).toPayIfUnderWon);
				}
			}

			internal int CountForOver
			{
				get
				{
					return this.history == null ? 0 : this.history.Sum(x => (x as OverUnderData).countOver);
				}
			}

			internal int CountForUnder
			{
				get
				{
					return this.history == null ? 0 : this.history.Sum(x => (x as OverUnderData).countUnder);
				}
			}
		}

		class FixedRiskStatistics : RiskStatistics
		{
			class FixedData : Data
			{
				internal decimal[] toPayIfWon;
				internal decimal[] placeFor;
				internal int[] count;
			}

			protected override Data GetNewDataInstance(Line line) 
			{
				var result = new FixedData();
				result.toPayIfWon = new decimal[(line as FixedLine).Count];
				result.placeFor = new decimal[(line as FixedLine).Count];
				result.count = new int[(line as FixedLine).Count];
				return result;
			}

			internal decimal PlaceForOtherThan(int ordinal)
			{
				if (this.history == null) return 0;

				decimal result = 0;
				foreach (var data in this.history)
				{
					result += (data as FixedData).placeFor.Where(x => x != ordinal).Sum();
				}

				return result;
			}

			internal void Increment(Line line, int ordinal, decimal placeFor, decimal toPayIfWon)
			{
				if (line == null) throw new ArgumentNullException(nameof(line));
				if (!(line is FixedLine)) throw new GameEngineException($"Line does not correspond to {nameof(FixedRiskStatistics)} type");
				if (placeFor <= 0) throw new GameEngineException($"Amount {placeFor} must be greater than zero");

				var data = (FixedData)base.GetOrCreateData(line as FixedLine);
				data.toPayIfWon[ordinal] += toPayIfWon;
				data.placeFor[ordinal] += placeFor;
				data.count[ordinal]++;
			}

			internal decimal ToPayIfWon(int ordinal)
			{
				if (this.history == null) return 0;

				decimal result = 0;
				foreach (var data in this.history)
				{
					result += (data as FixedData).toPayIfWon[ordinal];
				}

				return result;
			}

			internal decimal PlaceFor(int ordinal)
			{
				if (this.history == null) return 0;

				decimal result = 0;
				foreach (var data in this.history)
				{
					result += (data as FixedData).placeFor[ordinal];
				}

				return result;
			}

			internal int Count(int ordinal)
			{
				return this.history == null ? 0 : this.history.Sum(x => (x as FixedData).count[ordinal]);
			}
		}
	}
}
