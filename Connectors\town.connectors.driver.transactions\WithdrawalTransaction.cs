﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using town.connectors.drivers;

namespace Connectors.town.connectors.driver.transactions
{
    public sealed class WithdrawalTransaction : Result
    {
        public WithdrawalTransaction(int authorizationId, TransactionStatus status, string entity, PaymentMethod paymentMethod, string[] currencyIsoCode) :
			base(entity, paymentMethod, currencyIsoCode, TransactionType.Withdrawal)
        {
			AuthorizationId = authorizationId;
			Status = status;
		}

		public int AuthorizationId { get; }
		public TransactionStatus Status { get; }
	}
}
