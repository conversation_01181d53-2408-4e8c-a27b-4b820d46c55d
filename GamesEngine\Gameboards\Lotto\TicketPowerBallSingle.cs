﻿using GamesEngine.Bets;
using GamesEngine.Games.Lotto;
using System;
using System.Linq;

namespace GamesEngine.Gameboards.Lotto
{
    internal sealed class TicketPowerBallSingle : TicketPowerBall
    {
        
        internal TicketPowerBallSingle(Player player, Lottery lottery, DateTime drawDate, string number1, string number2, string number3, string number4, string number5, string powerBall, DateTime creationDate, decimal ticketCost, Prizes prizes) :
            base(player, lottery, drawDate, number1, number2, number3, number4, number5, powerBall, creationDate, ticketCost, prizes)
        {}

        internal TicketPowerBallSingle(Player player, Lottery lottery, DateTime drawDate, string numbers, DateTime creationDate, decimal ticketCost, Prizes prizes) :
            base(player, lottery, drawDate, numbers, creationDate, ticketCost, prizes)
        {}

        internal override void GenerateWagers()
        {
            var betAmount = BetAmount();
            GenerateWagers(betAmount);
        }

        internal override void GenerateWagers(decimal betAmount)
        {
            decimal prize = base.Prizes.MaxPrizeFor(TicketType.PBS);
            prize *= betAmount;

            var prizeToWin = prize - betAmount;
            prizeToWin = prizeToWin < 0 ? 0 : prizeToWin;

            var strNumbers = AsStringForAccounting();
            AddWager(betAmount, prizeToWin, strNumbers, SubTickets().ElementAt(0));
        }

        internal sealed override TicketType IdOfType()
        {
            return TicketType.PBS;
        }

        internal override string GameTypeForReports()
        {
            return QueryMakerOfHistoricalPicks.ID_POWERBALL;
        }

        internal override decimal PowerplayCost()
        {
            return 0;
        }
    }
}
