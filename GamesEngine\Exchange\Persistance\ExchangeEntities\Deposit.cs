﻿using System;
using System.Collections.Generic;

namespace ExchangeAPI.ExchangeEntities
{
    public partial class Deposit
    {
        public long Id { get; set; }
        public int? Authorizationid { get; set; }
        public int Fromcurrencyid { get; set; }
        public int Tocurrencyid { get; set; }
        public string Tocustomer { get; set; }
        public decimal Amount { get; set; }
        public long Batchnumber { get; set; }
        public decimal Gross { get; set; }
        public decimal Net { get; set; }
        public decimal Profit { get; set; }
        public long? Exchangerateid { get; set; }
        public DateTime Datecreated { get; set; }
        public long Whocreated { get; set; }
        public DateTime? Dateapproved { get; set; }
        public long? Whoapproved { get; set; }
        public DateTime? Daterejected { get; set; }
        public long? Whorejected { get; set; }
        public sbyte Approvals { get; set; }
        public sbyte Approvalsrequired { get; set; }
        public sbyte Rejections { get; set; }
        public string Description { get; set; }
        public bool Deleted { get; set; }
        public string Voucher { get; set; }
        public string Voucherurl { get; set; }
        public string Depositor { get; set; }
        public string Account { get; set; }
        public string Rejectionreason { get; set; }
        public long? Processorid { get; set; }
        public int Domain { get; set; }
        public string Journalentrydetailid { get; set; }

        public virtual Domains DomainNavigation { get; set; }
        public virtual Exchangerate Exchangerate { get; set; }
        public virtual Currency Fromcurrency { get; set; }
        public virtual Journalentry Journalentrydetail { get; set; }
        public virtual Currency Tocurrency { get; set; }
        public virtual Exchangeuser WhoapprovedNavigation { get; set; }
        public virtual Exchangeuser WhocreatedNavigation { get; set; }
        public virtual Exchangeuser WhorejectedNavigation { get; set; }
    }
}
