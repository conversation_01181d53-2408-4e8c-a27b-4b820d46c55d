﻿using Connectors.town.connectors.driver.transactions;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.commons;
using town.connectors.drivers;
using town.connectors.drivers.hades;
using static town.connectors.drivers.Result;

namespace Connectors.town.connectors.drivers.hades
{
	public class Authorization : ASIProcessorDriver
	{
		private const float VERSION = 1.0F;
		private HttpClient _postFreeFormTicketClient;
		private string SystemId;
		private string SystemPassword;
		private string ClerkId;

		public Authorization()
			: base(TransactionType.Sale, VERSION)
        {
        }

		public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			if (_postFreeFormTicketClient == null)
			{
				string companyBaseUrlServices = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
				_postFreeFormTicketClient = new HttpClient
				{
					BaseAddress = new Uri(companyBaseUrlServices),
					Timeout = TimeSpan.FromMinutes(10)
				};
			}

			if (CustomSettings.ThereArePendingChanges)
			{
				bool changeApplied = false;
				string companyBaseUrlServices = CustomSettings.Get(now, "CompanyBaseUrlServices", out changeApplied).AsString;
				if (changeApplied)
				{
					_postFreeFormTicketClient = new HttpClient
					{
						BaseAddress = new Uri(companyBaseUrlServices),
						Timeout = TimeSpan.FromMinutes(10)
					};
				}

				SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
				SystemPassword = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
				ClerkId = CustomSettings.Get(now, "CompanyClerkId").AsString;
			}


			var CustomerId = recordSet.Mappings["atAddress"];
			var Amount = recordSet.Mappings["purchaseTotal.Value"];
			var PurchaseBody = recordSet.Mappings["concept"];

			var result = await PurchaseTicketAsync(now, CustomerId.AsString, Amount.AsDecimal, PurchaseBody.AsString);

			AuthorizationTransaction auth;
			if (result > 0)
			{
				auth = new AuthorizationTransaction(result, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes);
				return (T)Convert.ChangeType(auth, typeof(T));
			}
			auth = new AuthorizationTransaction(result, TransactionStatus.DENIED, Entity, PaymentMethod, CurrencyIsoCodes);
			return (T)Convert.ChangeType(auth, typeof(T));
		}

		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			throw new NotImplementedException();
		}

		public override void Prepare(DateTime now)
		{
			CustomSettings.AddVariableParameter("atAddress");
			CustomSettings.AddVariableParameter("purchaseTotal.Value");
			CustomSettings.AddVariableParameter("concept");
		}
        private async Task<int> PurchaseTicketAsync(DateTime now, string customerId, decimal amount, string purchaseBody)
        {
            if (String.IsNullOrWhiteSpace(customerId)) throw new ArgumentNullException(nameof(customerId));
            if (amount <= 0) throw new Exception($"Amount {amount} is not valid to send request {nameof(PostFreeFormTicketAsync)}");

            Debug.WriteLine($"Accounting service {nameof(PostFreeFormTicketAsync)} received {nameof(customerId)}:{customerId} {nameof(amount)}:{amount}");
            var result = await PostFreeFormTicketAsync(now, customerId, amount, purchaseBody);
            return result;
        }

        private async Task<int> PostFreeFormTicketAsync(DateTime now, string customerId, decimal totalRisk, string purchaseBody)
		{
			if (String.IsNullOrWhiteSpace(customerId)) throw new ArgumentNullException(nameof(customerId));
			if (totalRisk <= 0) throw new Exception($"{nameof(totalRisk)} {totalRisk} is not valid to send request {nameof(PostFreeFormTicketAsync)}");

			var strTotal = totalRisk.ToString();
			var values = new PostFreeFormTicketBody()
			{
				SystemId = SystemId,
				SystemPassword = SystemPassword,
				ClerkId = ClerkId,
				CustomerId = customerId,
				TotalRisk = strTotal
			};
			const string url = "/v1/5dimesAPI/PostFreeFormTicket";
			var jsonString = await ASIJsonUtils.ToJsonAsync(values);
			var httpContent = new StringContent(jsonString, Encoding.UTF8, "application/json");

			string responseString = "";

			string valuesWithHiddenFields = await ASIJsonUtils.HideSensitiveDataAsync(values, Loggers.GetIntance().AccountingServicesASIPostFreeFormTicket);

			try
			{
				Loggers.GetIntance().AccountingServicesASIPostFreeFormTicket.Debug($@"url:{url} data:{valuesWithHiddenFields}");

				bool wasADeadlock = false;
				do
				{
					var response = await _postFreeFormTicketClient.PostAsync(url, httpContent);
					responseString = await response.Content.ReadAsStringAsync();
					if (responseString != null && responseString.Contains("error") && responseString.Contains("was deadlocked on lock"))
					{
						wasADeadlock = true;
						NotifyWarn(nameof(PostFreeFormTicketAsync), $"Url:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}\nPurchase Body: {purchaseBody}", $"Response threw a deadlock, retry in progress");
						await Task.Delay(100);
					}
					else
					{
						wasADeadlock = false;
					}
				}
				while (wasADeadlock);

				Loggers.GetIntance().AccountingServicesASIPostFreeFormTicket.Debug($@"response:{responseString}");
			}
			catch (Exception e)
			{
				Loggers.GetIntance().AccountingServicesASIPostFreeFormTicket.Error($@"url:{url} data:{valuesWithHiddenFields} type:{e.GetType()} error:{e.Message}", e);

				var extraErrorMessage = string.Empty;
				if (e.InnerException is HttpRequestException) extraErrorMessage = "There was a problem getting the response from server";
				InternalOnError(nameof(PostFreeFormTicketAsync), 1, e, $"Url:{url}", $"Request: {valuesWithHiddenFields}", $"Response: {responseString}", $"Purchase Body: {purchaseBody}", extraErrorMessage);

				return FAKE_DOCUMENT_NUMBER;
			}

			if (String.IsNullOrWhiteSpace(responseString))
			{
				NotifyWarn(nameof(PostFreeFormTicketAsync), $"Url:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}\nPurchase Body: {purchaseBody}", $"Response can not be empty");
				return FAKE_DOCUMENT_NUMBER;
			}
			else
			{
				var errorResponse = Commons.FromJson<JsonErrorResponse>(responseString);
				if (errorResponse != null)
				{
					if (errorResponse.Error.Message != "Risk is higher than customer's available balance")
					{
						NotifyWarn(nameof(PostFreeFormTicketAsync), $"Url:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}\nPurchase Body: {purchaseBody}", $"Response threw an error");
					}
					return FAKE_DOCUMENT_NUMBER;
				}
				else
				{
					var documentNumber = Convert.ToInt32(responseString);
					if (documentNumber <= 0)
					{
						NotifyWarn(nameof(PostFreeFormTicketAsync), $"Url:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}\nPurchase Body: {purchaseBody}", $"{nameof(documentNumber)} {documentNumber} is not valid");
						return FAKE_DOCUMENT_NUMBER;
					}
					return documentNumber;
				}
			}
		}
    }
}
