﻿using GamesEngine.MessageQueuing;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using town.connectors.drivers;
using town.connectors.drivers.fiero;

namespace GamesEngine.Business
{
    class WholeTransactionTypes: WholeCatalog
    {
        internal WholeTransactionTypes(Company company) : base(company)
        {
        }

        internal CatalogMember Add(bool itIsThePresent, string name)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (Exists(name)) throw new GameEngineException($"{nameof(name)} '{name}' is already used");

            nextConsecutive = NextConsecutive();
            var transaction = new ProcessorTransaction(nextConsecutive, name);
            Add(transaction);
            if (itIsThePresent && (Settings.Integration.UseKafka || Settings.Integration.UseKafkaForAuto))
            {
                var msg = new TransactionTypeAddMessage(nextConsecutive, name);
                Settings.Integration.Kafka.Send(itIsThePresent, Settings.Integration.Kafka.TopicForCatalog, msg);
            }

            return transaction;
        }

        override internal CatalogMember Add(int id, string name)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} {id} must be greater than 0");
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (Exists(name)) throw new GameEngineException($"{nameof(name)} '{name}' is already used");
            if (Exists(id)) throw new GameEngineException($"{nameof(id)} '{id}' is already used");

            if (id > nextConsecutive) nextConsecutive = id;
            var transaction = new ProcessorTransaction(id, name);
            members.Add(transaction);
            return transaction;
        }
    }

    [Puppet]
    internal class GroupOFTransactions : Objeto, IEnumerable<ProcessorTransaction>
    {
        private List<ProcessorTransaction> types;
        internal IEnumerable<ProcessorTransaction> Types => types;
        internal string TransactionsAsText => string.Join('_', types.Select(type => type.Name));

        public GroupOFTransactions()
        {
            types = new List<ProcessorTransaction>();
        }
        internal GroupOFTransactions Add(ProcessorTransaction transaction)
        {
            types.Add(transaction);
            return this;
        }

        internal bool Any(int typeId)
        {
            var result = types.Any(type => type.Id == typeId);
            return result;
        }
        internal bool Any(ProcessorTransaction transaction)
        {
            var result = types.Any(type => type == transaction);
            return result;
        }
        internal int Count 
        {
            get { return types.Count;  }
        }

        public IEnumerator<ProcessorTransaction> GetEnumerator()
        {
            return types.GetEnumerator();
        }

        IEnumerator IEnumerable.GetEnumerator()
        {
            return types.GetEnumerator();
        }

        internal bool Search(string name)
        {
            return types.FirstOrDefault(type => type.Name == name) != null;
        }

        internal bool MatchWith(string name, Type processorDriverType)
        {
            var result = types.FirstOrDefault(type => (processorDriverType != typeof(DepositThenLock) && type.Name == name) ||
                                                (processorDriverType == typeof(DepositThenLock) && type.Name == $"{TransactionType.Deposit} then Lock") ||
                                                (processorDriverType == typeof(DepositThenLockForMultipleAuthorizations) && type.Name.StartsWith($"{TransactionType.Deposit} then Lock")));
            return result != null;
        }
    }

    public class ProcessorTransaction : CatalogMember
    {
        string description;
        internal string Description 
        {
            get 
            { 
                return description; 
            }
            set
            {
                if (string.IsNullOrWhiteSpace(value)) throw new ArgumentNullException(nameof(value));
                if (value.Length > 200) throw new GameEngineException($"{nameof(description)} is too large");

                description = value;
            }
        }

        public ProcessorTransaction(int id, string name) : base(id, name)
        {

        }

    }
}
