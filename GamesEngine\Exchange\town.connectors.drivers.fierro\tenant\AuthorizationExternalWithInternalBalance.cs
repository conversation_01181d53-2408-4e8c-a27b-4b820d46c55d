﻿using Connectors.town.connectors.commons;
using Connectors.town.connectors.driver.transactions;
using GamesEngine.Finance;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using town.connectors.commons;
using static town.connectors.drivers.Result;

namespace town.connectors.drivers.fiero
{
    public class AuthorizationExternalWithInternalBalance : FieroTenantDriver
	{
		public AuthorizationExternalWithInternalBalance()
			: base(Tenant_Actions.AuthorizationInternal)
        {
        }

		public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			var result = await AuthorzationAsync(now, recordSet);
			
			return (T)Convert.ChangeType(result, typeof(T));
		}

		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			throw new NotImplementedException();
		}

		public override void Prepare(DateTime now)
		{
			CustomSettings.AddVariableParameter("actor");
			CustomSettings.AddVariableParameter("context");
			CustomSettings.AddVariableParameter("atAddress");
			CustomSettings.AddVariableParameter("purchaseTotal");
			CustomSettings.AddVariableParameter("storeId");
			CustomSettings.AddVariableParameter("concept");
			CustomSettings.AddVariableParameter("referenceNumber");
			CustomSettings.AddVariableParameter("accountNumber");
			CustomSettings.AddVariableParameter("useless");
			CustomSettings.AddVariableParameter("processorId");
			CustomSettings.AddVariableParameter("who");
			CustomSettings.AddVariableParameter("fragmentInformation");
			CustomSettings.AddVariableParameter("toWinsByDrawAndNumber");

		}
		private async Task<AuthorizationTransaction> AuthorzationAsync(DateTime now, CustomSettings.RecordSet recordSet)
		{
			var Actor = recordSet.Mappings["actor"];
			var Context = recordSet.Mappings["context"];
			var AtAddress = recordSet.Mappings["atAddress"];
			var PurchaseTotalValue = recordSet.Mappings["purchaseTotal"];
			var StoreId = recordSet.Mappings["storeId"];
			var Concept = recordSet.Mappings["concept"];
			var ReferenceNumber = recordSet.Mappings["referenceNumber"];
			var AccountNumber = recordSet.Mappings["accountNumber"];
            var Useless = recordSet.Mappings["useless"];
            var ProcessorId = recordSet.Mappings["processorId"];
			var Who = recordSet.Mappings["who"];
            var FragmentInformation = recordSet.Mappings["fragmentInformation"];
			var ToWinsByDrawAndNumber = recordSet.Mappings["toWinsByDrawAndNumber"];

			var actor = Actor.As<RestAPISpawnerActor>();
			var context = Context.As<HttpContext>();
			string atAddress = AtAddress.AsString;
			var purchaseTotal = PurchaseTotalValue.As<Currency>();
			int storeId = StoreId.AsInt;
			string concept = Concept.AsString;
			string reference = ReferenceNumber.AsString;
			string accountNumber = AccountNumber.AsString;
            var useless = Useless.AsDateTime;
            int processorId = ProcessorId.AsInt;
			string who = Who.AsString;
            var fragmentInformation = FragmentInformation.As<FragmentInformation>();
			var toWinsByDrawAndNumber = ToWinsByDrawAndNumber.As<List<ToWinByDrawAndNumber>>();
			var result = await PurchaseAsync(actor, context, atAddress, purchaseTotal, storeId, concept, reference, accountNumber, useless, processorId, who, fragmentInformation, toWinsByDrawAndNumber);
			return result;
		}

		async Task<AuthorizationTransaction> PurchaseAsync(RestAPISpawnerActor actor, HttpContext context, string atAddress, Currency purchaseTotal, int storeId, string concept, string reference, 
			string accountNumber, DateTime useless, int processorId, string who, FragmentInformation fragmentInformation, IEnumerable<ToWinByDrawAndNumber> toWinsByDrawAndNumber)
		{
            int authorization = Authorization.FAKE_TICKET_NUMBER;
            try
			{
				var hasAccountNumber = !string.IsNullOrWhiteSpace(accountNumber);
				string command = hasAccountNumber ? $@"
					{{
						print atAddress.HasEnoughFor('{accountNumber}', Currency('{purchaseTotal.CurrencyCode}',{purchaseTotal.Value})) hasEnough;
					}}" : 
					$@"
					{{
						print atAddress.AnyAccountHasEnoughFor(Currency('{purchaseTotal.CurrencyCode}',{purchaseTotal.Value})) hasEnough;
					}}";
				var result = await actor.PerformQryAsync(atAddress, context, command);
				if (!(result is OkObjectResult))
				{
					return new AuthorizationTransaction(Authorization.FAKE_TICKET_NUMBER, TransactionStatus.DENIED, Entity, PaymentMethod, CurrencyIsoCodes, AuthorizationResponseCode.AuthorizationFail);
				}

				string jsonResult = (result as ObjectResult).Value.ToString();
				HasEnoughResponse response = JsonConvert.DeserializeObject<HasEnoughResponse>(jsonResult);

				if (response.HasEnough)
				{
					StringBuilder authorizationScript = new StringBuilder();
                    StringBuilder commandForAuthorizations = new StringBuilder();
                    StringBuilder risks = new StringBuilder();
                    bool allRisksAreEquals = true;
                    var lastIndex = toWinsByDrawAndNumber.Count() - 1;
                    var firstRisk = toWinsByDrawAndNumber.ElementAt(0).risk;
                    AuthorizationsNumbers.BuildCommand(toWinsByDrawAndNumber.Select(x => x.ticketId), ref commandForAuthorizations);
                    foreach (var toWinByDrawAndNumber in toWinsByDrawAndNumber)
                    {
						if (!int.TryParse(toWinByDrawAndNumber.ticketId, out authorization)) return new AuthorizationTransaction(Authorization.FAKE_TICKET_NUMBER, TransactionStatus.DENIED, Entity, PaymentMethod, CurrencyIsoCodes, AuthorizationResponseCode.AuthorizationFail);
                        if (firstRisk != toWinByDrawAndNumber.risk) allRisksAreEquals = false;

                        risks.Append(toWinByDrawAndNumber.risk);
                        if (toWinByDrawAndNumber != toWinsByDrawAndNumber.ElementAt(lastIndex))
                        {
                            risks.Append("','");
                        }
                    }
                    if (authorization != Authorization.FAKE_TICKET_NUMBER && authorization > 0)
                    {
                        var normalizedUseless = useless.ToString("MM/dd/yyyy HH:mm:ss");
                        if (hasAccountNumber)
                        {
							if (allRisksAreEquals)
							{
								authorizationScript.Append("atAddress.CreateAuthorization(itIsThePresent,Now,Currency('").Append(purchaseTotal.CurrencyCode).Append("', ").Append(firstRisk).Append("), auths.Numbers, store, '").Append(concept).Append("', '").Append(reference).Append("', '").Append(accountNumber).Append("', ").Append(normalizedUseless).Append(", ").Append(processorId).Append(", '").Append(who).Append("');").AppendLine();
							}
                            else
							{
                                authorizationScript.Append("atAddress.CreateAuthorization(itIsThePresent,Now,'").Append(purchaseTotal.CurrencyCode).Append("', {'").Append(risks).Append("'}, auths.Numbers, store, '").Append(concept).Append("', '").Append(reference).Append("', '").Append(accountNumber).Append("', ").Append(normalizedUseless).Append(", ").Append(processorId).Append(", '").Append(who).Append("');").AppendLine();
                            }
                        }
                        else
                        {
							if (allRisksAreEquals)
							{
								authorizationScript.Append("atAddress.CreateAuthorization(itIsThePresent,Now,Currency('").Append(purchaseTotal.CurrencyCode).Append("', ").Append(firstRisk).Append("), auths.Numbers, store, '").Append(concept).Append("', '").Append(reference).Append("', account, ").Append(normalizedUseless).Append(", ").Append(processorId).Append(", '").Append(who).Append("');").AppendLine();
							}
                            else
							{
                                authorizationScript.Append("atAddress.CreateAuthorization(itIsThePresent,Now,'").Append(purchaseTotal.CurrencyCode).Append("', {'").Append(risks).Append("'}, auths.Numbers, store, '").Append(concept).Append("', '").Append(reference).Append("', account, ").Append(normalizedUseless).Append(", ").Append(processorId).Append(", '").Append(who).Append("');").AppendLine();
                            }
                        }
                    }
                    else
                    {
                        return new AuthorizationTransaction(Authorization.FAKE_TICKET_NUMBER, TransactionStatus.DENIED, Entity, PaymentMethod, CurrencyIsoCodes, AuthorizationResponseCode.AuthorizationFail);
                    }

                    command = hasAccountNumber ? $@"
						{{
							store = company.Sales.StoreById({storeId});
							{commandForAuthorizations}
							{authorizationScript}
						}}" : $@"
						{{
							balance = atAddress.CreateBalanceIfNotExists('{purchaseTotal.CurrencyCode}');
							Eval('available = '+balance.Available+';');
							balance.SetInitialBalance(itIsThePresent, available);
							store = company.Sales.StoreById({storeId});
							account = atAddress.SearchAccountWithBalanceGreaterThan(Currency('{purchaseTotal.CurrencyCode}',{purchaseTotal.Value}));
                            {commandForAuthorizations}
							{authorizationScript}
						}}";
					result = await actor.PerformCmdAsync(atAddress, context, command);
					if (!(result is OkObjectResult))
					{
						return new AuthorizationTransaction(Authorization.FAKE_TICKET_NUMBER, TransactionStatus.DENIED, Entity, PaymentMethod, CurrencyIsoCodes, AuthorizationResponseCode.AuthorizationFail);
					}
					return new AuthorizationTransaction(int.Parse(toWinsByDrawAndNumber.First().ticketId), TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes, AuthorizationResponseCode.OK);
					
				}
				else
                {
					return new AuthorizationTransaction(Authorization.FAKE_TICKET_NUMBER, TransactionStatus.DENIED, Entity, PaymentMethod, CurrencyIsoCodes, AuthorizationResponseCode.InsufficientFunds);
				}
			}
			catch (Exception e)
			{
				ErrorsSender.Send(e, $"atAddress:{atAddress}", $"purchaseTotal:{purchaseTotal.CurrencyCode.ToString()}{purchaseTotal.Value}", $"storeSequence:{storeId.ToString()}", $"concept:{concept}", $"reference:{reference}");
				return new AuthorizationTransaction(Authorization.FAKE_TICKET_NUMBER, TransactionStatus.DENIED, Entity, PaymentMethod, CurrencyIsoCodes, AuthorizationResponseCode.AuthorizationFail);
			}
		}

		class HasEnoughResponse
		{
			public bool HasEnough { get; set; }
		}
	}
}
