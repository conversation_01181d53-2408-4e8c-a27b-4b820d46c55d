﻿using GamesEngine;
using GamesEngine.Finance;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Mvc;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace town.connectors.drivers.fiero
{
	public class FragmentAnAuthorizationAppended : FieroTenantDriver
	{
		public FragmentAnAuthorizationAppended() : base(Tenant_Actions.Fragment)
		{
		}
		public override void Prepare(DateTime now)
		{
			CustomSettings.AddVariableParameter("actor");
			CustomSettings.AddVariableParameter("atAddress");
			CustomSettings.AddVariableParameter("authorizationNumber");
			CustomSettings.AddVariableParameter("theLowestFragmentNumber");
			CustomSettings.AddVariableParameter("theHighestFragmentNumber");
			CustomSettings.AddVariableParameter("fragmentsChunks");
			//CustomSettings.Prepare();
		}

		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			var Actor = recordSet.Mappings["actor"];
			var AtAddress = recordSet.Mappings["atAddress"];
			var AuthorizationNumber = recordSet.Mappings["authorizationNumber"];
			var TheLowestFragmentNumber = recordSet.Mappings["theLowestFragmentNumber"];
			var TheHighestFragmentNumber = recordSet.Mappings["theHighestFragmentNumber"];
			var FragmentsChunks = recordSet.Mappings["fragmentsChunks"];

			var actor = Actor.As<RestAPISpawnerActor>();
			string atAddress = AtAddress.AsString;
			int authorizationNumber = AuthorizationNumber.AsInt;
			int theLowestFragmentNumber = TheLowestFragmentNumber.AsInt;
			int theHighestFragmentNumber = TheHighestFragmentNumber.AsInt;
			var fragmentsChunks = FragmentsChunks.As<FragmentsChunks>();

			AppendFragmentAuthorization(actor, atAddress, authorizationNumber, theLowestFragmentNumber, theHighestFragmentNumber, fragmentsChunks);
			return (T)Convert.ChangeType(null, typeof(T));

		}
		public override Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet) 
        {
            throw new NotImplementedException();
        }
        
        void AppendFragmentAuthorization(RestAPISpawnerActor actor, string atAddress, int authorizationNumber, int theLowestFragmentNumber, int theHighestFragmentNumber, FragmentsChunks fragmentsChunks)
		{
			bool fistIteration = true;
			bool lastIteration = false;
			int iteration = 0;
			List<string> commands = new List<string>();
			var countOfChunks = fragmentsChunks.AmountOfChunks();
            var isMultipleAuthorization = authorizationNumber == -1;
            foreach (Fragments fragments in fragmentsChunks.List())
			{
				iteration++;
				lastIteration = iteration == countOfChunks;

				StringBuilder commandBuilder = new StringBuilder();
                commandBuilder.AppendLine("{");
                if (isMultipleAuthorization)
                {
                    AuthorizationsNumbers.BuildCommand(fragments.TicketNumbers, ref commandBuilder);
                    commandBuilder.Append("authorizations = atAddress.GetAuthorizations(auths.Numbers);").AppendLine();
                    commandBuilder.Append("authorizations.InitFragmentCreation();").AppendLine();
                }
                else
                {
                    commandBuilder.Append("authorization = atAddress.GetAuthorization(").Append(authorizationNumber).Append(");").AppendLine();
                    if (fistIteration)
                    {
                        fistIteration = false;
                        commandBuilder.Append("authorization.InitFragmentCreation(").Append(theLowestFragmentNumber).Append(", ").Append(theHighestFragmentNumber).Append(");").AppendLine();
                    }
                }

                decimal fragmentsRisk = fragments.FragmentsRisk;
				decimal fragmentsTowin = fragments.FragmentsTowin;
				List<string> fragmentsReferences = fragments.FragmentsReferences;
				var escapedFragmentsDescription = fragments.BetDescriptions.Select(description => EscapeSingleQuotes(description));
				List<string> fragmentsToWins = fragments.ToWins;
				List<string> fragmentsToRisks = fragments.Risks;
				bool hasSameToWinAmounts = fragments.HasSameToWinAmounts;
				bool hasSameRiskAmounts = fragments.HasSameRiskAmounts;

				if (hasSameToWinAmounts && hasSameRiskAmounts)
				{
                    if (isMultipleAuthorization)
                    {
                        commandBuilder.Append("authorizations.AppendFragments(").Append("{'").Append(string.Join("','", fragmentsReferences)).Append("'}, {'").Append(string.Join("','", escapedFragmentsDescription)).Append("'}, ").Append(fragmentsRisk).Append(", ").Append(fragmentsTowin).Append(");").AppendLine();
                    }
                    else
                    {
                        commandBuilder.Append("authorization.AppendFragments(").Append("{'").Append(string.Join("','", fragmentsReferences)).Append("'}, {'").Append(string.Join("','", escapedFragmentsDescription)).Append("'}, ").Append(fragmentsRisk).Append(", ").Append(fragmentsTowin).Append(");").AppendLine();
                    }
				}
				else if (!hasSameToWinAmounts && hasSameRiskAmounts)
				{
                    if (isMultipleAuthorization)
                    {
                        commandBuilder.Append("authorizations.AppendFragments(").Append("{'").Append(string.Join("','", fragmentsReferences)).Append("'}, {'").Append(string.Join("','", escapedFragmentsDescription)).Append("'}, ").Append(fragmentsRisk).Append(", {").Append(string.Join(",", fragmentsToWins)).Append("});").AppendLine();
                    }
                    else
                    {
                        commandBuilder.Append("authorization.AppendFragments(").Append("{'").Append(string.Join("','", fragmentsReferences)).Append("'}, {'").Append(string.Join("','", escapedFragmentsDescription)).Append("'}, ").Append(fragmentsRisk).Append(", {").Append(string.Join(",", fragmentsToWins)).Append("});").AppendLine();
                    }
				}
				else if (hasSameToWinAmounts && !hasSameRiskAmounts)
				{
                    if (isMultipleAuthorization)
                    {
                        commandBuilder.Append("authorizations.AppendFragments(").Append("{'").Append(string.Join("','", fragmentsReferences)).Append("'}, {'").Append(string.Join("','", escapedFragmentsDescription)).Append("'}, {").Append(string.Join(",", fragmentsToRisks)).Append("}, ").Append(fragmentsTowin).Append(");").AppendLine();
                    }
                    else
                    {
                        commandBuilder.Append("authorization.AppendFragments(").Append("{'").Append(string.Join("','", fragmentsReferences)).Append("'}, {'").Append(string.Join("','", escapedFragmentsDescription)).Append("'}, {").Append(string.Join(",", fragmentsToRisks)).Append("}, ").Append(fragmentsTowin).Append(");").AppendLine();
                    }
				}
				else if (!hasSameToWinAmounts && !hasSameRiskAmounts)
				{
                    if (isMultipleAuthorization)
                    {
                        commandBuilder.Append("authorizations.AppendFragments(").Append("{'").Append(string.Join("','", fragmentsReferences)).Append("'}, {'").Append(string.Join("','", escapedFragmentsDescription)).Append("'}, {").Append(string.Join(",", fragmentsToRisks)).Append("}, {").Append(string.Join(",", fragmentsToWins)).Append("});").AppendLine();
                    }
                    else
                    {
                        commandBuilder.Append("authorization.AppendFragments(").Append("{'").Append(string.Join("','", fragmentsReferences)).Append("'}, {'").Append(string.Join("','", escapedFragmentsDescription)).Append("'}, {").Append(string.Join(",", fragmentsToRisks)).Append("}, {").Append(string.Join(",", fragmentsToWins)).Append("});").AppendLine();
                    }
				}
				else
				{
					throw new GameEngineException($"There is no valid option for risk and towin combination.");
				}

                if (isMultipleAuthorization)
                {
                    commandBuilder.Append("authorizations.CommitFragmentCreation();").AppendLine();
                }
                else
                {
                    if (lastIteration) commandBuilder.Append("authorization.CommitFragmentCreation();").AppendLine();
                }

                commandBuilder.AppendLine("}");
                commands.Add(commandBuilder.ToString());
			}

			foreach (string command in commands)
			{
				var commandResult = actor.PerformCmd(atAddress, command);
				if (!(commandResult is OkObjectResult))
				{
					ErrorsSender.Send($"authorization:{authorizationNumber}.\nCount {fragmentsChunks.TotalAmountOfFragments()}.\n failed command:{command}.\n complete command:{string.Join(" | ", commands)}",
						"Fragment creation fails.");

					throw new GameEngineException($"failure in command:{command}");
				}
			}
		}

        private static string EscapeSingleQuotes(string input)
        {
            if (string.IsNullOrEmpty(input))
            {
                return input;
            }

            var stringBuilder = new StringBuilder(input.Length);
            foreach (char c in input)
            {
                if (c == '\'')
                {
                    stringBuilder.Append("\\'");
                }
                else
                {
                    stringBuilder.Append(c);
                }
            }

            return stringBuilder.ToString();
        }
    }
}
