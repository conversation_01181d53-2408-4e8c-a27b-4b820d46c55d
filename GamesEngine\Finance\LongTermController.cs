﻿using Cassandra;
using GamesEngine.Business;
using System;
using System.Collections.Generic;
using System.Linq;
using static GamesEngine.Finance.LongTermControllerUtils;

namespace GamesEngine.Finance
{

    public static class LongTermControllerUtils
    {
        private static string[] nombresEnum = Enum.GetNames(typeof(Movement.type));
        public static int posicionDelMovement(string movementTxt)
        {
            return Array.IndexOf(nombresEnum, movementTxt);
        }

        public struct AuthorizationFragmentStruct
        {
            public int AUTHORIZATION_ID { get; internal set; }
            public int FRAGMENT_ID { get; internal set; }
            public int STATUS { get; internal set; }
            public decimal RISK { get; internal set; }
            public string REFERENCE { get; internal set; }
            public string DESCRIPTION { get; internal set; }
            public decimal TO_WIN { get; internal set; }
            public decimal ADJUSTED_WIN { get; internal set; }
            public decimal ADJUSTED_LOSS { get; internal set; }
            public int CURRENCY { get; internal set; }
            public int REASON { get; internal set; }
            public int EDIT_MODE { get; internal set; }
        }

        public struct MovementStruct
        {
            public DateTime DAY { get; internal set; }
            public int SOURCE { get; internal set; }
            public int MOVEMENT { get; internal set; }
            public decimal AMOUNT { get; internal set; }
            public decimal NEWBALANCE { get; internal set; }
            public decimal NEWLOCKBALANCE { get; internal set; }
            public string ACCOUNT_NUMBER { get; internal set; }
            public int WHO { get; internal set; }
            public string DOCUMENTNUMBER { get; internal set; }
            public string REFERENCE { get; internal set; }
            public string CONCEPT { get; internal set; }
            public int STORE { get; internal set; }
        }
    }

    public class LongTermController
    {
        private static Cluster cluster;
        private static LongTermController instance;

        private static string HOST_NAME = "localhost";
        private static string KEY_SPACE = "ncubo_keyspace";

        public static bool ENABLED { get; private set; } = false;
        public static int MonthsToArchivedMovements { get; private set; } = Int32.MaxValue;

        private LongTermController(string HostnameString)
        {
            cluster = Cluster.Builder().AddContactPoint(HostnameString).Build();
        }

        public static LongTermController Instance()
        {
            if (string.IsNullOrWhiteSpace(HOST_NAME)) throw new ArgumentNullException(nameof(HOST_NAME));
            if (string.IsNullOrWhiteSpace(KEY_SPACE)) throw new ArgumentNullException(nameof(KEY_SPACE));

            if (instance == null)
            {
                instance = new LongTermController(HOST_NAME);
            }
            return instance;
        }

        public void AllowController(bool allow)
        {
            ENABLED = allow;
        }

        public static void ConfigureLongTerm(string hostName, string keySpace, bool useCassandra, int monthsToArchivedMovements)
        {
            if (string.IsNullOrWhiteSpace(hostName)) throw new ArgumentNullException(nameof(hostName));
            if (string.IsNullOrWhiteSpace(keySpace)) throw new ArgumentNullException(nameof(keySpace));
            if (monthsToArchivedMovements <= 0) throw new ArgumentNullException(nameof(monthsToArchivedMovements));

            HOST_NAME = hostName;
            KEY_SPACE = keySpace;
            ENABLED = useCassandra;
            MonthsToArchivedMovements = monthsToArchivedMovements;
        }

        public static void ReloadInstance(string hostName, string keySpace)
        {
            HOST_NAME = hostName;
            KEY_SPACE = keySpace;
            instance = null;
            _ = Instance();
        }

        private static bool CqlRunQuery(string cqlQuery)
        {
            if (!ENABLED) throw new GameEngineException("Cluster is not available");

            bool result = true;

            try
            {
                using (ISession session = cluster.Connect(KEY_SPACE))
                {
                    _ = session.Execute(cqlQuery);
                }
            }
            catch
            {
                result = false;
            }

            return result;
        }

        private T QueryGetSingleValue<T>(string cqlQuery)
        {
            if (!ENABLED) throw new Exception("Clustes is not available");

            try
            {
                using (ISession session = cluster.Connect(KEY_SPACE))
                {
                    RowSet responseRow = session.Execute(cqlQuery);
                    Row row = responseRow.GetRows().First();
                    return row.GetValue<T>(0);
                }
            }
            catch
            {
            }

            throw new Exception("Error while getting results");
        }

        public bool ExitsTable(string tableName)
        {
            if (string.IsNullOrWhiteSpace(tableName)) throw new ArgumentNullException(nameof(tableName));

            if (ENABLED)
            {
                string fixedTableName = tableName.ToLower();

                string query = $"SELECT COUNT(table_name) FROM system_schema.tables WHERE keyspace_name = '{KEY_SPACE}' AND table_name = '{fixedTableName}';";
                return QueryGetSingleValue<Int64>(query) == 1;
            }

            return false;
        }

        public string CreateStorageMovements(string tableName)
        {
            return $"CREATE TABLE {tableName}(DAY TIMESTAMP, SOURCE INT, MOVEMENT INT, AMOUNT DECIMAL, NEWBALANCE DECIMAL, NEWLOCKBALANCE DECIMAL, ACCOUNT_NUMBER TEXT, WHO INT, DOCUMENTNUMBER TEXT, REFERENCE TEXT, CONCEPT TEXT, STORE INT, PRIMARY KEY(DOCUMENTNUMBER));";
        }

        public bool SaveMovementsLongTerm(string tableName, IEnumerable<MovementStruct> allMovements)
        {
            if (string.IsNullOrWhiteSpace(tableName)) throw new ArgumentNullException(nameof(tableName));
            if (allMovements == null) throw new ArgumentNullException(nameof(allMovements));

            bool result = true;

            if (!ENABLED) return result;

            string fixedTableName = tableName.ToLower();

            if (!ExitsTable(fixedTableName))
            {
                string query = CreateStorageMovements(fixedTableName);
                result = CqlRunQuery(query);
            }

            if (result) try
                {
                    using (ISession session = cluster.Connect(KEY_SPACE))
                    {
                        foreach (MovementStruct movement in allMovements)
                        {
                            var statement = new SimpleStatement($"INSERT INTO {fixedTableName}(DAY, SOURCE, MOVEMENT, AMOUNT, NEWBALANCE, NEWLOCKBALANCE, ACCOUNT_NUMBER, WHO, DOCUMENTNUMBER, REFERENCE, CONCEPT, STORE) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                                movement.DAY,
                                movement.SOURCE,
                                movement.MOVEMENT,
                                movement.AMOUNT,
                                movement.NEWBALANCE,
                                movement.NEWLOCKBALANCE,
                                movement.ACCOUNT_NUMBER,
                                movement.WHO,
                                movement.DOCUMENTNUMBER,
                                movement.REFERENCE,
                                movement.CONCEPT,
                                movement.STORE
                            );
                            session.Execute(statement);
                        }
                    }
                }
                catch
                {
                    result = false;
                }
            return result;
        }

        public int AddMovement(List<Movement> movements, int countMovements, int sourceNumber, string atAddress, int storeId, Currencies.CODES currencyCode, DateTime startDate, DateTime endDate, int whoId, int initialIndex, int amountOfRows, int movementTypeId)
        //public MovementsFiltered ListMovement(string tableName, int storeId, Currencies.CODES currencyCode, DateTime startDate, DateTime endDate, int whoId, int initialIndex, int amountOfRows, int movementTypeId)
        {
            string tableName = MovementStorage.GetTableName(atAddress, currencyCode.ToString());
            string cqlQuery = $"SELECT DAY, SOURCE, MOVEMENT, AMOUNT, NEWBALANCE, NEWLOCKBALANCE, ACCOUNT_NUMBER, WHO, DOCUMENTNUMBER, REFERENCE, CONCEPT, STORE FROM {tableName}";

            if(movements == null) movements = new List<Movement>();

            if (!ENABLED) throw new Exception("Clustes is not available");

            try
            {
                using (ISession session = cluster.Connect(KEY_SPACE))
                {
                    RowSet responseRow = session.Execute(cqlQuery);
                    IEnumerable<Row> rows = responseRow.GetRows();
                    foreach (Row row in rows)
                    {
                        int index = 0;
                        DateTime day = row.GetValue<DateTime>(index++);
                        int sourceAsInt = (int)row.GetValue<Int64>(index++);
                        string movement = row.GetValue<string>(index++);
                        decimal amount = row.GetValue<decimal>(index++);
                        decimal newBalance = row.GetValue<decimal>(index++);
                        string documentNumber = row.GetValue<string>(index++);

                        string who = row.GetValue<string>(index++);//?Posiblemente este en Null

                        int store = (int)row.GetValue<Int64>(index++);
                        string concept = row.GetValue<string>(index++);
                        string reference = row.GetValue<string>(index++);

                        countMovements = (int)row.GetValue<Int64>(index++);//Mejor manejar esto desde C#

                        decimal newLock = row.GetValue<decimal>(index++);
                        string accountNumber = row.GetValue<string>(index++);

                        Movement.type type;
                        bool result = Enum.TryParse(movement, true, out type);
                        if (!result)
                        {
                            throw new GameEngineException($@"There is no Movement.type for type {movement}");
                        }

                        Currency currency = Currency.Factory(currencyCode.ToString(), amount);

                        Movement m = null;
                        bool isDebit = MovementStorage.ItsADebitMovement(type);
                        if (isDebit)
                        {
                            m = Movement.GenerateADebitMovement(sourceAsInt, atAddress, day, type, currency, newBalance, who, documentNumber, storeId, concept, reference, newLock, accountNumber, WholePaymentProcessor.NoPaymentProcessor);
                        }
                        else
                        {
                            m = Movement.GenerateACreditMovement(sourceAsInt, atAddress, day, type, currency, newBalance, who, documentNumber, storeId, concept, reference, newLock, accountNumber, WholePaymentProcessor.NoPaymentProcessor);
                        }

                        movements.Add(m);
                    }
                }
            }
            catch
            {
            }
            return countMovements;
        }
    }
}
