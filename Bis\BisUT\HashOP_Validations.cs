using Bis;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using static Bis.RiskIndex;

namespace BisUT
{
	[TestClass]
	public class HashOP_Validations
	{
		private Parameters parameters;
		private Variable amount_variable;
		private Variable name_variable;
		private RiskRule rule_id_13_Int_10_IsOP;
		private RiskRule rule_id_12_Int_11_IsOP;
		private RiskRule rule_id_15_Int_11_and_string_hello_IsOP;
		private RiskRule rule_id_16_Int_11_and_string_hello1_IsOP;
		private RiskRule rule_id_14_Equals;

		internal void SetUp()
		{
			parameters = new Parameters();
			amount_variable = new Variable(parameters, typeof(int), "amount");
			name_variable = new Variable(parameters, typeof(string), "name");

			rule_id_13_Int_10_IsOP = new RiskRule
			(
				13,
				new RiskPredicate[]
				{
					new RiskPredicate(
						new IsOp(
							amount_variable,
							new IntLiteral(10)
						)
					)
				},
				RiskAction.Allow
			);
			rule_id_12_Int_11_IsOP = new RiskRule
			(
				12,
				new RiskPredicate[]
				{
					new RiskPredicate(
						new IsOp(
							amount_variable,
							new IntLiteral(11)
						)
					)
				},
				RiskAction.Allow
			);
			rule_id_14_Equals = new RiskRule
			(
				14,
				new RiskPredicate[]
				{
					new RiskPredicate(
						new EqualOp(
							BoolLiteral.FALSE,
							BoolLiteral.FALSE
						)
					)
				},
				RiskAction.Allow
			);
			rule_id_15_Int_11_and_string_hello_IsOP = new RiskRule
			(
				15,
				new RiskPredicate[]
				{
					new RiskPredicate(
						new IsOp(
							amount_variable,
							new IntLiteral(11)
						)
					),
					new RiskPredicate(
						new EqualOp(
							name_variable,
							new StringLiteral("hello")
						)
					)
				},
				RiskAction.Allow
			);
			rule_id_16_Int_11_and_string_hello1_IsOP = new RiskRule
			(
				16,
				new RiskPredicate[]
				{
					new RiskPredicate(
						new IsOp(
							amount_variable,
							new IntLiteral(11)
						)
					),
					new RiskPredicate(
						new EqualOp(
							name_variable,
							new StringLiteral("hello1")
						)
					)
				},
				RiskAction.Allow
			);
		}

		[TestMethod]
		public void From_Default_2_HashOP()
		{
			SetUp();

			RiskIndex index = new RiskIndex("onTest");
			index.IndexOn("amount", typeof(int));
			index.Include(rule_id_13_Int_10_IsOP);

			//parameters["amount"] = 9;
			//Assert.AreEqual(Result.ALLOW, index.Evaluate(parameters));

			parameters["amount"] = 10;
			var result = index.Evaluate(parameters);
			Assert.AreEqual(13, result.RuleId);
			Assert.AreEqual(RiskAction.Allow, result.Action);

		}
		[TestMethod]
		public void From_IteratorOR_2_HashOP()
		{
			SetUp();

			RiskIndex index = new RiskIndex("onTest");
			index.IndexOn("amount", typeof(int));
			index.Include(rule_id_13_Int_10_IsOP);
			index.Include(rule_id_14_Equals);

			parameters["amount"] = 9;
			var result = index.Evaluate(parameters);
			Assert.AreEqual(14, result.RuleId);
			Assert.AreEqual(RiskAction.Allow, result.Action);

			parameters["amount"] = 10;
			result = index.Evaluate(parameters);
			Assert.AreEqual(13, result.RuleId);
			Assert.AreEqual(RiskAction.Allow, result.Action);

		}
		[TestMethod]
		public void From_HashOP_2_IteratorOR()
		{
			SetUp();

			RiskIndex index = new RiskIndex("onTest");
			index.IndexOn("amount", typeof(int));
			index.Include(rule_id_14_Equals); 
			index.Include(rule_id_13_Int_10_IsOP);
			index.Include(rule_id_12_Int_11_IsOP);

			parameters["amount"] = 9;
			var result = index.Evaluate(parameters);
			Assert.AreEqual(14, result.RuleId);
			Assert.AreEqual(RiskAction.Allow, result.Action);

			parameters["amount"] = 11;
			result = index.Evaluate(parameters);
			Assert.AreEqual(12, result.RuleId);
			Assert.AreEqual(RiskAction.Allow, result.Action);

			parameters["amount"] = 10;
			result = index.Evaluate(parameters);
			Assert.AreEqual(13, result.RuleId);
			Assert.AreEqual(RiskAction.Allow, result.Action);

			parameters["amount"] = 9;
			result = index.Evaluate(parameters);
			Assert.AreEqual(14, result.RuleId);
			Assert.AreEqual(RiskAction.Allow, result.Action);

		}

		[TestMethod]
		public void From_2_Different_HashOP_With_Same_Value()
		{
			SetUp();

			RiskIndex index = new RiskIndex("onTest");
			index.IndexOn("amount", typeof(int));
			index.IndexOn("name", typeof(string));
			index.Include(rule_id_14_Equals);
			index.Include(rule_id_15_Int_11_and_string_hello_IsOP);
			index.Include(rule_id_16_Int_11_and_string_hello1_IsOP);
			// como ya existia el valor 11, para meter la nueva mete un OR. Antes left nuevo va a right
			// OR ocupa un peque;a modificacion , si ninguno de los arboles pega hay que mandar un false.
			//L false R false -> -1 
			// Expression.Confition(
			//Expression.OR(L, R),
			//result del OR,
			//retornar el -1... hacer un falseOP, que es parecido al allow OP
			// )

			//parameters["amount"] = 9;
			//var result = index.Evaluate(parameters);
			//Assert.AreEqual(14, result.RuleId);
			//Assert.AreEqual(RiskAction.Allow, result.Action);

			parameters["amount"] = 11;
			parameters["name"] = "hello";
			var result = index.Evaluate(parameters);
			Assert.AreEqual(15, result.RuleId);
			Assert.AreEqual(RiskAction.Allow, result.Action);

			parameters["amount"] = 11;
			parameters["name"] = "hello1";
			result = index.Evaluate(parameters);
			Assert.AreEqual(16, result.RuleId);
			Assert.AreEqual(RiskAction.Allow, result.Action);



		}

	}

}