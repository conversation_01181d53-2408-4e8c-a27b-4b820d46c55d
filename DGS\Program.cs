﻿using RestSharp;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;
using System.Xml;
using System.Xml.Linq;

namespace DGS
{
	class Program
	{
		static void Main(string[] args)
		{
			Console.WriteLine("GetPlayerBalance");

			var client = new RestClient("http://**************:2905/proxyplayer.asmx/GetPlayerBalance");
			client.Timeout = -1;
			var request = new RestRequest(Method.POST);
			request.AddHeader("Content-Type", "application/x-www-form-urlencoded");
			request.AddParameter("prmIdPlayer", "297");
			request.AddParameter("prmCurrencyCode", "1");
			IRestResponse response = client.Execute(request);
			//<?xml version="1.0" encoding="utf-8"?>
			//< string xmlns = "http://tempuri.org/" > &lt; xml ErrorCode = "0" ErrorMsgKey = "" ErrorMsgParams = "" ErrorMsg = "" CurrentBalance = "2,083.11 1" AvailBalance = "1,966.61 1" AmountAtRisk = "116.50 1" RealAvailBalance = "101,966.61 1" CreditLimit = "100,000.00 1" FreePlayAmount = "100.00 1" ThisWeek = "-3.00 1" LastWeek = "0.00 1" BonusPoints = "2383.50" / &gt;</ string >

											 StringWriter myWriter = new StringWriter();
			HttpUtility.HtmlDecode(response.Content, myWriter);
			string myDecodedString = myWriter.ToString();

			XDocument root = XDocument.Parse(myDecodedString, LoadOptions.None);
			Console.WriteLine(root);
			XElement node = root.Elements().First().Elements().First();//.First().Descendants("xml");
			Console.WriteLine((int)node.Attribute("ErrorCode"));
			Console.WriteLine((string)node.Attribute("AvailBalance"));
			Console.WriteLine((string)node.Attribute("RealAvailBalance"));


			Console.WriteLine("GetPlayerBalanceWithFormat");

			client = new RestClient("http://**************:2905/proxyplayer.asmx/GetPlayerBalanceWithFormat");
			client.Timeout = -1;
			request = new RestRequest(Method.POST);
			request.AddHeader("Content-Type", "application/x-www-form-urlencoded");
			request.AddParameter("prmIdPlayer", "297");
			request.AddParameter("prmFormat", "{0}");
			response = client.Execute(request);
			//success
			//<?xml version="1.0" encoding="utf-8"?>
			//<string xmlns="http://tempuri.org/">&lt;xml ErrorCode="0" ErrorMsgKey="" ErrorMsgParams="" ErrorMsg="" CurrentBalance="2083.1100" AvailBalance="1966.6100" AmountAtRisk="116.5000" RealAvailBalance="101966.6100" CreditLimit="100000.0000" FreePlayAmount="100.0000" ThisWeek="-3.0000" LastWeek="0.0000" BonusPoints="2383.50" /&gt;</string>
			//error
			//<? xml version = "1.0" encoding = "utf-8" ?>
			//< string xmlns = "http://tempuri.org/" > &lt; xml ErrorCode = "2" ErrorMsgKey = "" ErrorMsgParams = "" ErrorMsg = "System.FormatException: Input string was not in a correct format.&amp;#xD;&amp;#xA;   at System.Text.StringBuilder.AppendFormat(IFormatProvider provider, String format, Object[] args)&amp;#xD;&amp;#xA;   at System.String.Format(IFormatProvider provider, String format, Object[] args)&amp;#xD;&amp;#xA;   at DGSserver.bus.Player.PlayerBalance(Int32 prmIdPlayer, String prmCurrencyCode, String sFormat, SystemPreferences oSysPref)" CurrentBalance = "" AvailBalance = "" AmountAtRisk = "" RealAvailBalance = "" CreditLimit = "" FreePlayAmount = "" ThisWeek = "" LastWeek = "" BonusPoints = "0" / &gt;</ string >
			//error2
			//<? xml version = "1.0" encoding = "utf-8" ?>
			//< string xmlns = "http://tempuri.org/" > &lt; xml ErrorCode = "1" ErrorMsgKey = "IDPLYNOFOUND" ErrorMsgParams = "" ErrorMsg = "IdPlayer not found." CurrentBalance = "" AvailBalance = "" AmountAtRisk = "" RealAvailBalance = "" CreditLimit = "" FreePlayAmount = "" ThisWeek = "" LastWeek = "" BonusPoints = "0" / &gt;</ string >
			//error 3
			//Missing parameter: prmIdPlayer.
			myWriter = new StringWriter();
			HttpUtility.HtmlDecode(response.Content, myWriter);
			myDecodedString = myWriter.ToString();

			try
			{
				root = XDocument.Parse(myDecodedString, LoadOptions.None);
				node = root.Elements().First().Elements().First();//.First().Descendants("xml");
				int errorCode = (int)node.Attribute("ErrorCode");
				Console.WriteLine(errorCode);
				Console.WriteLine((string)node.Attribute("ErrorMsg"));
				Console.WriteLine((string)node.Attribute("ErrorMsgKey"));
				if (errorCode == 0)
				{
					Console.WriteLine((decimal)node.Attribute("AvailBalance"));
					Console.WriteLine((decimal)node.Attribute("RealAvailBalance"));
				}
			}
			catch (Exception e)
			{
				Console.WriteLine(myDecodedString);
			}


			Console.WriteLine("InsertTransaction2");

			client = new RestClient("http://**************:2905/proxyplayer.asmx/InsertTransaction2");
			client.Timeout = -1;
			request = new RestRequest(Method.POST);
			request.AddHeader("Content-Type", "application/x-www-form-urlencoded");
			request.AddParameter("prmTransactionType", "T_RECEIPT");
			request.AddParameter("prmIdplayer", "297");
			request.AddParameter("prmPaymentMethod", "cash");
			request.AddParameter("prmDescription", "$6 Deposit");
			request.AddParameter("prmReference", "********");
			request.AddParameter("prmAmount", "5.23");
			request.AddParameter("prmFee", "0");
			request.AddParameter("prmBonus", "0");
			request.AddParameter("UseDecimals", "true");
			response = client.Execute(request);
			//error
			//<string xmlns="http://tempuri.org/"><xml ErrorCode="1" ErrorMsgKey="DONTXFER" ErrorMsgParams="297" ErrorMsg="This Account (297) can not accept this type of transaction." IdTrasaction="0" PreviousBalance="0" CurrentBalance="0" /></string>
			//success 1
			//<? xml version = "1.0" encoding = "utf-8" ?>
			//<string xmlns="http://tempuri.org/"><xml ErrorCode="0" ErrorMsgKey="" ErrorMsgParams="" ErrorMsg="" IdTrasaction="3157" PreviousBalance="1954.6100" CurrentBalance="1948.6100" /></string>
			//success 2
			//<? xml version = "1.0" encoding = "utf-8" ?>
			//<string xmlns="http://tempuri.org/"><xml ErrorCode="0" ErrorMsgKey="" ErrorMsgParams="" ErrorMsg="" IdTrasaction="3158" PreviousBalance="1948.6100" CurrentBalance="1966.6100" /></string>
			//error 3
			//Missing parameter: prmIdPlayer.
			myWriter = new StringWriter();
			HttpUtility.HtmlDecode(response.Content, myWriter);
			myDecodedString = myWriter.ToString();

			try
			{
				root = XDocument.Parse(myDecodedString, LoadOptions.None);
				node = root.Elements().First().Elements().First();//.First().Descendants("xml");
				int errorCode = (int)node.Attribute("ErrorCode");
				Console.WriteLine(errorCode);
				Console.WriteLine((string)node.Attribute("ErrorMsg"));
				Console.WriteLine((string)node.Attribute("ErrorMsgKey"));
				if (errorCode == 0)
				{
					Console.WriteLine((int)node.Attribute("IdTrasaction"));
				}
			}
			catch (Exception e)
			{
				Console.WriteLine(myDecodedString);
			}



		}
	}
}
