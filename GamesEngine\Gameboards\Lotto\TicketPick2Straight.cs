﻿using GamesEngine.Bets;
using GamesEngine.Business;
using GamesEngine.Games.Lotto;
using GamesEngine.Preferences.Lotto;
using GamesEngine.Tools;
using System;
using System.Collections.Generic;
using System.Linq;
using static GamesEngine.Finance.PaymentChannels;

namespace GamesEngine.Gameboards.Lotto
{
	internal sealed class TicketPick2Straight : TicketPick<Pick2>
	{

		internal TicketPick2Straight(Player player, Lottery lottery, DateTime drawDate, string number1, string number2, Selection selectionMode, DateTime creationDate, decimal ticketCost, Prizes prizes) :
			base(player, lottery, drawDate, new Pick2(number1, number2), selectionMode, creationDate, ticketCost, prizes)
		{
		}

		internal TicketPick2Straight(Player player, Lottery lottery, DateTime drawDate, string [] numbers, Selection selectionMode, DateTime creationDate, decimal ticketCost, Prizes prizes) :
			base(player, lottery, drawDate, numbers, selectionMode, creationDate, ticketCost, prizes)
		{
		}

		protected sealed override void GradeNumber(Pick2 winnerNumber, int fireBallNumber = int.MinValue)
		{
			var withFireBall = BelongsToFireBallDraw;
            if (withFireBall && fireBallNumber == int.MinValue) throw new GameEngineException("FireBall number is required");
			if (withFireBall && (fireBallNumber < 0 || fireBallNumber > 9)) throw new GameEngineException($"FireBall number {fireBallNumber} must be between 1 and 2");
			if (winnerNumber.Count != 1) throw new GameEngineException($"Winner {winnerNumber.AsString()} must be a single number");

			int digit1 = winnerNumber[1].SingleDigit;
			int digit2 = winnerNumber[2].SingleDigit;
			bool won = false;
			payout = 0;
			foreach (Pick2 pick in base.Numbers)
			{
				bool isExcluded = IsExcluded(digit1, digit2);
				if (!isExcluded)
				{
                    int prize = Prize();

					if (!withFireBall)
					{
                        if (pick.IsMarked(digit1, digit2))
                        {
                            won = true;
                            var rawPrice = prize * BetAmount(PickPortion.NORMAL_LEVEL);
                            Commons.ValidateAmount(rawPrice);
                            payout = payout + rawPrice;
                        }
                    }
					else
                    {
						if (pick.IsMarked(digit1, digit2))
						{
							won = true;
                            var rawPrice = RoundToNearestFiveCents(prize * BetAmount(PickPortion.NORMAL_LEVEL));
                            Commons.ValidateAmount(rawPrice);
                            payout = payout + rawPrice;
                        }
						if (pick.IsMarked(fireBallNumber, digit2))
						{
							won = true;
                            var rawPrice = RoundToNearestFiveCents(prize * BetAmount(PickPortion.LEVEL1_PORTION));
                            Commons.ValidateAmount(rawPrice);
                            payout = payout + rawPrice;
                        }
						if (pick.IsMarked(digit1, fireBallNumber))
						{
							won = true;							
							var rawPrice = RoundToNearestFiveCents(prize * BetAmount(PickPortion.LEVEL2_PORTION));
                            Commons.ValidateAmount(rawPrice);
                            payout = payout + rawPrice;
                        }
                    }
                }
			}
			if (won)
			{
				base.GradeAsWinner();
			}
			else
			{
				base.GradeAsLoser();
			}
		}

        protected override decimal BetAmount(PickPortion pickPortion)
        {
            var originalValue = TicketAmount() / Count;
            Commons.ValidateAmount(originalValue);
            if (!BelongsToFireBallDraw) return originalValue;

            decimal porcionSinFireBall = originalValue / 2;
            if (pickPortion == PickPortion.NORMAL_LEVEL) return porcionSinFireBall;

            decimal porcionCombinacion1 = originalValue - porcionSinFireBall;
            porcionCombinacion1 = porcionCombinacion1 / 2;
            if (pickPortion == PickPortion.LEVEL1_PORTION) return porcionCombinacion1;

            decimal porcionCombinacion2 = originalValue - porcionSinFireBall - porcionCombinacion1;
            if (pickPortion == PickPortion.LEVEL2_PORTION) return porcionCombinacion2;

            throw new GameEngineException("Invalid PickPortion");
        }

        internal override TicketType IdOfType()
		{
			return TicketType.P2S;
		}

		internal override string GameTypeForReports()
		{
			return QueryMakerOfHistoricalPicks.ID_PICK2;
		}

		internal int Prize()
		{
			return Prizes.Prize(IdOfType(), PrizesPicks.PRIZE_CRITERIA);
		}

        internal override IEnumerable<TicketByPrize> TicketsByPrize()
		{
			var tickets = new TicketByPrize[] {
				new TicketByPrizePick2Straight(this, PrizesPicks.PRIZE_CRITERIA)
			};
			foreach (SubTicket<IPick> subTicket in SubTickets())
			{
				tickets[0].Add(subTicket);
			}
			return tickets;
		}

		internal override void GenerateWagers()
		{
			var betAmount = BetAmount();
			GenerateWagers(betAmount);
		}

		internal override void GenerateWagers(decimal betAmount)
		{
			decimal prize = Prize();

			prize *= betAmount;
			var prizeToWin = prize - betAmount;
			prizeToWin = prizeToWin < 0 ? 0 : prizeToWin;

			if (!NumbersFollowPattern())
			{
				foreach (SubTicket<IPick> subticket in SubTickets())
				{
					var strNumbers = subticket.AsStringForAccounting();
					AddWager(betAmount, prizeToWin, strNumbers, subticket);
				}
			}
			else
			{
				ValidateThereIsOnlyOneSubticket();
				var ticketCost = TicketAmount();
				var strNumbers = Numbers.ElementAt(0).AsStringForAccounting();
				AddWager(ticketCost, prizeToWin, strNumbers, SubTickets().ToArray());
			}
		}
	}

	internal class TicketByPrizePick2Straight : TicketByPrize
	{
		internal TicketByPrizePick2Straight(Ticket ticket, int prizeCriteria) : base(ticket, prizeCriteria)
		{

		}

		internal override bool HasNumber(int[] numbers)
		{
			var result = Subtickets.Cast<SubTicket<IPick>>().Any(x => x.IsTheSameNumberWithTheSameOrder(numbers[0], numbers[1]));
			return result;
		}

		internal override string AsString()
		{
			var result = Ticket.AsString();
			return result;
		}

	}
}
