﻿using ExchangeAPI.ExchangeEntities;
using ExchangeAPI.Model;
using ExchangeAPI.Service;
using GamesEngine;
using GamesEngine.Exchange.Persistance;
using GamesEngine.Settings;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ExchangeAPI.Logic
{
    public class Withdrawals : IWithdrawals
    {
        private const string ALL_SELECTED = "all";
        private const int MAXIMUM_AMOUNT_OF_ROWS_ALLOWED = 100;

        public static WithdrawalsDTO FilteredBy(DateTime startDate, DateTime endDate, string state, string identificationNumber, string transactionId, string cashierName, string domain,
            int initialIndex, int amountOfRows)
        {
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate.Hour != 0 || startDate.Minute != 0 || startDate.Second != 0) throw new GameEngineException($"{nameof(startDate)} {startDate} is not valid");
            if (endDate.Hour != 0 || endDate.Minute != 0 || endDate.Second != 0) throw new GameEngineException($"{nameof(endDate)} {endDate} is not valid");
            if (string.IsNullOrWhiteSpace(state)) throw new ArgumentNullException(nameof(state));
            if (string.IsNullOrWhiteSpace(identificationNumber)) throw new ArgumentNullException(nameof(identificationNumber));
            if (string.IsNullOrWhiteSpace(transactionId)) throw new ArgumentNullException(nameof(transactionId));
			if (string.IsNullOrWhiteSpace(cashierName)) throw new ArgumentNullException(nameof(cashierName));
            if (string.IsNullOrWhiteSpace(domain)) throw new ArgumentNullException(nameof(domain));
            if (initialIndex < 0) throw new GameEngineException($"{nameof(initialIndex)} {initialIndex} is not valid");
            if (amountOfRows <= 0) throw new GameEngineException($"{nameof(amountOfRows)} {amountOfRows} is not valid");

            if (amountOfRows > MAXIMUM_AMOUNT_OF_ROWS_ALLOWED)
            {
                amountOfRows = MAXIMUM_AMOUNT_OF_ROWS_ALLOWED;
            }

            var allStatesSelected = state.ToLower().Trim() == ALL_SELECTED;
            var allIdentificationNumberSelected = identificationNumber.ToLower().Trim() == ALL_SELECTED;
            var allTransactionIdsSelected = transactionId.ToLower().Trim() == ALL_SELECTED;
			var allCashiersSelected = cashierName.ToLower().Trim() == ALL_SELECTED;
            var allDomainsSelected = domain.ToLower().Trim() == ALL_SELECTED;

            using (var context = new ExchangeContext())
            {
                var withdrawals = context.Withdrawal
                    .Include(withdrawal => withdrawal.WhocreatedNavigation)
                    .Include(withdrawal => withdrawal.DomainNavigation)
                    .Include(withdrawal => withdrawal.Fromcurrency)
                    .Include(withdrawal => withdrawal.Tocurrency)
                    .Where(withdrawal => !withdrawal.Deleted && withdrawal.Datecreated.Date >= startDate && withdrawal.Datecreated.Date <= endDate
                        && (allStatesSelected || (withdrawal.Approvals == withdrawal.Approvalsrequired && state == TransactionState.Approved.ToString()) || (withdrawal.Rejections > 0 && state == TransactionState.Rejected.ToString()) || (withdrawal.Approvals == 0 && withdrawal.Rejections == 0 && state == TransactionState.Pending.ToString()))
                        && (allIdentificationNumberSelected || withdrawal.Fromcustomer == identificationNumber) 
                        && (allTransactionIdsSelected || withdrawal.Id.ToString() == transactionId) 
                        && (allCashiersSelected || withdrawal.WhocreatedNavigation.Name == cashierName)
                        && (allDomainsSelected || withdrawal.DomainNavigation.Domain == domain)
                        )
                    .OrderByDescending(withdrawal => withdrawal.Datecreated);
                var withdrawalsPaged = withdrawals
                    .Skip(initialIndex)
                    .Take(amountOfRows);
                var withdrawalsDTO = withdrawalsPaged.MapToWithdrawalsDTO();
                withdrawalsDTO.TotalRecords = withdrawals.Count();
                return withdrawalsDTO;
            }
        }

		private static Withdrawal WithdrawalFor(int id)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");

            using (var context = new ExchangeContext())
            {
                var withdrawalFound = context.Withdrawal
                    .Include(transfer => transfer.Exchangerate)
                    .Where(withdrawal => ! withdrawal.Deleted && withdrawal.Id == id)
                    .FirstOrDefault();
                return withdrawalFound;
            }
        }

        void IWithdrawals.Save(Withdrawal withdrawal)
        {
            if (withdrawal == null) throw new ArgumentNullException(nameof(withdrawal));

            using (var context = new ExchangeContext())
            {
                context.Withdrawal.Add(withdrawal);
                context.SaveChanges();
            }
        }

        public static Withdrawal UpdateAsApproved(ExchangeContext context, int id, DateTime date, decimal profit, decimal purchase, decimal sale, string employeeName, int processorId)
        {
            if (context == null) throw new ArgumentNullException(nameof(context));
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");

            var user = Integration.ExchangeUsers.User(employeeName);
            var withdrawal = WithdrawalFor(id);
            if (withdrawal == null) throw new ArgumentNullException(nameof(withdrawal));

            withdrawal.Approvals = 1;
            withdrawal.Whoapproved = user.Id;
            withdrawal.Dateapproved = date;
            withdrawal.Net = purchase;
            withdrawal.Profit = profit;
            withdrawal.Amount = sale;
            withdrawal.Processorid = processorId;
            context.Withdrawal.Update(withdrawal);
            context.SaveChanges();
            return withdrawal;
        }

        public static Withdrawal UpdateJournalEntry(ExchangeContext context, int id, string journalEntryDetailId)
        {
            if (context == null) throw new ArgumentNullException(nameof(context));
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(journalEntryDetailId)) throw new ArgumentNullException(nameof(journalEntryDetailId));

            var withdrawal = WithdrawalFor(id);
            if (withdrawal == null) throw new ArgumentNullException(nameof(withdrawal));

            withdrawal.Journalentrydetailid = journalEntryDetailId;
            context.Withdrawal.Update(withdrawal);
            context.SaveChanges();
            return withdrawal;
        }

        public static Withdrawal UpdateAsRejected(int id, DateTime date, string rejectionReason, string employeeName)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");

            var user = Integration.ExchangeUsers.User(employeeName);
            var withdrawal = WithdrawalFor(id);
            if (withdrawal == null) throw new ArgumentNullException(nameof(withdrawal));

            withdrawal.Rejections = 1;
            withdrawal.Whorejected = user.Id;
            withdrawal.Daterejected = date;
            withdrawal.Rejectionreason = rejectionReason;
            using (var context = new ExchangeContext())
            {
                context.Withdrawal.Update(withdrawal);
                context.SaveChanges();
            }
            return withdrawal;
        }

        public static Withdrawal UpdateVoucherUrl(int id, string voucherUrl)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(voucherUrl)) throw new ArgumentNullException(nameof(voucherUrl));

            var withdrawal = WithdrawalFor(id);
            if (withdrawal == null) throw new ArgumentNullException(nameof(withdrawal));

            withdrawal.Voucherurl = voucherUrl;
            using (var context = new ExchangeContext())
            {
                context.Withdrawal.Update(withdrawal);
                context.SaveChanges();
            }
            return withdrawal;
        }
    }
}
