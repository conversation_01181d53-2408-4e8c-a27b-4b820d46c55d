﻿using System;
using System.Collections.Generic;
using System.Text;

namespace GamesEngine.Exchange.Persistance
{
	public class JournalEntryEntity
	{
		public DateTime EntryDate { get; set; }
		public string Title { get; set; }
		public string SystemId { get; set; }
		public string AccountingAccount { get; set; }
		public string Description { get; set; }
		public string ReferenceId { get; set; }
		public decimal Debit { get; set; }
		public decimal Credit { get; set; }
		public int LineNumber { get; set; }
	}
}
