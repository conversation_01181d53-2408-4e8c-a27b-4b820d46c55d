﻿using GamesEngine.Games;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Domains
{
    internal class DomainsActivations<T>
    {
        private readonly Dictionary<T, DomainsActivation> activations = new Dictionary<T, DomainsActivation>();

        internal DomainsActivations()
		{

		}

        internal DomainsActivations(IEnumerable<T> elements):this(elements, DomainsActivation.MAX_LOG_CAPACITY)
        {
        }

        internal DomainsActivations(IEnumerable<T> elements, byte maxLogCapacity)
        {
            if (maxLogCapacity <= 0) throw new GameEngineException($"Capacity {maxLogCapacity} must be greater than 0");

            foreach (var element in elements)
            {
                if (element == null) throw new ArgumentNullException(nameof(element));

                var activation = new DomainsActivation(maxLogCapacity);
                activations.Add(element, activation);
            }
        }

        internal void AddActivatableElement(T t)
        {
            if (t == null) throw new ArgumentNullException(nameof(t));

            activations.Add(t, new DomainsActivation());
        }

        internal void AddActivatableElement(T t, DomainsActivation domainsActivation)
        {
            if (t == null) throw new ArgumentNullException(nameof(t));
            if (domainsActivation == null) throw new ArgumentNullException(nameof(domainsActivation));

            var newDomainsActivation = new DomainsActivation();
            activations.Add(t, newDomainsActivation);
            newDomainsActivation.CopyActivationsFrom(domainsActivation);
        }

        internal bool IsEnabled(T t, Domain domain)
        {
            if (t == null) throw new ArgumentNullException(nameof(t));
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            DomainsActivation activation;
            activations.TryGetValue(t, out activation);
            if (activation == null) throw new GameEngineException($"There is no '{t.GetType().Name}' initialized with domains activation");
            var result = activation.IsEnabled(domain);
            return result;
        }

        internal bool Contains(Domain domain)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            foreach (var domainActivation in activations.Values)
            {
                if (domainActivation.Contains(domain)) return true;
            }
            return false;
        }

        internal void IncludeNewDomainForAll(Domain domain, bool enabled = true)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            foreach (var domainActivation in activations.Values)
            {
                domainActivation.IncludeDomain(domain);
                if (!enabled) domainActivation.DisableDomain(domain);
            }
        }

        internal DomainsActivation IncludeDomainForElement(T t, Domain domain, bool enabled = true)
        {
            if (t == null) throw new ArgumentNullException(nameof(t));
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            DomainsActivation activation;
            activations.TryGetValue(t, out activation);
            if (activation == null) throw new GameEngineException($"There is no '{t.GetType().Name}' initialized with domains activation");
            activation.IncludeDomain(domain);
            if (!enabled) activation.DisableDomain(domain);
            return activation;
        }

        internal void EnableDomain(T t, Domain domain, string employeeName, DateTime now)
        {
            if (t == null) throw new ArgumentNullException(nameof(t));
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (string.IsNullOrEmpty(employeeName)) throw new ArgumentNullException(nameof(employeeName));
            if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");

            DomainsActivation activation;
            activations.TryGetValue(t, out activation);
            if (activation == null) throw new GameEngineException($"There is no '{t.GetType().Name}' initialized with domains activation");
            activation.EnableDomain(domain);
            activation.AddAnnotation(domain.Id.ToString(), "Enabled", employeeName, now);
        }

        internal DomainsActivation EnableDomain(T t, Domain domain)
        {
            if (t == null) throw new ArgumentNullException(nameof(t));
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            DomainsActivation activation;
            activations.TryGetValue(t, out activation);
            if (activation == null) throw new GameEngineException($"There is no '{t.GetType().Name}' initialized with domains activation");
            activation.EnableDomain(domain);
            return activation;
        }

        internal void DisableDomain(T t, Domain domain, string employeeName, DateTime now)
        {
            if (t == null) throw new ArgumentNullException(nameof(t));
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (string.IsNullOrEmpty(employeeName)) throw new ArgumentNullException(nameof(employeeName));
            if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");

            DomainsActivation activation;
            activations.TryGetValue(t, out activation);
            if (activation == null) throw new GameEngineException($"There is no '{t.GetType().Name}' initialized with domains activation");
            activation.DisableDomain(domain);
            activation.AddAnnotation(domain.Id.ToString(), "Disabled", employeeName, now);
        }

        internal DomainsActivation DisableDomain(T t, Domain domain)
        {
            if (t == null) throw new ArgumentNullException(nameof(t));
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            DomainsActivation activation;
            activations.TryGetValue(t, out activation);
            if (activation == null) throw new GameEngineException($"There is no '{t.GetType().Name}' initialized with domains activation");
            activation.DisableDomain(domain);
            return activation;
        }

        internal IEnumerable<T> EnabledElements(Domain domain)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            var result = new List<T>();
            foreach (var element in activations)
            {
                if (element.Value.IsEnabled(domain)) result.Add(element.Key);
            }

            return result;
        }

        internal IEnumerable<T> EnabledElements()
        {
            var result = new List<T>();
            foreach (var element in activations)
            {
                if (AnyEnabledDomain(element.Key)) result.Add(element.Key);
            }

            return result;
        }

        internal IEnumerable<T> EnabledElementsInThisCollection(IEnumerable<T> elementsToCompare, Domain domain)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            var result = new List<T>();
            foreach (var element in activations)
            {
                if (elementsToCompare.Contains(element.Key))
                {
                    if (element.Value.IsEnabled(domain)) 
                        result.Add(element.Key);
                }
            }

            return result;
        }

        internal DomainsActivation DomainsActivationFrom(T t)
        {
            if (t == null) throw new ArgumentNullException(nameof(t));
            if (! activations.ContainsKey(t)) throw new GameEngineException($"'{t.GetType().Name}' does not exist in domains activations");

            var result = activations[t];
            return result;
        }

        internal bool AnyEnabledDomain(T t)
        {
            if (t == null) throw new ArgumentNullException(nameof(t));
            if (! activations.ContainsKey(t)) throw new GameEngineException($"'{t.GetType().Name}' does not exist in domains activations");

            var result = activations[t].AnyEnabledDomain();
            return result;
        }

    }
}
