﻿using ExchangeAPI.ExchangeEntities;
using ExchangeAPI.Model;
using ExchangeAPI.Service;
using GamesEngine;
using GamesEngine.Finance;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static GamesEngine.Finance.Currencies;

namespace ExchangeAPI.Logic
{
    public class DailyProfits
    {
        public static void UpdateDepositAsApproved(int id, int authorizationId, DateTime date, decimal profit, decimal purchase, decimal sale, Coin fromCurrencyCode, Coin toCurrencyCode, string employeeName, int processorId)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");
            if (authorizationId <= 0) throw new GameEngineException($"{nameof(authorizationId)} must be greater than 0");
            if (purchase <= 0) throw new GameEngineException($"{nameof(purchase)} must be greater than 0");
            if (sale <= 0) throw new GameEngineException($"{nameof(sale)} must be greater than 0");

            using (var context = new ExchangeContext())
            {
                using (var transaction = context.Database.BeginTransaction())
                {
                    try
                    {
                        var deposit = Deposits.UpdateAsApproved(context, id, authorizationId, date, profit, purchase, sale, employeeName, processorId);
                        var isThereExchange = deposit.Exchangerateid != null;
                        if (isThereExchange)
                        {
                            ProfitableTransactions.Save(context, deposit);
                            SaveOrUpdate(context, date.Date, profit, purchase, sale, fromCurrencyCode, toCurrencyCode);
                        }

                        transaction.Commit();
                    }
                    catch (Exception e)
                    {
                        transaction.Rollback();
                        var message = $"{nameof(UpdateDepositAsApproved)} method cannot update id:{id}";
                        Loggers.GetIntance().Db.Error(message, e);
                        ErrorsSender.Send(e, message);
                        throw;
                    }
                }
            }
        }

        public static void UpdateWithdrawalAsApproved(int id, DateTime date, decimal profit, decimal purchase, decimal sale, Coin fromCurrencyCode, Coin toCurrencyCode, string employeeName, int processorId)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");
            if (purchase <= 0) throw new GameEngineException($"{nameof(purchase)} must be greater than 0");
            if (sale <= 0) throw new GameEngineException($"{nameof(sale)} must be greater than 0");

            using (var context = new ExchangeContext())
            {
                using (var transaction = context.Database.BeginTransaction())
                {
                    try
                    {
                        var withdrawal = Withdrawals.UpdateAsApproved(context, id, date, profit, purchase, sale, employeeName, processorId);
                        var isThereExchange = withdrawal.Exchangerateid != null;
                        if (isThereExchange)
                        {
                            ProfitableTransactions.Save(context, withdrawal);
                            SaveOrUpdate(context, date.Date, profit, purchase, sale, fromCurrencyCode, toCurrencyCode);
                        }

                        transaction.Commit();
                    }
                    catch (Exception e)
                    {
                        transaction.Rollback();
                        var message = $"{nameof(UpdateWithdrawalAsApproved)} method cannot update id:{id}";
                        Loggers.GetIntance().Db.Error(message, e);
                        ErrorsSender.Send(e, message);
                        throw;
                    }
                }
            }
        }

        public static void UpdateTransferAsApproved(int id, DateTime date, decimal profit, decimal purchase, decimal sale, Coin fromCurrencyCode, Coin toCurrencyCode, string employeeName, int processorId)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");
            if (purchase <= 0) throw new GameEngineException($"{nameof(purchase)} must be greater than 0");
            if (sale <= 0) throw new GameEngineException($"{nameof(sale)} must be greater than 0");

            using (var context = new ExchangeContext())
            {
                using (var transaction = context.Database.BeginTransaction())
                {
                    try
                    {
                        var transfer = Transfers.UpdateAsApproved(context, id, date, profit, purchase, sale, employeeName, processorId);
                        var isThereExchange = transfer.Exchangerateid != null;
                        if (isThereExchange)
                        {
                            ProfitableTransactions.Save(context, transfer);
                            SaveOrUpdate(context, date.Date, profit, purchase, sale, fromCurrencyCode, toCurrencyCode);
                        }

                        transaction.Commit();
                    }
                    catch (Exception e)
                    {
                        transaction.Rollback();
                        var message = $"{nameof(UpdateTransferAsApproved)} method cannot update id:{id}";
                        Loggers.GetIntance().Db.Error(message, e);
                        ErrorsSender.Send(e, message);
                        throw;
                    }
                }
            }
        }

        public static DailyProfitsDTO FilterBy(DateTime startDate, DateTime endDate, string currencyCode, int initialIndex, int amountOfRows)
        {
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (initialIndex < 0) throw new GameEngineException($"{nameof(initialIndex)} {initialIndex} is not valid");
            if (amountOfRows <= 0) throw new GameEngineException($"{nameof(amountOfRows)} {amountOfRows} is not valid");

            using (var context = new ExchangeContext())
            {
                IQueryable<DailyProfitDTO> dailyProfits = context.Dailyprofits.Where(dailyProfit => dailyProfit.Currency.Isocode==currencyCode && dailyProfit.Day >= startDate && dailyProfit.Day <= endDate)
                    .Select(dailyProfit =>
                        new DailyProfitDTO()
                        {
                            Day = dailyProfit.Day,
                            Purchases = dailyProfit.Purchases,
                            Sales = dailyProfit.Sales,
                            Profits = dailyProfit.Profits,
                            CurrencyCode = currencyCode
                        });

                dailyProfits = dailyProfits
                    .OrderBy(dailyProfit => dailyProfit.Day);
                var dailyProfitsPaged = dailyProfits
                    .Skip(initialIndex)
                    .Take(amountOfRows);
                var dailyProfitsDTO = dailyProfitsPaged.MapToDailyProfitsDTO(currencyCode);
                dailyProfitsDTO.TotalRecords = dailyProfits.Count();
                dailyProfitsDTO.TotalSales = dailyProfits.Sum(dailyProfit => dailyProfit.Sales);
                dailyProfitsDTO.TotalPurchases = dailyProfits.Sum(dailyProfit => dailyProfit.Purchases);
                dailyProfitsDTO.TotalProfits = dailyProfits.Sum(dailyProfit => dailyProfit.Profits);
            }
            return new DailyProfitsDTO(currencyCode);
        }

        private static void SaveOrUpdate(ExchangeContext context, DateTime date, decimal profit, decimal purchase, decimal sale, Coin fromCoin, Coin toCoin)
        {
            if (date.Hour != 0 || date.Minute != 0 || date.Second != 0) throw new GameEngineException($"{nameof(date)} {date} is not valid");
            if (purchase < 0) throw new GameEngineException($"{nameof(purchase)} must be greater than 0");
            if (sale < 0) throw new GameEngineException($"{nameof(sale)} must be greater than 0");

            var dailyProfit = context.Dailyprofits.FirstOrDefault(dailyProfit => dailyProfit.Currency.Isocode == fromCoin.Iso4217Code && dailyProfit.Day == date);
            if (dailyProfit != null)
            {
                dailyProfit.Profits += profit;
                dailyProfit.Purchases += purchase;
                context.Dailyprofits.Update(dailyProfit);
            }
            else
            {
                dailyProfit = new Dailyprofits()
                {
                    Day = date,
                    Profits = profit,
                    Purchases = purchase,
                    Sales = 0m,
                    Currencyid = fromCoin.Id
                };
                context.Dailyprofits.Add(dailyProfit);
            }

            dailyProfit = context.Dailyprofits.FirstOrDefault(dailyProfit => dailyProfit.Currency.Isocode == toCoin.Iso4217Code && dailyProfit.Day == date);
            if (dailyProfit != null)
            {
                dailyProfit.Sales += sale;
                context.Dailyprofits.Update(dailyProfit);
            }
            else
            {
                dailyProfit = new Dailyprofits()
                {
                    Day = date,
                    Profits = 0m,
                    Purchases = 0m,
                    Sales = sale,
                    Currencyid = toCoin.Id
                };
                context.Dailyprofits.Add(dailyProfit);
            }
            context.SaveChanges();
        }

    }
}
