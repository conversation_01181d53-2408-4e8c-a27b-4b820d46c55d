﻿using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Text;

namespace GamesEngine.Customers
{
    public enum InputType
    {
        Checkbox,
        Date,
        Email,
        File,
        Number,
        Radio,
        Tel,
        Text
    }

    [Puppet]
    internal class Input : Objeto
    {
        internal string Id { get; }
        internal string Name { get; }
        internal InputType Type { get; }
        private readonly List<Attribute> attributes = new List<Attribute>();

        public Input(string id, string name, InputType type)
        {
            if (string.IsNullOrWhiteSpace(id)) throw new ArgumentNullException(nameof(id));
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

            Id = id;
            Name = name;
            Type = type;
        }

        internal string TypeAsString()
        {
            return Type.ToString().ToLower();
        }

        internal void AddAttribute(Attribute attribute)
        {
            if (attribute == null) throw new ArgumentNullException(nameof(attribute));

            attributes.Add(attribute);
        }

        internal IEnumerable<Attribute> ListAttributes()
        {
            return attributes;
        }

        internal bool IsType(InputType type)
        {
            return Type == type;
        }

        internal bool HasAttribute(AttributeType attributeType)
        {
            var exists = attributes.Exists(attribute => attribute.Type == attributeType);
            return exists;
        }
    }

    [Puppet]
    internal sealed class InputBuilder : Objeto
    {
        private readonly Input input;

        public InputBuilder(string id, string name, InputType type)
        {
            input = new Input(id, name, type);
        }

        internal InputBuilder SetChecked()
        {
            if (input.Type != InputType.Checkbox && input.Type != InputType.Radio) throw new GameEngineException($"{nameof(AttributeType)} {nameof(AttributeType.Checked)} only can be used with {nameof(InputType.Checkbox)} or {nameof(InputType.Radio)}");

            input.AddAttribute(new BooleanAttribute(true, AttributeType.Checked));
            return this;
        }

        internal InputBuilder SetDisabled()
        {
            input.AddAttribute(new BooleanAttribute(true, AttributeType.Disabled));
            return this;
        }

        internal InputBuilder AddMax(string value)
        {
            if (string.IsNullOrWhiteSpace(value)) throw new ArgumentNullException(nameof(value));
            if (input.Type != InputType.Date && input.Type != InputType.Number) throw new GameEngineException($"{nameof(AttributeType)} {nameof(AttributeType.Max)} only can be used with {nameof(InputType.Date)} or {nameof(InputType.Number)}");

            input.AddAttribute(new StringAttribute(value, AttributeType.Max));
            return this;
        }

        internal InputBuilder AddMaxLength(int value)
        {
            if (value <= 0) throw new GameEngineException($"{nameof(value)} must be greater than 0");

            input.AddAttribute(new IntegerAttribute(value, AttributeType.MaxLength));
            return this;
        }

        internal InputBuilder AddMin(string value)
        {
            if (string.IsNullOrWhiteSpace(value)) throw new ArgumentNullException(nameof(value));
            if (input.Type != InputType.Date && input.Type != InputType.Number) throw new GameEngineException($"{nameof(AttributeType)} {nameof(AttributeType.Max)} only can be used with {nameof(InputType.Date)} or {nameof(InputType.Number)}");

            input.AddAttribute(new StringAttribute(value, AttributeType.Min));
            return this;
        }

        internal InputBuilder SetMultiple()
        {
            if (input.Type != InputType.Email && input.Type != InputType.File) throw new GameEngineException($"{nameof(AttributeType)} {nameof(AttributeType.Multiple)} only can be used with {nameof(InputType.Email)} or {nameof(InputType.File)}");

            input.AddAttribute(new BooleanAttribute(true, AttributeType.Multiple));
            return this;
        }

        internal InputBuilder AddPattern(string value)
        {
            if (string.IsNullOrWhiteSpace(value)) throw new ArgumentNullException(nameof(value));
            if (input.Type != InputType.Date && input.Type != InputType.Email && input.Type != InputType.Tel && input.Type != InputType.Text) throw new GameEngineException($"{nameof(AttributeType)} {nameof(AttributeType.Pattern)} only can be used with {nameof(InputType.Date)} or {nameof(InputType.Email)} or {nameof(InputType.Tel)} or {nameof(InputType.Text)}");

            input.AddAttribute(new StringAttribute(value, AttributeType.Pattern));
            return this;
        }

        internal InputBuilder AddPlaceholder(string value)
        {
            if (string.IsNullOrWhiteSpace(value)) throw new ArgumentNullException(nameof(value));
            if (input.Type != InputType.Email && input.Type != InputType.Tel && input.Type != InputType.Text) throw new GameEngineException($"{nameof(AttributeType)} {nameof(AttributeType.Placeholder)} only can be used with {nameof(InputType.Email)} or {nameof(InputType.Tel)} or {nameof(InputType.Text)}");

            input.AddAttribute(new StringAttribute(value, AttributeType.Placeholder));
            return this;
        }

        internal InputBuilder SetReadonly()
        {
            input.AddAttribute(new BooleanAttribute(true, AttributeType.Readonly));
            return this;
        }

        internal InputBuilder SetRequired()
        {
            input.AddAttribute(new BooleanAttribute(true, AttributeType.Required));
            return this;
        }

        internal InputBuilder AddSize(int value)
        {
            if (value <= 0) throw new GameEngineException($"{nameof(value)} must be greater than 0");
            if (input.Type != InputType.Email && input.Type != InputType.Tel && input.Type != InputType.Text) throw new GameEngineException($"{nameof(AttributeType)} {nameof(AttributeType.Size)} only can be used with {nameof(InputType.Email)} or {nameof(InputType.Tel)} or {nameof(InputType.Text)}");

            input.AddAttribute(new IntegerAttribute(value, AttributeType.Size));
            return this;
        }

        internal InputBuilder AddValue(string value)
        {
            if (string.IsNullOrWhiteSpace(value)) throw new ArgumentNullException(nameof(value));

            input.AddAttribute(new StringAttribute(value, AttributeType.Value));
            return this;
        }

        internal Input Build()
        {
            if (string.IsNullOrWhiteSpace(input.Name)) throw new ArgumentNullException(nameof(input.Name));

            return input;
        }
    }
}
