﻿using GamesEngine.MessageQueuing;
using GamesEngine.Settings;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static GamesEngine.Business.WholePaymentMethods;

namespace GamesEngine.Business
{
    class WholeEntities: WholeCatalog
    {
        internal WholeEntities(Company company) : base(company)
        {
        }
        internal CatalogMember Add(bool itIsThePresent, string name)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (Exists(name)) throw new GameEngineException($"{nameof(name)} '{name}' is already used");

            nextConsecutive = NextConsecutive();
            var entity = new Entity(nextConsecutive, name);
            Add(entity);
            if (itIsThePresent && (Integration.UseKafka || Integration.UseKafkaForAuto))
            {
                var msg = new EntityAddMessage(nextConsecutive, name);
                Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForCatalog, msg);
            }
            return entity;
        }

        override internal CatalogMember Add(int id, string name)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} {id} must be greater than 0");
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (Exists(name)) throw new GameEngineException($"{nameof(name)} '{name}' is already used");
            if (Exists(id)) throw new GameEngineException($"{nameof(id)} '{id}' is already used");

            if (id > nextConsecutive) nextConsecutive = id;
            var entity = new Entity(id, name);
            members.Add(entity);
            return entity;
        }
    }

    internal class Entity: CatalogMember
    {
        public Entity(int id, string name) : base(id, name)
        {
        }

    }
}
