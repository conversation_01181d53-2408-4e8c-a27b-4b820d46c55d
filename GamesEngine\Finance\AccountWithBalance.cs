﻿using GamesEngine.Business;
using GamesEngine.PurchaseOrders;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GamesEngine.Finance
{

	internal sealed class AccountWithBalances
	{
		private Dictionary<string, AccountWithBalance> accounts;
		internal const string NO_ACCOUNT_NUMBER="N/A";
		private readonly List<MovementsCollector> movementsCollector;

		internal AtAddress Owner { get;}

		internal AccountWithBalances(AtAddress owner)
		{
			if (owner == null) throw new GameEngineException(nameof(owner));

			accounts = new Dictionary<string, AccountWithBalance>(StringComparer.InvariantCultureIgnoreCase);
			Owner = owner;
			this.movementsCollector = new List<MovementsCollector>();
		}

		//public Balances Balances 
		//{ 
		//	get 
		//	{
		//		return balances;
		//	}
		//}

		internal Balance CreateNewAccountIfNotExists(Coin currencyCode)
		{
			string number = $"{currencyCode.Iso4217Code}";
			AccountWithBalance account;
			if (!accounts.TryGetValue(number, out account))
			{
				account = new AccountWithBalance(currencyCode, Owner, number);
				accounts.Add(number, account);
			}
			return account.Balance;
		}

		internal AccountWithBalance CreateNewAccountIfNotExists(Coin currencyCode, string number)
		{
			AccountWithBalance account;
			if (!accounts.TryGetValue(number, out account))
			{
				account = new AccountWithBalance(currencyCode, Owner, number);
				accounts.Add(number, account);
			}
			else if (account.Balance.CurrencyCode != currencyCode.Iso4217Code)
			{
				throw new GameEngineException($" Account {number} it's in {account.Balance.CurrencyCode}. {currencyCode} it's not valid.");
			}

			return account;
		}
		internal bool SearchByNumber(Currencies.CODES currencyCode, out Balance balance)
		{
			string accountNumber = $"{currencyCode}";
			return SearchByNumber(accountNumber, out balance);
		}

		internal bool SearchByNumber(string accountNumber, out Balance balance)
		{
			balance = null;
			AccountWithBalance result;
			accounts.TryGetValue(accountNumber, out result);

			if (result != null)
			{
				balance = result.Balance;
				return true;
			}
			return false;
		}


		internal bool CheckIfExistsAccount(string accountNumber)
		{
			return accounts.ContainsKey(accountNumber);
		}

		internal bool CheckIfExistsAccountIn(Coin currencyCode)
		{
			foreach (AccountWithBalance account in accounts.Values.ToArray() )
			{
				if (account.Balance.CurrencyCode == currencyCode.Iso4217Code) return true;
			}
			return false;
		}

		[Obsolete]
		internal Balance SearchByNumber(Currencies.CODES currencyCode)
		{
			AccountWithBalance result;
			string accountNumber = $"{currencyCode}";
			accounts.TryGetValue(accountNumber, out result);

			if (result == null)
			{
				throw new GameEngineException($"There is no account with number {accountNumber}.");
			}
			return result.Balance;
		}

		internal Balance SearchByCoin(string currencyCode)
		{
			string accountNumber = $"{currencyCode}";
			AccountWithBalance result = SearchByNumber(accountNumber);
			return result.Balance;
		}

		internal AccountWithBalance SearchByNumber(string accountNumber)
		{
			AccountWithBalance result;
			accounts.TryGetValue(accountNumber, out result);

			if (result == null)
			{
				throw new GameEngineException($"There is no account with number {accountNumber}.");
			}
			return result;
		}

		internal AccountWithBalance SearchAccountWithBalanceGreaterThan(Currency currency)
		{
			foreach (AccountWithBalance account in accounts.Values.ToArray())
			{
				var balance = account.Balance;
				if (balance.Coin == currency.Coin)
				{
					if (balance.HasEnoughFor(currency)) return account;
				}
			}

			throw new GameEngineException($"No account with balance greater than {currency.CurrencyCode} {currency.Value}.");
		}

		internal AccumulatedBalanceWithAccounts GetAccumulatedBalance(Coin currencyCode)
		{
			var accumulatedBalance = new AccumulatedBalanceWithAccounts(currencyCode);
			foreach (AccountWithBalance account in accounts.Values.ToArray())
			{
				var balance = account.Balance;
				if (balance.CurrencyCode == currencyCode.Iso4217Code)
				{
					accumulatedBalance.Add(account);
				}
			}

			return accumulatedBalance;
		}

		internal bool AnyAccountHasEnoughFor(Currency currency)
		{
			foreach (AccountWithBalance account in accounts.Values.ToArray())
			{
				var balance = account.Balance;
				if (balance.Coin == currency.Coin)
				{
					if (balance.HasEnoughFor(currency)) return true;
				}
			}

			return false;
		}

		internal Balance BalanceOrNull(Currencies.CODES currencyCode)
		{
			AccountWithBalance accountWithBalance = null;
			string accountNumber = $"{currencyCode}";
			accounts.TryGetValue(accountNumber, out accountWithBalance);
			if ( accountWithBalance != null)
			{
				return accountWithBalance.Balance;
			}
			return null;
		}

		internal void Lock(bool itIsThePresent, DateTime date, Currency amount, Source source, string who, string documentNumber, Store store, string concept, string reference, int processorId)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));

			CreateNewAccountIfNotExists(amount.Coin).Lock(itIsThePresent, date, amount, source, who, documentNumber, store, concept, reference, processorId);
		}

		internal void Lock(bool itIsThePresent, DateTime date, Currency amount, string who, string documentNumber, Store store, string concept, string reference, int processorId)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));

			CreateNewAccountIfNotExists(amount.Coin).Lock(itIsThePresent, date, amount, who, documentNumber, store, concept, reference, processorId);
		}

		internal void UnLock(bool itIsThePresent, DateTime date, Currency amount, Source source, string who, string documentNumber, Store store, string concept, string reference, int processorId)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));

			CreateNewAccountIfNotExists(amount.Coin).UnLock(itIsThePresent, date, amount, source, who, documentNumber, store, concept, reference, processorId);
		}

		internal void UnLock(MovementsCollector movements, Currency amount, Authorization authorization, string concept, string reference, int processorId)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));

			CreateNewAccountIfNotExists(amount.Coin).UnLock(movements, amount, authorization, concept, reference, processorId);
		}

		internal void Accredit(bool itIsThePresent, DateTime date, Currency amount, Source source, string who, string documentNumber, Store store, string concept, string reference, int processorId)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));

			CreateNewAccountIfNotExists(amount.Coin).Credit(itIsThePresent, date, amount, source, who, documentNumber, store, concept, reference, processorId);

		}

		internal void Accredit(MovementsCollector movements, Currency amount, Authorization authorization, string concept, string reference, int processorId)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));

			CreateNewAccountIfNotExists(amount.Coin).Credit(movements, amount, authorization, concept, reference, processorId);
		}

		internal void Withdraw(MovementsCollector movements, Currency amount, Authorization authorization, string concept, string reference, int processorId)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));

			CreateNewAccountIfNotExists(amount.Coin).Debit(movements, amount, authorization, concept, reference, processorId);
		}

		internal void Withdraw(bool itIsThePresent, DateTime date, Currency amount, Source source, string who, string documentNumber, Store store, string concept, string reference, int processorId)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));

			CreateNewAccountIfNotExists(amount.Coin).Debit(itIsThePresent, date, amount, source, who, documentNumber, store, concept, reference, processorId);
		}

		internal void Unlock(bool itIsThePresent, DateTime date, Currency amount, Source source, string who, string documentNumber, Store store, string concept, string reference, int processorId)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));

			CreateNewAccountIfNotExists(amount.Coin).UnLock(itIsThePresent, date, amount, source, who, documentNumber, store, concept, reference, processorId);
		}

		internal MovementsCollector TakeMovementCollector(DateTime now, Store store, int authorizationNumber)
		{
			MovementsCollector result = new MovementsCollector(now, store, this.Owner, authorizationNumber);
			movementsCollector.Add(result);
			return result;
		}

		internal void ApplyChanges(int bufferId, bool itIsThePresent, string who)
		{
			if (who == null) throw new ArgumentNullException(nameof(who));

			foreach (MovementsCollector collector in movementsCollector)
			{
				collector.Who = who;
				collector.Save(bufferId, itIsThePresent);
				Movements.Storage.SavePayedFragment(itIsThePresent, collector);
			}

			Owner.Forget();
			this.movementsCollector.Clear();
		}
	}

	[Puppet]
	internal sealed class AccumulatedBalances : Objeto
	{
		private readonly List<AccumulatedBalanceWithAccounts> balances = new List<AccumulatedBalanceWithAccounts>();
		internal IEnumerable<AccumulatedBalanceWithAccounts> Balances 
		{ 
			get 
			{ 
				return balances; 
			} 
		}

		internal bool IsEmpty
        {
			get
            {
				return balances.Count == 0;
			}
        }

		internal void Add(AccumulatedBalanceWithAccounts accumulatedBalance)
        {
			balances.Add(accumulatedBalance);

		}
	}

	[Puppet]
	internal sealed class AccumulatedBalanceWithAccounts : Objeto
	{
		internal string CurrencyCode { get;}

		internal string CurrencyCodeAsText
		{
			get
			{
				return CurrencyCode.ToString();
			}
		}

		internal decimal Available { get; private set; }

		internal decimal Locked { get; private set; }

		internal bool IsEmpty
		{
			get
			{
				return accounts.Count == 0;
			}
		}

		private readonly List<AccountWithBalance> accounts = new List<AccountWithBalance>();
		internal IEnumerable<AccountWithBalance> Accounts 
		{ 
			get 
			{ 
				return accounts; 
			} 
		}

		internal AccumulatedBalanceWithAccounts(Coin currencyCode)
        {
			CurrencyCode = currencyCode.Iso4217Code;
		}

		internal void Add(AccountWithBalance account)
		{
			if (account == null) throw new ArgumentNullException(nameof(account));
			if (account.Balance.CurrencyCode != CurrencyCode) throw new GameEngineException($"It is not possible to add {nameof(account)} because it is using '{account.Balance.CurrencyCode}' but accumulated balance uses '{CurrencyCode}'.");

			accounts.Add(account);
			Available += account.Balance.Available;
			Locked += account.Balance.Locked;
		}
	}

	[Puppet]
	internal sealed class AccountWithBalance : Objeto
	{
		internal string Number { get; }
		internal AtAddress Owner { get; }

		internal AccountWithBalance(Coin currencyCode, AtAddress owner, string number)
		{
			if (owner == null) throw new GameEngineException(nameof(owner));
			if (string.IsNullOrEmpty(number)) throw new GameEngineException(nameof(number));

			Number = number;
			Owner = owner;
			Balance = new Balance(currencyCode, this);
		}

		internal Balance Balance { get; }

		internal void Accredit(bool itIsThePresent, DateTime now, Currency amount, Source source, string who, string documentNumber, Store store, string concept, string reference)
		{
			Accredit(itIsThePresent, now, amount, source, who, documentNumber, store, concept, reference, WholePaymentProcessor.NoPaymentProcessor);
		}

		internal void Accredit(bool itIsThePresent, DateTime now, Currency amount, Source source, string who, string documentNumber, Store store, string concept, string reference, int processorId)
		{
			Balance.Accredit(itIsThePresent, now, amount, source, who, documentNumber, store, concept, reference, processorId);
		}

		internal void Lock(bool itIsThePresent, DateTime now, Currency amount, string who, string documentNumber, Store store, string concept, string reference)
		{
			Lock(itIsThePresent, now, amount, who, documentNumber, store, concept, reference, WholePaymentProcessor.NoPaymentProcessor);
		}

		internal void Lock(bool itIsThePresent, DateTime now, Currency amount, string who, string documentNumber, Store store, string concept, string reference, int processorId)
		{
			Balance.Lock(itIsThePresent, now, amount, who, documentNumber, store, concept, reference, processorId);
		}

		internal void Withdraw(bool itIsThePresent, DateTime now, Currency amount, Source source, string who, string documentNumber, Store store, string concept, string reference)
		{
			Withdraw(itIsThePresent, now, amount, source, who, documentNumber, store, concept, reference, WholePaymentProcessor.NoPaymentProcessor);
		}

		internal void Withdraw(bool itIsThePresent, DateTime now, Currency amount, Source source, string who, string documentNumber, Store store, string concept, string reference, int processorId)
		{
			Balance.Withdraw(itIsThePresent, now, amount, source, who, documentNumber, store, concept, reference, processorId);
		}

		internal void Unlock(bool itIsThePresent, DateTime now, Currency amount, string who, string documentNumber, Store store, string concept, string reference)
		{
			Unlock(itIsThePresent, now, amount, who, documentNumber, store, concept, reference, WholePaymentProcessor.NoPaymentProcessor);
		}

		internal void Unlock(bool itIsThePresent, DateTime now, Currency amount, string who, string documentNumber, Store store, string concept, string reference, int processorId)
		{
			Balance.UnLock(itIsThePresent, now, amount, who, documentNumber, store, concept, reference, processorId);
		}

		internal void UnLock(MovementsCollector movements, Currency amount, Authorization authorization, string concept, string reference, int processorId)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));

			Balance.UnLock(movements, amount, authorization, concept, reference, processorId);
		}

		internal void Accredit(MovementsCollector movements, Currency amount, Authorization authorization, string concept, string reference, int processorId)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));

			Balance.Credit(movements, amount, authorization, concept, reference, processorId);
		}

		internal void Withdraw(MovementsCollector movements, Currency amount, Authorization authorization, string concept, string reference, int processorId)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));

			Balance.Debit(movements, amount, authorization, concept, reference, processorId);
		}
	}
}
