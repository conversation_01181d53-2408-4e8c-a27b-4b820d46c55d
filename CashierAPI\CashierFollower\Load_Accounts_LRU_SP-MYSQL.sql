﻿USE `pruebas`;
DROP procedure IF EXISTS `Load_Accounts_LRU_SP`;

USE `pruebas`;
DROP procedure IF EXISTS `pruebas`.`Load_Accounts_LRU_SP`;
;

DELIMITER $$
USE `pruebas`$$
CREATE DEFINER=`root`@`%` PROCEDURE `Load_Accounts_LRU_SP`()
BEGIN
	DECLARE table_exists INT DEFAULT 0;
    
    DECLARE t_name VARCHAR(50);
    DECLARE done INT DEFAULT FALSE;
	DECLARE cur CURSOR FOR SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME NOT LIKE '%\_%' AND (TABLE_NAME LIKE 'C%' OR TABLE_NAME LIKE 'c%') ORDER BY TABLE_NAME ASC;
	DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

	SELECT COUNT(*) INTO table_exists FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'accountsLRU';
    IF table_exists = 0 THEN
        CREATE TABLE accountsLRU(Cuenta VARCHAR(50) PRIMARY KEY, FechaHora datetime);
    END IF;
    
	OPEN cur;
	bucle: LOOP
		FETCH cur INTO t_name;
		IF done THEN
		  LEAVE bucle;
		END IF;

		SET @command = CONCAT('INSERT INTO accountsLRU(Cuenta, FechaHora) SELECT \'', t_name, '\', FechaHora FROM ', t_name, ' AS t2 ORDER BY id DESC LIMIT 1 ON DUPLICATE KEY UPDATE FechaHora = t2.FechaHora;');
		PREPARE stmt FROM @command;
		EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        
	END LOOP;
	CLOSE cur;
END$$

DELIMITER ;
;
