﻿using Puppeteer.EventSourcing;
using Puppeteer;

namespace CashierAPI.CashierFollower
{
    public class WithdrawRule : RuleWithoutActor
    {
        protected const string START_TEXT_LINE1 = "atAddress.GetOrCreateSource(";
        protected const string START_TEXT_LINE2 = ".Withdraw(";
        protected const string START_TEXT_LINE3 = ", Currency('";
        protected const string END_TEXT_LINE3 = "',";
        protected const string TEXT_SetInitialBalance = "balance.SetInitialBalance(";
        protected const string END_TEXT_LINE = ");";

        public override void Then(<PERSON>ript script)
        {
            if (script.Text.IndexOf(TEXT_SetInitialBalance) != -1)
            {
                UtilStringRules.SetHeaderText(START_TEXT_LINE3, END_TEXT_LINE3);
                var currencyCode = UtilStringRules.GetBodyArgText(script.Text).Trim();
                if (InitialBalanceHandler.ExistsBalance(currencyCode)) DBHelperCashierFollower.ProceedToMarkSkipsForSpecificDairyId(InitialBalanceHandler.GetCurrentId(currencyCode));
                InitialBalanceHandler.SetCurrentId(currencyCode, script.DairyId);
            }
            else if (script.DairyId != LastIdRule.Id)
            {
                DBHelperCashierFollower.ProceedToMarkSkipsForSpecificDairyId(script.DairyId);
            }
        }

        public override void When(Script script)
        {
            if (script.Text.IndexOf(START_TEXT_LINE1) != -1 && script.Text.IndexOf(START_TEXT_LINE2) != -1 && script.Text.IndexOf(START_TEXT_LINE3) != -1)
            {
                Then(script);
            }

        }

        public override void FinalizeRule()
        {
            return;
        }
    }
}
