﻿using GamesEngine.Domains;
using GamesEngine.Games.Lotto;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GamesEngine.Bets
{
    internal class PlayersProfiles:Objeto
    {
        private readonly Dictionary<Player, PlayerProfile> profilesPerPlayers = new Dictionary<Player, PlayerProfile>();
        private readonly BetRangeProfiles betRangeProfilesPick2 = new BetRangeProfiles();
        private readonly BetRangeProfiles betRangeProfilesPick3 = new BetRangeProfiles();
        private readonly BetRangeProfiles betRangeProfilesPick4 = new BetRangeProfiles();
        private readonly BetRangeProfiles betRangeProfilesPick5 = new BetRangeProfiles();
        private readonly BetRangeProfiles betRangeProfilesPowerball = new BetRangeProfiles();
        private readonly BetRangeProfiles betRangeProfilesKeno = new BetRangeProfiles();
        private readonly Profiles profiles = new Profiles();

        internal BetRangeProfiles BetRangeProfilesByPick(int pick)
        {
            if (!PicksLotteryGame.PickValues.Contains(pick)) throw new GameEngineException($"{nameof(pick)} {pick} is not valid");
            switch (pick)
            {
                case 2:
                    return betRangeProfilesPick2;
                case 3:
                    return betRangeProfilesPick3;
                case 4:
                    return betRangeProfilesPick4;
                case 5:
                    return betRangeProfilesPick5;
                default:
                    throw new GameEngineException($"There is no {nameof(BetRangeProfiles)} implementation for {nameof(pick)} {pick}");
            }
        }

        internal BetRangeProfiles BetRangeProfilesForPowerball
        {
            get
            {
                return betRangeProfilesPowerball;
            }
        }

        internal BetRangeProfiles BetRangeProfilesKeno
        {
            get
            {
                return betRangeProfilesKeno;
            }
        }

        internal Profiles Profiles
        {
            get
            {
                return profiles;
            }
        }

        internal IEnumerable<Player> Players()
        {
            var result = profilesPerPlayers.Keys.ToList();
            return result;
        }

        internal PlayerProfile PlayerProfileOf(Player player)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));

            var profile = profilesPerPlayers.GetValueOrDefault(player);
            if (profile == null) throw new GameEngineException($"{nameof(player)} {player.Nickname} does not have profiles assigned");
            return profile;
        }

        private bool HasBetRangePlayerProfileForPick(int pick, Profile profile, Domain domain)
        {
            switch (pick)
            {
                case 2:
                    var hasBetRangeProfile = betRangeProfilesPick2.GetAll.Any(x => x.Profile == profile && x.Domain == domain);
                    return hasBetRangeProfile;
                case 3:
                    hasBetRangeProfile = betRangeProfilesPick3.GetAll.Any(x => x.Profile == profile && x.Domain == domain);
                    return hasBetRangeProfile;
                case 4:
                    hasBetRangeProfile = betRangeProfilesPick4.GetAll.Any(x => x.Profile == profile && x.Domain == domain);
                    return hasBetRangeProfile;
                case 5:
                    hasBetRangeProfile = betRangeProfilesPick5.GetAll.Any(x => x.Profile == profile && x.Domain == domain);
                    return hasBetRangeProfile;
                default:
                    throw new GameEngineException($"There is no {nameof(BetRangeProfile)} implementation for {nameof(pick)} {pick}");
            }
        }

        internal bool HasBetRangePlayerProfileForPick(int pick, Player player, Domain domain)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            var hasProfile = profilesPerPlayers.ContainsKey(player);
            if (hasProfile)
            {
                switch (pick)
                {
                    case 2:
                        var playerProfile = PlayerProfileOf(player);
                        hasProfile = HasBetRangePlayerProfileForPick(pick, playerProfile.Profile, domain);
                        return hasProfile;
                    case 3:
                        playerProfile = PlayerProfileOf(player);
                        hasProfile = HasBetRangePlayerProfileForPick(pick, playerProfile.Profile, domain);
                        return hasProfile;
                    case 4:
                        playerProfile = PlayerProfileOf(player);
                        hasProfile = HasBetRangePlayerProfileForPick(pick, playerProfile.Profile, domain);
                        return hasProfile;
                    case 5:
                        playerProfile = PlayerProfileOf(player);
                        hasProfile = HasBetRangePlayerProfileForPick(pick, playerProfile.Profile, domain);
                        return hasProfile;
                    default:
                        throw new GameEngineException($"There is no {nameof(BetRangeProfile)} implementation for {nameof(pick)} {pick}");
                }
            }
            return hasProfile;
        }

        private BetRangeProfile BetRangePlayerProfileForPick(int pick, Profile profile, Domain domain)
        {
            switch (pick)
            {
                case 2:
                    var betRangeProfile = betRangeProfilesPick2.GetAll.Single(x => x.Profile == profile && x.Domain == domain);
                    return betRangeProfile;
                case 3:
                    betRangeProfile = betRangeProfilesPick3.GetAll.Single(x => x.Profile == profile && x.Domain == domain);
                    return betRangeProfile;
                case 4:
                    betRangeProfile = betRangeProfilesPick4.GetAll.Single(x => x.Profile == profile && x.Domain == domain);
                    return betRangeProfile;
                case 5:
                    betRangeProfile = betRangeProfilesPick5.GetAll.Single(x => x.Profile == profile && x.Domain == domain);
                    return betRangeProfile;
                default:
                    throw new GameEngineException($"There is no {nameof(BetRangeProfile)} implementation for {nameof(pick)} {pick}");
            }
        }

        internal decimal MaxAmountForPick(int pick, Player player, Domain domain)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            var playerProfile = PlayerProfileOf(player);
            if (playerProfile == null) throw new GameEngineException($"{nameof(player)} {player.Nickname} does not have profile for url {domain.Url}");
            switch (pick)
            {
                case 2:
                    var betRangeProfile = BetRangePlayerProfileForPick(pick, playerProfile.Profile, domain);
                    return betRangeProfile.MaxAmount;
                case 3:
                    betRangeProfile = BetRangePlayerProfileForPick(pick, playerProfile.Profile, domain);
                    return betRangeProfile.MaxAmount;
                case 4:
                    betRangeProfile = BetRangePlayerProfileForPick(pick, playerProfile.Profile, domain);
                    return betRangeProfile.MaxAmount;
                case 5:
                    betRangeProfile = BetRangePlayerProfileForPick(pick, playerProfile.Profile, domain);
                    return betRangeProfile.MaxAmount;
                default:
                    throw new GameEngineException($"There is no {nameof(BetRangeProfile)} implementation for {nameof(pick)} {pick}");
            }
        }

        internal decimal MinAmountForPick(int pick, Player player, Domain domain)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            var playerProfile = PlayerProfileOf(player);
            if (playerProfile == null) throw new GameEngineException($"{nameof(player)} {player.Nickname} does not have profile for url {domain.Url}");
            switch (pick)
            {
                case 2:
                    var betRangeProfile = BetRangePlayerProfileForPick(pick, playerProfile.Profile, domain);
                    return betRangeProfile.MinAmount;
                case 3:
                    betRangeProfile = BetRangePlayerProfileForPick(pick, playerProfile.Profile, domain);
                    return betRangeProfile.MinAmount;
                case 4:
                    betRangeProfile = BetRangePlayerProfileForPick(pick, playerProfile.Profile, domain);
                    return betRangeProfile.MinAmount;
                case 5:
                    betRangeProfile = BetRangePlayerProfileForPick(pick, playerProfile.Profile, domain);
                    return betRangeProfile.MinAmount;
                default:
                    throw new GameEngineException($"There is no {nameof(BetRangeProfile)} implementation for {nameof(pick)} {pick}");
            }
        }

        private bool HasBetRangePlayerProfileForKeno(Profile profile, Domain domain)
        {
            var hasBetRangeProfile = betRangeProfilesKeno.GetAll.Any(x => x.Profile == profile && x.Domain == domain);
            return hasBetRangeProfile;
        }
        private bool HasBetRangePlayerProfileForPowerball(Profile profile, Domain domain)
        {
            var hasBetRangeProfile = betRangeProfilesPowerball.GetAll.Any(x => x.Profile == profile && x.Domain == domain);
            return hasBetRangeProfile;
        }

        internal bool HasBetRangePlayerProfileForPowerball(Player player, Domain domain)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            var hasProfile = profilesPerPlayers.ContainsKey(player);
            if (hasProfile)
            {
                var playerProfile = PlayerProfileOf(player);
                hasProfile = HasBetRangePlayerProfileForPowerball(playerProfile.Profile, domain);
            }
            return hasProfile;
        }

        internal bool HasBetRangePlayerProfileForKeno(Player player, Domain domain)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            var hasProfile = profilesPerPlayers.ContainsKey(player);
            if (hasProfile)
            {
                var playerProfile = PlayerProfileOf(player);
                hasProfile = HasBetRangePlayerProfileForKeno(playerProfile.Profile, domain);
            }
            return hasProfile;
        }

        private BetRangeProfile BetRangePlayerProfileForPowerball(Profile profile, Domain domain)
        {
            var betRangeProfile = betRangeProfilesPick2.GetAll.Single(x => x.Profile == profile && x.Domain == domain);
            return betRangeProfile;
        }
        private BetRangeProfile BetRangePlayerProfileForKeno(Profile profile, Domain domain)
        {
            var betRangeProfile = betRangeProfilesKeno.GetAll.Single(x => x.Profile == profile && x.Domain == domain);
            return betRangeProfile;
        }
        internal decimal MaxAmountForPowerball(Player player, Domain domain)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            var playerProfile = PlayerProfileOf(player);
            if (playerProfile == null) throw new GameEngineException($"{nameof(player)} {player.Nickname} does not have profile for url {domain.Url}");
            
            var betRangeProfile = BetRangePlayerProfileForPowerball(playerProfile.Profile, domain);
            return betRangeProfile.MaxAmount;
        }

        internal decimal MinAmountForPowerball(Player player, Domain domain)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            var playerProfile = PlayerProfileOf(player);
            if (playerProfile == null) throw new GameEngineException($"{nameof(player)} {player.Nickname} does not have profile for url {domain.Url}");

            var betRangeProfile = BetRangePlayerProfileForPowerball(playerProfile.Profile, domain);
            return betRangeProfile.MinAmount;
        }

        internal decimal MaxAmountForKeno(Player player, Domain domain)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            var playerProfile = PlayerProfileOf(player);
            if (playerProfile == null) throw new GameEngineException($"{nameof(player)} {player.Nickname} does not have profile for url {domain.Url}");

            var betRangeProfile = BetRangePlayerProfileForKeno(playerProfile.Profile, domain);
            return betRangeProfile.MaxAmount;
        }

        internal decimal MinAmountForKeno(Player player, Domain domain)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            var playerProfile = PlayerProfileOf(player);
            if (playerProfile == null) throw new GameEngineException($"{nameof(player)} {player.Nickname} does not have profile for url {domain.Url}");

            var betRangeProfile = BetRangePlayerProfileForKeno(playerProfile.Profile, domain);
            return betRangeProfile.MinAmount;
        }

        internal void Assign(DateTime now, Player player, Profile profile)
        {
            if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (profile == null) throw new ArgumentNullException(nameof(profile));

            var playerProfile = profilesPerPlayers.GetValueOrDefault(player);
            if (playerProfile == null)
            {
                playerProfile = new PlayerProfile(now, profile);
                profilesPerPlayers.Add(player, playerProfile);
            }
            else
            {
                playerProfile.Update(profile);
            }
        }

        internal void Unassign(DateTime now, Player player)
        {
            if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (player == null) throw new ArgumentNullException(nameof(player));

            var profile = profilesPerPlayers.GetValueOrDefault(player);
            if (profile == null) throw new GameEngineException($"{nameof(player)} {player.Nickname} does not have profiles assigned");
            profilesPerPlayers.Remove(player);
        }
    }

    internal class PlayerProfile : Objeto
    {
        private readonly DateTime creationDate;
        private Profile profile;

        internal DateTime CreationDate
        {
            get
            {
                return creationDate;
            }
        }

        internal Profile Profile
        {
            get
            {
                return profile;
            }
        }

        internal PlayerProfile(DateTime creationDate, Profile profile)
        {
            this.creationDate = creationDate;
            this.profile = profile;
        }

        internal void Update(Profile profile)
        {
            this.profile = profile;
        }
    }
}
