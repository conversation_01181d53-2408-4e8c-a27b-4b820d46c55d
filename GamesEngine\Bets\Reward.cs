﻿using GamesEngine.Games.Tournaments;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;

namespace GamesEngine.Bets
{
	[Puppet]
    public abstract class Reward : Objeto
    {
        private readonly Contributions contributions = new Contributions();
        private string name;
        private readonly int id;

        internal Reward(int id, string name)
        {
            if (String.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            this.name = name;
            this.id = id;
        }

        internal virtual void Contribute(Player player, decimal amount)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (amount <= 0) throw new GameEngineException("Amount must be greater than zero");
            contributions.Add(player, amount);
        }

        internal void Uncontribute(Player player, decimal amount)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (amount <= 0) throw new GameEngineException("Amount must be greater than zero");
            contributions.CancelContribution(player, amount);
        }

        internal int Id
        {
            get
            {
                return this.id;
            }
        }

        public string Name
        {
            set
            {
                if (String.IsNullOrWhiteSpace(value)) throw new ArgumentNullException(nameof(Name));
                name = Business.Accounting.ReplaceAccountNumbers(value);
            }
            get
            {
                return name;
            }
        }

        public void UpdateName(bool itIsThePresent, string name, GameAbbreviation abbreviation)
        {
            if (String.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(Name));
            this.Name = name;
            Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForProfanity,
                new PoolNameMessage(ProfanityMessageType.POOL_NAME, this.Id, this.name, abbreviation));
        }

        protected internal bool Participates(Player player)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            return contributions.Participates(player);
        }

        internal decimal GrandPrice
        {
            get
            {
                return contributions.Total;
            }
        }
        internal int CountOfPlayers()
        {
            int count = contributions.CountOfPlayers();
            return count;
        }
    }
}

