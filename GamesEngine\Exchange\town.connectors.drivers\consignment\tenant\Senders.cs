﻿using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;
using town.connectors;
using town.connectors.drivers;

namespace Connectors.town.connectors.drivers.consignment
{
    public class Senders: ConsignmentTenantDriver
    {
        public Senders() : base(Tenant_Actions.Others)
        {

        }

        public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            throw new NotImplementedException();
        }

        public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            if (CustomSettings.ThereArePendingChanges)
            {
                ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
                SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
                Password = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
            }

            var result = await GetSendersAsync(recordSet);
            return (T)Convert.ChangeType(result, typeof(T));
        }

        async Task<SendersResponse> GetSendersAsync(CustomSettings.RecordSet recordSet)
        {
            ResponseSendersEnvelope responseXml = null;
            string responseString = string.Empty;
            string xmlWithHiddenFields = string.Empty;
            var url = $"{ServicesUrl}?op=Get_senders";
            try
            {
                string xmlString = CreateSoapEnvelope(recordSet, out xmlWithHiddenFields);
                var webRequest = new HttpRequestMessage(HttpMethod.Post, url)
                {
                    Content = new StringContent(xmlString, Encoding.UTF8, "text/xml"),
                };

                Loggers.GetIntance().AccountingConsignmentGetSenders.Debug($@"url:{url} data:{xmlWithHiddenFields}");
                using (HttpResponseMessage response = await httpClient.SendAsync(webRequest, HttpCompletionOption.ResponseHeadersRead))
                {
                    responseString = await response.Content.ReadAsStringAsync();
                }
                Loggers.GetIntance().AccountingConsignmentGetSenders.Debug($@"response:{responseString}");

                XmlSerializer xmls = new XmlSerializer(typeof(ResponseSendersEnvelope));
                responseXml = (ResponseSendersEnvelope)xmls.Deserialize(new StringReader(responseString));
            }
            catch (Exception e)
            {
                Loggers.GetIntance().AccountingConsignmentGetSenders.Error($@"url:{url} data:{xmlWithHiddenFields} type:{e.GetType()} error:{e.Message}", e);

                var extraErrorMessage = string.Empty;
                InternalOnError(nameof(GetSendersAsync), 1, e, $"Url:{url}", $"Request: {xmlWithHiddenFields}", $"Response: {responseString}");
            }

            if (responseXml == null || string.IsNullOrWhiteSpace(responseXml.Body.Get_sendersResponse.Get_sendersResult))
            {
                NotifyWarn(nameof(GetSendersAsync), $"Url:{url}\nResponse: {responseString}", $"Response cannot be empty");
            }
            else
            {
                XmlSerializer serializer = new XmlSerializer(typeof(SendersResponse));
                StringReader reader = new StringReader(responseXml.Body.Get_sendersResponse.Get_sendersResult);
                var result = (SendersResponse)serializer.Deserialize(reader);
                return result;
            }

            return new SendersResponse();
        }

        string CreateSoapEnvelope(CustomSettings.RecordSet recordSet, out string xmlWithHiddenFields)
        {
            var provider = recordSet.Mappings["provider"].AsInt;

            var xmlBuilder = new StringBuilder();
            xmlBuilder.AppendLine("<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">");
            xmlBuilder.AppendLine("<soap:Body>");
            xmlBuilder.AppendLine("<Get_senders xmlns=\"http://tempuri.org/\">");
            xmlBuilder.Append("<user>").Append(SystemId).AppendLine("</user>");
            xmlBuilder.Append("<password>").Append(Password).AppendLine("</password>");
            xmlBuilder.Append("<provider>").Append(provider).AppendLine("</provider>");
            xmlBuilder.AppendLine("</Get_senders>");
            xmlBuilder.AppendLine("</soap:Body>");
            xmlBuilder.AppendLine("</soap:Envelope>");

            var xmlBuilderWithHiddenFields = new StringBuilder();
            xmlBuilderWithHiddenFields.AppendLine("<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">");
            xmlBuilderWithHiddenFields.AppendLine("<soap:Body>");
            xmlBuilderWithHiddenFields.AppendLine("<Get_senders xmlns=\"http://tempuri.org/\">");
            xmlBuilderWithHiddenFields.AppendLine("<user>XXXX</user>");
            xmlBuilderWithHiddenFields.AppendLine("<password>XXXX</password>");
            xmlBuilderWithHiddenFields.Append("<provider>").Append(provider).AppendLine("</provider>");
            xmlBuilderWithHiddenFields.AppendLine("</Get_senders>");
            xmlBuilderWithHiddenFields.AppendLine("</soap:Body>");
            xmlBuilderWithHiddenFields.AppendLine("</soap:Envelope>");

            xmlWithHiddenFields = xmlBuilderWithHiddenFields.ToString();
            return xmlBuilder.ToString();
        }

        [XmlRoot("Envelope", Namespace = "http://schemas.xmlsoap.org/soap/envelope/")]
        public class ResponseSendersEnvelope
        {
            public ResponseSendersBody Body { get; set; }
        }

        public class ResponseSendersBody
        {
            [XmlElement(ElementName = "Get_sendersResponse", Namespace = "http://tempuri.org/")]
            public GetSendersResponse Get_sendersResponse { get; set; }
        }

        public class GetSendersResponse
        {
            public string Get_sendersResult { get; set; }
        }

        [XmlRoot("response")]
        public class SendersResponse
        {
            [XmlElement("message")]
            public MessageResponse Message { get; set; }
            [XmlElement("data")]
            public DataResponse Data { get; set; }
        }

        [XmlRoot("data")]
        public class DataResponse
        {
            [XmlElement("sender")]
            public List<SenderResponse> Sender { get; set; }
            [XmlAttribute("amount")]
            public string Amount { get; set; }
        }

        [XmlRoot("message")]
        public class MessageResponse
        {
            [XmlAttribute("typeId")]
            public string TypeId { get; set; }
            [XmlAttribute("type")]
            public string Type { get; set; }
        }

        [XmlRoot("sender")]
        public class SenderResponse
        {
            [XmlAttribute("name")]
            public string Name { get; set; }
            [XmlAttribute("merchantId")]
            public string MerchantId { get; set; }
        }

        public override void Prepare(DateTime now)
        {
            CustomSettings.AddVariableParameter("provider");

            SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
            Password = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
            ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
        }
    }
}
