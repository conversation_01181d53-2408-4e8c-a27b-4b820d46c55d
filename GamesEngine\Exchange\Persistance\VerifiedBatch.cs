﻿using GamesEngine.Finance;
using System;
using System.Collections.Generic;
using System.Text;

namespace GamesEngine.Exchange.Persistance
{
    public class VerifiedBatch
    {
        public DateTime Date { get; set; }
        public string Type { get; set; }
        public int CurrencyId { get; set; }
        public Coin Currency => Coinage.GetById(CurrencyId);
        public string CurrencyCode => Currency.Iso4217Code;
        public decimal Initial { get; set; }
        public decimal Available { get; set; }
        public decimal Locked { get; set; }
        public decimal LockedAccumulated { get; set; }
        public decimal SpendAccumulated { get; set; }
        public long BatchNumber { get; set; }
    }
}
