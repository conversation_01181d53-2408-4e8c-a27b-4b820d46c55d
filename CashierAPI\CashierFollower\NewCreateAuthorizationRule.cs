﻿using GamesEngine.PurchaseOrders;
using MySql.Data.MySqlClient;
using Puppeteer;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;

namespace CashierAPI.CashierFollower
{
    public class NewCreateAuthorizationRule : RuleWithoutActor
    {
        protected const string START_TEXT_LINE = "atAddress.CreateAuthorization(";
        protected const string END_TEXT_LINE = ");";
        protected const string START_TEXT_LINEAuthorizationsNumbers = "auths = AuthorizationsNumbers(";
        protected const string START_TEXT_LINEAuthorizationsNumbersAdd = "auths.Add(";
        protected const string START_TEXT_LINE3 = ", Currency('";
        protected const string END_TEXT_LINE3 = "',";
        protected const string TEXT_SetInitialBalance = "balance.SetInitialBalance(";

        public override void Then(<PERSON>ript script)
        {
            var authorizationNumbers = AuthorizationNumbers(script.Text);
            var currencyCode = CurrencyCode(script.Text);
            if (script.DairyId == InitialBalanceHandler.GetLastId(currencyCode) && script.Text.IndexOf(TEXT_SetInitialBalance) != -1) 
            {
                InitialBalanceHandler.SetLastAuthorizationNumbers(currencyCode, authorizationNumbers);
                if (InitialBalanceHandler.ExistsBalance(currencyCode)) DBHelperCashierFollower.ProceedToMarkSkipsForSpecificDairyId(InitialBalanceHandler.GetCurrentId(currencyCode));
            }
            var isAuthorizationWithFP = currencyCode == "FP";
            if (authorizationNumbers!=null && (!isAuthorizationWithFP || InitialBalanceHandler.OldFPCommandsWithSkip))
            {
                DBHelperCashierFollower.AddNewAuthorization(authorizationNumbers, script.DairyId, currencyCode);
            }

        }

        public override void When(Script script)
        {
            if (script.Text.IndexOf(START_TEXT_LINE) != -1 && script.Text.IndexOf(START_TEXT_LINE3) != -1)
            {
                Then(script);
            }
        }

        public string[] AuthorizationNumbers(string script)
        {
            if (script.IndexOf(START_TEXT_LINEAuthorizationsNumbers) != -1)
            {
                UtilStringRules.SetHeaderText(START_TEXT_LINEAuthorizationsNumbers, END_TEXT_LINE);
                var body = UtilStringRules.GetBodyArgText(script).Trim();

                string[] arguments = body.Split(',');
                int authorization = int.Parse(arguments[0]);
                var consecutive = arguments.Length > 1 ? int.Parse(arguments[1]) : 1;
                var authorizationNumbers = arguments.Length > 1 ? new AuthorizationsNumbers(authorization, consecutive) : new AuthorizationsNumbers(authorization);

                if (script.IndexOf(START_TEXT_LINEAuthorizationsNumbersAdd) != -1)
                {
                    UtilStringRules.SetHeaderText(START_TEXT_LINEAuthorizationsNumbersAdd, END_TEXT_LINE);
                    body = UtilStringRules.GetBodyArgText(script).Trim();
                    arguments = body.Split(").Add(");
                    foreach (var arg in arguments)
                    {
                        if (int.TryParse(arg, out authorization))
                        {
                            authorizationNumbers.Add(authorization);
                        }
                        else
                        {
                            var addArgs = arg.Split(',');
                            authorization = int.Parse(addArgs[0]);
                            consecutive = addArgs.Length > 1 ? int.Parse(addArgs[1]) : 1;
                            authorizationNumbers.Add(authorization, consecutive);
                        }
                    }
                }
                return authorizationNumbers.Numbers.Select(n => n.ToString()).ToArray();
            }
            else
            {
                UtilStringRules.SetHeaderText(START_TEXT_LINE, END_TEXT_LINE);
                var body = UtilStringRules.GetBodyArgText(script);
                var arguments = UtilStringRules.GetArguments(body);
                var authorizationsJoinedByComma = arguments[3].Trim();

                if (authorizationsJoinedByComma.StartsWith("{") && authorizationsJoinedByComma.EndsWith("}"))
                {
                    var authorizations = authorizationsJoinedByComma.TrimStart('{').TrimEnd('}').Split(',');
                    bool isValidAuthorization = true;
                    foreach (var authorizationNumber in authorizations)
                    {
                        if (!int.TryParse(authorizationNumber, out int authorizationNumberInt))
                        {
                            isValidAuthorization = false;
                            break;
                        }
                    }
                    if (!isValidAuthorization)
                    {
                        authorizationsJoinedByComma = arguments[4].Trim();
                        authorizations = authorizationsJoinedByComma.TrimStart('{').TrimEnd('}').Split(',');
                    }

                    return authorizations;
                }
                else
                {
                    return new string[] { authorizationsJoinedByComma };
                }
            }
        }

        private string CurrencyCode(string script)
        {
            UtilStringRules.SetHeaderText(START_TEXT_LINE3, END_TEXT_LINE3);
            var currencyCode = UtilStringRules.GetBodyArgText(script).Trim();
            return currencyCode;
        }

        public override void FinalizeRule()
        {
            return;
        }
    }
}
