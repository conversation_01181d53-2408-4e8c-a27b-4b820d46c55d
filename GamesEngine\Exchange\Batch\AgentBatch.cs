﻿using GamesEngine.Finance;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Exchange.Batch
{
	[Puppet]
	internal class AgentBatch : BatchSet
	{
		private readonly BatchSet parentBatch;
		private readonly string name;
		private readonly DateTime creationDate;
		private int level;
		private AgentBatchCosts costs;
		internal AgentBatchCosts Costs
		{ 
			get 
			{
				if (costs != null)
				{
					return costs;
				}
				else if (parentBatch is MarketplaceBatch  && costs ==  null)
				{
					costs = new AgentBatchCosts();
					return costs;
				}
				else if (parentBatch is AgentBatch)
				{
					return ((AgentBatch)parentBatch).Costs;
				}
				else
				{
					throw new GameEngineException($" There is no any costs assigned to this batchset.");
				}
			} 
		}

		internal AgentBatch(BatchSet parentBatch, string name, DateTime creationDate) : base(parentBatch.Marketplace, creationDate)
		{
			if(parentBatch == null) throw new ArgumentNullException(nameof(parentBatch));
			if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

			this.parentBatch = parentBatch;
			this.name = name;
			this.creationDate = creationDate;
			this.level = parentBatch.Level;
			this.level++;
			this.consecutive = parentBatch.Consecutive;
			this.consecutive++;
		}

		internal override void ReceiveAmount(Currency amount, Currency cost, string employeeName)
		{
			//TODO erick revisar  if (costs[cost.CurrencyCode] != null) throw new GameEngineException("It's not possible to receive more available again to the same currency.");
			// SOlo la agencia puede llamar este metodo
			//Solo el agente puede llamar Assing

			if (this.parentBatch.InitialAccountItsNotConfiguredFor(amount.Coin)) throw new GameEngineException($"There are no {nameof(SetOfBalances)} for the marketplace yet.");
			if (outTransaction.Available(amount.Coin).Value > 0) throw new GameEngineException("Marketplace batch already has funds.");
			if (cost == null) throw new GameEngineException("Cost is not defined.");
			if (amount.Value <= 0) throw new GameEngineException("Amount to receive can not be negative or zero");
			if (cost.Value <= 0) throw new GameEngineException("Currency cost can not be negative or zero");

			if (InitialAccountItsNotConfiguredFor()) LoadNewInitialAccounts(new SetOfBalances(this.parentBatch.InitialAccount));

			outTransaction.Deposit(amount, cost, employeeName);
			outTransaction.InitialAmount(amount, employeeName);

			((MarketplaceBatch)this.parentBatch).InitialTransaction.WithDraw(amount, employeeName);//TODO Erick,  revisar
			((MarketplaceBatch)this.parentBatch).InitialTransaction.UnLock(amount, employeeName);

			FixedExchangeRate costRate = new FixedExchangeRate(amount, cost);
			Costs.Add(amount.Coin, costRate);
		}
		internal void AssignFunds(string agentName, int batchNumber, Currency amount, string employeeName, DateTime now)
		{
			if (InitialAccountItsNotConfiguredFor(amount.Coin)) throw new GameEngineException($"There are no {nameof(SetOfBalances)} for the marketplace yet.");
			if (string.IsNullOrWhiteSpace(agentName)) throw new ArgumentNullException(nameof(agentName));
			if (batchNumber <= 0) throw new GameEngineException("Invalid batch number");
			if (amount == null) throw new ArgumentNullException(nameof(amount));
			if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));
			if (now == default(DateTime)) throw new ArgumentNullException(nameof(now));

			AgentBatch child = agents.FirstOrDefault(x => x.Name == agentName);
			if (child == null) throw new GameEngineException($"Agent {agentName} does not exist");
			if (child.agents == null) child.agents = new List<AgentBatch>();
			
			if (child.InitialAccountItsNotConfiguredFor()) child.LoadNewInitialAccounts(new SetOfBalances(InitialAccount));

			BatchTransactions batchTransaction = child.BatchTransactions;
			if (batchTransaction == null) batchTransaction = child.CreateNewBatchTransaction(batchNumber, now);
			if (outTransaction.Available(amount.Coin).Value < amount.Value) throw new GameEngineException($"There is no enough available in {amount.GetType()}.  You can not assign a greater amount than available.");
			if (!child.IsFinalAgent && !batchTransaction.IsReady) throw new GameEngineException($"Can not assign funds to {agentName} because is an internal agent or batch status is not {BatchStatus.READY.ToString()}");

			outTransaction.WithDraw(amount, employeeName);
			outTransaction.UnLock(amount, employeeName);

			batchTransaction.Initial(amount, employeeName, now);
			batchTransaction.Deposit(amount, employeeName, now);
		}

		internal FixedExchangeRate CostsFor(Coin currencyCode)
		{
			return Costs.For(currencyCode);
		}

		internal string Name
		{
			get
			{
				return this.name;
			}
		}

		internal override string FullName
		{
			get
			{
				return parentBatch.FullName + AGENT_SEPARATOR + name;
			}
		}

		internal DateTime CreationDate
		{
			get
			{
				return this.creationDate;
			}
		}

		internal BatchSet Parent
		{
			get
			{
				return this.parentBatch;
			}
		}

		internal override int Level
		{
			get
			{
				return this.level;
			}

			set
			{
				this.level = value;
			}
		}

		internal string Prefix
		{
			get
			{
				return this.name + "_" + consecutive;
			}
		}

		internal override void ApplyBalancesModifications(BatchMovements draftMovements)
		{
			if (IsFinalAgent)
			{
				foreach (BatchMovement movement in draftMovements.List())
				{
					if (movement.BalanceType == Balances.Types.NONE)
					{
						continue;
					}
					else if (movement.BalanceType == Balances.Types.IN)
					{
						continue;
					}
					else if (movement.BalanceType == Balances.Types.OUT)
					{
						ApplyOutBalancesModifications(movement);
					}
					else
					{
						throw new GameEngineException($"There is no valid implementation for {movement.BalanceType} in {nameof(ApplyBalancesModifications)}");
					}
				}
				this.parentBatch.ApplyBalancesModifications(draftMovements);
			}
			else if (parentBatch != null)
			{
				foreach (BatchMovement movement in draftMovements.List())
				{
					if (movement.BalanceType == Balances.Types.NONE)
					{
						continue;
					}
					else if (movement.BalanceType == Balances.Types.IN)
					{
						ApplyInBalancesModifications(movement);
					}
					else if (movement.BalanceType == Balances.Types.OUT)
					{
						ApplyOutBalancesModifications(movement);
					}
					else
					{
						throw new GameEngineException($"There is no valid implementation for {movement.BalanceType} in {nameof(ApplyBalancesModifications)}");
					}
				}
				this.parentBatch.ApplyBalancesModifications(draftMovements);
			}
			else
			{
				throw new GameEngineException($"There is no valid implementation for {nameof(AgentBatch)} with {nameof(parentBatch)} empty.");
			}
		}

		internal override void Close(bool itIsThePresent, DateTime closeDate, string employee)
		{
			base.CloseCurrentBatchSet(itIsThePresent, closeDate);

			InitialAccount.TakeSecondSnap();
		}

		internal override void Verify(bool itIsThePresent, DateTime verifyDate, string employee)
		{
			base.VerifyCurrentBatchSet(itIsThePresent, verifyDate);
		}

		internal override void Open(bool itIsThePresent, DateTime openedDate, string employee)
		{
			if (InitialAccountItsNotConfiguredFor()) throw new GameEngineException($"There are no {nameof(SetOfBalances)} for the marketplace yet.");
			if (! this.parentBatch.ItsOpen) throw new GameEngineException("This batch could not be open, because it has not been opened by the admin.");

			base.OpenCurrentBatchSet(itIsThePresent, openedDate);

			InitialAccount.TakeFirstSnap();
		}

		internal bool CanOpen()
		{
			bool result = (! (this.ItsOpen) && this.parentBatch.ItsOpen);
			return result;
		}
		internal Transaction SearchTransactionByTrackinginformation(string trackingCode, string reference)
		{
			return BatchTransactions.SearchTransactionByTrackinginformation(trackingCode, reference);
		}
	}

	internal class AgentBatchCosts
	{
		private Dictionary<Coin, FixedExchangeRate> costs;

		internal AgentBatchCosts()
		{
			costs= new Dictionary<Coin, FixedExchangeRate>();
		}

		internal void Add(Coin currencyCode, FixedExchangeRate costRate)
		{
			costs.Add(currencyCode, costRate);
		}

		internal FixedExchangeRate For(Coin currencyCode)
		{
			return costs[currencyCode];
		}
	}
}
