﻿using Connectors.town.connectors.drivers;
using log4net;
using Puppeteer.EventSourcing;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Runtime.Serialization;
using System.Runtime.Serialization.Json;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using town.connectors.commons;

namespace town.connectors.drivers.zeus
{
    public abstract class DGSTenantDriver : TenantDriver
    {
		protected const string SUBSTITUTE_PLAYER_TOKEN = "On6rV1zt3wUS6lqffy7l37FF1tcyofRLIN5pGuwLC9xBgubq6NGbiUA52oAMwhaI";
		protected const string PLAYER_TOKEN_NAME = "tk_1";
		protected const int MAX_RETRIES = 3;
		private const float VERSION = 1.0F;
		protected const int FAKE_DOCUMENT_NUMBER = -1;
		protected const int TIME_IN_SECONDS_BEFORE_TOKEN_EXPIRES = 300;
		protected string SystemId { get; set; }
		protected string Password { get; set; }
		protected string ServicesUrl { get; set; }
		
		private static RestClient _getTokenClient;
		protected static DateTime TokenExpirationTime { get; set; }

		public DGSTenantDriver(Tenant_Actions tenantActions)
			: base(tenantActions, "Zeus", PaymentMethod.ThirdParty, "USD", VERSION)
		{

		}

		public override string Description => "Zeus driver";
		public override string Fabricator => "Ncubo";
		public override DateTime ReleaseDate => new DateTime(2019,01,01);

		internal static async Task RequestAppTokenAsync(string servicesUrl, string systemId, string password)
		{
			while (true)
			{
				DGSProcessorDriver.AppToken = await GetTokenAsync(servicesUrl, systemId, password);
				if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken)) await Task.Delay(3000);
				else
				{
					UpdateTokenExpirationTime();
					Loggers.GetIntance().AccountingServicesDGSGetToken.Debug($"{nameof(RequestAppTokenAsync)} new token: {DGSProcessorDriver.AppToken} expiration time: {TokenExpirationTime}");
					int waitingTimeInSeconds = Convert.ToInt32((TokenExpirationTime.AddSeconds(-TIME_IN_SECONDS_BEFORE_TOKEN_EXPIRES) - DateTime.UtcNow).TotalSeconds) * 1000;
					await Task.Delay(waitingTimeInSeconds);
				}
			}
		}

		internal static void RequestAppToken(string servicesUrl, string systemId, string password)
		{
			DGSProcessorDriver.AppToken = GetToken(servicesUrl, systemId, password);
			UpdateTokenExpirationTime();
			Loggers.GetIntance().AccountingServicesDGSGetToken.Debug($"{nameof(RequestAppTokenAsync)} new token: {DGSProcessorDriver.AppToken} expiration time: {TokenExpirationTime}");
		}

		private static void UpdateTokenExpirationTime()
		{
			try
			{
				JwtSecurityTokenHandler tokenHandler = new JwtSecurityTokenHandler();
				var handler = new JwtSecurityTokenHandler();
				var jwtSecurityToken = handler.ReadJwtToken(DGSProcessorDriver.AppToken);
				TokenExpirationTime = jwtSecurityToken.ValidTo;
			}
			catch (Exception e)
			{
				Loggers.GetIntance().AccountingServicesDGSGetToken.Error($"{nameof(UpdateTokenExpirationTime)} new token: {DGSProcessorDriver.AppToken} expiration time: {TokenExpirationTime}", e);
			}
		}
		private static async Task<string> GetTokenAsync(string servicesUrl, string appId, string appKey)
		{
			if (String.IsNullOrWhiteSpace(appId)) throw new ArgumentNullException(nameof(appId));
			if (String.IsNullOrWhiteSpace(appKey)) throw new ArgumentNullException(nameof(appKey));

			if (_getTokenClient == null)
			{
				_getTokenClient = new RestClient(servicesUrl);
			}

			var values = new AppLoginBody()
			{
				username = appId,
				password = appKey
			};
			const string url = "/Auth/Login";
			var jsonString = Commons.ToJson(values);
			string responseString = "";
			string valuesWithHiddenFields = $@"appId:{appId}, appKey:{appKey}";
			try
			{
				Loggers.GetIntance().AccountingServicesDGSGetToken.Debug($@"url:{url} data:{valuesWithHiddenFields}");

				RestRequest request = new RestRequest(url, Method.POST);
				request.AddHeader("Content-Type", "application/json");
				request.AddParameter("application/json", jsonString, ParameterType.RequestBody);
				var response = await _getTokenClient.ExecuteAsync(request);
				responseString = response.Content;

				Loggers.GetIntance().AccountingServicesDGSGetToken.Debug($@"response:{responseString}");
				var responseObj = Commons.FromJson<TokenResponse>(responseString);
				DGSProcessorDriver.AppToken = responseObj.token;
			}
			catch (Exception e)
			{
				Loggers.GetIntance().AccountingServicesDGSGetToken.Error($@"url:{url} data:{valuesWithHiddenFields} type:{e.GetType()} error:{e.Message}", e);
				return string.Empty;
			}

			if (String.IsNullOrWhiteSpace(responseString))
			{
				//NotifyWarn(nameof(GetTokenAsync), $"Url:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}", $"Response can not be empty");
				return string.Empty;
			}

			return DGSProcessorDriver.AppToken;
		}

		static string GetToken(string servicesUrl, string appId, string appKey)
		{
			if (String.IsNullOrWhiteSpace(appId)) throw new ArgumentNullException(nameof(appId));
			if (String.IsNullOrWhiteSpace(appKey)) throw new ArgumentNullException(nameof(appKey));

			if (_getTokenClient == null)
			{
				_getTokenClient = new RestClient(servicesUrl);
			}

			var values = new AppLoginBody()
			{
				username = appId,
				password = appKey
			};
			const string url = "/Auth/Login";
			var jsonString = Commons.ToJson(values);
			string responseString = "";
			string valuesWithHiddenFields = $@"appId:{appId}, appKey:{appKey}";
			try
			{
				Loggers.GetIntance().AccountingServicesDGSGetToken.Debug($@"url:{url} data:{valuesWithHiddenFields}");

				RestRequest request = new RestRequest(url, Method.POST);
				request.AddHeader("Content-Type", "application/json");
				request.AddParameter("application/json", jsonString, ParameterType.RequestBody);
				var response = _getTokenClient.Execute(request);
				responseString = response.Content;

				Loggers.GetIntance().AccountingServicesDGSGetToken.Debug($@"response:{responseString}");
				var responseObj = Commons.FromJson<TokenResponse>(responseString);
				DGSProcessorDriver.AppToken = responseObj.token;
			}
			catch (Exception e)
			{
				Loggers.GetIntance().AccountingServicesDGSGetToken.Error($@"url:{url} data:{valuesWithHiddenFields} type:{e.GetType()} error:{e.Message}", e);
				return string.Empty;
			}

			if (String.IsNullOrWhiteSpace(responseString))
			{
				return string.Empty;
			}

			return DGSProcessorDriver.AppToken;
		}
	}
	public abstract class DGSProcessorDriver : ProcessorDriver
	{
		protected const string SUBSTITUTE_PLAYER_TOKEN = "On6rV1zt3wUS6lqffy7l37FF1tcyofRLIN5pGuwLC9xBgubq6NGbiUA52oAMwhaI";
		protected const string PLAYER_TOKEN_NAME = "tk_1";
		private const float VERSION = 1.0F;
		protected const int FAKE_DOCUMENT_NUMBER = -1;
		protected string SystemId { get; set; }
		protected string Password { get; set; }
		protected string ServicesUrl { get; set; }
		
		internal static string AppToken { get; set; }

		public DGSProcessorDriver( TransactionType transactionType)
			  : base("Zeus", PaymentMethod.ThirdParty, "USD", transactionType, VERSION)
		{

		}

		public override string Description => "Zeus driver";
		public override string Fabricator => "Ncubo";
		public override DateTime ReleaseDate => new DateTime(2019, 01, 01);

	}


	public class AppLoginBody
	{
		public string username { get; set; }
		public string password { get; set; }
	}

	public class TokenResponse
	{
		public string token { get; set; }
	}

	public class PlayerBalanceBody
	{
		public string idPlayer { get; set; }
		public decimal realAvailableBalance { get; set; }
	}

	public class CreditTransactionBody
	{
		public int idTransaction { get; set; }
		public string idPlayer { get; set; }
		public string description { get; set; }
		public decimal amount { get; set; }
		public string reference { get; set; }
		public decimal fee { get; set; }
		public decimal bonus { get; set; }
	}

	public class DebitTransactionBody
	{
		public int idTransaction { get; set; }
		public string idPlayer { get; set; }
		public string description { get; set; }
		public decimal amount { get; set; }
		public string reference { get; set; }
		public decimal fee { get; set; }
		public decimal bonus { get; set; }
		public int toWin { get; set; }
		public List<string> date { get; set; }
	}

	public class CreditAndDebitTransactionBody
	{
		public int idTransaction { get; set; }
		public string idplayer { get; set; }
		public string description { get; set; }
		public decimal amount { get; set; }
		public string token { get; set; }
	}
	public class CreditAndDebitTransactionResponse
	{
		public int transactionId { get; set; }
	}
}
