﻿using GamesEngine.Bets;
using GamesEngine.Business;
using GamesEngine.Games.Tournaments;
using GamesEngine.Games.Tournaments.MarchMadness.Groups;
using GamesEngine.PurchaseOrders;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GamesEngine.Gameboards.MarchMadness
{
	[Puppet]
    internal class MarchMadnessEdition : Objeto
    {
        private readonly List<TupleRoundFee> generalPoolsRooms = new List<TupleRoundFee>();

        private readonly Dictionary<Pool, Room> roomsByPools = new Dictionary<Pool, Room>();
        private readonly Dictionary<Room, Pool> poolsByRooms = new Dictionary<Room, Pool>();
        private readonly Rooms rooms;

        private readonly List<BracketsOffering> bracketsCatalog = new List<BracketsOffering>();
        private Round[] roundsToSaleBrackets;
        private decimal[] fees;

        private bool initialized = false;
        private readonly Company company;
        private readonly int year;

        internal MarchMadnessEdition(Company company, int year)
        {
            if (company == null) throw new ArgumentNullException(nameof(company));
            if (year < MarchMadnessTournament.MIN_MARCH_MADNESS_SECURE_YEAR || year > MarchMadnessTournament.MAX_MARCH_MADNESS_SECURE_YEAR) throw new GameEngineException($"The year must be greater than {MarchMadnessTournament.MIN_MARCH_MADNESS_SECURE_YEAR} and less than {MarchMadnessTournament.MAX_MARCH_MADNESS_SECURE_YEAR}");

            this.year = year;
            this.company = company;
            this.rooms = new Rooms();
			MarchMadnessTournament t = company.Tournaments.GetMarchMadnessTournamentOf(year);
			roundsToSaleBrackets = new Round[] { t.GetRoundNumber(1), t.GetRoundNumber(2), t.GetRoundNumber(3) };
		}

        internal int Year
        {
            get
            {
                return year;
            }
        }

        internal void CreateGeneralPools(Round[] roundsToSale, decimal[] feesToCreate)
        {
            //if (initialized) throw new GameEngineException("General Pools were already initialized. They can not be initialized twice.");
            if (roundsToSale == null || roundsToSale.Length == 0) throw new ArgumentNullException(nameof(roundsToSale));
            if (feesToCreate == null || feesToCreate.Length == 0) throw new ArgumentNullException(nameof(feesToCreate));
            if (roundsToSale.Distinct().Count() != roundsToSale.Count()) throw new GameEngineException("There are duplicated rounds on definition of General Pools");
            if (feesToCreate.Distinct().Count() != feesToCreate.Count()) throw new GameEngineException("There are duplicated fees on definition of General Pools");

            foreach (Round r in roundsToSale)
            {
                foreach (decimal f in feesToCreate)
                {
					var newPool = company.Book.CreateNewPool($"${f} General Pool - {r.Name}", f);
					var newRoom = (InternalRoom)rooms.NewInternalRoom($"${f} General Pool - {r.Name}");
					newRoom.Round = r;
					TupleRoundFee rxf = new TupleRoundFee(r, f, newPool, newRoom);
					generalPoolsRooms.Add(rxf);
					LinkPoolAndRoom(newPool, newRoom);
                }
            }

			List<decimal> newFees = this.fees == null ? new List<decimal>() : this.fees.ToList();

			foreach (Round round in roundsToSale)
			{
				if (roundsToSaleBrackets.Contains(round))
				{
					foreach (decimal f in feesToCreate)
					{
						if (!newFees.Contains(f)) newFees.Add(f);
					}
				}
				else
				{
					Array.Resize(ref roundsToSaleBrackets, roundsToSaleBrackets.Length + 1);
					roundsToSaleBrackets[roundsToSaleBrackets.GetUpperBound(0)] = round;
				}
			}
			this.fees = newFees.OrderBy(n => n).ToArray();
			this.initialized = true;
		}

        internal InternalRoom Room(Round round, decimal fee)
        {
            if (!initialized) throw new GameEngineException("General Pools have not been initialized yet. Use CreateGeneralPools method.");
            if (round == null) throw new ArgumentNullException(nameof(round));
            InternalRoom result = generalPoolsRooms.Single(x=> x.Round == round && x.Fee == fee).Room;
            if (result == null) throw new GameEngineException($"{round.Name} or General Pool of ${fee} is unknown.");
            return result;
        }

        internal void UpdateLeaderboards()
        {
            foreach(Room r in rooms.GetAll())
            {
                r.ReorderLeaderBoard();
            }
        }

        internal Pool GeneralPool(Round round, decimal fee)
        {
            if (!initialized) throw new GameEngineException("General Pools have not been initialized yet. Use CreateGeneralPools method.");
            if (round == null) throw new ArgumentNullException(nameof(round));
            Pool result = generalPoolsRooms.Single(x => x.Round == round && x.Fee == fee).Pool;
            if (result == null) throw new GameEngineException($"{round.Name} and General Pool of ${fee} is unknown.");
            return result;
        }

		private Product AddNewProduct(decimal price, string description)
		{
            int nextId = this.company.Products().GetMaxId() + 1;
            Product product = company.GetOrCreateProductById(nextId);
			product.Description = description;
			product.Price = price;
			return product;
		}

		internal IEnumerable<Pool> GeneralPoolsBy(decimal fee)
        {
            if (!fees.Contains(fee)) throw new GameEngineException($"There are not General Pools with {nameof(fee)} {fee}");
            var result = generalPoolsRooms.Where(x => x.Fee==fee).Select(x => x.Pool);
            return result;
        }

        internal IEnumerable<Pool> GeneralPoolsBy(Round round)
        {
            if (round == null) throw new ArgumentNullException(nameof(round));
            var result = generalPoolsRooms.Where(x => x.Round == round).Select(x => x.Pool);
            return result;
        }

        internal Pool GeneralPoolOf(Bracket bracket)
        {
            if (bracket == null) throw new ArgumentNullException(nameof(bracket));
            var generalRooms = GeneralPools().Select(pool => Room(pool)).ToList();
            var generalRoomOfBracket = generalRooms.Single(room => room.ListAllGameBoardsInThisRoom().Contains(bracket));
            var result = Pool(generalRoomOfBracket);
            return result;
        }

        internal IEnumerable<Pool> PoolsOf(Bracket bracket)
        {
            if (bracket == null) throw new ArgumentNullException(nameof(bracket));
            var poolsOfBracket = roomsByPools.Where(pair => pair.Value.ListAllGameBoardsInThisRoom().Contains(bracket)).
                Select(pair => pair.Key).ToList();
            return poolsOfBracket;
        }

        internal Round RoundOf(Room room)
        {
            if (room == null) throw new ArgumentNullException(nameof(room));
            Round result;
            if (generalPoolsRooms.Any(x => x.Room == room))
            {
                result = generalPoolsRooms.Single(x => x.Room == room).Round;
            }
            else
            {
                Gameboard gameboard = room.ListAllGameBoardsInThisRoom().First();
                MarchMadnessBracket bracket = (MarchMadnessBracket)gameboard;
                result = bracket.InitialRoundOfPredictions();
            }
            return result;
        }

        internal IEnumerable<BracketsOffering> ProductsToCreateBracket()
        {
            return bracketsCatalog.Where(x=> x.Availability == Availability.MultiRound && x.Purpose == Purpose.Creation);
        }

		internal IEnumerable<BracketsOffering> ProductsToReplicateBracket()
        {
            return bracketsCatalog.Where(x=> x.Availability == Availability.MultiRound && x.Purpose == Purpose.Replication);
        }

		internal BracketsOffering SuperLeagueCreationProduct()
		{
			var product = bracketsCatalog.Single(x => x.Availability == Availability.SingleRound && x.Purpose == Purpose.Creation && x.Round.Number == 1);
			return product;
		}

		internal bool ExistSuperLeagueBracket()
		{
			var result = bracketsCatalog.Any(x => x.Availability == Availability.SingleRound && x.Purpose == Purpose.Creation && x.Round.Number == 1);
			return result;
		}

		internal BracketsOffering SuperLeagueReplicateProduct()
		{
			var product = bracketsCatalog.Single(x => x.Availability == Availability.SingleRound && x.Purpose == Purpose.Replication && x.Round.Number == 1);
			return product;
		}

        internal bool ExistFinalFourBracket()
        {
            var result = bracketsCatalog.Any(x => x.Availability == Availability.SingleRound && x.Purpose == Purpose.Creation && x.Round.Number == 4);
            return result;
        }

        internal BracketsOffering FinalFourCreationProduct()
		{
			var product = bracketsCatalog.Single(x => x.Availability == Availability.SingleRound && x.Purpose == Purpose.Creation && x.Round.Number == 4);
			return product;
		}

		internal BracketsOffering FinalFourReplicateProduct()
		{
			var product = bracketsCatalog.Single(x => x.Availability == Availability.SingleRound && x.Purpose == Purpose.Replication && x.Round.Number == 4);
			return product;
		}

		internal void MakeSureBuyABracket(Product productToBuy)
		{
			if (productToBuy == null) throw new ArgumentException(nameof(productToBuy));
			var offering = bracketsCatalog.Single(x=> x.Product == productToBuy);
			var currentRound = Tournament.CurrentRound();
			if (currentRound != offering.Round) throw new GameEngineException($"Predictions are only valid on Round of {offering.Round.Number}.");
			if(offering.Purpose != Purpose.Creation) throw new GameEngineException($"This product code is not for bracket creation.");
		}

		internal IEnumerable<Product> ProductsToSellCreateBracketForRound(Round round)
        {
            List<Product> result = new List<Product>();
			var bracketsToSellForThisRound = bracketsCatalog.Where(x => x.Round == round && (x.Availability == Availability.MultiRound || x.Availability == Availability.SingleRound) && x.Purpose == Purpose.Creation);

			foreach (BracketsOffering toSell in bracketsToSellForThisRound)
            {
                result.Add(toSell.Product);
            }
            return result;
        }

        internal IEnumerable<Product> ProductsToSellReplicateBracketForRound(Round round)
        {
            List<Product> result = new List<Product>();
            foreach (BracketsOffering toSell in bracketsCatalog.Where(x => x.Round == round && (x.Availability == Availability.MultiRound || x.Availability == Availability.SingleRound) && x.Purpose == Purpose.Replication))
            {
                result.Add(toSell.Product);
            }
            return result;
        }

		internal void NewBracketToSell(decimal price)
		{
			if (price < 0) throw new GameEngineException($"Price can not be {price}. Prices must be equals or greater than zero.");
			if (bracketsCatalog.Count(x => x.Price == price && x.Purpose == Purpose.Creation) > 0) throw new GameEngineException($"Catalog already have a Product to sell Brackets of price: ${price}. Prices must be unique.");

			foreach(Round round in roundsToSaleBrackets)
			{
				ProductsToSellBracket(price, round);
			}
			if(this.year != 2019) this.CreateGeneralPools(roundsToSaleBrackets, new decimal[] { price });
		}

        private void ProductsToSellBracket(decimal price, Round round)
		{
			if (price < 0) throw new GameEngineException($"Price can not be {price}. Prices must be equals or greater than zero.");
			var countGames = round.CountGames() * 2;
			var product1 = AddNewProduct(price, $"Brackets {year} Bracket[Round of {countGames}] of ${price}");
			var product2 = AddNewProduct(price, $"Brackets {year} Replicate Bracket[Round of {countGames}] of ${price}");
			bracketsCatalog.Add(new BracketsOffering(round, product1, Availability.MultiRound, Purpose.Creation));
			bracketsCatalog.Add(new BracketsOffering(round, product2, Availability.MultiRound, Purpose.Replication));
        }

		internal void NewSpecialBracketToSell(decimal price, Round round, string specialBracketName)
		{
			if (price < 0) throw new GameEngineException($"Price can not be {price}. Prices must be equals or greater than zero.");
			if (bracketsCatalog.Count(x => x.Price == price && x.Round == round && x.Purpose == Purpose.Creation) > 0) throw new GameEngineException($"Catalog already have a Product to sell Brackets of price: ${price}. Prices must be unique.");
			var product1 = AddNewProduct(price, $"Brackets {year} Bracket {specialBracketName}[Round of {round.Alias}] of ${price}");
			var product2 = AddNewProduct(price, $"Brackets {year} Replicate Bracket {specialBracketName}[Round of {round.Alias}] of ${price}");
			bracketsCatalog.Add(new BracketsOffering(round, product1, Availability.SingleRound, Purpose.Creation));
			bracketsCatalog.Add(new BracketsOffering(round, product2, Availability.SingleRound, Purpose.Replication));
			this.CreateGeneralPools(new Round[] { round }, new decimal[] { price });
        }

		internal void StopSellingBracket(decimal price)
        {
            int countOfRemovedItems = bracketsCatalog.RemoveAll(x => x.Price == price && x.Availability == Availability.MultiRound && x.Purpose == Purpose.Creation);
			if (countOfRemovedItems == 0) throw new GameEngineException($"Catalog does not have a Product to sell Brackets of price: ${price}.");
		}

		internal IEnumerable<BracketsOffering> ProductsToSellGroups()
        {
			var result = bracketsCatalog.Where(x => x.Availability == Availability.MultiRound && x.Purpose == Purpose.Grouping);
			return result;
        }

		internal BracketsOffering ProductToSellSuperLeagueGroups()
		{
			var result = bracketsCatalog.First(x => x.Availability == Availability.SingleRound && x.Purpose == Purpose.Grouping);
			return result;
		}

		internal IEnumerable<Product> ProductsToSellCreateGroupForRound(Round round)
        {
            if (Tournament.IsEnded() || Tournament.IsReadyToPay()) throw new GameEngineException($"The tournament {year} is over.");

            List<Product> result = bracketsCatalog.Where(x => x.Round == round && x.Availability == Availability.MultiRound && x.Purpose == Purpose.Grouping).Select(x => x.Product).ToList();
			return result;
        }

        internal IEnumerable<Product> ProductsToSellJoinGroupForRound(Round round)
        {
            if (Tournament.IsEnded() || Tournament.IsReadyToPay()) throw new GameEngineException($"The tournament {year} is over.");

            List<Product> result = bracketsCatalog.Where(x => x.Round == round && x.Availability == Availability.MultiRound && x.Purpose == Purpose.Joining).Select(x => x.Product).ToList();
			return result;
        }

        internal bool HasProductsToSell(Round round)
        {
			var result = bracketsCatalog.Any(x => x.Round == round);
			return result;
        }

		internal bool HasProductsToSell(Round round, decimal fee)
		{
            var result = bracketsCatalog.Any(x => x.Round == round && x.Price == fee);
			return result;
		}

		internal Product CheapestProductsToSellJoinGroup(Round round)
        {
            Product result = null;
            foreach (Product toSell in ProductsToSellJoinGroupForRound(round))
            {
                if (result == null || result.Price > toSell.Price) result = toSell;
            }
            return result;
        }


        internal Product CheapestProductsToSellCreateGroup(Round round)
        {
            Product result = null;
            foreach (Product toSell in ProductsToSellCreateGroupForRound(round))
            {
                if (result == null || result.Price > toSell.Price) result = toSell;
            }
            return result;
        }

        internal Product CheapestProductsToSellReplicateBracket(Round round)
        {
            Product result = null;
            foreach (Product toSell in ProductsToSellReplicateBracketForRound(round))
            {
                if (result == null || result.Price > toSell.Price) result = toSell;
            }
            return result;
        }

        internal Product CheapestProductsToSellCreateBracket(Round round)
        {
            Product result = null;
            foreach (Product toSell in ProductsToSellCreateBracketForRound(round))
            {
                if (result == null || result.Price > toSell.Price) result = toSell;
            }
            return result;
        }

		internal void NewGroupToSell(decimal price)
		{
			if (price < 0) throw new GameEngineException($"Price can not be {price}. Prices must be equals or greater than zero.");
			if (bracketsCatalog.Count(x => x.Price == price && x.Availability == Availability.MultiRound && x.Purpose == Purpose.Grouping) > 0) throw new GameEngineException($"Catalog already have a Product to sell Groups of price: ${price}. Prices must be unique.");

			foreach (Round round in roundsToSaleBrackets)
			{
				ProductsToSellGroups(price, round);
			}
		}

		private void ProductsToSellGroups(decimal price, Round round)
		{
			if (price < 0) throw new GameEngineException($"Price can not be {price}. Prices must be equals or greater than zero.");
			var countGames = round.CountGames() * 2;
			var product1 = AddNewProduct(price, $"Brackets {year} Create Group of ${price} on Round of {countGames}");
			var product2 = AddNewProduct(price, $"Brackets {year} Join bracket to a Group of ${price} on Round of {countGames}");
			bracketsCatalog.Add(new BracketsOffering(round, product1, Availability.MultiRound, Purpose.Grouping));
			bracketsCatalog.Add(new BracketsOffering(round, product2, Availability.MultiRound, Purpose.Joining));
        }

		internal void NewSpecialGroupToSell(decimal price, Round round)
		{
			if (price < 0) throw new GameEngineException($"Price can not be {price}. Prices must be equals or greater than zero.");
			if (bracketsCatalog.Count(x => x.Price == price && x.Availability == Availability.SingleRound && x.Purpose == Purpose.Grouping) > 0) throw new GameEngineException($"Catalog already have a Product to sell Brackets of price: ${price}. Prices must be unique.");
			var product1= AddNewProduct(price, $"Brackets {year} Create Super League Group of ${price} on Round of {64}");
			var product2 = AddNewProduct(price, $"Brackets {year} Join bracket to a Super League Group of ${price} on Round of {64}");
			bracketsCatalog.Add(new BracketsOffering(round, product1, Availability.SingleRound, Purpose.Grouping));
			bracketsCatalog.Add(new BracketsOffering(round, product2, Availability.SingleRound, Purpose.Joining));
        }

		internal void StopSellingGroup(decimal price)
        {
			int countOfRemovedItems = bracketsCatalog.RemoveAll(x => x.Price == price && x.Availability == Availability.MultiRound && x.Purpose == Purpose.Grouping);
			if (countOfRemovedItems == 0) throw new GameEngineException($"Catalog does not have a Product to sell Groups of price: ${price}.");
		}

		internal IEnumerable<decimal> Fees()
        {
            return new List<decimal>(fees);
        }

        internal IEnumerable<int> RoomSizes()
        {
            return rooms.RoomSizes();
        }

        internal void AllowRoomSize(int size)
        {
            rooms.AllowRoomSize(size);
        }

        internal void DenyRoomSize(int size)
        {
            rooms.DenyRoomSize(size);
        }

        internal IEnumerable<Round> RoundsToSaleBrackets()
        {
            return new List<Round>(roundsToSaleBrackets);
        }

        internal Round RoundToSaleBrackets(int index)
        {
            return roundsToSaleBrackets[index];
        }

        internal IEnumerable<Pool> GeneralPools()
        {
            return generalPoolsRooms.Select(x => x.Pool);
        }

		internal IEnumerable<Pool> GeneralPoolsForSuperLeague()
		{
			List<Pool> result = new List<Pool>();
			var generalRooms = this.generalPoolsRooms.Where(x => IsASuperLeaguePool(x.Pool)).Select(item => item.Pool);
			result.AddRange(generalRooms);
			return result;
		}

		internal IEnumerable<Pool> GeneralPoolsForFinalFour()
		{
			List<Pool> result = new List<Pool>();
			var generalRooms = this.generalPoolsRooms.Where(x => IsAFinalFourPool(x.Pool)).Select(item => item.Pool);
			result.AddRange(generalRooms);
			return result;
		}

		internal Rooms Rooms
        {
            get
            {
                return rooms;
            }
        }

        internal Rooms NonGeneralRoomsOf(Round round)
        {
            if (round == null) throw new ArgumentNullException(nameof(round));
            var generalRooms = this.generalPoolsRooms.Where(x=> x.Round == round).Select(item => item.Room);
            var roomsOfRound = rooms.GetAll().Where(room => room.Round == round && !generalRooms.Contains(room));
            Rooms result = new Rooms();
            result.AddAll(roomsOfRound);
            return result;
        }

        internal Rooms NonGeneralRooms()
        {
            var generalRooms = this.generalPoolsRooms.Select(item => item.Room);
            var roomsOfRound = rooms.GetAll().Where(x => ! generalRooms.Contains(x));
            Rooms result = new Rooms();
            result.AddAll(roomsOfRound);
            return result;
        }

        internal Rooms AvailableRoomsOf(Round round)
        {
            Rooms result = new Rooms();
			var nonGeneralRooms = NonGeneralRoomsOf(round);

			result.AddAll(nonGeneralRooms.PublicRoomsWithSpaces(GeneralsRooms()).GetAll());
            result.AddAll(nonGeneralRooms.PrivateRoomsWithSpaces().GetAll());
            return result;
        }

        private void LinkPoolAndRoom(Pool pool, Room room)
        {
            poolsByRooms.Add(room, pool);
            roomsByPools.Add(pool, room);
        }

        private void UnlinkPoolAndRoom(Pool pool, Room room)
        {
            poolsByRooms.Remove(room);
            roomsByPools.Remove(pool);
        }

        private MarchMadnessTournament Tournament
        {
            get
            {
                MarchMadnessTournament tournament = this.company.Tournaments.GetMarchMadnessTournamentOf(year);
                return tournament;
            }
        }

        [Obsolete]
        internal Room NewInternalRoom(string description, Pool pool)
        {
            Room room = rooms.NewInternalRoom(description);
            if (Tournament.IsStarted())
            {
                room.Round = Tournament.CurrentRound();
            }
            else if (Tournament.IsRegistrationClosed() || Tournament.IsRegistrationOpen() || Tournament.IsReadyForRegistration())
            {
                room.Round = Tournament.Rounds().FirstRound();
            }
            else
            {
                throw new GameEngineException("Internal groups cannot be created at this phase of the tournament");
            }
            LinkPoolAndRoom(pool, room);
            return room;
        }

        internal Room NewInternalRoom(string description, Pool pool, Round round)
        {
            if (pool == null) throw new ArgumentNullException(nameof(pool));
            if (round == null) throw new ArgumentNullException(nameof(round));
            if (string.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));

            Room room = rooms.NewInternalRoom(description);
            if (Tournament.IsStarted() || Tournament.IsRegistrationClosed() || Tournament.IsRegistrationOpen() || Tournament.IsReadyForRegistration())
            {
                room.Round = round;
            }
            else
            {
                throw new GameEngineException("Internal groups cannot be created at this phase of the tournament");
            }

            LinkPoolAndRoom(pool, room);
            return room;
        }

        internal Room NewPublicRoom(Bets.Player host, string description, Pool pool)
        {
            if (host == null) throw new ArgumentNullException(nameof(host));
            if (pool == null) throw new ArgumentNullException(nameof(pool));
            if (string.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));
            if (rooms.HasRoomNamed(host, description)) throw new GameEngineException($"{nameof(Room)} named '{description}' already exist for player '{host.Id}'");

            Room room = rooms.NewPublicRoom(host, description);
            room.Round = Tournament.CurrentRound();
            LinkPoolAndRoom(pool, room);
            return room;
        }

        internal Room NewPrivateRoom(Bets.Player host, string description, Pool pool)
        {
            if (host == null) throw new ArgumentNullException(nameof(host));
            if (pool == null) throw new ArgumentNullException(nameof(pool));
            if (string.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));
            if (HostHasRoomNamed(host, description)) throw new GameEngineException($"{nameof(Room)} named '{description}' already exist for player '{host.Id}'");

            Room room = rooms.NewPrivateRoom(host, description);
            room.Round = Tournament.CurrentRound();
            LinkPoolAndRoom(pool, room);
            return room;
        }

        internal void CheckForRoomNameAvailability(Bets.Player host, string description)
        {
            if (host == null) throw new ArgumentNullException(nameof(host));
            if (string.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));
            if (HostHasRoomNamed(host, description)) throw new GameEngineException($"{nameof(Room)} named '{description}' already exist for player '{host.Id}'");
        }

        internal bool HostHasRoomNamed(Bets.Player host, string name)
        {
            if (host == null) throw new ArgumentNullException(nameof(host));
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

            var result = rooms.HasRoomNamed(host, name);
            return result;
        }

        internal void RemoveRoom(Pool pool, Room room)
        {
            rooms.Remove(room);
            UnlinkPoolAndRoom(pool, room);
        }

        internal Pool Pool(Room room)
        {
            if (room == null) throw new ArgumentNullException(nameof(room));
            if (poolsByRooms.Count(pair => pair.Key == room) == 0) throw new GameEngineException($"Room '{room.Id}' is unKnown");
            var result = poolsByRooms.Single(pair => pair.Key == room);
            return result.Value;
        }

        internal Room Room(Reward pool)
        {
            if (pool == null) throw new ArgumentNullException(nameof(pool));
            if (roomsByPools.Count(pair => pair.Key.Equals(pool)) == 0) throw new GameEngineException($"We are so sorry, but the round has started and the pool named \"{pool.Name}\" did not go through. Amount refunded.");
            var result = roomsByPools.Single(pair => pair.Key == pool);
            return result.Value;
        }

        internal Rooms GeneralsRooms()
        {
            Rooms result = new Rooms();
            var generalRooms = this.generalPoolsRooms.Select(item => item.Room);
            result.AddAll(generalRooms);
            return result;
        }

        internal Rooms GeneralRoomsBy(int roundNumber)
        {
            if ((roundNumber <= 0) || Tournament.CountRounds() < roundNumber) throw new GameEngineException("Round number is not valid");
            Rooms result = new Rooms();
            var generalRooms = this.generalPoolsRooms.Where(x => x.Round.Number == roundNumber && !IsASuperLeaguePool(x.Pool)).Select(item => item.Room);
            result.AddAll(generalRooms);
            return result;
        }

        internal Room GeneralRoomBy(int roundNumber, decimal fee)
        {
            if ((roundNumber <= 0) || Tournament.CountRounds() < roundNumber) throw new GameEngineException("Round number is not valid");
            if (!fees.Contains(fee)) throw new GameEngineException($"There are not General Pools with {nameof(fee)} {fee}");

            foreach (var roundFee in generalPoolsRooms)
            {
                if (roundFee.Round.Number == roundNumber && roundFee.Fee == fee && ! IsASuperLeaguePool(roundFee.Pool))
                {
                    return roundFee.Room;
                }
            }

            throw new GameEngineException($"There is no {nameof(Room)} with {nameof(roundNumber)} '{roundNumber}' and {nameof(fee)} '{fee}'");
        }

        internal Rooms GeneralRoomsForSuperLeague()
        {
            Rooms result = new Rooms();
            var generalRooms = this.generalPoolsRooms.Where(x => IsASuperLeaguePool(x.Pool)).Select(item => item.Room);
            result.AddAll(generalRooms);
            return result;
        }

        internal Rooms GeneralRoomsForFinalFour()
        {
            Rooms result = new Rooms();
            var generalRooms = this.generalPoolsRooms.Where(x => IsAFinalFourPool(x.Pool)).Select(item => item.Room);
            result.AddAll(generalRooms);
            return result;
        }

		internal bool IsASuperLeaguePool(Pool pool)
		{
			if (pool == null) throw new ArgumentNullException(nameof(pool));
			Room room = this.Room(pool);
			var round = room.Round;
			bool isRound64 = round.Number == 1;
			if (!isRound64) return false;

			if (!ExistSuperLeagueBracket()) return false;

			var fee = SuperLeagueCreationProduct().Product.Price;
			if (fee != pool.Fee) return false;

			var allBracketsInThisPool = room.ListAllGameBoardsInThisRoom().Cast<MarchMadnessBracket>();
			var generalPools = allBracketsInThisPool.Select(x => this.GeneralPoolOf(x)).Distinct();
			if(generalPools.Count() != 1) return false;

			if (generalPools.Any(x => x.Fee != fee)) return false;

			return true;
		}

        internal bool IsAFinalFourPool(Pool pool)
        {
            if (pool == null) throw new ArgumentNullException(nameof(pool));
            Room room = this.Room(pool);
            var round = room.Round;
            bool isRound8 = round.Number == 4;
            if (!isRound8) return false;

            if (!ExistFinalFourBracket()) return false;

            var fee = FinalFourCreationProduct().Product.Price;
            if (fee != pool.Fee) return false;

            var allBracketsInThisPool = room.ListAllGameBoardsInThisRoom().Cast<MarchMadnessBracket>();
            var generalPools = allBracketsInThisPool.Select(x => this.GeneralPoolOf(x)).Distinct();
            if (generalPools.Count() != 1) return false;

            if (generalPools.Any(x => x.Fee != fee)) return false;

            return true;
        }

        internal bool IsGeneralPool(Pool pool)
        {
            var result = generalPoolsRooms.Any(x => x.Pool == pool);
            return result;
        }

        internal IEnumerable<Pool> PoolsWithOnlyOneMemberIn(Round round)
        {
            var result = NonGeneralRoomsOf(round).GetAll().
                Where(room => room.HasOnlyOneParticipant()).
                Select(room => Pool(room));
            return result;
        }

        internal IEnumerable<Room> RoomsOfARoundOwnedByPlayer(Bets.Player player, Round round)
        {
            var result = NonGeneralRoomsOf(round).GetAll().
                Where(room => room.HasHost() && room.IsOwner(player));
            return result;
        }

        internal int CountRooms
        {
            get
            {
                return rooms.Count;
            }
        }

        internal int CountGeneralRoomsBy(int roundNumber)
        {
            if ((roundNumber <= 0) || Tournament.CountRounds() < roundNumber) throw new GameEngineException("Round number is not valid");
            return GeneralRoomsBy(roundNumber).Count;
        }

        internal int CountInternalRoomsBy(int roundNumber)
        {
            if ((roundNumber <= 0) || Tournament.CountRounds() < roundNumber) throw new GameEngineException("Round number is not valid");
            var count = rooms.InternalRoomsWithoutGeneralsByRound(GeneralsRooms(), roundNumber).Count;
            return count;
        }

        internal int CountPublicRoomsBy(int roundNumber)
        {
            if ((roundNumber <= 0) || Tournament.CountRounds() < roundNumber) throw new GameEngineException("Round number is not valid");
            var count = rooms.GetPublicRoomsBy(roundNumber).Count;
            return count;
        }

        internal int CountPrivateRoomsBy(int roundNumber)
        {
            if ((roundNumber <= 0) || Tournament.CountRounds() < roundNumber) throw new GameEngineException("Round number is not valid");
            var count = rooms.GetPrivateRoomsBy(roundNumber).Count;
            return count;
        }

        internal int CountMembersInGeneralRoomsForSuperLeague()
        {
            var count = GeneralRoomsForSuperLeague().CountOfGameboardOwners();
            return count;
        }

        internal int CountMembersInGeneralRoomsForFinalFour()
        {
            var count = GeneralRoomsForFinalFour().CountOfGameboardOwners();
            return count;
        }

        internal int CountMembersInGeneralRoomsFor(int roundNumber)
        {
            if ((roundNumber <= 0) || Tournament.CountRounds() < roundNumber) throw new GameEngineException("Round number is not valid");
            var count = GeneralRoomsBy(roundNumber).CountOfGameboardOwners();
            return count;
        }

        internal int CountMembersInGeneralRoomsFor(int roundNumber, decimal fee)
        {
            if ((roundNumber <= 0) || Tournament.CountRounds() < roundNumber) throw new GameEngineException("Round number is not valid");
            var count = GeneralRoomBy(roundNumber, fee).CountOfGameboardOwners();
            return count;
        }

        internal int CountMembersInInternalRoomsFor(int roundNumber)
        {
            if ((roundNumber <= 0) || Tournament.CountRounds() < roundNumber) throw new GameEngineException("Round number is not valid");
            var count = rooms.InternalRoomsWithoutGeneralsByRound(GeneralsRooms(), roundNumber).CountOfGameboardOwners();
            return count;
        }

        internal int CountMembersInPublicRoomsFor(int roundNumber)
        {
            if ((roundNumber <= 0) || Tournament.CountRounds() < roundNumber) throw new GameEngineException("Round number is not valid");
            var count = rooms.GetPublicRoomsBy(roundNumber).CountOfGameboardOwners();
            return count;
        }

        internal int CountMembersInPrivateRoomsFor(int roundNumber)
        {
            if ((roundNumber <= 0) || Tournament.CountRounds() < roundNumber) throw new GameEngineException("Round number is not valid");
            var count = rooms.GetPrivateRoomsBy(roundNumber).CountOfGameboardOwners();
            return count;
        }

        internal int CountMembersInRoomsWithoutGeneralsFor(int roundNumber)
        {
            if ((roundNumber <= 0) || Tournament.CountRounds() < roundNumber) throw new GameEngineException("Round number is not valid");
            var count = rooms.RoomsWithoutGeneralsByRound(GeneralsRooms(), roundNumber).CountOfGameboardOwners();
            return count;
        }

        internal decimal ContributionInGeneralPoolsWith(decimal fee)
        {
            if (!fees.Contains(fee)) throw new GameEngineException($"There are not General Pools with {nameof(fee)} {fee}");

            var pools = GeneralPoolsBy(fee);
            var brackets = company.GameboardsOf(pools);
            var result = brackets.Count() * fee;
            return result;
        }

        internal decimal ContributionInGeneralPoolsFor3FirstRoundsWithoutSpecialPools(int roundNumber)
        {
            if ((roundNumber <= 0) || roundNumber > 3) throw new GameEngineException("Round number is not valid for this sum");

            var round = Tournament.GetRoundNumber(roundNumber);
            var pools = GeneralPoolsBy(round);
            if (roundNumber == 1)
            {
                pools = pools.Where(pool => ! IsASuperLeaguePool(pool));
            }

            decimal contribution = 0;
            foreach (var pool in pools)
            {
                var brackets = company.GameboardsOf(pool);
                contribution += brackets.Count() * pool.Fee;
            }
            
            return contribution;
        }

        internal decimal ContributionInGeneralPoolsFor(int roundNumber, decimal fee)
        {
            if ((roundNumber <= 0) || roundNumber > 3) throw new GameEngineException("Round number is not valid for this sum");
            if (!fees.Contains(fee)) throw new GameEngineException($"There are not General Pools with {nameof(fee)} {fee}");

            var round = Tournament.GetRoundNumber(roundNumber);
            var pool = GeneralPool(round,fee);
            var brackets = company.GameboardsOf(pool);
            var result = brackets.Count() * fee;
            return result;
        }

        internal decimal ContributionInSuperLeague()
        {
            var fee = 50;
            var round = Tournament.GetRoundNumber(1);
            var pool = GeneralPool(round, fee);
            var brackets = company.GameboardsOf(pool);
            var result = brackets.Count() * fee;
            return result;
        }

        internal decimal ContributionInFinalFour()
        {
            var fee = 25;
            var round = Tournament.GetRoundNumber(4);
            var pool = GeneralPool(round, fee);
            var brackets = company.GameboardsOf(pool);
            var result = brackets.Count() * fee;
            return result;
        }

        internal int NextRoomConsecutive()
        {
            return rooms.NextSequence();
        }

        internal void RemoveInternalRoom(InternalRoom room)
        {
            if (room == null) throw new ArgumentNullException(nameof(room));
            if (room.HasHost()) throw new GameEngineException($"This room is not {nameof(InternalRoom)}.");

            var pool = Pool(room);
            if (room.IsEmpty())
            {
                RemoveRoom(pool, room);
            }
            else
            {
                throw new GameEngineException($"The room {room.Name} is not empty.");
            }
        }

        internal bool IsRemovableInternalRoom(InternalRoom room)
        {
            if (room == null) throw new ArgumentNullException(nameof(room));
            if (room.HasHost()) throw new GameEngineException($"This room is not {nameof(InternalRoom)}.");

            var result = room.IsEmpty();
            return result;
        }
    }

    class TupleRoundFee
    {
        private readonly Round round;
        private readonly decimal fee;
        private readonly Pool pool;
        private readonly InternalRoom room;

        internal TupleRoundFee(Round round, decimal fee, Pool pool, InternalRoom room)
        {
            this.round = round;
            this.fee = fee;
			this.pool = pool;
			this.room = room;
        }

        internal Round Round
        {
            get
            {
                return round;
            }
        }

		internal Pool Pool
		{
			get
			{
				return pool;
			}
		}

		internal InternalRoom Room
		{
			get
			{
				return room;
			}
		}

		internal decimal Fee
		{
			get
			{
				return fee;
			}
		}
    }

	enum Availability {MultiRound, SingleRound};
	enum Purpose {Creation, Replication, Grouping, Joining};

    [Puppet]
    internal class BracketsOffering : Objeto
    {
        private readonly Round round;
		private readonly Product product;
		private readonly Availability availability;
		private readonly Purpose purpose;

		internal BracketsOffering(Round round, Product product, Availability availability, Purpose purpose)
		{
			this.round = round;
			this.product = product;
			this.availability = availability;
			this.purpose = purpose;
		}

		internal Round Round
		{
			get
			{
				return this.round;
			}
		}

		internal Product Product
		{
			get
			{
				return this.product;
			}
		}

		internal Availability Availability
		{
			get
			{
				return this.availability;
			}
		}

		internal Purpose Purpose
		{
			get
			{
				return this.purpose;
			}
		}

        internal decimal Price { get { return this.product.Price; } }
    }
}
