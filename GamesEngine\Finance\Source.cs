﻿using GamesEngine.Business;
using GamesEngine.PurchaseOrders;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;

namespace GamesEngine.Finance
{
	[Puppet]
	internal class Source : Objeto
	{
		internal int Number { get;}
		internal Coin Currency { get;}
		internal AtAddress Owner { get;}
		internal string CurrencyAsText
		{
			get
			{
				return this.Currency.ToString();
			}
		}
		internal string CurrencyTypeAsText
		{
			get
			{
				return Coinage.Type(Currency.Iso4217Code);
			}
		}

		internal Source(bool itIsThePresent, DateTime creationDate, int number, Coin coin, AtAddress owner)
		{
			if (owner == null) throw new ArgumentException(nameof(owner));

			Number = number;
			Owner = owner;
			Currency = coin;
		}

		internal void Lock(bool itIsThePresent, DateTime date, Currency amount, string documentNumber, Store store, string concept, string reference)
		{
			Lock(itIsThePresent, date, amount, documentNumber, store, concept, reference, WholePaymentProcessor.NoPaymentProcessor);
		}
		internal void Lock(bool itIsThePresent, DateTime date, Currency amount, string documentNumber, Store store, string concept, string reference, int processorId)
		{
			if (amount == null) throw new ArgumentException(nameof(amount));

			Owner.Lock(itIsThePresent, date, amount, this, Users.LADYBET, documentNumber, store, concept, reference, processorId);
		}

		internal void Lock(bool itIsThePresent, DateTime date, Currency amount, string who, string documentNumber, Store store, string concept, string reference)
		{
			Lock(itIsThePresent, date, amount, who, documentNumber, store, concept, reference, WholePaymentProcessor.NoPaymentProcessor);
		}

		internal void Lock(bool itIsThePresent, DateTime date, Currency amount, string who, string documentNumber, Store store, string concept, string reference, int processorId)
		{
			if (amount == null) throw new ArgumentException(nameof(amount));

			Owner.Lock(itIsThePresent, date, amount, this, who, documentNumber, store, concept, reference, processorId);
		}

		internal void UnLock(bool itIsThePresent, DateTime date, Currency amount, string who, string documentNumber, Store store, string concept, string reference)
		{
			UnLock(itIsThePresent, date, amount, who, documentNumber, store, concept, reference, WholePaymentProcessor.NoPaymentProcessor);
		}

		internal void UnLock(bool itIsThePresent, DateTime date, Currency amount, string who, string documentNumber, Store store, string concept, string reference, int processorId)
		{
			if (amount == null) throw new ArgumentException(nameof(amount));

			Owner.UnLock(itIsThePresent, date, amount, this, who, documentNumber, store, concept, reference, processorId); 
		}

		internal void Accredit(bool itIsThePresent, DateTime date, Currency amount, string who, string documentNumber, Store store, string concept, string reference)
		{
			Accredit(itIsThePresent, date, amount, who, documentNumber, store, concept, reference, WholePaymentProcessor.NoPaymentProcessor);
		}

		internal void Accredit(bool itIsThePresent, DateTime date, Currency amount, string who, string documentNumber, Store store, string concept, string reference, int processorId)
		{
			if (amount == null) throw new ArgumentException(nameof(amount));

			Owner.Accredit(itIsThePresent, date, amount, this, who, documentNumber, store, concept, reference, processorId);
		}

		[Obsolete]
        internal void Accredit(bool itIsThePresent, DateTime date, Currency amount, string who, string documentNumber, Store store, string concept, string reference, string accountNumber)
        {
            Accredit(itIsThePresent, date, amount, who, documentNumber, store, concept, reference, accountNumber, WholePaymentProcessor.NoPaymentProcessor);
        }

        internal void Accredit(bool itIsThePresent, DateTime date, Currency amount, string who, string documentNumber, Store store, string concept, string reference, string accountNumber, int processorId)
		{
			if (amount == null) throw new ArgumentException(nameof(amount));

			Owner.Accredit(itIsThePresent, date, amount, this, who, documentNumber, store, concept, reference, accountNumber, processorId);
		}

		internal void Withdraw(bool itIsThePresent, DateTime date, Currency amount, string documentNumber, Store store, string concept, string reference)
		{
			Withdraw(itIsThePresent, date, amount, documentNumber, store, concept, reference, WholePaymentProcessor.NoPaymentProcessor);
		}

		internal void Withdraw(bool itIsThePresent, DateTime date, Currency amount, string documentNumber, Store store, string concept, string reference, int processorId)
		{
			if (amount == null) throw new ArgumentException(nameof(amount));

			Owner.Withdraw(itIsThePresent, date, amount, this, Users.LADYBET, documentNumber, store, concept, reference, processorId);
		}

		internal void Withdraw(bool itIsThePresent, DateTime date, Currency amount, string who, string documentNumber, Store store, string concept, string reference)
		{
			Withdraw(itIsThePresent, date, amount, who, documentNumber, store, concept, reference, WholePaymentProcessor.NoPaymentProcessor);
		}

		internal void Withdraw(bool itIsThePresent, DateTime date, Currency amount, string who, string documentNumber, Store store, string concept, string reference, int processorId)
		{
			Owner.Withdraw(itIsThePresent, date, amount, this, who, documentNumber, store, concept, reference, processorId);
		}

		internal void Withdraw(bool itIsThePresent, DateTime date, Currency amount, string who, string documentNumber, Store store, string concept, string reference, string accountNumber, int processorId)
		{
			if (amount == null) throw new ArgumentException(nameof(amount));

			Owner.Withdraw(itIsThePresent, date, amount, this, who, documentNumber, store, concept, reference, accountNumber, processorId);
		}
	}
}
