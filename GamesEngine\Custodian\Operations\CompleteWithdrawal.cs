﻿using GamesEngine.Business;
using GamesEngine.Domains;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using System;
using System.Collections.Generic;
using System.Text;
using town.connectors.drivers;

namespace GamesEngine.Custodian.Operations
{
	internal class CompleteWithdrawal : Operation
	{
		private Guardian guardian;
		internal int AccountId { get; }
		internal DateTime CreationDate { get; }
		internal TransactionType Abbreviation { get; }
		internal int ReferenceNumber { get; }
		internal string Description { get; }
		internal Currency DisbursementAmount { get; }
		internal int Group { get; }
		internal string ProcessorId { get; }
		internal Domain Domain { get; }
		private int approvals;
		private int approvalsRequired;
		internal StatusCodes Status { get; }
		internal Approvers ApproversWhoAlreadyApprove { get; }
		internal int Approvals { get; }
		internal int ApprovalsRequired { get; }
		internal bool Scheduled { get; }
		internal string Identifier { get; }

		public CompleteWithdrawal(Guardian guardian, InprocessWithDrawal inprocessWithDrawal):
			base(inprocessWithDrawal.TransactionId)
		{
			if (guardian == null) throw new ArgumentNullException(nameof(guardian));
			if (inprocessWithDrawal == null) throw new ArgumentNullException(nameof(inprocessWithDrawal));

			Status = StatusCodes.COMPLETE;
			ApproversWhoAlreadyApprove = inprocessWithDrawal.ApproversWhoAlreadyApprove;
			Approvals = inprocessWithDrawal.Approvals;
			ApprovalsRequired = inprocessWithDrawal.ApprovalsRequired;
			Scheduled = inprocessWithDrawal.Scheduled;
			this.guardian = guardian;
			AccountId = inprocessWithDrawal.ProcessorAccount.Id;
			CreationDate = inprocessWithDrawal.CreationDate;
			Abbreviation = inprocessWithDrawal.Abbreviation;
			ReferenceNumber = inprocessWithDrawal.ReferenceNumber;
			Description = inprocessWithDrawal.Description;
			DisbursementAmount = inprocessWithDrawal.DisbursementAmount;
			Group = inprocessWithDrawal.Group;
			ProcessorId = inprocessWithDrawal.Processor.Driver.Id;
			Domain = inprocessWithDrawal.Domain;
			Identifier = inprocessWithDrawal.Identifier;
		}
		internal InternalOperationUpdateMessage GenerateMessage(DateTime now)
		{
			Approvers approvers = (ApproversWhoAlreadyApprove == null) ? new Approvers() : ApproversWhoAlreadyApprove;

			return new InternalOperationUpdateMessage(
				Abbreviation,
				CreationDate,
				now,
				TransactionId,
				AccountId,
				ReferenceNumber,
				Description,
				DisbursementAmount,
				Group,
				ProcessorId,
				approvals,
				approvalsRequired,
				Scheduled,
				Domain.Url,
				approvers,
				Status,
				true,
				100,
				Identifier);
		}
	}
}
