using GamesEngine.Finance;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.commons;
using town.connectors.drivers;
using town.connectors.drivers.artemis;
using static town.connectors.drivers.Result;
using Puppeteer.EventSourcing;
using Connectors.town.connectors.commons;
using GamesEngine.Settings;
using System.Reflection;

namespace Connectors.town.connectors.drivers.artemis
{
    public class Fragment_an_Authorization : DGSTenantDriver, IFragmentDriver
    {
        private RestClient _postInsertWagersClient;
        private int _amountOfWagerPerChunk = 500;
        public int AmountOfWagersPerChunk
        {
            get
            {
                return _amountOfWagerPerChunk;
            }
            set
            {
                if (value < 1)
                {
                    throw new Exception($@"_amountOfWagerPerChunk must be greater than 1. ");
                }
                _amountOfWagerPerChunk = value;
            }
        }

        public Fragment_an_Authorization() : base(Tenant_Actions.Fragment)
        {
        }
        public override void Prepare(DateTime now)
        {
            CustomSettings.AddVariableParameter("customerId");
            CustomSettings.AddVariableParameter("amount");
            CustomSettings.AddVariableParameter("description");
            CustomSettings.AddVariableParameter("toWinsByDrawAndNumber");

            //CustomSettings.Prepare();

            SystemId = CustomSettings.Get(now, "TokenSystemId").AsString;
            Password = CustomSettings.Get(now, "TokenSystemPassword").AsSecret.ToString();
            ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
        }
        public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            throw new NotImplementedException();
        }
        public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
			if (_postInsertWagersClient == null)
			{
                if (string.IsNullOrWhiteSpace(SystemId)) throw new ArgumentNullException("Custom variable 'TokenSystemId' is required");
                if (string.IsNullOrWhiteSpace(Password)) throw new ArgumentNullException("Custom variable 'TokenSystemPassword' is required");
                if (string.IsNullOrWhiteSpace(ServicesUrl)) throw new ArgumentNullException("Custom variable 'CompanyBaseUrlServices' is required");

                if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken))
                {
                    _ = Task.Run(async () =>
                    {
                        await DGSTenantDriver.RequestAppTokenAsync(ServicesUrl, SystemId, Password);
                    });
                }

                _postInsertWagersClient = new RestClient(ServicesUrl);
            }

            if (CustomSettings.ThereArePendingChanges)
            {
                bool changeApplied = false;
                ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices", out changeApplied).AsString;
                SystemId = CustomSettings.Get(now, "TokenSystemId").AsString;
                Password = CustomSettings.Get(now, "TokenSystemPassword").AsSecret.ToString();

                if (changeApplied)
                {
                    if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken))
                    {
                        _ = Task.Run(async () =>
                        {
                            await DGSTenantDriver.RequestAppTokenAsync(ServicesUrl, SystemId, Password);
                        });
                    }

                    _postInsertWagersClient = new RestClient(ServicesUrl);
                }
            }

            var result = RegisterWagers(now, recordSet);
			return (T)Convert.ChangeType(result, typeof(T));
		}

        public InsertWagersResponse RegisterWagers(DateTime now, CustomSettings.RecordSet recordSet)
        {
            var CustomerId = recordSet.Mappings["customerId"];
            var Amount = recordSet.Mappings["amount"];
            var Description = recordSet.Mappings["description"];
            var ToWinsByDrawAndNumber = recordSet.Mappings["toWinsByDrawAndNumber"];

            string customerId = CustomerId.AsString;
            decimal amount = Amount.AsDecimal;
            string description = Description.AsString;
            var toWinsByDrawAndNumber = ToWinsByDrawAndNumber.As<List<ArtemisToWinByDrawAndNumber>>();

            if (String.IsNullOrWhiteSpace(customerId)) throw new ArgumentNullException(nameof(customerId));
            if (amount <= 0) throw new Exception($"Amount {amount} is not valid to send request {nameof(RegisterWagers)}");
            if (String.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));

            Debug.WriteLine($"Accounting service {nameof(RegisterWagers)} received {nameof(customerId)}:{customerId} {nameof(amount)}:{amount} {nameof(description)}:{description}");
            var result = InsertWagers(customerId, amount, description, toWinsByDrawAndNumber);
            return result;
        }

        private InsertWagersResponse InsertWagers(string customerId, decimal amount, string description, List<ArtemisToWinByDrawAndNumber> toWinsByDrawAndNumber)
        {
            if (String.IsNullOrWhiteSpace(customerId)) throw new ArgumentNullException(nameof(customerId));
            if (String.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));
            if (amount <= 0) throw new Exception($"{nameof(amount)} {amount} is not valid to send request {nameof(InsertWagers)}");
            if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken)) throw new Exception($"{nameof(DGSProcessorDriver.AppToken)} is empty");

            var values = new NonPostedTransactionBody()
            {
                idPlayer = int.Parse(customerId),
                amount = amount,
                description = description,
                tranCode = "D",
                tranType = "X"
            };
            const string url = "/transactions/non-posted";
            var jsonString = Commons.ToJson(values);

            string responseString = "";
            string valuesWithHiddenFields = $@"idPlayer:{customerId}, description:{description}, amount:{amount}";
            IRestResponse response;
            var responseObj = new InsertWagersResponse();
            responseObj.idTransaction = FAKE_DOCUMENT_NUMBER;
            responseObj.Url = url;
            try
            {
                Loggers.GetIntance().AccountingServicesDGSCreateWagers.Debug($@"url:{url} data:{valuesWithHiddenFields}");

                var request = new RestRequest(url, Method.POST);
                request.AddHeader("Authorization", $"Bearer {DGSProcessorDriver.AppToken}");
                request.AddHeader("Content-Type", "application/json");
                request.AddParameter("application/json", jsonString, ParameterType.RequestBody);

                var transactionId = APMHelper.CurrentTransactionId(APMHelper.CurrentTransaction());
                APMHelper.StartSpan(transactionId, transactionId);

                response = _postInsertWagersClient.Execute(request);

                APMHelper.EndSpan(transactionId);
                APMHelper.DisposeSpan(transactionId);

                responseString = response.Content;
                responseObj.Response = responseString;
                Loggers.GetIntance().AccountingServicesDGSCreateWagers.Debug($"{nameof(InsertWagers)}\nUrl:{url}\nResponse:{responseString}");
            }
            catch (Exception e)
            {
                APMHelper.CaptureException(e);
                Loggers.GetIntance().AccountingServicesDGSCreateWagers.Error($@"url:{url} data:{valuesWithHiddenFields} type:{e.GetType()} error:{e.Message}", e);

                var extraErrorMessage = string.Empty;
                if (e.InnerException is HttpRequestException) extraErrorMessage = "There was a problem getting the response from server";
                InternalOnError(nameof(InsertWagers), 1, e, $"Url:{url}", $"Request: {valuesWithHiddenFields}", $"Response: {responseString}", extraErrorMessage);
                return responseObj;
            }

            if (string.IsNullOrWhiteSpace(responseString))
            {
                responseObj.Code = AuthorizationResponseCode.AuthorizationFail;
            }
            else if (response.StatusCode == HttpStatusCode.OK || response.StatusCode == HttpStatusCode.Created)
            {
                try
                {
                    var successResponse = Commons.FromJson<NonPostedTransactionResponse>(responseString);
                    if (successResponse != null && successResponse.transactionId > 0)
                    {
                        responseObj.idTransaction = successResponse.transactionId;
                        responseObj.idPlayer = customerId;
                        responseObj.description = description;
                        responseObj.amount = amount;
                        responseObj.Code = AuthorizationResponseCode.OK;
                    }
                    else
                    {
                        responseObj.Code = AuthorizationResponseCode.UnexpectedFormat;
                    }
                }
                catch (Exception e)
                {
                    APMHelper.CaptureException(e);
                    responseObj.Code = AuthorizationResponseCode.UnexpectedFormat;
                }
            }
            else
            {
                try
                {
                    var errorResponse = Commons.FromJson<NonPostedTransactionErrorResponse>(responseString);
                    if (errorResponse != null)
                    {
                        responseObj.code = errorResponse.detail ?? errorResponse.title ?? "Unknown error";
                        responseObj.message = errorResponse.detail ?? errorResponse.title ?? "Unknown error";
                        responseObj.idTransaction = FAKE_DOCUMENT_NUMBER;

                        if (errorResponse.status == 400 && (errorResponse.detail?.Contains("insufficient") == true || errorResponse.detail?.Contains("funds") == true))
                        {
                            responseObj.Code = AuthorizationResponseCode.InsufficientFunds;
                        }
                        else
                        {
                            responseObj.Code = AuthorizationResponseCode.AuthorizationFail;
                        }
                    }
                    else
                    {
                        responseObj.Code = AuthorizationResponseCode.UnexpectedFormat;
                    }
                }
                catch (Exception e)
                {
                    APMHelper.CaptureException(e);
                    responseObj.Code = AuthorizationResponseCode.UnexpectedFormat;
                }
            }

            return responseObj;
        }

        public struct FragmentResponse: IDepositResponse
        {
            public int AuthorizationId { get; }
            public string ProcessorKey { get; }
            public TransactionStatus Status { get; }
            public InsertWagersResponse Response { get; set; }
            public string Message { get; }
            public AuthorizationResponseCode Code { get; }

            public FragmentResponse(int authorizationId, TransactionStatus status, AuthorizationResponseCode code, string message)
            {
                AuthorizationId = authorizationId;
                Status = status;
                Response = null;
                ProcessorKey = null;
                Message = message;
                Code = code;
            }
            public FragmentResponse(int authorizationId, TransactionStatus status, InsertWagersResponse response, string processorKey)
            {
                AuthorizationId = authorizationId;
                Status = status;
                Response = response;
                ProcessorKey = processorKey;
                Message = string.Empty;
                Code = AuthorizationResponseCode.OK;
            }
        }

        public class NonPostedTransactionBody
        {
            public int idPlayer { get; set; }
            public string description { get; set; }
            public decimal amount { get; set; }
            public string tranCode { get; set; }
            public string tranType { get; set; }
        }

        public class NonPostedTransactionResponse
        {
            public int transactionId { get; set; }
        }

        public class NonPostedTransactionErrorResponse
        {
            public string type { get; set; }
            public string title { get; set; }
            public int status { get; set; }
            public string detail { get; set; }
            public string instance { get; set; }
            public string additionalProp1 { get; set; }
            public string additionalProp2 { get; set; }
            public string additionalProp3 { get; set; }
        }

        public class InsertWagersResponse : IPaymentChannelResponse
        {
            public int idTransaction { get; set; }
            public string idPlayer { get; set; }
            public string description { get; set; }
            public decimal amount { get; set; }
            public string referenceId { get; set; }
            public int toWin { get; set; }
            public List<string> date { get; set; }
            public string code { get; set; }
            public string message { get; set; }
            public List<ToWinByDrawAndNumber> tickets { get; set; }
            public List<string> details { get; set; }
            public AuthorizationResponseCode Code { get; set; }
            public string Response { get; set; }
            public string Url { get; set; }
        }

        public class ToWinByDrawAndNumberResponse
        {
            public string date { get; set; }
            public string drawHour { get; set; }
            public string number { get; set; }
            public string draw { get; set; }
            public int maxPayout { get; set; }
            public int volumeByNumber { get; set; }
        }
    }

}
