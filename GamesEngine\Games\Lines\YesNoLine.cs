﻿using GamesEngine.Gameboards.Lines;
using GamesEngine.Games.Tournaments;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using GamesEngine.Settings;
using GamesEngine.Time;
using System;
using System.Collections.Generic;

namespace GamesEngine.Games.Lines
{
	internal class YesNoLine : Line
	{
		private readonly int yesReward;
		private readonly int noReward;
		private readonly double yesCoefficientReward;
		private readonly double noCoefficientReward;
		private YesNoAnswer realAnswer;

		internal YesNoLine(int lineId, Question question, Tier tier, Game game, Shelve shelve, int index, int yesReward, int noReward, string text, string who, DateTime now) : base(lineId, question, tier, game, shelve, index, who, now)
		{
			ValidateReward(yesReward, noReward);
			if (string.IsNullOrWhiteSpace(text)) throw new ArgumentNullException(nameof(text));

			this.yesReward = yesReward;
			this.noReward = noReward;
			this.yesCoefficientReward = CalculateRewardCoefficient(YesNoAnswer.YES);
			this.noCoefficientReward = CalculateRewardCoefficient(YesNoAnswer.NO);
			base.Text = ReplacePlaceholders(text, game);

			LogRewards();
		}

		private YesNoLine(YesNoLine previousVersion, int yesReward, int noReward, string who, DateTime now) : base(previousVersion, who, now)
		{
			ValidateReward(yesReward, noReward);

			this.yesReward = yesReward;
			this.noReward = noReward;
			this.yesCoefficientReward = CalculateRewardCoefficient(YesNoAnswer.YES);
			this.noCoefficientReward = CalculateRewardCoefficient(YesNoAnswer.NO);

			if (!base.IsDraft && !base.IsCanceled)
			{
				var changedLineEvent = this.GetChangedLineEvent(now);
				PlatformMonitor.GetInstance().WhenNewEvent(changedLineEvent);
			}

			LogRewards(previousVersion.YesReward, previousVersion.NoReward);
		}

		internal YesNoLine NewVersion(int yesReward, int noReward, string who, DateTime now)
		{
			var result = new YesNoLine(this, yesReward, noReward, who, now);
			return result;
		}

		internal WagerAnswer GetAnswer(bool answer)
		{
			YesNoAnswer result = answer ? YesNoAnswer.YES : YesNoAnswer.NO;
			return result;
		}

		private double CalculateRewardCoefficient(YesNoAnswer answer)
		{
			double result = 0;
			if (answer == YesNoAnswer.YES)
			{
				if (this.yesReward < 0)
				{
					result = 100 / (double)Math.Abs(this.yesReward);
				}
				else
				{
					result = Math.Abs(this.yesReward) / 100.0;
				}
			}
			else if (answer == YesNoAnswer.NO)
			{
				if (this.noReward < 0)
				{
					result = 100 / (double)Math.Abs(this.noReward);
				}
				else
				{
					result = Math.Abs(this.noReward) / 100.0;
				}
			}
			else
			{
				throw new GameEngineException("Wager does not have a valid chosen answer");
			}

			if (result == 0) throw new GameEngineException($"Prize reward for a {nameof(YesNoLine)} can not be zero.");

			return result;
		}

		internal static new void ValidateReward(int yesReward, int noReward)
		{
			if (yesReward == 0 || noReward == 0) throw new GameEngineException($"Both rewards must be greater than zero {yesReward}/{noReward}");
			if (Math.Abs(yesReward) <= 100) throw new GameEngineException($"Team A reward {yesReward} must be greater than 100");
			if (Math.Abs(noReward) <= 100) throw new GameEngineException($"Team B reward {noReward} must be greater than 100");
		}

		protected override double CoefficientReward(WagerAnswer answer)
		{
			if (answer == null) throw new ArgumentNullException(nameof(answer));

			if (answer == YesNoAnswer.YES)
			{
				return this.yesCoefficientReward;
			}
			else if (answer == YesNoAnswer.NO)
			{
				return this.noCoefficientReward;
			}

			throw new GameEngineException("Wager does not have a valid chosen answer");
		}

		internal override string RewardAsString(WagerAnswer answer)
		{
			if (answer == null) throw new ArgumentNullException(nameof(answer));

			int result;
			if (answer == YesNoAnswer.YES)
			{
				result = this.yesReward;
			}
			else if (answer == YesNoAnswer.NO)
			{
				result = this.noReward;
			}
			else
			{
				throw new GameEngineException("Wager does not have a valid chosen answer");
			}

			return (result > 0 ? "+" : "-") + result;
		}

		internal bool AnswerIsYes()
		{
			return realAnswer == YesNoAnswer.YES;
		}

		internal void SetRealAnswer(bool answer)
		{
			if (!this.IsLastVersion()) throw new GameEngineException("Answer should be set only for the original line");

			var yNAnswer = answer ? YesNoAnswer.YES : YesNoAnswer.NO;

			var currVersion = this;
			var exit = false;
			while (!exit)
			{
				currVersion.realAnswer = yNAnswer;
				exit = currVersion.IsOriginalVersion();
				if (!exit) currVersion = (YesNoLine)currVersion.PreviousVersion;
			}
		}

		internal override decimal Grade(Wager wager)
		{
			if (wager == null) throw new ArgumentNullException(nameof(wager));
			if (this.realAnswer == null) throw new GameEngineException("Real answer of this line has not been set");

			var answer = (YesNoAnswer)wager.ChosenAnswer;
			decimal prize = 0;
			bool won = answer == realAnswer;

			if (won)
			{
				prize = wager.ToWin();
				wager.GradeAsWinner();
			}
			else
			{
				prize = 0;
				wager.GradeAsLoser();
			}
			wager.Showcase.RiskAssestment.UpdateGrade(wager);

			return prize;
		}

		internal override string LineTypeAsString => LineType.YES_NO_LINE.ToString();

		internal override void Serialize(KafkaMessage message)
		{
			if (this.IsPending()) throw new GameEngineException("Line should be serialized only to send to BI and the end of grades");
			if ( ! IsOriginalVersion()) PreviousVersion.Serialize(message);

			message.AddProperty((char)LineType.YES_NO_LINE);
			message.AddProperty(base.LineId);
			message.AddProperty(base.Version);
			message.AddProperty(this.yesReward);
			message.AddProperty(this.noReward);
			message.AddProperty(base.Text);
			message.AddProperty(this.realAnswer == YesNoAnswer.YES ? 'Y' : 'N');
			message.AddProperty((int)base.Grading);
			message.AddProperty((int)base.BasedOn.Id);
		}

		protected override LineEvent GetChangedLineEvent(DateTime timestamp)
		{
			var result = new ChangedYesNoLineEvent(timestamp, this);
			return result;
		}

		protected override void ForcePublishedLineEvent(bool itIsThePresent, DateTime timestamp)
		{
			if (!base.IsPublished) throw new GameEngineException($"Line must be on publish visibility to send an event.");

			if (itIsThePresent)
			{
				var changedLineEvent = new PublishedYesNoLineEvent(timestamp, this);
				PlatformMonitor.GetInstance().WhenNewEvent(changedLineEvent);
			}
		}

		private void LogRewards()
		{
			Log.AppendAtTheEndOfTheLastEntry($"Rewards: over : {yesReward}, under : {noReward}");
		}

		private void LogRewards(int previousYesReward, int previousNoReward)
		{
			var log = OriginalVersion.Log;
			log.AppendAtTheEndOfTheLastEntry($"Previous rewards: yes : {previousYesReward}, no : {previousNoReward}. New rewards: yes : {yesReward}, no : {noReward}");
		}

		internal int YesReward
		{
			get
			{
				return this.yesReward;
			}
		}

		internal int NoReward
		{
			get
			{
				return this.noReward;
			}
		}
	}
}
