﻿using GamesEngine;
using GamesEngine.Business.Marketing;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;

namespace CashierAPI
{
    public class Settings
    {
		public static string CassandraHost { get; set; }
		public static string KeySpace { get; set; }
		public static bool UseCassandra { get; set; }
		public static int MonthsToArchivedMovements { get; set; }

		public static string DbSelected { get; set; }
        public static string ScriptBeforeRecovering { get; internal set; }
        public static string MySQL { get; set; }
        public static string SqlServer { get; internal set; }
		public static string IN_MEMORY { get; internal set; }
		public static void UseInMemoryStorageForUnitTesting()
		{
			Settings.DbSelected = DatabaseType.IN_MEMORY.ToString();
		}

		public static void ConfigureStorage()
        {
            MovementStorage storage = GetAStorageInstance();
			Movements.Storage = storage;
			StorageAsync storageAsync = ConfigureStorages();
			Movements.StorageAsync = storageAsync;
		}

        private static MovementStorage GetAStorageInstance()
        {
            MovementStorage storage = null;

            if (Settings.DbSelected == HistoricalDatabaseType.MySQL.ToString())
            {
                storage = ConfigureStorage(HistoricalDatabaseType.MySQL, Settings.MySQL);
            }
            else if (Settings.DbSelected == HistoricalDatabaseType.SQLServer.ToString())
            {
                storage = ConfigureStorage(HistoricalDatabaseType.SQLServer, Settings.SqlServer);
            }
            else if (String.IsNullOrWhiteSpace(Settings.DbSelected))
            {
#if DEBUG
				var DOTNET_RUNNING_IN_CONTAINER = bool.Parse(System.Environment.GetEnvironmentVariable("DOTNET_RUNNING_IN_CONTAINER") ?? "false");
				if (DOTNET_RUNNING_IN_CONTAINER)
					storage = ConfigureStorage(HistoricalDatabaseType.MySQL, Settings.MySQL);
				else
					storage = new MovementStorageMemory();
            #else
                throw new Exception($"There is no connection for {Integration.Db.DBSelected}");
            #endif
            }

			ConfigureStorageLongTerm(Settings.CassandraHost, Settings.KeySpace, Settings.UseCassandra, Settings.MonthsToArchivedMovements);

			return storage;
        }

		private static StorageAsync ConfigureStorages()
		{
			StorageAsync storage = null;
			if (Settings.DbSelected == HistoricalDatabaseType.MySQL.ToString())
			{
				storage = ConfigureStorageInternal(HistoricalDatabaseType.MySQL, Settings.MySQL);
			}
			else if (Settings.DbSelected == HistoricalDatabaseType.SQLServer.ToString())
			{
				storage = ConfigureStorageInternal(HistoricalDatabaseType.SQLServer, Settings.SqlServer);
			}
			else if (String.IsNullOrWhiteSpace(Settings.DbSelected))
			{
#if DEBUG
				var DOTNET_RUNNING_IN_CONTAINER = bool.Parse(System.Environment.GetEnvironmentVariable("DOTNET_RUNNING_IN_CONTAINER") ?? "false");
				if (DOTNET_RUNNING_IN_CONTAINER)
					storage = ConfigureStorageInternal(HistoricalDatabaseType.MySQL, Settings.MySQL);
				else
					storage = new StorageMemoryAsync();
#else
                throw new Exception($"There is no connection for {Integration.Db.DBSelected}");
#endif
			}

			return storage;
		}

		private static void ConfigureStorageLongTerm(string CassandraHostString, string KeySpace, bool UseCassandra, int MonthsToArchivedMovements)
		{
			if (String.IsNullOrEmpty(CassandraHostString)) throw new ArgumentNullException(nameof(CassandraHostString));
			if (MonthsToArchivedMovements <= 0) throw new ArgumentNullException(nameof(MonthsToArchivedMovements));

			LongTermController.ConfigureLongTerm(CassandraHostString, KeySpace, UseCassandra, MonthsToArchivedMovements);
		}

		private static MovementStorage ConfigureStorage(HistoricalDatabaseType dbType, string connectionString)
        {
            if (String.IsNullOrEmpty(connectionString)) throw new ArgumentNullException(nameof(connectionString));

            MovementStorage storage;

            if (dbType == HistoricalDatabaseType.MySQL)
            {
                storage = new MovementStorageMySQL(connectionString);
            }
            else if (dbType == HistoricalDatabaseType.SQLServer)
            {
                storage = new MovementStorageSQLServer(connectionString);
            }
            else
            {
                throw new GameEngineException($"There is no {nameof(MovementStorage)} implementation.");
            }
			
			storage.CreateAuthorizationSequenceIfNotExists();
			storage.CreateUserMovementsTableIfNotExists();
			
			return storage;
        }

		private static StorageAsync ConfigureStorageInternal(HistoricalDatabaseType dbType, string connectionString)
		{
			if (String.IsNullOrEmpty(connectionString)) throw new ArgumentNullException(nameof(connectionString));

			StorageAsync storage;

			if (dbType == HistoricalDatabaseType.MySQL)
			{
				storage = new StorageMySQLAsync(connectionString);
			}
			else if (dbType == HistoricalDatabaseType.SQLServer)
			{
				storage = new StorageSQLServerAsync(connectionString);
			}
			else
			{
				throw new GameEngineException($"There is no {nameof(StorageAsync)} implementation.");
			}

			return storage;
		}
	}
}
