﻿using Connectors.town.connectors.commons;
using Connectors.town.connectors.driver.transactions;
using GamesEngine.Finance;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using town.connectors.commons;
using static town.connectors.drivers.Result;

namespace town.connectors.drivers.fiero
{
    public class AuthorizationMultiple : FieroTenantDriver
	{
		public const int FAKE_TICKET_NUMBER = -1;
		private const float VERSION = 1.0F;

		public string CashierUrl { get; private set; }
		public override string Description => $"Fiero {nameof(AuthorizationMultiple)} driver {VERSION}";

		public AuthorizationMultiple()
			: base(Tenant_Actions.AuthorizationInternal)
        {
        }

		public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			var result = await AuthorzationAsync(now, recordSet);
			AuthorizationTransaction auth;
			if (result.AuthorizationNumber > 0)
			{
				auth = new AuthorizationTransaction(result.AuthorizationNumber, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes);
				return (T)Convert.ChangeType(auth, typeof(T));
			}
			auth = new AuthorizationTransaction(result.AuthorizationNumber, TransactionStatus.DENIED, Entity, PaymentMethod, CurrencyIsoCodes, result.Code, result.Url, result.Response);
			return (T)Convert.ChangeType(auth, typeof(T));
		}

		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			throw new NotImplementedException();
		}

		public override void Prepare(DateTime now)
		{
			CustomSettings.AddVariableParameter("accountNumber");
			CustomSettings.AddVariableParameter("atAddress");
			CustomSettings.AddVariableParameter("purchaseTotal.Value");
			CustomSettings.AddVariableParameter("purchaseTotal.CurrencyCode");
			CustomSettings.AddVariableParameter("storeId");
			CustomSettings.AddVariableParameter("concept");
			CustomSettings.AddVariableParameter("fragmentInformation");
			CustomSettings.AddVariableParameter("referenceNumber");
			CustomSettings.AddVariableParameter("useless");
			CustomSettings.AddVariableParameter("context");
			CustomSettings.AddVariableParameter("toWinsByDrawAndNumber");

			CashierUrl = CustomSettings.Get(now, "CashierDriver.url").AsString;
		}
		private async Task<GamesEngine.Finance.LockBalanceResponse> AuthorzationAsync(DateTime now, CustomSettings.RecordSet recordSet)
		{

			var AccountNumber = recordSet.Mappings["accountNumber"];
			var AtAddress = recordSet.Mappings["atAddress"];
			var PurchaseTotalValue = recordSet.Mappings["purchaseTotal.Value"];
			var PurchaseTotalCurrencyCode = recordSet.Mappings["purchaseTotal.CurrencyCode"];
			var StoreId = recordSet.Mappings["storeId"];
			var Concept = recordSet.Mappings["concept"];
			var FragmentInformation = recordSet.Mappings["fragmentInformation"];
			var ReferenceNumber = recordSet.Mappings["referenceNumber"];
			var Useless = recordSet.Mappings["useless"];
			var Context = recordSet.Mappings["context"];
			var ToWinsByDrawAndNumber = recordSet.Mappings["toWinsByDrawAndNumber"];

			string accountNumber = AccountNumber.AsString;
			string atAddress = AtAddress.AsString;
			decimal purchaseTotal = PurchaseTotalValue.AsDecimal;
			
			var lockBalanceData = new ExternalMultiLockBalanceData();
			lockBalanceData.AtAddress = atAddress;
			lockBalanceData.AccountNumber = accountNumber;
			lockBalanceData.PurchaseTotal = purchaseTotal;
			lockBalanceData.CurrencyCode = PurchaseTotalCurrencyCode.AsString;
			lockBalanceData.StoreId = StoreId.AsInt;
			lockBalanceData.Concept = Concept.AsString;
			if (recordSet.ContainsKeyName("fragmentInformation"))
				lockBalanceData.FragmentInformation = FragmentInformation.As<FragmentInformation>();
			if (recordSet.ContainsKeyName("referenceNumber"))
				lockBalanceData.Reference = ReferenceNumber.AsString;
            lockBalanceData.Useless = Useless.AsDateTime;
            lockBalanceData.ToWinsByDrawAndNumber = ToWinsByDrawAndNumber.As<List<ToWinByDrawAndNumber>>();
			HttpContext context = Context.As<HttpContext>();

			GamesEngine.Finance.LockBalanceResponse lockBalanceResponse = new GamesEngine.Finance.LockBalanceResponse();
			try
			{
				var url = $"{CashierUrl}api/customers/{atAddress}/balance/externalMultiLock";
				HttpRestClientConfiguration restClientToCashier = HttpRestClientConfiguration.GetInstance();
				var resultFromCashier = await restClientToCashier.PostAsync(context, url, lockBalanceData);
				if (!(resultFromCashier is OkObjectResult))
				{
					string body = string.Empty;
					if (resultFromCashier is ContentResult)
					{
						body = $@"error:{((ObjectResult)resultFromCashier).ToString()} \n url:{url} \n accountNumber:{accountNumber} \n purchaseTotal:{purchaseTotal}";
					}
					else
					{
						body = $@"url:{url} \n accountNumber:{accountNumber} \n purchaseTotal:{purchaseTotal}";
					}
					ErrorsSender.Send(body, $@"Lock amount to {accountNumber} fails.");
                    lockBalanceResponse.Url = url;
					lockBalanceResponse.Response = body;
					lockBalanceResponse.Code = AuthorizationResponseCode.AuthorizationFail;
					lockBalanceResponse.AuthorizationNumber = FAKE_TICKET_NUMBER;
                    return lockBalanceResponse;
				}

				string resp = ((OkObjectResult)resultFromCashier).Value.ToString();
				lockBalanceResponse = JsonConvert.DeserializeObject<GamesEngine.Finance.LockBalanceResponse>(resp);
				lockBalanceResponse.Url = url;
				lockBalanceResponse.Response = resp;
			}
			catch (Exception e)
			{
				ErrorsSender.Send(e);
                lockBalanceResponse.Code = AuthorizationResponseCode.AuthorizationFail;
                lockBalanceResponse.AuthorizationNumber = FAKE_TICKET_NUMBER;
			}

			return lockBalanceResponse;
		}

	}
}
