﻿using GamesEngine.Exchange;
using GamesEngine.Logs;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Log = GamesEngine.Logs.Log;

namespace GamesEngine.Customers
{
    [Puppet]
    internal class CustomerTypes: Objeto
    {
        internal List<CustomerType> Types { get; } = new List<CustomerType>();

        internal int Count => Types.Count;

        internal CustomerType NewCustomerType(int id, string name, string employeeName, DateTime now)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));
            if (ExistsName(name)) throw new GameEngineException($"There is a {nameof(CustomerType)} with the {nameof(name)} '{name}'");

            var customerType = new CustomerType(this, id, name, employeeName, now);
            Types.Add(customerType);
            nextConsecutive = id;

            return customerType;
        }

        private int nextConsecutive = 0;
        internal int NextConsecutive()
        {
            return nextConsecutive + 1;
        }

        internal bool ExistsName(string name)
        {
            if (string.IsNullOrWhiteSpace(name)) return false;

            foreach (var customerType in Types)
            {
                if (customerType.Name == name) return true;
            }
            return false;
        }

        internal IEnumerable<CustomerType> ListEnabledCustomerTypes()
        {
            var types = new List<CustomerType>();
            foreach (var customerType in Types)
            {
                if (customerType.Enabled)
                    types.Add(customerType);
            }
            return types;
        }

        internal CustomerType FindCustomerType(string name)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

            foreach (var customerType in Types)
            {
                if (customerType.Name == name) return customerType;
            }
            throw new GameEngineException($"There is no {nameof(CustomerType)} for {nameof(name)} '{name}' yet.");
        }

        internal CustomerType FindCustomerType(int id)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");

            foreach (var customerType in Types)
            {
                if (customerType.Id == id) return customerType;
            }
            throw new GameEngineException($"There is no {nameof(CustomerType)} for {nameof(id)} '{id}' yet.");
        }

        internal IEnumerable<CustomerTypeWithCustomerInfo> FindCustomerTypes(List<int> customerTypeIds, List<string> avatarPaths, List<string> identificationNumbers)
        {
            if (customerTypeIds.Count == 0) throw new GameEngineException($"{nameof(customerTypeIds)} cannot be empty");
            if (customerTypeIds.Count != avatarPaths.Count || customerTypeIds.Count != identificationNumbers.Count) throw new GameEngineException($"{nameof(customerTypeIds)}, {nameof(avatarPaths)}, {nameof(identificationNumbers)} must have the same amount of elements");

            var customerTypes = new List<CustomerTypeWithCustomerInfo>();
            for (int index = 0; index < customerTypeIds.Count; index++)
            {
                var customerTypeId = customerTypeIds[index];
                var avatarPath = avatarPaths[index];
                var identificationNumber = identificationNumbers[index];
                foreach (var customerType in Types)
                {
                    if (customerType.Id == customerTypeId)
                    {
                        customerTypes.Add(new CustomerTypeWithCustomerInfo(customerType, avatarPath, identificationNumber));
                        break;
                    }
                }
            }
            return customerTypes;
        }

        [Puppet]
        internal class CustomerTypeWithCustomerInfo : Objeto
        {
            internal CustomerType Type { get; }
            internal string AvatarPath { get; }
            internal string IdentificationNumber { get; }
            internal CustomerTypeWithCustomerInfo(CustomerType type, string avatarPath, string identificationNumber)
            {
                Type = type;
                AvatarPath = avatarPath;
                IdentificationNumber = identificationNumber;
            }
        }
    }

    [Puppet]
    internal class CustomerType: Objeto
    {
        internal int Id { get;}

        internal string Name { get; private set; }

        internal string Description { get; set; } = string.Empty;

        internal bool Enabled { get; private set; }

        private string badgePath = string.Empty;
        internal string BadgePath 
        {
            get 
            {
                return badgePath;
            }
            set
            {
                if (string.IsNullOrWhiteSpace(value)) throw new ArgumentNullException(nameof(value));
                badgePath = value;
            }
        }

        private readonly CustomerTypes customerTypes;
        internal Log Log { get; }

        private const int MAX_NUMBER_OF_ENTRIES = 5;

        public CustomerType(CustomerTypes customerTypes, int id, string name, string employeeName, DateTime now)
        {
            if (customerTypes == null) throw new ArgumentNullException(nameof(customerTypes));
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

            this.customerTypes = customerTypes;
            Id = id;
            Name = name;
            Enabled = true;
            Log = new Log(name, MAX_NUMBER_OF_ENTRIES);
            Log.AddEntry(now, employeeName, $"Created by {employeeName} at {now.ToString("dd/MM/yyyy hh:mm tt")}");
        }

        internal void Update(string name, bool enabled, string employeeName, DateTime now)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));
            if (Name != name && customerTypes.ExistsName(name)) throw new GameEngineException($"There is a {nameof(CustomerType)} with the {nameof(name)} '{name}'");

            Name = name;
            Enabled = enabled;
            Log.AddEntry(now, employeeName, $"Last update by {employeeName} at {now.ToString("dd/MM/yyyy hh:mm tt")}");
        }

        internal void Enable()
        {
            Enabled = true;
        }

        internal void Disable()
        {
            Enabled = false;
        }
    }
}
