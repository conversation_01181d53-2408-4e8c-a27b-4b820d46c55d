﻿using ExchangeAPI.ExchangeEntities;
using GamesEngine;
using GamesEngine.Accounting.JournalTemplates;
using GamesEngine.Business;
using GamesEngine.Exchange;
using GamesEngine.Exchange.Persistance;
using GamesEngine.Finance;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using town.connectors.drivers;
using static ExchangeAPI.Consumers.CashierResponse;
using static ExchangeAPI.UnsentTransaction;
using static GamesEngine.Exchange.BalanceMessage;
using static town.connectors.drivers.Result;
using Deposit = ExchangeAPI.ExchangeEntities.Deposit;

namespace ExchangeAPI
{
    public class Consumers
    {
        public static Storage Storage { get; } = new Storage();

        internal void CreateConsumerForTopics()
        {
            new ExternalOperationsConsumer(Integration.Kafka.Group, Integration.Kafka.TopicForIncomingOperations).StartListening();
            new ExternalApprovalForWithdrawalsConsumer(Integration.Kafka.Group, $"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForWithdrawals}").StartListening();
            new ConversionSpreadConsumer(Integration.Kafka.Group, $"{KafkaMessage.CONVERSION_SPREAD_EXCHANGE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForTransacctions}").StartListening();
            new ExchangeTransactionsConsumer(Integration.Kafka.Group, $"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForTransacctions}").StartListening();
            new VoucherUrlConsumer(Integration.Kafka.Group, $"{KafkaMessage.VOUCHER_URL_EXCHANGE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForTransacctions}").StartListening();
            new BatchBalancesConsumer(Integration.Kafka.Group, Integration.Kafka.TopicForBalances).StartListening();
            new FragmentPaymentsCallbackConsumer(Integration.Kafka.Group, Integration.Kafka.TopicForFragmentPaymentsCallback(Store.STORES_SEQUENCE_EXCHANGE)).StartListening();
            new CustomersConsumer($"{KafkaMessage.EXCHANGE_CONSUMER_PREFIX}{Integration.Kafka.Group}", Integration.Kafka.TopicForCustomers).StartListening();

            new StoreRegistrationConsumer(Integration.Kafka.Group, Integration.Kafka.TopicForStoreRegistration).StartListening();
            new CatalogConsumer(ValidateMessageOwnership, GetActor, $"{KafkaMessage.EXCHANGE_CONSUMER_PREFIX}{Integration.Kafka.Group}", Integration.Kafka.TopicForCatalog).StartListening();
        }

        bool ValidateMessageOwnership(string storeAlias)
        {
            var result = ExchangeManagerAPI.ExchangeManager.PerformQry(@"
                    {{
					    print company.Sales.CurrentStore.Alias storeAlias;
                    }}"
                );
            if (!(result is OkObjectResult))
            {
                throw new Exception($@"Error:{((ObjectResult)result).Value}");
            }
            var o = (OkObjectResult)result;
            var json = o.Value.ToString();
            var storeData = JsonConvert.DeserializeObject<StoreData>(json);
            return storeData.storeAlias == storeAlias;
        }

        RestAPIActorAsync GetActor()
        {
            var result = ExchangeManagerAPI.ExchangeManager;
            return result;
        }

        struct StoreData
        {
            public string storeAlias { get; set; }
        }

        private class CustomersConsumer : GamesEngine.Settings.Consumer
        {
            public CustomersConsumer(string group, string topic) : base(group, topic)
            {
            }


            public override void OnMessageBeforeCommit(string msg)
            {
                if (String.IsNullOrEmpty(msg)) throw new Exception(nameof(msg));

                CustomerMessage.CustomerMessageType messageType = CustomerMessage.GetType(msg);
                if (messageType == CustomerMessage.CustomerMessageType.NEW_CUSTOMER)
                {
                    NewCustomerMessage customerMsg = new NewCustomerMessage(msg);

                    var result = ExchangeManagerAPI.ExchangeManager.PerformQry($@"
                        {{
                            ItsNewOne = company.ExistsCustomer('{customerMsg.AccountNumber}');
                            print ! ItsNewOne theCustomerItsNewOne;
                        }}
                    ");

                    if (!(result is OkObjectResult))
                    {
                        if (result is ContentResult contentResult) throw new GameEngineException($"Error: {contentResult.Content}");
                        if (result is ObjectResult objResult) throw new GameEngineException($"Error: {objResult.Value}");
                        throw new GameEngineException($@"Error:{result}");
                    }

                    OkObjectResult o = (OkObjectResult)result;
                    string json = o.Value.ToString();
                    ExistCustomer existCustomer = JsonConvert.DeserializeObject<ExistCustomer>(json);

                    bool itsNewOne = existCustomer.TheCustomerItsNewOne;
                    if (itsNewOne)
                    {
                        var commandAssigningIdentifier = string.IsNullOrWhiteSpace(customerMsg.CustomerIdentifier) ? string.Empty : $"customer.Identifier='{customerMsg.CustomerIdentifier}';";
                        result = ExchangeManagerAPI.ExchangeManager.PerformCmd($@"
					        customer = company.GetOrCreateCustomerById('{customerMsg.AccountNumber}');
                            {commandAssigningIdentifier}
                            cust{customerMsg.AccountNumber} = customer;
					        {{
						        Eval('id =' + marketplace.NextAccountConsecutive() + ';');
						        marketplace.RegisterNewAccount(id, customer.FindAccountByCurrency('{Currencies.CODES.USD}'));
					        }}
				        ");
                        if (!(result is OkObjectResult))
                        {
                            if (result is ContentResult contentResult) throw new GameEngineException($"Error: {contentResult.Content}");
                            if (result is ObjectResult objResult) throw new GameEngineException($"Error: {objResult.Value}");
                            throw new GameEngineException($@"Error:{result}");
                        }
                    }
                }
                else if (messageType == CustomerMessage.CustomerMessageType.STATUS) 
                {
                    var message = new CustomerStatusSynchronizationMessage(msg);
                    string cmdToApproveOrReject;
                    switch (message.Status)
                    {
                        case Customer.CustomerStatus.Drafted:
                            cmdToApproveOrReject = "customer.Disapprove();";
                            break;
                        case Customer.CustomerStatus.Approved:
                            cmdToApproveOrReject = "customer.Approve();";
                            break;
                        case Customer.CustomerStatus.Rejected:
                            cmdToApproveOrReject = "customer.Reject();";
                            break;
                        default:
                            throw new GameEngineException($"There is no implementation for {nameof(message.Status)} {message.Status}");
                    }
                    var result = ExchangeManagerAPI.ExchangeManager.PerformCmd($@"
                    {{                               
                        customer = company.CustomerByAccountNumber('{message.AccountNumber}');
					    {cmdToApproveOrReject}
                    }}"
                    );
                    if (!(result is OkObjectResult))
                    {
                        if (result is ContentResult contentResult) throw new GameEngineException($"Error: {contentResult.Content}");
                        if (result is ObjectResult objResult) throw new GameEngineException($"Error: {objResult.Value}");
                        throw new GameEngineException($@"Error:{result}");
                    }
                }
            }
        }

        [DataContract(Name = "ExistCustomer")]
        public class ExistCustomer
        {
            [DataMember(Name = "theCustomerItsNewOne")]
            public bool TheCustomerItsNewOne { get; set; }
        }

        class StoreRegistrationConsumer : Consumer
        {
            public StoreRegistrationConsumer(string group, string topic) : base(group, topic)
            {
            }

            public override void OnMessageBeforeCommit(string msg)
            {
                if (string.IsNullOrWhiteSpace(msg)) throw new Exception(nameof(msg));

                var storeMsg = new StoreRegistrationMessage(msg);
                var tenantAndStoreId = RegisterTenantAndStore(storeMsg);

                var msgCallback = new StoreRegistrationCallbackMessage(storeMsg.TenantName, storeMsg.StoreName, tenantAndStoreId.tenantId, tenantAndStoreId.storeId, storeMsg.StoreAlias);
                Integration.Kafka.Send(true, $"{Integration.Kafka.TopicForStoreRegistration}{KafkaMessage.CATALOG_CALLBACK_CONSUMER_SUFFIX}", msgCallback);
            }

            TenantAndStoreId RegisterTenantAndStore(StoreRegistrationMessage storeMsg)
            {
                var result = ExchangeManagerAPI.ExchangeManager.PerformQry($@"
                    {{
                        existsTenant = company.System.Tenants.Exists('{storeMsg.TenantName}');
                        print existsTenant existsTenant;
                        if (existsTenant)
                        {{
                            tenant = company.System.Tenants.Find('{storeMsg.TenantName}');
                            print tenant.Id tenantId;
                        }}

                        existsStore = company.System.Stores.Exists('{storeMsg.StoreName}');
                        print existsStore existsStore;
					    if (existsStore)
                        {{
                            tenant = company.System.Stores.Find('{storeMsg.StoreName}');
                            print store.Id storeId;
                        }}
                    }}
				");
                if (!(result is OkObjectResult))
                {
                    throw new Exception($@"Error:{((ObjectResult)result).Value}");
                }
                var o = (OkObjectResult)result;
                var json = o.Value.ToString();
                var tenantAndStoreExistence = JsonConvert.DeserializeObject<TenantAndStoreExistence>(json);
                var scriptToAddTenantAndStore = new StringBuilder();
                if (!tenantAndStoreExistence.existsTenant)
                {
                    scriptToAddTenantAndStore.AppendLine(@$"tenant = company.System.Tenants.Add(itIsThePresent, '{storeMsg.TenantName}');
                                                            print tenant.Id tenantId;");
                }
                if (!tenantAndStoreExistence.existsStore)
                {
                    scriptToAddTenantAndStore.AppendLine(@$"store = company.Sales.AddStore(itIsThePresent, '{storeMsg.StoreName}');
				                                            store.Alias = '{storeMsg.StoreAlias}';
                                                            print store.Id storeId;");
                }

                TenantAndStoreId tenantAndStoreId = new TenantAndStoreId();
                if (scriptToAddTenantAndStore.Length != 0)
                {
                    result = ExchangeManagerAPI.ExchangeManager.PerformCmd($@"
                        {scriptToAddTenantAndStore}
				    ");
                    if (!(result is OkObjectResult))
                    {
                        throw new Exception($@"Error:{((ObjectResult)result).Value}");
                    }
                    o = (OkObjectResult)result;
                    json = o.Value.ToString();
                    tenantAndStoreId = JsonConvert.DeserializeObject<TenantAndStoreId>(json);
                }
                if (tenantAndStoreExistence.tenantId != 0)
                {
                    tenantAndStoreId.tenantId = tenantAndStoreExistence.tenantId;
                }
                if (tenantAndStoreExistence.storeId != 0)
                {
                    tenantAndStoreId.storeId = tenantAndStoreExistence.storeId;
                }
                var scriptToAddCustomSettings = GetScriptToAddCustomSettings(storeMsg);

                result = ExchangeManagerAPI.ExchangeManager.PerformCmd($@"
                    tenant = company.System.Tenants.Add({tenantAndStoreId.tenantId}, '{storeMsg.TenantName}');
                    tenant.MakeCurrent();
					store = company.Sales.CreateStore({tenantAndStoreId.storeId}, '{storeMsg.StoreName}');
				    store.Alias = '{storeMsg.StoreAlias}';
                    store.MakeCurrent();
                    {scriptToAddCustomSettings}
				");
                if (!(result is OkObjectResult))
                {
                    throw new Exception($@"Error:{((ObjectResult)result).Value}");
                }

                return tenantAndStoreId;
            }

            string GetScriptToAddCustomSettings(StoreRegistrationMessage storeMsg)
            {
                var script = new StringBuilder();
                script.AppendLine("customSettings = CustomSettingsCollection(company);");
                foreach (var customSetting in storeMsg.CustomSettings)
                {
                    switch (customSetting.EnumType)
                    {
                        case CustomSettingType.Boolean:
                        case CustomSettingType.Integer:
                            script.AppendLine($"cs = customSettings.AddFixedParameter(Now, '{customSetting.Key}', {customSetting.Value});");
                            break;
                        case CustomSettingType.String:
                            script.AppendLine($"cs = customSettings.AddFixedParameter(Now, '{customSetting.Key}', '{customSetting.Value}');");
                            break;
                        case CustomSettingType.Secret:
                            script.AppendLine($"cs = customSettings.AddSecretParameter(Now, '{customSetting.Key}', '{customSetting.Value}');");
                            break;
                        default:
                            throw new Exception($"No implementation for {nameof(customSetting.Type)} '{customSetting.Type}'");
                    }
                    if (! string.IsNullOrWhiteSpace(customSetting.Description)) script.AppendLine($"cs.Description = '{Validator.StringEscape(customSetting.Description)}';");
                }
                return script.ToString();
            }

            struct TenantAndStoreId
            {
                public int tenantId { get; set; }
                public int storeId { get; set; }
            }

            struct TenantAndStoreExistence
            {
                public int tenantId { get; set; }
                public int storeId { get; set; }
                public bool existsTenant { get; set; }
                public bool existsStore { get; set; }
            }
        }

        private class ConversionSpreadConsumer : Consumer
        {
            public ConversionSpreadConsumer(string group, string topic) : base(group, topic)
            {
            }

            public override void OnMessageBeforeCommit(string msg)
            {
                if (String.IsNullOrEmpty(msg)) throw new ArgumentNullException(nameof(msg));

                var message = new ConversionSpreadMessage(msg);

                var user = Integration.ExchangeUsers.User(message.EmployeeName);
                var exchangerate = new Exchangerate()
                {
                    Id = message.Id,
                    Date = message.Date,
                    Fromcurrencyid = message.FromCurrencyCode.Id,
                    Tocurrencyid = message.ToCurrencyCode.Id,
                    Purchaseprice = message.PurchasePrice,
                    Saleprice = message.SalePrice,
                    Whocreated = user.Id
                };

                Integration.ExchangeRates.Save(exchangerate);
            }
        }

        const int DEFAULT_APPROVALS_REQUIRED = 1;
        private class ExchangeTransactionsConsumer : Consumer
        {
            public ExchangeTransactionsConsumer(string group, string topic) : base(group, topic)
            {
            }

            public override void OnMessageBeforeCommit(string msg)
            {
                if (string.IsNullOrWhiteSpace(msg)) throw new ArgumentNullException(nameof(msg));

                TransactionMessageType messageType = (TransactionMessageType)(int)msg[0];
                switch (messageType)
                {
                    case TransactionMessageType.JournalEntry:
                        AddJournalEntry(msg);
                        break;
                    case TransactionMessageType.Coin:
                        InsertCurrency(new CoinMessage(msg));
                        break;
                    case TransactionMessageType.Transaction:
                        {
                            var headerMessage = new HeaderExchangeTransactionMessage(msg);
                            switch (headerMessage.Type)
                            {
                                case TransactionType.Deposit:
                                    switch (headerMessage.Status)
                                    {
                                        case TransactionStatus.APPROVED:
                                            var approvedMessage = new ApprovedTransactionMessage(msg);
                                            Logic.DailyProfits.UpdateDepositAsApproved(
                                                approvedMessage.Id,
                                                approvedMessage.AuthorizationId,
                                                approvedMessage.ApprovalDate,
                                                approvedMessage.Profit,
                                                approvedMessage.Purchase,
                                                approvedMessage.Sale,
                                                approvedMessage.FromCurrencyCode,
                                                approvedMessage.ToCurrencyCode,
                                                approvedMessage.EmployeeName,
                                                approvedMessage.ProcessorId
                                                );
                                            break;
                                        case TransactionStatus.DENIED:
                                            var deniedMessage = new DeniedTransactionMessage(msg);
                                            Logic.Deposits.UpdateAsRejected(deniedMessage.Id, deniedMessage.RejectionDate, deniedMessage.Reason, deniedMessage.EmployeeName);
                                            break;
                                        case TransactionStatus.DRAFT:
                                            var draftedMessage = new DraftDepositMessage(msg);
                                            if (draftedMessage.Type != TransactionType.Deposit) throw new GameEngineException($"{nameof(draftedMessage.Type)} is {draftedMessage.Type} and should be {TransactionType.Deposit}");

                                            var user = Integration.ExchangeUsers.User(draftedMessage.EmployeeName);
                                            var domain = Integration.Domains.Domain(draftedMessage.Domain);
                                            var deposit = new Deposit()
                                            {
                                                Id = draftedMessage.Id,
                                                Authorizationid = draftedMessage.Id,
                                                Batchnumber = draftedMessage.BatchNumber,
                                                Datecreated = draftedMessage.Date,
                                                Fromcurrencyid = draftedMessage.Coin.Id,
                                                Tocurrencyid = draftedMessage.ToCoin.Id,
                                                Exchangerateid = draftedMessage.RateId == 0 ? (long?)null : draftedMessage.RateId,
                                                Tocustomer = draftedMessage.Customer,
                                                Amount = draftedMessage.Amount,
                                                Gross = draftedMessage.Gross,
                                                Net = draftedMessage.Net,
                                                Profit = draftedMessage.Profit,
                                                Approvals = 0,
                                                Approvalsrequired = DEFAULT_APPROVALS_REQUIRED,
                                                Rejections = 0,
                                                Description = draftedMessage.Concept == DraftTransactionMessage.EMPTY_STRING ? string.Empty : draftedMessage.Concept,
                                                Whocreated = user.Id,
                                                Account = draftedMessage.Account,
                                                Depositor = draftedMessage.Depositor == DraftTransactionMessage.EMPTY_STRING ? string.Empty : draftedMessage.Depositor,
                                                Voucher = draftedMessage.Voucher == DraftTransactionMessage.EMPTY_STRING ? string.Empty : draftedMessage.Voucher,
                                                Voucherurl = draftedMessage.VoucherUrl == DraftTransactionMessage.EMPTY_STRING ? string.Empty : draftedMessage.VoucherUrl,
                                                Domain = domain.Id,
                                                Processorid = draftedMessage.ProcessorId
                                            };

                                            Integration.Deposits.Save(deposit);
                                            break;
                                        default:
                                            throw new GameEngineException($"There is no implementation to update {nameof(headerMessage.Status)} {headerMessage.Status}");
                                    }
                                    break;
                                case TransactionType.Withdrawal:
                                    switch (headerMessage.Status)
                                    {
                                        case TransactionStatus.APPROVED:
                                            var approvedMessage = new ApprovedTransactionMessage(msg);
                                            Logic.DailyProfits.UpdateWithdrawalAsApproved(
                                                approvedMessage.Id,
                                                approvedMessage.ApprovalDate,
                                                approvedMessage.Profit,
                                                approvedMessage.Purchase,
                                                approvedMessage.Sale,
                                                approvedMessage.FromCurrencyCode,
                                                approvedMessage.ToCurrencyCode,
                                                approvedMessage.EmployeeName,
                                                approvedMessage.ProcessorId
                                                );
                                            break;
                                        case TransactionStatus.DENIED:
                                            var deniedMessage = new DeniedTransactionMessage(msg);
                                            Logic.Withdrawals.UpdateAsRejected(deniedMessage.Id, deniedMessage.RejectionDate, deniedMessage.Reason, deniedMessage.EmployeeName);
                                            break;
                                        case TransactionStatus.DRAFT:
                                            var draftedMessage = new DraftWithdrawalMessage(msg);
                                            if (draftedMessage.Type != TransactionType.Withdrawal) throw new GameEngineException($"{nameof(draftedMessage.Type)} is {draftedMessage.Type} and should be {TransactionType.Withdrawal}");

                                            var user = Integration.ExchangeUsers.User(draftedMessage.EmployeeName);
                                            var domain = Integration.Domains.Domain(draftedMessage.Domain);
                                            var withdrawal = new Withdrawal()
                                            {
                                                Id = draftedMessage.Id,
                                                Batchnumber = draftedMessage.BatchNumber,
                                                Datecreated = draftedMessage.Date,
                                                Fromcurrencyid = draftedMessage.Coin.Id,
                                                Tocurrencyid = draftedMessage.ToCoin.Id,
                                                Exchangerateid = draftedMessage.RateId == 0 ? (long?)null : draftedMessage.RateId,
                                                Fromcustomer = draftedMessage.Customer,
                                                Amount = draftedMessage.Amount,
                                                Gross = draftedMessage.Gross,
                                                Net = draftedMessage.Net,
                                                Profit = draftedMessage.Profit,
                                                Approvals = 0,
                                                Approvalsrequired = DEFAULT_APPROVALS_REQUIRED,
                                                Rejections = 0,
                                                Description = draftedMessage.Concept == DraftTransactionMessage.EMPTY_STRING ? string.Empty : draftedMessage.Concept,
                                                Authorizationid = draftedMessage.AuthorizationId,
                                                Whocreated = user.Id,
                                                Account = draftedMessage.Account,
                                                Realaccount = draftedMessage.RealAccount == DraftTransactionMessage.EMPTY_STRING ? string.Empty : draftedMessage.RealAccount,
                                                Minerfee = draftedMessage.MinerFee,
                                                Domain = domain.Id,
                                                Processorid = draftedMessage.ProcessorId
                                            };

                                            Integration.Withdrawals.Save(withdrawal);
                                            break;
                                        default:
                                            throw new GameEngineException($"There is no implementation to update {nameof(headerMessage.Status)} {headerMessage.Status}");
                                    }
                                    break;
                                case TransactionType.Transfer:
                                    switch (headerMessage.Status)
                                    {
                                        case TransactionStatus.APPROVED:
                                            var approvedMessage = new ApprovedTransactionMessage(msg);
                                            Logic.DailyProfits.UpdateTransferAsApproved(
                                                approvedMessage.Id,
                                                approvedMessage.ApprovalDate,
                                                approvedMessage.Profit,
                                                approvedMessage.Purchase,
                                                approvedMessage.Sale,
                                                approvedMessage.FromCurrencyCode,
                                                approvedMessage.ToCurrencyCode,
                                                approvedMessage.EmployeeName,
                                                approvedMessage.ProcessorId
                                                );
                                            break;
                                        case TransactionStatus.DENIED:
                                            var deniedMessage = new DeniedTransactionMessage(msg);
                                            Logic.Transfers.UpdateAsRejected(deniedMessage.Id, deniedMessage.RejectionDate, deniedMessage.Reason, deniedMessage.EmployeeName);
                                            break;
                                        case TransactionStatus.DRAFT:
                                            var draftedMessage = new DraftTransferMessage(msg);
                                            if (draftedMessage.Type != TransactionType.Transfer) throw new GameEngineException($"{nameof(draftedMessage.Type)} is {draftedMessage.Type} and should be {TransactionType.Transfer}");

                                            var user = Integration.ExchangeUsers.User(draftedMessage.EmployeeName);
                                            var domain = Integration.Domains.Domain(draftedMessage.Domain);
                                            var transfer = new Transfer()
                                            {
                                                Id = draftedMessage.Id,
                                                Batchnumber = draftedMessage.BatchNumber,
                                                Datecreated = draftedMessage.Date,
                                                Fromcurrencyid = draftedMessage.Coin.Id,
                                                Fromcustomer = draftedMessage.Customer,
                                                Amount = draftedMessage.Amount,
                                                Gross = draftedMessage.Gross,
                                                Net = draftedMessage.Net,
                                                Profit = draftedMessage.Profit,
                                                Approvals = 0,
                                                Approvalsrequired = DEFAULT_APPROVALS_REQUIRED,
                                                Rejections = 0,
                                                Description = draftedMessage.Concept == DraftTransactionMessage.EMPTY_STRING ? string.Empty : draftedMessage.Concept,
                                                Tocustomer = draftedMessage.ToCustomer,
                                                Tocurrencyid = draftedMessage.ToCoin.Id,
                                                Exchangerateid = draftedMessage.RateId == 0 ? (long?)null : draftedMessage.RateId,
                                                Authorizationid = draftedMessage.AuthorizationId,
                                                Whocreated = user.Id,
                                                Account = draftedMessage.Account,
                                                Realaccount = draftedMessage.RealAccount == DraftTransactionMessage.EMPTY_STRING ? string.Empty : draftedMessage.RealAccount,
                                                Targetaccount = draftedMessage.TargetAccount,
                                                Domain = domain.Id
                                            };

                                            Integration.Transfers.Save(transfer);
                                            break;
                                        default:
                                            throw new GameEngineException($"There is no implementation to update {nameof(headerMessage.Status)} {headerMessage.Status}");
                                    }
                                    break;
                                case TransactionType.CreditNote:
                                    switch (headerMessage.Status)
                                    {
                                        case TransactionStatus.APPROVED:
                                            var approvedMessage = new ApprovedTransactionMessage(msg);
                                            Logic.CreditNotes.UpdateAsApproved(
                                                approvedMessage.Id,
                                                approvedMessage.ApprovalDate,
                                                approvedMessage.EmployeeName,
                                                approvedMessage.ProcessorId
                                                );
                                            break;
                                        case TransactionStatus.DENIED:
                                            var deniedMessage = new DeniedTransactionMessage(msg);
                                            Logic.CreditNotes.UpdateAsRejected(deniedMessage.Id, deniedMessage.RejectionDate, deniedMessage.Reason, deniedMessage.EmployeeName);
                                            break;
                                        case TransactionStatus.DRAFT:
                                            var mainMessage = new DraftCreditNoteMessages(msg);
                                            var message = mainMessage.DraftCreditNoteMessage;
                                            if (message.Type != TransactionType.CreditNote) throw new GameEngineException($"{nameof(message.Type)} is {message.Type} and should be {TransactionType.CreditNote}");

                                            var user = Integration.ExchangeUsers.User(message.EmployeeName);
                                            var domain = Integration.Domains.Domain(message.Domain);
                                            var creditNote = new CreditNote()
                                            {
                                                Id = message.Id,
                                                Batchnumber = message.BatchNumber,
                                                Datecreated = message.Date,
                                                Currencyid = message.Coin.Id,
                                                Customer = message.Customer,
                                                Amount = message.Amount,
                                                Approvals = 0,
                                                Approvalsrequired = DEFAULT_APPROVALS_REQUIRED,
                                                Rejections = 0,
                                                Description = message.Concept,
                                                Whocreated = user.Id,
                                                Account = message.Account,
                                                Referenceid = message.ReferenceId,
                                                Domain = domain.Id
                                            };

                                            var noAttachments = mainMessage.AttachmentMessages.Count == 0;
                                            if (noAttachments)
                                            {
                                                Logic.CreditNotes.Save(creditNote);
                                            }
                                            else
                                            {
                                                var urls = mainMessage.AttachmentMessages.Select(attachment => attachment.Url);
                                                Logic.CreditNotes.SaveWithAttachments(creditNote, urls);
                                            }
                                            break;
                                        default:
                                            throw new GameEngineException($"There is no implementation to update {nameof(headerMessage.Status)} {headerMessage.Status}");
                                    }
                                    break;
                                case TransactionType.DebitNote:
                                    switch (headerMessage.Status)
                                    {
                                        case TransactionStatus.APPROVED:
                                            var approvedMessage = new ApprovedTransactionMessage(msg);
                                            Logic.DebitNotes.UpdateAsApproved(
                                                approvedMessage.Id,
                                                approvedMessage.ApprovalDate,
                                                approvedMessage.EmployeeName,
                                                approvedMessage.ProcessorId
                                                );
                                            break;
                                        case TransactionStatus.DENIED:
                                            var deniedMessage = new DeniedTransactionMessage(msg);
                                            Logic.DebitNotes.UpdateAsRejected(deniedMessage.Id, deniedMessage.RejectionDate, deniedMessage.Reason, deniedMessage.EmployeeName);
                                            break;
                                        case TransactionStatus.DRAFT:
                                            var mainMessage = new DraftDebitNoteMessages(msg);
                                            var message = mainMessage.DraftDebitNoteMessage;
                                            if (message.Type != TransactionType.DebitNote) throw new GameEngineException($"{nameof(message.Type)} is {message.Type} and should be {TransactionType.DebitNote}");

                                            var user = Integration.ExchangeUsers.User(message.EmployeeName);
                                            var domain = Integration.Domains.Domain(message.Domain);
                                            var debitNote = new DebitNote()
                                            {
                                                Id = message.Id,
                                                Batchnumber = message.BatchNumber,
                                                Datecreated = message.Date,
                                                Currencyid = message.Coin.Id,
                                                Customer = message.Customer,
                                                Amount = message.Amount,
                                                Approvals = 0,
                                                Approvalsrequired = DEFAULT_APPROVALS_REQUIRED,
                                                Rejections = 0,
                                                Description = message.Concept,
                                                Whocreated = user.Id,
                                                Account = message.Account,
                                                Referenceid = message.ReferenceId,
                                                Domain = domain.Id
                                            };

                                            var noAttachments = mainMessage.AttachmentMessages.Count == 0;
                                            if (noAttachments)
                                            {
                                                Logic.DebitNotes.Save(debitNote);
                                            }
                                            else
                                            {
                                                var urls = mainMessage.AttachmentMessages.Select(attachment => attachment.Url);
                                                Logic.DebitNotes.SaveWithAttachments(debitNote, urls);
                                            }
                                            break;
                                        default:
                                            throw new GameEngineException($"There is no implementation to update {nameof(headerMessage.Status)} {headerMessage.Status}");
                                    }
                                    break;
                                default:
                                    throw new GameEngineException($"There is no implementation to update {nameof(headerMessage.Type)} {headerMessage.Type}");
                            }
                        }
                        break;
                    default:
                        throw new GameEngineException($"There is no implementation for {nameof(messageType)} {messageType}");
                }
            }

            void AddJournalEntry(string msg)
            {
                var baseMessage = new JournalEntryMessage(msg);
                switch (baseMessage.Type)
                {
                    case TransactionType.Deposit:
                        var msgDeposit = new JournalEntryForDepositMessage(msg);
                        var template = DepositTransaction.Template;

                        var transactionIdArg = template.Parameters["TransactionId"].Argument;
                        var dateArg = template.Parameters["Date"].Argument;
                        var customerNumberArg = template.Parameters["CustomerNumber"].Argument;
                        var accountNumberArg = template.Parameters["AccountNumber"].Argument;
                        var currencyArg = template.Parameters["Currency"].Argument;
                        var saleRateArg = template.Parameters["SaleRate"].Argument;
                        var purchaseRateArg = template.Parameters["PurchaseRate"].Argument;
                        var totalAmountArg = template.Parameters["TotalAmount"].Argument;

                        transactionIdArg.SetValue(msgDeposit.TransactionId.ToString());
                        dateArg.SetValue(msgDeposit.ApprovalDateFormattedAsText);
                        customerNumberArg.SetValue(msgDeposit.CustomerNumber);
                        accountNumberArg.SetValue(msgDeposit.AccountNumber);
                        var currencyCodeAsText = msgDeposit.CurrencyCode.ToString();
                        currencyArg.SetValue(currencyCodeAsText);
                        saleRateArg.SetValue(msgDeposit.SaleRate);
                        purchaseRateArg.SetValue(msgDeposit.PurchaseRate);
                        totalAmountArg.SetValue(msgDeposit.TotalAmount);

                        JournalEntry journalEntry = template.Calculate($"AD-{msgDeposit.JournalEntryId}", msgDeposit.ApprovalDate, "TransactionId", $"Deposit {currencyCodeAsText}");
                        InsertJournalEntry(journalEntry, msgDeposit.EmployeeName, baseMessage.Type, msgDeposit.TransactionId);

                        break;
                    case TransactionType.Withdrawal:
                        var msgWithdrawal = new JournalEntryForWithdrawalMessage(msg);
                        template = WithdrawalTransaction.Template;

                        var authorizationIdArg = template.Parameters["AuthorizationId"].Argument;
                        transactionIdArg = template.Parameters["TransactionId"].Argument;
                        dateArg = template.Parameters["Date"].Argument;
                        customerNumberArg = template.Parameters["CustomerNumber"].Argument;
                        accountNumberArg = template.Parameters["AccountNumber"].Argument;
                        currencyArg = template.Parameters["Currency"].Argument;
                        saleRateArg = template.Parameters["SaleRate"].Argument;
                        purchaseRateArg = template.Parameters["PurchaseRate"].Argument;
                        var currencyCostArg = template.Parameters["CurrencyCost"].Argument;
                        totalAmountArg = template.Parameters["TotalAmount"].Argument;

                        authorizationIdArg.SetValue(msgWithdrawal.AuthorizationId.ToString());
                        transactionIdArg.SetValue(msgWithdrawal.TransactionId.ToString());
                        dateArg.SetValue(msgWithdrawal.ApprovalDateFormattedAsText);
                        customerNumberArg.SetValue(msgWithdrawal.CustomerNumber);
                        accountNumberArg.SetValue(msgWithdrawal.AccountNumber);
                        currencyCodeAsText = msgWithdrawal.CurrencyCode.ToString();
                        currencyArg.SetValue(currencyCodeAsText);
                        saleRateArg.SetValue(msgWithdrawal.SaleRate);
                        purchaseRateArg.SetValue(msgWithdrawal.PurchaseRate);
                        currencyCostArg.SetValue(msgWithdrawal.CurrencyCost);
                        totalAmountArg.SetValue(msgWithdrawal.TotalAmount);

                        journalEntry = template.Calculate($"AD-{msgWithdrawal.JournalEntryId}", msgWithdrawal.ApprovalDate, "TransactionId", $"Withdrawal {currencyCodeAsText}");
                        InsertJournalEntry(journalEntry, msgWithdrawal.EmployeeName, baseMessage.Type, msgWithdrawal.TransactionId);

                        break;
                    case TransactionType.Transfer:
                        var msgTransfer = new JournalEntryForTransferMessage(msg);
                        template = TransferTransaction.Template;

                        authorizationIdArg = template.Parameters["AuthorizationId"].Argument;
                        transactionIdArg = template.Parameters["TransactionId"].Argument;
                        dateArg = template.Parameters["Date"].Argument;
                        var fromCustomerNumberArg = template.Parameters["FromCustomerNumber"].Argument;
                        var fromAccountNumberArg = template.Parameters["FromAccountNumber"].Argument;
                        var toCustomerNumberArg = template.Parameters["ToCustomerNumber"].Argument;
                        var toAccountNumberArg = template.Parameters["ToAccountNumber"].Argument;
                        currencyArg = template.Parameters["Currency"].Argument;
                        saleRateArg = template.Parameters["SaleRate"].Argument;
                        purchaseRateArg = template.Parameters["PurchaseRate"].Argument;
                        currencyCostArg = template.Parameters["CurrencyCost"].Argument;
                        totalAmountArg = template.Parameters["TotalAmount"].Argument;

                        authorizationIdArg.SetValue(msgTransfer.AuthorizationId.ToString());
                        transactionIdArg.SetValue(msgTransfer.TransactionId.ToString());
                        dateArg.SetValue(msgTransfer.ApprovalDateFormattedAsText);
                        fromCustomerNumberArg.SetValue(msgTransfer.FromCustomerNumber);
                        fromAccountNumberArg.SetValue(msgTransfer.FromAccountNumber);
                        toCustomerNumberArg.SetValue(msgTransfer.ToCustomerNumber);
                        toAccountNumberArg.SetValue(msgTransfer.ToAccountNumber);
                        currencyCodeAsText = msgTransfer.CurrencyCode.ToString();
                        currencyArg.SetValue(currencyCodeAsText);
                        saleRateArg.SetValue(msgTransfer.SaleRate);
                        purchaseRateArg.SetValue(msgTransfer.PurchaseRate);
                        currencyCostArg.SetValue(msgTransfer.CurrencyCost);
                        totalAmountArg.SetValue(msgTransfer.TotalAmount);

                        journalEntry = template.Calculate($"AD-{msgTransfer.JournalEntryId}", msgTransfer.ApprovalDate, "TransactionId", $"Swap {currencyCodeAsText}");
                        InsertJournalEntry(journalEntry, msgTransfer.EmployeeName, baseMessage.Type, msgTransfer.TransactionId);

                        break;
                    case TransactionType.CreditNote:
                        var msgCreditNote = new JournalEntryForCreditNoteMessage(msg);
                        template = CreditNoteTransaction.Template;

                        transactionIdArg = template.Parameters["TransactionId"].Argument;
                        dateArg = template.Parameters["Date"].Argument;
                        customerNumberArg = template.Parameters["CustomerNumber"].Argument;
                        accountNumberArg = template.Parameters["AccountNumber"].Argument;
                        currencyArg = template.Parameters["Currency"].Argument;
                        saleRateArg = template.Parameters["SaleRate"].Argument;
                        purchaseRateArg = template.Parameters["PurchaseRate"].Argument;
                        currencyCostArg = template.Parameters["CurrencyCost"].Argument;
                        totalAmountArg = template.Parameters["TotalAmount"].Argument;

                        transactionIdArg.SetValue(msgCreditNote.TransactionId.ToString());
                        dateArg.SetValue(msgCreditNote.ApprovalDateFormattedAsText);
                        customerNumberArg.SetValue(msgCreditNote.CustomerNumber);
                        accountNumberArg.SetValue(msgCreditNote.AccountNumber);
                        currencyCodeAsText = msgCreditNote.CurrencyCode.ToString();
                        currencyArg.SetValue(currencyCodeAsText);
                        saleRateArg.SetValue(msgCreditNote.SaleRate);
                        purchaseRateArg.SetValue(msgCreditNote.PurchaseRate);
                        currencyCostArg.SetValue(msgCreditNote.CurrencyCost);
                        totalAmountArg.SetValue(msgCreditNote.TotalAmount);

                        journalEntry = template.Calculate($"AD-{msgCreditNote.JournalEntryId}", msgCreditNote.ApprovalDate, "TransactionId", $"Credit Note {currencyCodeAsText}");
                        InsertJournalEntry(journalEntry, msgCreditNote.EmployeeName, baseMessage.Type, msgCreditNote.TransactionId);

                        break;
                    case TransactionType.DebitNote:
                        var msgDebitNote = new JournalEntryForDebitNoteMessage(msg);
                        template = DebitNoteTransaction.Template;

                        transactionIdArg = template.Parameters["TransactionId"].Argument;
                        dateArg = template.Parameters["Date"].Argument;
                        customerNumberArg = template.Parameters["CustomerNumber"].Argument;
                        accountNumberArg = template.Parameters["AccountNumber"].Argument;
                        currencyArg = template.Parameters["Currency"].Argument;
                        saleRateArg = template.Parameters["SaleRate"].Argument;
                        purchaseRateArg = template.Parameters["PurchaseRate"].Argument;
                        totalAmountArg = template.Parameters["TotalAmount"].Argument;

                        transactionIdArg.SetValue(msgDebitNote.TransactionId.ToString());
                        dateArg.SetValue(msgDebitNote.ApprovalDateFormattedAsText);
                        customerNumberArg.SetValue(msgDebitNote.CustomerNumber);
                        accountNumberArg.SetValue(msgDebitNote.AccountNumber);
                        currencyCodeAsText = msgDebitNote.CurrencyCode.ToString();
                        currencyArg.SetValue(currencyCodeAsText);
                        saleRateArg.SetValue(msgDebitNote.SaleRate);
                        purchaseRateArg.SetValue(msgDebitNote.PurchaseRate);
                        totalAmountArg.SetValue(msgDebitNote.TotalAmount);

                        journalEntry = template.Calculate($"AD-{msgDebitNote.JournalEntryId}", msgDebitNote.ApprovalDate, "TransactionId", $"Debit Note {currencyCodeAsText}");
                        InsertJournalEntry(journalEntry, msgDebitNote.EmployeeName, baseMessage.Type, msgDebitNote.TransactionId);

                        break;
                    case TransactionType.Sale:
                        var msgSale = new JournalEntryForSaleMessage(msg);
                        template = SaleTransaction.Template;

                        transactionIdArg = template.Parameters["TransactionId"].Argument;
                        dateArg = template.Parameters["Date"].Argument;
                        currencyArg = template.Parameters["Currency"].Argument;
                        saleRateArg = template.Parameters["SaleRate"].Argument;
                        purchaseRateArg = template.Parameters["PurchaseRate"].Argument;
                        totalAmountArg = template.Parameters["TotalAmount"].Argument;

                        transactionIdArg.SetValue(msgSale.TransactionId.ToString());
                        dateArg.SetValue(msgSale.ApprovalDateFormattedAsText);
                        currencyCodeAsText = msgSale.CurrencyCode.ToString();
                        currencyArg.SetValue(currencyCodeAsText);
                        saleRateArg.SetValue(msgSale.SaleRate);
                        purchaseRateArg.SetValue(msgSale.PurchaseRate);
                        totalAmountArg.SetValue(msgSale.TotalAmount);

                        journalEntry = template.Calculate($"AD-{msgSale.JournalEntryId}", msgSale.ApprovalDate, "TransactionId", $"Sale {currencyCodeAsText}");
                        InsertJournalEntry(journalEntry, msgSale.EmployeeName, baseMessage.Type, msgSale.TransactionId);

                        break;
                    default:
                        throw new GameEngineException($"There is no implementation to update {nameof(baseMessage.Type)} {baseMessage.Type}");
                }
            }

            void InsertJournalEntry(JournalEntry journalEntry, string employeeName, TransactionType transactionType, int transactionId)
            {
                var user = Integration.ExchangeUsers.User(employeeName);
                var entry = new Journalentry()
                {
                    Date = journalEntry.Date,
                    Title = journalEntry.Title,
                    Reference = journalEntry.Reference,
                    Whocreated = user.Id,
                    Systemid = JournalEntry.SYSTEM_ID_EXCHANGE,
                    Journalentrydetailid = journalEntry.Id
                };

                var details = new List<Journalentrydetails>();
                foreach (var row in journalEntry.Rows)
                {
                    var detail = new Journalentrydetails()
                    {
                        Account = row.AccountNumber,
                        Description = row.Description,
                        Sequence = row.Sequence,
                        Journalentry = entry
                    };

                    if (row.IsCredit)
                    {
                        detail.Credit = row.Credit;
                    }
                    else
                    {
                        detail.Debit = row.Debit;
                    }
                    details.Add(detail);
                }
                Logic.JournalEntries.SaveWithDetails(entry, details, transactionType, transactionId);
            }

            void InsertCurrency(CoinMessage msg)
            {
                var currency = new ExchangeEntities.Currency()
                {
                    Id = msg.Coin.Id,
                    Isocode = msg.Coin.Iso4217Code,
                    Sign = msg.Coin.Sign,
                    Decimalprecision = (sbyte)msg.Coin.DecimalPrecision,
                    Unicode = msg.Coin.UnicodeAsText
                };
                ExchangeAPI.Logic.Currencies.Save(currency);
            }
        }

        private class FragmentPaymentsCallbackConsumer : GamesEngine.Settings.Consumer
        {
            public FragmentPaymentsCallbackConsumer(string group, string topic) : base(group, topic)
            {
            }

            public override void OnMessageBeforeCommit(string msg)
            {
                if (String.IsNullOrEmpty(msg)) throw new Exception(nameof(msg));

                CashierResponseType messageType = new CashierResponse(msg).ResponseType;

                if (messageType == CashierResponse.CashierResponseType.PAY_FRAGMENT_WITH_PROBLEMS)
                {
                    FragmentPaymentsWithProblemsResponse payFragmentsWithProblemsResponse = new FragmentPaymentsWithProblemsResponse(msg);

                    if (payFragmentsWithProblemsResponse.HasItems())
                    {
                        
                    }
                }

            }
        }

        public class CashierResponse : KafkaMessage
        {
            public enum CashierResponseType { LOCK_BALANCE, PAY_FRAGMENT_WITH_PROBLEMS }
            private readonly CashierResponseType type;

            internal CashierResponse(CashierResponseType type)
            {
                this.ResponseType = type;
            }

            public CashierResponse(string msg) : base(msg)
			{

			}

            public CashierResponseType ResponseType { get; protected set; }

            protected override void Deserialize(string[] serializedMessage, out int fieldOrder)
            {
                base.Deserialize(serializedMessage, out fieldOrder);
                ResponseType = (CashierResponseType)int.Parse(serializedMessage[fieldOrder++]);
            }

            protected override void InternalSerialize()
            {
                AddProperty((int)ResponseType);
            }
        }

        [DataContract(Name = "CustomerInformation")]
        class CustomerInformation
        {
            [DataMember(Name = "currencyOfTheAccount")]
            public string CurrencyOfTheAccount { get; set; }
        }

        private class ExternalOperationsConsumer : Consumer
		{
			public ExternalOperationsConsumer(string group, string topic) : base(group, topic)
			{
			}

			public override void OnMessageBeforeCommit(string msg)
			{
				IncomingDepositMessage message = new IncomingDepositMessage(msg);
				try
				{
					string command = $@"
					{{
						customer = company.CustomerByIdentifier('{message.CustomerId}');
						account = customer.FindAccount('{message.CustomerTargetAccount}');
						print account.CurrencyCodeAsText currencyOfTheAccount;
					}}";
					var result = ExchangeManagerAPI.ExchangeManager.PerformQry(command);
					if (!(result is OkObjectResult))
					{
						var error = ((ObjectResult)result).Value;
						if (error is ContentResult) throw new GameEngineException(((ContentResult)error).Content.ToString());
						throw new GameEngineException(((ContentResult)((ObjectResult)result).Value).Content.ToString());
					}

					OkObjectResult o = (OkObjectResult)result;
					string json = o.Value.ToString();
					CustomerInformation customerInformation = JsonConvert.DeserializeObject<CustomerInformation>(json);

					string customerId = message.CustomerId;
					string customerTargetAccount = message.CustomerTargetAccount;
					string trackingCode = message.TrackingCode;
					string publicKey = message.PublicKey;

					StringBuilder builder = new StringBuilder();
					StringBuilder errors = new StringBuilder();
					foreach (UnsentTransaction unsentTransactions in message.UnsentTransactions.List())
					{
						if (unsentTransactions.ItsFirstTimeProcessedFromNode)
						{
							if (unsentTransactions.CurrencyItsDistinctThan(customerInformation.CurrencyOfTheAccount))
							{
								Exchangerate rate = Integration.ExchangeRates.CurrentExchangeRateFor(
									unsentTransactions.Money.CurrencyCodeAsText,
									customerInformation.CurrencyOfTheAccount,
									message.UtcCreationDate);

								if (rate == null) throw new GameEngineException($"There is no rate for {customerInformation.CurrencyOfTheAccount}/{unsentTransactions.Money.CurrencyCodeAsText} on {message.UtcCreationDate}.");

								builder.Append($@"
								Eval('transactionNumber =' + marketplace.NewTransationNumber() + ';');
								rate = marketplace.CreateANewConversionSpread(Now, {rate.Fromcurrencyid}, {rate.Tocurrencyid}, {rate.Saleprice}, {rate.Purchaseprice});
								transaction = marketplace.From(transactionNumber, account, batch).Deposit(Now, itIsThePresent, rate, Currency('{unsentTransactions.Money.CurrencyCode}',{unsentTransactions.Money.Value}), 'N/A');
								transaction.SaveTrackingInformation('{message.TrackingCode}', '{unsentTransactions.ReferenceNumber}');
							");
							}
							else
							{
								builder.Append($@"
								Eval('transactionNumber =' + marketplace.NewTransationNumber() + ';');
								transaction = marketplace.From(transactionNumber, account, batch).Deposit(Now, itIsThePresent, Currency('{unsentTransactions.Money.CurrencyCode}',{unsentTransactions.Money.Value}), 'N/A');
								transaction.SaveTrackingInformation('{message.TrackingCode}', '{unsentTransactions.ReferenceNumber}');
							");
							}

                            if (unsentTransactions.Status == UnsentTransactionStatus.APPROVED) 
                            { 
                                builder.Append($@"
                                    Eval('journalEntryNumber =' + marketplace.NewJournalEntryNumber() + ';');
                                    transaction.Approve(Now, itIsThePresent, 'N/A', journalEntryNumber);
                                "); 
                            }
						}
						else if (unsentTransactions.Status == UnsentTransactionStatus.APPROVED)
						{
							builder.Append($@"
								transaction = batch.SearchTransactionByTrackinginformation('{message.TrackingCode}', '{unsentTransactions.ReferenceNumber}');
                                Eval('journalEntryNumber =' + marketplace.NewJournalEntryNumber() + ';');
								transaction.Approve(Now, itIsThePresent, 'N/A', journalEntryNumber);
							");
						}
						else
						{
							errors.AppendLine($"{msg}");
						}
					}

					if (errors.Length >0)
					{
						ErrorsSender.Send($@"errors: {errors.ToString()} /n msg:{msg}",
													$"Error placing the transaction {trackingCode}");
					}

					command = $@"
						{{
							batch = marketplace.SearchAgentBatch('CR/blocks/blocks');
							customer = company.CustomerByIdentifier('{customerId}');
							account = customer.FindAccount('{customerTargetAccount}');
							{builder.ToString()}
						}}
					";
					result = ExchangeManagerAPI.ExchangeManager.PerformCmd(command);
					if (!(result is OkObjectResult))
					{
						ErrorsSender.Send($@"Error:{((ObjectResult)result).Value.ToString()} /ln Details:{builder.ToString()} /n msg:{msg}",
							$"Error placing the transaction {trackingCode}");
					}
				}
				catch (Exception e)
				{
					ErrorsSender.Send($"Error placing the transaction {message.TrackingCode}", e, $@"msg:{msg}");
				}
			}
		}
        private class BatchBalancesConsumer : Consumer
        {
            public BatchBalancesConsumer(string group, string topic) : base(group, topic)
            {
            }

            public override void OnMessageBeforeCommit(string msg)
            {
                BalanceMessage message = new BalanceMessage(msg);
                foreach (BatchBalancesMessage account in message.Details)
                {
                    Storage.SaveVerifiedBatches(account,message.Date,message.Type);
                }
            }
        }

        private class VoucherUrlConsumer : Consumer
        {
            public VoucherUrlConsumer(string group, string topic) : base(group, topic)
            {
            }

            public override void OnMessageBeforeCommit(string msg)
            {
                if (String.IsNullOrEmpty(msg)) throw new ArgumentNullException(nameof(msg));

                var message = new VoucherUrlMessage(msg);
                switch (message.Type)
                {
                    case TransactionType.Deposit:
                        Logic.Deposits.UpdateVoucherUrl(message.Id, message.Url);
                        break;
                    case TransactionType.Withdrawal:
                        Logic.Withdrawals.UpdateVoucherUrl(message.Id, message.Url);
                        break;
                    case TransactionType.Transfer:
                        Logic.Transfers.UpdateVoucherUrl(message.Id, message.Url);
                        break;
                    case TransactionType.CreditNote:
                        Logic.CreditNotes.UpdateVoucherUrl(message.Id, message.Url);
                        break;
                    case TransactionType.DebitNote:
                        Logic.DebitNotes.UpdateVoucherUrl(message.Id, message.Url);
                        break;
                    default:
                        throw new GameEngineException($"There is no implementation to update {nameof(message.Type)} {message.Type}");
                }
            }
        }

        class ExternalApprovalForWithdrawalsConsumer : Consumer
        {
            public ExternalApprovalForWithdrawalsConsumer(string group, string topic) : base(group, topic)
            {
            }

            public override void OnMessageBeforeCommit(string msg)
            {
                if (String.IsNullOrEmpty(msg)) throw new ArgumentNullException(nameof(msg));

                var message = new ApprovalForWithdrawalsMessage(msg);
                
                var result = ExchangeManagerAPI.ExchangeManager.PerformCmd($@"
                    {{                               
                        transaction = marketplace.FindDraftTransaction({message.TransactionId}, '{message.AgentPath}', '{message.EmployeeName}');
						Eval('journalEntryNumber =' + marketplace.NewJournalEntryNumber() + ';');
						transactionCompleted = transaction.Approve(Now, itIsThePresent, '{message.EmployeeName}', journalEntryNumber);
                    }}"
                );
                if (!(result is OkObjectResult))
                {
                    if (result is ContentResult contentResult) throw new GameEngineException($"Error: {contentResult.Content}");
                    if (result is ObjectResult objResult) throw new GameEngineException($"Error: {objResult.Value}");
                    throw new GameEngineException($@"Error:{result}");
                }
            }
        }
    }

    public class VoucherUrlMessage : KafkaMessage
    {
        public TransactionType Type { get; set; }
        public int Id { get; set; }
        public string Url { get; set; }

        internal VoucherUrlMessage(TransactionType type, int id, string url)
        {
            Type = type;
            Id = id;
            Url = url;
        }

        public VoucherUrlMessage(string message) : base(message)
        {
        }

        protected override void Deserialize(string [] serializedMessage, out int fieldOrder)
        {
            base.Deserialize(serializedMessage, out fieldOrder);
            Type = (TransactionType)int.Parse(serializedMessage[fieldOrder++]);
            Id = int.Parse(serializedMessage[fieldOrder++]);
            Url = serializedMessage[fieldOrder++];
        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty((int)Type).
            AddProperty(Id).
            AddProperty(Url);
        }
    }

    internal sealed class IncomingDepositMessage : KafkaMessage
    {
        public DateTime UtcCreationDate { get; private set; }
        public UnsentTransactions UnsentTransactions { get; private set; }
        public string CustomerId { get; private set; }
        public string CustomerTargetAccount { get; private set; }
        public string TrackingCode { get; private set; }
        public string PublicKey { get;}
        public string PublicAddress { get; private set; }

        internal IncomingDepositMessage(string trackingCode, string publicAddress, string customerId, string customerTargetAccount, UnsentTransactions unsentTransactions)
        {
            UnsentTransactions = unsentTransactions;
            TrackingCode = trackingCode;
            PublicAddress = publicAddress;
            CustomerId = customerId;
            CustomerTargetAccount = customerTargetAccount;
        }

        public IncomingDepositMessage(string msg):base(msg)
        {
        }

        protected override void Deserialize(string [] properties, out int fieldOrder)
        {
            base.Deserialize(properties, out fieldOrder);
            TrackingCode = properties[fieldOrder++];
            PublicAddress = properties[fieldOrder++];
            CustomerId = properties[fieldOrder++];
            CustomerTargetAccount = properties[fieldOrder++];
            int year = int.Parse(properties[fieldOrder++]);
            int month = int.Parse(properties[fieldOrder++]);
            int day = int.Parse(properties[fieldOrder++]);
            int hour = int.Parse(properties[fieldOrder++]);
            int minute = int.Parse(properties[fieldOrder++]);
            int second = int.Parse(properties[fieldOrder++]);
            UtcCreationDate = new DateTime(year, month, day, hour, minute, second);

            UnsentTransactions = new UnsentTransactions();
            for ( int i =10; i < properties.Length;)
            {
                string referenceNumber = properties[i++];
                year = int.Parse(properties[i++]);
                month = int.Parse(properties[i++]);
                day = int.Parse(properties[i++]);
                hour = int.Parse(properties[i++]);
                minute = int.Parse(properties[i++]);
                second = int.Parse(properties[i++]);

                DateTime unsentTransactionsUTCCreationDate = new DateTime(year, month, day, hour, minute, second);

                decimal amount = decimal.Parse(properties[i++]);

                string transactionId = properties[i++];
                var amountInCurrency = GamesEngine.Finance.Currency.Factory(properties[i++], amount);
                
                int confirmations = int.Parse(properties[i++]);
                UnsentTransactionStatus status = (UnsentTransactionStatus)int.Parse(properties[i++]);

                bool itsFirstTimeProcessedFromNode = bool.Parse(properties[i++]);
                bool statusHasChanged = bool.Parse(properties[i++]);

                UnsentTransactions.Add(new UnsentTransaction(referenceNumber, unsentTransactionsUTCCreationDate, amountInCurrency, transactionId, confirmations, status, itsFirstTimeProcessedFromNode, statusHasChanged));
            }
        }

        protected override void InternalSerialize()
        {
            throw new GameEngineException($"Not implemented {nameof(InternalSerialize)}");
        }
    }

    internal class UnsentTransaction
    {
        internal enum UnsentTransactionStatus { DRAFT = 0, APPROVED = 1 }
        internal UnsentTransaction(string referenceNumber, DateTime unsentTransactionsUTCCreationDate, GamesEngine.Finance.Currency amount, string transactionId, int confirmations, UnsentTransactionStatus status, bool itsFirstTimeProcessedFromNode, bool statusHasChanged)
        {
            ReferenceNumber = referenceNumber;
            UtcCreationDate = unsentTransactionsUTCCreationDate;
            Money = amount;
            TransactionId = transactionId;
            Confirmations = confirmations;
            Status = status;
            ItsFirstTimeProcessedFromNode = itsFirstTimeProcessedFromNode;
            StatusHasChanged = statusHasChanged;
        }

        internal string ReferenceNumber { get; }
        internal DateTime UtcCreationDate { get; }
        internal GamesEngine.Finance.Currency Money { get; }
        internal string TransactionId { get; }
        internal int Confirmations { get; }
        internal UnsentTransactionStatus Status { get; }
        internal bool ItsFirstTimeProcessedFromNode { get; }
        internal bool StatusHasChanged { get; }

        internal bool CurrencyItsDistinctThan(string currencyOfTheAccount)
        {
            return Money.CurrencyCodeAsText != currencyOfTheAccount;
        }
    }

    internal class UnsentTransactions
    {
        private List<UnsentTransaction> unsentTransactions = new List<UnsentTransaction>();
        internal void Add(UnsentTransaction unsentTransaction)
        {
            unsentTransactions.Add(unsentTransaction);
        }

        internal IEnumerable<UnsentTransaction> List()
        {
            return unsentTransactions.ToArray();
        }

        internal bool HasTransactions()
        {
            return (unsentTransactions.Count > 0);
        }
    }
}
