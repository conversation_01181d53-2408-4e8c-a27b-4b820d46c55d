﻿using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
[assembly: InternalsVisibleTo("ExchangeAPI")]
[assembly: InternalsVisibleTo("ExchangeMocks")]

namespace GamesEngine.Accounting.JournalTemplates
{
	[Puppet]
	internal class JournalEntryTemplate:Objeto
	{
		private readonly int id;
		private readonly string name;
		private readonly List<JournalEntryLineTemplate> lines = new List<JournalEntryLineTemplate>();
		internal IEnumerable<JournalEntryLineTemplate> Lines
		{
			get
			{
				return lines;
			}
		}
		private readonly Parameters parameters = new Parameters();
		private bool isActive = true;

		internal JournalEntryTemplate(int id, string name)
		{
			if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
			if (id <= 0) throw new GameEngineException($"Id {nameof(id)} must be greater than 0");

			this.id = id;
			this.name = name;
		}

		internal bool IsActive
        {
            get
            {
				return isActive;
            }
			set
            {
				isActive = value;
            }
        }

		internal void AddDebit(string account, string description, string amountParam)
		{
			if (string.IsNullOrWhiteSpace(account)) throw new ArgumentNullException(nameof(account));
			if (string.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));
			if (string.IsNullOrWhiteSpace(amountParam)) throw new ArgumentNullException(nameof(amountParam));

			Parameter amount = this.parameters[amountParam];
			if (!(amount is AmountParameter)) throw new GameEngineException($"Parameter '{amountParam}' must be a Account's type parameter but it is an '{amount.GetType().Name}'.");

			int lineNumber = lines.Count + 1;
			JournalEntryLineTemplate newLine = JournalEntryLineTemplate.Debit(this, lineNumber, account, description, (AmountParameter) amount);
			this.lines.Add(newLine);
		}

		internal void AddCredit(string account, string description, string amountParam)
		{
			if (string.IsNullOrWhiteSpace(account)) throw new ArgumentNullException(nameof(account));
			if (string.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));
			if (string.IsNullOrWhiteSpace(amountParam)) throw new ArgumentNullException(nameof(amountParam));

			Parameter amount = this.parameters[amountParam];
			if (!(amount is AmountParameter)) throw new GameEngineException($"Parameter '{amountParam}' must be a Account's type parameter but it is an '{amount.GetType().Name}'.");

			int lineNumber = lines.Count + 1;
			JournalEntryLineTemplate newLine = JournalEntryLineTemplate.Credit(this, lineNumber, account, description, (AmountParameter) amount);
			this.lines.Add(newLine);
		}

		internal void CopyLinesFrom(JournalEntryTemplate template)
        {
			if (template == null) throw new ArgumentNullException(nameof(template));

			foreach (var line in template.Lines)
            {
				if (line.IsCredit)
                {
					AddCredit(line.AccountNumber, line.Description, line.ParameterName);
				}
				else if (line.IsDebit)
				{
					AddDebit(line.AccountNumber, line.Description, line.ParameterName);
				}
			}
        }

		internal Parameters Parameters
		{
			get
			{
				return this.parameters;
			}
		}

		internal int Id
        {
			get
            {
				return this.id;
            }
        }

		internal JournalEntry Calculate(string journalEntryId, DateTime date, string reference, string title)
		{
			if (!isActive) throw new GameEngineException("Templace '{name}' is not active. Change to active to generate Journal Entries.");
			if (string.IsNullOrWhiteSpace(journalEntryId)) throw new ArgumentNullException(nameof(journalEntryId));
			if (date == default(DateTime)) throw new ArgumentNullException(nameof(date));
			if (string.IsNullOrWhiteSpace(reference)) throw new ArgumentNullException(nameof(reference));
			if (string.IsNullOrWhiteSpace(title)) throw new ArgumentNullException(nameof(title));

			if (lines.Count < 2) throw new GameEngineException($"Template has {lines.Count} entries. You can not create a balanced journal entry with this movements.");

			Parameter referenceParam = this.parameters[reference];
			if (!(referenceParam is TextParameter)) throw new GameEngineException($"Parameter '{reference}' must be a Text's type parameter but it is an '{reference.GetType().Name}'.");

			AccountingMovement[] journalRows = GenerateJournalRows();
			JournalEntry result = new JournalEntry(journalEntryId, date, referenceParam.Argument.ValueAsString, title, journalRows);
			//3. quien lo genera... aux contable
			return result;
		}

		private AccountingMovement[] GenerateJournalRows()
		{
			AccountingMovement[] result = new AccountingMovement[this.lines.Count];
			foreach (JournalEntryLineTemplate line in lines)
			{
				var accountingMovement = line.GenerateJournalRow();
				result[line.LineNumber-1] = accountingMovement;
			}
			return result;
		}

		internal void ClearLines()
        {
			lines.Clear();
		}
	}
}
