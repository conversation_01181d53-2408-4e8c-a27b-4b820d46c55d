﻿using GamesEngine.Business;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using System;
using System.Collections.Generic;
using System.Text;
using town.connectors.drivers;
using static GamesEngine.Custodian.AccumulatesByCurrency;

namespace GamesEngine.Custodian
{
	internal class AccumulastesByDate
	{
		private readonly Dictionary<long, AccumulatesByCurrency> accumulastesByDate = new Dictionary<long, AccumulatesByCurrency>();
		internal bool TryGetValue(DateTime scheduledDate, out AccumulatesByCurrency amountStored)
		{
			amountStored = null;
			if (accumulastesByDate == null) return false;

			return accumulastesByDate.TryGetValue(scheduledDate.Date.Ticks, out amountStored);
		}

		internal void UpdateAccumulate(DateTime scheduledDate, Currency amount, string processorId, TransactionType transactionType)
		{
			AccumulatesByCurrency amountStored;
			bool found = accumulastesByDate.TryGetValue(scheduledDate.Date.Ticks, out amountStored);

			if (found)
			{
				amountStored.Add(scheduledDate, amount, processorId, transactionType);
			}
			else
			{
				accumulastesByDate.Add(scheduledDate.Date.Ticks, new AccumulatesByCurrency(scheduledDate, amount, processorId, transactionType));
			}
		}

		internal IEnumerable<AccumulatesByCurrency> List()
		{
			return accumulastesByDate.Values;
		}

		internal void Iterate(DateTime initialDateToSearch, DateTime finalDateToSearch, Func<AccumulatesByCurrency, bool> p)
		{
			if (initialDateToSearch > finalDateToSearch) throw new GameEngineException($"{nameof(initialDateToSearch)} can not be bigger than {nameof(initialDateToSearch)}");
			if (p == null) return;

			long initialIndexToSearch = initialDateToSearch.Date.Ticks;
			long finalIndexToSearch = finalDateToSearch.Date.Ticks;

			foreach (KeyValuePair<long, AccumulatesByCurrency> item in accumulastesByDate)
			{
				if (item.Key >= initialIndexToSearch && finalIndexToSearch >= item.Key)
				{
					bool stopTheIteration = p.Invoke(item.Value);
					if (stopTheIteration) return;
				}
			}
		}
	}

	internal class Accumulates
	{
		private List<Accumulate> accumulates = new List<Accumulate>();

		internal void Add(Accumulate accumulate)
		{
			accumulates.Add(accumulate);
		}

		internal IEnumerable<Accumulate> List()
		{
			return accumulates;
		}
	}

	internal class AccumulatesByCurrency
	{
		private Dictionary<Coin, Accumulate> accumulates;

		internal DateTime ScheduledDate { get; }

		internal AccumulatesByCurrency(DateTime scheduledDate, Currency value, string processorId, TransactionType transactionType)
		{
			accumulates = new Dictionary<Coin, Accumulate>();
			accumulates.Add(value.Coin, new Accumulate(scheduledDate, value, processorId, transactionType));
			ScheduledDate = scheduledDate;
		}

		internal void IterateAccumulate(Func<Accumulate, bool> p)
		{
			if (p == null) return;

			foreach (Accumulate accumulate in accumulates.Values)
			{
				bool stopTheIteration = p.Invoke(accumulate);
				if (stopTheIteration) return;
			}
		}

		internal AccumulatesByCurrency()
		{
			accumulates = new Dictionary<Coin, Accumulate>();
		}

		internal bool Amount(Coin coin, out Accumulate result)
		{
			return accumulates.TryGetValue(coin, out result);
		}

		public override String ToString()
		{
			return string.Join(", ", accumulates.Values);
		}

		internal void Add(DateTime scheduledDate, Currency amount, string processorId, TransactionType transactionType)
		{
			Accumulate result;
			bool found = accumulates.TryGetValue(amount.Coin, out result);
			if (!found)
			{
				result = new Accumulate(scheduledDate, amount, processorId, transactionType);
			}

			bool itsNegative = amount.Value < 0;
			bool resultItsNegative = result.Amount.Value - amount.Value < 0;
			if (itsNegative && resultItsNegative)
			{
				throw new GameEngineException($"{nameof(AccumulatesByCurrency)} can not be negative.");
			} 

			if (!found)
			{
				accumulates.Add(amount.Coin, result);
			}
			else
			{
				result.Update(scheduledDate, amount, processorId);
			}

			if (result.Amount.Value == 0) accumulates.Remove(amount.Coin);
		}

		//}
		//internal IEnumerable<Accumulate> List()
		//{
		//	return accumulates.Values;
		//}

	}

	internal class Accumulate
	{
		internal string CurrencyCode { get { return Amount.CurrencyCode; } }
		internal Coin Coin { get { return Amount.Coin; } }

		internal bool ItsIn(Coin coin)
		{
			return CurrencyCode == coin.Iso4217Code;
		}

		internal DateTime ScheduledDate { get; private set; }
		internal Currency Amount { get;}
		private List<string> processorIds;
		public IEnumerable<string> ProccessorsId { get { return processorIds; } }

		internal decimal Value { get { return Amount.Value; } }

		public TransactionType TransactionType { get; }

		internal Accumulate(DateTime scheduledDate, Currency amount, string proccessorId, TransactionType transactionType)
		{
			ScheduledDate = scheduledDate;
			Amount = amount;
			this.processorIds = new List<string>();
			this.processorIds.Add(proccessorId);
			TransactionType = transactionType;
		}

		internal bool ContainsProcessor(string processorId)
		{
			return this.processorIds.Contains(processorId);
		}

		internal void Update(DateTime scheduledDate, Currency amount, string processorId)
		{
			ScheduledDate = scheduledDate;
			Amount.Add(amount);
			if (amount.Value >= 0)
			{
				this.processorIds.Add(processorId);
			}
			else
			{
				this.processorIds.Remove(processorId);
			}
		}
	}
}
