﻿using GamesEngine;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Threading.Tasks;

namespace LottoAPI.Controllers
{
    public class APIController : AuthorizeController
    {
        [HttpGet("api/favorites")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> FavoritesListsAsync()
        {
            string playerId = Validator.StringEscape(Security.PlayerId(User));
            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
				{{
					player = company.CustomerByPlayerId('{playerId}').Player;
                    favoritesCatalog = player.FavoritesCatalog();
                    for (favoritesLists : favoritesCatalog.GetAll)
                    {{
                        favoriteNumbersList = favoritesLists;
                        print favoriteNumbersList.Name name;
                        print favoriteNumbersList.IconPath iconPath;
                        for (favoriteNumbers : favoriteNumbersList.GetAll)
                        {{
                            print favoriteNumbers.Enabled enabled;
                            print favoriteNumbers.AsString() number;
                            print favoriteNumbers.TicketTypeAsString() ticketType;
                        }}
                    }}
                }}
			");

            return result;
        }

        [HttpGet("api/favorites/enabled")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> FavoritesListsWithEnabledNumbersAsync()
        {
            string playerId = Validator.StringEscape(Security.PlayerId(User));
            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
				{{
					player = company.CustomerByPlayerId('{playerId}').Player;
                    favoritesCatalog = player.FavoritesCatalog();
                    for (favoritesLists : favoritesCatalog.GetAll)
                    {{
                        favoriteNumbersList = favoritesLists;
                        print favoriteNumbersList.Name name;
                        print favoriteNumbersList.IconPath iconPath;
                        for (favoriteNumbers : favoriteNumbersList.EnabledNumbers)
                        {{
                            print favoriteNumbers.AsString() number;
                            print favoriteNumbers.TicketTypeAsString() ticketType;
                        }}
                    }}
                }}
			");

            return result;
        }

        [HttpGet("api/favorites/{name}")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> FavoriteNumbersAsync(string name)
        {
            if (string.IsNullOrWhiteSpace(name)) return BadRequest($"{nameof(name)} is required");

            var nameEscaped = Validator.StringEscape(name);
            string playerId = Validator.StringEscape(Security.PlayerId(User));
            var result = await LottoAPI.Actor.PerformChkThenQryAsync(HttpContext, $@"
                {{
                    player = company.CustomerByPlayerId('{playerId}').Player;
                    favoritesCatalog = player.FavoritesCatalog();
                    Check(favoritesCatalog.ExistsFavoriteNumbers('{nameEscaped}')) Error 'Favorite {nameEscaped} does not exist';
                }}", $@"
				{{
					player = company.CustomerByPlayerId('{playerId}').Player;
                    favoritesCatalog = player.FavoritesCatalog();
                    favoriteNumbersList = favoritesCatalog.GetFavoriteNumbers('{nameEscaped}');
                    for (favoriteNumbers : favoriteNumbersList.GetAll)
                    {{
                        print favoriteNumbers.AsString() number;
                        print favoriteNumbers.TicketTypeAsString() ticketType;
                    }}
                }}
			");

            return result;
        }

        [HttpGet("api/favorites/recents/numbers/pick/{pickNumber:int:min(2):max(5)}")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> RecentsNumbersByPickAsync(int pickNumber)
        {
            string playerId = Validator.StringEscape(Security.PlayerId(User));
            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
				{{
					player = company.CustomerByPlayerId('{playerId}').Player;
                    favoritesCatalog = player.FavoritesCatalog();
                    favoriteNumbersList = favoritesCatalog.GetFavoriteNumbers('Recents');
                    for (recents : favoriteNumbersList.DistinctNumbersByPick({pickNumber}))
                    {{
                        print recents.AsString() number;
                        print recents.Date date;
                    }}
                }}
			");

            return result;
        }

        [HttpGet("api/favorites/recents/lastNumbers/pick/{pickNumber:int:min(2):max(5)}")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> LastNumbersByPickAsync(int pickNumber)
        {
            string playerId = Validator.StringEscape(Security.PlayerId(User));
            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
				{{
					player = company.CustomerByPlayerId('{playerId}').Player;
                    favoritesCatalog = player.FavoritesCatalog();
                    favoriteNumbersList = favoritesCatalog.GetFavoriteNumbers('Recents');
                    for (recents : favoriteNumbersList.LastNumbersByPick({pickNumber}))
                    {{
                        print recents.AsString() number;
                        print recents.Date date;
                    }}
                }}
			");

            return result;
        }

        [HttpPost("api/favorite")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> CreateFavoriteAsync([FromBody] FavoriteBody body)
        {
            if (body == null) return BadRequest($"{nameof(body)} is required");
            if (string.IsNullOrWhiteSpace(body.Name)) return BadRequest($"{nameof(body.Name)} is required");
            if (string.IsNullOrWhiteSpace(body.IconPath)) return BadRequest($"{nameof(body.IconPath)} is required");

            var nameEscaped = Validator.StringEscape(body.Name);
            string playerId = Validator.StringEscape(Security.PlayerId(User));
            var result = await LottoAPI.Actor.PerformChkThenCmdAsync(HttpContext, $@"
                {{
                    player = company.CustomerByPlayerId('{playerId}').Player;
                    favoritesCatalog = player.FavoritesCatalog();
                    Check(!favoritesCatalog.ExistsFavoriteNumbers('{nameEscaped}')) Error 'Favorite {nameEscaped} already exists';
                }}", $@"
				{{
					player = company.CustomerByPlayerId('{playerId}').Player;
                    favoritesCatalog = player.FavoritesCatalog();
                    favoritesCatalog.CreateFavoriteNumbers('{nameEscaped}','{body.IconPath}');
                }}
			");

            return result;
        }

        const string RecentsFavoriteName = "Recents";
        [HttpPut("api/favorites/{name}/rename")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> UpdateFavoriteNumbersNameAsync(string name, string newName)
        {
            if (string.IsNullOrWhiteSpace(name)) return BadRequest($"{nameof(name)} is required");
            if (string.IsNullOrWhiteSpace(newName)) return BadRequest($"{nameof(newName)} is required");

            var nameEscaped = Validator.StringEscape(name);
            var newNameEscaped = Validator.StringEscape(newName);
            string playerId = Validator.StringEscape(Security.PlayerId(User));
            var result = await LottoAPI.Actor.PerformChkThenCmdAsync(HttpContext, $@"
                {{
                    player = company.CustomerByPlayerId('{playerId}').Player;
                    favoritesCatalog = player.FavoritesCatalog();
                    Check({!nameEscaped.Equals(RecentsFavoriteName, StringComparison.OrdinalIgnoreCase)}) Error '{RecentsFavoriteName} cannot change name';
                    Check(favoritesCatalog.ExistsFavoriteNumbers('{nameEscaped}')) Error 'Favorite {nameEscaped} does not exist';
                    Check(!favoritesCatalog.ExistsFavoriteNumbers('{newNameEscaped}')) Error 'Favorite {newNameEscaped} already exists';
                }}", $@"
				{{
					player = company.CustomerByPlayerId('{playerId}').Player;
                    favoritesCatalog = player.FavoritesCatalog();
                    favoriteNumbersList = favoritesCatalog.GetFavoriteNumbers('{nameEscaped}');
                    favoriteNumbersList.Name = '{newNameEscaped}';
                }}
			");

            return result;
        }

        [HttpPut("api/favorites/{name}/icon")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> UpdateFavoriteNumbersIconAsync(string name, string newIcon)
        {
            if (string.IsNullOrWhiteSpace(name)) return BadRequest($"{nameof(name)} is required");
            if (string.IsNullOrWhiteSpace(newIcon)) return BadRequest($"{nameof(newIcon)} is required");

            var nameEscaped = Validator.StringEscape(name);
            string playerId = Validator.StringEscape(Security.PlayerId(User));
            var result = await LottoAPI.Actor.PerformChkThenCmdAsync(HttpContext, $@"
                {{
                    player = company.CustomerByPlayerId('{playerId}').Player;
                    favoritesCatalog = player.FavoritesCatalog();
                    Check({!nameEscaped.Equals(RecentsFavoriteName, StringComparison.OrdinalIgnoreCase)}) Error '{RecentsFavoriteName} cannot change icon';
                    Check(favoritesCatalog.ExistsFavoriteNumbers('{nameEscaped}')) Error 'Favorite {nameEscaped} does not exist';
                }}", $@"
				{{
					player = company.CustomerByPlayerId('{playerId}').Player;
                    favoritesCatalog = player.FavoritesCatalog();
                    favoriteNumbersList = favoritesCatalog.GetFavoriteNumbers('{nameEscaped}');
                    favoriteNumbersList.IconPath = '{newIcon}';
                }}
			");

            return result;
        }

        [HttpDelete("api/favorites/{name}")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> RemoveFavoriteNumbersAsync(string name)
        {
            if (string.IsNullOrWhiteSpace(name)) return BadRequest($"{nameof(name)} is required");

            var nameEscaped = Validator.StringEscape(name);
            string playerId = Validator.StringEscape(Security.PlayerId(User));
            var result = await LottoAPI.Actor.PerformChkThenCmdAsync(HttpContext, $@"
                {{
                    player = company.CustomerByPlayerId('{playerId}').Player;
                    favoritesCatalog = player.FavoritesCatalog();
                    Check({!nameEscaped.Equals(RecentsFavoriteName, StringComparison.OrdinalIgnoreCase)}) Error '{RecentsFavoriteName} cannot be removed';
                    Check(favoritesCatalog.ExistsFavoriteNumbers('{nameEscaped}')) Error 'Favorite {nameEscaped} does not exist';
                }}", $@"
				{{
					player = company.CustomerByPlayerId('{playerId}').Player;
                    favoritesCatalog = player.FavoritesCatalog();
                    favoritesCatalog.RemoveFavoriteNumbers('{nameEscaped}');
                }}
			");

            return result;
        }

        [HttpPost("api/favorites/{name}/numbers")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> AddFavoriteNumbersAsync(string name, [FromBody] FavoriteNumbersBody body)
        {
            if (string.IsNullOrWhiteSpace(name)) return BadRequest($"{nameof(name)} is required");
            if (body == null) return BadRequest($"{nameof(body)} is required");
            if (body.Numbers == null || body.Numbers.Count == 0) return BadRequest($"{nameof(body.Numbers)} is required");
            if (body.TicketTypes == null || body.TicketTypes.Count == 0) return BadRequest($"{nameof(body.TicketTypes)} is required");
            if (body.Numbers.Count != body.TicketTypes.Count) return BadRequest("The number of ticket types and numbers should be the same");

            var nameEscaped = Validator.StringEscape(name);
            var strTicketTypes = string.Join(',', body.TicketTypes.Select(x => $"'{x}'"));
            var strNumbers = string.Join(',', body.Numbers.Select(x => $"'{x}'"));
            var commandToAddNumbers = nameEscaped.Equals(RecentsFavoriteName, StringComparison.OrdinalIgnoreCase) ? $"favoriteNumbers.Add({{{strTicketTypes}}}, {{{strNumbers}}}, Now);" : $"favoriteNumbers.Add({{{strTicketTypes}}}, {{{strNumbers}}});";
            string playerId = Validator.StringEscape(Security.PlayerId(User));
            var result = await LottoAPI.Actor.PerformChkThenCmdAsync(HttpContext, $@"
                {{
                    player = company.CustomerByPlayerId('{playerId}').Player;
                    favoritesCatalog = player.FavoritesCatalog();
                    Check(favoritesCatalog.ExistsFavoriteNumbers('{nameEscaped}')) Error 'Favorite {nameEscaped} does not exist';
                }}", $@"
                {{
                    player = company.CustomerByPlayerId('{playerId}').Player;
                    favoritesCatalog = player.FavoritesCatalog();
                    favoriteNumbers = favoritesCatalog.GetFavoriteNumbers('{nameEscaped}');
                    {commandToAddNumbers}
                }}
            ");
            return result;
        }

        [HttpDelete("api/favorites/{name}/numbers/{number}")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> RemoveFavoriteNumberAsync(string name, string number, string ticketType)
        {
            if (string.IsNullOrWhiteSpace(name)) return BadRequest($"{nameof(name)} is required");
            if (string.IsNullOrWhiteSpace(number)) return BadRequest($"{nameof(number)} is required");
            if (string.IsNullOrWhiteSpace(ticketType)) return BadRequest($"{nameof(ticketType)} is required");

            var nameEscaped = Validator.StringEscape(name);
            string playerId = Validator.StringEscape(Security.PlayerId(User));
            var result = await LottoAPI.Actor.PerformChkThenCmdAsync(HttpContext, $@"
                {{
                    player = company.CustomerByPlayerId('{playerId}').Player;
                    favoritesCatalog = player.FavoritesCatalog();
                    Check(favoritesCatalog.ExistsFavoriteNumbers('{nameEscaped}')) Error 'Favorite {nameEscaped} does not exist';
                }}", $@"
                {{
                    player = company.CustomerByPlayerId('{playerId}').Player;
                    favoritesCatalog = player.FavoritesCatalog();
                    favoriteNumbers = favoritesCatalog.GetFavoriteNumbers('{nameEscaped}');
                    favoriteNumbers.Remove({ticketType}, '{number}');
                }}
            ");
            return result;
        }

        [HttpPost("api/favorites/{name}/move")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> MoveFavoriteNumberAsync(string name, [FromBody] FavoriteNumberMoveBody body)
        {
            if (string.IsNullOrWhiteSpace(name)) return BadRequest($"{nameof(name)} is required");
            if (body == null) return BadRequest($"{nameof(body)} is required");
            if (string.IsNullOrWhiteSpace(body.DestinationFavoriteName)) return BadRequest($"{nameof(body.DestinationFavoriteName)} is required");
            if (string.IsNullOrWhiteSpace(body.TicketType)) return BadRequest($"{nameof(body.TicketType)} is required");
            if (string.IsNullOrWhiteSpace(body.Number)) return BadRequest($"{nameof(body.Number)} is required");

            var nameEscaped = Validator.StringEscape(name);
            var destinationFavoriteNameEscaped = Validator.StringEscape(body.DestinationFavoriteName);
            string playerId = Validator.StringEscape(Security.PlayerId(User));
            var result = await LottoAPI.Actor.PerformChkThenCmdAsync(HttpContext, $@"
                {{
                    player = company.CustomerByPlayerId('{playerId}').Player;
                    favoritesCatalog = player.FavoritesCatalog();
                    Check(favoritesCatalog.ExistsFavoriteNumbers('{nameEscaped}')) Error 'Favorite {nameEscaped} does not exist';
                }}", $@"
                {{
                    player = company.CustomerByPlayerId('{playerId}').Player;
                    favoritesCatalog = player.FavoritesCatalog();
                    favoritesCatalog.MoveFavoriteNumber('{nameEscaped}', '{destinationFavoriteNameEscaped}', {body.TicketType}, '{body.Number}');
                }}
            ");
            return result;
        }

        [HttpPost("api/favorites/{name}/numbers/activation")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> ActivateFavoriteNumberAsync(string name, [FromBody] FavoriteNumberActivationBody body)
        {
            if (string.IsNullOrWhiteSpace(name)) return BadRequest($"{nameof(name)} is required");
            if (body == null) return BadRequest($"{nameof(body)} is required");
            if (string.IsNullOrWhiteSpace(body.TicketType)) return BadRequest($"{nameof(body.TicketType)} is required");
            if (string.IsNullOrWhiteSpace(body.Number)) return BadRequest($"{nameof(body.Number)} is required");

            var nameEscaped = Validator.StringEscape(name);
            string playerId = Validator.StringEscape(Security.PlayerId(User));
            var result = await LottoAPI.Actor.PerformChkThenCmdAsync(HttpContext, $@"
                {{
                    player = company.CustomerByPlayerId('{playerId}').Player;
                    favoritesCatalog = player.FavoritesCatalog();
                    Check(favoritesCatalog.ExistsFavoriteNumbers('{nameEscaped}')) Error 'Favorite {nameEscaped} does not exist';
                }}", $@"
                {{
                    player = company.CustomerByPlayerId('{playerId}').Player;
                    favoritesCatalog = player.FavoritesCatalog();
                    favoriteNumbers = favoritesCatalog.GetFavoriteNumbers('{nameEscaped}');
                    favoriteNumbers.Activate({body.TicketType}, '{body.Number}');
                }}
            ");
            return result;
        }

        [HttpPost("api/favorites/{name}/numbers/deactivation")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> DeactivateFavoriteNumberAsync(string name, [FromBody] FavoriteNumberActivationBody body)
        {
            if (string.IsNullOrWhiteSpace(name)) return BadRequest($"{nameof(name)} is required");
            if (body == null) return BadRequest($"{nameof(body)} is required");
            if (string.IsNullOrWhiteSpace(body.TicketType)) return BadRequest($"{nameof(body.TicketType)} is required");
            if (string.IsNullOrWhiteSpace(body.Number)) return BadRequest($"{nameof(body.Number)} is required");

            var nameEscaped = Validator.StringEscape(name);
            string playerId = Validator.StringEscape(Security.PlayerId(User));
            var result = await LottoAPI.Actor.PerformChkThenCmdAsync(HttpContext, $@"
                {{
                    player = company.CustomerByPlayerId('{playerId}').Player;
                    favoritesCatalog = player.FavoritesCatalog();
                    Check(favoritesCatalog.ExistsFavoriteNumbers('{nameEscaped}')) Error 'Favorite {nameEscaped} does not exist';
                }}", $@"
                {{
                    player = company.CustomerByPlayerId('{playerId}').Player;
                    favoritesCatalog = player.FavoritesCatalog();
                    favoriteNumbers = favoritesCatalog.GetFavoriteNumbers('{nameEscaped}');
                    favoriteNumbers.Deactivate({body.TicketType}, '{body.Number}');
                }}
            ");
            return result;
        }

        [DataContract(Name = "FavoriteBody")]
        public class FavoriteBody
        {
            [DataMember(Name = "name")]
            public string Name { get; set; }
            [DataMember(Name = "iconPath")]
            public string IconPath { get; set; }
        }

        [DataContract(Name = "FavoriteNumbersBody")]
        public class FavoriteNumbersBody
        {
            [DataMember(Name = "ticketTypes")]
            public List<string> TicketTypes { get; set; }
            [DataMember(Name = "numbers")]
            public List<string> Numbers { get; set; }
        }

        [DataContract(Name = "FavoriteNumberActivationBody")]
        public class FavoriteNumberActivationBody
        {
            [DataMember(Name = "ticketType")]
            public string TicketType { get; set; }
            [DataMember(Name = "number")]
            public string Number { get; set; }
        }

        [DataContract(Name = "FavoriteNumberMoveBody")]
        public class FavoriteNumberMoveBody
        {
            [DataMember(Name = "destinationFavoriteName")]
            public string DestinationFavoriteName { get; set; }
            [DataMember(Name = "ticketType")]
            public string TicketType { get; set; }
            [DataMember(Name = "number")]
            public string Number { get; set; }
        }
    }
}
