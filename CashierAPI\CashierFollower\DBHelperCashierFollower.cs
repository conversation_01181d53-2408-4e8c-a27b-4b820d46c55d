﻿using MySql.Data.MySqlClient;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CashierAPI.CashierFollower
{
    internal class DBHelperCashierFollower
    {
        internal static DatabaseType DbType;
        internal static string ConnectionString;
        internal static string AccountName;
        private static Dictionary<string, MovementDetailsInfo> AuthorizationStatusDetails;

        internal static void InitStatusForAccount(string accountName)
        {
            AccountName = accountName;

            if (AuthorizationStatusDetails == null)
            {
                AuthorizationStatusDetails = new Dictionary<string, MovementDetailsInfo>();
                InitialBalanceHandler.SetupCurrencyQueryParts();
            }
            else
            {
                AuthorizationStatusDetails.Clear();
                InitialBalanceHandler.Clear();
            }
        }

        internal static void LoadDbConnection(DatabaseType dbType, string connectionString)
        {
            DbType = dbType;
            ConnectionString = connectionString;
        }

        internal static void AddNewAuthorization(string[] authorizationNumbers, int dairyIdForAuthorization, string currencyCode)
        {
            if (authorizationNumbers == null) return;
            if (dairyIdForAuthorization <= 0) return;
            
            foreach (var authorizationNumberWithSpaces in authorizationNumbers)
            {
                var authorizationNumber = authorizationNumberWithSpaces.Trim();
                if (string.IsNullOrEmpty(authorizationNumber)) throw new ArgumentNullException(nameof(authorizationNumber));
                if (!int.TryParse(authorizationNumber, out int authorizationNumberInt)) throw new ArgumentException(nameof(authorizationNumber));
                if (AuthorizationStatusDetails.ContainsKey(authorizationNumber)) return;
                MovementDetailsInfo movementDetails = new MovementDetailsInfo(dairyIdForAuthorization, authorizationNumber, currencyCode);
                AuthorizationStatusDetails.Add(authorizationNumber, movementDetails);
            }
        }

        internal static void UpdateAuthorizationInfoForFragmentCreation(string authorizationNumber, int dairyIdForAuthorization)
        {
            UpdateAuthorizationInfoForFragmentCreationWithFragments(authorizationNumber, 0, dairyIdForAuthorization);
        }

        internal static void UpdateAuthorizationInfoForFragmentCreationWithFragments(string authorizationNumber, int fragmentsSize, int dairyIdForAuthorization)
        {
            if (string.IsNullOrEmpty(authorizationNumber)) throw new ArgumentNullException(nameof(authorizationNumber));
            if (!int.TryParse(authorizationNumber, out int authorizationNumberInt)) throw new ArgumentException(nameof(authorizationNumber));
            if (dairyIdForAuthorization <= 0) return;

            if (!AuthorizationStatusDetails.ContainsKey(authorizationNumber)) return;

            MovementDetailsInfo authorizationStatusDetail;
            AuthorizationStatusDetails.TryGetValue(authorizationNumber, out authorizationStatusDetail);

            authorizationStatusDetail.AddFragmentForAuthorization(fragmentsSize);
            authorizationStatusDetail.AddDairyIdForCreateFragments(dairyIdForAuthorization);
        }

        internal static void UpdateAuthorizationInfoForFragmentPayment(string authorizationNumber, int dairyIdForAuthorization, bool canBeAcreditAfterDeleteAuthorization)
        {
            if (string.IsNullOrEmpty(authorizationNumber)) throw new ArgumentNullException(nameof(authorizationNumber));
            if (!int.TryParse(authorizationNumber, out int authorizationNumberInt)) throw new ArgumentException(nameof(authorizationNumber));
            if (dairyIdForAuthorization <= 0) return;

            if (!AuthorizationStatusDetails.ContainsKey(authorizationNumber))
            {
                if (canBeAcreditAfterDeleteAuthorization)
                {
                    MovementDetailsInfo movementDetails = new MovementDetailsInfo(dairyIdForAuthorization, authorizationNumber, "USD");

                    if (!movementDetails.ContainsThisId(LastIdRule.Id))
                    {
                        MovementDetailsInfo.SkipDairy(AccountName, new List<int> { dairyIdForAuthorization });
                    }

                    return;
                }
            }

            MovementDetailsInfo authorizationStatusDetail;
            AuthorizationStatusDetails.TryGetValue(authorizationNumber, out authorizationStatusDetail);

            authorizationStatusDetail.AddDairyIdForFragmentPayment(dairyIdForAuthorization);
        }

        private static bool IsOneOfTheLastAuthorizations(string currencyCode, string authorizationNumber)
        {
            if (!InitialBalanceHandler.ExistsAuthorizationNumbers(currencyCode)) return false;
            return InitialBalanceHandler.ContainsAuthorizationNumber(currencyCode, authorizationNumber);
        }

        internal static void UpdateAuthorizationFragmentsStatus(string authorizationNumber, List<int> fragmentsStatus, DateTime now, int dailyIdForAuthorization)
        {
            if (string.IsNullOrEmpty(authorizationNumber)) throw new ArgumentNullException(nameof(authorizationNumber));
            if (!int.TryParse(authorizationNumber, out int authorizationNumberInt)) throw new ArgumentException(nameof(authorizationNumber));
            if (fragmentsStatus == null || fragmentsStatus.Count == 0) return;
            if (dailyIdForAuthorization <= 0) return;
            if (!AuthorizationStatusDetails.ContainsKey(authorizationNumber)) return;

            MovementDetailsInfo authorizationStatusDetail;
            AuthorizationStatusDetails.TryGetValue(authorizationNumber, out authorizationStatusDetail);
            var currencyCode = authorizationStatusDetail.currencyCode;
            if (IsOneOfTheLastAuthorizations(currencyCode, authorizationNumber)) return;
            if (authorizationStatusDetail.FragmentsForAuthorization.Count < fragmentsStatus.Count) return;

            try
            {
                foreach (int indexStatus in fragmentsStatus)
                {
                    int fixedIndex = indexStatus - 1;
                    authorizationStatusDetail.FragmentsForAuthorization[fixedIndex] = true;
                }

                authorizationStatusDetail.ChangeMaxAuthorizationDate = now;

                authorizationStatusDetail.AddDairyIdForChangeFragmentsStatus(dailyIdForAuthorization);

                AuthorizationStatusDetails[authorizationNumber] = authorizationStatusDetail;
            }
            catch (Exception)
            {
                throw;
            }
        }

        internal static void SkipDiaryWithExecutionError(IEnumerable<string> authorizationNumbers, int dairyIdForAuthorization)
        {
            if (authorizationNumbers == null) return;
            if (dairyIdForAuthorization <= 0) return;
            foreach (var authorizationNumberWithSpaces in authorizationNumbers)
            {
                var authorizationNumber = authorizationNumberWithSpaces.Trim();
                if (string.IsNullOrEmpty(authorizationNumber)) throw new ArgumentNullException(nameof(authorizationNumber));
                if (!int.TryParse(authorizationNumber, out int authorizationNumberInt)) throw new ArgumentException(nameof(authorizationNumber));
                if (AuthorizationStatusDetails.ContainsKey(authorizationNumber))
                {
                    MovementDetailsInfo authorizationStatusDetail;
                    AuthorizationStatusDetails.TryGetValue(authorizationNumber, out authorizationStatusDetail);
                    MovementDetailsInfo.SkipDairy(AccountName, new List<int> { dairyIdForAuthorization });

                    AuthorizationStatusDetails.Remove(authorizationNumber);

                    return;
                }
                else
                {
                    MovementDetailsInfo authorizationStatusDetail = new MovementDetailsInfo(dairyIdForAuthorization, authorizationNumber, "USD");

                    MovementDetailsInfo.SkipDairy(AccountName, new List<int> { dairyIdForAuthorization });
                }
            }
            
        }

        private static void ProceedToMarkSkips(string authorizationNumber, MovementDetailsInfo authorizationInfo)
        {
            if (!int.TryParse(authorizationNumber, out int authorizationNumberInt)) throw new ArgumentException(nameof(authorizationNumber));
            List<int> authorizationWithFinalSkip = new List<int>();

            if (authorizationInfo.dairyIdForAuthorization != 0) authorizationWithFinalSkip.Add(authorizationInfo.dairyIdForAuthorization);
            if (authorizationInfo.dairyIdForCreateFragments.Count != 0) authorizationWithFinalSkip.AddRange(authorizationInfo.dairyIdForCreateFragments);
            if (authorizationInfo.dairyIdForFragmentPayment.Count != 0) authorizationWithFinalSkip.AddRange(authorizationInfo.dairyIdForFragmentPayment);
            if (authorizationInfo.dairyIdForChangeFragmentsStatus.Count != 0) authorizationWithFinalSkip.AddRange(authorizationInfo.dairyIdForChangeFragmentsStatus);

            MovementDetailsInfo.SkipDairy(AccountName, authorizationWithFinalSkip);
        }

        internal static void ProceedToMarkSkipsForSpecificDairyId(int diaryId)
        {
            MovementDetailsInfo.SkipDiaryForSpecificDairyId(DbType, diaryId);
        }

        internal static void CalculatePossibleSkips(string authorizationNumber)
        {
            if (string.IsNullOrEmpty(authorizationNumber)) throw new ArgumentNullException(nameof(authorizationNumber));
            if (!int.TryParse(authorizationNumber, out int authorizationNumberInt)) throw new ArgumentException(nameof(authorizationNumber));
            if (!AuthorizationStatusDetails.ContainsKey(authorizationNumber)) return;

            MovementDetailsInfo authorizationInfo;
            AuthorizationStatusDetails.TryGetValue(authorizationNumber, out authorizationInfo);
            var currencyCode = authorizationInfo.currencyCode;
            if (IsOneOfTheLastAuthorizations(currencyCode, authorizationNumber)) return;

            bool allFragmesAreTrue = true;
            var movementDetailsWithTheSameDiaryId = MovementDetailsById(authorizationInfo.dairyIdForAuthorization);
            foreach (var movementDetail in movementDetailsWithTheSameDiaryId)
            {
                foreach (var position in movementDetail.FragmentsForAuthorization)
                {
                if (!position)
                {
                    allFragmesAreTrue = false;

                    break;
                }
            }
            }

            if (allFragmesAreTrue && !authorizationInfo.ContainsThisId(LastIdRule.Id))
            {
                foreach (var movementDetail in movementDetailsWithTheSameDiaryId)
                {
                    ProceedToMarkSkips(movementDetail.authorizationNumber, movementDetail);
            }
            }
        }

        private static IEnumerable<MovementDetailsInfo> MovementDetailsById(int diaryId)
        {
            var result = AuthorizationStatusDetails.Values.Where(x => x.dairyIdForAuthorization == diaryId);
            return result;
        }

        internal static IEnumerable<string> CashierAccountsRecentlyUse(DatabaseType dbType, string connectionString)
        {
            const int MAXIMUM_DAYS_OF_ACTIVITY_FOR_AN_ACCOUNT = 1000 * 365;
            return AllCashierAccounts(dbType, connectionString, MAXIMUM_DAYS_OF_ACTIVITY_FOR_AN_ACCOUNT);
        }

        internal static IEnumerable<string> CashierAccountsRecentlyUse(DatabaseType dbType, string connectionString, int minimumActityDays)
        {
            return AllCashierAccounts(dbType, connectionString, minimumActityDays);
        }

        internal static IEnumerable<string> AllCashierAccounts(DatabaseType dbType, string connectionString, int minimumActityDays)
        {
            if (string.IsNullOrWhiteSpace(connectionString)) throw new ArgumentException(nameof(connectionString));
            if (minimumActityDays < 0) throw new ArgumentException(nameof(minimumActityDays));

            List<string> allAccounts = new List<string>();

            StringBuilder accountString = new StringBuilder();
            string sqlString = $"SELECT Cuenta FROM accountsLRU WHERE DATEDIFF(CURDATE(), FechaHora) <= {minimumActityDays};";

            if (dbType == DatabaseType.MySQL)
            {
                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (MySqlCommand command = new MySqlCommand(sqlString, connection))
                        using (DbDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                allAccounts.Add(reader.GetString(0));
                            }
                            reader.Close();
                        }
                    }
                    catch (Exception ex)
                    {
                        throw ex;
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
            }
            else if (dbType == DatabaseType.SQLServer)
            {
                try
                {
                    using (SqlConnection connection = new SqlConnection(connectionString))
                    {
                        try
                        {
                            connection.Open();
                            using (SqlCommand command = new SqlCommand(sqlString, connection))
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    allAccounts.Add(reader.GetString(0));
                                }
                                reader.Close();
                            }
                        }
                        catch (Exception ex)
                        {
                            throw ex;
                        }
                        finally
                        {
                            connection.Close();
                        }

                    }
                }
                catch (Exception ex)
                {
                    throw ex;
                }
            }

            return allAccounts;
        }
    }
}
