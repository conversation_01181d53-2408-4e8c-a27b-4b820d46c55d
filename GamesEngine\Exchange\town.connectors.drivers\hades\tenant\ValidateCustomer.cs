﻿using Puppeteer.EventSourcing;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Runtime.Serialization;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.commons;
using town.connectors.drivers;
using town.connectors.drivers.hades;

namespace Connectors.town.connectors.drivers.hades
{
	public class ValidateCustomer : ASITenantDriver
	{
		private HttpClient _postValidateCustomerClient;
		private string SystemId;
        private string SystemPassword;
        private string ClerkId;
        public string CompanyBaseUrlServices { get; private set; }
		public ValidateCustomer() : base(Tenant_Actions.Validate)
		{
		}
		public override void Prepare(DateTime now)
		{
			CustomSettings.AddVariableParameter("customerId");
			CustomSettings.AddVariableParameter("token");

			//CustomSettings.Prepare();

			SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
			SystemPassword = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
			ClerkId = CustomSettings.Get(now, "CompanyClerkId").AsString;
			CompanyBaseUrlServices = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
		}
		public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
			if (_postValidateCustomerClient == null)
			{
				_postValidateCustomerClient = new HttpClient
				{
					BaseAddress = new Uri(CompanyBaseUrlServices),
					Timeout = TimeSpan.FromSeconds(200)
				};
			}

			if (CustomSettings.ThereArePendingChanges)
			{
				bool changeApplied = false;
				CompanyBaseUrlServices = CustomSettings.Get(now, "CompanyBaseUrlServices", out changeApplied).AsString;
				if (changeApplied)
				{
					_postValidateCustomerClient = new HttpClient
					{
						BaseAddress = new Uri(CompanyBaseUrlServices),
						Timeout = TimeSpan.FromMinutes(200)
					};
				}

				SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
				SystemPassword = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
				ClerkId = CustomSettings.Get(now, "CompanyClerkId").AsString;
			}

			var CustomerId = recordSet.Mappings["customerId"];
			var Token = recordSet.Mappings["token"];

			string customerId = CustomerId.AsString;
			string token = Token.AsString;

			var result = await ValidateCustomerAsync(now, customerId, token);
			return (T)Convert.ChangeType(result, typeof(T));
		}
		private async Task<bool> ValidateCustomerAsync(DateTime now, string customerId, string token)
		{
			return await PostValidateCustomerAsync(now, customerId, token);
		}
		private async Task<bool> PostValidateCustomerAsync(DateTime now, string customerId, string token)
		{
			if (String.IsNullOrWhiteSpace(customerId)) throw new ArgumentNullException(nameof(customerId));
			if (String.IsNullOrWhiteSpace(token)) throw new ArgumentNullException(nameof(token));

			var values = new PostValidateCustomerBody()
			{
				SystemId = SystemId,
				SystemPassword = SystemPassword,
				ClerkId = ClerkId,
				CustomerId = customerId,
				Token = token
			};

			const string url = "/v1/5dimesAPI/SvcValidateCustomer";
			var jsonString = await ASIJsonUtils.ToJsonAsync(values);
			var httpContent = new StringContent(jsonString, Encoding.UTF8, "application/json");

			string responseString = "";

			string valuesWithHiddenFields = await ASIJsonUtils.HideSensitiveDataAsync(values, Loggers.GetIntance().AccountingServicesASISvcValidateCustomer);

			try
			{
				Loggers.GetIntance().AccountingServicesASISvcValidateCustomer.Debug($@"url:{url} data:{valuesWithHiddenFields}");

				var response = await _postValidateCustomerClient.PostAsync(url, httpContent);
				responseString = await response.Content.ReadAsStringAsync();

				Loggers.GetIntance().AccountingServicesASISvcValidateCustomer.Debug($@"response:{responseString}");
			}
			catch (Exception e)
			{
				Loggers.GetIntance().AccountingServicesASISvcValidateCustomer.Error($@"url:{url} data:{valuesWithHiddenFields} type:{e.GetType()} error:{e.Message}", e);
				return false;
			}

			if (String.IsNullOrWhiteSpace(responseString))
			{
				NotifyWarn(nameof(PostValidateCustomerAsync), $"Url:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}", $"Response can not be empty");
				return false;
			}
			else
			{
				ValidateCustomerResponse response = Commons.FromJson<ValidateCustomerResponse>(responseString);
				if (response.Error != null)
				{
					if (string.Equals(response.Error.Message, "Invalid Token", StringComparison.OrdinalIgnoreCase))
					{
						Loggers.GetIntance().AccountingServicesASISvcValidateCustomer.Error($@"url:{url} data:{valuesWithHiddenFields} error:{response.Error.Message}", null);
					}
					else if (string.Equals(response.Error.Message, "Specified cast is not valid.", StringComparison.OrdinalIgnoreCase))
					{
						Loggers.GetIntance().AccountingServicesASISvcValidateCustomer.Error($@"url:{url} data:{valuesWithHiddenFields} error:{response.Error.Message}", null);
					}
					else
					{
						NotifyWarn(nameof(PostValidateCustomerAsync), $"Url:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}", $"Response threw an error {response.Error.Message}");
					}
					return false;
				}
				else
				{
					return true;
				}
			}
		}

		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            throw new NotImplementedException();
        }
    }
}
