﻿using GamesEngine.Domains;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;

[assembly: InternalsVisibleTo("LottoAPI")]

namespace GamesEngine.Bets
{
    internal class Profiles : Objeto
    {
        private readonly List<Profile> profiles = new List<Profile>();

        internal IEnumerable<Profile> GetAll
        {
            get
            {
                return profiles;
            }
        }

        internal void Add(Domain domain, string name)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (String.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (ExistsName(name)) throw new GameEngineException($"Profile name '{name}' already exist");

            var profile = new Profile(domain, name);
            profiles.Add(profile);
        }

        private bool ExistsName(string name)
        {
            var result = profiles.Exists(x => x.Name == name);
            return result;
        }

        internal Profile Profile(string name)
        {
            if (String.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (!ExistsName(name)) throw new GameEngineException($"Profile with name {name} does not exist");

            var profile = profiles.Single(x => x.Name == name);
            return profile;
        }
    }

    internal class Profile:Objeto
    {
        private readonly string name;
        private readonly Domain domain;

        internal string Name
        {
            get
            {
                return name;
            }
        }

        internal Domain Domain
        {
            get
            {
                return domain;
            }
        }

        internal Profile(Domain domain, string name)
        {
            this.domain = domain;
            this.name = name;
        }
    }
}
