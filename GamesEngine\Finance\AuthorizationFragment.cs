﻿using GamesEngine.Business;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;

namespace GamesEngine.Finance
{
	public enum FragmentStatus
	{
		Pending = 0,
		Payed = 1,
		Refunded = 2
	}

	public enum FragmentReason
	{
		Pending = 1,
		Loser = 2,
		Winner = 3,
		NoAction = 4
	}

	[Puppet]
	internal class AuthorizationFragment : Objeto
	{
		internal Authorization Authorization { get;}
		internal int Number { get;}
		public Currency Price { get;}
		internal FragmentStatus Status { get; private set; }
		private FragmentReason reason;
		internal string Reference { get;}
		public string Description { get; set; }
		public decimal Risk { get; set; }
		public decimal ToWin { get; set; }
		public decimal AdjustedWinAmount { get; set; }
		public decimal AdjustedLossAmount { get; set; }

		internal AuthorizationFragment(Authorization authorization, int number, Currency price, string reference, string description, decimal toWin)
		{
			if (authorization == null) throw new ArgumentNullException(nameof(authorization));
			if (number <= 0) throw new GameEngineException("Fragment number must be upper than zero");
			if (price == null) throw new ArgumentNullException(nameof(price));
			if (string.IsNullOrWhiteSpace(reference)) throw new ArgumentNullException(nameof(reference));
			if (toWin <= 0) throw new GameEngineException("To win must be upper than zero");

			Number = number;
			Price = price;
			Status = FragmentStatus.Pending;
			this.reason = FragmentReason.Pending;
			Authorization = authorization;
			Reference = reference;
			Description = description;
			ToWin = toWin;
		}

		internal AuthorizationFragment(Authorization authorization, int number, Currency price, string reference, string description, decimal risk, decimal toWin):
			this(authorization,number,price,reference,description,toWin)
		{
			if (risk <= 0) throw new GameEngineException("Risk must be upper than zero");

			Risk = risk;
		}

		internal AuthorizationFragment(Authorization authorization, int number, Currency price, string reference, string description, decimal risk, decimal toWin, FragmentStatus status, FragmentReason reason) :
			this(authorization, number, price, reference, description, risk, toWin)
		{
			Status = status;
			this.reason = reason;
		}

		internal void Pay(MovementsCollector movements, string concept, string reference, AccountWithBalance account)
		{
			Pay(movements, concept, reference, account, WholePaymentProcessor.NoPaymentProcessor);
		}

		internal void Pay(MovementsCollector movements, string concept, string reference, AccountWithBalance account, int processorId)
		{
			switch (Status)
			{
				case FragmentStatus.Pending:
					account.UnLock(movements, Price, Authorization, concept, reference, processorId);
					account.Withdraw(movements, Price, Authorization, concept, reference, processorId);
					Status = FragmentStatus.Payed;
					movements.AddFragmentToPayed(this);
					break;
				case FragmentStatus.Payed:
					if (reasonWasChanged)
					{
						movements.ChangeFragmentToPayed(this);
					}
					break;
				case FragmentStatus.Refunded:
					account.Withdraw(movements, Price, Authorization, concept, reference, processorId);
					Status = FragmentStatus.Payed;
					movements.ChangeFragmentToRefunded(this);
					break;
				default:
					throw new GameEngineException($"Can not change State {Status} to Payed.");
			}
		}

		internal void PayFragmentWithRiskZero(MovementsCollector movements)
		{
			if (!Authorization.Amount.ItsZero()) throw new GameEngineException("This method only pay authorizations with value zero");

			switch (Status)
			{
				case FragmentStatus.Pending:
					Status = FragmentStatus.Payed;
					movements.AddFragmentToPayed(this);
					break;
				default:
					throw new GameEngineException($"Can not change State {Status} to Payed.");
			}
		}

		internal void Refund(MovementsCollector movements, string concept, string reference, AccountWithBalance account)
		{
			Refund(movements, concept, reference, account, WholePaymentProcessor.NoPaymentProcessor);
		}

		internal void Refund(MovementsCollector movements, string concept, string reference, AccountWithBalance account, int processorId)
		{
			switch (Status)
			{
				case FragmentStatus.Payed:
					account.Accredit(movements, Price, Authorization, concept, reference, processorId);
					movements.ChangeFragmentToRefunded(this);
					Status = FragmentStatus.Refunded;
					break;
				case FragmentStatus.Refunded:
					if (reasonWasChanged)
					{
						movements.ChangeFragmentToRefunded(this);
					}
					break;
				case FragmentStatus.Pending:
					account.UnLock(movements, Price, Authorization, concept, reference, processorId);
					Status = FragmentStatus.Refunded;
					movements.AddFragmentToRefunded(this);
					break;
				default:
					throw new GameEngineException($"Can not change State {Status} to Refunded.");
			}
		}

		internal void ChangeAdjustments(decimal adjustedWinAmount, decimal adjustedLossAmount)
		{
			if (AdjustedWinAmount == adjustedWinAmount && AdjustedLossAmount == adjustedLossAmount) return;

			AdjustedWinAmount = adjustedWinAmount;
			AdjustedLossAmount = adjustedLossAmount;
		}

		private bool reasonWasChanged = false;
		internal FragmentReason Reason
		{
			get
			{
				return this.reason;
			}
			set
			{
				reasonWasChanged = this.reason != value;
				this.reason = value;
			}
		}

		internal AccountWithBalance Account { get { return Authorization.Account; } }

		internal bool IsPending()
		{
			return this.Status == FragmentStatus.Pending;
		}

		internal void ChangeInitialFragmentStatus(FragmentStatus status)
		{
			this.Status = status;
		}
	}
}
