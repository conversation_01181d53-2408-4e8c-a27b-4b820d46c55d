﻿using GamesEngine.Finance;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Threading.Tasks;

namespace ExchangeAPI.Model
{
	[DataContract(Name = "HistoricalTransactionsDTO")]
	public class HistoricalTransactionsDTO
	{
		[DataMember(Name = "transactions")]
		public List<HistoricalTransactionDTO> Transactions { get; set; } = new List<HistoricalTransactionDTO>();

		[DataMember(Name = "totalRecords")]
		public int TotalRecords { get; set; }

		internal void Add(HistoricalTransactionDTO historicalTransactionDTO)
		{
			Transactions.Add(historicalTransactionDTO);
		}

	}

	[DataContract(Name = "HistoricalTransactionDTO")]
	public class HistoricalTransactionDTO
	{
		public DateTime Date { get; set; }
		[DataMember(Name = "date")]
		public string DateAsString
		{
			get
			{
				return Date.ToString("MM/dd/yyyy HH:mm:ss");
			}
		}
		[DataMember(Name = "description")]
		public string Description { get; set; }
		[DataMember(Name = "transactionId")]
		public string TransactionId { get; set; }

		[DataMember(Name = "state")]
		public string StateAsString
		{
			get
			{
				return State.ToString();
			}
		}
		internal TransactionState State
		{
			get
			{
				if (Approvals == ApprovalsRequired) return TransactionState.Approved;
				if (Rejections == ApprovalsRequired) return TransactionState.Rejected;
				return TransactionState.Pending;
			}
		}
		public int Approvals { get; set; }
		public int ApprovalsRequired { get; set; }
		public int Rejections { get; set; }

		[DataMember(Name = "amountToCredit")]
		public decimal AmountToCredit { get; set; }
		[DataMember(Name = "amountToCreditCurrencyCode")]
		public string AmountToCreditCurrencyCode { get; set; }
		[DataMember(Name = "amountToCreditFormatted")]
		public string AmountToCreditFormatted
		{
			get
			{
				return Currency.Factory(AmountToCreditCurrencyCode, AmountToCredit).ToDisplayFormat();
			}
		}

		[DataMember(Name = "amountToDebit")]
		public decimal AmountToDebit { get; set; }
		[DataMember(Name = "amountToDebitCurrencyCode")]
		public string AmountToDebitCurrencyCode { get; set; }
		[DataMember(Name = "amountToDebitFormatted")]
		public string AmountToDebitFormatted
		{
			get
			{
				return Currency.Factory(AmountToDebitCurrencyCode, AmountToDebit).ToDisplayFormat();
			}
		}

		[DataMember(Name = "accountToCredit")]
		public string AccountToCredit { get; set; } = string.Empty;
		[DataMember(Name = "accountToDebit")]
		public string AccountToDebit { get; set; } = string.Empty;
		[DataMember(Name = "type")]
		public string Type { get; set; }
		[DataMember(Name = "isCreditForFilteredCurrency")]
		public bool? IsCreditForFilteredCurrency { get; set; }
	}
}
