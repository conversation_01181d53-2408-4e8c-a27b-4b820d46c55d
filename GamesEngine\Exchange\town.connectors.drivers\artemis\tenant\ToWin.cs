﻿using RestSharp;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.commons;
using town.connectors.drivers;
using town.connectors.drivers.artemis;
using Puppeteer.EventSourcing;

namespace Connectors.town.connectors.drivers.artemis
{
    public class ToWin : DGSTenantDriver
    {
		private RestClient _getPayoutClient;

        public ToWin() : base(Tenant_Actions.Others)
        {
        }
        public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
			if (_getPayoutClient == null)
			{
                if (string.IsNullOrWhiteSpace(SystemId)) throw new ArgumentNullException("Custom variable 'TokenSystemId' is required");
                if (string.IsNullOrWhiteSpace(Password)) throw new ArgumentNullException("Custom variable 'TokenSystemPassword' is required");
                if (string.IsNullOrWhiteSpace(ServicesUrl)) throw new ArgumentNullException("Custom variable 'CompanyBaseUrlServices' is required");

                if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken))
                {
                    await DGSTenantDriver.RequestAppTokenAsync(ServicesUrl, SystemId, Password);
                }

                _getPayoutClient = new RestClient(ServicesUrl);
            }

            if (CustomSettings.ThereArePendingChanges)
            {
                bool changeApplied = false;
                ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices", out changeApplied).AsString;
                SystemId = CustomSettings.Get(now, "TokenSystemId").AsString;
                Password = CustomSettings.Get(now, "TokenSystemPassword").AsSecret.ToString();

                if (changeApplied)
                {
                    if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken))
                    {
                        await DGSTenantDriver.RequestAppTokenAsync(ServicesUrl, SystemId, Password);
                    }

                    _getPayoutClient = new RestClient(ServicesUrl);
                }
            }
            var PlayerId = recordSet.Mappings["playerId"];
            var Pick = recordSet.Mappings["pick"];

            string playerId = PlayerId.AsString;
            if (string.IsNullOrWhiteSpace(playerId)) throw new ArgumentNullException(nameof(playerId));
            var pick = Pick.AsInt;
            if (pick < 2 || pick > 5) throw new Exception($"Parameter {nameof(pick)} is not valid");

			var result = await GetToWinAsync(playerId, pick);
			return (T)Convert.ChangeType(result, typeof(T));
		}

        public override void Prepare(DateTime now)
        {
            CustomSettings.AddVariableParameter("playerId");
            CustomSettings.AddVariableParameter("pick");

            //CustomSettings.Prepare();

            SystemId = CustomSettings.Get(now, "TokenSystemId").AsString;
            Password = CustomSettings.Get(now, "TokenSystemPassword").AsSecret.ToString();
            ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
        }

		private async Task<PayoutBody> GetToWinAsync(string playerId, int pick)
		{
			if (string.IsNullOrWhiteSpace(playerId)) throw new ArgumentNullException(nameof(playerId));
			if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken)) throw new Exception($"{nameof(DGSProcessorDriver.AppToken)} is empty");

			string url = $"/GetPayout";

			string responseString = "";
			int retryNumber = 0;
			var result = new PayoutBody();
			while (true)
			{
				try
				{
					var request = new RestRequest(url, Method.GET);
					request.AddHeader("Authorization", $"Bearer {DGSProcessorDriver.AppToken}");
					request.AddParameter("playerId", playerId);
					request.AddParameter("pick", pick);
					IRestResponse response = await _getPayoutClient.ExecuteAsync(request);
					responseString = response.Content;
					if ((int)response.StatusCode != 200)
					{
						Loggers.GetIntance().AccountingServicesDGS.Debug($"{nameof(GetToWinAsync)}\nUrl:{url}\nResponse: {responseString}");
					}
					break;
				}
				catch (Exception e)
				{
					Loggers.GetIntance().AccountingServicesDGS.Error($@"url:{url} type:{e.GetType()} error:{e.Message}", e);

					retryNumber++;

					await Task.Delay(5000);
					if (retryNumber == MAX_RETRIES) return result;
				}
			}

			if (string.IsNullOrWhiteSpace(responseString))
			{
				NotifyWarn(nameof(GetToWinAsync), $"Url:{url}\nResponse: {responseString}", $"Response can not be empty");
				return result;
			}
			else
			{

				var errorResponse = Commons.FromJson<ErrorResponse>(responseString);
				if (errorResponse != null && !string.IsNullOrWhiteSpace(errorResponse.Message))
				{
					Loggers.GetIntance().AccountingServicesDGS.Debug($"{nameof(GetToWinAsync)}\nUrl:{url}\nResponse: {responseString}");
				}
				else
				{
					try
					{
						var responseObj = Commons.FromJson<PayoutBody>(responseString);
						result = responseObj;
					}
					catch (Exception e)
					{
						NotifyWarn(nameof(GetToWinAsync), $"Url:{url}\nResponse: {responseString}", $"{e.Message}\r{nameof(responseString)} {responseString} is not valid");
					}
				}
				return result;
			}
		}

		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            throw new NotImplementedException();
        }

		public class PayoutBody
		{
			public List<ToWinBody> payout { get; set; }
		}

		public class ToWinBody
		{
			public string typeName { get; set; }
			public int toWin { get; set; }
		}
	}
}
