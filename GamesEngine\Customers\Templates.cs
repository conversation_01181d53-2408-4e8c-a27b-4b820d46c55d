﻿using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Customers
{
    public enum TEMPLATES_CONST
    {
        Basic = 1,
        Player = 2,
        Clerk = 3
    }

    [Puppet]
    internal class Templates : Objeto
    {
        private List<Template> templates = new List<Template>();
        private string[] optionsForFieldTypes;

        public Templates()
        {
            optionsForFieldTypes = Enum.GetNames(typeof(InputType));
        }

        internal IEnumerable<Template> GetAll
        {
            get { return templates; }
        }

        internal IEnumerable<string> InputTypes
        {
            get { return optionsForFieldTypes.ToList(); }
        }

        internal Template CreateTemplate(string name, int id)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");

            var template = new Template(name, id);
            nextConsecutive = id;
            return template;
        }

        private int nextConsecutive = 0;
        internal int NextTemplateConsecutive()
        {
            return nextConsecutive + 1;
        }

        internal void Add(Template template)
        {
            if (template == null) throw new ArgumentNullException(nameof(template));
            if (template.CountFields == 0) throw new GameEngineException($"{nameof(template)} does not have fields");
            if (!template.AnyUniqueField()) throw new GameEngineException($"{nameof(template)} needs at least one unique field");

            templates.Add(template);
        }

        internal bool Exists(string name)
        {
            if (string.IsNullOrWhiteSpace(name)) return false;

            foreach (var template in templates)
            {
                if (template.Name == name) return true;
            }

            return false;
        }

        internal Template GetTemplate(string name)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

            foreach (var template in templates)
            {
                if (template.Name == name)
                {
                    return template;
                }
            }

            throw new GameEngineException($"There is no {nameof(Template)} with {name}");
        }

        internal Template GetTemplateById(int id)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");

            foreach (var template in templates)
            {
                if (template.Id == id)
                {
                    return template;
                }
            }

            throw new GameEngineException($"There is no {nameof(Template)} with {id}");
        }

        internal Template GetTemplateOrNull(string name)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

            Template templateToSearch = null;
            foreach (var template in templates)
            {
                if (template.Name == name)
                {
                    templateToSearch = template;
                    break;
                }
            }

            return templateToSearch;
        }

        internal Template GetTemplateByIdOrNull(int id)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");

            Template templateToSearch = null;
            foreach (var template in templates)
            {
                if (template.Id == id)
                {
                    templateToSearch = template;
                    break;
                }
            }

            return templateToSearch;
        }

        internal void Remove(string name)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

            Template templateToRemove = GetTemplateOrNull(name);
            if (templateToRemove == null) throw new GameEngineException($"There is no {nameof(Template)} with {name}");
            templates.Remove(templateToRemove);
        }

        private int CountCopiesStartingWith(string copyName)
        {
            var count = 1;
            foreach (var template in templates)
            {
                if (template.Name.StartsWith(copyName))
                {
                    count++;
                }
            }
            return count;
        }

        internal void CopyFrom(string name, int id)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");

            Template templateToCopy = GetTemplateOrNull(name);
            if (templateToCopy == null) throw new GameEngineException($"There is no {nameof(Template)} with {name}");

            var copyName = $"{templateToCopy.Name}_copy";
            var count = CountCopiesStartingWith(copyName);
            var newTemplate = new Template($"{copyName}{count}", id);
            nextConsecutive = id;
            foreach (var field in templateToCopy.Fields)
            {
                newTemplate.AddSortedField(field.Position, field.Field);
            }
            templates.Add(newTemplate);
        }

    }

    [Puppet]
    internal class Template: Objeto
    {
        private string name;

        internal string Name
        {
            get 
            {
                if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
                return name; 
            }
            set 
            {
                if (string.IsNullOrWhiteSpace(value)) throw new ArgumentNullException(nameof(value));
                name = value; 
            }
        }

        private const int INITIAL_VERSION = 1;
        internal int Version { get; private set; } = INITIAL_VERSION;

        private readonly SortedFields fields = new SortedFields();

        internal IEnumerable<SortedField> Fields
        {
            get 
            { 
                return fields.GetAll; 
            }
        }

        internal int CountFields
        {
            get { return fields.Count; }
        }

        internal int Id { get;}

        internal Template(string name, int id)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");

            this.name = name;
            Id = id;
        }

        internal void AddSortedField(int position, Field field)
        {
            if (field == null) throw new ArgumentNullException(nameof(field));
            if (position <= 0) throw new GameEngineException($"{nameof(position)} must be greater than 0");
            if (fields.Exists(position)) throw new GameEngineException($"{nameof(position)} {position} already exist");

            fields.Add(new SortedField(position, field));
        }

        internal void ClearFields()
        {
            fields.Clear();
        }

        internal void IncreaseVersion()
        {
            Version++;
        }

        internal bool AnyFieldContainsKey(string key)
        {
            return fields.ContainsKey(key);
        }

        internal bool IsVerifiableField(string key)
        {
            return fields.IsVerifiable(key);
        }

        internal bool IsUniqueField(string key)
        {
            return fields.IsUnique(key);
        }

        internal bool AnyUniqueField()
        {
            return fields.AnyUniqueField();
        }
    }
}
