﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.Serialization;
using System.Runtime.Serialization.Json;
using System.Text;
using System.Threading.Tasks;
using GamesEngine.Finance;
using GamesEngine.Gameboards.Lotto;
using GamesEngine.PurchaseOrders;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace CashierAPI.Controllers
{
	public class ReportController : AuthorizeController
	{
		private const string ALL_ATADDRESS = "all";
        private const string ALL_TICKET_NUMBER = "all";
        private const int DOCUMENT_NUMBER_FOR_ALL = 0;

        [HttpGet("api/customers/{atAddress}/transactions/{currencyCode}")]
		[Authorize(Roles = "a16,b1")]
		public async Task<IActionResult> MovementsReportMasterAsync(string atAddress, string currencyCode, int storeId, string filterBy, int initialIndex, int amountOfRows, DateTime startDate, DateTime endDate, 
			string ticketNumber, string referenceNumber)
		{
			if (string.IsNullOrWhiteSpace(atAddress)) return BadRequest($"{nameof(atAddress)} is required");
			if (string.IsNullOrWhiteSpace(filterBy)) return BadRequest($"{nameof(filterBy)} is required");
			if (initialIndex < 0) return BadRequest($"{nameof(initialIndex)} must be greater or equal than 0");
			if (amountOfRows <= 0) return BadRequest($"{nameof(amountOfRows)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(ticketNumber)) return BadRequest($"{nameof(ticketNumber)} is required");

			atAddress = atAddress.Trim();
			var lineToGetMovements = string.Empty;
			var allAtAddressSelected = atAddress.ToLower().Trim() == ALL_ATADDRESS;
			var atAddressToUse = allAtAddressSelected ? MovementStorage.ATADDRESS_GENERAL : atAddress;

            var allTicketNumberSelected = ticketNumber.ToLower().Trim() == ALL_TICKET_NUMBER;
			int documentNumber = DOCUMENT_NUMBER_FOR_ALL;
			if (!allTicketNumberSelected)
            {
				if (!int.TryParse(ticketNumber, out documentNumber)) return BadRequest($"{nameof(ticketNumber)} {ticketNumber} is not valid");
			}

			var allReferenceNumberSelected = string.IsNullOrWhiteSpace(referenceNumber) || referenceNumber == ALL_TICKET_NUMBER;
			int reference = DOCUMENT_NUMBER_FOR_ALL;
			if (!allReferenceNumberSelected)
			{
				if (!int.TryParse(referenceNumber, out reference)) return BadRequest($"{nameof(referenceNumber)} {referenceNumber} is not valid");
			}

			var coin = Coinage.Coin(currencyCode);
			var isAtAddressValid = allAtAddressSelected || Movements.Storage.ExistMovementsTableForThisAccountAndCurrency(atAddressToUse, coin);
			if (isAtAddressValid)
			{
				switch (filterBy)
				{
					case "last_7_days":
						{
							lineToGetMovements = $"movementsFiltered = balancesList.Reports.MovementsReportFilteredByLast7Days('{atAddressToUse}', '{currencyCode}', {storeId}, Now, {initialIndex}, {amountOfRows}, {documentNumber}, {reference});";
							break;
						}
					case "last_2_days":
						{
							lineToGetMovements = $"movementsFiltered = balancesList.Reports.MovementsReportFilteredByLast2Days('{atAddressToUse}', '{currencyCode}', {storeId}, Now, {initialIndex}, {amountOfRows}, {documentNumber}, {reference});";
							break;
						}
					case "last_14_days":
						{
							lineToGetMovements = $"movementsFiltered = balancesList.Reports.MovementsReportFilteredByLast14Days('{atAddressToUse}', '{currencyCode}', {storeId}, Now, {initialIndex}, {amountOfRows}, {documentNumber}, {reference});";
							break;
						}
					case "last_30_days":
						{
							lineToGetMovements = $"movementsFiltered = balancesList.Reports.MovementsReportFilteredByLast30Days('{atAddressToUse}', '{currencyCode}', {storeId}, Now, {initialIndex}, {amountOfRows}, {documentNumber}, {reference});";
							break;
						}
					case "custom_days":
						{
							if (startDate == default(DateTime)) return BadRequest($"Parameter {nameof(startDate)} is required");
							if (endDate == default(DateTime)) return BadRequest($"Parameter {nameof(endDate)} is required");
							var normalizedStartDate = $"{startDate.Month}/{startDate.Day}/{startDate.Year}";
							var normalizedEndDate = $"{endDate.Month}/{endDate.Day}/{endDate.Year}";

							lineToGetMovements = $"movementsFiltered = balancesList.Reports.MovementsReportFilteredBy('{atAddressToUse}', '{currencyCode}', {storeId}, {normalizedStartDate}, {normalizedEndDate}, {initialIndex}, {amountOfRows}, {documentNumber}, {reference});";
							break;
						}
					default:
						{
							return NotFound($"There is no filter for '{filterBy}'");
						}
				}

				return await CashierAPI.Cashier.PerformQryAsync(MovementStorage.ATADDRESS_GENERAL, HttpContext, $@"
				{{
					balancesList = company.CustomerBalancesList;
					{lineToGetMovements}
					print true foundAtAddress;
					print movementsFiltered.CountFoundRows recordsTotal;
					for(transactions:movementsFiltered.MovementRecords)
					{{
						transaction = transactions;
						print transaction.AtAddress atAddress;
						print transaction.TicketNumber authorizationNumber;
						print transaction.CreationDate creationDate;
						print transaction.Description description;
						print transaction.TotalAmount totalAmount;
						print transaction.TotalAmountFormatted totalAmountFormatted;
						print transaction.AvailableBalance availableBalance;
						print transaction.AvailableBalanceFormatted availableBalanceFormatted;
						print transaction.ProcessorId processorId;
						print transaction.Reference reference;
					}}
					processorsWithDistinctKey = company.System.PaymentProcessor.SearchProcessorsWithDistinctKey();
					for (processors:processorsWithDistinctKey)
					{{
						processor = processors;
						print processor.Alias alias;
						print processor.Entity.Name entity;
						print processor.PaymentMethodAsText paymentMethod;
						print processor.Transactions.TransactionsAsText transactionType;
						print processor.CurrencyIso4217Code currencyCode;

						account = guardian.Accounts().SearchByProcessor(processor.ProcessorKey);
						print account.Id accountId;
					}}
				}}
			");
			}
			else
			{
				return await CashierAPI.Cashier.PerformQryAsync(MovementStorage.ATADDRESS_GENERAL, HttpContext, $@"
				{{
					print false foundAtAddress;
				}}
			");
			}
		}

		[HttpGet("api/customers/{atAddress}/transactions/{currencyCode}/detail")]
		[Authorize(Roles = "a16,b2,player")]
		public async Task<IActionResult> MovementsDetailAsync(string atAddress, string currencyCode, int authorizationId, int initialIndex, int amountOfRows)
		{
			if (string.IsNullOrWhiteSpace(atAddress)) return BadRequest($"Parameter {nameof(atAddress)} is required");
			if (authorizationId <= 0) return BadRequest($"Parameter {nameof(authorizationId)} is required");
			if (initialIndex < 0) return BadRequest($"{nameof(initialIndex)} must be greater or equal than 0");
			if (amountOfRows <= 0) return BadRequest($"{nameof(amountOfRows)} must be greater than 0");

			atAddress = atAddress.Trim();

			var result = await CashierAPI.Cashier.PerformQryAsync(atAddress, HttpContext, $@"
				{{
					movementDetail = atAddress.MovementDetailReportBy('{currencyCode}', {authorizationId}, {initialIndex}, {amountOfRows});
					print movementDetail.CountFoundRows recordsTotal;
					for(transactions:movementDetail.MovementRecords)
					{{
						transaction = transactions;
						print transaction.Number transactionNumber;
						print transaction.Risk amount;
						print transaction.RiskFormatted amountFormatted;
						print transaction.ToWin toWin;
						print transaction.ToWinFormatted toWinFormatted;
						print transaction.ReasonAsString status;
						print transaction.Paid paid;
						print transaction.PaidFormatted paidFormatted;
						print transaction.Description description;
					}}
				}}
			"
			);
			return result;
		}

		[HttpGet("api/customers/{atAddress}/transactions/{currencyCode}/fullDetail")]
        [Authorize(Roles = "player,b3")]
        public async Task<IActionResult> MovementsFullDetailAsync(string atAddress, string currencyCode, int storeId, int initialIndex, int amountOfRows, DateTime startDate, DateTime endDate)
        {
            if (string.IsNullOrWhiteSpace(atAddress)) return BadRequest($"Parameter {nameof(atAddress)} is required");
            if (initialIndex < 0) return BadRequest($"{nameof(initialIndex)} must be greater or equal than 0");
            if (amountOfRows <= 0) return BadRequest($"{nameof(amountOfRows)} must be greater than 0");

			IActionResult result;
			var atAddressToUse = atAddress.Trim();
			var coin = Coinage.Coin(currencyCode);
            var isAtAddressValid = Movements.Storage.ExistMovementsTableForThisAccountAndCurrency(atAddressToUse, coin);
			if (isAtAddressValid)
            {
                if (startDate == default(DateTime)) return BadRequest($"Parameter {nameof(startDate)} is required");
                if (endDate == default(DateTime)) return BadRequest($"Parameter {nameof(endDate)} is required");

				var startDayAsText = $"{startDate.Month}/{startDate.Day}/{startDate.Year}";
				var endDayAsText = $"{endDate.Month}/{endDate.Day}/{endDate.Year}";
				result = await CashierAPI.Cashier.PerformQryAsync(atAddress, HttpContext, $@"
				{{
					print true foundAtAddress;
					exists = atAddress.CheckIfExistsAccountIn('{currencyCode}');
					if (exists)
					{{
						balance = atAddress.GetBalance('{currencyCode}');
						report = balance.Movements.MovementsFullDetailReportBy('{atAddressToUse}', {storeId}, {startDayAsText}, {endDayAsText}, {initialIndex}, {amountOfRows});
						movementDetails = report.MovementDetails;
						print report.LastTotalCountForDetailRecords recordsTotal;

						for(transactionDetail:movementDetails)
						{{
							fragment = transactionDetail;
							transaction = fragment.MovementRecord;
							print transaction.DocumentNumber authorizationNumber;
							print transaction.CreationDate creationDate;
							print transaction.TransactionDescription transactionDescription;
							print transaction.TotalAmount totalAmount;
							print transaction.TotalAmountFormatted totalAmountFormatted;
							print transaction.AvailableBalance availableBalance;
							print transaction.AvailableBalanceFormatted availableBalanceFormatted;

							print fragment.FullNumber fullNumber;
							print fragment.Risk amount;
							print fragment.RiskFormatted amountFormatted;
							print fragment.ToWin toWin;
							print fragment.ToWinFormatted toWinFormatted;
							print fragment.ReasonAsString status;
							print fragment.Description description;
						}}
					}}
				}}
			");

            }
            else
            {
                result = await CashierAPI.Cashier.PerformQryAsync(MovementStorage.ATADDRESS_GENERAL, HttpContext, $@"
				{{
					print false foundAtAddress;
				}}
			");
            }
			return result;
        }

        private const string ALL_ENTERED_BY = "all";
		private const string ALL_MOVEMENT_TYPE = "all";
		private const string SelectionAll = "all";
		[HttpGet("api/customers/{atAddress}/movements/{currencyCode}")]
		[Authorize(Roles = "a17,b4")]
		public async Task<IActionResult> MovementsReportAsync(string atAddress, string currencyCode, int storeId, string source, string enteredBy, DateTime startDate, DateTime endDate, int initialIndex, 
			int amountOfRows, string movementType, string accountNumber, string processorId)
		{
			if (string.IsNullOrWhiteSpace(atAddress)) return BadRequest($"Parameter {nameof(atAddress)} is required");
			if (string.IsNullOrWhiteSpace(source)) return BadRequest($"Parameter {nameof(source)} is required");
			if (string.IsNullOrWhiteSpace(enteredBy)) return BadRequest($"Parameter {nameof(enteredBy)} is required");
			if (string.IsNullOrWhiteSpace(movementType)) return BadRequest($"Parameter {nameof(movementType)} is required");
			if (string.IsNullOrWhiteSpace(accountNumber)) return BadRequest($"Parameter {nameof(accountNumber)} is required");
			if (string.IsNullOrWhiteSpace(processorId)) return BadRequest($"Parameter {nameof(processorId)} is required");
			Movement.type type;
			if (movementType != ALL_MOVEMENT_TYPE && ! Enum.TryParse(movementType, out type)) return BadRequest($"Parameter {nameof(movementType)} is not valid");
			if (startDate == default(DateTime)) return BadRequest($"Parameter {nameof(startDate)} is required");
			if (endDate == default(DateTime)) return BadRequest($"Parameter {nameof(endDate)} is required");

			var allAtAddressSelected = atAddress.ToLower().Trim() == ALL_ATADDRESS;
			var atAddressToUse = allAtAddressSelected ? MovementStorage.ATADDRESS_GENERAL : atAddress;
			var allEnteredByIdSelected = enteredBy.ToLower().Trim() == ALL_ENTERED_BY;
			int enteredById = allEnteredByIdSelected ? Users.NO_USER : int.Parse(enteredBy);
			var accountNumberToUse = accountNumber.Equals(SelectionAll, StringComparison.OrdinalIgnoreCase) ? string.Empty : accountNumber;

			var startDayAsText = $"{startDate.Month}/{startDate.Day}/{startDate.Year}";
			var endDayAsText = $"{endDate.Month}/{endDate.Day}/{endDate.Year}";
			IActionResult result;
			var coin = Coinage.Coin(currencyCode);
			var isAtAddressValid = allAtAddressSelected || Movements.Storage.ExistMovementsTableForThisAccountAndCurrency(atAddressToUse, coin);
			if (isAtAddressValid)
			{
				result = await CashierAPI.Cashier.PerformQryAsync(MovementStorage.ATADDRESS_GENERAL, HttpContext, $@"
				{{
					balancesList = company.CustomerBalancesList;
					movementsFiltered = balancesList.Reports.MovementsFilteredBy('{atAddressToUse}', '{currencyCode}', {storeId}, '{source}', {enteredById}, {startDayAsText}, {endDayAsText}, {initialIndex}, {amountOfRows}, '{movementType}', '{accountNumberToUse}', '{processorId}');
					print true foundAtAddress;
					print movementsFiltered.CountFoundRows recordsTotal;
					for(movements:movementsFiltered.Movements)
					{{
						movement = movements;

						print movement.Day day;
						sourceId = movement.Source;
						if (sourceId != 0)
						{{
							print balancesList.SourceName(sourceId, movement.CurrencyAsText) source;
						}}
						
						print movement.AtAddress atAddress;
						print movement.Concept concept;
						print movement.Who who;
                        print movement.DocumentNumber documentNumber;
						print movement.Reference Reference;

						if( movement.TypeAsText == 'Credit' || movement.TypeAsText == 'Unlock' )
						{{
							print movement.CurrentAmount credit;
							print 0 debit;
							print 0 lock;
							print movement.CurrentAmountFormatted creditFormatted;
							print movement.ZeroFormatted debitFormatted;
							print movement.ZeroFormatted lockFormatted;
						}}
						if( movement.TypeAsText == 'Debit' )
						{{
							print 0 credit;
							print movement.CurrentAmount debit;
							print 0 lock;
							print movement.ZeroFormatted creditFormatted;
							print movement.CurrentAmountFormatted debitFormatted;
							print movement.ZeroFormatted lockFormatted;
						}}
						if( movement.TypeAsText == 'Lock' )
						{{
							print 0 credit;
							print 0 debit;
							print movement.CurrentAmount lock;
							print movement.ZeroFormatted creditFormatted;
							print movement.ZeroFormatted debitFormatted;
							print movement.CurrentAmountFormatted lockFormatted;
						}}
						print movement.NewBalance currentBalance;
						print movement.NewBalanceFormatted currentBalanceFormatted;
						print movement.CurrentAmount currentAmount;
						print movement.CurrentAmountFormatted currentAmountFormatted;
						print movement.AccountNumber accountNumber;
						print movement.NewLock currentLock;
						print movement.NewLockFormatted currentLockFormatted;
						print movement.CurrencyAsText currencyCode;
						print movement.ProcessorId processorId;
					}}
					processorsWithDistinctKey = company.System.PaymentProcessor.SearchProcessorsWithDistinctKey();
					for (processors:processorsWithDistinctKey)
					{{
						processor = processors;
						print processor.Alias alias;
						print processor.Entity.Name entity;
						print processor.PaymentMethodAsText paymentMethod;
						print processor.Transactions.TransactionsAsText transactionType;
						print processor.CurrencyIso4217Code currencyCode;

						account = guardian.Accounts().SearchByProcessor(processor.ProcessorKey);
						print account.Id accountId;
					}}
				}}
			");
			}
			else
			{
				result = await CashierAPI.Cashier.PerformQryAsync(MovementStorage.ATADDRESS_GENERAL, HttpContext, $@"
				{{
					print false foundAtAddress;
				}}
			");
			}
			return result;
		}

		[HttpGet("api/customers/{atAddress}/mymovements/{currencyCode}")]
		[Authorize(Roles = "a17,b4,player")]
		public async Task<IActionResult> MyMovementsReportAsync(string atAddress, string currencyCode, int storeId, string source, string enteredBy, DateTime startDate, DateTime endDate, int initialIndex, int amountOfRows)
		{
			if (string.IsNullOrWhiteSpace(atAddress)) return BadRequest($"Parameter {nameof(atAddress)} is required");
			if (string.IsNullOrWhiteSpace(source)) return BadRequest($"Parameter {nameof(source)} is required");
			if (string.IsNullOrWhiteSpace(enteredBy)) return BadRequest($"Parameter {nameof(enteredBy)} is required");
			if (startDate == default(DateTime)) return BadRequest($"Parameter {nameof(startDate)} is required");
			if (endDate == default(DateTime)) return BadRequest($"Parameter {nameof(endDate)} is required");

			var allAtAddressSelected = atAddress.ToLower().Trim() == ALL_ATADDRESS;
			var atAddressToUse = allAtAddressSelected ? MovementStorage.ATADDRESS_GENERAL : atAddress;
			var allEnteredByIdSelected = enteredBy.ToLower().Trim() == ALL_ENTERED_BY;
			int enteredById = allEnteredByIdSelected ? Users.NO_USER : int.Parse(enteredBy);

			var startDayAsText = $"{startDate.Month}/{startDate.Day}/{startDate.Year}";
			var endDayAsText = $"{endDate.Month}/{endDate.Day}/{endDate.Year}";
			IActionResult result;
			var coin = Coinage.Coin(currencyCode);
			var isAtAddressValid = allAtAddressSelected || Movements.Storage.ExistMovementsTableForThisAccountAndCurrency(atAddressToUse, coin);
			if (isAtAddressValid)
			{
				result = await CashierAPI.Cashier.PerformQryAsync(MovementStorage.ATADDRESS_GENERAL, HttpContext, $@"
				{{
					balancesList = company.CustomerBalancesList;
					movementsFiltered = balancesList.Reports.MyMovementsFilteredBy('{atAddressToUse}', '{currencyCode}', {storeId}, '{source}', {enteredById}, {startDayAsText}, {endDayAsText}, {initialIndex}, {amountOfRows});
					print true foundAtAddress;
					print movementsFiltered.CountFoundRows recordsTotal;
					for(movements:movementsFiltered.Movements)
					{{
						movement = movements;

						print movement.Day day;
						sourceId = movement.Source;
						if (sourceId != 0)
						{{
							print balancesList.SourceName(sourceId, movement.CurrencyAsText) source;
						}}
						
						print movement.AtAddress atAddress;
						print movement.Concept concept;
						print movement.Who who;
                        print movement.DocumentNumber documentNumber;
						print movement.Reference Reference;

						if( movement.TypeAsText == 'Credit' || movement.TypeAsText == 'Unlock' )
						{{
							print movement.CurrentAmount credit;
							print 0 debit;
							print 0 lock;
							print movement.CurrentAmountFormatted creditFormatted;
							print movement.ZeroFormatted debitFormatted;
							print movement.ZeroFormatted lockFormatted;
						}}
						if( movement.TypeAsText == 'Debit')
						{{
							print 0 credit;
							print movement.CurrentAmount debit;
							print 0 lock;
							print movement.ZeroFormatted creditFormatted;
							print movement.CurrentAmountFormatted debitFormatted;
							print movement.ZeroFormatted lockFormatted;
						}}
						if( movement.TypeAsText == 'Lock' )
						{{
							print 0 credit;
							print 0 debit;
							print movement.CurrentAmount lock;
							print movement.ZeroFormatted creditFormatted;
							print movement.ZeroFormatted debitFormatted;
							print movement.CurrentAmountFormatted lockFormatted;
						}}
						print movement.NewBalance currentBalance;
						print movement.NewBalanceFormatted currentBalanceFormatted;
						print movement.CurrentAmount currentAmount;
						print movement.CurrentAmountFormatted currentAmountFormatted;
						print movement.AccountNumber accountNumber;
						print movement.NewLock currentLock;
						print movement.NewLockFormatted currentLockFormatted;
						print movement.CurrencyAsText currencyCode;
					}}
				}}
			");
			}
			else
			{
				result = await CashierAPI.Cashier.PerformQryAsync(MovementStorage.ATADDRESS_GENERAL, HttpContext, $@"
				{{
					print false foundAtAddress;
				}}
			");
			}
			return result;
		}

		[HttpGet("api/authorizations/{authorizationId}/fragments/{currencyCode}")]
		[Authorize(Roles = "devops,b5")]
		public async Task<IActionResult> FragmentsAsync(int authorizationId, string currencyCode, int initialIndex, int amountOfRows)
		{
			if (authorizationId <= 0) return BadRequest($"Parameter {nameof(authorizationId)} is required");
			if (initialIndex < 0) return BadRequest($"{nameof(initialIndex)} must be greater or equal than 0");
			if (amountOfRows <= 0) return BadRequest($"{nameof(amountOfRows)} must be greater than 0");

			var atAddressResult = await CashierAPI.Cashier.PerformQryAsync(MovementStorage.ATADDRESS_GENERAL, HttpContext, $@"
				{{
					balancesList = company.CustomerBalancesList;
					atAddress = balancesList.AtAddressFrom({authorizationId});
					print atAddress atAddress;
				}}");

			if (!(atAddressResult is OkObjectResult)) return BadRequest(((ObjectResult)atAddressResult).ToString());

			string value = ((OkObjectResult)atAddressResult).Value.ToString();
			AtAddressBody atAddress = JsonConvert.DeserializeObject<AtAddressBody>(value);
			if (string.IsNullOrWhiteSpace(atAddress.AtAddress)) return BadRequest($"{nameof(atAddress)} was not found");

			var result = await CashierAPI.Cashier.PerformQryAsync(atAddress.AtAddress, HttpContext, $@"
				{{
					balance = atAddress.GetBalance('{currencyCode}');
					movementDetail = balance.Movements.MovementDetailReportBy(atAddress, '{currencyCode}', {authorizationId}, {initialIndex}, {amountOfRows});
					print movementDetail.CountFoundRows recordsTotal;
					print {atAddress.AtAddress} atAddress;
					for(transactions:movementDetail.MovementRecords)
					{{
						transaction = transactions;
						
						print transaction.Number number;
						print transaction.Risk amount;
						print transaction.RiskFormatted amountFormatted;
						print transaction.ToWin toWin;
						print transaction.ToWinFormatted toWinFormatted;
						print transaction.ReasonAsString status;
						print transaction.AdjustedWinAmount adjustedWinAmount;
						print transaction.AdjustedWinAmountFormatted adjustedWinAmountFormatted;
						print transaction.AdjustedLossAmount adjustedLossAmount;
						print transaction.AdjustedLossAmountFormatted adjustedLossAmountFormatted;
						print transaction.Description description;
					}}
				}}
			");

			return result;
		}

		[HttpPut("api/authorizations/{authorizationId}/fragments/lr")]
		[Authorize(Roles = "devops,b6")]
		public async Task<IActionResult> UpdateLottoRewardFragmentsAsync(string authorizationId, [FromBody]FragmentsUpdatingBody body)
		{
			if (body == null) return BadRequest("Body is required");
			if (string.IsNullOrWhiteSpace(body.AtAddress)) return BadRequest($"Parameter {nameof(body.AtAddress)} is required");
			if (string.IsNullOrWhiteSpace(body.Who)) return BadRequest($"Parameter {nameof(body.Who)} is required");
			if (body.Fragments == null || body.Fragments.Count==0) return BadRequest($"Parameter {nameof(body.Fragments)} is required");
			if (body.Reason == FragmentReason.Pending) return BadRequest($"{nameof(FragmentReason)} {body.Reason} is not valid");

			var commandName = body.Reason == FragmentReason.NoAction ? "PrepareRefunds" : "PreparePayments";
			var strFragments = string.Join(",", body.Fragments);
			var initialFragmentStatus = new StringBuilder();
			var changesPerAuthorization = new StringBuilder();
			initialFragmentStatus.Append($"Eval('initialStatus = ' + authorization.FragmentsStatus({{{strFragments}}}) + ';'); authorization.ChangeInitialFragmentsStatus({{{strFragments}}}, initialStatus);");
			changesPerAuthorization.Append($".{commandName}(itIsThePresent, authorization, {{{strFragments}}}, Now, { Store.STORES_SEQUENCE_LOTTO}, '{body.Concept}', {body.Reason})");
			var query = $@"
				{{
					authorization = atAddress.GetAuthorizationForUpdate({authorizationId});
					{initialFragmentStatus.ToString()}
					{{
						ataddress
							{changesPerAuthorization.ToString()}
							.ApplyChanges(itIsThePresent, '{body.Who}');
					}}
				}}
			";
			var result = await CashierAPI.Cashier.PerformCmdAsync(body.AtAddress, HttpContext, query);
			return result;
		}

		[DataContract(Name = "AtAddressBody")]
		public class AtAddressBody
		{
			[DataMember(Name = "atAddress")]
			public string AtAddress { get; set; }
		}

		[DataContract(Name = "FragmentsUpdatingBody")]
		public class FragmentsUpdatingBody
		{
			[DataMember(Name = "atAddress")]
			public string AtAddress { get; set; }
			[DataMember(Name = "concept")]
			public string Concept { get; set; }
			[DataMember(Name = "who")]
			public string Who { get; set; }
			[DataMember(Name = "reason")]
			public FragmentReason Reason { get; set; }
			[DataMember(Name = "fragments")]
			public List<int> Fragments { get; set; }
		}
	}
}