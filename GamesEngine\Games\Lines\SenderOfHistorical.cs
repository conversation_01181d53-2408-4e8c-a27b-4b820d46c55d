﻿using GamesEngine.Gameboards;
using GamesEngine.Gameboards.Lines;
using GamesEngine.MessageQueuing;
using GamesEngine.Settings;
using GamesEngine.Time;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;

namespace GamesEngine.Games.Lines
{
	class SenderOfHistorical : IDisposable
	{
		private readonly Showcase showcase;
		private readonly Game game;
		private readonly bool itIsThePresent;
		private readonly MessagesBuffer<LineMessage> grading;

		internal SenderOfHistorical(Showcase showcase, bool itIsThePresent)
		{
			if (showcase == null) throw new ArgumentNullException(nameof(showcase));

			this.showcase = showcase;
			this.itIsThePresent = itIsThePresent;
			int blockSize = this.showcase.Matchday.Betboard.Tournament.Settings.BlockSizeCacheToHistorical;
			this.grading = new MessagesBuffer<LineMessage>(Integration.Kafka.TopicForLinesGrading, blockSize, itIsThePresent);
		}

		internal SenderOfHistorical(Game game, bool itIsThePresent)
		{
			if (game == null) throw new ArgumentNullException(nameof(game));

			this.game = game;
			this.itIsThePresent = itIsThePresent;
			int blockSize = this.game.Tournament.Settings.BlockSizeCacheToHistorical;
			this.grading = new MessagesBuffer<LineMessage>(Integration.Kafka.TopicForLinesGrading, blockSize, itIsThePresent);
		}

		internal void WriteGeneralLinesData(IEnumerable<Line> lines, DateTime now, string gradedBy)
		{
			if (!itIsThePresent) return;

			if (lines == null) throw new ArgumentNullException(nameof(lines));
			if (string.IsNullOrWhiteSpace(gradedBy)) throw new ArgumentNullException(nameof(gradedBy));
			if (lines.Any(x => x.Showcase != this.showcase)) throw new GameEngineException("There are some lines that do not belong to this showcase.");
			if (lines.Any(x => x.Tier == Tier.TIER_ONE && ! x.Game.IsGameOver())) throw new GameEngineException($"There are matches that have not ended therefore it can not be stored.");

			var tournament = this.showcase.Betboard.Tournament;
			int tournamentId = tournament.Id;
			string league = tournament.League.Name;
			string sport = tournament.Sport.Name;

			Game game = this.showcase.Game;

			Team teamA = game.TeamA;
			Team teamB = game.TeamB;
			int favorite = (int)game.Favorite.ToFavorite();

			LinesScoreMessageGame serializedMessage = new LinesScoreMessageGame(
				tournamentId, 
				game.Number, 
				teamA.Name, 
				teamA.ShortName, 
				teamB.Name, 
				teamB.ShortName, 
				favorite, 
				game.StartedDate, 
				game.EndedDate, 
				game.ScoreTeamA, 
				game.ScoreTeamB, 
				gradedBy, 
				now,
				league,
				sport,
				lines
			);
			Integration.Kafka.Send(this.itIsThePresent, Integration.Kafka.TopicForLinesGrading, serializedMessage);
		}

		internal void WriteScoresByPeriodsData(Period period, DateTime now, string gradedBy)
		{
			if (!itIsThePresent) return;
			if (string.IsNullOrWhiteSpace(gradedBy)) throw new ArgumentNullException(nameof(gradedBy));
			
			var tournament = this.game.Tournament;
			int tournamentId = tournament.Id;


			Team teamA = this.game.TeamA;
			Team teamB = this.game.TeamB;

			LinesScoreByPeriodsMessageGame serializedMessage = new LinesScoreByPeriodsMessageGame(
				tournamentId,
				this.game.Number,
				teamA.Name,
				teamB.Name,
				teamA.ShortName,
				teamB.ShortName,
				period.Name,
				now,
				this.game.ScoreTeamA,
				this.game.ScoreTeamB,
				gradedBy
			);
			Integration.Kafka.Send(this.itIsThePresent, Integration.Kafka.TopicForLinesGrading, serializedMessage);
		}

		internal void WriteWinnerData(Wager wager, DateTime now, string gradedBy)
		{
			if (!itIsThePresent) return;

			if (wager == null) throw new ArgumentNullException(nameof(wager));
			if (! wager.IsWinner() && !wager.IsGraded()) throw new GameEngineException($"Wager is in {wager.Prizing} and it is supposed to be a winner wager");
			if (string.IsNullOrWhiteSpace(gradedBy)) throw new ArgumentNullException(nameof(gradedBy));

			var line = wager.Line;
			var chosenAnswer = string.Empty;
			var fixedChosenAnswer = string.Empty;
			if (wager.ChosenAnswer is FixedAnswer)
			{
				fixedChosenAnswer = wager.ChosenAnswer.ChosenAnswerAsText();
			}
			else
			{
				chosenAnswer = wager.ChosenAnswer.ChosenAnswerAsText();
			}
			LineWinnerInfo result = new LineWinnerInfo(wager.Betboard.Tournament.Id, line.Game.Number, gradedBy, line.Version, line.LineId, now, wager.AuthorizationId, wager.AccountNumber, wager.Risk, wager.ToWin(), chosenAnswer, fixedChosenAnswer, creation: wager.CreationDate, wager.Grading);
			this.grading.Add(result);
		}

		internal void WriteLoserData(Wager wager, DateTime gradedDate, string gradedBy)
		{
			if (!itIsThePresent) return;

			if (wager == null) throw new ArgumentNullException(nameof(wager));
			if (wager.IsPending() || wager.IsNoAction()) throw new GameEngineException($"Wager is in {wager.Prizing} and it is supposed to be a loser wager");
			if (string.IsNullOrWhiteSpace(gradedBy)) throw new ArgumentNullException(nameof(gradedBy));

			var line = wager.Line;
			var chosenAnswer = string.Empty;
			var fixedChosenAnswer = string.Empty;
			if (wager.ChosenAnswer is FixedAnswer)
			{
				fixedChosenAnswer = wager.ChosenAnswer.ChosenAnswerAsText();
			}
			else
			{
				chosenAnswer = wager.ChosenAnswer.ChosenAnswerAsText();
			}
			LineLoserInfo result = new LineLoserInfo(wager.Betboard.Tournament.Id, line.Game.Number, gradedBy, line.Version, line.LineId, gradedDate, wager.AuthorizationId, wager.AccountNumber, wager.Risk, wager.ToWin(), chosenAnswer, fixedChosenAnswer, wager.CreationDate, wager.Grading);
			this.grading.Add(result);
		}

		internal void WriteNoActionData(Wager wager, DateTime gradedDate, string gradedBy)
		{
			if (!itIsThePresent) return;

			if (wager == null) throw new ArgumentNullException(nameof(wager));
			if (!wager.IsNoAction() && !wager.IsUnprized()) throw new GameEngineException($"Wager is in {wager.Prizing} and it is supposed to be a no action wager");
			if (string.IsNullOrWhiteSpace(gradedBy)) throw new ArgumentNullException(nameof(gradedBy));

			var line = wager.Line;
			var chosenAnswer = string.Empty;
			var fixedChosenAnswer = string.Empty;
			if (wager.ChosenAnswer is FixedAnswer) 
			{
				fixedChosenAnswer = wager.ChosenAnswer.ChosenAnswerAsText();
			}
			else
            {
				chosenAnswer = wager.ChosenAnswer.ChosenAnswerAsText();
			}
			LineNoActionInfo result = new LineNoActionInfo(wager.Betboard.Tournament.Id, line.Game.Number, gradedBy, line.Version, line.LineId, gradedDate, wager.AuthorizationId, wager.AccountNumber, wager.Risk, wager.ToWin(), chosenAnswer, fixedChosenAnswer, wager.CreationDate, wager.Grading);
			this.grading.Add(result);
		}

		internal void StartSending(int tournamentId, int gameNumber, int lineId)
		{
			LineWinnerInfo startWinnerInfo = LineWinnerInfo.STREAM_STARTING;
			startWinnerInfo.InitMessage(tournamentId, gameNumber, lineId);
			this.grading.Add(startWinnerInfo);

			LineLoserInfo startLoserInfo = LineLoserInfo.STREAM_STARTING;
			startLoserInfo.InitMessage(tournamentId, gameNumber, lineId);
			this.grading.Add(startLoserInfo);

			LineNoActionInfo startNoActionInfo = LineNoActionInfo.STREAM_STARTING;
			startNoActionInfo.InitMessage(tournamentId, gameNumber, lineId);
			this.grading.Add(startNoActionInfo);
		}

		internal void EndSending()
		{
			this.grading.Add(LineWinnerInfo.STREAM_ENDING);
			this.grading.Add(LineLoserInfo.STREAM_ENDING);
			this.grading.Add(LineNoActionInfo.STREAM_ENDING);
		}

		public void Dispose()
		{
			this.grading.Flush();
		}
	}

	public abstract class LinesInfoMessage : KafkaMessage
	{
		internal LinesInfoMessage()
		{

		}

		public LinesInfoMessage(string message) : base(message)
		{

		}

		protected LineInfo DeserializeLine(string[] gameAttributes, ref int lineIndex)
		{
			LineType lineType = (LineType)gameAttributes[lineIndex++][0];
			LineInfo result;
			switch (lineType)
			{
				case LineType.MONEY_LINE:
					MoneyLineInfo moneyLineInfo = new MoneyLineInfo();
					moneyLineInfo.LineId = int.Parse(gameAttributes[lineIndex++]);
					moneyLineInfo.Version = int.Parse(gameAttributes[lineIndex++]);
					moneyLineInfo.TeamAReward = int.Parse(gameAttributes[lineIndex++]);
					moneyLineInfo.TeamBReward = int.Parse(gameAttributes[lineIndex++]);
					moneyLineInfo.TheAnswer = gameAttributes[lineIndex++][0];
					moneyLineInfo.Grading = (GradingStatus)int.Parse(gameAttributes[lineIndex++]);
					moneyLineInfo.QuestionId = int.Parse(gameAttributes[lineIndex++]);
					result = moneyLineInfo;
					break;
				case LineType.MONEYDRAW_LINE:
					MoneyDrawLineInfo moneyDrawLineInfo = new MoneyDrawLineInfo();
					moneyDrawLineInfo.LineId = int.Parse(gameAttributes[lineIndex++]);
					moneyDrawLineInfo.Version = int.Parse(gameAttributes[lineIndex++]);
					moneyDrawLineInfo.TeamAReward = int.Parse(gameAttributes[lineIndex++]);
					moneyDrawLineInfo.TieReward = int.Parse(gameAttributes[lineIndex++]);
					moneyDrawLineInfo.TeamBReward = int.Parse(gameAttributes[lineIndex++]);
					moneyDrawLineInfo.TheAnswer = gameAttributes[lineIndex++][0];
					moneyDrawLineInfo.Grading = (GradingStatus)int.Parse(gameAttributes[lineIndex++]);
					moneyDrawLineInfo.QuestionId = int.Parse(gameAttributes[lineIndex++]);
					result = moneyDrawLineInfo;
					break;
				case LineType.SPREAD_LINE:
					SpreadLineInfo spreadLineInfo = new SpreadLineInfo();
					spreadLineInfo.LineId = int.Parse(gameAttributes[lineIndex++]);
					spreadLineInfo.Version = int.Parse(gameAttributes[lineIndex++]);
					spreadLineInfo.Spread = double.Parse(gameAttributes[lineIndex++]);
					spreadLineInfo.TeamAReward = int.Parse(gameAttributes[lineIndex++]);
					spreadLineInfo.TeamBReward = int.Parse(gameAttributes[lineIndex++]);
					spreadLineInfo.TheAnswer = gameAttributes[lineIndex++][0];
					spreadLineInfo.Grading = (GradingStatus)int.Parse(gameAttributes[lineIndex++]);
					spreadLineInfo.QuestionId = int.Parse(gameAttributes[lineIndex++]);
					result = spreadLineInfo;
					break;
				case LineType.TOTAL_POINTS_LINE:
					TotalPointsLineInfo totalPointsLineInfo = new TotalPointsLineInfo();
					totalPointsLineInfo.LineId = int.Parse(gameAttributes[lineIndex++]);
					totalPointsLineInfo.Version = int.Parse(gameAttributes[lineIndex++]);
					totalPointsLineInfo.Score = double.Parse(gameAttributes[lineIndex++]);
					totalPointsLineInfo.OverReward = int.Parse(gameAttributes[lineIndex++]);
					totalPointsLineInfo.UnderReward = int.Parse(gameAttributes[lineIndex++]);
					totalPointsLineInfo.TheAnswer = gameAttributes[lineIndex++][0];
					totalPointsLineInfo.Grading = (GradingStatus)int.Parse(gameAttributes[lineIndex++]);
					totalPointsLineInfo.QuestionId = int.Parse(gameAttributes[lineIndex++]);
					result = totalPointsLineInfo;
					break;
				case LineType.YES_NO_LINE:
					YesNoLineInfo yesNoLineInfo = new YesNoLineInfo();
					yesNoLineInfo.LineId = int.Parse(gameAttributes[lineIndex++]);
					yesNoLineInfo.Version = int.Parse(gameAttributes[lineIndex++]);
					yesNoLineInfo.YesReward = int.Parse(gameAttributes[lineIndex++]);
					yesNoLineInfo.NoReward = int.Parse(gameAttributes[lineIndex++]);
					yesNoLineInfo.Text = gameAttributes[lineIndex++];
					yesNoLineInfo.TheAnswer = gameAttributes[lineIndex++][0];
					yesNoLineInfo.Grading = (GradingStatus)int.Parse(gameAttributes[lineIndex++]);
					yesNoLineInfo.QuestionId = int.Parse(gameAttributes[lineIndex++]);
					result = yesNoLineInfo;
					break;
				case LineType.OVER_UNDER_LINE:
					OverUnderLineInfo overUnderLineInfo = new OverUnderLineInfo();
					overUnderLineInfo.LineId = int.Parse(gameAttributes[lineIndex++]);
					overUnderLineInfo.Version = int.Parse(gameAttributes[lineIndex++]);
					overUnderLineInfo.Score = double.Parse(gameAttributes[lineIndex++]);
					overUnderLineInfo.OverReward = int.Parse(gameAttributes[lineIndex++]);
					overUnderLineInfo.UnderReward = int.Parse(gameAttributes[lineIndex++]);
					overUnderLineInfo.Text = gameAttributes[lineIndex++];
					overUnderLineInfo.TheAnswer = gameAttributes[lineIndex++][0];
					overUnderLineInfo.Grading = (GradingStatus)int.Parse(gameAttributes[lineIndex++]);
					overUnderLineInfo.QuestionId = int.Parse(gameAttributes[lineIndex++]);
					result = overUnderLineInfo;
					break;
				case LineType.FIXED_LINE:
					FixedLineInfo fixedLineInfo = new FixedLineInfo();
					fixedLineInfo.LineId = int.Parse(gameAttributes[lineIndex++]);
					fixedLineInfo.Version = int.Parse(gameAttributes[lineIndex++]);
					fixedLineInfo.TheAnswer = gameAttributes[lineIndex++];
					fixedLineInfo.Text = gameAttributes[lineIndex++];
					var options = KafkaMessages.Split(gameAttributes[lineIndex++]);
					var rewards = KafkaMessages.Split(gameAttributes[lineIndex++]);
					Debug.Assert(options.Length == rewards.Length, "Options and rewards must have the same length");

					fixedLineInfo.Options = new string[options.Length];
					fixedLineInfo.Rewards = new int[options.Length];
					for (int i = 0; i < options.Length; i++)
					{
						fixedLineInfo.Options[i] = options[i];
						fixedLineInfo.Rewards[i] = int.Parse(rewards[i]);
					}
					fixedLineInfo.Grading = (GradingStatus)int.Parse(gameAttributes[lineIndex++]);
					fixedLineInfo.QuestionId = int.Parse(gameAttributes[lineIndex++]);
					result = fixedLineInfo;
					break;
				default:
					throw new GameEngineException($"Message {gameAttributes[lineIndex]} has an invalid line type");
			}

			return result;
		}
	}

	public class LinesScoreByPeriodsMessageGame : LinesInfoMessage
	{
		private int tournamentId;
		private int gameNumber;
		private string teamAName;
		private string teamAShortName;
		private string teamBName;
		private string teamBShortName;
		private string period;
		private DateTime moment;
		private int scoreTeamA;
		private int scoreTeamB;
		private string who;

		internal LinesScoreByPeriodsMessageGame(int tournamentId, int gameNumber, string teamAName, string teamBName, string teamAShortName, string teamBShortName, string period, DateTime moment, int scoreTeamA, int scoreTeamB, string who)
		{

			this.tournamentId = tournamentId;
			this.gameNumber = gameNumber;
			this.teamAName = teamAName;
			this.teamBName = teamBName;
			this.teamAShortName = teamAShortName;
			this.teamBShortName = teamBShortName;
			this.period = period;
			this.moment = moment;
			this.scoreTeamA = scoreTeamA;
			this.scoreTeamB = scoreTeamB;
			this.who = who;
		}

		public LinesScoreByPeriodsMessageGame(string message) : base(message)
		{
		}

		protected override void Deserialize(string[] gameAttributes, out int fieldOrder)
		{
			base.Deserialize(gameAttributes, out fieldOrder);
			var gradeType = (GradeLineType)int.Parse(gameAttributes[fieldOrder++]);
			if (gradeType != GradeLineType.SCORES_PERIODS) throw new GameEngineException($"This message {gameAttributes[fieldOrder - 1]} is not for Game Score Periods message.");

			this.tournamentId = int.Parse(gameAttributes[fieldOrder++]);
			this.gameNumber = int.Parse(gameAttributes[fieldOrder++]);
			this.teamAName = gameAttributes[fieldOrder++];
			this.teamBName = gameAttributes[fieldOrder++];
			this.teamAShortName = gameAttributes[fieldOrder++];
			this.teamBShortName = gameAttributes[fieldOrder++];
			this.period = gameAttributes[fieldOrder++];
			this.moment = new DateTime(
				int.Parse(gameAttributes[fieldOrder++]),
				int.Parse(gameAttributes[fieldOrder++]),
				int.Parse(gameAttributes[fieldOrder++]),
				int.Parse(gameAttributes[fieldOrder++]),
				int.Parse(gameAttributes[fieldOrder++]),
				int.Parse(gameAttributes[fieldOrder++])
			);
			this.scoreTeamA = int.Parse(gameAttributes[fieldOrder++]);
			this.scoreTeamB = int.Parse(gameAttributes[fieldOrder++]);
			this.who = gameAttributes[fieldOrder++];
		}

        protected override void InternalSerialize()
        {
			base.InternalSerialize();
			AddProperty((int)GradeLineType.SCORES_PERIODS).
			AddProperty(this.tournamentId).
			AddProperty(this.gameNumber).
			AddProperty(this.teamAName).
			AddProperty(this.teamBName).
			AddProperty(this.teamAShortName).
			AddProperty(this.teamBShortName).
			AddProperty(this.period).
			AddProperty(this.moment).
			AddProperty(this.scoreTeamA).
			AddProperty(this.scoreTeamB).
			AddProperty(this.who);
		}

        internal int TournamentId
		{
			get
			{
				return this.tournamentId;
			}
		}

		internal int GameNumber
		{
			get
			{
				return this.gameNumber;
			}
		}

		internal string TeamAName
		{
			get
			{
				return this.teamAName;
			}
		}

		internal string TeamAShortName
		{
			get
			{
				return this.teamAShortName;
			}
		}

		internal string TeamBName
		{
			get
			{
				return this.teamBName;
			}
		}

		internal string TeamBShortName
		{
			get
			{
				return this.teamBShortName;
			}
		}

		internal string Period
		{
			get
			{
				return this.period;
			}
		}

		internal DateTime Moment
		{
			get
			{
				return this.moment;
			}
		}

		internal int ScoreTeamA
		{
			get
			{
				return this.scoreTeamA;
			}
		}

		internal int ScoreTeamB
		{
			get
			{
				return this.scoreTeamB;
			}
		}

		internal string Who
		{
			get
			{
				return this.who;
			}
		}
	}

	public class LinesScoreMessageGame : LinesInfoMessage
	{
		private int tournamentId;
		private readonly IEnumerable<Line> lines;
		private const char LINES_ATTRIBUTE_SEPARATOR = ',';
		private List<LineInfo> lineInfo;
		private int gameNumber;
		private string teamAName;
		private string teamAShortName;
		private string teamBName;
		private string teamBShortName;
		private int favorite;
		private DateTime startGameDate;
		private DateTime endGameDate;
		private int scoreTeamA;
		private int scoreTeamB;
		private string gradedBy;
		private DateTime gradeDate;
		private string league;
		private string sport;

		internal LinesScoreMessageGame(int tournamentId, int gameNumber, string teamAName, string teamAShortName, string teamBName, string teamBShortName, int favorite, DateTime startGameDate, DateTime endGameDate, int scoreTeamA, int scoreTeamB, string gradedBy, DateTime gradeDate, string league, string sport, IEnumerable<Line> lines)
		{
			//TODO se envia la info generica de los showcase que hubo a BI. a las 20:00... 
			//va afuera en el llamado de este metodo
			
			this.tournamentId = tournamentId;
			this.gameNumber = gameNumber;
			this.teamAName = teamAName;
			this.teamAShortName = teamAShortName;
			this.teamBName = teamBName;
			this.teamBShortName = teamBShortName;
			this.favorite = favorite;
			this.startGameDate = startGameDate;
			this.endGameDate = endGameDate;
			this.scoreTeamA = scoreTeamA;
			this.scoreTeamB = scoreTeamB;
			this.gradedBy = gradedBy;
			this.gradeDate = gradeDate;
			this.league = league;
			this.sport = sport;
			this.lines = lines;
		}

		public LinesScoreMessageGame(string message) : base(message)
		{

		}

		protected override void Deserialize(string[] gameAttributes, out int fieldOrder)
		{
			base.Deserialize(gameAttributes, out fieldOrder);
			var gradeType = (GradeLineType)int.Parse(gameAttributes[fieldOrder++]);
			if (gradeType != GradeLineType.GAME_LINES_INFO) throw new GameEngineException($"This message {gameAttributes[fieldOrder - 1]} is not for Game Line Info message.");

			this.tournamentId = int.Parse(gameAttributes[fieldOrder++]);
			this.gameNumber = int.Parse(gameAttributes[fieldOrder++]);
			this.teamAName = gameAttributes[fieldOrder++];
			this.teamAShortName = gameAttributes[fieldOrder++];
			this.teamBName = gameAttributes[fieldOrder++];
			this.teamBShortName = gameAttributes[fieldOrder++];
			this.favorite = int.Parse(gameAttributes[fieldOrder++]);
			this.startGameDate = new DateTime(
				int.Parse(gameAttributes[fieldOrder++]),
				int.Parse(gameAttributes[fieldOrder++]),
				int.Parse(gameAttributes[fieldOrder++]),
				int.Parse(gameAttributes[fieldOrder++]),
				int.Parse(gameAttributes[fieldOrder++]),
				int.Parse(gameAttributes[fieldOrder++])
			);
			this.endGameDate = new DateTime(
				int.Parse(gameAttributes[fieldOrder++]),
				int.Parse(gameAttributes[fieldOrder++]),
				int.Parse(gameAttributes[fieldOrder++]),
				int.Parse(gameAttributes[fieldOrder++]),
				int.Parse(gameAttributes[fieldOrder++]),
				int.Parse(gameAttributes[fieldOrder++])
			);
			this.scoreTeamA = int.Parse(gameAttributes[fieldOrder++]);
			this.scoreTeamB = int.Parse(gameAttributes[fieldOrder++]);
			this.gradedBy = gameAttributes[fieldOrder++];
			this.gradeDate = new DateTime(
				int.Parse(gameAttributes[fieldOrder++]),
				int.Parse(gameAttributes[fieldOrder++]),
				int.Parse(gameAttributes[fieldOrder++]),
				int.Parse(gameAttributes[fieldOrder++]),
				int.Parse(gameAttributes[fieldOrder++]),
				int.Parse(gameAttributes[fieldOrder++])
			);
			this.league = gameAttributes[fieldOrder++];
			this.sport = gameAttributes[fieldOrder++];

			this.lineInfo = new List<LineInfo>();
			while (fieldOrder < gameAttributes.Length)
			{
				LineInfo lineInfoArr = base.DeserializeLine(gameAttributes, ref fieldOrder);
				lineInfo.Add(lineInfoArr);
			}
		}

        protected override void InternalSerialize()
        {
			base.InternalSerialize();
			AddProperty((int)GradeLineType.GAME_LINES_INFO).
			AddProperty(this.tournamentId).
			AddProperty(this.gameNumber).
			AddProperty(this.teamAName).
			AddProperty(this.teamAShortName).
			AddProperty(this.teamBName).
			AddProperty(this.teamBShortName).
			AddProperty(this.favorite).
			AddProperty(this.startGameDate);
			AddProperty(this.endGameDate);
			AddProperty(this.scoreTeamA).
			AddProperty(this.scoreTeamB).
			AddProperty(this.gradedBy).
			AddProperty(this.gradeDate).
			AddProperty(this.league).
			AddProperty(this.sport);

			foreach (var line in lines)
			{
				line.Serialize(this);
			}
		}

        internal int TournamentId
		{
			get
			{
				return this.tournamentId;
			}
		}

		internal int GameNumber
		{
			get
			{
				return this.gameNumber;
			}
		}

		internal string League
		{
			get
			{
				return this.league;
			}
		}
		internal string Sport
		{
			get
			{
				return this.sport;
			}
		}


		internal string TeamAName
		{
			get
			{
				return this.teamAName;
			}
		}

		internal string TeamAShortName
		{
			get
			{
				return this.teamAShortName;
			}
		}

		internal string TeamBName
		{
			get
			{
				return this.teamBName;
			}
		}

		internal string TeamBShortName
		{
			get
			{
				return this.teamBShortName;
			}
		}

		internal int Favorite
		{
			get
			{
				return this.favorite;
			}
		}

		internal DateTime StartGameDate
		{
			get
			{
				return this.startGameDate;
			}
		}

		internal DateTime EndGameDate
		{
			get
			{
				return this.endGameDate;
			}
		}

		internal int ScoreTeamA
		{
			get
			{
				return this.scoreTeamA;
			}
		}

		internal int ScoreTeamB
		{
			get
			{
				return this.scoreTeamB;
			}
		}

		internal string GradedBy
		{
			get
			{
				return this.gradedBy;
			}
		}

		internal DateTime GradeDate
		{
			get
			{
				return this.gradeDate;
			}
		}

		internal IEnumerable<LineInfo> Lines
		{
			get
			{
				if (this.lineInfo == null) throw new GameEngineException("This method must be used only to deserialize a message");

				return this.lineInfo;
			}
		}

	}

	public class LineInfo
	{
		internal int LineId { get; set; }
		internal int Version { get; set; }
		internal GradingStatus Grading { get; set; }
		internal int QuestionId { get; set; }
	}

	public class ABLineInfo : LineInfo
	{
		internal int TeamAReward { get; set; }
		internal int TeamBReward { get; set; }
		internal char TheAnswer { get; set; }
	}

	public class MoneyLineInfo : ABLineInfo
	{

	}

	public class MoneyDrawLineInfo : ABLineInfo
	{
		internal int TieReward { get; set; }
	}

	public class SpreadLineInfo : ABLineInfo
	{
		internal double Spread { get; set; }
	}

	public class YesNoLineInfo : LineInfo
	{
		internal int YesReward { get; set; }
		internal int NoReward { get; set; }
		internal string Text { get; set; }
		internal char TheAnswer { get; set; }
	}

	public class OverUnderLineInfo : LineInfo
	{
		internal double Score { get; set; }
		internal int OverReward { get; set; }
		internal int UnderReward { get; set; }
		internal string Text { get; set; }
		internal char TheAnswer { get; set; }
	}

	public class FixedLineInfo : LineInfo
	{
		internal string TheAnswer { get; set; }
		internal string Text { get; set; }
		internal string[] Options { get; set; }
		internal int[] Rewards { get; set; }
	}

	public class TotalPointsLineInfo : LineInfo
	{
		internal double Score { get; set; }
		internal int OverReward { get; set; }
		internal int UnderReward { get; set; }
		internal char TheAnswer { get; set; }
	}

	internal enum GradeLineType { LOSER_WAGER, WINNER_WAGER, NOACTION_WAGER, GAME_LINES_INFO, LINE_VERSIONS, SCORES_PERIODS };

	public class LineWinnerInfo : LineMessage
	{
		public static LineWinnerInfo STREAM_STARTING = new LineWinnerInfo(LineMessage.STREAM_STARTING_ID);
		public static LineWinnerInfo STREAM_ENDING = new LineWinnerInfo(LineMessage.STREAM_ENDING_ID);

		private LineWinnerInfo(int streamMark) : base(GradeLineType.WINNER_WAGER, streamMark)
		{

		}

		internal LineWinnerInfo(int tournamentId, int gameNumber, string gradedBy, int lineVersion, int lineId, DateTime now, int authorizationId, string accountNumber, decimal risk, decimal toWin, string chosenAnswer, string fixedChosenAnswer, DateTime creation, GameboardStatus grading) : 
			base(GradeLineType.WINNER_WAGER, tournamentId, gameNumber, gradedBy, lineVersion, lineId, now, authorizationId, accountNumber, risk, toWin, chosenAnswer, fixedChosenAnswer, creation, grading)
		{

		}

		public LineWinnerInfo(string message) : base(message)
		{

		}

        protected override void InternalSerialize()
        {
			base.InternalSerialize();
		}
    }

	public class LineLoserInfo : LineMessage
	{
		public static LineLoserInfo STREAM_STARTING = new LineLoserInfo(LineMessage.STREAM_STARTING_ID);
		public static LineLoserInfo STREAM_ENDING = new LineLoserInfo(LineMessage.STREAM_ENDING_ID);

		private LineLoserInfo(int streamMark) : base(GradeLineType.LOSER_WAGER, streamMark)
		{

		}

		public LineLoserInfo(string msg) : base(msg)
		{

		}

		internal LineLoserInfo(int tournamentId, int gameNumber, string gradedBy, int lineVersion, int lineId, DateTime gradedDate, int authorizationId, string accountNumber, decimal risk, decimal toWin, string chosenAnswer, string fixedChosenAnswer, DateTime creationDate, GameboardStatus grading) : 
			base(GradeLineType.LOSER_WAGER, tournamentId, gameNumber, gradedBy, lineVersion, lineId, gradedDate, authorizationId, accountNumber, risk, toWin, chosenAnswer, fixedChosenAnswer, creationDate, grading)
		{

		}

        protected override void InternalSerialize()
        {
			base.InternalSerialize();
		}
    }

	public class LineNoActionInfo : LineMessage
	{
		public static LineNoActionInfo STREAM_STARTING = new LineNoActionInfo(LineMessage.STREAM_STARTING_ID);
		public static LineNoActionInfo STREAM_ENDING = new LineNoActionInfo(LineMessage.STREAM_ENDING_ID);

		private LineNoActionInfo(int streamMark) : base(GradeLineType.NOACTION_WAGER, streamMark)
		{

		}

		internal LineNoActionInfo(int tournamentId, int gameNumber, string gradedBy, int lineVersion, int lineId, DateTime now, int authorizationId, string accountNumber, decimal risk, decimal toWin, string chosenAnswer, string fixedChosenAnswer, DateTime creation, GameboardStatus grading) : 
			base(GradeLineType.NOACTION_WAGER, tournamentId, gameNumber, gradedBy, lineVersion, lineId, now, authorizationId, accountNumber, risk, toWin, chosenAnswer, fixedChosenAnswer, creation, grading)
		{

		}

		public LineNoActionInfo(string message) : base(message)
		{

		}

        protected override void InternalSerialize()
        {
			base.InternalSerialize();
		}
    }

	public abstract class LineMessage : KafkaMessage
	{
		internal int TournamentId { get; private set; }
		internal int GameNumber { get; private set; }
		internal string GradedBy { get; private set; }
		internal int LineVersion { get; private set; }
		internal int LineId { get; private set; }
		internal DateTime GradedDate { get; private set; }
		internal int AuthorizationId { get; private set; }
		internal string AccountNumber { get; private set; }
		internal decimal Risk { get; private set; }
		internal decimal ToWin { get; private set; }
		internal string ChosenAnswer { get; private set; }
		internal string FixedChosenAnswer { get; private set; }
		internal DateTime CreationDate { get; private set; }
		internal GameboardStatus Grading { get; private set; }

		public const string NO_DATA = "N/A";
		protected const int STREAM_STARTING_ID = Int32.MinValue + 13;
		protected const int STREAM_ENDING_ID = Int32.MinValue + 17;

		internal GradeLineType GradeType { get; private set; }

		internal LineMessage(GradeLineType gradeType, int streamMark)
		{
			if(streamMark != STREAM_STARTING_ID && streamMark != STREAM_ENDING_ID) throw new GameEngineException("Poison message must be an starting or ending message");

			if (streamMark == STREAM_STARTING_ID)
			{
				LineVersion = streamMark;
				AuthorizationId = streamMark;
			}
			else if (streamMark == STREAM_ENDING_ID)
			{
				TournamentId = streamMark;
				GameNumber = streamMark;
			}
			GradeType = gradeType;
		}

		internal LineMessage(GradeLineType gradeType, int tournamentId, int gameNumber, string gradedBy, int lineVersion, int lineId, DateTime gradedDate, int authorizationId, string accountNumber, decimal risk, decimal toWin, string chosenAnswer, string fixedChosenAnswer, DateTime creationDate, GameboardStatus gradingStatus)
		{
			GradeType = gradeType;
			TournamentId = tournamentId;
			GameNumber = gameNumber;
			LineId = lineId;
			LineVersion = lineVersion;
			AuthorizationId = authorizationId;
			GradedBy = gradedBy;
			GradedDate = gradedDate;
			AccountNumber = accountNumber;
			Risk = risk;
			ToWin = toWin;
			CreationDate = creationDate;
			Grading = gradingStatus;
			ChosenAnswer = string.IsNullOrWhiteSpace(chosenAnswer) ? NO_DATA : chosenAnswer;
			FixedChosenAnswer = string.IsNullOrWhiteSpace(fixedChosenAnswer) ? NO_DATA : fixedChosenAnswer;
		}

		public LineMessage(string message) : base(message)
		{

		}

		internal void InitMessage(int tournamentId, int gameNumber, int lineId)
		{
			var startMark = LineVersion;
			if (startMark != STREAM_STARTING_ID) throw new GameEngineException("Poison message must be an starting message");

			this.TournamentId = tournamentId;
			this.GameNumber = gameNumber;
			this.LineId = lineId;
		}

		protected override void InternalSerialize()
		{
			base.InternalSerialize();
			if (IsStreamStarting())
			{
				AddProperty((int)GradeType).
				AddProperty(TournamentId).
				AddProperty(GameNumber).
				AddProperty(LineId).
				AddProperty(LineVersion).
				AddProperty(AuthorizationId);
			}
			else if (IsStreamEnding())
			{
				AddProperty((int)GradeType).
				AddProperty(TournamentId).
				AddProperty(GameNumber);
			}
			else
			{
				AddProperty((int)GradeType).
				AddProperty(TournamentId).
				AddProperty(GameNumber).
				AddProperty(LineId).
				AddProperty(LineVersion).
				AddProperty(AuthorizationId).
				AddProperty(GradedBy).
				AddProperty(GradedDate).
				AddProperty(AccountNumber).
				AddProperty(Risk).
				AddProperty(ToWin).
				AddProperty(ChosenAnswer).
				AddProperty(FixedChosenAnswer).
				AddProperty(CreationDate).
				AddProperty((int)Grading);
			}
		}

		protected override void Deserialize(string[] loserOrWinnerAttributes, out int fieldOrder)
		{
			base.Deserialize(loserOrWinnerAttributes, out fieldOrder);
			GradeType = (GradeLineType)int.Parse(loserOrWinnerAttributes[fieldOrder++]);
			TournamentId = int.Parse(loserOrWinnerAttributes[fieldOrder++]);
			GameNumber = int.Parse(loserOrWinnerAttributes[fieldOrder++]);
			if (IsStreamEnding())
			{

			} else
			{
				LineId = int.Parse(loserOrWinnerAttributes[fieldOrder++]);
				LineVersion = int.Parse(loserOrWinnerAttributes[fieldOrder++]);
				AuthorizationId = int.Parse(loserOrWinnerAttributes[fieldOrder++]);

				if (IsStreamStarting())
				{

				}
				else
				{
					GradedBy = loserOrWinnerAttributes[fieldOrder++];
					GradedDate = new DateTime(int.Parse(loserOrWinnerAttributes[fieldOrder++]), int.Parse(loserOrWinnerAttributes[fieldOrder++]),
										int.Parse(loserOrWinnerAttributes[fieldOrder++]), int.Parse(loserOrWinnerAttributes[fieldOrder++]),
										int.Parse(loserOrWinnerAttributes[fieldOrder++]), int.Parse(loserOrWinnerAttributes[fieldOrder++]));
					AccountNumber = loserOrWinnerAttributes[fieldOrder++];
					Risk = decimal.Parse(loserOrWinnerAttributes[fieldOrder++]);
					ToWin = decimal.Parse(loserOrWinnerAttributes[fieldOrder++]);
					ChosenAnswer = loserOrWinnerAttributes[fieldOrder++];
					FixedChosenAnswer = loserOrWinnerAttributes[fieldOrder++];
					CreationDate = new DateTime(int.Parse(loserOrWinnerAttributes[fieldOrder++]), int.Parse(loserOrWinnerAttributes[fieldOrder++]),
											int.Parse(loserOrWinnerAttributes[fieldOrder++]), int.Parse(loserOrWinnerAttributes[fieldOrder++]),
											int.Parse(loserOrWinnerAttributes[fieldOrder++]), int.Parse(loserOrWinnerAttributes[fieldOrder++]));
					Grading = (GameboardStatus)int.Parse(loserOrWinnerAttributes[fieldOrder++]);
				}
			}
			
		}

		public bool IsStreamStarting()
		{
			var result = LineVersion == STREAM_STARTING_ID && AuthorizationId == STREAM_STARTING_ID;
			return result;
		}

		public bool IsStreamEnding()
		{
			var result = TournamentId == STREAM_ENDING_ID && GameNumber == STREAM_ENDING_ID;
			return result;
		}


	}
}
