﻿using ExchangeAPI.ExchangeEntities;
using GamesEngine.Exchange.Batch;
using GamesEngine.Finance;
using GamesEngine.Settings;
using System;
using System.Collections.Generic;
using Currency = GamesEngine.Finance.Currency;

namespace GamesEngine.Exchange
{
	public class BalanceMessage : KafkaMessage
	{
		internal BalanceMessage(int balanceType, DateTime date, List<BatchBalancesMessage> details)
		{
			Type = balanceType;
			Date = date;
			Details = details;
		}

		public BalanceMessage(string message) : base(message)
		{
		}

		public List<BatchBalancesMessage> Details { get; internal set; }
		public DateTime Date { get; internal set; }
		public int Type { get; internal set; }


		protected override void Deserialize(string [] serializedMessage, out int fieldOrder)
		{
			base.Deserialize(serializedMessage, out fieldOrder);
			Type = int.Parse(serializedMessage[fieldOrder++]);
			int year = int.Parse(serializedMessage[fieldOrder++]);
			int mounth = int.Parse(serializedMessage[fieldOrder++]);
			int day = int.Parse(serializedMessage[fieldOrder++]);
			int hour = int.Parse(serializedMessage[fieldOrder++]);
			int minute = int.Parse(serializedMessage[fieldOrder++]);
			int seconds = int.Parse(serializedMessage[fieldOrder++]);
			Date = new DateTime(year, mounth, day, hour, minute, seconds);
			if (Details == null) Details = new List<BatchBalancesMessage>();
			for (; fieldOrder < serializedMessage.Length;)
			{
				Details.Add(
					new BatchBalancesMessage()
					{
						CurrencyId = int.Parse(serializedMessage[fieldOrder++]),
						Initial = decimal.Parse(serializedMessage[fieldOrder++]),
						Available = decimal.Parse(serializedMessage[fieldOrder++]),
						Locked = decimal.Parse(serializedMessage[fieldOrder++]),
						LockedAccumulated = decimal.Parse(serializedMessage[fieldOrder++]),
						SpendAccumulated = decimal.Parse(serializedMessage[fieldOrder++])
					}
				);
			}
		}

        protected override void InternalSerialize()
        {
			base.InternalSerialize();
			AddProperty(Type).
			AddProperty(Date);

			foreach (BatchBalancesMessage message in Details)
			{
				AddProperty(message.CurrencyId).
				AddProperty(message.Initial).
				AddProperty(message.Available).
				AddProperty(message.Locked).
				AddProperty(message.LockedAccumulated).
				AddProperty(message.SpendAccumulated);
			}
		}

        public class BatchBalancesMessage
		{
			public int CurrencyId { get; internal set; }
			public decimal Initial { get; internal set; }
			public decimal Available { get; internal set; }
			public decimal Locked { get; internal set; }
			public decimal LockedAccumulated { get; internal set; }
			public decimal SpendAccumulated { get; internal set; }
			public int BatchNumber { get; internal set; }

			
		}
	}
	internal abstract class Balances
	{
		internal enum Types { NONE = 0, IN =1, OUT=2};
		protected readonly BatchAccounts accounts;
		internal Types BalanceType { get;}
		internal SetOfBalances InitialAccount { get; set; }

		protected Balances(int batchNumber, Balances.Types balanceType, SetOfBalances initialAccount)
		{
			this.accounts = new BatchAccounts(batchNumber);
			BalanceType = balanceType;
			InitialAccount = initialAccount;
		}

		internal BalanceMessage CreateMessage(DateTime  date)
		{
			return new BalanceMessage(
				(int)BalanceType,
				date,
				accounts.CreateMessage()
			);
		}

		internal bool InitialAccountItsNotConfiguredFor(Coin code)
		{
			return InitialAccount == null || InitialAccount.ItsConfiguredNotFor(code);
		}
		internal bool InitialAccountItsNotConfiguredFor()
		{
			return InitialAccount == null;
		}
	}
	
	
	internal class InBalances : Balances
	{
		internal InBalances(int batchNumber, SetOfBalances initialAccount) : base(batchNumber, Types.IN, initialAccount)
		{
		}

		internal void Deposit(Currency amount, string employeeName)
		{
			if (InitialAccountItsNotConfiguredFor(amount.Coin)) throw new GameEngineException($"There are no {nameof(SetOfBalances)} for the marketplace yet.");

			//TODO como manejar la auditoria... Como guardar quien lo creo y quien lo deposito
			BatchAccount result = FindAccountByCurrency(amount.Coin);
			result.Credit(amount);
		}

		internal void WithDraw(Currency amount, string employeeName)
		{
			if (InitialAccountItsNotConfiguredFor(amount.Coin)) throw new GameEngineException($"There are no {nameof(SetOfBalances)} for the marketplace yet.");

			BatchAccount result = FindAccountByCurrency(amount.Coin);
			result.Lock(amount);
		}

		internal Currency Available(Coin currencyCode)
		{
			BatchAccount result = FindAccountByCurrency(currencyCode);
			return result.Available;
		}

		internal BatchAccount FindAccountByCurrency(Coin currencyCode)
		{
			return accounts.FindAccountByCurrency(currencyCode);
		}

		internal IEnumerable<BatchAccount> Accounts()
		{
			var result = this.accounts.ListAccounts();
			return result;
		}

		internal InBalances Lock(Currency amount, Transaction transaction, BatchMovements movements, BatchMovements parentMovements)
		{
			if (InitialAccountItsNotConfiguredFor(amount.Coin)) throw new GameEngineException($"There are no {nameof(SetOfBalances)} for the marketplace yet.");

			BatchAccount result = FindAccountByCurrency(amount.Coin);
			result.Lock(amount);

			movements.CreateNewLockMovement(transaction.Id, amount, transaction.TransactionDate, Balances.Types.IN);
			parentMovements.CreateNewLockMovement(transaction.Id, amount, transaction.TransactionDate, Balances.Types.IN);

			return this;
		}

		internal InBalances Lock(Currency amount)
		{
			if (InitialAccountItsNotConfiguredFor(amount.Coin)) throw new GameEngineException($"There are no {nameof(SetOfBalances)} for the marketplace yet.");

			BatchAccount result = FindAccountByCurrency(amount.Coin);
			result.Lock(amount);

			return this;
		}

		internal InBalances Debit(Currency amount)
		{
			if (InitialAccountItsNotConfiguredFor(amount.Coin)) throw new GameEngineException($"There are no {nameof(SetOfBalances)} for the marketplace yet.");

			BatchAccount result = FindAccountByCurrency(amount.Coin);
			result.Debit(amount);

			return this;
		}

		internal InBalances Unlock(Currency amount)
		{
			if (InitialAccountItsNotConfiguredFor(amount.Coin)) throw new GameEngineException($"There are no {nameof(SetOfBalances)} for the marketplace yet.");

			BatchAccount result = FindAccountByCurrency(amount.Coin);
			result.Unlock(amount);

			return this;
		}

		internal InBalances UnLock(Currency amount, Transaction transaction, BatchMovements movements, BatchMovements parentMovements)
		{
			if (InitialAccountItsNotConfiguredFor(amount.Coin)) throw new GameEngineException($"There are no {nameof(SetOfBalances)} for the marketplace yet.");

			BatchAccount result = FindAccountByCurrency(amount.Coin);
			result.Unlock(amount);

			movements.CreateNewUnLockMovement(transaction.Id, amount, transaction.TransactionDate, Balances.Types.IN);
			parentMovements.CreateNewUnLockMovement(transaction.Id, amount, transaction.TransactionDate, Balances.Types.IN);

			return this;
		}

		internal InBalances Accredit(Currency amount, Transaction transaction, BatchMovements movements, BatchMovements parentMovements)
		{
			if (InitialAccountItsNotConfiguredFor(amount.Coin)) throw new GameEngineException($"There are no {nameof(SetOfBalances)} for the marketplace yet.");

			BatchAccount result = FindAccountByCurrency(amount.Coin);
			result.Credit(amount);

			movements.CreateNewCreditMovement(transaction.Id, amount, transaction.TransactionDate, Balances.Types.IN);
			parentMovements.CreateNewCreditMovement(transaction.Id, amount, transaction.TransactionDate, Balances.Types.IN);

			return this;
		}

		internal InBalances Accredit(Currency amount)
		{
			if (InitialAccountItsNotConfiguredFor(amount.Coin)) throw new GameEngineException($"There are no {nameof(SetOfBalances)} for the marketplace yet.");

			BatchAccount result = FindAccountByCurrency(amount.Coin);
			result.Credit(amount);

			return this;
		}

		internal InBalances Debit(Currency amount, Transaction transaction, BatchMovements movements, BatchMovements parentMovements)
		{
			if (InitialAccountItsNotConfiguredFor(amount.Coin)) throw new GameEngineException($"There are no {nameof(SetOfBalances)} for the marketplace yet.");

			BatchAccount result = FindAccountByCurrency(amount.Coin);
			result.Debit(amount);

			movements.CreateNewDebitMovement(transaction.Id, amount, transaction.TransactionDate, Balances.Types.IN);
			parentMovements.CreateNewDebitMovement(transaction.Id, amount, transaction.TransactionDate, Balances.Types.IN);

			return this;
		}
	}
}
