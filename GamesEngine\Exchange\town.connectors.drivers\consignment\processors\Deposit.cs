﻿using Connectors.town.connectors.driver.transactions;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Serialization;
using town.connectors;
using town.connectors.drivers;
using static town.connectors.drivers.Result;

namespace Connectors.town.connectors.drivers.consignment
{
    public class Deposit: ConsignmentProcessorDriver
    {
        public Deposit() : base(TransactionType.Deposit)
        {

        }

        public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            if (CustomSettings.ThereArePendingChanges)
            {
                ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
                SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
                Password = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
            }

            DepositTransaction result;
            var authorizationNumber = AddDeposit(recordSet);
            if (authorizationNumber == FAKE_DOCUMENT_NUMBER) result = new DepositTransaction(FAKE_DOCUMENT_NUMBER, TransactionStatus.DENIED, Entity, PaymentMethod, CurrencyIsoCodes);
            else result = new DepositTransaction(authorizationNumber, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes);
            return (T)Convert.ChangeType(result, typeof(T));
        }

        public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            if (CustomSettings.ThereArePendingChanges)
            {
                ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
                SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
                Password = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
            }

            DepositTransaction result;
            var authorizationNumber = await AddDepositAsync(recordSet);
            if (authorizationNumber == FAKE_DOCUMENT_NUMBER) result = new DepositTransaction(FAKE_DOCUMENT_NUMBER, TransactionStatus.DENIED, Entity, PaymentMethod, CurrencyIsoCodes);
            else result = new DepositTransaction(authorizationNumber, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes);
            return (T)Convert.ChangeType(result, typeof(T));
        }

        async Task<int> AddDepositAsync(CustomSettings.RecordSet recordSet)
        {
            ResponseDepositEnvelope responseDeposit = null;
            string responseString = string.Empty;
            string valuesWithHiddenFields = string.Empty;
            var url = $"{ServicesUrl}?op=Add_deposit";
            try
            {
                string xmlString = CreateSoapEnvelope(recordSet, out valuesWithHiddenFields);
                var webRequest = new HttpRequestMessage(HttpMethod.Post, url)
                {
                    Content = new StringContent(xmlString, Encoding.UTF8, "text/xml")
                };

                Loggers.GetIntance().AccountingConsignmentDeposit.Debug($@"url:{url} data:{valuesWithHiddenFields}");
                using (HttpResponseMessage response = await httpClient.SendAsync(webRequest, HttpCompletionOption.ResponseHeadersRead))
                {
                    responseString = await response.Content.ReadAsStringAsync();
                }
                Loggers.GetIntance().AccountingConsignmentDeposit.Debug($@"response:{responseString}");

                XmlSerializer xmls = new XmlSerializer(typeof(ResponseDepositEnvelope));
                responseDeposit = (ResponseDepositEnvelope)xmls.Deserialize(new StringReader(responseString));
            }
            catch (Exception e)
            {
                Loggers.GetIntance().AccountingConsignmentDeposit.Error($@"url:{url} data:{valuesWithHiddenFields} type:{e.GetType()} error:{e.Message}", e);

                var extraErrorMessage = string.Empty;
                InternalOnError(nameof(AddDeposit), 1, e, $"Url:{url}", $"Request: {valuesWithHiddenFields}", $"Response: {responseString}");
            }

            if (responseDeposit == null || string.IsNullOrWhiteSpace(responseDeposit.Body.Add_depositResponse.Add_depositResult))
            {
                NotifyWarn(nameof(AddDeposit), $"Url:{url}\nResponse: {responseString}", $"Response cannot be empty");
            }
            else
            {
                var isNumeric = int.TryParse(responseDeposit.Body.Add_depositResponse.Add_depositResult, out int authorizationNumber);
                if (isNumeric) return authorizationNumber;
                else NotifyWarn(nameof(AddDeposit), $"Url:{url}\nResponse: {responseString}", "No authorization number found");
            }

            return FAKE_DOCUMENT_NUMBER;
        }

        int AddDeposit(CustomSettings.RecordSet recordSet)
        {
            ResponseDepositEnvelope responseDeposit = null;
            string responseString = string.Empty;
            string valuesWithHiddenFields = string.Empty;
            var url = $"{ServicesUrl}?op=Add_deposit";
            try
            {
                string xmlString = CreateSoapEnvelope(recordSet, out valuesWithHiddenFields);
                var webRequest = new HttpRequestMessage(HttpMethod.Post, url)
                {
                    Content = new StringContent(xmlString, Encoding.UTF8, "text/xml")
                };

                Loggers.GetIntance().AccountingConsignmentDeposit.Debug($@"url:{url} data:{valuesWithHiddenFields}");
                var response = httpClient.Send(webRequest);
                using var reader = new StreamReader(response.Content.ReadAsStream());
                responseString = reader.ReadToEnd();
                Loggers.GetIntance().AccountingConsignmentDeposit.Debug($@"response:{responseString}");

                XmlSerializer xmls = new XmlSerializer(typeof(ResponseDepositEnvelope));
                responseDeposit = (ResponseDepositEnvelope)xmls.Deserialize(new StringReader(responseString));
            }
            catch (Exception e)
            {
                Loggers.GetIntance().AccountingConsignmentDeposit.Error($@"url:{url} data:{valuesWithHiddenFields} type:{e.GetType()} error:{e.Message}", e);

                var extraErrorMessage = string.Empty;
                InternalOnError(nameof(AddDeposit), 1, e, $"Url:{url}", $"Request: {valuesWithHiddenFields}", $"Response: {responseString}");
            }

            if (responseDeposit == null || string.IsNullOrWhiteSpace(responseDeposit.Body.Add_depositResponse.Add_depositResult))
            {
                NotifyWarn(nameof(AddDeposit), $"Url:{url}\nResponse: {responseString}", $"Response cannot be empty");
            }
            else
            {
                var isNumeric = int.TryParse(responseDeposit.Body.Add_depositResponse.Add_depositResult, out int authorizationNumber);
                if (isNumeric) return authorizationNumber;
                else NotifyWarn(nameof(AddDeposit), $"Url:{url}\nResponse: {responseString}", "No authorization number found");
            }
            
            return FAKE_DOCUMENT_NUMBER;
        }

        string CreateSoapEnvelope(CustomSettings.RecordSet recordSet, out string xmlWithHiddenFields)
        {
            var customerId = recordSet.Mappings["customerId"].AsString;
            var sendersName = recordSet.Mappings["sendersName"].AsString;
            var country = recordSet.Mappings["country"].AsString;
            var state = recordSet.Mappings["state"].AsString;
            var city = recordSet.Mappings["city"].AsString;
            var controlNum = recordSet.Mappings["controlNum"].AsString;
            var amount = recordSet.Mappings["amount"].AsDecimal;
            var readyForProcessing = recordSet.Mappings["readyForProcessing"].AsString;
            var providerId = recordSet.Mappings["Input$Provider"].AsInt;

            var xmlBuilder = new StringBuilder();
            xmlBuilder.AppendLine("<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">");
            xmlBuilder.AppendLine("<soap:Body>");
            xmlBuilder.AppendLine("<Add_deposit xmlns=\"http://tempuri.org/\">");
            xmlBuilder.Append("<username>").Append(SystemId).AppendLine("</username>");
            xmlBuilder.Append("<password>").Append(Password).AppendLine("</password>");
            xmlBuilder.Append("<merchId>").Append(customerId).AppendLine("</merchId>");
            xmlBuilder.Append("<sendersName>").Append(sendersName).AppendLine("</sendersName>");
            xmlBuilder.Append("<country>").Append(country).AppendLine("</country>");
            xmlBuilder.Append("<state>").Append(state).AppendLine("</state>");
            xmlBuilder.Append("<city>").Append(city).AppendLine("</city>");
            xmlBuilder.Append("<controlNum>").Append(controlNum).AppendLine("</controlNum>");
            xmlBuilder.Append("<amount>").Append(amount).AppendLine("</amount>");
            xmlBuilder.Append("<readyForProcessing>").Append(readyForProcessing).AppendLine("</readyForProcessing>");
            xmlBuilder.Append("<provider>").Append(providerId).AppendLine("</provider>");
            xmlBuilder.AppendLine("</Add_deposit>");
            xmlBuilder.AppendLine("</soap:Body>");
            xmlBuilder.AppendLine("</soap:Envelope>");

            var xmlBuilderWithHiddenFields = new StringBuilder();
            xmlBuilderWithHiddenFields.AppendLine("<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">");
            xmlBuilderWithHiddenFields.AppendLine("<soap:Body>");
            xmlBuilderWithHiddenFields.AppendLine("<Add_deposit xmlns=\"http://tempuri.org/\">");
            xmlBuilderWithHiddenFields.AppendLine("<username>XXXX</username>");
            xmlBuilderWithHiddenFields.AppendLine("<password>XXXX</password>");
            xmlBuilderWithHiddenFields.Append("<merchId>").Append(customerId).AppendLine("</merchId>");
            xmlBuilderWithHiddenFields.Append("<sendersName>").Append(sendersName).AppendLine("</sendersName>");
            xmlBuilderWithHiddenFields.Append("<country>").Append(country).AppendLine("</country>");
            xmlBuilderWithHiddenFields.Append("<state>").Append(state).AppendLine("</state>");
            xmlBuilderWithHiddenFields.Append("<city>").Append(city).AppendLine("</city>");
            xmlBuilderWithHiddenFields.Append("<controlNum>").Append(controlNum).AppendLine("</controlNum>");
            xmlBuilderWithHiddenFields.Append("<amount>").Append(amount).AppendLine("</amount>");
            xmlBuilderWithHiddenFields.Append("<readyForProcessing>").Append(readyForProcessing).AppendLine("</readyForProcessing>");
            xmlBuilderWithHiddenFields.Append("<provider>").Append(providerId).AppendLine("</provider>");
            xmlBuilderWithHiddenFields.AppendLine("</Add_deposit>");
            xmlBuilderWithHiddenFields.AppendLine("</soap:Body>");
            xmlBuilderWithHiddenFields.AppendLine("</soap:Envelope>");

            xmlWithHiddenFields = xmlBuilderWithHiddenFields.ToString();
            return xmlBuilder.ToString();
        }

        [XmlRoot("Envelope", Namespace = "http://schemas.xmlsoap.org/soap/envelope/")]
        public class ResponseDepositEnvelope
        {
            public ResponseDepositBody Body { get; set; }
        }

        public class ResponseDepositBody
        {
            [XmlElement(ElementName = "Add_depositResponse", Namespace = "http://tempuri.org/")]
            public AddDepositResponse Add_depositResponse { get; set; }
        }

        public class AddDepositResponse
        {
            public string Add_depositResult { get; set; }
        }

        public override void Prepare(DateTime now)
        {
            CustomSettings.AddVariableParameter("customerId");
            CustomSettings.AddVariableParameter("sendersName");
            CustomSettings.AddVariableParameter("country");
            CustomSettings.AddVariableParameter("state");
            CustomSettings.AddVariableParameter("city");
            CustomSettings.AddVariableParameter("controlNum");
            CustomSettings.AddVariableParameter("amount");
            CustomSettings.AddVariableParameter("readyForProcessing");
            CustomSettings.AddVariableParameter("Input$Provider");

            SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
            Password = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
            ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
        }
    }
}
