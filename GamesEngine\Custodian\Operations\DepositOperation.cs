﻿using GamesEngine.Domains;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using GamesEngine.Settings;
using System;
using System.Runtime.CompilerServices;
using town.connectors.drivers;
using static GamesEngine.Business.WholePaymentProcessor;
using static GamesEngine.Custodian.Operations.Operation;
using static town.connectors.drivers.Result;

[assembly: InternalsVisibleTo("GuardianAPI")]
namespace GamesEngine.Custodian.Operations
{
	using Company = GamesEngine.Business.Company;

	internal abstract class Deposit : Operation
	{
		internal StatusCodes Status { get; }
		internal Guardian Guardian { get; }
		internal AccountNumber ProcessorAccount { get; }
		internal DateTime CreationDate { get; }
		internal int AuthorizationId { get; }
		internal Currency Amount { get; }
		internal int Group { get; }
		internal PaymentProcessor Processor { get; }
		internal Domain Domain { get; }
		internal string Description { get; }
		internal string AccountNumber { get; }
		internal string Identifier { get; }
		internal TransactionType Abbreviation { get; }

		internal Deposit(Guardian guardian, DateTime creationDate, int transactionId, int authorizationId, string description, Currency amount, int group, PaymentProcessor processor, Domain domain, 
			StatusCodes status, string identificationDocumentNumber, string accountNumber)
	: base(transactionId)
		{
			if (guardian == null) throw new ArgumentNullException(nameof(guardian));
			if (transactionId <= 0) throw new GameEngineException($"{nameof(transactionId)} must be greater than 0");
			if (authorizationId <= 0) throw new GameEngineException($"{nameof(authorizationId)} must be greater than 0");
			if (amount == null) throw new ArgumentNullException(nameof(amount));
			if (group <= 0) throw new GameEngineException($"{nameof(group)} must be greater than 0");
			if (processor == null) throw new ArgumentNullException(nameof(processor));
			if (domain == null) throw new ArgumentNullException(nameof(domain));
			if (string.IsNullOrEmpty(identificationDocumentNumber)) throw new ArgumentNullException(nameof(identificationDocumentNumber));
			if (string.IsNullOrEmpty(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

			Guardian = guardian;
			ProcessorAccount = guardian.Accounts().SearchBy(processor);
			CreationDate = creationDate;
			AuthorizationId = authorizationId;
			Amount = amount;
			Group = group;
			Processor = processor;
			Domain = domain;
			Status = status;
			Description = description;
			Abbreviation = TransactionType.Deposit;
			AccountNumber = accountNumber;
			Identifier = identificationDocumentNumber;
		}

		internal abstract InternalOperationUpdateMessage GenerateMessage(DateTime now);

		internal Company Company
		{
			get
			{
				return this.Guardian.Company;
			}
		}
	}


	internal sealed class DepositExecution
	{
		private CompleteDeposit completeDeposit;
		internal int Id { get; }
		internal decimal Amount { get { return completeDeposit.Amount.Value; } }
		internal DateTime ScheduledDate { get { return completeDeposit.CreationDate; } }
		internal int TransactionId { get { return completeDeposit.TransactionId; } }
		internal PaymentProcessor Processor { get { return completeDeposit.Processor; } }
		internal DisbursmentStatusCode Status { get; }
		internal AccountNumber Account { get; }

		internal DepositExecution(CompleteDeposit completeDeposit)
		{
			this.completeDeposit = completeDeposit;
			Id = 1;
			Account = new AccountNumber(1, completeDeposit.AccountNumber, completeDeposit.ProcessorAccount.ProcessorKey, completeDeposit.Amount.Coin);

			switch (completeDeposit.Status)
			{
				case StatusCodes.COMPLETE:
					Status = DisbursmentStatusCode.APPROVED;
					break;
				case StatusCodes.IN_PROCESS:
					Status = DisbursmentStatusCode.UNPAYED;
					break;
				case StatusCodes.PENDING:
					Status = DisbursmentStatusCode.UNPAYED;
					break;
				default:
					throw new GameEngineException($" {completeDeposit.Status} it's not supported.");
			}
		}

		internal KafkaMessage GenerateMessage(DateTime date, string approver, TransactionStatus status)
		{
			return new DisbursementExecutionMessage(
				completeDeposit.AuthorizationId,
				date,
				date,
				status,
				completeDeposit.TransactionId,
				Id,
				completeDeposit.Amount.Value,
				completeDeposit.Amount.Coin,
				approver,
				Account,
				Account.Coin
				);
		}
	}
}
