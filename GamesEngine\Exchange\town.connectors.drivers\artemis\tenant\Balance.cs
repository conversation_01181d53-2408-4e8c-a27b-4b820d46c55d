using RestSharp;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.commons;
using town.connectors.drivers;
using town.connectors.drivers.artemis;
using Puppeteer.EventSourcing;

namespace Connectors.town.connectors.drivers.artemis
{
    public class Balance : DGSTenantDriver
	{
        private RestClient _getBalanceClient;
        public Balance() : base(Tenant_Actions.Balance)
        {
        }
        public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            throw new NotImplementedException();
        }
        public override void Prepare(DateTime now)
        {
            CustomSettings.AddVariableParameter("customerId");

            //CustomSettings.Prepare();

            SystemId = CustomSettings.Get(now, "TokenSystemId").AsString;
            Password = CustomSettings.Get(now, "TokenSystemPassword").AsSecret.ToString();
            ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
        }
        public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
			if (_getBalanceClient == null)
			{
				if (string.IsNullOrWhiteSpace(SystemId)) throw new ArgumentNullException("Custom variable 'TokenSystemId' is required");
				if (string.IsNullOrWhiteSpace(Password)) throw new ArgumentNullException("Custom variable 'TokenSystemPassword' is required");
				if (string.IsNullOrWhiteSpace(ServicesUrl)) throw new ArgumentNullException("Custom variable 'CompanyBaseUrlServices' is required");

                if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken))
                {
                    await DGSTenantDriver.RequestAppTokenAsync(ServicesUrl, SystemId, Password);
                }

                _getBalanceClient = new RestClient(ServicesUrl);
			}

            if (CustomSettings.ThereArePendingChanges)
            {
                bool changeApplied = false;
                ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices", out changeApplied).AsString;
                SystemId = CustomSettings.Get(now, "TokenSystemId").AsString;
                Password = CustomSettings.Get(now, "TokenSystemPassword").AsSecret.ToString();

                if (changeApplied)
                {
                    if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken))
                    {
                        await DGSTenantDriver.RequestAppTokenAsync(ServicesUrl, SystemId, Password);
                    }

                    _getBalanceClient = new RestClient(ServicesUrl);
                }
            }
            var result = await AvailableBalanceAsync(now, recordSet);
			return (T)Convert.ChangeType(result, typeof(T));
		}

        private async Task<decimal> AvailableBalanceAsync(DateTime now, CustomSettings.RecordSet recordSet)
        {
            if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken)) throw new Exception($"{nameof(DGSProcessorDriver.AppToken)} is empty");

            var CustomerId = recordSet.Mappings["customerId"];
            string customerId = CustomerId.AsString;

            if (string.IsNullOrWhiteSpace(customerId)) throw new ArgumentNullException(nameof(customerId));

            string url = $"/players/{customerId}/balance";

            string responseString = "";
            int retryNumber = 0;

            while (true)
            {
                try
                {
                    var request = new RestRequest(url, Method.GET);
                    request.AddHeader("Authorization", $"Bearer {DGSProcessorDriver.AppToken}");
                    var response = await _getBalanceClient.ExecuteAsync(request);
                    responseString = response.Content;
                    if ((int)response.StatusCode != 200)
                    {
                        Loggers.GetIntance().AccountingServicesDGS.Debug($"{nameof(AvailableBalanceAsync)}\nUrl:{url}\nResponse: {responseString}");
                    }
                    break;
                }
                catch (Exception e)
                {
                    Loggers.GetIntance().AccountingServicesDGS.Error($@"url:{url} type:{e.GetType()} error:{e.Message}", e);

                    retryNumber++;

                    await Task.Delay(5000);
                    if (retryNumber == MAX_RETRIES) return 0;
                }
            }

            if (string.IsNullOrWhiteSpace(responseString))
            {
                NotifyWarn(nameof(AvailableBalanceAsync), $"Url:{url}\nResponse: {responseString}", $"Response can not be empty");
                return 0;
            }
            else
            {
                decimal amount = 0;

                try
                {
                    var errorResponse = Commons.FromJson<BalanceErrorResponse>(responseString);
                    if (errorResponse != null && (!string.IsNullOrWhiteSpace(errorResponse.detail) || !string.IsNullOrWhiteSpace(errorResponse.title)))
                    {
                        Loggers.GetIntance().AccountingServicesDGS.Debug($"{nameof(AvailableBalanceAsync)}\nUrl:{url}\nResponse: {responseString}");
                        return amount;
                    }
                }
                catch
                {
                    NotifyWarn(nameof(AvailableBalanceAsync), $"Url:{url}\nResponse: {responseString}", $"{nameof(responseString)} {responseString} is not possible to cast error");
                }

                try
                {
                    var responseObj = Commons.FromJson<NewPlayerBalanceResponse>(responseString);
                    if (responseObj != null)
                    {
                        amount = responseObj.realAvailableBalance;
                    }
                    else
                    {
                        var oldResponseObj = Commons.FromJson<PlayerBalanceBody>(responseString);
                        if (oldResponseObj != null)
                        {
                            amount = oldResponseObj.realAvailBalance;
                        }
                    }
                }
                catch (Exception e)
                {
                    NotifyWarn(nameof(AvailableBalanceAsync), $"Url:{url}\nResponse: {responseString}", $"{e.Message}\r{nameof(responseString)} {responseString} is not valid");
                }

                return amount;
            }
        }
    }
}
