﻿using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Text;

namespace GamesEngine.Accounting.JournalTemplates
{
	internal class JournalEntryLineTemplate:Objeto
	{
		private readonly int lineNumber;

		private readonly string accountNumber;
		internal string AccountNumber
		{
			get
			{
				return accountNumber;
			}
		}

		private readonly Parameter amount;
		internal string ParameterName => amount.Identifier;

		private readonly string description;
		internal string Description
		{
			get
			{
				return description;
			}
		}
		private readonly LineType lineType;
		internal bool IsDebit
		{
			get
			{
				return lineType == LineType.Debit;
			}
		}
		internal bool IsCredit
		{
			get
			{
				return lineType == LineType.Credit;
			}
		}

		private enum LineType
		{
			Debit = 0,
			Credit = 1
		}

		internal static JournalEntryLineTemplate Debit(JournalEntryTemplate template, int lineNumber, string accountNumber, string description, AmountParameter debit)
		{
			JournalEntryLineTemplate result = new JournalEntryLineTemplate(template, lineNumber, LineType.Debit, accountNumber, description, debit);
			return result;
		}

		internal static JournalEntryLineTemplate Credit(JournalEntryTemplate template, int lineNumber, string accountNumber, string description, AmountParameter credit)
		{
			JournalEntryLineTemplate result = new JournalEntryLineTemplate(template, lineNumber, LineType.Credit, accountNumber, description, credit);
			return result;
		}

		private JournalEntryLineTemplate(JournalEntryTemplate template, int lineNumber, LineType lineType, string accountNumber, string description, Parameter amount)
		{
			if (lineNumber <= 0) throw new GameEngineException($"Jornal Entry line number: {lineNumber} is not valid.");
			if (String.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));
			if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));
			if (amount == null) throw new ArgumentNullException(nameof(amount));

			this.lineNumber = lineNumber;
			this.accountNumber = accountNumber;
			this.description = description;

			this.amount = amount;
			this.lineType = lineType;

			CalculateDescriptionPlaceholders(template);
		}


		private List<Func<string>> ptrToDescriptionFragments;

		private void CalculateDescriptionPlaceholders(JournalEntryTemplate template)
        {
			ptrToDescriptionFragments = new List<Func<string>>();
			parseDescription(0, template);
		}

		private void parseDescription(int index, JournalEntryTemplate template)
        {
			if (index < description.Length)
			{
				if (description[index] == '{')
					parsePlaceholder(index, template);
				else
					parseText(index, template);
			}
		}

		private void parsePlaceholder(int index, JournalEntryTemplate template)
		{
			int leftBracket = this.description.IndexOf('{', index);
			if (leftBracket != -1)
			{
				int rightBracket = this.description.IndexOf('}', leftBracket + 1);
				if (rightBracket == -1) throw new GameEngineException("Description template mismatch right bracket");
				string placeholder = this.description.Substring(leftBracket+1, rightBracket - leftBracket-1);
				if (string.IsNullOrWhiteSpace(placeholder)) throw new GameEngineException("Invalid placeholder name. Placeholder can not be empty");
				if (placeholder.Trim() != placeholder) throw new GameEngineException("Invalid placeholder name. It can not contains white spaces");

				Argument argument = template.Parameters[placeholder].Argument;
				ptrToDescriptionFragments.Add(() => argument.ValueAsString);
				parseDescription(rightBracket + 1, template);
			}
		}

		private void parseText(int index, JournalEntryTemplate template)
        {
			string text;
			int leftBracket = this.description.IndexOf('{', index);
			if (leftBracket == -1)
            {
				text = this.description.Substring(index);
				ptrToDescriptionFragments.Add(() => text);
			}
			else
            {
				text = this.description.Substring(index, leftBracket-index);
				ptrToDescriptionFragments.Add(() => text);
				parseDescription(leftBracket, template);
			}
		}


		internal AccountingMovement GenerateJournalRow()
		{
			decimal amount = 0;

			switch (lineType)
			{
				case LineType.Debit:
					amount = this.amount.Argument.ValueAsDecimal;
					break;
				case LineType.Credit:
					amount = -this.amount.Argument.ValueAsDecimal;
					break;
			}

			StringBuilder description = new StringBuilder();
			foreach (Func<string> fragment in ptrToDescriptionFragments)
				description.Append(fragment());

			AccountingMovement result = new AccountingMovement(
				(byte)this.lineNumber,
				this.accountNumber, 
				amount, 
				description.ToString()
			);
			return result;
		}

		internal int LineNumber
		{
			get
			{
				return this.lineNumber;
			}
		}
	}
}
