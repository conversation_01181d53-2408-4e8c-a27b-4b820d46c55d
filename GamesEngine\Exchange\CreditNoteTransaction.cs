﻿using GamesEngine.Accounting.JournalTemplates;
using GamesEngine.Finance;
using GamesEngine.Settings;
using System;
using System.Collections.Generic;
using System.Text;
using town.connectors.drivers;

namespace GamesEngine.Exchange
{
    internal sealed class CreditNoteTransaction : Transaction
    {
		internal const int TEMPLATE_ID = 6;
		internal CreditNoteTransaction(TransactionDefinition transactionDefinition, IConversionSpread conversionSpread, string employeeName, Currency amount, DateTime now)
            : base(transactionDefinition, conversionSpread, amount, now,  TransactionType.CreditNote)
        {
			transactionDefinition.Batch.AddTransactionDenifition(this);
		}

		public static JournalEntryTemplate Template { get; private set; }
		internal static void InitializeTemplate(JournalEntryTemplates templates)
		{
			Template = templates.CreateTemplate(CreditNoteTransaction.TEMPLATE_ID, "Template for " + nameof(CreditNoteTransaction));
			var parameters = Template.Parameters;

			parameters.AddTextParameter("TransactionId");
			parameters.AddTextParameter("Date");
			parameters.AddTextParameter("CustomerNumber");
			parameters.AddTextParameter("AccountNumber");
			parameters.AddTextParameter("Currency");
			parameters.AddAmountParameter("SaleRate");
			parameters.AddAmountParameter("PurchaseRate");
			parameters.AddAmountParameter("CurrencyCost");
			parameters.AddAmountParameter("TotalAmount");

			Template.AddDebit("100-01", "Dummy Credit Note Transaction {TransactionId}", "TotalAmount");
			Template.AddCredit("200-01", "Dummy Credit Note Transaction {TransactionId}", "TotalAmount");
		}

		protected override void AfterApprove(DateTime date, bool itsThePresent, Currency gross, Currency comission, Currency profit, Currency net, Currency amountToCustomer, string employeeName, TransactionCompleted transactionCompleted, int journalEntryNumber)
        {
			if (!itsThePresent) return;

			CustomerAccount toAccount = TransactionDefinition.Account;
			string description = $"Credit note {amountToCustomer.CurrencyCode} {amountToCustomer.Value} to {toAccount.Identificator}";
			
			SendDepositMessage(
				itsThePresent,
				toAccount.CurrencyCode.ToString(),
				toAccount.CustomerAccountNumber,
				amountToCustomer.Value,
				description,
				this.Id,
				employeeName,
				toAccount.Identificator,
				ProcessorAccountId,
				PaymentChannels.Agents.INSIDER
				);

			if (Integration.UseKafka || Integration.UseKafkaForAuto)
			{
				if (base.JournalEntryTemplate.IsActive)//TODO: no specific template for this transaction type
				{
					var currencyCost = TransactionDefinition.Batch.CalculateCost(amountToCustomer);
					var saleRate = ConversionSpread is NoConversionSpread ? 0m : ConversionSpread.SaleRate.Price;
					var purchaseRate = ConversionSpread is NoConversionSpread ? 0m : ConversionSpread.PurchaseRate.Price;
					Integration.Kafka.Send(
						itsThePresent,
						$"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForTransacctions}",
						new JournalEntryForCreditNoteMessage(
							base.Id,
							date,
							journalEntryNumber,
							employeeName,
							saleRate,
							purchaseRate,
							toAccount.CustomerAccountNumber,
							toAccount.Identificator,
							toAccount.Coin,
							amountToCustomer.Value,
							currencyCost.Value
						)
					);
				}

				var authorizationId = 0; //TODO
				Integration.Kafka.Send(
					itsThePresent,
					$"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForTransacctions}",
					new ApprovedTransactionMessage(TransactionType.CreditNote, Id, date, profit.Value, net.Value, amountToCustomer.Value, gross.Coin, amountToCustomer.Coin, employeeName, authorizationId, ProcessorAccountId)
				);
			}
		}

        internal override void AfterDeny(DateTime date, bool itsThePresent, string employeeName, string reason)
        {
			TransactionDefinition.Deny(this);

			if (!itsThePresent) return;

			if (Integration.UseKafka || Integration.UseKafkaForAuto)
			{
				Integration.Kafka.Send(
					itsThePresent,
					$"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForTransacctions}",
					new DeniedTransactionMessage(TransactionType.CreditNote, Id, date, reason, employeeName)
				);
			}
		}

		internal override bool isSameCurrencyTransaction()
		{
			return TransactionDefinition.Account.CurrencyCode == Amount.CurrencyCode;
		}

		protected sealed override int TemplateNumber
		{
			get
			{
				return TEMPLATE_ID;
			}
		}

	}

	public class JournalEntryForCreditNoteMessage : JournalEntryMessage
	{
		public string CustomerNumber { get; private set; }
		public string AccountNumber { get; private set; }
		public Coin Coin { get; private set; }
		public string CurrencyCode { get { return Coin.Iso4217Code; } }
		public decimal TotalAmount { get; private set; }
		public decimal CurrencyCost { get; private set; }

		public JournalEntryForCreditNoteMessage(int transactionId, DateTime date, int journalEntryId, string employeeName, decimal saleRate, decimal purchaseRate, string customerNumber, 
			string accountNumber, Coin currencyCode, decimal totalAmount, decimal currencyCost) :
			base(TransactionType.CreditNote, transactionId, date, journalEntryId, employeeName, saleRate, purchaseRate)
		{
			this.CustomerNumber = customerNumber;
			this.AccountNumber = accountNumber;
			this.Coin = currencyCode;
			this.TotalAmount = totalAmount;
			CurrencyCost = currencyCost;
		}

		public JournalEntryForCreditNoteMessage(string serialized) : base(serialized)
		{
			
		}

		protected override void InternalSerialize()
		{
			base.InternalSerialize();
			AddProperty(CustomerNumber).
			AddProperty(AccountNumber).
			AddProperty(CurrencyCode).
			AddProperty(TotalAmount).
			AddProperty(CurrencyCost);
		}

		protected override void Deserialize(string [] message, out int fieldOrder)
		{
			base.Deserialize(message, out fieldOrder);
			CustomerNumber = message[fieldOrder++];
			AccountNumber = message[fieldOrder++];
			Coin = Coinage.KafkaProperty2Coin(message[fieldOrder++]);
			TotalAmount = decimal.Parse(message[fieldOrder++]);
			CurrencyCost = decimal.Parse(message[fieldOrder++]);
		}
	}

}
