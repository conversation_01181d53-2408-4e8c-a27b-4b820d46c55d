﻿using System;
using System.Collections.Generic;
using System.Text;

namespace GamesEngine.Accounting.JournalTemplates
{
	internal class Argument
	{
		private readonly ParameterType parameterType;
		private string textValue;
		private decimal decimalValue;
		private const decimal EMPTY_ARGUMENT = -1m;

		internal Argument (ParameterType parameterType)
        {
			this.parameterType = parameterType;
			this.textValue = null;
			this.decimalValue = EMPTY_ARGUMENT;
		}

		internal void SetValue(string value)
        {
			if (parameterType != ParameterType.Text) throw new GameEngineException($"Parameter is not a String compatible");
			if (string.IsNullOrWhiteSpace(value)) throw new ArgumentNullException(nameof(value));

			this.textValue = value;
			this.decimalValue = 0;
		}

		internal void SetValue(decimal value)
		{
			if (parameterType != ParameterType.Amount) throw new GameEngineException($"Parameter is not a Amount compatible");
			if (value < 0) throw new GameEngineException($"Value {value} can not be negative");
			this.decimalValue = value;
		}

		internal string ValueAsString
		{
			get
			{
				if (this.decimalValue == EMPTY_ARGUMENT) throw new GameEngineException($"Value for argument has not been set");
				switch (parameterType)
                {
					case ParameterType.Text:
						return ""+this.textValue;
					case ParameterType.Amount:
						return ""+this.decimalValue;
				}
				throw new GameEngineException($"Parameter Type {parameterType} is not handled.");
			}
		}

		internal decimal ValueAsDecimal
		{
			get
			{
				if (this.decimalValue == EMPTY_ARGUMENT) throw new GameEngineException($"Value for argument has not been set");
				switch (parameterType)
				{
					case ParameterType.Amount:
						return this.decimalValue;
					case ParameterType.Text:
						throw new GameEngineException($"Parameter Type {parameterType} can not be converted to decimal.");
				}
				throw new GameEngineException($"Parameter Type {parameterType} is not handled.");
			}
		}

	}
}
