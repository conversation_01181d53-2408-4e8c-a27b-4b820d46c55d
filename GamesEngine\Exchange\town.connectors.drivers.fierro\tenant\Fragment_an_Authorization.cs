﻿using Connectors.town.connectors.drivers;
using GamesEngine;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Mvc;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.commons;

namespace town.connectors.drivers.fiero
{
	public class Fragment_an_Authorization : FieroTenantDriver
	{
		public Fragment_an_Authorization() : base(Tenant_Actions.Fragment)
		{
		}
		public override void Prepare(DateTime now)
		{
			CustomSettings.AddVariableParameter("actor");
			CustomSettings.AddVariableParameter("customerId");
			CustomSettings.AddVariableParameter("ticketNumber");
			CustomSettings.AddVariableParameter("wagers");

			//CustomSettings.Prepare();
		}

		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			var Actor = recordSet.Mappings["actor"];
			var CustomerId = recordSet.Mappings["customerId"];
			var TicketNumber = recordSet.Mappings["ticketNumber"];
			var Wagers = recordSet.Mappings["wagers"];

			var actor = Actor.As<RestAPISpawnerActor>();
			string customerId = CustomerId.AsString;
			int ticketNumber = TicketNumber.AsInt;
			PostFreeFormWager[] wagers = Wagers.As<PostFreeFormWager[]>();

			if (String.IsNullOrWhiteSpace(customerId)) throw new ArgumentNullException(nameof(CustomerId));
			if (wagers == null) throw new ArgumentNullException(nameof(wagers));
			if (wagers.Length == 0) throw new Exception($"At least one wager is required to send request {nameof(FragmentAuthorization)} TicketNumber: {ticketNumber} CustomerId: {customerId} on RegisterWagers");

			Debug.WriteLine($"Accounting service {nameof(FragmentAuthorization)} received {nameof(customerId)}:{customerId} {nameof(ticketNumber)}:{ticketNumber} {nameof(wagers)}:{wagers}");

			var result = FragmentAuthorization(actor, customerId, ticketNumber, wagers);
			return (T)Convert.ChangeType(result, typeof(T));

		}
		public override Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet) 
        {
            throw new NotImplementedException();
        }

		PostFreeFormWagerCollectionSuccessResponse CreateFakePostFreeFormWagerCollectionResponse(PostFreeFormWager[] wagers, int initialWagerId)
		{
			PostFreeFormWager[] cloned = new PostFreeFormWager[wagers.Length];
			wagers.CopyTo(cloned, 0);
			var fakeResponse = new PostFreeFormWagerCollectionSuccessResponse()
			{
				Wagers = cloned
			};
			foreach (PostFreeFormWager wager in fakeResponse.Wagers)
			{
				wager.WagerNumber = initialWagerId.ToString();
				initialWagerId++;
			}
			return fakeResponse;
		}

        private static string EscapeSingleQuotes(string input)
        {
            if (string.IsNullOrEmpty(input))
            {
                return input;
            }

            var stringBuilder = new StringBuilder(input.Length);
            foreach (char c in input)
            {
                if (c == '\'')
                {
                    stringBuilder.Append("\\'");
                }
                else
                {
                    stringBuilder.Append(c);
                }
            }

            return stringBuilder.ToString();
        }

        public const int MaxFragmentsPerGroup = 100;
		PostFreeFormWagerCollectionSuccessResponse FragmentAuthorization(RestAPISpawnerActor actor, string atAddress, int authorizationNumber, PostFreeFormWager[] wagers)
		{
			PostFreeFormWagerCollectionSuccessResponse result = CreateFakePostFreeFormWagerCollectionResponse(wagers, 1);

			var wagerNumbers = result.Wagers.Select(x => int.Parse(x.WagerNumber));
			var wagerRisk = result.Wagers.Select(x => decimal.Parse(x.Risk)).ToList();
			var wagerTowin = result.Wagers.Select(x => decimal.Parse(x.ToWin)).ToList();

			decimal fragmentsRisk = wagerRisk[0];
			decimal fragmentsTowin = wagerTowin[0];
			var fragmentsReferences = result.Wagers.Select(x => x.ReferenceNumber);
            var fragmentsDescription = result.Wagers.Select(x => x.BetDescription);
			var fragmentsToWins = result.Wagers.Select(x => decimal.Parse(x.ToWin).ToString("F"));
			var fragmentsToRisks = result.Wagers.Select(x => decimal.Parse(x.Risk).ToString("F"));
			bool hasSameToWinAmounts = wagerTowin.Count(x => x == fragmentsTowin) == wagerTowin.Count;
			bool hasSameRiskAmounts = wagerRisk.Count(x => x == fragmentsRisk) == wagerRisk.Count;
			int theLowestFragmentNumber = wagerNumbers.Min();
			int theHighestFragmentNumber = wagerNumbers.Max();

            var commandBuilder = new StringBuilder();
            var isMultipleAuthorization = authorizationNumber == -1;
            var countFragments = fragmentsDescription.Count();
			int groupsOfRowsToInsert = countFragments > MaxFragmentsPerGroup ? ((countFragments - 1) / MaxFragmentsPerGroup) + 1 : 1;
			for (int index = 0; index < groupsOfRowsToInsert; index++)
			{
				var indexToStart = index * MaxFragmentsPerGroup;
                commandBuilder.AppendLine("{");
                if (isMultipleAuthorization)
                {
                    AuthorizationsNumbers.BuildCommand(result.Wagers.Select(w => w.TicketNumber).Skip(indexToStart).Take(MaxFragmentsPerGroup), ref commandBuilder);
                    commandBuilder.Append("authorizations = atAddress.GetAuthorizations(auths.Numbers);").AppendLine();
                    var amountOfFragmentsInGroup = Math.Min(result.Wagers.Length, MaxFragmentsPerGroup);
                    commandBuilder.Append("authorizations.CreateFragments();").AppendLine();
                }
                else
                {
                    commandBuilder.Append("authorization = atAddress.GetAuthorization(").Append(authorizationNumber).Append(");").AppendLine();
                    if (index == 0) commandBuilder.Append("authorization.CreateFragments(").Append(theLowestFragmentNumber).Append(", ").Append(theHighestFragmentNumber).Append(");").AppendLine();
                }

                var referencesInGroup = fragmentsReferences.Skip(indexToStart).Take(MaxFragmentsPerGroup);
				var escapedDescriptionInGroup = fragmentsDescription.Skip(indexToStart).Take(MaxFragmentsPerGroup).Select(description => EscapeSingleQuotes(description));
				var maxFragmentNumber = escapedDescriptionInGroup.Count();
				if (hasSameToWinAmounts && hasSameRiskAmounts)
				{
                    if (isMultipleAuthorization)
                    {
                        commandBuilder.Append("authorizations.AddFragments(").Append(indexToStart).Append(", {'").Append(string.Join("','", referencesInGroup)).Append("'}, {'").Append(string.Join("','", escapedDescriptionInGroup)).Append("'}, ").Append(fragmentsRisk).Append(", ").Append(fragmentsTowin).Append(");").AppendLine();
                    }
                    else
                    {
                        commandBuilder.Append("authorization.AddFragments(1, ").Append(maxFragmentNumber).Append(", {'").Append(string.Join("','", referencesInGroup)).Append("'}, {'").Append(string.Join("','", escapedDescriptionInGroup)).Append("'}, ").Append(fragmentsRisk).Append(", ").Append(fragmentsTowin).Append(");").AppendLine();
                    }
				}
				else if (!hasSameToWinAmounts && hasSameRiskAmounts)
				{
					var toWinsInGroup = fragmentsToWins.Skip(indexToStart).Take(MaxFragmentsPerGroup);
                    if (isMultipleAuthorization)
                    {
                        commandBuilder.Append("authorizations.AddFragments(").Append(indexToStart).Append(", {'").Append(string.Join("','", referencesInGroup)).Append("'}, {'").Append(string.Join("','", escapedDescriptionInGroup)).Append("'}, ").Append(fragmentsRisk).Append(", {").Append(string.Join(",", toWinsInGroup)).Append("});").AppendLine();
                    }
                    else
                    {
                        commandBuilder.Append("authorization.AddFragments(1, ").Append(maxFragmentNumber).Append(", {'").Append(string.Join("','", referencesInGroup)).Append("'}, {'").Append(string.Join("','", escapedDescriptionInGroup)).Append("'}, ").Append(fragmentsRisk).Append(", {").Append(string.Join(",", toWinsInGroup)).Append("});").AppendLine();
                    }
                }
				else if (hasSameToWinAmounts && !hasSameRiskAmounts)
				{
					var risksInGroup = fragmentsToRisks.Skip(indexToStart).Take(MaxFragmentsPerGroup);
                    if (isMultipleAuthorization)
                    {
                        commandBuilder.Append("authorizations.AddFragments(").Append(indexToStart).Append(", {'").Append(string.Join("','", referencesInGroup)).Append("'}, {'").Append(string.Join("','", escapedDescriptionInGroup)).Append("'}, {").Append(string.Join(",", risksInGroup)).Append("}, ").Append(fragmentsTowin).Append(");").AppendLine();
                    }
                    else
                    {
                        commandBuilder.Append("authorization.AddFragments(1, ").Append(maxFragmentNumber).Append(", {'").Append(string.Join("','", referencesInGroup)).Append("'}, {'").Append(string.Join("','", escapedDescriptionInGroup)).Append("'}, {").Append(string.Join(",", risksInGroup)).Append("}, ").Append(fragmentsTowin).Append(");").AppendLine();
                    }
                }
				else if (!hasSameToWinAmounts && !hasSameRiskAmounts)
				{
					var toWinsInGroup = fragmentsToWins.Skip(indexToStart).Take(MaxFragmentsPerGroup);
					var risksInGroup = fragmentsToRisks.Skip(indexToStart).Take(MaxFragmentsPerGroup);
                    if (isMultipleAuthorization)
                    {
                        commandBuilder.Append("authorizations.AddFragments(").Append(indexToStart).Append(", {'").Append(string.Join("','", referencesInGroup)).Append("'}, {'").Append(string.Join("','", escapedDescriptionInGroup)).Append("'}, {").Append(string.Join(",", risksInGroup)).Append("}, {").Append(string.Join(",", toWinsInGroup)).Append("});").AppendLine();
                    }
                    else
                    {
                        commandBuilder.Append("authorization.AddFragments(1, ").Append(maxFragmentNumber).Append(", {'").Append(string.Join("','", referencesInGroup)).Append("'}, {'").Append(string.Join("','", escapedDescriptionInGroup)).Append("'}, {").Append(string.Join(",", risksInGroup)).Append("}, {").Append(string.Join(",", toWinsInGroup)).Append("});").AppendLine();
                    }
                }
				else
				{
					throw new GameEngineException($"There is no valid option for risk and towin combination.");
				}
                commandBuilder.AppendLine("}");
                var commandResult = actor.PerformCmd(atAddress, commandBuilder.ToString());
				if (!(commandResult is OkObjectResult))
				{
					ErrorsSender.Send($"command:{commandBuilder}.\nauthorization:{authorizationNumber}.\nWagersCount {result.Wagers.Count()}",
						"Fragment creation fails.");

					throw new GameEngineException($"failure in command:{commandBuilder}");
				}
                commandBuilder.Clear();
            }
			return result;
		}

	}
}
