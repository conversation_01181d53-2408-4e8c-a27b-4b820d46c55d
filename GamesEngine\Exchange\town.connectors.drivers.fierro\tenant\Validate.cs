﻿using Connectors.town.connectors.driver.transactions;
using GamesEngine.Finance;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.commons;
using town.connectors.drivers;
using town.connectors.drivers.fiero;
using static town.connectors.drivers.Result;

namespace town.connectors.drivers.fiero
{
    internal class Validate : FieroTenantDriver
	{
		public Validate() : base(Tenant_Actions.Validate)
		{
		}
		public override void Prepare(DateTime now)
		{

			//CustomSettings.Prepare();
		}
		public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			await Task.Yield();
			var result = true;

			return (T)Convert.ChangeType(result, typeof(T));
		}
		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			var result = true;

			return (T)Convert.ChangeType(result, typeof(T));
		}
    }
}
