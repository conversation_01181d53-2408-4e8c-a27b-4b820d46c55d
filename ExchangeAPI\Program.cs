﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using log4net;
using Microsoft.AspNetCore;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Puppeteer.EventSourcing;

namespace ExchangeAPI
{
	public class Program
	{
		public static void Main(string[] args)
		{
			var logRepository = log4net.LogManager.GetRepository(System.Reflection.Assembly.GetEntryAssembly());
			log4net.Config.XmlConfigurator.Configure(logRepository, new System.IO.FileInfo("log4net.config"));

			Loggers.GetIntance().Db.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "database"));
			Loggers.GetIntance().AccountingServicesASI.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "accountingservices-asi"));
			Loggers.GetIntance().AccountingServicesASIRemoveTransaction.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "accountingservices-asi-removetransaction"));
			Loggers.GetIntance().AccountingServicesASIPostTransaction.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "accountingservices-asi-posttransaction"));
			Loggers.GetIntance().AccountingServicesASIPostTransactionWRef.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "accountingservices-asi-posttransactionwref"));
			Loggers.GetIntance().AccountingServicesASIPostFreeFormTicket.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "accountingservices-asi-postfreeformticket"));
			Loggers.GetIntance().AccountingServicesASIPostFreeFormWagerCollection.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "accountingservices-asi-postfreeformwagercollection"));
			Loggers.GetIntance().AccountingServicesASIPostFreeFormTicketAndWagers.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "accountingservices-asi-postfreeformticketandwagers"));
			Loggers.GetIntance().AccountingServicesASIGradeFreeFormWagerCollection.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "accountingservices-asi-gradefreeformwagercollection"));
			Loggers.GetIntance().AccountingServicesASISvcValidateCustomer.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "accountingservices-asi-svcvalidatecustomer"));
			Loggers.GetIntance().AccountingServicesASIGetLottoCustomer.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "accountingservices-asi-getlottocustomer"));
			Loggers.GetIntance().AccountingServicesASIGetTicketWagers.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "accountingservices-asi-getticketwagers"));

			Loggers.GetIntance().AccountingServicesDGS.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "accountingservices-dgs"));
			Loggers.GetIntance().AccountingServicesDGSGetToken.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "accountingservices-dgs-gettoken"));
			Loggers.GetIntance().AccountingServicesDGSDeposit.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "accountingservices-dgs-deposit"));
			Loggers.GetIntance().AccountingServicesDGSWithdraw.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "accountingservices-dgs-withdraw"));
			Loggers.GetIntance().AccountingServicesDGSValidateCustomer.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "accountingservices-dgs-validatecustomer"));
			Loggers.GetIntance().AccountingServicesDGSCreateWagers.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "accountingservices-dgs-createwagers"));
			Loggers.GetIntance().AccountingServicesDGSUpdateWagers.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "accountingservices-dgs-updatewagers"));

			Loggers.GetIntance().Emails.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "emails"));
			Loggers.GetIntance().Scripts.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "scripts"));


			var host = BuildWebHost(args);
			host.Run();
		}

		public static IWebHost BuildWebHost(string[] args) =>
			WebHost.CreateDefaultBuilder(args)
				.UseStartup<Startup>()
				.UseUrls("http://0.0.0.0:52220")
				.ConfigureLogging((hostingContext, logging) =>
				{
					// Requires `using Microsoft.Extensions.Logging;`
					logging.AddConfiguration(hostingContext.Configuration.GetSection("Logging"));
					logging.AddConsole();
					logging.AddDebug();
					logging.AddEventSourceLogger();
				})
				.Build();
	}
}
