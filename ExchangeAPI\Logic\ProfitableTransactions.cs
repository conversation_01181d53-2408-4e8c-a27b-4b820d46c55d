﻿using ExchangeAPI.ExchangeEntities;
using ExchangeAPI.Model;
using ExchangeAPI.Service;
using GamesEngine;
using GamesEngine.Exchange;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using town.connectors.drivers;

namespace ExchangeAPI.Logic
{
    public class ProfitableTransactions
    {
        public static ProfitableTransactionsDTO FilterBy(DateTime startDate, DateTime endDate, int initialIndex, int amountOfRows)
        {
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate.Hour != 0 || startDate.Minute != 0 || startDate.Second != 0) throw new GameEngineException($"{nameof(startDate)} {startDate} is not valid");
            if (endDate.Hour != 0 || endDate.Minute != 0 || endDate.Second != 0) throw new GameEngineException($"{nameof(endDate)} {endDate} is not valid");
            if (initialIndex < 0) throw new GameEngineException($"{nameof(initialIndex)} {initialIndex} is not valid");
            if (amountOfRows <= 0) throw new GameEngineException($"{nameof(amountOfRows)} {amountOfRows} is not valid");

            using (var context = new ExchangeContext())
            {
                var transactions = context.Profitabletransaction
                    .Include(transaction => transaction.WhoapprovedNavigation)
                    .Include(transaction => transaction.Fromcurrency)
                    .Include(transaction => transaction.Tocurrency)
                    .Where(transaction => transaction.Date.Date >= startDate && transaction.Date.Date <= endDate)
                    .OrderBy(transaction => transaction.Date);
                var transactionsPaged = transactions
                    .Skip(initialIndex)
                    .Take(amountOfRows);
                var transactionsDTO = transactionsPaged.MapToProfitableTransactionsDTO();
                transactionsDTO.TotalRecords = transactions.Count();
                transactionsDTO.TotalProfits = transactions.Sum(dailyProfit => dailyProfit.Profit);
                transactionsDTO.TotalGross = transactions.Sum(dailyProfit => dailyProfit.Gross);
                transactionsDTO.TotalNet = transactions.Sum(dailyProfit => dailyProfit.Net);
                transactionsDTO.TotalAmountReceived = transactions.Sum(dailyProfit => dailyProfit.Amount);
                return transactionsDTO;
            }
        }

        public static void Save(ExchangeContext context, ExchangeAPI.ExchangeEntities.Deposit deposit)
        {
            if (context == null) throw new ArgumentNullException(nameof(context));
            if (deposit == null) throw new ArgumentNullException(nameof(deposit));
            if (deposit.Exchangerateid == null) throw new ArgumentNullException(nameof(deposit.Exchangerateid));

            var transaction = new Profitabletransaction()
            {
                Id = deposit.Id,
                Batchnumber = deposit.Batchnumber,
                Authorizationid = 0,
                Date = deposit.Datecreated,
                Fromcurrencyid = deposit.Fromcurrencyid,
                Tocurrencyid = deposit.Tocurrencyid,
                Type = TransactionType.Deposit.ToString(),
                Exchangerateid = deposit.Exchangerateid.Value,
                Whocreated = deposit.Whocreated,
                Whoapproved = deposit.Whoapproved,
                Fromcustomer = string.Empty,
                Tocustomer = deposit.Tocustomer,
                Amount = deposit.Amount,
                Gross = deposit.Gross,
                Net = deposit.Net,
                Profit = deposit.Profit,
                Account = string.Empty,
                Targetaccount = deposit.Account
            };
            context.Profitabletransaction.Add(transaction);
            
            context.SaveChanges();
        }

        public static void Save(ExchangeContext context, Withdrawal withdrawal)
        {
            if (context == null) throw new ArgumentNullException(nameof(context));
            if (withdrawal == null) throw new ArgumentNullException(nameof(withdrawal));
            if (withdrawal.Exchangerateid == null) throw new ArgumentNullException(nameof(withdrawal.Exchangerateid));

            var transaction = new Profitabletransaction()
            {
                Id = withdrawal.Id,
                Batchnumber = withdrawal.Batchnumber,
                Authorizationid = withdrawal.Authorizationid,
                Date = withdrawal.Datecreated,
                Fromcurrencyid = withdrawal.Fromcurrencyid,
                Tocurrencyid = withdrawal.Tocurrencyid.Value,
                Type = TransactionType.Withdrawal.ToString(),
                Exchangerateid = withdrawal.Exchangerateid.Value,
                Whocreated = withdrawal.Whocreated,
                Whoapproved = withdrawal.Whoapproved,
                Fromcustomer = withdrawal.Fromcustomer,
                Tocustomer = string.Empty,
                Amount = withdrawal.Amount,
                Gross = withdrawal.Gross,
                Net = withdrawal.Net,
                Profit = withdrawal.Profit,
                Account = withdrawal.Account,
                Targetaccount = string.Empty
            };
            context.Profitabletransaction.Add(transaction);

            context.SaveChanges();
        }

        public static void Save(ExchangeContext context, Transfer transfer)
        {
            if (context == null) throw new ArgumentNullException(nameof(context));
            if (transfer == null) throw new ArgumentNullException(nameof(transfer));
            if (transfer.Exchangerateid == null) throw new ArgumentNullException(nameof(transfer.Exchangerateid));

            var transaction = new Profitabletransaction()
            {
                Id = transfer.Id,
                Batchnumber = transfer.Batchnumber,
                Authorizationid = transfer.Authorizationid,
                Date = transfer.Datecreated,
                Fromcurrencyid = transfer.Fromcurrencyid,
                Tocurrencyid = transfer.Tocurrencyid,
                Type = TransactionType.Transfer.ToString(),
                Exchangerateid = transfer.Exchangerateid.Value,
                Whocreated = transfer.Whocreated,
                Whoapproved = transfer.Whoapproved,
                Fromcustomer = transfer.Fromcustomer,
                Tocustomer = transfer.Tocustomer,
                Amount = transfer.Amount,
                Gross = transfer.Gross,
                Net = transfer.Net,
                Profit = transfer.Profit,
                Account = transfer.Account,
                Targetaccount = transfer.Targetaccount
            };
            context.Profitabletransaction.Add(transaction);

            context.SaveChanges();
        }
    }
}
