﻿using System;
using System.Collections.Generic;

namespace ExchangeAPI.ExchangeEntities
{
    public partial class Journalentrydetails
    {
        public long Id { get; set; }
        public string Account { get; set; }
        public string Description { get; set; }
        public decimal? Debit { get; set; }
        public decimal? Credit { get; set; }
        public byte Sequence { get; set; }
        public long Journalentryid { get; set; }

        public virtual Journalentry Journalentry { get; set; }
    }
}
