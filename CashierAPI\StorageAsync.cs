﻿using GamesEngine.Finance;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using MySql.Data.MySqlClient;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace CashierAPI
{
	public class StorageMySQLAsync : StorageAsync
	{
		public StorageMySQLAsync(string connectionString) : base(connectionString)
		{
		}

		public override async Task<bool> ExistMovementsTableForThisAccountAndCurrencyAsync(string address, Coin coin)
		{
			if (string.IsNullOrEmpty(address)) return false;

			bool exists = true;
			var tableName = GetTableName(address, coin);
			string sql = $"SELECT 1 FROM {tableName} LIMIT 1";
			using (MySqlConnection connection = new MySqlConnection(connectionString))
			{
				try
				{
					await connection.OpenAsync();
					using (MySqlCommand command = new MySqlCommand(sql, connection))
					{
						var dataReader = await command.ExecuteReaderAsync();
						await dataReader.CloseAsync();
					}
				}
				catch
				{
					exists = false;
				}
				finally
				{
					await connection.CloseAsync();
				}
			}
			return exists;
		}

		public override async Task<int> NextValueAuthorizationNumberAsync()
		{
			#region It has a TestCase, look for NextValueAuthorizationNumber_ItIsAsyncOnCashierAPI_ThisIsOnlyForUnitTestPurposes_DoNotUseIt() if it has any change
			string sql = "SELECT nextval('authorizations') as next_sequence;";
			int result = -1;
			try
			{
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						await connection.OpenAsync();
						using (MySqlCommand command = new MySqlCommand(sql, connection))
						{
							var dataReader = await command.ExecuteReaderAsync();

							await dataReader.ReadAsync();

							result = dataReader.GetInt32(0);

							await dataReader.CloseAsync();
						}
					}
					catch (Exception e)
					{
						Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
						ErrorsSender.Send(e, sql);
						throw e;
					}
					finally
					{
						await connection.CloseAsync();
					}
				}
			}
			catch (Exception e)
			{
				Loggers.GetIntance().Db.Error($@"sql:{sql}", e.InnerException);
				ErrorsSender.Send(e.InnerException, sql);
				throw e;
			}

			return result;
			#endregion
		}

	}

	public class StorageMemoryAsync : StorageAsync
	{
		private readonly Dictionary<string, List<Movement>> movementsDB = new Dictionary<string, List<Movement>>();

		public StorageMemoryAsync() : base("")
		{
		}

		public override async Task<bool> ExistMovementsTableForThisAccountAndCurrencyAsync(string atAddress, Coin coin)
		{
			bool result = false;
			await Task.Run(() => {
				result = movementsDB.ContainsKey(GetTableName(atAddress, coin));
			});

			return result;
		}

		private static int autorizations;
		public override async Task<int> NextValueAuthorizationNumberAsync()
		{
			#region It has a TestCase, look for NextValueAuthorizationNumber_ItIsAsyncOnCashierAPI_ThisIsOnlyForUnitTestPurposes_DoNotUseIt() if it has any change
			int result = -1;
			await Task.Run(() =>
			{
				if (autorizations == int.MaxValue) autorizations = 0;
				autorizations = autorizations + 1;
				result = autorizations;
			});

			return result;
			#endregion
		}

	}

	public class StorageSQLServerAsync : StorageAsync
	{
		public StorageSQLServerAsync(string connectionString) : base(connectionString)
		{
		}
		public override async Task<bool> ExistMovementsTableForThisAccountAndCurrencyAsync(string atAddress, Coin coin)
		{
			bool exists = false;
			StringBuilder statement = new StringBuilder();

			statement.Append("IF EXISTS(")
				.Append("SELECT 1 FROM INFORMATION_SCHEMA.TABLES ")
				.Append("WHERE TABLE_NAME = '" + GetTableName(atAddress, coin) + "')")
				.Append("SELECT 1 ELSE SELECT 0;");
			string sql = statement.ToString();

			using (SqlConnection connection = new SqlConnection(connectionString))
			{
				try
				{
					await connection.OpenAsync();
					using (SqlCommand command = new SqlCommand(sql, connection))
					{
						var result = (int)await command.ExecuteScalarAsync();
						exists = result == 1;
					}
				}
				catch
				{
					exists = false;
				}
				finally
				{
					await connection.CloseAsync();
				}
			}
			return exists;
		}

		public override async Task<int> NextValueAuthorizationNumberAsync()
		{
			#region It has a TestCase, look for NextValueAuthorizationNumber_ItIsAsyncOnCashierAPI_ThisIsOnlyForUnitTestPurposes_DoNotUseIt() if it has any change
			string sql = "SELECT NEXT VALUE FOR authorizations";
			int result = -1;
			try
			{
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						await connection.OpenAsync();
						using (SqlCommand command = new SqlCommand(sql, connection))
						{
							var dataReader = await command.ExecuteReaderAsync();

							await dataReader.ReadAsync();

							result = dataReader.GetInt32(0);

							await dataReader.CloseAsync();
						}
					}
					catch (Exception e)
					{
						Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
						ErrorsSender.Send(e, sql);
						throw e;
					}
					finally
					{
						await connection.CloseAsync();
					}
				}
			}
			catch (Exception e)
			{
				Loggers.GetIntance().Db.Error($@"sql:{sql}", e.InnerException);
				ErrorsSender.Send(e.InnerException, sql);
				throw e;
			}

			return result;
			#endregion
		}

	}
}
