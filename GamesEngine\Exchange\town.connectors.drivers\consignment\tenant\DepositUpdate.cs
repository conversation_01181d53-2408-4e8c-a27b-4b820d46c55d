﻿using Connectors.town.connectors.driver.transactions;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;
using town.connectors;
using town.connectors.drivers;
using static town.connectors.drivers.Result;

namespace Connectors.town.connectors.drivers.consignment
{
    public class DepositUpdate: ConsignmentTenantDriver
    {
        public DepositUpdate() : base(Tenant_Actions.Others)
        {

        }

        public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            throw new NotImplementedException();
        }

        public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            if (CustomSettings.ThereArePendingChanges)
            {
                ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
                SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
                Password = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
            }

            DepositTransaction result;
            var authorizationNumber = await GetDepositUpdateAsync(recordSet);
            if (authorizationNumber == FAKE_DOCUMENT_NUMBER) result = new DepositTransaction(FAKE_DOCUMENT_NUMBER, TransactionStatus.DENIED, Entity, PaymentMethod, CurrencyIsoCodes);
            else result = new DepositTransaction(authorizationNumber, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes);
            return (T)Convert.ChangeType(result, typeof(T));
        }

        async Task<int> GetDepositUpdateAsync(CustomSettings.RecordSet recordSet)
        {
            ResponseEditDepositEnvelope responseXml = null;
            string responseString = string.Empty;
            string xmlWithHiddenFields = string.Empty;
            var url = $"{ServicesUrl}?op=Edit_deposit";
            try
            {
                string xmlString = CreateSoapEnvelope(recordSet, out xmlWithHiddenFields);
                var webRequest = new HttpRequestMessage(HttpMethod.Post, url)
                {
                    Content = new StringContent(xmlString, Encoding.UTF8, "text/xml"),
                };

                Loggers.GetIntance().AccountingConsignmentGetDepositUpdate.Debug($@"url:{url} data:{xmlWithHiddenFields}");
                using (HttpResponseMessage response = await httpClient.SendAsync(webRequest, HttpCompletionOption.ResponseHeadersRead))
                {
                    responseString = await response.Content.ReadAsStringAsync();
                }
                Loggers.GetIntance().AccountingConsignmentGetDepositUpdate.Debug($@"response:{responseString}");

                XmlSerializer xmls = new XmlSerializer(typeof(ResponseEditDepositEnvelope));
                responseXml = (ResponseEditDepositEnvelope)xmls.Deserialize(new StringReader(responseString));
            }
            catch (Exception e)
            {
                Loggers.GetIntance().AccountingConsignmentGetDepositUpdate.Error($@"url:{url} data:{xmlWithHiddenFields} type:{e.GetType()} error:{e.Message}", e);

                var extraErrorMessage = string.Empty;
                InternalOnError(nameof(GetDepositUpdateAsync), 1, e, $"Url:{url}", $"Request: {xmlWithHiddenFields}", $"Response: {responseString}");
            }

            if (responseXml == null || string.IsNullOrWhiteSpace(responseXml.Body.Edit_depositResponse.Edit_depositResult))
            {
                NotifyWarn(nameof(GetDepositUpdateAsync), $"Url:{url}\nResponse: {responseString}", $"Response cannot be empty");
            }
            else
            {
                var isNumeric = int.TryParse(responseXml.Body.Edit_depositResponse.Edit_depositResult, out int authorizationNumber);
                if (isNumeric) return authorizationNumber;
                else NotifyWarn(nameof(GetDepositUpdateAsync), $"Url:{url}\nResponse: {responseString}", "No authorization number found");
            }

            return FAKE_DOCUMENT_NUMBER;
        }

        string CreateSoapEnvelope(CustomSettings.RecordSet recordSet, out string xmlWithHiddenFields)
        {
            var depositId = recordSet.Mappings["depositId"].AsString;
            var sendersName = recordSet.Mappings["sendersName"].AsString;
            var country = recordSet.Mappings["country"].AsString;
            var state = recordSet.Mappings["state"].AsString;
            var city = recordSet.Mappings["city"].AsString;
            var controlNum = recordSet.Mappings["controlNum"].AsString;

            var xmlBuilder = new StringBuilder();
            xmlBuilder.AppendLine("<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">");
            xmlBuilder.AppendLine("<soap:Body>");
            xmlBuilder.AppendLine("<Edit_deposit xmlns=\"http://tempuri.org/\">");
            xmlBuilder.Append("<user>").Append(SystemId).AppendLine("</user>");
            xmlBuilder.Append("<password>").Append(Password).AppendLine("</password>");
            xmlBuilder.Append("<depositId>").Append(depositId).AppendLine("</depositId>");
            xmlBuilder.Append("<sendersName>").Append(sendersName).AppendLine("</sendersName>");
            xmlBuilder.Append("<country>").Append(country).AppendLine("</country>");
            xmlBuilder.Append("<state>").Append(state).AppendLine("</state>");
            xmlBuilder.Append("<city>").Append(city).AppendLine("</city>");
            xmlBuilder.Append("<controlNum>").Append(controlNum).AppendLine("</controlNum>");
            xmlBuilder.AppendLine("</Edit_deposit>");
            xmlBuilder.AppendLine("</soap:Body>");
            xmlBuilder.AppendLine("</soap:Envelope>");

            var xmlBuilderWithHiddenFields = new StringBuilder();
            xmlBuilderWithHiddenFields.AppendLine("<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">");
            xmlBuilderWithHiddenFields.AppendLine("<soap:Body>");
            xmlBuilderWithHiddenFields.AppendLine("<Edit_deposit xmlns=\"http://tempuri.org/\">");
            xmlBuilderWithHiddenFields.AppendLine("<username>XXXX</username>");
            xmlBuilderWithHiddenFields.AppendLine("<password>XXXX</password>");
            xmlBuilderWithHiddenFields.Append("<depositId>").Append(depositId).AppendLine("</depositId>");
            xmlBuilderWithHiddenFields.Append("<sendersName>").Append(sendersName).AppendLine("</sendersName>");
            xmlBuilderWithHiddenFields.Append("<country>").Append(country).AppendLine("</country>");
            xmlBuilderWithHiddenFields.Append("<state>").Append(state).AppendLine("</state>");
            xmlBuilderWithHiddenFields.Append("<city>").Append(city).AppendLine("</city>");
            xmlBuilderWithHiddenFields.Append("<controlNum>").Append(controlNum).AppendLine("</controlNum>");
            xmlBuilderWithHiddenFields.AppendLine("</Edit_deposit>");
            xmlBuilderWithHiddenFields.AppendLine("</soap:Body>");
            xmlBuilderWithHiddenFields.AppendLine("</soap:Envelope>");

            xmlWithHiddenFields = xmlBuilderWithHiddenFields.ToString();
            return xmlBuilder.ToString();
        }

        [XmlRoot("Envelope", Namespace = "http://schemas.xmlsoap.org/soap/envelope/")]
        public class ResponseEditDepositEnvelope
        {
            public ResponseEditDepositBody Body { get; set; }
        }

        public class ResponseEditDepositBody
        {
            [XmlElement(ElementName = "Edit_depositResponse", Namespace = "http://tempuri.org/")]
            public EditDepositResponse Edit_depositResponse { get; set; }
        }

        public class EditDepositResponse
        {
            public string Edit_depositResult { get; set; }
        }

        public override void Prepare(DateTime now)
        {
            CustomSettings.AddVariableParameter("depositId");
            CustomSettings.AddVariableParameter("sendersName");
            CustomSettings.AddVariableParameter("country");
            CustomSettings.AddVariableParameter("state");
            CustomSettings.AddVariableParameter("city");
            CustomSettings.AddVariableParameter("controlNum");

            SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
            Password = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
            ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
        }
    }
}
