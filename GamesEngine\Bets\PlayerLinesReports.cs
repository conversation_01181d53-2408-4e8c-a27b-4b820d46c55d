﻿using GamesEngine.Gameboards.Lines;
using GamesEngine.Games.Lines;
using GamesEngine.PurchaseOrders;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Bets
{
    internal class PlayerLinesReports:Objeto
    {
        private readonly Player player;
        private readonly IEnumerable<Bet> bets;
        private readonly Customer customer;
        internal PlayerLinesReports(Player player, IEnumerable<Bet> bets, Customer customer)
        {
            this.player = player;
            this.bets = bets;
            this.customer = customer;
        }

        internal IEnumerable<Wager> MyAliveWagers()
        {
            var result = new List<Wager>();
            foreach (var bet in bets)
            {
                if (bet.Gameboard is Wager wager)
                {
                    if (wager.IsPending())
                    {
                        result.Add(wager);
                    }
                }
            }

            return result.OrderByDescending(wager => wager.CreationDate).ToList();
        }


        internal IEnumerable<Wager> MyAliveWagers(DateTime startedDate, DateTime now)
        {
            if (startedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (now < startedDate) throw new GameEngineException($"{nameof(startedDate)} cannot be greater than {nameof(now)}");

            var result = new List<Wager>();
            foreach (var bet in bets)
            {
                if (bet.Gameboard is Wager wager)
                {
                    var gameDate = wager.Line.Game.ScheduledDate;
                    if (wager.IsPending() && gameDate >= startedDate && gameDate <= now)
                    {
                        result.Add(wager);
                    }
                }
            }

            return result.OrderBy(wager => wager.Line.Game.ScheduledDate).ThenByDescending(wager => wager.CreationDate).ToList();
        }

        internal IEnumerable<Wager> MyWinnerWagers(DateTime startedDate, DateTime now)
        {
            if (startedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (now < startedDate) throw new GameEngineException($"{nameof(startedDate)} cannot be greater than {nameof(now)}");

            var result = new List<Wager>();
            foreach (var bet in bets)
            {
                if (bet.Gameboard is Wager wager)
                {
                    var gameDate = wager.Line.Game.ScheduledDate;
                    if (wager.Line.Showcase.IsThereAnyPendingLine() && wager.IsWinner() && gameDate >= startedDate && gameDate <= now)
                    {
                        result.Add(wager);
                    }
                }
            }

            return result.OrderBy(wager => wager.Line.Game.ScheduledDate).ThenByDescending(wager => wager.CreationDate).ToList();
        }

        internal IEnumerable<Wager> MyLoserWagers(DateTime startedDate, DateTime now)
        {
            if (startedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (now < startedDate) throw new GameEngineException($"{nameof(startedDate)} cannot be greater than {nameof(now)}");

            var result = new List<Wager>();
            foreach (var bet in bets)
            {
                if (bet.Gameboard is Wager wager)
                {
                    var gameDate = wager.Line.Game.ScheduledDate;
                    if (wager.Line.Showcase.IsThereAnyPendingLine() && wager.IsLoser() && gameDate >= startedDate && gameDate <= now)
                    {
                        result.Add(wager);
                    }
                }
            }

            return result.OrderBy(wager => wager.Line.Game.ScheduledDate).ThenByDescending(wager => wager.CreationDate).ToList();
        }

        internal IEnumerable<Wager> MyNoActionWagers(DateTime startedDate, DateTime now)
        {
            if (startedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (now < startedDate) throw new GameEngineException($"{nameof(startedDate)} cannot be greater than {nameof(now)}");

            var result = new List<Wager>();
            foreach (var bet in bets)
            {
                if (bet.Gameboard is Wager wager)
                {
                    var gameDate = wager.Line.Game.ScheduledDate;
                    if (wager.Line.Showcase.IsThereAnyPendingLine() && wager.IsNoAction() && gameDate >= startedDate && gameDate <= now)
                    {
                        result.Add(wager);
                    }
                }
            }

            return result.OrderBy(wager => wager.Line.Game.ScheduledDate).ThenByDescending(wager => wager.CreationDate).ToList();
        }

        internal CompletedWagers MyWinnerWagersBetween(DateTime startedDate, DateTime endedDate)
        {
            if (startedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (endedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");

            var queryMaker = new Games.Lines.QueryMakerOfHistorical();
            var winners = queryMaker.WinnerWagersOfPlayerBetween(startedDate, endedDate, customer.AccountNumber);
            return winners;
        }

        internal CompletedWagers MyLoserWagersBetween(DateTime startedDate, DateTime endedDate)
        {
            if (startedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (endedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");

            var queryMaker = new Games.Lines.QueryMakerOfHistorical();
            var winners = queryMaker.LoserWagersOfPlayerBetween(startedDate, endedDate, customer.AccountNumber);
            return winners;
        }

        internal CompletedWagers MyNoActionWagersBetween(DateTime startedDate, DateTime endedDate)
        {
            if (startedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (endedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");

            var queryMaker = new Games.Lines.QueryMakerOfHistorical();
            var winners = queryMaker.NoActionWagersOfPlayerBetween(startedDate, endedDate, customer.AccountNumber);
            return winners;
        }

        internal CompletedWagers MyWinnerWagersOfGame(int gameId)
        {
            if (gameId <= 0) throw new GameEngineException($"{nameof(gameId)} must be greater than 0");

            var queryMaker = new Games.Lines.QueryMakerOfHistorical();
            var winners = queryMaker.WinnerWagersOfGame(gameId, customer.AccountNumber);
            return winners;
        }

        internal CompletedWagers MyLoserWagersOfGame(int gameId)
        {
            if (gameId <= 0) throw new GameEngineException($"{nameof(gameId)} must be greater than 0");

            var queryMaker = new Games.Lines.QueryMakerOfHistorical();
            var winners = queryMaker.LoserWagersOfGame(gameId, customer.AccountNumber);
            return winners;
        }

        internal CompletedWagers MyNoActionWagersOfGame(int gameId)
        {
            if (gameId <= 0) throw new GameEngineException($"{nameof(gameId)} must be greater than 0");

            var queryMaker = new Games.Lines.QueryMakerOfHistorical();
            var winners = queryMaker.NoActionWagersOfGame(gameId, customer.AccountNumber);
            return winners;
        }

        internal CompletedMatches LastPlayedGames()
        {
            var queryMaker = new Games.Lines.QueryMakerOfHistorical();
            var result = queryMaker.LastPlayedGamesOfPlayer(customer.AccountNumber);
            return result;
        }
    }
}
