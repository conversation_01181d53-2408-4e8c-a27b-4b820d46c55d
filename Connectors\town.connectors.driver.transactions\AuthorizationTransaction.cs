﻿using Connectors.town.connectors.commons;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using town.connectors.drivers;

namespace Connectors.town.connectors.driver.transactions
{
	public sealed class AuthorizationTransaction : Result, IPaymentChannelResponse
    {
        public AuthorizationTransaction(int authorizationId, TransactionStatus status, string entity, PaymentMethod paymentMethod, string[] currencyIsoCode) :
			base(entity, paymentMethod, currencyIsoCode, TransactionType.Sale)
        {
			AuthorizationId = authorizationId;
			Status = status;
		}
		public AuthorizationTransaction(int authorizationId, TransactionStatus status, string entity, PaymentMethod paymentMethod, string[] currencyIsoCode, AuthorizationResponseCode code) :
			this(authorizationId, status, entity, paymentMethod, currencyIsoCode)
		{
			Code = code;
		}
        public AuthorizationTransaction(int authorizationId, TransactionStatus status, string entity, PaymentMethod paymentMethod, string[] currencyIsoCode, AuthorizationResponseCode code, string url, string response) :
            this(authorizationId, status, entity, paymentMethod, currencyIsoCode, code)
        {
            Url = url;
            Response = response;
        }

        public int AuthorizationId { get; }
		public TransactionStatus Status { get; }

        public AuthorizationResponseCode Code { get; }
        public string Url { get; }
        public string Response { get; }
    }
}
