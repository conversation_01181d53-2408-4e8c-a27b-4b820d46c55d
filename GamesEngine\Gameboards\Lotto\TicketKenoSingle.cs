﻿using GamesEngine.Bets;
using GamesEngine.Games.Lotto;
using System;
using System.Collections.Generic;
using System.Text;

namespace GamesEngine.Gameboards.Lotto
{
    internal abstract class TicketKenoSingle : TicketKeno
    {
        internal TicketKenoSingle(Player player, decimal baseCost, LotteryKeno lotteryKeno, DateTime drawdate, string drawId, int[] numbers, DateTime creationDate, decimal ticketCost, Prizes prizes) :
            base(player, baseCost, lotteryKeno, drawdate, drawId, numbers, false, false, creationDate, ticketCost, prizes)
        {
        }

	}
    internal sealed class TicketKeno10Single : TicketKenoSingle
    {
        internal TicketKeno10Single(Player player, decimal baseCost, LotteryKeno lotteryKeno, DateTime drawdate, string drawId, int[] numbers, DateTime creationDate, decimal ticketCost, Prizes prizes) :
            base(player, baseCost, lotteryKeno, drawdate, drawId, numbers, creationDate, ticketCost, prizes)
        {
        }
        internal override TicketType IdOfType()
        {
            return TicketType.K10;
        }

        internal override string GameTypeForReports()
        {
            return QueryMakerOfHistoricalKeno.ID_K10_NO_MUL_NO_BULL;
        }

    }
    internal sealed class TicketKeno12Single : TicketKenoSingle
    {
        internal TicketKeno12Single(Player player, decimal baseCost, LotteryKeno lotteryKeno, DateTime drawdate, string drawId, int[] numbers, DateTime creationDate, decimal ticketCost, Prizes prizes) :
            base(player, baseCost, lotteryKeno, drawdate, drawId, numbers, creationDate, ticketCost, prizes)
        {
        }
        internal override TicketType IdOfType()
        {
            return TicketType.K12;
        }

        internal override string GameTypeForReports()
        {
            return QueryMakerOfHistoricalKeno.ID_K12_NO_MUL_NO_BULL;
        }

    }
}
