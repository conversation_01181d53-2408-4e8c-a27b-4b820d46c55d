﻿using System;
using System.Data.SqlClient;
using System.Text;
using MySql.Data.MySqlClient;
using Puppeteer.EventSourcing.Interprete;
using Puppeteer.EventSourcing.Interprete.Libraries;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Data;
using System.Threading;
using System.Threading.Tasks;
using System.Data.Common;
using System.IO;
using System.Collections.Generic;
using System.IO.Compression;
using System.Xml;
using System.Transactions;
using System.Reflection;

namespace Puppeteer.EventSourcing.DB
{
    internal class Dairy
    {

        private readonly string connectionString;
        private readonly string name;
        private readonly DairyStorage dairyStorage;
        public const string EXECUTION_ERROR_TAG = "//EXECUTION ERROR WAS DETECTED ON THIS COMMAND";
        public const string RETRY_TCP_TAG = "//TCP PROVIDER ERROR. IT IS A RETRY";
        public const bool RECOVERY_USING_SKIP_FLAG = true;

        internal Dairy(DatabaseType dbType, string connectionString, string name)
        {
            if (String.IsNullOrEmpty(connectionString)) throw new ArgumentNullException(nameof(connectionString));
            if (String.IsNullOrEmpty(name)) throw new ArgumentNullException(nameof(name));
            this.connectionString = connectionString;
            this.name = name;
            if (dbType == DatabaseType.MySQL)
            {
                dairyStorage = new DairyStorageMySQL(connectionString, name);
            }
            else if (dbType == DatabaseType.SQLServer)
            {
                dairyStorage = new DairyStorageSQLServer(connectionString, name);
            }
        }

        internal void RecuperarEstado(Actor actor, Parser parser)
        {
            if (actor == null) throw new ArgumentNullException(nameof(actor));
            if (dairyStorage == null) throw new Exception("El Actor no puede guardar ni recuperar su último estado pues no se estableció conexión a ninguna Base de Datos.");
            dairyStorage.RecuperarEstado(actor, parser);
        }

        internal async Task RecuperarEstadoAsync(Actor actor, Parser parser)
        {
            if (actor == null) throw new ArgumentNullException(nameof(actor));
            if (dairyStorage == null) throw new Exception("El Actor no puede guardar ni recuperar su último estado pues no se estableció conexión a ninguna Base de Datos.");
            await dairyStorage.RecuperarEstadoAsync(actor, parser);
        }

        internal void AplicarEstadoEn(Actor actorDestino, Parser parserDestino)
        {
            if (actorDestino == null) throw new ArgumentNullException(nameof(actorDestino));
            if (dairyStorage == null) throw new Exception("No se puede aplicar el Diario pues no se estableció conexión a ninguna Base de Datos.");
            dairyStorage.AplicarEstadoEn(actorDestino, parserDestino);
        }

        internal void AplicarEstadoEnFollower(Actor actor, Parser parser, ActorFollower follower)
        {
            if (actor == null) throw new ArgumentNullException(nameof(actor));
            if (dairyStorage == null) throw new Exception("No se puede aplicar el Diario pues no se estableció conexión a ninguna Base de Datos.");
            dairyStorage.AplicarEstadoEnFollower(actor, parser, follower);
        }

        internal MemoryStream EjecutarArchive(DateTime startDate, DateTime endDate)
        {
            return dairyStorage.EjecutarAchive(startDate, endDate);
        }

        internal void EjecutarTrim(DateTime trimmedDown)
        {
            dairyStorage.EjecutarTrim(trimmedDown);
        }

        public static IEnumerable<string> ListarActoresACargar(string dbType, string connectionString, double porcentaMinimoDeAporte)
        {
            if (porcentaMinimoDeAporte < 0 && porcentaMinimoDeAporte > 100) throw new ArgumentException(nameof(porcentaMinimoDeAporte));

            IEnumerable<string> actoresACargar = new List<string>();

            if (dbType == DatabaseType.SQLServer.ToString())
            {

                actoresACargar = DairyStorageSQLServer.GetActorsToLoad(connectionString, porcentaMinimoDeAporte);
            }
            else if (dbType == DatabaseType.MySQL.ToString())
            {
                actoresACargar = DairyStorageMySQL.GetActorsToLoad(connectionString, porcentaMinimoDeAporte);
            }
            else
            {
                throw new Exception("El Actor listar los actores a cargar pues el tipo de Base de Datos no existe.");
            }

            return actoresACargar;
        }

        internal int AplicarEstadoEnRedBlack(Actor actor, Parser parser, int lastRecoveredId)
        {
            if (actor == null) throw new ArgumentNullException(nameof(actor));
            if (dairyStorage == null) throw new Exception("No se puede aplicar el Diario pues no se estableció conexión a ninguna Base de Datos.");
            return dairyStorage.AplicarEstadoEnRedBlack(actor, parser, lastRecoveredId);
        }

        internal void EscribirEnDairy(int dairyStorageId, string script, IpAddress ip, UserInLog user, DateTime now)
        {
            dairyStorage.DateOfLastActivity = DateTime.Now;
            dairyStorage.EscribirEnDairy(dairyStorageId, script, ip, user, now);
        }

        internal async Task EscribirEnDairyAsync(int dairyStorageId, string script, IpAddress ip, UserInLog user, DateTime now)
        {
            dairyStorage.DateOfLastActivity = DateTime.Now;
            await dairyStorage.EscribirEnDairyAsync(dairyStorageId, script, ip, user, now);
        }

        internal int TakeAndIncrementId()
        {
            var id = ++dairyStorage.Id;
            return id;
        }

        internal int Id
        {
            get
            {
                return dairyStorage.Id;
            }
        }

        internal DateTime DateOfLastActivity
        {
            get
            {
                return dairyStorage.DateOfLastActivity;
            }
        }

        private abstract class DairyStorage
        {
            protected readonly string connectionString;
            protected readonly string name;

            protected static StreamWriter swDairyPeriodRangeToExport;
            protected static MemoryStream msDairyPeriodRangeToExport;

            internal int Id = 0;
            internal DateTime DateOfLastActivity = DateTime.Now;


            protected DairyStorage(string connectionString, string name)
            {
                this.connectionString = connectionString;
                this.name = name;
            }

            protected internal abstract void RecuperarEstado(Actor actor, Parser parser);

            [Obsolete]
            protected internal abstract void EscribirEnDairy(string script, IpAddress ip, UserInLog user, DateTime now);
            protected internal abstract void EscribirEnDairy(int id, string script, IpAddress ip, UserInLog user, DateTime now);
            [Obsolete]
            protected internal abstract Task EscribirEnDairyAsync(string script, IpAddress ip, UserInLog user, DateTime now);
            protected internal abstract Task EscribirEnDairyAsync(int id, string script, IpAddress ip, UserInLog user, DateTime now);
            protected internal abstract void AplicarEstadoEn(Actor actorDestino, Parser parserDestino);
            protected internal abstract void AplicarEstadoEnFollower(Actor actorDestino, Parser parserDestino, ActorFollower follower);
            protected internal abstract int AplicarEstadoEnRedBlack(Actor actor, Parser parser, int lastRecoveredId);
            protected internal abstract Task RecuperarEstadoAsync(Actor actor, Parser parser);

            protected internal abstract MemoryStream EjecutarAchive(DateTime fechaInicio, DateTime fechaFin);
            protected internal abstract IEnumerable<string> NombreDeLosActores(string name);
            protected internal abstract void EjecutarTrim(DateTime trimmedDown);
            internal abstract void ChangePrimaryKey();

            private const double CANTIDAD_DE_DIAS_CON_APORTE_MINIMO = 3;


            protected static int CalcularMaximoDeActoresACargar(IEnumerable<int> acumuladoPorDia, double porcentaMinimoDeAporte)
            {
                if (acumuladoPorDia == null) throw new ArgumentException(nameof(acumuladoPorDia));
                if (porcentaMinimoDeAporte < 0 && porcentaMinimoDeAporte > 100) throw new ArgumentException(nameof(porcentaMinimoDeAporte));

                var actoresAcumulados = 0;
                var acumuladoDeDiasConAporteMinimo = 0;
                double porcentajeTotalDelDia = 0;


                foreach (var acumuladoDiaActual in acumuladoPorDia)
                {
                    actoresAcumulados += acumuladoDiaActual;
                    porcentajeTotalDelDia = ((double)acumuladoDiaActual / actoresAcumulados) * 100;

                    /*
                    acumuladoDeDiasConAporteMinimo = (porcentajeTotalDelDia < PORCENTAJE_MINIMO_DE_APORTE) ? acumuladoDeDiasConAporteMinimo + 1 : 0
					*/

                    if (porcentajeTotalDelDia < porcentaMinimoDeAporte)
                    {
                        acumuladoDeDiasConAporteMinimo++;
                    }
                    else
                    {
                        acumuladoDeDiasConAporteMinimo = 0;
                    }

                    if (acumuladoDeDiasConAporteMinimo >= CANTIDAD_DE_DIAS_CON_APORTE_MINIMO)
                    {
                        break;
                    }
                }
                return actoresAcumulados;
            }

            protected void SaveTempFileToZip(ZipArchive archive, string fileName)
            {
                try
                {
                    ZipArchiveEntry entry = archive.CreateEntry(fileName);
                    using (Stream stream = new MemoryStream(msDairyPeriodRangeToExport.GetBuffer()))
                    {
                        using (Stream entryStream = entry.Open())
                        {
                            stream.CopyTo(entryStream);
                        }
                    }
                }
                catch (Exception e)
                {
                }
            }
        }

        private class DairyStorageMySQL : DairyStorage
        {
            internal DairyStorageMySQL(string connectionString, string name) : base(connectionString, name)
            {
            }

            private async Task<bool> CrearDairyAsync(String nombreDelDiario)
            {
                StringBuilder statement = new StringBuilder();
                bool created = false;

                statement
                    .Append("SELECT count(TABLE_NAME) alreadyexists FROM information_schema.TABLES WHERE TABLE_NAME = ")
                    .Append("'").Append(nombreDelDiario).Append("' ")
                    .Append(" AND TABLE_SCHEMA in (SELECT DATABASE());");

                statement
                    .Append("create table IF NOT EXISTS ").Append(nombreDelDiario)
                    .Append("(")
                    .Append("id BIGINT UNSIGNED NOT NULL,")
                    .Append("FechaHora DATETIME(3) NOT NULL,")
                    .Append("Ip VARCHAR(39) NOT NULL,")
                    .Append("User VARCHAR(45) NOT NULL,")
                    .Append("Script TEXT NOT NULL,")
                    .Append("Skip TINYINT(1) NOT NULL DEFAULT 0,")
                    .Append("PRIMARY KEY (id)")
#if DEBUG
                    .Append(") CHARSET=utf8;");
#else
                    .Append(") ENGINE=InnoDB CHARSET=utf8;");
#endif
                string sql = statement.ToString();
                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    using (MySqlCommand command = new MySqlCommand(sql, connection))
                    using (DbDataReader reader = await command.ExecuteReaderAsync())
                    {
                        await reader.ReadAsync();
                        created = reader.GetInt32(0) == 0;

                        await reader.CloseAsync();
                    }
                    await connection.CloseAsync();
                }

                return created;
            }

            private bool CrearDairy(String nombreDelDiario)
            {
                StringBuilder statement = new StringBuilder();
                bool created = false;

                statement
                    .Append("SELECT count(TABLE_NAME) alreadyexists FROM information_schema.TABLES WHERE TABLE_NAME = ")
                    .Append("'").Append(nombreDelDiario).Append("' ")
                    .Append(" AND TABLE_SCHEMA in (SELECT DATABASE());");

                statement
                    .Append("create table IF NOT EXISTS ").Append(nombreDelDiario)
                    .Append("(")
                    .Append("id BIGINT UNSIGNED NOT NULL,")
                    .Append("FechaHora DATETIME(3) NOT NULL,")
                    .Append("Ip VARCHAR(39) NOT NULL,")
                    .Append("User VARCHAR(45) NOT NULL,")
                    .Append("Script TEXT NOT NULL,")
                    .Append("Skip TINYINT(1) NOT NULL DEFAULT 0,")
                    .Append("PRIMARY KEY (id)")
                    .Append(") ENGINE=InnoDB CHARSET=utf8;");
                string sql = statement.ToString();
                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    connection.Open();
                    using (MySqlCommand command = new MySqlCommand(sql, connection))
                    using (DbDataReader reader = command.ExecuteReader())
                    {
                        reader.Read();
                        created = reader.GetInt32(0) == 0;

                        reader.Close();
                    }
                    connection.Close();
                }

                return created;
            }

            private void CrearFollower()
            {
                StringBuilder statement = new StringBuilder();

                statement
                    .Append("create table IF NOT EXISTS Follower")
                    .Append("(")
                    .Append("FollowerId INT UNSIGNED NOT NULL,")
                    .Append("DairyId BIGINT UNSIGNED NOT NULL,")
                    .Append("Description VARCHAR(45) NOT NULL,")
                    .Append("PRIMARY KEY (FollowerId)")
                    .Append(") ENGINE=InnoDB CHARSET=utf8;");
                string sql = statement.ToString();
                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (MySqlCommand command = new MySqlCommand(sql, connection))
                        {
                            command.ExecuteNonQuery();
                        }
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
            }

            protected internal override void RecuperarEstado(Actor actor, Parser parser)
            {
                actor.ItsANewOne = CrearDairy(name);

                const int BLOQUEAR_SI_CRECE_HASTA = 100000;

                int ultimoId = 0;
                int delta = 0;
                bool salir = false;
                int cantidadDeLeaderInitialization = 0;
                int avanceParcial = 0;
                int porcentajeDeAvanceTotal = 0;
                int cadaCuantoMostrarAvance = Int32.MaxValue;
                string whereSkip = RECOVERY_USING_SKIP_FLAG ? " WHERE Skip = 0 " : "";

                string sqlCount = "SELECT Count(*) Cantidad FROM " + base.name + " " + whereSkip + " and id > " + ultimoId;

                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    connection.Open();

                    using (MySqlCommand command = new MySqlCommand(sqlCount, connection))
                    using (DbDataReader reader = command.ExecuteReader())
                    {
                        reader.Read();
                        int totalDeRegistros = reader.GetInt32(0);
                        cadaCuantoMostrarAvance = (int)(totalDeRegistros / 100.0 + 1.0);
                        reader.Close();
                    }
                    connection.Close();

                }

                using (BlockingCollection<Programa> prgQueue = new BlockingCollection<Programa>(BLOQUEAR_SI_CRECE_HASTA))
                {
                    _ = Task.Run(() =>
                    {
                        try
                        {
                            int fails = 0;
                            while (!salir)
                            {
                                string sql = "SELECT FechaHora, Script, Ip, User, Id FROM " + base.name + " " + whereSkip + " and id > " + ultimoId + " ORDER BY id";

                                using (MySqlConnection connection = new MySqlConnection(connectionString))
                                {
                                    try
                                    {
                                        connection.Open();

                                        using (MySqlCommand command = new MySqlCommand(sql, connection))
                                        using (DbDataReader reader = command.ExecuteReader())
                                        {
                                            try
                                            {
                                                bool salirRead = false;
                                                int intentos = 5;
                                                while (!salirRead && intentos > 0)
                                                {
                                                    try
                                                    {
                                                        while (reader.Read())
                                                        {
                                                            fails = 0;
                                                            DateTime fechaHora = reader.GetDateTime(0);
                                                            string script = reader.GetString(1);
                                                            string ipAddress = reader.GetString(2);

                                                            IpAddress ip = new IpAddress(ipAddress);
                                                            string serializedUser = reader.GetString(3);
                                                            UserInLog user = UserInLog.GenerateUserBasedOn(serializedUser);

                                                            Id = reader.GetInt32(4);

                                                            parser.EstablecerComando(script);
                                                            Programa programa = parser.Procesar(false, false);
                                                            programa.Now = new Libraries.FechaHora(fechaHora);
                                                            programa.Ip = ip;
                                                            programa.User = user;
                                                            programa.DairyId = Id;
                                                            prgQueue.Add(programa);
                                                        }
                                                        salirRead = true;
                                                    }
                                                    catch (MySql.Data.MySqlClient.MySqlException mysqlException)
                                                    {
                                                        intentos--;
                                                        fails++;
                                                        Console.WriteLine($"Current fails : {fails} Error in : {base.name}.");
                                                        Console.WriteLine(mysqlException);
                                                        Console.WriteLine();
                                                    }
                                                }
                                            }
                                            catch (Exception e)
                                            {
                                                Console.WriteLine($"Error in : {base.name}.");
                                                Console.WriteLine(e);
                                            }
                                            finally
                                            {
                                                reader.Close();
                                            }
                                        }
                                    }

                                    finally
                                    {
                                        connection.Close();
                                    }
                                }


                                delta = ultimoId - Id;
                                ultimoId = Id;
                                if (delta == 0)
                                {
                                    const bool AT_LEAST_IS_NECESSARY_ONE_MORE_TIME = false;
                                    if (cantidadDeLeaderInitialization < 1 && actor.OnLeaderInitialization != null)
                                    {
                                        actor.OnLeaderInitialization();
                                        salir = AT_LEAST_IS_NECESSARY_ONE_MORE_TIME;
                                        cantidadDeLeaderInitialization++;
                                    }
                                    else
                                    {
                                        salir = delta == 0;
                                    }
                                }
                                else
                                {
                                    salir = delta == 0;
                                }
                            }
                        }
                        catch (Exception e) //As is inside a fire and forget, this is needed to see the error and execute the CompleteAdding unlocking the main process.
                        {
                            Console.WriteLine($"Error in : {base.name}.");
                            Console.WriteLine(e);
                        }
                        finally
                        {
                            prgQueue.CompleteAdding();
                        }
                    });

                    foreach (Programa p in prgQueue.GetConsumingEnumerable())
                    {
                        try
                        {
                            //JONATHAN: Comente esta linea para que no consuma
                            actor.Perform(p);

                            avanceParcial++;
                            if (avanceParcial == cadaCuantoMostrarAvance)
                            {
                                porcentajeDeAvanceTotal++;
                                avanceParcial = 0;
                                Console.Write($"{porcentajeDeAvanceTotal}%");
                            }
                        }
                        catch
                        {
                            if (p.Script.IndexOf(Dairy.EXECUTION_ERROR_TAG) == -1)
                            {
                                Console.WriteLine("Existe un comando con error LEVANTANDO en el DairyId: " + p.DairyId + " con el script");
                                Console.WriteLine("-- >[" + p.Script.Replace("\r", System.Environment.NewLine) + "]<--");
                            }
                            else
                            {
                                Console.WriteLine($"Existe un {Dairy.EXECUTION_ERROR_TAG} previamente EJECUTADO en produccion en el DairyId: {p.DairyId}");
                            }
                        }
                    }
                }
            }


            protected internal override void AplicarEstadoEn(Actor actorDestino, Parser parserDestino)
            {
                Programa programa;

                string sql = "SELECT FechaHora, Script, Ip, User, Id FROM " + base.name + " ORDER BY id";

                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (MySqlCommand command = new MySqlCommand(sql, connection))
                        using (MySqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                DateTime fechaHora = reader.GetDateTime("FechaHora");
                                string script = reader.GetString("Script");

                                string ipAddress = reader.GetString("Ip");

                                IpAddress ip = new IpAddress(ipAddress);
                                string serializedUser = reader.GetString("User");
                                UserInLog user = UserInLog.GenerateUserBasedOn(serializedUser);

                                Id = reader.GetInt32("Id");

                                parserDestino.EstablecerComando(script);
                                programa = parserDestino.Procesar(false, false);
                                programa.Now = new Libraries.FechaHora(fechaHora);
                                programa.Ip = ip;
                                programa.User = user;
                                programa.DairyId = Id;
                                actorDestino.Perform(programa);
                            }
                            reader.Close();
                        }
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
            }

            private int UltimoDairyId(ActorFollower follower)
            {
                string sql = "SELECT DairyId FROM Follower WHERE FollowerId = " + follower.FollowerId;
                int result = 0;
                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (MySqlCommand command = new MySqlCommand(sql, connection))
                        using (MySqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                result = reader.GetInt32("DairyId");
                            }
                            reader.Close();
                        }
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
                if (result != 0) follower.DairyId = result;
                return result;
            }

            protected internal override void AplicarEstadoEnFollower(Actor actor, Parser parser, ActorFollower follower)
            {
                CrearFollower();

                bool salir = false;
                int ultimoId = UltimoDairyId(follower);
                const int LEVANTAR_DESDE_INICIO = 0;
                int delta = LEVANTAR_DESDE_INICIO;
                Programa programa;
                int fails = 0;

                follower.StartSpan(MethodBase.GetCurrentMethod().Name + "-" + actor.Name, "db");

                while (!follower.Finished && !salir)
                {
                    salir = true;
                    string sql = "SELECT FechaHora, Script, Ip, User, Id FROM " + base.name + $" nolock where id > {delta} AND SKIP = 0 ORDER BY id";

                    using (MySqlConnection connection = new MySqlConnection(connectionString))
                    {
                        try
                        {
                            connection.Open();
                            using (MySqlCommand command = new MySqlCommand(sql, connection))
                            using (DbDataReader reader = command.ExecuteReader())
                            {
                                try
                                {
                                    bool salirRead = false;
                                    int intentos = 5;
                                    while (!salirRead && intentos > 0)
                                    {
                                        try
                                        {
                                            while (reader.Read())
                                            {
                                                DateTime fechaHora = reader.GetDateTime(0);
                                                string script = reader.GetString(1);
                                                string ipAddress = reader.GetString(2);

                                                IpAddress ip = new IpAddress(ipAddress);
                                                string serializedUser = reader.GetString(3);
                                                UserInLog user = UserInLog.GenerateUserBasedOn(serializedUser);

                                                Id = reader.GetInt32(4);

                                                parser.EstablecerComando(script);
                                                programa = parser.Procesar(false, false);
                                                programa.Now = new Libraries.FechaHora(fechaHora);
                                                programa.Ip = ip;
                                                programa.User = user;
                                                programa.DairyId = Id;
                                                if (follower.ConEstado)
                                                {
                                                    try
                                                    {
                                                        actor.Perform(programa);
                                                    }
                                                    catch
                                                    {
                                                        if (programa.Script.IndexOf(Dairy.EXECUTION_ERROR_TAG) == -1)
                                                        {
                                                            Console.WriteLine("Existe un comando con error LEVANTANDO en el DairyId: " + programa.DairyId + " con el script");
                                                            Console.WriteLine("-- >[" + programa.Script.Replace("\r", System.Environment.NewLine) + "]<--");
                                                        }
                                                        else
                                                        {
                                                            Console.WriteLine($"Existe un {Dairy.EXECUTION_ERROR_TAG} previamente EJECUTADO en produccion en el DairyId: {programa.DairyId}");
                                                        }
                                                    }
                                                    bool needsToMatchRules = Id > ultimoId;
                                                    if (needsToMatchRules)
                                                        follower.MatchRules(programa, actor);
                                                }
                                                else
                                                {
                                                    follower.MatchRules(programa);
                                                }

                                                salirRead = true;
                                            }
                                        }
                                        catch (MySql.Data.MySqlClient.MySqlException mysqlException)
                                        {
                                            salir = false;
                                            intentos--;
                                            fails++;
                                            Console.WriteLine($"Current fails : {fails} Error in : {base.name}.");
                                            Console.WriteLine(mysqlException);
                                            Console.WriteLine();
                                        }
                                        ultimoId = Id;
                                        delta = Id;
                                        ActualizarUltimoDairyId(follower, ultimoId);
                                    }
                                }
                                catch (Exception e)
                                {
                                    follower.CaptureException(e, actor.Name);
                                    Console.WriteLine($"Error in : {base.name}.");
                                    Console.WriteLine(e);
                                }
                                finally
                                {
                                    reader.Close();
                                }

                            }
                        }
                        finally
                        {
                            connection.Close();
                        }
                    }
                }
                follower.EndSpan(MethodBase.GetCurrentMethod().Name + "-" + actor.Name);
                follower.DisposeSpan(MethodBase.GetCurrentMethod().Name + "-" + actor.Name);
                follower.FinalizeRules();
            }

            private void ActualizarUltimoDairyId(ActorFollower follower, int ultimoId)
            {
                if (ultimoId > follower.DairyId)
                {
                    string sql;
                    if (follower.DairyId != 0)
                        sql = "UPDATE Follower SET DairyId = " + ultimoId + " WHERE FollowerId = " + follower.FollowerId;
                    else
                        sql = $"INSERT INTO Follower (FollowerId, DairyId, Description) VALUES ({follower.FollowerId}, {ultimoId}, '{follower.Actor.GetType().Name} Follower')";

                    try
                    {
                        using (MySqlConnection connection = new MySqlConnection(connectionString))
                        {
                            try
                            {
                                connection.Open();
                                using (MySqlCommand command = new MySqlCommand(sql, connection))
                                {
                                    command.CommandType = CommandType.Text;
                                    command.ExecuteNonQuery();
                                    follower.DairyId = ultimoId;
                                }
                            }
                            finally
                            {
                                connection.Close();
                            }
                        }
                    }
                    catch
                    {

                    }
                }
            }

            private string SqlCommand(string script, IpAddress ip, UserInLog user, DateTime now)
            {
                if (String.IsNullOrEmpty(script)) throw new ArgumentNullException(nameof(script));
                StringBuilder statement = new StringBuilder()
                    .Append("insert into ")
                    .Append(base.name)
                    .Append("(FechaHora,Ip,User,Script) values (")
                        .Append('"').Append(now.ToString("yyyy-MM-dd HH:mm:ss.fff")).Append('"')
                        .Append(',')
                        .Append('"').Append(ip.Ip).Append('"')
                        .Append(',')
                        .Append(user.ToMySQLFormat())
                        .Append(',')
                        .Append('"')
                        .Append(
                            script
                            .Replace("\\", "\\\\")
                            .Replace(LiteralHilera.SLASH_OR_SINGLE_QUOTED_CHARACTER, '\\') // SLASH_OR_SINGLE_QUOTED_CHARACTER Para MySQL es SLASH
                            .Replace("" + LiteralHilera.DOUBLE_QUOTED_CHARACTER, "\\\"")
                            .Replace("" + LiteralHilera.PIPE_CHARACTER, "\\\\"))
                        .Append('"')
                    .Append(')');
                string sql = statement.ToString();
                return sql;
            }
            private string SqlCommand(int id, string script, IpAddress ip, UserInLog user, DateTime now)
            {
                if (String.IsNullOrEmpty(script)) throw new ArgumentNullException(nameof(script));
                StringBuilder statement = new StringBuilder()
                    .Append("insert into ")
                    .Append(base.name)
                        .Append("(id,FechaHora,Ip,User,Script) values (")
                        .Append(id)
                        .Append(',')
                        .Append('"').Append(now.ToString("yyyy-MM-dd HH:mm:ss.fff")).Append('"')
                        .Append(',')
                        .Append('"').Append(ip.Ip).Append('"')
                        .Append(',')
                        .Append(user.ToMySQLFormat())
                        .Append(',')
                        .Append('"')
                        .Append(
                            script
                            .Replace("\\", "\\\\")
                            .Replace(LiteralHilera.SLASH_OR_SINGLE_QUOTED_CHARACTER, '\\') // SLASH_OR_SINGLE_QUOTED_CHARACTER Para MySQL es SLASH
                            .Replace("" + LiteralHilera.DOUBLE_QUOTED_CHARACTER, "\\\"")
                            .Replace("" + LiteralHilera.PIPE_CHARACTER, "\\\\"))
                        .Append('"')
                    .Append(')');
                string sql = statement.ToString();
                return sql;
            }

            protected internal override async Task EscribirEnDairyAsync(string script, IpAddress ip, UserInLog user, DateTime now)
            {
                string sqlCommand = SqlCommand(script, ip, user, now);

                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        await connection.OpenAsync();
                        using (MySqlCommand command = new MySqlCommand(sqlCommand.ToString(), connection))
                        {
                            await command.ExecuteNonQueryAsync();
                        }
                    }
                    catch (MySqlException e)
                    {
                        Loggers.GetIntance().Db.Error($@"sql:{sqlCommand} type:{e.GetType()} error:{e.Message}", e);
                        throw new Exception("Error al escribir en MySQL el Script en el Dairy: [" + sqlCommand + "]. " + e.Message);
                    }
                    catch (Exception e)
                    {
                        Loggers.GetIntance().Db.Error($@"sql:{sqlCommand} type:{e.GetType()} error:{e.Message}", e);
                        throw e;
                    }
                    finally
                    {
                        await connection.CloseAsync();
                    }
                }
            }

            protected internal override async Task EscribirEnDairyAsync(int dairyStorageId, string script, IpAddress ip, UserInLog user, DateTime now)
            {
                string sqlCommand = SqlCommand(dairyStorageId, script, ip, user, now);

                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        await connection.OpenAsync();
                        using (MySqlCommand command = new MySqlCommand(sqlCommand.ToString(), connection))
                        {
                            await command.ExecuteNonQueryAsync();
                        }
                    }
                    catch (MySqlException e)
                    {
                        Loggers.GetIntance().Db.Error($@"sql:{sqlCommand} type:{e.GetType()} error:{e.Message}", e);
                        throw new Exception("Error al escribir en MySQL el Script en el Dairy: [" + sqlCommand + "]. " + e.Message);
                    }
                    catch (Exception e)
                    {
                        Loggers.GetIntance().Db.Error($@"sql:{sqlCommand} type:{e.GetType()} error:{e.Message}", e);
                        throw e;
                    }
                    finally
                    {
                        await connection.CloseAsync();
                    }
                }
            }

            protected internal override void EscribirEnDairy(string script, IpAddress ip, UserInLog user, DateTime now)
            {
                string sqlCommand = SqlCommand(script, ip, user, now);

                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (MySqlCommand command = new MySqlCommand(sqlCommand, connection))
                        {
                            command.ExecuteNonQuery();
                        }
                    }
                    catch (MySqlException e)
                    {
                        Loggers.GetIntance().Db.Error($@"sql:{sqlCommand} type:{e.GetType()} error:{e.Message}", e);
                        throw new Exception("Error al escribir en MySQL el Script en el Dairy: [" + sqlCommand + "]. " + e.Message);
                    }
                    catch (Exception e)
                    {
                        Loggers.GetIntance().Db.Error($@"sql:{sqlCommand} type:{e.GetType()} error:{e.Message}", e);
                        throw e;
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
            }

            protected internal override void EscribirEnDairy(int dairyStorageId, string script, IpAddress ip, UserInLog user, DateTime now)
            {
                string sqlCommand = SqlCommand(dairyStorageId, script, ip, user, now);

                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (MySqlCommand command = new MySqlCommand(sqlCommand, connection))
                        {
                            command.ExecuteNonQuery();
                        }
                    }
                    catch (MySqlException e)
                    {
                        Loggers.GetIntance().Db.Error($@"sql:{sqlCommand} type:{e.GetType()} error:{e.Message}", e);
                        throw new Exception("Error al escribir en MySQL el Script en el Dairy: [" + sqlCommand + "]. " + e.Message);
                    }
                    catch (Exception e)
                    {
                        Loggers.GetIntance().Db.Error($@"sql:{sqlCommand} type:{e.GetType()} error:{e.Message}", e);
                        throw e;
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
            }

            internal override void ChangePrimaryKey()
            {
                Debug.WriteLine("Mysql tables doesn't need any change.");
            }

            protected internal override int AplicarEstadoEnRedBlack(Actor actor, Parser parser, int lastRecoveredId)
            {
                int LEVANTAR_DESDE_INICIO = lastRecoveredId;
                int delta = LEVANTAR_DESDE_INICIO;

                string sql = "SELECT FechaHora, Script, Ip, User, Id FROM " + base.name + $" nolock where id > {delta} ORDER BY id";

                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (MySqlCommand command = new MySqlCommand(sql, connection))
                        using (DbDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                DateTime fechaHora = reader.GetDateTime(0);
                                string script = reader.GetString(1);

                                string ipAddress = reader.GetString(2);

                                IpAddress ip = new IpAddress(ipAddress);
                                string serializedUser = reader.GetString(3);
                                UserInLog user = UserInLog.GenerateUserBasedOn(serializedUser);

                                Id = reader.GetInt32(4);

                                parser.EstablecerComando(script);
                                Programa programa = parser.Procesar(false, false);
                                programa.Now = new Libraries.FechaHora(fechaHora);
                                programa.Ip = ip;
                                programa.User = user;
                                programa.DairyId = Id;

                                try
                                {
                                    if (programa.Script.IndexOf(Dairy.EXECUTION_ERROR_TAG) == -1)
                                    {
                                        actor.Perform(programa);
                                    }
                                }
                                catch
                                {
                                    if (programa.Script.IndexOf(Dairy.EXECUTION_ERROR_TAG) == -1)
                                    {
                                        Console.WriteLine("Existe un comando con error LEVANTANDO en el DairyId: " + programa.DairyId + " con el script");
                                        Console.WriteLine("-- >[" + programa.Script.Replace("\r", System.Environment.NewLine) + "]<--");
                                    }
                                    else
                                    {
                                        Console.WriteLine($"Existe un {Dairy.EXECUTION_ERROR_TAG} previamente EJECUTADO en produccion en el DairyId: {programa.DairyId}");
                                    }
                                }
                            }
                            reader.Close();
                        }
                    }
                    finally
                    {
                        connection.Close();
                    }
                }

                delta = Id;
                return delta;
            }
            protected internal override async Task RecuperarEstadoAsync(Actor actor, Parser parser)
            {
                actor.ItsANewOne = await CrearDairyAsync(name);

                const int BLOQUEAR_SI_CRECE_HASTA = 100000;

                int ultimoId = 0;
                int delta = 0;
                bool salir = false;
                int cantidadDeLeaderInitialization = 0;
                using (BlockingCollection<Programa> prgQueue = new BlockingCollection<Programa>(BLOQUEAR_SI_CRECE_HASTA))
                {
                    var readertask = Task.Run(async () =>
                    {
                        try
                        {
                            while (!salir)
                            {
                                string whereSkip = RECOVERY_USING_SKIP_FLAG ? " WHERE Skip = 0 " : "";
                                string sql = "SELECT FechaHora, Script, Ip, User, Id FROM " + base.name + " " + whereSkip + " and id > " + ultimoId + " ORDER BY id";
                                string sqlCount = "SELECT Count(*) Cantidad FROM " + base.name + whereSkip + " and id > " + ultimoId;

                                int avanceParcial = 0;
                                int porcentajeDeAvanceTotal = 0;
                                int cadaCuantoMostrarAvance = Int32.MaxValue;
                                using (MySqlConnection connection = new MySqlConnection(connectionString))
                                {
                                    try
                                    {
                                        await connection.OpenAsync();
                                        using (MySqlCommand command = new MySqlCommand(sqlCount, connection))
                                        using (DbDataReader reader = await command.ExecuteReaderAsync())
                                        {
                                            await reader.ReadAsync();
                                            int totalDeRegistros = reader.GetInt32(0);
                                            cadaCuantoMostrarAvance = (int)(totalDeRegistros / 100.0 + 1.0);
                                            await reader.CloseAsync();
                                        }

                                        using (MySqlCommand command = new MySqlCommand(sql, connection))
                                        using (DbDataReader reader = await command.ExecuteReaderAsync())
                                        {
                                            while (await reader.ReadAsync())
                                            {
                                                avanceParcial++;
                                                if (avanceParcial == cadaCuantoMostrarAvance)
                                                {
                                                    porcentajeDeAvanceTotal++;
                                                    avanceParcial = 0;
                                                    Console.Write($"{porcentajeDeAvanceTotal}%");
                                                }

                                                DateTime fechaHora = reader.GetDateTime(0);
                                                string script = reader.GetString(1);

                                                DateOfLastActivity = fechaHora;

                                                string ipAddress = reader.GetString(2);

                                                IpAddress ip = new IpAddress(ipAddress);
                                                string serializedUser = reader.GetString(3);
                                                UserInLog user = UserInLog.GenerateUserBasedOn(serializedUser);

                                                Id = reader.GetInt32(4);

                                                parser.EstablecerComando(script);
                                                Programa programa = parser.Procesar(false, false);
                                                programa.Now = new Libraries.FechaHora(fechaHora);
                                                programa.Ip = ip;
                                                programa.User = user;
                                                programa.DairyId = Id;
                                                prgQueue.Add(programa);
                                            }
                                            await reader.CloseAsync();
                                        }
                                    }
                                    finally
                                    {
                                        await connection.CloseAsync();
                                    }
                                }
                                delta = ultimoId - Id;
                                ultimoId = Id;
                                if (delta == 0)
                                {
                                    const bool AT_LEAST_IS_NECESSARY_ONE_MORE_TIME = false;
                                    if (cantidadDeLeaderInitialization < 1 && actor.OnLeaderInitialization != null)
                                    {
                                        actor.OnLeaderInitialization();
                                        salir = AT_LEAST_IS_NECESSARY_ONE_MORE_TIME;
                                        cantidadDeLeaderInitialization++;
                                    }
                                    else
                                    {
                                        salir = delta == 0;
                                    }
                                }
                                else
                                {
                                    salir = delta == 0;
                                }
                            }
                        }
                        catch (Exception e) //As is inside a fire and forget, this is needed to see the error and execute the CompleteAdding unlocking the main process.
                        {
                            Console.WriteLine($"Error in : {base.name}.");
                            Console.WriteLine(e);
                        }
                        finally
                        {
                            prgQueue.CompleteAdding();
                        }
                    });

                    foreach (Programa p in prgQueue.GetConsumingEnumerable())
                    {
                        try
                        {
                            actor.Perform(p);
                        }
                        catch
                        {
                            if (p.Script.IndexOf(Dairy.EXECUTION_ERROR_TAG) == -1)
                            {
                                Console.WriteLine("Existe un comando con error LEVANTANDO en el DairyId: " + p.DairyId + " con el script");
                                Console.WriteLine("-- >[" + p.Script.Replace("\r", System.Environment.NewLine) + "]<--");
                            }
                            else
                            {
                                Console.WriteLine($"Existe un {Dairy.EXECUTION_ERROR_TAG} previamente EJECUTADO en produccion en el DairyId: {p.DairyId}");
                            }
                        }
                    }

                    await Task.WhenAll(readertask);
                }
            }

            protected internal override MemoryStream EjecutarAchive(DateTime fechaInicio, DateTime fechaFin)
            {
                IEnumerable<string> actorsNames = NombreDeLosActores(name);
                if (actorsNames == null) return null;

                //ClearZipStream();
                MemoryStream compressedFileForArchive = new MemoryStream();
                ZipArchive archive = new ZipArchive(compressedFileForArchive, ZipArchiveMode.Create, false);

                StringBuilder insertString = new StringBuilder();

                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    insertString.Append("USE ");
                    insertString.Append(connection.Database);
                    insertString.AppendLine(";");

                    try
                    {
                        connection.Open();
                        foreach (var aName in actorsNames)
                        {
                            msDairyPeriodRangeToExport = new MemoryStream();
                            swDairyPeriodRangeToExport = new StreamWriter(msDairyPeriodRangeToExport, Encoding.UTF8);


                            var fileName = aName + "-" + fechaFin.ToString("yyyyMMdd") + "_bak.sql";
                            string sql = "SELECT FechaHora, Ip, User, Script, Skip, Id FROM " + aName + " nolock WHERE FechaHora >= '" + fechaInicio.ToString("yyyy-MM-dd HH:mm:ss") + "' AND FechaHora < '" + fechaFin.ToString("yyyy-MM-dd HH:mm:ss") + "' AND Skip = 1 ORDER BY id";
                            using (MySqlCommand command = new MySqlCommand(sql, connection))
                            using (MySqlDataReader reader = command.ExecuteReader())
                            {
                                if (!reader.HasRows) continue;

                                command.CommandTimeout = 60;
                                while (reader.Read())
                                {
                                    DateTime fechaHora = reader.GetDateTime(0);
                                    string script = reader.GetString(3);

                                    string ip = reader.GetString(1);

                                    UserInLog user = UserInLog.GenerateUserBasedOn(reader.GetString(2));
                                    byte skip = Convert.ToByte(reader.GetBoolean(4));
                                    int id = reader.GetInt32(5);

                                    insertString.Append("INSERT INTO ");
                                    insertString.Append(aName);
                                    insertString.Append("(id, FechaHora, Ip1, Ip2, Ip3, Ip4, User, Script, Skip) ");
                                    insertString.Append("VALUES (");
                                    insertString.Append(id);
                                    insertString.Append(',');
                                    insertString.Append("STR_TO_DATE('"); insertString.Append(fechaHora); insertString.Append("','%m/%d/%Y %h:%i:%s %p')");   //STR_TO_DATE('4/23/2019 9:37:16 AM','%m/%d/%Y %h:%i:%s %p')
                                    insertString.Append(',');
                                    insertString.Append(ip);
                                    insertString.Append(',');
                                    insertString.Append(user);
                                    insertString.Append(',');
                                    insertString.Append("'" + script.Replace("'", "\\\'").Replace("\"", "\\\"") + "'");
                                    insertString.Append(',');
                                    insertString.Append(skip);
                                    insertString.AppendLine(");");

                                    swDairyPeriodRangeToExport.Write(insertString);
                                    swDairyPeriodRangeToExport.Flush();

                                    insertString.Clear();
                                }
                                reader.Close();

                                if (msDairyPeriodRangeToExport != null) SaveTempFileToZip(archive, fileName);
                            }

                        }
                    }
                    finally
                    {
                        connection.Close();
                        archive.Dispose();
                        compressedFileForArchive.Dispose();
                    }
                }

                return compressedFileForArchive;
            }

            protected override internal IEnumerable<string> NombreDeLosActores(string name)
            {
                List<string> actors = new List<string>();

                var existActor = ProbarSiEsTablaNueva(name);
                if (existActor && name != "general")
                {
                    actors.Add(name);
                }
                else if (ProbarSiEsTablaNueva("general"))
                {
                    using (MySqlConnection connection = new MySqlConnection(connectionString))
                    {
                        StringBuilder sql = new StringBuilder();

                        sql.Append("SELECT TABLE_NAME FROM ");
                        sql.Append(connection.Database);
                        sql.Append(".INFORMATION_SCHEMA.TABLES WHERE (TABLE_NAME LIKE 'C%' AND TABLE_NAME NOT LIKE 'C%[_]%')");
                        try
                        {
                            connection.Open();
                            using (MySqlCommand command = new MySqlCommand(sql.ToString(), connection))
                            using (MySqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    actors.Add(reader.GetString(0));
                                }
                                reader.Close();
                            }
                        }
                        finally
                        {
                            connection.Close();
                        }
                    }
                }

                return actors;
            }

            private bool ProbarSiEsTablaNueva(string tableName)
            {
                bool esTablaNueva = true;
                string sql = "SELECT 1 FROM " + tableName + " LIMIT 1";
                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (MySqlCommand command = new MySqlCommand(sql, connection))
                        {
                            var dataReader = command.ExecuteReader();
                            dataReader.Close();
                        }
                    }
                    catch
                    {
                        esTablaNueva = false;
                    }
                    finally
                    {
                        connection.Close();
                    }
                }

                return esTablaNueva;
            }

            protected internal override void EjecutarTrim(DateTime trimmedDown)
            {
                IEnumerable<string> actorsNames = NombreDeLosActores(name);

                StringBuilder sql = new StringBuilder();
                const string POSTFIX = "_$OLD";

                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        foreach (var aName in actorsNames)
                        {
                            bool needsTrim = NeedsTrim(aName, trimmedDown);

                            if (needsTrim)
                            {
                                /*ARMA EL SCRIPT EL RENAME*/
                                sql.Append("RENAME TABLE ");
                                sql.Append(aName);
                                sql.Append(" TO ");
                                sql.Append(aName);
                                sql.Append(POSTFIX);
                                sql.Append(";");

                                using (MySqlCommand command = new MySqlCommand(sql.ToString(), connection))
                                {
                                    command.ExecuteNonQuery();
                                }

                                sql.Clear();

                                if (!ExisteTabla(aName))
                                {
                                    CrearDairy(aName);
                                }

                                /*ARMA EL SCRIPT DE CRATE TABLE, INDICES Y DATA*/
                                sql.Append("INSERT INTO ");
                                sql.Append(aName);
                                sql.Append("(id, FechaHora, Ip, User, Script, Skip) ");
                                sql.Append(" SELECT id, FechaHora, Ip, User, Script, Skip FROM ");
                                sql.Append(aName);
                                sql.Append(POSTFIX);
                                sql.Append(" WHERE (Skip = 0");
                                sql.Append(" AND FechaHora < '");
                                sql.Append(trimmedDown.ToString("yyyy-MM-dd HH:mm:ss"));
                                sql.Append("') OR FechaHora >= '");
                                sql.Append(trimmedDown.ToString("yyyy-MM-dd HH:mm:ss"));
                                sql.Append("' ORDER BY id;");

                                /*ARMA EL SCRIPT DE DROP TABLE*/
                                sql.AppendLine();
                                sql.Append("DROP TABLE ");
                                sql.Append(aName);
                                sql.Append(POSTFIX);
                                sql.Append(';');

                                using (MySqlCommand command = new MySqlCommand(sql.ToString(), connection))
                                {
                                    command.CommandTimeout = 200;
                                    command.CommandType = CommandType.Text;
                                    command.ExecuteNonQuery();
                                }
                            }
                        }
                    }
                    catch (SqlException e)
                    {
                        Loggers.GetIntance().Db.Error($@"sql:{sql} type:{e.GetType()} error:{e.Message}", e);

                        throw new Exception("Error al escribir en SQLServer el Script en el Dairy: [" + sql + "]. " + e.Message);
                    }
                    finally
                    {
                        connection.Close();
                        sql.Clear();
                    }
                }
            }

            private bool NeedsTrim(string aName, DateTime trimmedDown)
            {
                bool needsTrim = false;

                string sql = $"SELECT * FROM {aName} NOLOCK WHERE Skip = 1 AND FechaHora > '{trimmedDown.ToString("yyyy-M-dd HH:mm:ss.fff")}' LIMIT 1;";

                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (MySqlCommand command = new MySqlCommand(sql.ToString(), connection))
                        using (MySqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                needsTrim = true;
                            }
                            reader.Close();
                        }
                    }
                    catch
                    {
                    }
                    finally
                    {
                        connection.Close();
                    }
                }

                return needsTrim;
            }

            private bool ExisteTabla(string tableName)
            {
                bool existe = false;
                StringBuilder statement = new StringBuilder();
                statement.Append("SELECT count(TABLE_NAME) alreadyexists FROM information_schema.TABLES WHERE TABLE_NAME = ")
                    .Append("'").Append(tableName).Append("' ")
                    .Append(" AND TABLE_SCHEMA in (SELECT DATABASE());");
                string sql = statement.ToString();
                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (MySqlCommand command = new MySqlCommand(sql, connection))
                        {
                            var resultado = (int)command.ExecuteScalar();
                            existe = resultado == 1;
                        }
                    }
                    catch
                    {
                        existe = false;
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
                return existe;
            }


            protected internal static IEnumerable<string> GetActorsToLoad(string connectionString, double porcentaMinimoDeAporte)
            {
                if (porcentaMinimoDeAporte < 0 && porcentaMinimoDeAporte > 100) throw new ArgumentException(nameof(porcentaMinimoDeAporte));

                List<int> acumuladoPorDia = new List<int>();
                List<string> result = new List<string>(); ;


                string statement = "SELECT DATEDIFF(CURDATE(), FechaHora) As Dias, COUNT(*) As CuentasPorDia FROM accountsLRU GROUP BY DATEDIFF(CURDATE(), FechaHora) ORDER BY Dias ASC;";
                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (MySqlCommand command = new MySqlCommand(statement, connection))
                        using (MySqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var cuentasPorDia = reader.GetInt32(1);
                                acumuladoPorDia.Add(cuentasPorDia);
                            }
                            reader.Close();
                        }

                        int topDeCuentasACargar = CalcularMaximoDeActoresACargar(acumuladoPorDia, porcentaMinimoDeAporte);

                        statement = $"SELECT Cuenta, FechaHora, DATEDIFF(CURDATE(), FechaHora) As Dias FROM accountsLRU ORDER BY Dias Asc LIMIT {topDeCuentasACargar};";

                        using (MySqlCommand command = new MySqlCommand(statement, connection))
                        using (MySqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                result.Add(reader.GetString(0));
                            }
                            reader.Close();
                        }

                    }
                    finally
                    {
                        connection.Close();
                    }
                }

                return result;
            }
        }

        internal void ChangePrimaryKey()
        {
            dairyStorage.ChangePrimaryKey();
        }

        private class DairyStorageSQLServer : DairyStorage
        {
            internal readonly string sqlServerInsertCommand;

            internal DairyStorageSQLServer(string connectionString, string name) : base(connectionString, name)
            {
                this.sqlServerInsertCommand = "insert into " + name + " (id, FechaHora,Ip,[User],Script) values (@id, @fechahora,@ip,@userId,@script)";
            }

            private bool ExisteTabla(string tableName)
            {
                bool existe = false;
                StringBuilder statement = new StringBuilder();
                statement.Append("IF EXISTS(")
                    .Append("SELECT 1 FROM INFORMATION_SCHEMA.TABLES ")
                    .Append("WHERE TABLE_NAME = '" + tableName + "')")
                    .Append("SELECT 1 ELSE SELECT 0;");
                string sql = statement.ToString();
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (SqlCommand command = new SqlCommand(sql, connection))
                        {
                            var resultado = (int)command.ExecuteScalar();
                            existe = resultado == 1;
                        }
                    }
                    catch
                    {
                        existe = false;
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
                return existe;
            }
            private async Task<bool> CrearDairyAsync(string tableName)
            {
                bool created = false;
                StringBuilder statement = new StringBuilder();
                statement
                    .Append("IF NOT EXISTS(")
                    .Append("SELECT 1 FROM INFORMATION_SCHEMA.TABLES ")
                    .Append("WHERE TABLE_NAME = '").Append(tableName).Append("')")
                    .Append(" BEGIN")
                    .Append("  create table ").Append(tableName)
                    .Append("  (")
                    .Append("  id INT PRIMARY KEY,")
                    .Append("  FechaHora DATETIME NOT NULL,")
                    .Append("  Ip VARCHAR(39) NOT NULL,")
                    .Append("  [User] VARCHAR(45) NOT NULL,")
                    .Append("  Script TEXT NOT NULL,")
                    .Append("  [Skip] BIT NOT NULL DEFAULT 0")
                    .Append(" );")
                    .Append(" SELECT 1 created;")
                    .Append(" END")
                    .Append(" ELSE")
                    .Append(" BEGIN")
                    .Append("  SELECT 0 created;")
                    .Append(" END");

                string sql = statement.ToString();

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    using (SqlCommand command = new SqlCommand(sql, connection))
                    using (SqlDataReader reader = await command.ExecuteReaderAsync())
                    {
                        await reader.ReadAsync();
                        created = reader.GetInt32(0) == 1;

                        await reader.CloseAsync();
                    }
                    await connection.CloseAsync();
                }

                return created;
            }
            private bool CrearDairy(string tableName)
            {
                bool created = false;
                StringBuilder statement = new StringBuilder();
                statement
                    .Append("IF NOT EXISTS(")
                    .Append("SELECT 1 FROM INFORMATION_SCHEMA.TABLES ")
                    .Append("WHERE TABLE_NAME = '").Append(tableName).Append("')")
                    .Append(" BEGIN")
                    .Append("  create table ").Append(tableName)
                    .Append("  (")
                    .Append("  id INT PRIMARY KEY,")
                    .Append("  FechaHora DATETIME NOT NULL,")
                    .Append("  Ip VARCHAR(39) NOT NULL,")
                    .Append("  [User] VARCHAR(45) NOT NULL,")
                    .Append("  Script TEXT NOT NULL,")
                    .Append("  [Skip] BIT NOT NULL DEFAULT 0")
                    .Append(" );")
                    .Append(" SELECT 1 created;")
                    .Append(" END")
                    .Append(" ELSE")
                    .Append(" BEGIN")
                    .Append("  SELECT 0 created;")
                    .Append(" END");

                string sql = statement.ToString();

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand(sql, connection))
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        reader.Read();
                        created = reader.GetInt32(0) == 1;

                        reader.Close();
                    }
                    connection.Close();
                }

                return created;
            }
            private void CrearFollower()
            {
                StringBuilder statement = new StringBuilder();

                statement
                    .Append("create table Follower")
                    .Append("(")
                    .Append("FollowerId INT NOT NULL PRIMARY KEY,")
                    .Append("DairyId INT NOT NULL,")
                    .Append("Description VARCHAR(45) NOT NULL")
                    .Append(");");
                string sql = statement.ToString();
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (SqlCommand command = new SqlCommand(sql, connection))
                        {
                            command.ExecuteNonQuery();
                        }
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
            }

            protected internal override void RecuperarEstado(Actor actor, Parser parser)
            {
                actor.ItsANewOne = CrearDairy(name);

                const int BLOQUEAR_SI_CRECE_HASTA = 100000;

                int ultimoId = 0;
                int delta = 0;
                bool salir = false;
                int cantidadDeLeaderInitialization = 0;
                using (BlockingCollection<Programa> prgQueue = new BlockingCollection<Programa>(BLOQUEAR_SI_CRECE_HASTA))
                {
                    _ = Task.Run(() =>
                    {
                        try
                        {
                            bool isLottoApi = base.name.ToUpper() == "LOTTOACTOR";
                            while (!salir)
                            {
                                string whereSkip = RECOVERY_USING_SKIP_FLAG ? " WHERE Skip = 0 " : "";
                                string sql = "SELECT FechaHora, Script, Ip, [User], Id FROM " + base.name + " WITH (nolock) " + whereSkip + " and id > " + ultimoId + " ORDER BY id";
                                string sqlCount = "SELECT Count(*) Cantidad FROM " + base.name + " WITH (nolock) " + whereSkip + " and id > " + ultimoId;

                                int avanceParcial = 0;
                                int porcentajeDeAvanceTotal = 0;
                                int cadaCuantoMostrarAvance = Int32.MaxValue;
                                using (SqlConnection connection = new SqlConnection(connectionString))
                                {
                                    try
                                    {
                                        connection.Open();
                                        using (SqlCommand command = new SqlCommand(sqlCount, connection))
                                        using (SqlDataReader reader = command.ExecuteReader())
                                        {
                                            reader.Read();
                                            int totalDeRegistros = reader.GetInt32(0);
                                            cadaCuantoMostrarAvance = (int)(totalDeRegistros / 100.0 + 1.0);
                                            reader.Close();
                                        }

                                        using (SqlCommand command = new SqlCommand(sql, connection))
                                        using (SqlDataReader reader = command.ExecuteReader())
                                        {
                                            while (reader.Read())
                                            {
                                                avanceParcial++;
                                                if (avanceParcial == cadaCuantoMostrarAvance)
                                                {
                                                    porcentajeDeAvanceTotal++;
                                                    avanceParcial = 0;
                                                    Console.Write($"{porcentajeDeAvanceTotal}%");
                                                }

                                                DateTime fechaHora = reader.GetDateTime(0);
                                                string script = reader.GetString(1);
                                                string ipAddress = reader.GetString(2);

                                                IpAddress ip = new IpAddress(ipAddress);
                                                UserInLog user = UserInLog.GenerateUserBasedOn(reader.GetString(3));
                                                int id = reader.GetInt32(4);

                                                Id = id;

                                                parser.EstablecerComando(script);
                                                Programa programa = parser.Procesar(false, false);
                                                programa.Now = new Libraries.FechaHora(fechaHora);
                                                programa.Ip = ip;
                                                programa.User = user;
                                                programa.DairyId = Id;
                                                prgQueue.Add(programa);
                                            }
                                            reader.Close();
                                        }
                                    }
                                    finally
                                    {
                                        connection.Close();
                                    }
                                }

                                delta = ultimoId - Id;
                                ultimoId = Id;
                                if (delta == 0)
                                {
                                    const bool AT_LEAST_IS_NECESSARY_ONE_MORE_TIME = false;
                                    if (cantidadDeLeaderInitialization < 1 && actor.OnLeaderInitialization != null)
                                    {
                                        actor.OnLeaderInitialization();
                                        salir = AT_LEAST_IS_NECESSARY_ONE_MORE_TIME;
                                        cantidadDeLeaderInitialization++;
                                    }
                                    else
                                    {
                                        salir = delta == 0;
                                    }
                                }
                                else
                                {
                                    salir = delta == 0;
                                }
                            }
                        }
                        catch (Exception e) //As is inside a fire and forget, this is needed to see the error and execute the CompleteAdding unlocking the main process.
                        {
                            Console.WriteLine($"Error in : {base.name}.");
                            Console.WriteLine(e);
                        }
                        finally
                        {
                            prgQueue.CompleteAdding();
                        }
                    });

                    foreach (Programa p in prgQueue.GetConsumingEnumerable())
                    {
                        try
                        {
                            actor.Perform(p);
                        }
                        catch
                        {
                            if (p.Script.IndexOf(Dairy.EXECUTION_ERROR_TAG) == -1)
                            {
                                Console.WriteLine("Existe un comando con error LEVANTANDO en el DairyId: " + p.DairyId + " con el script");
                                Console.WriteLine("-- >[" + p.Script.Replace("\r", System.Environment.NewLine) + "]<--");
                            }
                            else
                            {
                                Console.WriteLine($"Existe un {Dairy.EXECUTION_ERROR_TAG} previamente EJECUTADO en produccion en el DairyId: {p.DairyId}");
                            }
                        }
                    }
                }
            }

            protected internal override void AplicarEstadoEn(Actor actorDestino, Parser parserDestino)
            {
                string sql = "SELECT FechaHora, Script, Ip, [User], Id FROM " + base.name + " ORDER BY id";
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (SqlCommand command = new SqlCommand(sql, connection))
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                DateTime fechaHora = reader.GetDateTime(0);
                                string script = reader.GetString(1);
                                string ipAddress = reader.GetString(2);

                                IpAddress ip = new IpAddress(ipAddress);
                                UserInLog user = UserInLog.GenerateUserBasedOn(reader.GetString(3));
                                Id = reader.GetInt32(4);

                                parserDestino.EstablecerComando(script);
                                Programa programa = parserDestino.Procesar(false, false);
                                programa.Now = new Libraries.FechaHora(fechaHora);
                                programa.Ip = ip;
                                programa.User = user;
                                programa.DairyId = Id;
                                actorDestino.Perform(programa);
                            }
                            reader.Close();
                        }
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
            }

            private int UltimoDairyId(ActorFollower follower)
            {
                string sql = "SELECT DairyId FROM Follower WHERE FollowerId = " + follower.FollowerId;
                int result = 0;
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (SqlCommand command = new SqlCommand(sql, connection))
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                result = reader.GetInt32(0);
                            }
                            reader.Close();
                        }
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
                if (result != 0) follower.DairyId = result;
                return result;
            }

            protected internal override void AplicarEstadoEnFollower(Actor actor, Parser parser, ActorFollower follower)
            {
                if (!ExisteTabla("Follower"))
                {
                    CrearFollower();
                }

                bool salir = false;
                int ultimoId = UltimoDairyId(follower);
                const int LEVANTAR_DESDE_INICIO = 0;
                int delta = LEVANTAR_DESDE_INICIO;
                while (!follower.Finished && !salir)
                {
                    salir = true;
                    //TODO: Revertir cambios
                    //string sql = "SELECT FechaHora, Script, Ip, [User], Id FROM " + base.name + $" WITH (nolock) WHERE Id > {delta} AND SKIP = 0 ORDER BY id";
                    string sql = "SELECT FechaHora, Script, 0, [User], Id FROM " + base.name + $" WITH (nolock) WHERE Id > {delta} ORDER BY id";

                    using (SqlConnection connection = new SqlConnection(connectionString))
                    {
                        try
                        {
                            connection.Open();
                            using (SqlCommand command = new SqlCommand(sql, connection))
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    DateTime fechaHora = reader.GetDateTime(0);
                                    string script = reader.GetString(1);
                                    //string ipAddress = reader.GetString(2);

                                    //IpAddress ip = new IpAddress(ipAddress);
                                    IpAddress ip = IpAddress.DEFAULT;
                                    UserInLog user = UserInLog.GenerateUserBasedOn(reader.GetString(3));
                                    Id = reader.GetInt32(4);


                                    if (follower.ConEstado)
                                    {
                                        parser.EstablecerComando(script);
                                        Programa programa = parser.Procesar(false, false);
                                        programa.Now = new Libraries.FechaHora(fechaHora);
                                        programa.Ip = ip;
                                        programa.User = user;
                                        programa.DairyId = Id;

                                        try
                                        {
                                            actor.Perform(programa);
                                        }
                                        catch
                                        {
                                            if (programa.Script.IndexOf(Dairy.EXECUTION_ERROR_TAG) == -1)
                                            {
                                                Console.WriteLine("Existe un comando con error LEVANTANDO en el DairyId: " + programa.DairyId + " con el script");
                                                Console.WriteLine("-- >[" + programa.Script.Replace("\r", System.Environment.NewLine) + "]<--");
                                            }
                                            else
                                            {
                                                Console.WriteLine($"Existe un {Dairy.EXECUTION_ERROR_TAG} previamente EJECUTADO en produccion en el DairyId: {programa.DairyId}");
                                            }
                                        }
                                        bool needsToMatchRules = Id > ultimoId;
                                        if (needsToMatchRules)
                                            follower.MatchRules(programa, actor);
                                    }
                                    else
                                    {
                                        follower.MatchRules(script, Id, fechaHora);
                                    }
                                }
                                reader.Close();
                            }
                        }
                        finally
                        {
                            connection.Close();
                        }
                    }

                    ultimoId = Id;
                    delta = Id;
                    ActualizarUltimoDairyId(follower, ultimoId);
                    follower.PartialUpdate();
                    Thread.Sleep(TimeSpan.FromMinutes(1));
                }
            }

            private void ActualizarUltimoDairyId(ActorFollower follower, int ultimoId)
            {
                if (ultimoId > follower.DairyId)
                {
                    string sql;
                    if (follower.DairyId != 0)
                        sql = "UPDATE Follower SET DairyId = " + ultimoId + " WHERE FollowerId = " + follower.FollowerId;
                    else
                        sql = $"INSERT INTO Follower (FollowerId, DairyId, Description) VALUES ({follower.FollowerId}, {ultimoId}, '{follower.Actor.GetType().Name} Follower')";

                    try
                    {
                        using (SqlConnection connection = new SqlConnection(connectionString))
                        {
                            try
                            {
                                connection.Open();
                                using (SqlCommand command = new SqlCommand(sql, connection))
                                {
                                    command.CommandType = CommandType.Text;
                                    command.ExecuteNonQuery();
                                    follower.DairyId = ultimoId;
                                }
                            }
                            finally
                            {
                                connection.Close();
                            }
                        }
                    }
                    catch
                    {

                    }
                }
            }


            private string SqlCommand(string script, IpAddress ip, UserInLog user, DateTime now)
            {
                if (String.IsNullOrEmpty(script)) throw new ArgumentNullException(nameof(script));
                StringBuilder statement = new StringBuilder()
                    .Append("insert into ")
                    .Append(base.name)
                    .Append("(FechaHora,Ip,[User],Script) values (")
                        .Append('\'').Append(now.ToString("yyyy-MM-dd HH:mm:ss.fff")).Append('\'')
                        .Append(',')
                        .Append('\'').Append(ip.Ip).Append('\'')
                        .Append(',')
                        .Append(user.ToMySQLFormat())
                        .Append(',')
                        .Append('\'')
                        .Append(
                            script
                            .Replace(LiteralHilera.SLASH_OR_SINGLE_QUOTED_CHARACTER, '\'') //SLASH_OR_SINGLE_QUOTED_CHARACTER, Para SQLServer es SINGLE_QUOTED
                            .Replace(LiteralHilera.DOUBLE_QUOTED_CHARACTER, '"')
                            .Replace(LiteralHilera.PIPE_CHARACTER, '\\'))
                        .Append('\'')
                    .Append(')');
                string sql = statement.ToString();
                return sql;
            }
            private string SqlCommand(int id, string script, IpAddress ip, UserInLog user, DateTime now)
            {
                if (String.IsNullOrEmpty(script)) throw new ArgumentNullException(nameof(script));
                StringBuilder statement = new StringBuilder()
                    .Append("insert into ")
                    .Append(base.name)
                    .Append("(id, FechaHora,Ip,[User],Script) values (")
                        .Append(id)
                        .Append(',')
                        .Append('\'').Append(now.ToString("yyyy-MM-dd HH:mm:ss.fff")).Append('\'')
                        .Append(',')
                        .Append('\'').Append(ip.Ip).Append('\'')
                        .Append(',')
                        .Append(user.ToMySQLFormat())
                        .Append(',')
                        .Append('\'')
                        .Append(
                            script
                            .Replace(LiteralHilera.SLASH_OR_SINGLE_QUOTED_CHARACTER, '\'') //SLASH_OR_SINGLE_QUOTED_CHARACTER, Para SQLServer es SINGLE_QUOTED
                            .Replace(LiteralHilera.DOUBLE_QUOTED_CHARACTER, '"')
                            .Replace(LiteralHilera.PIPE_CHARACTER, '\\'))
                        .Append('\'')
                    .Append(')');
                string sql = statement.ToString();
                return sql;
            }

            private string UnEscapeSQLServer(string script)
            {
                StringBuilder result = new StringBuilder();
                foreach (char c in script)
                {
                    switch (c)
                    {
                        case LiteralHilera.SLASH_OR_SINGLE_QUOTED_CHARACTER:
                            break;
                        case LiteralHilera.DOUBLE_QUOTED_CHARACTER:
                            result.Append('"');
                            break;
                        case LiteralHilera.PIPE_CHARACTER:
                            result.Append('\\');
                            break;
                        default:
                            result.Append(c);
                            break;
                    }
                }
                return result.ToString();
            }

            protected internal override async Task EscribirEnDairyAsync(string originalScript, IpAddress ip, UserInLog user, DateTime now)
            {
                string script = originalScript;
                bool retry;
                do
                {
                    retry = false;
                    using (SqlConnection connection = new SqlConnection(connectionString))
                    {
                        try
                        {
                            await connection.OpenAsync();
                            using (SqlCommand command = new SqlCommand(this.sqlServerInsertCommand, connection))
                            {
                                command.Parameters.Add("@fechahora", SqlDbType.DateTime);
                                command.Parameters.Add("@ip", SqlDbType.VarChar, 39);
                                command.Parameters.Add("@userId", SqlDbType.VarChar, 45);
                                command.Parameters.Add("@script", SqlDbType.Text);

                                command.Parameters["@fechahora"].Value = now;
                                command.Parameters["@ip"].Value = ip.Ip;
                                command.Parameters["@userId"].Value = user.ToMySQLFormat();
                                command.Parameters["@script"].Value = UnEscapeSQLServer(script);

                                _ = await command.ExecuteNonQueryAsync();
                            }
                        }
                        catch (SqlException e)
                        {
                            var sqlCommand = SqlCommand(script, ip, user, now);
                            Loggers.GetIntance().Db.Error($@"sql:{sqlCommand} type:{e.GetType()} error:{e.Message}", e);

                            if (e.Message.Contains("A transport-level error has occurred when receiving results from the server."))
                            {
                                retry = true;
                                script = Dairy.RETRY_TCP_TAG + '\r' + originalScript;
                            }
                            else if (e.Message.Contains("A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections."))
                            {
                                retry = true;
                                script = Dairy.RETRY_TCP_TAG + '\r' + originalScript;
                            }
                            else
                            {
                                throw new Exception("Error al escribir en SQLServer el Script en el Dairy: [" + sqlCommand + "]. " + e.Message);
                            }
                        }
                        catch (Exception e)
                        {
                            var sqlCommand = SqlCommand(script, ip, user, now);
                            Loggers.GetIntance().Db.Error($@"sql:{sqlCommand} type:{e.GetType()} error:{e.Message}", e);
                            throw e;
                        }
                        finally
                        {
                            await connection.CloseAsync();
                        }
                    }
                }
                while (retry);
            }

            protected internal override async Task RecuperarEstadoAsync(Actor actor, Parser parser)
            {
                actor.ItsANewOne = await CrearDairyAsync(name);

                const int BLOQUEAR_SI_CRECE_HASTA = 100000;

                int ultimoId = 0;
                int delta = 0;
                bool salir = false;
                int cantidadDeLeaderInitialization = 0;

                using (BlockingCollection<Programa> prgQueue = new BlockingCollection<Programa>(BLOQUEAR_SI_CRECE_HASTA))
                {
                    var readertask = Task.Run(async () =>
                    {
                        try
                        {
                            bool isLottoApi = base.name.ToUpper() == "LOTTOACTOR";
                            while (!salir)
                            {
                                string whereSkip = RECOVERY_USING_SKIP_FLAG ? " WHERE Skip = 0 " : "";
                                string sql = "SELECT FechaHora, Script, Ip, [User], Id FROM " + base.name + " WITH (nolock) " + " " + whereSkip + " and id > " + ultimoId + " ORDER BY id";
                                string sqlCount = "SELECT Count(*) Cantidad FROM " + base.name + " WITH (nolock) " + whereSkip + " and id > " + ultimoId;

                                int avanceParcial = 0;
                                int porcentajeDeAvanceTotal = 0;
                                int cadaCuantoMostrarAvance = Int32.MaxValue;
                                using (SqlConnection connection = new SqlConnection(connectionString))
                                {
                                    try
                                    {
                                        await connection.OpenAsync();
                                        using (SqlCommand command = new SqlCommand(sqlCount, connection))
                                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                                        {
                                            await reader.ReadAsync();
                                            int totalDeRegistros = reader.GetInt32(0);
                                            cadaCuantoMostrarAvance = (int)(totalDeRegistros / 100.0 + 1.0);
                                            await reader.CloseAsync();
                                        }
                                        await connection.OpenAsync();
                                        using (SqlCommand command = new SqlCommand(sql, connection))
                                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                                        {
                                            while (await reader.ReadAsync())
                                            {
                                                avanceParcial++;
                                                if (avanceParcial == cadaCuantoMostrarAvance)
                                                {
                                                    porcentajeDeAvanceTotal++;
                                                    avanceParcial = 0;
                                                    Console.Write($"{porcentajeDeAvanceTotal}%");
                                                }

                                                DateTime fechaHora = reader.GetDateTime(0);
                                                string script = reader.GetString(1);

                                                DateOfLastActivity = fechaHora;

                                                string ipAddress = reader.GetString(2);

                                                IpAddress ip = new IpAddress(ipAddress);
                                                UserInLog user = UserInLog.GenerateUserBasedOn(reader.GetString(3));
                                                int id = reader.GetInt32(4);

                                                Id = id;

                                                parser.EstablecerComando(script);
                                                Programa programa = parser.Procesar(false, false);
                                                programa.Now = new Libraries.FechaHora(fechaHora);
                                                programa.Ip = ip;
                                                programa.User = user;
                                                programa.DairyId = Id;
                                                prgQueue.Add(programa);
                                            }
                                            await reader.CloseAsync();
                                        }
                                    }
                                    finally
                                    {
                                        await connection.CloseAsync();
                                    }
                                }

                                delta = ultimoId - Id;
                                ultimoId = Id;
                                if (delta == 0)
                                {
                                    const bool AT_LEAST_IS_NECESSARY_ONE_MORE_TIME = false;
                                    if (cantidadDeLeaderInitialization < 1 && actor.OnLeaderInitialization != null)
                                    {
                                        actor.OnLeaderInitialization();
                                        salir = AT_LEAST_IS_NECESSARY_ONE_MORE_TIME;
                                        cantidadDeLeaderInitialization++;
                                    }
                                    else
                                    {
                                        salir = delta == 0;
                                    }
                                }
                                else
                                {
                                    salir = delta == 0;
                                }
                            }
                        }
                        catch (Exception e) //As is inside a fire and forget, this is needed to see the error and execute the CompleteAdding unlocking the main process.
                        {
                            Console.WriteLine($"Error in : {base.name}.");
                            Console.WriteLine(e);
                        }
                        finally
                        {
                            prgQueue.CompleteAdding();
                        }
                    });

                    foreach (Programa p in prgQueue.GetConsumingEnumerable())
                    {
                        try
                        {
                            actor.Perform(p);
                        }
                        catch
                        {
                            if (p.Script.IndexOf(Dairy.EXECUTION_ERROR_TAG) == -1)
                            {
                                Console.WriteLine("Existe un comando con error LEVANTANDO en el DairyId: " + p.DairyId + " con el script");
                                Console.WriteLine("-- >[" + p.Script.Replace("\r", System.Environment.NewLine) + "]<--");
                            }
                            else
                            {
                                Console.WriteLine($"Existe un {Dairy.EXECUTION_ERROR_TAG} previamente EJECUTADO en produccion en el DairyId: {p.DairyId}");
                            }
                        }
                    }

                    await Task.WhenAll(readertask);
                }
            }

            protected internal override async Task EscribirEnDairyAsync(int dairyStorageId, string originalScript, IpAddress ip, UserInLog user, DateTime now)
            {
                string script = originalScript;
                bool retry;
                do
                {
                    retry = false;
                    using (SqlConnection connection = new SqlConnection(connectionString))
                    {
                        try
                        {
                            await connection.OpenAsync();
                            using (SqlCommand command = new SqlCommand(this.sqlServerInsertCommand, connection))
                            {
                                command.Parameters.Add("@id", SqlDbType.BigInt);
                                command.Parameters.Add("@fechahora", SqlDbType.DateTime);
                                command.Parameters.Add("@ip", SqlDbType.VarChar, 39);
                                command.Parameters.Add("@userId", SqlDbType.VarChar, 45);
                                command.Parameters.Add("@script", SqlDbType.Text);

                                command.Parameters["@id"].Value = dairyStorageId;
                                command.Parameters["@fechahora"].Value = now;
                                command.Parameters["@ip"].Value = ip.Ip;
                                command.Parameters["@userId"].Value = user.ToMySQLFormat();
                                command.Parameters["@script"].Value = UnEscapeSQLServer(script);

                                _ = await command.ExecuteNonQueryAsync();
                            }
                        }
                        catch (SqlException e)
                        {
                            var sqlCommand = SqlCommand(dairyStorageId, script, ip, user, now);
                            Loggers.GetIntance().Db.Error($@"sql:{sqlCommand} type:{e.GetType()} error:{e.Message}", e);

                            if (e.Message.Contains("A transport-level error has occurred when receiving results from the server."))
                            {
                                retry = true;
                                script = Dairy.RETRY_TCP_TAG + '\r' + originalScript;
                            }
                            else if (e.Message.Contains("A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections."))
                            {
                                retry = true;
                                script = Dairy.RETRY_TCP_TAG + '\r' + originalScript;
                            }
                            else
                            {
                                throw new Exception("Error al escribir en SQLServer el Script en el Dairy: [" + sqlCommand + "]. " + e.Message);
                            }
                        }
                        catch (Exception e)
                        {
                            var sqlCommand = SqlCommand(dairyStorageId, script, ip, user, now);
                            Loggers.GetIntance().Db.Error($@"sql:{sqlCommand} type:{e.GetType()} error:{e.Message}", e);
                            throw e;
                        }
                        finally
                        {
                            await connection.CloseAsync();
                        }
                    }
                }
                while (retry);
            }

            protected internal override void EscribirEnDairy(string originalScript, IpAddress ip, UserInLog user, DateTime now)
            {
                string script = originalScript;
                bool retry;
                do
                {
                    retry = false;
                    using (SqlConnection connection = new SqlConnection(connectionString))
                    {
                        try
                        {
                            connection.Open();
                            using (SqlCommand command = new SqlCommand(this.sqlServerInsertCommand, connection))
                            {
                                command.Parameters.Add("@fechahora", SqlDbType.DateTime);
                                command.Parameters.Add("@ip", SqlDbType.VarChar, 39);
                                command.Parameters.Add("@userId", SqlDbType.VarChar, 45);
                                command.Parameters.Add("@script", SqlDbType.Text);

                                command.Parameters["@fechahora"].Value = now;
                                command.Parameters["@ip"].Value = ip.Ip;
                                command.Parameters["@userId"].Value = user.ToMySQLFormat();
                                command.Parameters["@script"].Value = UnEscapeSQLServer(script);
                                command.ExecuteNonQuery();
                            }
                        }
                        catch (SqlException e)
                        {
                            var sqlCommand = SqlCommand(script, ip, user, now);
                            Loggers.GetIntance().Db.Error($@"sql:{sqlCommand} type:{e.GetType()} error:{e.Message}", e);

                            if (e.Message.Contains("A transport-level error has occurred when receiving results from the server."))
                            {
                                retry = true;
                                script = Dairy.RETRY_TCP_TAG + '\r' + originalScript;
                            }
                            else
                            {
                                throw new Exception("Error al escribir en SQLServer el Script en el Dairy: [" + sqlCommand + "]. " + e.Message);
                            }
                        }
                        catch (Exception e)
                        {
                            var sqlCommand = SqlCommand(script, ip, user, now);
                            Loggers.GetIntance().Db.Error($@"sql:{sqlCommand} type:{e.GetType()} error:{e.Message}", e);
                            throw e;
                        }
                        finally
                        {
                            connection.Close();
                        }
                    }
                }
                while (retry);
            }

            protected internal override void EscribirEnDairy(int dairyStorageId, string originalScript, IpAddress ip, UserInLog user, DateTime now)
            {
                string script = originalScript;
                bool retry;
                do
                {
                    retry = false;
                    using (SqlConnection connection = new SqlConnection(connectionString))
                    {
                        try
                        {
                            connection.Open();
                            using (SqlCommand command = new SqlCommand(this.sqlServerInsertCommand, connection))
                            {
                                command.Parameters.Add("@id", SqlDbType.BigInt);
                                command.Parameters.Add("@fechahora", SqlDbType.DateTime);
                                command.Parameters.Add("@ip", SqlDbType.VarChar, 39);
                                command.Parameters.Add("@userId", SqlDbType.VarChar, 45);
                                command.Parameters.Add("@script", SqlDbType.Text);

                                command.Parameters["@id"].Value = dairyStorageId;
                                command.Parameters["@fechahora"].Value = now;
                                command.Parameters["@ip"].Value = ip.Ip;
                                command.Parameters["@userId"].Value = user.ToMySQLFormat();
                                command.Parameters["@script"].Value = UnEscapeSQLServer(script);
                                command.ExecuteNonQuery();
                            }
                        }
                        catch (SqlException e)
                        {
                            var sqlCommand = SqlCommand(dairyStorageId, script, ip, user, now);
                            Loggers.GetIntance().Db.Error($@"sql:{sqlCommand} type:{e.GetType()} error:{e.Message}", e);

                            if (e.Message.Contains("A transport-level error has occurred when receiving results from the server."))
                            {
                                retry = true;
                                script = Dairy.RETRY_TCP_TAG + '\r' + originalScript;
                            }
                            else
                            {
                                throw new Exception("Error al escribir en SQLServer el Script en el Dairy: [" + sqlCommand + "]. " + e.Message);
                            }
                        }
                        catch (Exception e)
                        {
                            var sqlCommand = SqlCommand(dairyStorageId, script, ip, user, now);
                            Loggers.GetIntance().Db.Error($@"sql:{sqlCommand} type:{e.GetType()} error:{e.Message}", e);
                            throw e;
                        }
                        finally
                        {
                            connection.Close();
                        }
                    }
                }
                while (retry);
            }

            internal override void ChangePrimaryKey()
            {
                string stCommand = $@"
							sp_rename '{name}', '{name}_old';

							create table {name}
							(
								id INT PRIMARY KEY,
								FechaHora DATETIME NOT NULL,
								Ip VARCHAR(39) NOT NULL, 
								[User] VARCHAR(45) NOT NULL,
								Script TEXT NOT NULL,
								[Skip] BIT NOT NULL DEFAULT 0
							);

							INSERT INTO {name}
							SELECT * FROM {name}_old;
							
							--Its commented
							--DROP TABLE {name}_old;

						";
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (SqlCommand command = new SqlCommand(stCommand, connection))
                        {
                            command.ExecuteNonQuery();
                        }
                    }
                    catch (Exception e)
                    {
                        Loggers.GetIntance().Db.Error($@"sql:{stCommand} type:{e.GetType()} error:{e.Message}", e);
                        throw e;
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
            }

            protected internal override int AplicarEstadoEnRedBlack(Actor actor, Parser parser, int lastRecoveredId)
            {
                int LEVANTAR_DESDE_INICIO = lastRecoveredId;
                int delta = LEVANTAR_DESDE_INICIO;

                string sql = "SELECT FechaHora, Script, Ip, [User], Id FROM " + base.name + $" WITH (nolock) WHERE Id > {delta} ORDER BY id";

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (SqlCommand command = new SqlCommand(sql, connection))
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                DateTime fechaHora = reader.GetDateTime(0);
                                string script = reader.GetString(1);
                                string ipAddress = reader.GetString(2);

                                IpAddress ip = new IpAddress(ipAddress);
                                UserInLog user = UserInLog.GenerateUserBasedOn(reader.GetString(3));
                                Id = reader.GetInt32(4);

                                parser.EstablecerComando(script);
                                Programa programa = parser.Procesar(false, false);
                                programa.Now = new Libraries.FechaHora(fechaHora);
                                programa.Ip = ip;
                                programa.User = user;
                                programa.DairyId = Id;

                                try
                                {
                                    if (programa.Script.IndexOf(Dairy.EXECUTION_ERROR_TAG) == -1)
                                    {
                                        actor.Perform(programa);
                                    }
                                }
                                catch
                                {
                                    if (programa.Script.IndexOf(Dairy.EXECUTION_ERROR_TAG) == -1)
                                    {
                                        Debug.WriteLine("Existe un comando con error LEVANTANDO en el DairyId: " + programa.DairyId + " con el script");
                                        Debug.WriteLine("-- >[" + programa.Script.Replace("\r", System.Environment.NewLine) + "]<--");
                                    }
                                    else
                                    {
                                        Debug.WriteLine($"Existe un {Dairy.EXECUTION_ERROR_TAG} previamente EJECUTADO en produccion en el DairyId: {programa.DairyId}");
                                    }
                                }

                            }
                            reader.Close();
                        }
                    }
                    finally
                    {
                        connection.Close();
                    }
                }

                delta = Id;
                return delta;
            }

            protected internal override MemoryStream EjecutarAchive(DateTime fechaInicio, DateTime fechaFin)
            {

                IEnumerable<string> actorsNames = NombreDeLosActores(name);
                if (actorsNames == null) return null;

                MemoryStream compressedFileForArchive = new MemoryStream();
                ZipArchive archive = new ZipArchive(compressedFileForArchive, ZipArchiveMode.Create, false);


                StringBuilder insertString = new StringBuilder();

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    insertString.Append("USE ");
                    insertString.Append(connection.Database);
                    insertString.Append("\nGO \n");

                    try
                    {
                        connection.Open();
                        foreach (var aName in actorsNames)
                        {
                            msDairyPeriodRangeToExport = new MemoryStream();
                            swDairyPeriodRangeToExport = new StreamWriter(msDairyPeriodRangeToExport, Encoding.UTF8);

                            var fileName = aName + "-" + fechaFin.ToString("yyyyMMdd") + "_bak.sql";
                            string sql = "SELECT FechaHora, Ip1, Ip2, Ip3, Ip4, [User], Script, Skip, Id FROM " + aName + " WITH (nolock) WHERE FechaHora >= '" + fechaInicio + "' AND FechaHora < '" + fechaFin + "' AND Skip = 1 ORDER BY id";
                            using (SqlCommand command = new SqlCommand(sql, connection))
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                if (!reader.HasRows) continue;

                                command.CommandTimeout = 60;
                                insertString.Append("IF NOT EXISTS (SELECT 1 FROM sys.tables WHERE name = '");
                                insertString.Append(aName);
                                insertString.AppendLine("')");
                                insertString.AppendLine("BEGIN");
                                insertString.Append("PRINT 'Table ");
                                insertString.Append('"');
                                insertString.Append(aName);
                                insertString.Append('"');
                                insertString.Append(" does not exists.");
                                insertString.AppendLine("';");
                                insertString.AppendLine("SET noexec on;");
                                insertString.AppendLine("END");

                                while (reader.Read())
                                {
                                    DateTime fechaHora = reader.GetDateTime(0);
                                    string script = reader.GetString(6);

                                    byte ip1 = (byte)reader.GetByte(1);
                                    byte ip2 = (byte)reader.GetByte(2);
                                    byte ip3 = (byte)reader.GetByte(3);
                                    byte ip4 = (byte)reader.GetByte(4);

                                    UserInLog user = UserInLog.GenerateUserBasedOn(reader.GetString(5));
                                    byte skip = Convert.ToByte(reader.GetBoolean(7));
                                    int id = reader.GetInt32(8);

                                    insertString.Append("INSERT [dbo].");
                                    insertString.Append(aName);
                                    insertString.Append(" ([FechaHora], [Ip1], [Ip2], [Ip3], [Ip4], [User], [Script], [Skip], [id]) VALUES (CAST(N'");
                                    insertString.Append(fechaHora);
                                    insertString.Append("' AS DateTime), ");
                                    insertString.Append(ip1);
                                    insertString.Append(", ");
                                    insertString.Append(ip2);
                                    insertString.Append(", ");
                                    insertString.Append(ip3);
                                    insertString.Append(", ");
                                    insertString.Append(ip4);
                                    insertString.Append(", N''");
                                    insertString.Append(user);
                                    insertString.Append("'', N'");
                                    insertString.Append(script.Replace("'", "''"));
                                    insertString.Append("', ");
                                    insertString.Append(skip);
                                    insertString.Append(", ");
                                    insertString.Append(id);
                                    insertString.Append(')');
                                    insertString.Append("\nGO \n");

                                    swDairyPeriodRangeToExport.Write(insertString);
                                    insertString.Clear();
                                }
                                insertString.AppendLine("SET noexec off;");
                                swDairyPeriodRangeToExport.Write(insertString);
                                insertString.Clear();
                                reader.Close();

                                if (msDairyPeriodRangeToExport != null)
                                {
                                    SaveTempFileToZip(archive, fileName);
                                }
                            }

                        }
                    }
                    finally
                    {
                        connection.Close();
                        archive.Dispose();
                        compressedFileForArchive.Dispose();
                    }
                }

                return compressedFileForArchive;
            }

            protected override internal IEnumerable<string> NombreDeLosActores(string name)
            {
                List<string> actors = new List<string>();

                var existActor = ExisteTabla(name);
                if (existActor && name != "general")
                {
                    actors.Add(name);
                }
                else if (ExisteTabla("general"))
                {
                    using (SqlConnection connection = new SqlConnection(connectionString))
                    {
                        StringBuilder sql = new StringBuilder();

                        sql.Append("SELECT TABLE_NAME FROM ");
                        sql.Append(connection.Database);
                        sql.Append(".INFORMATION_SCHEMA.TABLES WHERE (TABLE_NAME LIKE 'C%' AND TABLE_NAME NOT LIKE 'C%[_]%') ORDER BY TABLE_NAME ASC;");
                        try
                        {
                            connection.Open();
                            using (SqlCommand command = new SqlCommand(sql.ToString(), connection))
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    actors.Add(reader.GetString(0));
                                }
                                reader.Close();
                            }
                        }
                        finally
                        {
                            connection.Close();
                        }
                    }
                }

                return actors;
            }

            protected internal override void EjecutarTrim(DateTime trimmedDown)
            {
                IEnumerable<string> actorsNames = NombreDeLosActores(name);

                StringBuilder sql = new StringBuilder();
                const string POSTFIX = "_$OLD";

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        foreach (var aName in actorsNames)
                        {
                            bool needsTrim = NeedsTrim(aName, trimmedDown);

                            if (needsTrim)
                            {
                                /*ARMA EL SCRIPT EL RENAME*/
                                sql.Append("EXEC sp_rename '");
                                sql.Append(aName);
                                sql.Append("', '");
                                sql.Append(aName);
                                sql.Append(POSTFIX);
                                sql.Append("';");

                                using (SqlCommand command = new SqlCommand(sql.ToString(), connection))
                                {
                                    command.ExecuteNonQuery();
                                }

                                sql.Clear();

                                if (!ExisteTabla(aName))
                                {
                                    CrearDairy(aName);
                                }

                                /*ARMA EL SCRIPT DE CRATE TABLE, INDICES Y DATA*/
                                sql.Append("INSERT INTO ");
                                sql.Append(aName);
                                sql.Append("(id, FechaHora, Ip1, Ip2, Ip3, Ip4, [User], Script, [Skip]) ");
                                sql.Append("SELECT id, FechaHora, Ip1, Ip2, Ip3, Ip4, [User], Script, [Skip]");
                                sql.Append(" FROM ");
                                sql.Append(aName);
                                sql.Append(POSTFIX);
                                sql.Append(" WITH(NOLOCK) WHERE (Skip = 0");
                                sql.Append(" AND FechaHora < '");
                                sql.Append(trimmedDown);
                                sql.Append("') OR FechaHora >= '");
                                sql.Append(trimmedDown);
                                sql.Append("' ORDER BY id;");

                                /*ARMA EL SCRIPT DE DROP TABLE*/
                                sql.Append("DROP TABLE ");
                                sql.Append(aName);
                                sql.Append(POSTFIX);
                                sql.Append(';');

                                using (SqlCommand command = new SqlCommand(sql.ToString(), connection))
                                {
                                    command.CommandTimeout = 600;
                                    command.ExecuteNonQuery();
                                }
                            }
                        }
                    }
                    catch (SqlException e)
                    {
                        Loggers.GetIntance().Db.Error($@"sql:{sql} type:{e.GetType()} error:{e.Message}", e);

                        throw new Exception("Error al escribir en SQLServer el Script en el Dairy: [" + sql + "]. " + e.Message);
                    }
                    finally
                    {
                        connection.Close();
                        sql.Clear();
                    }
                }
            }

            private bool NeedsTrim(string aName, DateTime trimmedDown)
            {
                bool needsTrim = false;

                string sql = $"SELECT TOP 1 * FROM {aName} WITH(NOLOCK) WHERE Skip = 1 AND FechaHora > {trimmedDown};";

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (SqlCommand command = new SqlCommand(sql.ToString(), connection))
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                needsTrim = true;
                            }
                            reader.Close();
                        }
                    }
                    catch
                    {
                    }
                    finally
                    {
                        connection.Close();
                    }
                }

                return needsTrim;
            }

            protected internal static IEnumerable<string> GetActorsToLoad(string connectionString, double porcentaMinimoDeAporte)
            {
                throw new NotImplementedException();
            }
        }
    }
}
