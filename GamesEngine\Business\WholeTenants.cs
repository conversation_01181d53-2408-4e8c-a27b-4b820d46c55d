﻿using GamesEngine.MessageQueuing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamesEngine.Business
{
    class WholeTenants: WholeCatalog
    {
        Tenant currentTenant = Tenant.EMPTY;

        internal WholeTenants(Company company) : base(company)
        {
        }
        internal CatalogMember Add(bool itIsThePresent, string name)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (Exists(name)) throw new GameEngineException($"{nameof(name)} '{name}' is already used");

            nextConsecutive = NextConsecutive();
            var tenant = new Tenant(this, nextConsecutive, name);
            members.Add(tenant);
            if (itIsThePresent && (Settings.Integration.UseKafka || Settings.Integration.UseKafkaForAuto))
            {
                var msg = new TenantAddMessage(nextConsecutive, name);
                Settings.Integration.Kafka.Send(itIsThePresent, Settings.Integration.Kafka.TopicForCatalog, msg);
            }

            tenant.Visible = true;
            tenant.Enabled = true;
            return tenant;
        }

        override internal CatalogMember Add(int id, string name)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} {id} must be greater than 0");
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (Exists(name)) throw new GameEngineException($"{nameof(name)} '{name}' is already used");

            if (id > nextConsecutive) nextConsecutive = id;
            var tenant = new Tenant(this, id, name);
            members.Add(tenant);
            return tenant;
        }

        internal void SetCurrent(Tenant tenant)
        {
            if (tenant == null) throw new ArgumentNullException(nameof(tenant));
            if (tenant == Tenant.EMPTY) throw new GameEngineException($"{nameof(Tenant)} cannot be empty");
            if (tenant == currentTenant) throw new GameEngineException($"This is the current store {tenant.Name}");

            currentTenant = tenant;
        }

        internal Tenant CurrentTenant
        {
            get
            {
                if (currentTenant == Tenant.EMPTY) throw new GameEngineException($"{nameof(Tenant)} has not been set");

                return currentTenant;
            }
        }
    }

    class Tenant : CatalogMember
    {
        WholeTenants wholeTenants;
        internal static Tenant EMPTY = new Tenant();

        public Tenant(WholeTenants wholeTenants, int id, string name) : base(id, name)
        {
            this.wholeTenants = wholeTenants;
        }

        private Tenant() : base(int.MinValue, "-")
        {
        }

        internal void MakeCurrent()
        {
            wholeTenants.SetCurrent(this);
        }

    }
}
