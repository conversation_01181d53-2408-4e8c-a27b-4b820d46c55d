﻿using Puppeteer.EventSourcing.Libraries;
using System;
using System.Linq;
using System.Collections.Generic;
using System.Text;
using Puppeteer.EventSourcing;

namespace GamesEngine.Bets
{
    [Puppet]
    internal class PresetBetAmounts : Objeto
    {
        private readonly List<PresetBetAmount> presetBetAmounts = new List<PresetBetAmount>();

        internal int Count
        {
            get
            {
                return presetBetAmounts.Count;
            }
        }

        internal IEnumerable<PresetBetAmount> GetAll
        {
            get
            {
                var result = presetBetAmounts.OrderBy(x => x.Amount).ToList();
                return result;
            }
        }

        internal IEnumerable<decimal> EnabledBetAmounts(decimal min, decimal max)
        {
            if (min < 0) throw new GameEngineException("Amount of bet must be greater than zero");
            if (min > max) throw new GameEngineException("Maximum amount must be greater than minimum one");
            var result = presetBetAmounts.Where(x => x.Amount >= min && x.Amount <= max && x.Enabled).
                Select(x => x.Amount).
                OrderBy(x => x).ToList();
            return result;
        }

        private PresetBetAmount Amount(decimal amount)
        {
            if (amount <= 0) throw new GameEngineException("Amount of bet must be greater than zero");
            var betAmount = presetBetAmounts.Single(x => x.Amount == amount);
            return betAmount;
        }

        private bool Exists(decimal amount)
        {
            if (amount <= 0) throw new GameEngineException("Amount of bet must be greater than zero");
            var exists = presetBetAmounts.Exists(x => x.Amount == amount);
            return exists;
        }

        internal void Add(decimal amount, string employeeName, DateTime now)
        {
            if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));
            if (amount <= 0) throw new GameEngineException("Amount of bet must be greater than zero");
            if (Exists(amount)) throw new GameEngineException($"Amount {amount} is already in preset");

            presetBetAmounts.Add(new PresetBetAmount(amount, employeeName, now));
        }

        internal void Update(decimal currentAmount, decimal newAmount, string employeeName, DateTime now)
        {
            if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));
            if (currentAmount <= 0) throw new GameEngineException("Amount of bet must be greater than zero");
            if (!Exists(currentAmount)) throw new GameEngineException($"Amount {currentAmount} does not exist in preset");
            if (Exists(newAmount)) throw new GameEngineException($"Amount {newAmount} is already in preset");

            var currentBetAmount = Amount(currentAmount);
            currentBetAmount.Update(newAmount, employeeName, now);
        }

        internal void Enable(decimal amount, string employeeName, DateTime now)
        {
            if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));
            if (amount <= 0) throw new GameEngineException("Amount of bet must be greater than zero");
            if (!Exists(amount)) throw new GameEngineException($"Amount {amount} is not preset");

            var betAmount = Amount(amount);
            betAmount.Enable(employeeName, now);
        }

        internal void Disable(decimal amount, string employeeName, DateTime now)
        {
            if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));
            if (amount <= 0) throw new GameEngineException("Amount of bet must be greater than zero");
            if (!Exists(amount)) throw new GameEngineException($"Amount {amount} is not preset");

            var betAmount = Amount(amount);
            betAmount.Disable(employeeName, now);
        }

        internal bool IsEnabled(decimal amount)
        {
            if (amount <= 0) throw new GameEngineException("Amount of bet must be greater than zero");
            if (!Exists(amount)) throw new GameEngineException($"Amount {amount} is not preset");

            var betAmount = Amount(amount);
            return betAmount.Enabled;
        }

        [Puppet]
        internal class PresetBetAmount : Objeto
        {
            private decimal amount;
            private bool enabled;
            private string whoEnteredLastChange;
            private DateTime dateLastChange;

            internal decimal Amount
            {
                get
                {
                    return amount;
                }
            }

            internal bool Enabled
            {
                get
                {
                    return enabled;
                }
            }

            internal string WhoEnteredLastChange
            {
                get
                {
                    return whoEnteredLastChange;
                }
            }

            internal DateTime DateLastChange
            {
                get
                {
                    return dateLastChange;
                }
            }

            internal PresetBetAmount(decimal amount, string employeeName, DateTime now)
            {
                this.amount = amount;
                this.enabled = true;
                whoEnteredLastChange = employeeName;
                dateLastChange = now;
            }

            internal void Update(decimal amount, string employeeName, DateTime now)
            {
                this.amount = amount;
                this.enabled = true;
                whoEnteredLastChange = employeeName;
                dateLastChange = now;
            }

            internal void Enable(string employeeName, DateTime now)
            {
                this.enabled = true;
                whoEnteredLastChange = employeeName;
                dateLastChange = now;
            }

            internal void Disable(string employeeName, DateTime now)
            {
                this.enabled = false;
                whoEnteredLastChange = employeeName;
                dateLastChange = now;
            }
        }
    }
}
