﻿using GamesEngine.Business;
using GamesEngine.MessageQueuing;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static GamesEngine.Finance.PaymentChannels;

namespace GamesEngine
{
    public class CatalogConsumer : Consumer
    {
        public delegate bool ValidateMessageOwnership(string storeAlias);
        ValidateMessageOwnership validateMessageOwnership;
        public delegate RestAPIActorAsync GetActor();
        GetActor getActor;

        public CatalogConsumer(ValidateMessageOwnership validateMessageOwnership, GetActor getActor, string group, string topic) : base(group, topic)
        {
            this.validateMessageOwnership = validateMessageOwnership;
            this.getActor = getActor;
        }

        public override void OnMessageBeforeCommit(string msg)
        {
            if (string.IsNullOrWhiteSpace(msg)) throw new Exception(nameof(msg));

            if (!MessageCatalog.HasToProccessCatalogMessages()) return;
            if (MessageCatalog.WasSendedByMe(msg)) return;
            var catalogMsg = MessageCatalog.Factory(msg);
            
            string scriptToKnowExistence = string.Empty;
            switch (catalogMsg.CatalogAction)
            {
                case CatalogAction.AGENT_ADD:
                    var agentAddMsg = (AgentAddMessage)catalogMsg;
                    var agentPath = agentAddMsg.AgentPath;
                    scriptToKnowExistence = $@"
                            {{
                                exists = marketplace.ExistsAgent('{agentPath}');
                                print exists exists;
                                existsDomain = company.Sales.ExistsDomain('{agentAddMsg.DomainUrl}');
                                print existsDomain existsDomain;
                                existsCurrentStore = company.Sales.HasCurrentStore();
                                print existsCurrentStore existsCurrentStore;
                                if (exists)
                                {{
                                    agent = marketplace.SearchAgent('{agentPath}');
                                    containsStore = agent.ContainsStore({agentAddMsg.OriginStoreId});
                                    print containsStore containsStore;
                                    if (existsDomain)
                                    {{
                                        domain = company.Sales.DomainFrom('{agentAddMsg.DomainUrl}');
                                        containsDomain = agent.ContainsDomain({agentAddMsg.OriginStoreId}, domain);
                                        print containsDomain containsDomain;
                                    }}
                                }}
                            }}
                        ";
                    break;
                case CatalogAction.AGENT_STORE_ONOFF:
                    var agentOnOffMsg = (AgentStoreOnOffMessage)catalogMsg;
                    agentPath = agentOnOffMsg.AgentPath;
                    scriptToKnowExistence = $@"
                            {{
                                exists = marketplace.ExistsAgent('{agentPath}');
                                print exists exists;
                                existsDomain = company.Sales.ExistsDomain('{agentOnOffMsg.DomainUrl}');
                                print existsDomain existsDomain;
                                existsCurrentStore = company.Sales.HasCurrentStore();
                                print existsCurrentStore existsCurrentStore;
                                if (exists)
                                {{
                                    agent = marketplace.SearchAgent('{agentPath}');
                                    containsStore = agent.ContainsStore({agentOnOffMsg.OriginStoreId});
                                    print containsStore containsStore;
                                    if (existsDomain)
                                    {{
                                        domain = company.Sales.DomainFrom('{agentOnOffMsg.DomainUrl}');
                                        containsDomain = agent.ContainsDomain({agentOnOffMsg.OriginStoreId}, domain);
                                        print containsDomain containsDomain;
                                    }}
                                }}
                            }}
                        ";
                    break;
                case CatalogAction.COIN_ADD:
                    var coinAddMsg = (CoinAddMessage)catalogMsg;
                    scriptToKnowExistence = $@"
                            {{
                                exists = company.System.Coins.ExistsIsoCode('{coinAddMsg.Iso4217Code}');
                                print exists exists;
                            }}
                        ";
                    break;
                case CatalogAction.DOMAIN_ADD:
                    var domainAddMessage = (DomainAddMessage)catalogMsg;
                    scriptToKnowExistence = $@"
                            {{
                                exists = company.Sales.ExistsDomain({domainAddMessage.Id});
                                print exists exists;
                            }}
                        ";
                    break;
                case CatalogAction.DOMAIN_ONOFF:
                    var domainOnOffMessage = (DomainOnOffMessage)catalogMsg;
                    scriptToKnowExistence = $@"
                            {{
                                exists = company.Sales.ExistsDomain({domainOnOffMessage.Id});
                                print exists exists;
                                existsCurrentStore = company.Sales.HasCurrentStore();
                                print existsCurrentStore existsCurrentStore;
                            }}
                        ";
                    break;
                case CatalogAction.ENTITY_ADD:
                    var entityAddMessage = (EntityAddMessage)catalogMsg;
                    scriptToKnowExistence = $@"
                            {{
                                exists = company.System.Entities.Exists({entityAddMessage.Id});
                                print exists exists;
                            }}
                        ";
                    break;
                case CatalogAction.PAYMENTMETHOD_ADD:
                    var paymentMethodAddMessage = (PaymentMethodAddMessage)catalogMsg;
                    scriptToKnowExistence = $@"
                            {{
                                exists = company.System.PaymentMethods.Exists({paymentMethodAddMessage.Id});
                                print exists exists;
                            }}
                        ";
                    break;
                case CatalogAction.STORE_ADD:
                    var storeAddMessage = (StoreAddMessage)catalogMsg;
                    scriptToKnowExistence = $@"
                            {{
                                exists = company.System.Stores.Exists({storeAddMessage.Id});
                                print exists exists;
                            }}
                        ";
                    break;
                case CatalogAction.TENANT_ADD:
                    var tenantAddMessage = (TenantAddMessage)catalogMsg;
                    scriptToKnowExistence = $@"
                            {{
                                exists = company.System.Tenants.Exists({tenantAddMessage.Id});
                                print exists exists;
                            }}
                        ";
                    break;
                case CatalogAction.TRANSACTIONTYPEMESSAGE_ADD:
                    var transactionTypeAddMessage = (TransactionTypeAddMessage)catalogMsg;
                    scriptToKnowExistence = $@"
                            {{
                                exists = company.System.TransactionTypes.Exists({transactionTypeAddMessage.Id});
                                print exists exists;
                            }}
                        ";
                    break;
                default:
                    throw new Exception($"No {nameof(CatalogAction)} implementation for {nameof(catalogMsg.CatalogAction)} '{catalogMsg.CatalogAction}'");
            }

            var actor = getActor();
            IActionResult result = actor.PerformQry(scriptToKnowExistence);
            if (!(result is OkObjectResult))
            {
                if (result is ContentResult contentResult) throw new Exception($"Error: {contentResult.Content}");
                if (result is ObjectResult objResult) throw new Exception($"Error: {objResult.Value}");
                throw new Exception($@"Error:{result}");
            }

            var o = (OkObjectResult)result;
            var json = o.Value.ToString();
            StringBuilder script = new StringBuilder();
            var catalogMemberExistence = JsonConvert.DeserializeObject<CatalogMemberExistence>(json);
            switch (catalogMsg.CatalogAction)
            {
                case CatalogAction.AGENT_ADD:
                    var agentAddMessage = (AgentAddMessage)catalogMsg;
                    AddingDomainsAndStore(catalogMemberExistence, agentAddMessage, script);
                    break;
                case CatalogAction.AGENT_STORE_ONOFF:
                    var agentStoreOnOffMessage = (AgentStoreOnOffMessage)catalogMsg;
                    UpdatingDomainsAndStore(catalogMemberExistence, agentStoreOnOffMessage, script);
                    break;
                case CatalogAction.COIN_ADD:
                    var coinAddMsg = (CoinAddMessage)catalogMsg;
                    if (!catalogMemberExistence.exists)
                        script.Append("company.System.Coins.Add(").
                            Append(coinAddMsg.Id).Append(", '").
                            Append(coinAddMsg.Iso4217Code).Append("', '").
                            Append(coinAddMsg.Sign).Append("', ").
                            Append(coinAddMsg.DecimalPrecision).Append(", '").
                            Append(coinAddMsg.Unicode).Append("', '").
                            Append(coinAddMsg.Name).Append("', ").
                            Append(coinAddMsg.Type).Append(");");
                    break;
                case CatalogAction.DOMAIN_ADD:
                    var domainAddMessage = (DomainAddMessage)catalogMsg;
                    if (!catalogMemberExistence.exists)
                        script.Append("company.Sales.CreateDomain(").
                            Append(domainAddMessage.Id).
                            Append(", '").Append(domainAddMessage.Url).
                            Append("', ").Append(Agents.ARTEMIS).Append(");");
                    break;
                case CatalogAction.DOMAIN_ONOFF:
                    var domainOnOffMessage = (DomainOnOffMessage)catalogMsg;
                    if (catalogMemberExistence.existsCurrentStore) 
                    {
                        string enableOrDisableDomain;
                        string commandToMakeVisibleOrInvisibleDomain;
                        if (domainOnOffMessage.Enabled)
                        {
                            enableOrDisableDomain = "company.Sales.CurrentStore.EnableDomain(domain);";
                            commandToMakeVisibleOrInvisibleDomain = "domain.Visible = true;";
                        }
                        else
                        {
                            enableOrDisableDomain = "company.Sales.CurrentStore.DisableDomain(domain);";
                            commandToMakeVisibleOrInvisibleDomain = "domain.Visible = false;";
                        }
                        script.Append($@"
				            {{
					            domain = company.Sales.DomainFrom({domainOnOffMessage.Id});
					            {enableOrDisableDomain}
                                {commandToMakeVisibleOrInvisibleDomain}
				            }}
				        ");
                    }
                    break;
                case CatalogAction.ENTITY_ADD:
                    var entityAddMessage = (EntityAddMessage)catalogMsg;
                    if (!catalogMemberExistence.exists)
                        script.Append("company.System.Entities.Add(").Append(entityAddMessage.Id).Append(",'").Append(entityAddMessage.Name).Append("'); ");
                    break;
                case CatalogAction.PAYMENTMETHOD_ADD:
                    var paymentMethodAddMessage = (PaymentMethodAddMessage)catalogMsg;
                    if (!catalogMemberExistence.exists)
                        script.Append("company.System.PaymentMethods.Add(").Append(paymentMethodAddMessage.Id).Append(",'").Append(paymentMethodAddMessage.Name).Append("'); ");
                    break;
                case CatalogAction.STORE_ADD:
                    var storeAddMessage = (StoreAddMessage)catalogMsg;
                    if (!catalogMemberExistence.exists)
                        script.Append("company.Sales.CreateStore(").Append(storeAddMessage.Id).Append(",'").Append(storeAddMessage.Name).Append("');");
                    break;
                case CatalogAction.TENANT_ADD:
                    var tenantAddMessage = (TenantAddMessage)catalogMsg;
                    if (!catalogMemberExistence.exists)
                        script.Append("company.System.Tenants.Add(").Append(tenantAddMessage.Id).Append(",'").Append(tenantAddMessage.Name).Append("');");
                    break;
                case CatalogAction.TRANSACTIONTYPEMESSAGE_ADD:
                    var transactionTypeAddMessage = (TransactionTypeAddMessage)catalogMsg;
                    if (!catalogMemberExistence.exists)
                        script.Append("company.System.TransactionTypes.Add(").Append(transactionTypeAddMessage.Id).Append(",'").Append(transactionTypeAddMessage.Name).Append("'); ");
                    break;
                default:
                    throw new Exception($"No {nameof(CatalogAction)} implementation for {nameof(catalogMsg.CatalogAction)} '{catalogMsg.CatalogAction}'");
            }

            if (script.Length != 0)
            {
                result = actor.PerformCmd(script.ToString());
                if (!(result is OkObjectResult))
                {
                    if (result is ContentResult contentResult) throw new Exception($"Error: {contentResult.Content}");
                    if (result is ObjectResult objResult) throw new Exception($"Error: {objResult.Value}");
                    throw new Exception($@"Error:{result}");
                }
            }
        }

        void AddingDomainsAndStore(CatalogMemberExistence catalogMemberExistence, AgentAddMessage agentAddMessage, StringBuilder script)
        {
            if (!catalogMemberExistence.exists)
            {
                script.Append("agent = marketplace.AddAgent('").Append(agentAddMessage.Id).Append("');");
            }
            if (!catalogMemberExistence.existsDomain)
            {
                script.Append("Eval('domainId =' + company.Sales.NextDomainConsecutive() + ';');");
                script.Append("domain = company.Sales.CreateDomain(domainId, '").Append(agentAddMessage.DomainUrl).Append("', ").Append(Agents.ARTEMIS).Append(");");
                if (catalogMemberExistence.existsCurrentStore)
                {
                    script.Append("company.Sales.CurrentStore.Add(domain);");
                    script.Append("company.Sales.CurrentStore.DisableDomain(domain);");
                }
            }
            if (!catalogMemberExistence.containsDomain)
            {
                if (catalogMemberExistence.exists)
                {
                    script.Append("agent = marketplace.SearchAgent('").Append(agentAddMessage.AgentPath).Append("');");
                }
                if (catalogMemberExistence.existsDomain)
                {
                    script.Append("domain = company.Sales.DomainFrom('").Append(agentAddMessage.DomainUrl).Append("');");
                }
                if (catalogMemberExistence.containsStore)
                {
                    script.Append("agent.Assign(").Append(agentAddMessage.OriginStoreId).Append(", domain);");
                }
                else
                {
                    script.Append("store = company.Sales.StoreById(").Append(agentAddMessage.OriginStoreId).Append(");");
                    script.Append("agent.Assign(").Append(agentAddMessage.OriginStoreId).Append(", store.Name, domain);");
                }
            }
        }

        void UpdatingDomainsAndStore(CatalogMemberExistence catalogMemberExistence, AgentStoreOnOffMessage catalogMsg, StringBuilder script)
        {
            if (!catalogMemberExistence.existsCurrentStore) return;
            script.Append("agent = marketplace.SearchAgent('").Append(catalogMsg.AgentPath).Append("');");
            if (catalogMemberExistence.existsDomain)
                script.Append("domain = company.Sales.DomainFrom('").Append(catalogMsg.DomainUrl).Append("');");
            else
            {
                script.Append("Eval('domainId =' + company.Sales.NextDomainConsecutive() + ';');");
                script.Append("domain = company.Sales.CreateDomain(domainId, '").Append(catalogMsg.DomainUrl).Append("', ").Append(Agents.ARTEMIS).Append(");");
                if (catalogMemberExistence.existsCurrentStore)
                {
                    script.Append("company.Sales.CurrentStore.Add(domain);");
                    script.Append("company.Sales.CurrentStore.DisableDomain(domain);");
                }
            }

            if (!catalogMemberExistence.containsDomain)
            {
                if (catalogMemberExistence.containsStore)
                {
                    script.Append("agent.Assign(").Append(catalogMsg.OriginStoreId).Append(", domain);");
                }
                else
                {
                    script.Append("store = company.Sales.StoreById(").Append(catalogMsg.OriginStoreId).Append(");");
                    script.Append("agent.Assign(").Append(catalogMsg.OriginStoreId).Append(", store.Name, domain);");
                }
            }
            if (catalogMsg.Enabled)
            {
                script.Append("agent.EnableDomain(").Append(catalogMsg.OriginStoreId).Append(", domain);");
            }
            else
            {
                script.Append("agent.DisableDomain(").Append(catalogMsg.OriginStoreId).Append(", domain);");
            }
        }

        struct CatalogMemberExistence
        {
            public bool exists { get; set; }
            public bool existsDomain { get; set; }
            public bool existsCurrentStore { get; set; }
            public bool containsDomain { get; set; }
            public bool containsStore { get; set; }
        }
    }
}
