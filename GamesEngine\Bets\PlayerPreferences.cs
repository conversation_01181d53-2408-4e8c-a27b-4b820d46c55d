﻿using GamesEngine.Games.Lines;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;

namespace GamesEngine.Bets
{
	[Puppet]
	internal class PlayerPreferences : Objeto
	{

		private FavoritesSports favoritesSports;
		private GamesReminder gamesReminder;
		private OddsFormats oddsFormats;
		internal enum OddsFormats { MoneyLine = 1, Fractional = 2, Decimal = 3, ImpliedProbability = 4 }
		
		internal PlayerPreferences()
		{
			this.oddsFormats = OddsFormats.MoneyLine;
		}

		internal void OddFormat(OddsFormats odd)
		{
			this.oddsFormats = odd;
		}
		internal string OddFormatAsString()
		{
			return this.oddsFormats.ToString();
		}

		internal FavoritesSports Sports
		{
			get
			{
				if (this.favoritesSports == null)
				{
					this.favoritesSports = new FavoritesSports();
				}
				return favoritesSports;
			}
		}
		internal bool HasFavoriteSports()
		{
			var result = this.favoritesSports != null && this.favoritesSports.HasFavorites();
			return result;
		}

		internal GamesReminder GamesReminder
		{
			get
			{
				if (this.gamesReminder == null)
				{
					this.gamesReminder = new GamesReminder();
				}
				return gamesReminder;
			}
		}
	}
}
