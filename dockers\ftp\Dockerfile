FROM ubuntu:18.04
ENV DEBIAN_FRONTEND noninteractive

RUN apt-get update
RUN apt-get -y install apt-utils iputils-ping
RUN rm -rf /etc/localtime
RUN ln -s /usr/share/zoneinfo/America/New_York /etc/localtime
RUN apt-get -y install tzdata
RUN apt-get -y install vsftpd

COPY run.sh /opt/run.sh
RUN chmod 775 /opt/run.sh

RUN useradd -m el4
RUN echo "el4:123456" | chpasswd
RUN chmod 775 -R /home/<USER>

RUN touch /etc/vsftpd.user_list
COPY vsftpd.conf /etc/vsftpd.conf

CMD /opt/run.sh && tail -f /var/log/vsftpd.log

EXPOSE 20 21 10090 10100
