﻿using GamesEngine.Games;
using GamesEngine.Games.Tournaments;
using System;
using System.Numerics;
using System.Text;

namespace GamesEngine.Gameboards.MarchMadness
{
    internal abstract class Bracket : Gameboard
    {
        private readonly Tournament tournament;
        private readonly Predictions predictions;

        internal Bracket(Bets.Player player, string name, Tournament tournament, DateTime creationDate) : base(player, tournament.Tournaments.Company.Gameboards, creationDate)
        {
            if (tournament == null) throw new ArgumentNullException(nameof(tournament));
            if (String.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

            if (tournament.IsEnded() || tournament.IsReadyToPay()) throw new GameEngineException($"The tournament {tournament.Description} is over.");
            if (!tournament.IsStarted()) throw new GameEngineException("The tournament has not started");
            var currentRound = tournament.CurrentRound();
            if (currentRound.IsStarted() || currentRound.IsClosed()) throw new GameEngineException("New brackets can not be created if the final round already begins.");
            
            this.tournament = tournament;
            this.Name = name;
            this.predictions = new Predictions(tournament, this);
            this.tournament.GamesFrom(currentRound).AddOnChangeToAllGames(OnScoreUpdate);
        }

        /// <summary>
        /// This method update the braket's score based on the result of particular game.
        /// </summary>
        public abstract void OnScoreUpdate(bool itIsThePresent, Game game);

        internal Tournament Tournament
        {
            get
            {
                return tournament;
            }
        }

        internal virtual Predictions Predictions
        {
            get
            {
                return predictions;
            }
        }

        internal new string ToString()
        {
            StringBuilder result = new StringBuilder();
            result.Append(predictions.ToString());
            result.AppendLine();
            result.Append(tournament.ToString());
            return result.ToString();
        }

        internal bool StartedOn(Round round)
        {
            if (round == null) throw new ArgumentNullException(nameof(round));
            return predictions.StartedOn(round);
        }

        //Esta funcion junto con la de abajo parecen estar haciendo lo mismo, 
        //pero InitialRoundOfPredictions lo hace calculado, mientras que RoundWhereStartPreditions es un Field, 
        //deberia quedar la del Field pero no se hace el cambio porque ya se esta establizando y puede provocar regresiones
        internal Round RoundWhereStartPreditions
        {
            get
            {
                return predictions.RoundWhereStartPreditions;
            }
        }

        internal Round InitialRoundOfPredictions()
        {
            return predictions.InitialRoundOfPredictions();
        }
    }
}
