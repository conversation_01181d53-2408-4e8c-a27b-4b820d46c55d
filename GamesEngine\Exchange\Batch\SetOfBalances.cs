﻿using GamesEngine.Finance;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Exchange.Batch
{
	[Puppet]
	internal sealed class SetOfBalances : Objeto
	{
		private Dictionary<Coin, ThirdPartyBalances> balances = new Dictionary<Coin, ThirdPartyBalances>();
		internal SetOfBalances()
		{
		}
		internal SetOfBalances(SetOfBalances initial)
		{
			foreach (var keyPair in initial.balances)
			{
				balances.Add(keyPair.Key, new ThirdPartyBalances(keyPair.Value));
			}
		}

		internal void TakeFirstSnap()
		{
			foreach (ThirdPartyBalances balance in balances.Values)
			{
				balance.TaketFirstSnap();
			}
		}

		internal void TakeSecondSnap()
		{
			foreach (ThirdPartyBalances balance in balances.Values)
			{
				balance.TakeSecondSnap();
			}
		}

		internal void AddAccountIn(string accountNumber, Currency initialBalance)
		{
			if (string.IsNullOrEmpty(accountNumber)) throw new GameEngineException($"Bank account {accountNumber} must be greater then zero.");	
			if (initialBalance.Value <= 0) throw new GameEngineException($" {initialBalance} must be greater then zero.");

			ThirdPartyBalances accounts;
			var currencyCode = initialBalance.Coin;

			if (!balances.TryGetValue(currencyCode, out accounts))
			{
				accounts = new ThirdPartyBalances();
				balances.Add(currencyCode, accounts);
			}

			accounts.AddInAccount(new ThirdPartyBalance(accountNumber, initialBalance));
		}

		internal void AddAccountOut(string accountNumber, Currency initialBalance)
		{
			if (string.IsNullOrEmpty(accountNumber)) throw new GameEngineException($"Bank account {accountNumber} must be greater then zero.");
			if (initialBalance.Value <= 0) throw new GameEngineException($" {initialBalance} must be greater then zero.");

			ThirdPartyBalances accounts;
			var currencyCode = initialBalance.Coin;

			if (!balances.TryGetValue(currencyCode, out accounts))
			{
				accounts = new ThirdPartyBalances();
				balances.Add(currencyCode, accounts);
			}

			accounts.AddOutAccount(new ThirdPartyBalance(accountNumber, initialBalance));
		}

		internal void RemoveAccountIn(Coin currencyCode, string accountNumber)
		{
			ThirdPartyBalances accounts;
			if (!balances.TryGetValue(currencyCode, out accounts))
			{
				throw new GameEngineException($"There are no accounts for {currencyCode.ToString()} currency.");
			}
			accounts.RemoveInAccount(accountNumber);
		}

		internal void RemoveAccountOut(Coin currencyCode, string accountNumber)
		{
			ThirdPartyBalances accounts;
			if (!balances.TryGetValue(currencyCode, out accounts))
			{
				throw new GameEngineException($"There are no accounts for {currencyCode.ToString()} currency.");
			}
			accounts.RemoveOutAccount(accountNumber);
		}

		internal bool ItsConfiguredNotFor(Coin currencyCode)
		{
			ThirdPartyBalances inThirdPartyBalances;
			bool found;
			
			found = balances.TryGetValue(currencyCode, out inThirdPartyBalances );
			if (!found) return true;

			return ! (inThirdPartyBalances.Count() > 0);
		}
		internal string OutAccountFor(Coin currencyCode)
		{
			ThirdPartyBalances inThirdPartyBalances = balances[currencyCode];
			
			return inThirdPartyBalances.OutAccountFor();
		}
		internal void IncreaseOutBalances(Currency amount)
		{
			ThirdPartyBalances accounts;
			if (!balances.TryGetValue(amount.Coin, out accounts))
			{
				accounts = new ThirdPartyBalances();
				balances.Add(amount.Coin, accounts);
			}

			accounts.AddOutMovement(amount.Value);
		}

		internal void IncreaseInBalances(Currency amount)
		{
			ThirdPartyBalances accounts;
			if (!balances.TryGetValue(amount.Coin, out accounts))
			{
				accounts = new ThirdPartyBalances();
				balances.Add(amount.Coin, accounts);
			}

			accounts.AddInMovement(amount.Value);
		}

		internal decimal CurrentBalance(Coin currencyCode)
		{
			return balances[currencyCode].CurrentBalance();
		}
		internal decimal InitialAmountFor(Coin currencyCode)
		{
			return balances[currencyCode].InitialAmount();
		}

		internal decimal Movements(Coin currencyCode)
		{
			return balances[currencyCode].Movements();
		}

		internal sealed class ThirdPartyBalances
		{
			private ThirdPartyBalance realInAccounts;
			private ThirdPartyBalance realOutAccounts;
			private decimal inMovementsAccumulated;
			private decimal outMovementsAccumulated;
			private decimal InitialAmountSnap;
			private decimal FinalAmountSnap;
			private bool FinalSnapItsBeenCalled;

			internal ThirdPartyBalances()
			{
			}
			internal ThirdPartyBalances(ThirdPartyBalances value)
			{
				if (value.realInAccounts == null) throw new GameEngineException($"{nameof(realInAccounts)} cant be empty.");
				if (value.realOutAccounts == null) throw new GameEngineException($"{nameof(realOutAccounts)} cant be empty.");

				this.realInAccounts = new ThirdPartyBalance(value.realInAccounts);
				this.realOutAccounts = new ThirdPartyBalance(value.realOutAccounts);
			}

			internal void TaketFirstSnap()
			{
				if (FinalSnapItsBeenCalled) throw new GameEngineException($"This balances are already snapshotted.");

				InitialAmountSnap = InitialAmount();
			}
			internal void TakeSecondSnap()
			{
				if (FinalSnapItsBeenCalled) throw new GameEngineException($"This balances are already snapshotted.");

				FinalAmountSnap = CurrentBalance();
				inMovementsAccumulated = 0;
				outMovementsAccumulated = 0;
				FinalSnapItsBeenCalled = true;
			}
			internal void AddInAccount(ThirdPartyBalance realAccount)
			{
				if (FinalSnapItsBeenCalled) throw new GameEngineException($"This balances are already snapshotted.");
				if (realAccount==null) throw new ArgumentException(nameof(realAccount));
				if (realInAccounts != null) throw new GameEngineException($"The key {realInAccounts.AccountNumber} already exists.");

				realInAccounts = realAccount;
			}

			internal void AddInMovement(decimal value)
			{
				if (FinalSnapItsBeenCalled) throw new GameEngineException($"This balances are already snapshotted.");

				inMovementsAccumulated += value;
			}

			internal void AddOutAccount(ThirdPartyBalance realAccount)
			{
				if (FinalSnapItsBeenCalled) throw new GameEngineException($"This balances are already snapshotted.");
				if (realAccount == null) throw new ArgumentException(nameof(realAccount));
				if (realOutAccounts != null) throw new GameEngineException($"The key {realOutAccounts.AccountNumber} already exists.");

				realOutAccounts = realAccount;
			}

			internal void AddOutMovement(decimal value)
			{
				if (FinalSnapItsBeenCalled) throw new GameEngineException($"This balances are already snapshotted.");

				outMovementsAccumulated += value;
			}

			internal int Count()
			{
				int result = 0;
				if (realInAccounts != null) result++;
				if (realOutAccounts != null) result++;
				return result;
			}

			internal decimal CurrentBalance()
			{
				if (FinalSnapItsBeenCalled) return FinalAmountSnap;

				return InitialAmount() + inMovementsAccumulated - outMovementsAccumulated;
			}

			internal decimal InitialAmount()
			{
				decimal initial = 0;
				initial += realInAccounts.RealAccountValue.Value;
				initial += realOutAccounts.RealAccountValue.Value;

				return initial;
			}

			internal void RemoveInAccount(string accountNumber)
			{
				if (FinalSnapItsBeenCalled) throw new GameEngineException($"This balances are already snapshotted.");
				if (realInAccounts.AccountNumber != accountNumber) throw new GameEngineException($"There is no account number {accountNumber} already registered.");

				realInAccounts = null;
			}
			internal void RemoveOutAccount(string accountNumber)
			{
				if (FinalSnapItsBeenCalled) throw new GameEngineException($"This balances are already snapshotted.");
				if (realOutAccounts.AccountNumber != accountNumber) throw new GameEngineException($"There is no account number {accountNumber} already registered.");

				realOutAccounts = null;
			}

			internal decimal Movements()
			{
				return inMovementsAccumulated + outMovementsAccumulated;
			}

			internal string OutAccountFor()
			{
				return realOutAccounts.AccountNumber;
			}
		}

		internal sealed class ThirdPartyBalance
		{
			internal ThirdPartyBalance(ThirdPartyBalance otherone)
			{
				this.AccountNumber = otherone.AccountNumber;
				this.RealAccountValue = otherone.RealAccountValue;
			}

			internal ThirdPartyBalance(string accountNumber, Currency realAccountValue)
			{
				if (string.IsNullOrEmpty(accountNumber)) throw new GameEngineException($" {nameof(accountNumber)} it's required.");
				if (realAccountValue == null) throw new GameEngineException($" {nameof(realAccountValue)} it's required.");

				AccountNumber = accountNumber;
				RealAccountValue = realAccountValue;
			}

			internal string AccountNumber { get; }
			internal Currency RealAccountValue { get; }
		}



	}
}
