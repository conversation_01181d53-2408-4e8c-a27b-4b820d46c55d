﻿using GamesEngine.Bets;
using GamesEngine.Games.Lotto;
using GamesEngine.Tools;
using System;
using System.Linq;

namespace GamesEngine.Gameboards.Lotto
{
    internal abstract class TicketKenoBulleye : TicketKeno
    {
        internal TicketKenoBulleye(Player player, decimal baseCost,LotteryKeno lotteryKeno, DateTime date, string drawId, int[] numbers, DateTime creationDate, decimal ticketCost, Prizes prizes) :
           base(player, baseCost, lotteryKeno, date, drawId, numbers, false,true, creationDate, ticketCost, prizes)

        {
        }
    }
    internal sealed class Ticket10KenoBulleye : TicketKenoBulleye
    {
        internal Ticket10KenoBulleye(Player player, decimal baseCost,LotteryKeno lotteryKeno, DateTime date, string drawId, int[] numbers, DateTime creationDate, decimal ticketCost, Prizes prizes) :
           base(player, baseCost, lotteryKeno, date, drawId, numbers, creationDate, ticketCost, prizes)

        {
        }
        internal override TicketType IdOfType()
        {
            return TicketType.K10;
        }

        internal override string GameTypeForReports()
        {
            return QueryMakerOfHistoricalKeno.ID_K10_NO_MUL_BULL;
        }
    }
    internal sealed class Ticket12KenoBulleye : TicketKenoBulleye
    {
        internal Ticket12KenoBulleye(Player player, decimal baseCost,LotteryKeno lotteryKeno, DateTime drawDate, string drawId, int[] numbers, DateTime creationDate, decimal ticketCost, Prizes prizes) :
           base(player, baseCost, lotteryKeno, drawDate, drawId, numbers, creationDate, ticketCost, prizes)

        {
        }

        internal override TicketType IdOfType()
        {
            return TicketType.K12;
        }

        internal override string GameTypeForReports()
        {
            return QueryMakerOfHistoricalKeno.ID_K12_NO_MUL_BULL;
        }
    }
}
