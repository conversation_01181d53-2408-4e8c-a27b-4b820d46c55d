﻿using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using static GamesEngine.Finance.Authorizations;

namespace GamesEngine.Finance
{

	internal class PartialFragment
	{
		internal int FragmentId { get;}
		internal FragmentStatus Status { get;}
		internal FragmentReason Reason { get;}
		internal string Reference { get;}
		internal string Description { get;}
		internal decimal Risk { get;}
		internal decimal ToWin { get;}
		internal decimal AdjustedWinAmount { get;}
		internal decimal AdjustedLossAmount { get;}
		internal string AccountNumber { get;}
		internal DateTime Useless { get; }

		internal PartialFragment(int fragmentId, FragmentStatus status, string reference, string description, decimal risk,  decimal toWin, FragmentReason reason, decimal adjustedWinAmount, decimal adjustedLossAmount, string accountNumber, DateTime useless)
		{
			FragmentId = fragmentId;
			Status = status;
			Reference = reference;
			Description = description;
			Risk = risk;
			ToWin = toWin;
			Reason = reason;
			AdjustedWinAmount = adjustedWinAmount;
			AdjustedLossAmount = adjustedLossAmount;
			AccountNumber = accountNumber;
			Useless = useless;
		}
	}

	[Puppet]
	internal class Authorization : Objeto
	{
		internal int Number { get;}
		internal Currency Amount { get;}
		internal AtAddress AtAddress { get;}
		internal AccountWithBalance Account { get;}
		internal DateTime Useless { get; }
		private AuthorizationFragments withFragments = null;

		internal Authorization(int number, Currency amount, AccountWithBalance account, DateTime useless)
		{
			if (account == null) throw new ArgumentException(nameof(account));

			Number = number;
			Amount = amount;
			AtAddress = account.Owner;
			Account = account;
			Useless = useless;
		}

		internal void CreateFakeFragment(string reference, string description, decimal toWin)
		{
			if (withFragments != null)
			{
				return;
			}

			AuthorizationFragments result = new AuthorizationFragments(this, reference, description, toWin);
			withFragments = result;
		}

		internal void CreateFragments(int theLowestFragmentNumber, int theHighestFragmentNumber, IEnumerable<string> references, IEnumerable<string> descriptions, decimal risk, decimal toWin)
		{
			if (withFragments != null)
			{
				return ;
			}
			
			AuthorizationFragments result = new AuthorizationFragments(this, theLowestFragmentNumber, theHighestFragmentNumber, Currency.Factory(Amount.CurrencyCode, risk), references, descriptions, risk, toWin);
			withFragments = result;
		}

		internal void CreateFragments(int theLowestFragmentNumber, int theHighestFragmentNumber, IEnumerable<string> references, IEnumerable<string> descriptions, decimal risk, IEnumerable<decimal> toWins)
		{
			if (withFragments != null)
			{
				return;
			}

			AuthorizationFragments result = new AuthorizationFragments(this, theLowestFragmentNumber, theHighestFragmentNumber, Currency.Factory(Amount.CurrencyCode, risk), references, descriptions, risk, toWins);
			withFragments = result;
		}

		internal void CreateFragments(int theLowestFragmentNumber, int theHighestFragmentNumber, IEnumerable<string> references, IEnumerable<string> descriptions, IEnumerable<decimal> risks, decimal toWin)
		{
			if (withFragments != null)
			{
				return;
			}

			AuthorizationFragments result = new AuthorizationFragments(this, theLowestFragmentNumber, theHighestFragmentNumber, Amount.Coin, references, descriptions, risks, toWin);
			withFragments = result;
		}

		internal void CreateFragments(int theLowestFragmentNumber, int theHighestFragmentNumber, IEnumerable<string> references, IEnumerable<string> descriptions, IEnumerable<decimal> risks, IEnumerable<decimal> toWins)
		{
			if (withFragments != null)
			{
				return;
			}

			AuthorizationFragments result = new AuthorizationFragments(this, theLowestFragmentNumber, theHighestFragmentNumber, Amount.Coin, references, descriptions, risks, toWins);
			withFragments = result;
		}

		internal void CreateFragments(int theLowestFragmentNumber, int theHighestFragmentNumber, decimal amountOfEachFragment, IEnumerable<PartialFragment> fragmentInfo)
		{
			if (withFragments != null)
			{
				return;
			}
			
			AuthorizationFragments result = new AuthorizationFragments(this, theLowestFragmentNumber, theHighestFragmentNumber, Currency.Factory(Amount.CurrencyCode, amountOfEachFragment), fragmentInfo);
			withFragments = result;
		}

		internal void CreateFragments(int theLowestFragmentNumber, int theHighestFragmentNumber)
		{
			if (withFragments != null) throw new GameEngineException($"Fragments were already created.");

			withFragments = new AuthorizationFragments(theLowestFragmentNumber, theHighestFragmentNumber);
		}

		internal void AddFragments(int theLowestFragmentNumber, int theHighestFragmentNumber, IEnumerable<string> references, IEnumerable<string> descriptions, decimal risk, decimal toWin)
		{
			if (withFragments == null) throw new GameEngineException($"Fragments were not created previously. Use method {nameof(CreateFragments)} first");

			withFragments.Add(this, theLowestFragmentNumber, theHighestFragmentNumber, Currency.Factory(Amount.CurrencyCode, risk), references, descriptions, risk, toWin);
		}

		internal void AddFragments(int theLowestFragmentNumber, int theHighestFragmentNumber, IEnumerable<string> references, IEnumerable<string> descriptions, decimal risk, IEnumerable<decimal> toWins)
		{
			if (withFragments == null) throw new GameEngineException($"Fragments were not created previously. Use method {nameof(CreateFragments)} first");

			withFragments.Add(this, theLowestFragmentNumber, theHighestFragmentNumber, Currency.Factory(Amount.CurrencyCode, risk), references, descriptions, risk, toWins);
		}

		internal void AddFragments(int theLowestFragmentNumber, int theHighestFragmentNumber, IEnumerable<string> references, IEnumerable<string> descriptions, IEnumerable<decimal> risks, decimal toWin)
		{
			if (withFragments == null) throw new GameEngineException($"Fragments were not created previously. Use method {nameof(CreateFragments)} first");

			withFragments.Add(this, theLowestFragmentNumber, theHighestFragmentNumber, Amount.Coin, references, descriptions, risks, toWin);
		}

		internal void AddFragments(int theLowestFragmentNumber, int theHighestFragmentNumber, IEnumerable<string> references, IEnumerable<string> descriptions, IEnumerable<decimal> risks, IEnumerable<decimal> toWins)
		{
			if (withFragments == null) throw new GameEngineException($"Fragments were not created previously. Use method {nameof(CreateFragments)} first");

			withFragments.Add(this, theLowestFragmentNumber, theHighestFragmentNumber, Amount.Coin, references, descriptions, risks, toWins);
		}

		private AuthorizationTemporaryFragments authorizationTemporaryFragments;
		internal void InitFragmentCreation(int theLowestFragmentNumber, int theHighestFragmentNumber)
		{
			authorizationTemporaryFragments = new AuthorizationTemporaryFragments(this, theLowestFragmentNumber, theHighestFragmentNumber);
		}

		internal void AppendFragments(IEnumerable<string> references, IEnumerable<string> descriptions, decimal risk, decimal toWin)
		{
			authorizationTemporaryFragments.AppendFragments(references, descriptions, risk, toWin);
		}

		internal void AppendFragments(IEnumerable<string> references, IEnumerable<string> descriptions, decimal risk, IEnumerable<decimal> toWins)
		{
			authorizationTemporaryFragments.AppendFragments(references, descriptions, risk, toWins);
		}

		internal void AppendFragments(IEnumerable<string> references, IEnumerable<string> descriptions, IEnumerable<decimal> risks, decimal toWin)
		{
			authorizationTemporaryFragments.AppendFragments(references, descriptions, risks, toWin);
		}

		internal void AppendFragments( IEnumerable<string> references, IEnumerable<string> descriptions, IEnumerable<decimal> risks, IEnumerable<decimal> toWins)
		{
			authorizationTemporaryFragments.AppendFragments( references, descriptions, risks, toWins);
		}

		internal void CommitFragmentCreation()
		{
			if (authorizationTemporaryFragments == null) throw new GameEngineException("Buffer its empty;");

			AuthorizationFragments result = new AuthorizationFragments(
				this, 
				authorizationTemporaryFragments.TheLowestFragmentNumber, 
				authorizationTemporaryFragments.TheHighestFragmentNumber,
				Amount.Coin,
				authorizationTemporaryFragments.References,
				authorizationTemporaryFragments.Descriptions,
				authorizationTemporaryFragments.Risks,
				authorizationTemporaryFragments.ToWins
				);

			withFragments = result;
			ClearTemporaryFragment();
		}
		internal void ClearTemporaryFragment()
		{
			authorizationTemporaryFragments = null;
		}

		internal static Authorization Load(int authorizationNumber, AtAddress atAddress, Purpose purpose)
		{
			Authorization result = Movements.Storage.RetrieveFragments(authorizationNumber, atAddress, purpose);
			return result;
		}

		internal void ChangeInitialFragmentsStatus(IEnumerable<int> fragments, int status)
		{
			if (typeof(FragmentStatus).IsEnumDefined(status))
			{
				FragmentStatus initialStatus = (FragmentStatus)status;
				foreach (var fragment in fragments)
				{
					withFragments.ChangeInitialFragmentsStatus(fragment, initialStatus);
				}
			}
			else
			{
				throw new GameEngineException($"Status code {status} does not exist.");
			}
		}

		internal void PayFragments(bool itIsThePresent, IEnumerable<int> fragments, string concept, MovementsCollector movements, FragmentReason reason, int processorId)
		{
			foreach (var fragment in fragments)
			{
				withFragments.PayFragment(movements, concept, fragment, reason, "", Account, processorId);
			}

			if (!HasPendingsFragments())
			{
				AtAddress.AddToDiscartedAuthorizations(this);
			}
		}

		internal void PayFakeFragment(MovementsCollector movements, FragmentReason reason)
		{
			withFragments.PayFakeFragment(movements, reason);

			if (!HasPendingsFragments())
			{
				AtAddress.AddToDiscartedAuthorizations(this);
			}
		}

		internal void RefundFragments(bool itIsThePresent, IEnumerable<int> noActionFragments, string concept, MovementsCollector movements, FragmentReason reason, int processorId)
		{
			foreach (var noaction in noActionFragments)
			{
				withFragments.RefundFragment(movements, concept, noaction, reason, "", Account, processorId);
			}

			if (!HasPendingsFragments())
			{
				AtAddress.AddToDiscartedAuthorizations(this);
			}
		}

		internal void PrepareAdjustments(int authorizationFragments, decimal adjustedWinAmount, decimal adjustedLossAmount)
		{
			AuthorizationFragment fragment;
			bool found = withFragments.Search(authorizationFragments, out fragment);
			if (!found) throw new GameEngineException($"Fragment {authorizationFragments} doesnt exists for authorization {Number}.");

			fragment.ChangeAdjustments(adjustedWinAmount, adjustedLossAmount);
		}

		internal bool HasPendingsFragments()
		{
			return withFragments.HasPendings();
		}

		internal int FragmentsStatus(IEnumerable<int> fragmentNumbers)
		{
			return (int)withFragments.FragmentsStatus(fragmentNumbers);
		}

		internal IEnumerable<AuthorizationFragment> PendingFragments()
		{
            if (withFragments == null) return Enumerable.Empty<AuthorizationFragment>();
            return withFragments.PendingFragments();
		}

		internal IEnumerable<AuthorizationFragment> PayedFragments()
		{
			if (withFragments == null) return Enumerable.Empty<AuthorizationFragment>();
			return withFragments.PayedFragments();
		}
	}
}
