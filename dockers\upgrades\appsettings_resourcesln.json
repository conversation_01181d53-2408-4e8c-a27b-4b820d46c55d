{"DBDairy": {"ConnectionStrings": {"MySQL": "persistsecurityinfo=True;port=3307;Server=bd.qa4.ncubo.com;Database=resources;user id=root;password=*********;SslMode=none", "SQLServer": "data source=sql.qa4.exchange.com;initial catalog=exch_test_02;user id=sa;password=***********;multipleactiveresultsets=True"}, "DBSelected": "MySQL"}, "BIIntegration": {"useKafka": true, "useDb": false, "kafka": {"server": "queue.exchange.com:9092", "topicForWinner": "LottoWinners", "topicForLosers": "LottoLosers", "topicForNoActions": "LottoNoAction", "topicForTransacctions": "Transactions", "topicForGrades": "LottoGrades", "topicForDrawings": "LottoDrawings", "topicForCustomers": "Customers", "topicForNotifications": "Notifications", "topicForProfanity": "Profanity", "topicForScheduler": "Scheduler", "topicForPrizes": "LottoPrizes", "topicForFragments": "Fragments", "topicForPayFragments": "PayFragments", "topicForMovements": "Movements", "topicForDeposits": "Deposits", "topicForWithdrawals": "<PERSON><PERSON><PERSON><PERSON>", "topicForChallenges": "Challenges", "group": "games"}, "DBHistorical": {"ConnectionStrings": {"MySQL": "persistsecurityinfo=True;port=3307;Server=bd.qa4.ncubo.com;Database=resources;user id=root;password=*********;SslMode=none", "SQLServer": "data source=sql.qa4.exchange.com;initial catalog=exch_test_02;user id=sa;password=***********;multipleactiveresultsets=True"}, "DBSelected": "MySQL"}}}