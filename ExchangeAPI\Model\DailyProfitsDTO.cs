﻿using GamesEngine;
using GamesEngine.Finance;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Threading.Tasks;

namespace ExchangeAPI.Model
{
    [DataContract(Name = "DailyProfitsDTO")]
    public class DailyProfitsDTO
    {
        [DataMember(Name = "dailyProfits")]
        public List<DailyProfitDTO> DailyProfits { get; set; } = new List<DailyProfitDTO>();

        [DataMember(Name = "totalRecords")]
        public int TotalRecords { get; set; }

        [DataMember(Name = "totalPurchases")]
        public decimal TotalPurchases { get; set; }

        [DataMember(Name = "totalPurchasesFormatted")]
        public string TotalPurchasesFormatted { get { return Currency.Factory(CurrencyCode, TotalPurchases).ToDisplayFormat(); } }

        [DataMember(Name = "totalSales")]
        public decimal TotalSales { get; set; }

        [DataMember(Name = "totalSalesFormatted")]
        public string TotalSalesFormatted { get { return Currency.Factory(CurrencyCode, TotalSales).ToDisplayFormat(); } }

        [DataMember(Name = "totalProfits")]
        public decimal TotalProfits { get; set; }

        [DataMember(Name = "totalProfitsFormatted")]
        public string TotalProfitsFormatted { get { return Currency.Factory(CurrencyCode, TotalProfits).ToDisplayFormat(); } }

        [DataMember(Name = "currencyCode")]
        public string CurrencyCode { get; set; }

        internal void Add(DailyProfitDTO dailyProfit)
        {
            if (CurrencyCode != dailyProfit.CurrencyCode) throw new GameEngineException($"{nameof(CurrencyCode)} must be common for all records");

            DailyProfits.Add(dailyProfit);
        }

        public DailyProfitsDTO(string currencyCode)
        {
            CurrencyCode = currencyCode;
        }
    }

    [DataContract(Name = "DailyProfitDTO")]
    public class DailyProfitDTO
    {
        [DataMember(Name = "day")]
        public string DayAsString 
        {
            get
            {
                return Day.ToString("MM/dd/yyyy");
            }
        }
        public DateTime Day { get; set; }

        [DataMember(Name = "purchases")]
        public decimal Purchases { get; set; }

        [DataMember(Name = "purchasesFormatted")]
        public string PurchasesFormatted { get { return Currency.Factory(CurrencyCode, Purchases).ToDisplayFormat(); } }

        [DataMember(Name = "sales")]
        public decimal Sales { get; set; }

        [DataMember(Name = "salesFormatted")]
        public string SalesFormatted { get { return Currency.Factory(CurrencyCode, Sales).ToDisplayFormat(); } }

        [DataMember(Name = "profits")]
        public decimal Profits { get; set; }

        [DataMember(Name = "profitsFormatted")]
        public string ProfitsFormatted { get { return Currency.Factory(CurrencyCode, Profits).ToDisplayFormat(); } }

        [DataMember(Name = "currencyCode")]
        public string CurrencyCode { get; set; }
    }
}
