﻿using Connectors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.Serialization.Json;
using System.Text;
using System.Threading.Tasks;
[assembly: InternalsVisibleTo("GamesEngineTests")]
namespace town.connectors.drivers
{
    public enum PaymentMethod
    {
        Cash = 0,
        Bank = 1,
        Creditcard = 2,
        FinancialServices = 3,
        ThirdParty = 4,
        Secrets = 5,
        P2PTransfer = 6
    }
    public enum TransactionType { 
        Deposit = 0,
        Deposit_Then_Lock = 1,
        Withdrawal = 3,
        Transfer = 4, 
        CreditNote = 5, 
        DebitNote = 6, 
        Sale = 7, 
        Refund = 8, 
        Sale_And_Fragmentation = 9,
        ReturnPrize = 10,
        PayPrize = 11,
        Purchase = 12,
        PurchaseTicket = 13,
        RevertRefund = 14
    };
    public enum TransactionMessageType
    {
        Transaction,
        JournalEntry,
        Coin
    }
    public enum Tenant_Actions
    {
        Others = 0,
        Balance = 1,
        Validate = 2,
        Fragment = 3,
        Grade = 4,
        AuthorizationInternal = 5
    };

    public abstract class Driver
    {
        private readonly float version;

        public string Id { get; }

        //private DateTime now;
        //private readonly CustomSettings parameters;
        private Action<string, Exception, string[]> whenErrors;
        private Action<string, Exception, string[]> whenWarnings;
        public float Version { get { return version; } }
        protected Driver(string entity, PaymentMethod paymentMethod, string currencyIsoCode, float version):
            this(entity, paymentMethod, new string[] { currencyIsoCode }, version)
        {
        
        
        }
        protected Driver(string entity, PaymentMethod paymentMethod, string[] currencyIsoCodes, float version)
        {
            if (version == 0) throw new ArgumentException(nameof(version));
            if (string.IsNullOrEmpty(entity)) throw new ArgumentException(nameof(entity));
            if (currencyIsoCodes == null) throw new ArgumentException(nameof(currencyIsoCodes));
            if (currencyIsoCodes.Length == 0) throw new ArgumentException(nameof(currencyIsoCodes));

            Entity = entity;
            PaymentMethod = paymentMethod;
            CurrencyIsoCodes = currencyIsoCodes;
            this.version = version;
            if (currencyIsoCodes.Length == 1)
            {
                Id = $"{entity}-{paymentMethod.ToString()}-{this.GetType().Name}-{currencyIsoCodes[0]}-{version}";
            }
            else 
            {
                Id = $"{entity}-{paymentMethod.ToString()}-{this.GetType().Name}-[{string.Join("-", currencyIsoCodes)}]-{version}";
            }
            
        }

        public abstract string Description { get; }
        public abstract string Fabricator { get; }
        public abstract DateTime ReleaseDate { get; }
        public string Entity { get; }
        public PaymentMethod PaymentMethod { get; }

        public string[] CurrencyIsoCodes { get; }
        public CustomSettings CustomSettings { get; private set; }

        public abstract Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet);
        public abstract T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet);
        public abstract void Prepare(DateTime now);

        public void ConfigureThenPrepare(DateTime now, CustomSettings cs)
        {
            CustomSettings = cs.ClonenlyFixedParameters();
            Prepare(now);
            CustomSettings.Prepare();
        }

        public void RegisterANotifyErrorsBehavior(Action<string, Exception , string[]> whenErrors)
        {
            this.whenErrors = whenErrors;
        }
        public void RegisterANotifyWarningsBehavior(Action<string, Exception, string[]> whenWarnings)
        {
            this.whenWarnings = whenWarnings;
        }
        internal void WhenErrors(string message, Exception e, string[] details)
        {
            if (string.IsNullOrEmpty(message)) throw new ArgumentException(nameof(message));
            if (e == null) throw new ArgumentException(nameof(e));
            if (details == null) details = new string[0];

            if (this.whenErrors != null) this.whenErrors(message, e, details);
        }
        internal void WhenWarnings(string message, Exception e, params string[] details)
        {
            if (string.IsNullOrEmpty(message)) throw new ArgumentException(nameof(message));
            if (e == null) throw new ArgumentException(nameof(e));
            if (details == null) details = new string[0];

            if (this.whenWarnings != null) this.whenWarnings(message, e, details);
        }
        private static List<Type> AllTypes(string path,out string log)
        {
            List<Type> allTypes = new List<Type>();
            StringBuilder builder = new StringBuilder();

            foreach (string dll in Directory.GetFiles(path, "*.dll"))
            {
                try
                {
                    Assembly assembly = Assembly.Load(dll);
                    allTypes.AddRange(assembly.GetTypes());
                }
                catch (Exception e)
                {
                    builder.AppendLine($"{dll} {e.Message}");
                }
            }
            foreach (Assembly ass in AppDomain.CurrentDomain.GetAssemblies())
            {
                allTypes.AddRange(
                       ass.GetTypes()
                       );
            }

            allTypes = allTypes.Where(possibleType => !possibleType.IsAbstract && CheckDriverInTheSuperClass(possibleType)).ToList();
            log = builder.ToString();

            return allTypes;
        }
        private static List<Driver> result;
        //public static List<Driver> LoadAllDriverFrom(string path, DateTime now, CustomSettingsPerType csperTypes, out string log)
        public static List<Driver> LoadAllConnectorsFrom(string path, out string log)
        {
            StringBuilder builder = new StringBuilder();
            List<Type> allTypes = AllTypes(path, out log);
            builder.AppendLine(log);
            result = new List<Driver>();

            foreach (var possibleType in allTypes)
            {
                try
                {
                    //var constuctor = possibleType.GetConstructor(new Type[] { now.GetType(), typeof(CustomSettings) });
                    var constuctor = possibleType.GetConstructor(new Type[] { });
                    if (constuctor == null)
                    {
                        builder.AppendLine($"{possibleType} not valid constructor,");
                        continue;
                    }

                    //CustomSettings cs = csperTypes.Get(possibleType);
                    //Driver o = (Driver)constuctor.Invoke(new object[] { now, cs });
                    Driver o = (Driver)constuctor.Invoke(new object[] { });

                    result.Add((Driver)o);
                }
                catch (Exception e)
                {
                    builder.AppendLine($"{possibleType} {e.Message}");
                }
            }

            log = builder.ToString();

            if (result == null)
            {
                System.Diagnostics.Debugger.Break();
            }


            return result;
        }    
       
        public static Driver Get<T>()
        {
            return result.Single(x => x.GetType() == typeof(T));
        }
        private static bool CheckDriverInTheSuperClass(Type type)
        {
            if (type.BaseType == null) return false;
            //if (type.IsAbstract) return false;
            if (type == typeof(Object) || type.BaseType == typeof(Object)) return false;
            if (type.BaseType == typeof(Driver)) return true;
            if (type.BaseType.FullName == typeof(Driver).FullName) return true;

            return CheckDriverInTheSuperClass(type.BaseType);
        }
        internal void WhenErrors(string message, params string[] details)
        {
            if (string.IsNullOrEmpty(message)) throw new ArgumentException(nameof(message));
            if (details == null) details = new string[0];

            if (this.whenErrors != null) this.whenErrors(message, null, details);
        }
        internal void WhenWarnings(string message, params string[] details)
        {
            if (string.IsNullOrEmpty(message)) throw new ArgumentException(nameof(message));
            if (details == null) details = new string[0];

            if (this.whenWarnings != null) this.whenWarnings(message, null, details);
        }
        protected void InternalOnError(string methodName, int retryNumber, Exception e, params string[] FurtherDetails)
        {
            OnError(methodName, retryNumber, e, FurtherDetails);
            if (retryNumber % 100 >= 0 && retryNumber % 100 <= 5)
            {
                NotifyWarn(methodName, retryNumber, e, FurtherDetails);
            }
        }
        private void OnError(string method, int retryNumber, Exception e, params string[] FurtherDetails)
        {
            Debug.WriteLine($"method:{method} \n retryNumber: {retryNumber} \n error:{e.Message} \n StackTrace: {e.StackTrace}");
        }
        public void NotifyError(string method, int retryNumber, Exception e, params string[] FurtherDetails)
        {
            Debug.WriteLine($"method:{method} \n retryNumber: {retryNumber} \n error:{e.Message} \n StackTrace: {e.StackTrace}");
            WhenErrors($@"{method} Err: {e.Message}", e, FurtherDetails);
        }
        public void NotifyError(string method, string body, string subject)
        {
            Debug.WriteLine($"method:{method} \n body: {body} \n subject:{subject}");
            WhenErrors(body, $@"{method} Err: {subject}");
        }
        public void NotifyWarn(string method, string body, string subject)
        {
            Debug.WriteLine($"method:{method} \n body: {body} \n subject:{subject}");
            WhenWarnings(body, $@"{method}  Warn: {subject}");
        }
        public void NotifyWarn(string method, int retryNumber, Exception e, params string[] FurtherDetails)
        {
            Debug.WriteLine($"method:{method} \n retryNumber: {retryNumber} \n error:{e.Message} \n StackTrace: {e.StackTrace}");
            WhenWarnings($@"{method} Warn: {e.Message}", e, FurtherDetails);
        }
    }
    public abstract class ProcessorDriver : Driver
    {
        protected ProcessorDriver(string entity, PaymentMethod paymentMethod, string currencyIsoCode, TransactionType transactionType, float version)
            : base(entity, paymentMethod, currencyIsoCode, version)
        {
            TransactionType = transactionType;
        }
        protected ProcessorDriver(string entity, PaymentMethod paymentMethod, string[] currencyIsoCodes, TransactionType transactionType, float version)
          : base(entity, paymentMethod, currencyIsoCodes, version)
        {
            TransactionType = transactionType;
        }
        public TransactionType TransactionType { get; }

    }
    public abstract class TenantDriver : Driver
    {
        protected TenantDriver(Tenant_Actions tenantAction, string entity, PaymentMethod paymentMethod, string currencyIsoCode, float version) 
            : base(entity, paymentMethod, new string []{ currencyIsoCode  }, version)
        {
            TenantAction = tenantAction;
        }
        protected TenantDriver(Tenant_Actions tenantAction, string entity, PaymentMethod paymentMethod, string[] currencyIsoCodes, float version)
          : base(entity, paymentMethod, currencyIsoCodes, version)
        {
            TenantAction = tenantAction;
        }
        public Tenant_Actions TenantAction { get; }
    }
    public class Commons
    {
        public static string ToJson(object instance)
        {
            using (MemoryStream mst = new MemoryStream())
            {
                var serializer = new DataContractJsonSerializer(instance.GetType());
                serializer.WriteObject(mst, instance);
                mst.Position = 0;

                using (StreamReader r = new StreamReader(mst))
                {
                    return r.ReadToEnd();
                }
            }
        }

        public static T FromJson<T>(string json)
        {
            if (json.StartsWith("{"))
            {
                var bytes = Encoding.Unicode.GetBytes(json);

                using (MemoryStream mst = new MemoryStream(bytes))
                {
                    var serializer = new DataContractJsonSerializer(typeof(T));
                    return (T)serializer.ReadObject(mst);
                }
            }
            return default(T);
        }
    }
}
