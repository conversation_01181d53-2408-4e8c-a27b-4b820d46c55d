{"profiles": {"IIS Express": {"commandName": "IISExpress", "commandLineArgs": "test", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "LoyaltyAPI": {"commandName": "Project", "commandLineArgs": "artemis", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "applicationUrl": "http://localhost:58164/"}, "Docker": {"commandName": "<PERSON>er", "launchBrowser": true, "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}", "publishAllPorts": true}, "LoyaltyFollower": {"commandName": "<PERSON>er", "commandLineArgs": "follower", "launchBrowser": true, "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}", "publishAllPorts": true}}, "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:58164/", "sslPort": 0}}}