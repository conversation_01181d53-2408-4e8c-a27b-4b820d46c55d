﻿using GamesEngine.Business;
using GamesEngine.PurchaseOrders;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using static GamesEngine.Finance.GeneralSources;

namespace GamesEngine.Finance
{
	[Puppet]
	internal class CustomerBalancesList : Objeto
	{
        private ReportsGenerator reports;

        internal CustomerBalancesList(Company company)
		{
            reports = new ReportsGenerator();
        }

		internal GeneralSource CreateSource(int sourceNumber, DateTime creationDate, string name, string isoCode)
		{
			if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
			if (GeneralSources.Any(sourceNumber)) throw new GameEngineException($"Source {name} already exist");

			GeneralSource result = GeneralSources.CreateSource(sourceNumber, creationDate,  name, isoCode);
			return result;
		}

		internal int NextSourceId
		{
			get
			{
                return GeneralSources.NextSourceId;
            }
		}

		internal IEnumerable<GeneralSource> List()
		{
			return GeneralSources.List();
        }

        internal IEnumerable<CurrencyFromSource> ValidCurrencies()
        {
            return GeneralSources.ValidCurrencies();
        }

        internal string SourceName(int sourceNumber, string currencyAsText)
        {
            if (sourceNumber <= 0) throw new GameEngineException($"{nameof(sourceNumber)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(currencyAsText)) throw new ArgumentNullException(nameof(currencyAsText));

            return GeneralSources.SourceName(sourceNumber, currencyAsText);
        }

        internal string AtAddressFrom(int documentNumber)
        {
            if (documentNumber < 0) throw new GameEngineException($"{nameof(documentNumber)} must be greater than 0");

            var atAddress = Movements.Storage.AtAddressFrom(documentNumber);
            return atAddress;
        }

        internal ReportsGenerator Reports
        {
            get
            {
                return reports;
            }
        }

        internal class ReportsGenerator : Objeto
        {
            internal MovementsReport MovementsReportFilteredByLast7Days(string atAddress, string currencyCode, int storeId, DateTime now, int initialIndex, int amountOfRows, int documentNumber, int referenceNumber)
            {
                if (string.IsNullOrWhiteSpace(atAddress)) throw new ArgumentException(nameof(atAddress));
                if (string.IsNullOrWhiteSpace(currencyCode)) throw new ArgumentException(nameof(currencyCode));
                if (now == default(DateTime)) throw new ArgumentNullException(nameof(now));
                if (initialIndex < 0) throw new GameEngineException($"{nameof(initialIndex)} must be greater or equal than 0");
                if (amountOfRows <= 0) throw new GameEngineException($"{nameof(amountOfRows)} must be greater than 0");
                if (documentNumber < 0) throw new GameEngineException($"{nameof(documentNumber)} must be greater than 0");
                if (referenceNumber < 0) throw new GameEngineException($"{nameof(referenceNumber)} must be greater than 0");

                var startDate = now.AddDays(-7).Date;
                var endDate = now.Date;
                return MovementsReportFilteredBy(atAddress, currencyCode, storeId, startDate, endDate, initialIndex, amountOfRows, documentNumber, referenceNumber);
            }

            internal MovementsReport MovementsReportFilteredByLast2Days(string atAddress, string currencyCode, int storeId, DateTime now, int initialIndex, int amountOfRows, int documentNumber, int referenceNumber)
            {
                if (string.IsNullOrWhiteSpace(atAddress)) throw new ArgumentException(nameof(atAddress));
                if (string.IsNullOrWhiteSpace(currencyCode)) throw new ArgumentException(nameof(currencyCode));
                if (now == default(DateTime)) throw new ArgumentNullException(nameof(now));
                if (initialIndex < 0) throw new GameEngineException($"{nameof(initialIndex)} must be greater or equal than 0");
                if (amountOfRows <= 0) throw new GameEngineException($"{nameof(amountOfRows)} must be greater than 0");
                if (documentNumber < 0) throw new GameEngineException($"{nameof(documentNumber)} must be greater than 0");
                if (referenceNumber < 0) throw new GameEngineException($"{nameof(referenceNumber)} must be greater than 0");

                var startDate = now.AddDays(-2).Date;
                var endDate = now.Date;
                return MovementsReportFilteredBy(atAddress, currencyCode, storeId, startDate, endDate, initialIndex, amountOfRows, documentNumber, referenceNumber);
            }

            internal MovementsReport MovementsReportFilteredByLast14Days(string atAddress, string currencyCode, int storeId, DateTime now, int initialIndex, int amountOfRows, int documentNumber, int referenceNumber)
            {
                if (string.IsNullOrWhiteSpace(atAddress)) throw new ArgumentException(nameof(atAddress));
                if (string.IsNullOrWhiteSpace(currencyCode)) throw new ArgumentException(nameof(currencyCode));
                if (now == default(DateTime)) throw new ArgumentNullException(nameof(now));
                if (initialIndex < 0) throw new GameEngineException($"{nameof(initialIndex)} must be greater or equal than 0");
                if (amountOfRows <= 0) throw new GameEngineException($"{nameof(amountOfRows)} must be greater than 0");
                if (documentNumber < 0) throw new GameEngineException($"{nameof(documentNumber)} must be greater than 0");
                if (referenceNumber < 0) throw new GameEngineException($"{nameof(referenceNumber)} must be greater than 0");

                var startDate = now.AddDays(-14).Date;
                var endDate = now.Date;
                return MovementsReportFilteredBy(atAddress, currencyCode, storeId, startDate, endDate, initialIndex, amountOfRows, documentNumber, referenceNumber);
            }

            internal MovementsReport MovementsReportFilteredByLast30Days(string atAddress, string currencyCode, int storeId, DateTime now, int initialIndex, int amountOfRows, int documentNumber, int referenceNumber)
            {
                if (string.IsNullOrWhiteSpace(atAddress)) throw new ArgumentException(nameof(atAddress));
                if (string.IsNullOrWhiteSpace(currencyCode)) throw new ArgumentException(nameof(currencyCode));
                if (now == default(DateTime)) throw new ArgumentNullException(nameof(now));
                if (initialIndex < 0) throw new GameEngineException($"{nameof(initialIndex)} must be greater or equal than 0");
                if (amountOfRows <= 0) throw new GameEngineException($"{nameof(amountOfRows)} must be greater than 0");
                if (documentNumber < 0) throw new GameEngineException($"{nameof(documentNumber)} must be greater than 0");
                if (referenceNumber < 0) throw new GameEngineException($"{nameof(referenceNumber)} must be greater than 0");

                var startDate = now.AddDays(-30).Date;
                var endDate = now.Date;
                return MovementsReportFilteredBy(atAddress, currencyCode, storeId, startDate, endDate, initialIndex, amountOfRows, documentNumber, referenceNumber);
            }

            private const int MAXIMUM_AMOUNT_OF_ROWS_ALLOWED = 100;
            internal MovementsReport MovementsReportFilteredBy(string atAddress, string currencyCode, int storeId, DateTime paramStartDate, DateTime paramEndDate, int initialIndex, int amountOfRows, int documentNumber, int referenceNumber)
            {
                if (string.IsNullOrWhiteSpace(atAddress)) throw new ArgumentException(nameof(atAddress));
                if (string.IsNullOrWhiteSpace(currencyCode)) throw new ArgumentException(nameof(currencyCode));
                if (paramStartDate == default(DateTime)) throw new ArgumentNullException(nameof(paramStartDate));
                if (paramEndDate == default(DateTime)) throw new ArgumentNullException(nameof(paramEndDate));
                if (initialIndex < 0) throw new GameEngineException($"{nameof(initialIndex)} must be greater or equal than 0");
                if (amountOfRows <= 0) throw new GameEngineException($"{nameof(amountOfRows)} must be greater than 0");
                if (documentNumber < 0) throw new GameEngineException($"{nameof(documentNumber)} must be greater than 0");
                if (referenceNumber < 0) throw new GameEngineException($"{nameof(referenceNumber)} must be greater than 0");

                if (amountOfRows > MAXIMUM_AMOUNT_OF_ROWS_ALLOWED)
                {
                    amountOfRows = MAXIMUM_AMOUNT_OF_ROWS_ALLOWED;
                }
                var startDate = paramStartDate.Date;
                var endDate = paramEndDate.Date;

                var report = Movements.Storage.MovementsReportFilteredBy(
                    atAddress,
                    Coinage.Coin(currencyCode),
                    storeId,
                    startDate,
                    endDate,
                    initialIndex,
                    amountOfRows,
                    documentNumber,
                    referenceNumber
                    );
                return report;
            }

            internal MovementsFiltered MovementsFilteredBy(string atAddress, string currencyCode, int storeId, string sourceId, int enteredBy, DateTime startDate, DateTime endDate, int initialIndex, 
                int amountOfRows, string movementType, string accountNumber, string paymentProcessorId)
            {
                return MovementsFilteredBy(atAddress, Coinage.Coin(currencyCode), storeId, sourceId, enteredBy, startDate, endDate, initialIndex, amountOfRows, movementType, accountNumber, paymentProcessorId);
            }
            internal MovementsFiltered MovementsFilteredBy(string atAddress, Coin currencyCode, int storeId, string sourceId, int enteredBy, DateTime startDate, DateTime endDate, int initialIndex, 
                int amountOfRows, string movementType, string accountNumber, string processorId)
            {
                if (string.IsNullOrWhiteSpace(atAddress)) throw new ArgumentNullException(nameof(atAddress));
                if (string.IsNullOrWhiteSpace(sourceId)) throw new ArgumentNullException(nameof(sourceId));
                if (string.IsNullOrWhiteSpace(processorId)) throw new ArgumentNullException(nameof(processorId));
                if (enteredBy < 0) throw new GameEngineException($"{nameof(enteredBy)} must be greater or equal than 0");
                if (startDate == default(DateTime)) throw new ArgumentNullException(nameof(startDate));
                if (endDate == default(DateTime)) throw new ArgumentNullException(nameof(endDate));
                if (startDate > endDate) throw new GameEngineException($"{nameof(startDate)} cannot be greater than {nameof(endDate)}");
                if (initialIndex < 0) throw new GameEngineException($"{nameof(initialIndex)} must be greater or equal than 0");
                if (amountOfRows <= 0) throw new GameEngineException($"{nameof(amountOfRows)} must be greater than 0");

                if (amountOfRows > MAXIMUM_AMOUNT_OF_ROWS_ALLOWED)
                {
                    amountOfRows = MAXIMUM_AMOUNT_OF_ROWS_ALLOWED;
                }

                const string ALL = "all";
                int sourceAsInt = Sources.NO_SOURCE_ID;
                var allSourcesAreSelected = sourceId == ALL;
                if (!allSourcesAreSelected)
                {
                    if (!int.TryParse(sourceId, out sourceAsInt)) throw new GameEngineException($"{nameof(sourceId)} '{sourceId}' is not {nameof(Int32)}");
                    if (sourceAsInt <= 0) throw new GameEngineException($"{nameof(sourceAsInt)} must be greater than 0");
                }

                int processorIdAsInt = WholePaymentProcessor.NoPaymentProcessor;
                var allProcessorsAreSelected = processorId == ALL;
                if (!allProcessorsAreSelected)
                {
                    if (!int.TryParse(processorId, out processorIdAsInt)) throw new GameEngineException($"{nameof(processorId)} '{processorId}' is not {nameof(Int32)}");
                    if (processorIdAsInt <= 0) throw new GameEngineException($"{nameof(processorIdAsInt)} must be greater than 0");
                }

                int movementTypeAsInt = Movement.ALL_MOVEMENT_TYPE;
                Movement.type type;
                var allMovementTypeAreSelected = movementType == ALL;
                if (!allMovementTypeAreSelected)
                {
                    if (!Enum.TryParse(movementType, out type)) throw new GameEngineException($"{nameof(movementType)} '{movementType}' is not valid for {nameof(Movement.type)}");
                    movementTypeAsInt = (int)type;
                    if (movementTypeAsInt < Movement.ALL_MOVEMENT_TYPE) throw new GameEngineException($"{nameof(movementTypeAsInt)} must be greater than 0");
                }

                var movements = Movements.Storage.ListMovement(
                        sourceAsInt,
                        atAddress,
                        storeId,
                        currencyCode,
                        startDate.Date,
                        endDate.Date,
                        enteredBy,
                        initialIndex,
                        amountOfRows,
                        movementTypeAsInt,
                        accountNumber,
                        processorIdAsInt
                        );
                return movements;
            }

            internal MovementsFiltered MyMovementsFilteredBy(string atAddress, string currencyCode, int storeId, string sourceId, int enteredBy, DateTime startDate, DateTime endDate, int initialIndex, int amountOfRows)
            {
                if (string.IsNullOrWhiteSpace(atAddress)) throw new ArgumentNullException(nameof(atAddress));
                if (string.IsNullOrWhiteSpace(sourceId)) throw new ArgumentNullException(nameof(sourceId));
                if (enteredBy < 0) throw new GameEngineException($"{nameof(enteredBy)} must be greater or equal than 0");
                if (startDate == default(DateTime)) throw new ArgumentNullException(nameof(startDate));
                if (endDate == default(DateTime)) throw new ArgumentNullException(nameof(endDate));
                if (startDate > endDate) throw new GameEngineException($"{nameof(startDate)} cannot be greater than {nameof(endDate)}");
                if (initialIndex < 0) throw new GameEngineException($"{nameof(initialIndex)} must be greater or equal than 0");
                if (amountOfRows <= 0) throw new GameEngineException($"{nameof(amountOfRows)} must be greater than 0");

                if (amountOfRows > MAXIMUM_AMOUNT_OF_ROWS_ALLOWED)
                {
                    amountOfRows = MAXIMUM_AMOUNT_OF_ROWS_ALLOWED;
                }

                const string ALL = "all";
                int sourceAsInt = Sources.NO_SOURCE_ID;
                var allSourcesAreSelected = sourceId == ALL;
                if (!allSourcesAreSelected)
                {
                    if (!int.TryParse(sourceId, out sourceAsInt)) throw new GameEngineException($"{nameof(sourceId)} '{sourceId}' is not {nameof(Int32)}");
                    if (sourceAsInt <= 0) throw new GameEngineException($"{nameof(sourceAsInt)} must be greater than 0");
                }

                var movements = Movements.Storage.ListMovement(
                        sourceAsInt,
                        atAddress,
                        storeId,
                        Coinage.Coin(currencyCode),
                        startDate.Date,
                        endDate.Date,
                        enteredBy,
                        initialIndex,
                        amountOfRows
                        );
                return movements;
            }
        }
    }

    public static class GeneralSources
    {
        static List<GeneralSource> sources = new List<GeneralSource>();
        static List<CurrencyFromSource> validCurrencies = new List<CurrencyFromSource>() { new CurrencyFromSource(Currencies.CODES.FP), new CurrencyFromSource(Currencies.CODES.USD)};

        internal static GeneralSource CreateSource(int sourceNumber, DateTime creationDate, string name, string isoCode)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (sources.Any(x => x.Number == sourceNumber)) throw new GameEngineException($"Source {name} already exist");

            GeneralSource result = new GeneralSource(creationDate, sourceNumber, name, isoCode);
            sources.Add(result);
            return result;
        }

        internal static int NextSourceId
        {
            get
            {
                var result = sources.Count == 0 ? 1 : sources.Max(x => x.Number) + 1;
                return result;
            }
        }

        internal static IEnumerable<GeneralSource> List()
        {
            return sources;
        }

        internal static IEnumerable<CurrencyFromSource> ValidCurrencies()
        {
            return validCurrencies;
        }

        internal static string SourceName(int sourceNumber, string currencyAsText)
        {
            if (sourceNumber <= 0) throw new GameEngineException($"{nameof(sourceNumber)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(currencyAsText)) throw new ArgumentNullException(nameof(currencyAsText));

            foreach (var source in sources)
            {
                if (source.Number == sourceNumber) return source.Name;
            }
            return $"Campaign {currencyAsText}";
        }

        internal static bool Any(int sourceNumber)
        {
            var result = sources.Any(x => x.Number == sourceNumber);
            return result;
        }

        [Puppet]
        internal class GeneralSource : Objeto
        {
            readonly DateTime creationDate;
            readonly int sourceNumber;
            readonly string name;
            readonly string isoCode;

            internal GeneralSource(DateTime creationDate, int sourceNumber, string name, string isoCode)
            {
                this.creationDate = creationDate;
                this.sourceNumber = sourceNumber;
                this.name = name;
                this.isoCode = isoCode;
            }

            internal int Number
            {
                get
                {
                    return this.sourceNumber;
                }
            }

            internal string Name
            {
                get
                {
                    return this.name;
                }
            }

            internal string CurrencyAsText
            {
                get
                {
                    return this.isoCode.ToString();
                }
            }

            internal string CurrencyTypeAsText
            {
                get
                {
                    return Coinage.Type(isoCode.ToString());
                }
            }
        }

        [Puppet]
        internal class CurrencyFromSource : Objeto
        {
            readonly Currencies.CODES isoCode;

            internal int Number
            {
                get
                {
                    return (int)isoCode;
                }
            }

            internal string Name
            {
                get
                {
                    return Coinage.Coin(isoCode).Name;
                }
            }

            internal string CurrencyAsText
            {
                get
                {
                    return this.isoCode.ToString();
                }
            }

            internal string CurrencyTypeAsText
            {
                get
                {
                    return Coinage.Type(isoCode.ToString());
                }
            }

            internal CurrencyFromSource(Currencies.CODES isoCode)
            {
                this.isoCode = isoCode;
            }
        }
    }
}
