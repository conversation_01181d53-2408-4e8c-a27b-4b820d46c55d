﻿using Connectors.town.connectors.driver.transactions;
using GamesEngine;
using GamesEngine.Finance;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;
using static town.connectors.drivers.Result;

namespace town.connectors.drivers.fiero
{
    internal class Withdrawal : FierroProcessorDriver
	{
		private const float VERSION = 1.1F;
		public override string Description => $"Fiero {nameof(Withdrawal)} driver {VERSION}";
		public Withdrawal()
			: base(TransactionType.Withdrawal, VERSION)
        {
        }

		public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			throw new NotImplementedException();

		}

		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			var result = DeductAmount(now, recordSet);
			WithdrawalTransaction auth;
			if (result > 0)
			{
				auth = new WithdrawalTransaction(result, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes);
			}
			else
            {
				auth = new WithdrawalTransaction(result, TransactionStatus.DENIED, Entity, PaymentMethod, CurrencyIsoCodes);
			}
			
			return (T)Convert.ChangeType(auth, typeof(T));
		}

		public override void Prepare(DateTime now)
		{
			CustomSettings.AddVariableParameter("actor");
			CustomSettings.AddVariableParameter("message");

			//CustomSettings.Prepare();
		}

		int MakeAWithDraw(WithdrawMessage message)
		{
			if (message == null) throw new ArgumentNullException(nameof(message));
			Currency amount = Currency.Factory(message.Currency, message.Amount);

			var authorization = Movements.Storage.NextAuthorizationNumber();

			return authorization;
		}

		private int DeductAmount(DateTime now, CustomSettings.RecordSet recordSet)
		{
			var Actor = recordSet.Mappings["actor"];
			var Message = recordSet.Mappings["message"];
			var actor = Actor.As<RestAPISpawnerActor>();
			WithdrawMessage message = Message.As<WithdrawMessage>();
			var description = Validator.StringEscape(message.Description);
			Currency amount = Currency.Factory(message.Currency, message.Amount);
			string command = "";
			int authorization = MakeAWithDraw(message);

			if (message.HasSource())
			{
				command = $@"
							{{
								source{message.SourceNumber} = atAddress.GetOrCreateSource(itIsThePresent, now, {message.SourceNumber}, '{message.Currency}', '{message.SourceName}');
								balance = atAddress.CreateBalanceIfNotExists('{message.Currency}');
								Eval('available = '+balance.Available+';');
								balance.SetInitialBalance(itIsThePresent, available);
								store = company.Sales.StoreById({message.StoreId.ToString()});
								source{message.SourceNumber}.Withdraw(itIsThePresent, Now, Currency('{message.Currency}',{message.Amount}), '{message.Who}', '{authorization}', store, '{description}', '{message.Reference}','{message.AccountNumber}', {message.ProcessorId});
							}}";
			}
			else if (!string.IsNullOrEmpty(message.AccountNumber))
			{
				command = $@"
							{{
								balance = atAddress.CreateAccountIfNotExists('{message.Currency}', '{message.AccountNumber}').Balance;
								Eval('available = '+balance.Available+';');
								balance.SetInitialBalance(itIsThePresent, available);
								store = company.Sales.StoreById({message.StoreId.ToString()});
								balance.Withdraw(itIsThePresent, Now, Currency('{amount.CurrencyCode}',{amount.Value}), '{message.Who}', '{authorization}', store, '{description}', '{message.Reference}', {message.ProcessorId});
							}}";
			}
			else
			{
				command = $@"
							{{
								balance = atAddress.CreateBalanceIfNotExists('{message.Currency}');
								Eval('available = '+balance.Available+';');
								balance.SetInitialBalance(itIsThePresent, available);
								store = company.Sales.StoreById({message.StoreId.ToString()});
								balance.Withdraw(itIsThePresent, Now, Currency('{amount.CurrencyCode}',{amount.Value}), '{message.Who}', '{authorization}', store, '{description}', '{message.Reference}', {message.ProcessorId});
							}}";
			}

			var result = actor.PerformCmd(message.AtAddress, command);
			if (!(result is OkObjectResult))
			{
				throw new GameEngineException($"Withdraw was not saved coz command fails on cashier.");
			}

			return authorization;
		}

	}
}
