﻿using GamesEngine.Finance;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Threading.Tasks;

namespace ExchangeAPI.Model
{
	[DataContract(Name = "JournalEntriesDTO")]
	public class JournalEntriesDTO
    {
		[DataMember(Name = "journalEntries")]
		public List<JournalEntryDTO> JournalEntries { get; set; } = new List<JournalEntryDTO>();

		[DataMember(Name = "totalRecords")]
		public int TotalRecords { get; set; }

		internal void Add(JournalEntryDTO journalEntryDTO)
		{
			JournalEntries.Add(journalEntryDTO);
		}
	}

	[DataContract(Name = "JournalEntryDTO")]
	public class JournalEntryDTO
	{
		[DataMember(Name = "date")]
		public string Date { get; set; }
		[DataMember(Name = "title")]
		public string Title { get; set; }
		[DataMember(Name = "entryId")]
		public string EntryId { get; set; }
		[DataMember(Name = "reference")]
		public string Reference { get; set; }
		[DataMember(Name = "whoCreated")]
		public string WhoCreated { get; set; }
		[DataMember(Name = "totalCredit")]
		public decimal TotalCredit 
		{ 
			get 
			{ 
				return Details.Sum(detail => detail.Credit); 
			} 
		}
		[DataMember(Name = "totalCreditFormatted")]
		public string TotalCreditFormatted
		{
			get
			{
				return Currency.Factory(Currencies.CODES.USD.ToString(), TotalCredit).ToDisplayFormat();
			}
		}
		[DataMember(Name = "totalDebit")]
		public decimal TotalDebit
		{
			get
			{
				return Details.Sum(detail => detail.Debit);
			}
		}
		[DataMember(Name = "totalDebitFormatted")]
		public string TotalDebitFormatted
		{
			get
			{
				return Currency.Factory(Currencies.CODES.USD.ToString(), TotalDebit).ToDisplayFormat();
			}
		}
		[DataMember(Name = "details")]
		public List<JournalEntryDetailDTO> Details { get; set; } = new List<JournalEntryDetailDTO>();

		internal void Add(JournalEntryDetailDTO journalEntryDTO)
		{
			Details.Add(journalEntryDTO);
		}
	}

	[DataContract(Name = "JournalEntryDetailDTO")]
	public class JournalEntryDetailDTO
	{
		[DataMember(Name = "sequence")]
		public long Sequence { get; set; }
		[DataMember(Name = "account")]
		public string Account { get; set; }
		[DataMember(Name = "description")]
		public string Description { get; set; }
		[DataMember(Name = "credit")]
		public decimal Credit { get; set; }
		[DataMember(Name = "creditFormatted")]
		public string CreditFormatted
		{
			get
			{
				return Currency.Factory(Currencies.CODES.USD.ToString(), Credit).ToDisplayFormat();
			}
		}
		[DataMember(Name = "debit")]
		public decimal Debit { get; set; }
		[DataMember(Name = "debitFormatted")]
		public string DebitFormatted
		{
			get
			{
				return Currency.Factory(Currencies.CODES.USD.ToString(), Debit).ToDisplayFormat();
			}
		}
	}
}
