﻿using Puppeteer;
using Puppeteer.EventSourcing;

namespace CashierAPI.CashierFollower
{
    public class InitialBalanceFPRule: RuleWithoutActor
    {
        protected const string TEXT_SetInitialBalance = "balance.SetInitialBalance(";
        protected const string TEXT_available = "available =";
        protected const string FREEPLAY_CURRENCY = "'FP'";

        public override void Then(Script script)
        {
            if (script.Text.IndexOf(TEXT_available) == -1)
            {
                if (script.DairyId == InitialBalanceHandler.GetLastId("FP")) InitialBalanceHandler.OldFPCommandsWithSkip = false;
                else DBHelperCashierFollower.ProceedToMarkSkipsForSpecificDairyId(script.DairyId);
            }
        }

        public override void When(Script script)
        {
            if (script.Text.IndexOf(TEXT_SetInitialBalance) != -1 && script.Text.IndexOf(FREEPLAY_CURRENCY) != -1)
            {
                Then(script);
            }

        }

        public override void FinalizeRule()
        {
            return;
        }
    }
}
