﻿using GamesEngine.Resources;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Bets
{
	class TagCollection : IEnumerable<Tag>
	{
		private readonly HashSet<Tag> tags = new HashSet<Tag>();

		internal void Add(Tag tag)
		{
			if (tag == null) throw new ArgumentNullException(nameof(tag));
			if (this.tags.Contains(tag)) throw new GameEngineException($"Tags already contains {tag.Label}");

			this.tags.Add(tag);
		}

		internal void Remove(Tag tag)
		{
			if (tag == null) throw new ArgumentNullException(nameof(tag));
			if (!this.tags.Contains(tag)) throw new GameEngineException($"Tags does not contains {tag.Label}");

			this.tags.Remove(tag);
		}

		internal void Clear()
		{
			this.tags.Clear();
		}

		internal Tag Find(string label)
		{
			if (string.IsNullOrWhiteSpace(label)) throw new ArgumentNullException(nameof(label));

			var result = Enumerable.FirstOrDefault(tags, x => x.Label == label);
			return result;
		}

		internal bool Has(Tag tag)
		{
			if (tag == null) throw new ArgumentNullException(nameof(tag));

			bool result = this.tags.Contains(tag);
			return result;
		}

		public IEnumerator<Tag> GetEnumerator()
		{
			return this.tags.GetEnumerator();
		}

		IEnumerator IEnumerable.GetEnumerator()
		{
			return this.tags.GetEnumerator();
		}
	}

	[Puppet]
	public class Tag : Objeto
	{
		private readonly string label;
		private string description;
		private readonly ResourceUrl imageUrl;

		internal Tag(string label, string imageUrl)
		{
			if (string.IsNullOrWhiteSpace(label)) throw new ArgumentNullException(nameof(label));

			this.label = label;
			this.imageUrl = new ResourceUrl(imageUrl);
		}

		internal string Label
		{
			get
			{
				return this.label;
			}
		}

		internal string Description
		{
			get
			{
				return this.description;
			}
			set
            {
				if (string.IsNullOrWhiteSpace(value)) throw new ArgumentNullException(nameof(value));

				description = value;
			}
		}

		internal ResourceUrl ImageUrl
		{
			get
			{
				return this.imageUrl;
			}
		}
	}

	internal class TaggedGroup<T>
	{
		private readonly Dictionary<T, TagCollection> set = new Dictionary<T, TagCollection>();

		internal void Tag(T entity, Tag tag)
		{
			TagCollection tags;
			if(!set.TryGetValue(entity, out tags))
			{
				tags = new TagCollection();
				this.set.Add(entity, tags);
			}
			else
			{
				if (tags.Has(tag)) throw new GameEngineException($"Entity already has tag {tag.Label}");
			}
			tags.Add(tag);
		}

		internal void Untag(T entity, Tag tag)
		{
			TagCollection tags;
			if (!set.TryGetValue(entity, out tags))
			{
				throw new GameEngineException($"Entity does not have tag {tag.Label}");
			}
			else
			{
				if (!tags.Has(tag)) throw new GameEngineException($"Entity does not have tag {tag.Label}");
			}
			tags.Remove(tag);
		}

		internal void Untags(T entity)
		{
			TagCollection tags;
			if (!set.TryGetValue(entity, out tags))
			{
				throw new GameEngineException($"Entity does not have tags");
			}
			tags.Clear();
		}

		internal bool HasTag(T entity, Tag tag)
		{
			if (entity == null) throw new ArgumentNullException(nameof(entity));
			if (tag == null) throw new ArgumentNullException(nameof(tag));

			TagCollection tags;
			if (!set.TryGetValue(entity, out tags))
			{
				return false;
			}

			return tags.Has(tag);
		}

		internal IEnumerable<T> WhoHas(Tag tag)
		{
			if (tag == null) throw new ArgumentNullException(nameof(tag));
			List<T> result = new List<T>();
			foreach(T entity in this.set.Keys)
			{
				TagCollection tags = this.set[entity];
				if (tags.Has(tag))
					result.Add(entity);
			}

			return result;
		}

		internal IEnumerable<Tag> Tags(T entity)
		{
			if (entity == null) throw new ArgumentNullException(nameof(entity));
			TagCollection tags;
			if (!set.TryGetValue(entity, out tags))
			{
				return Enumerable.Empty<Tag>();
			}

			return tags;
		}
	}

}
