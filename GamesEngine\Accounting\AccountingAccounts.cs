﻿using System;
using System.Collections.Generic;
using System.Text;

namespace GamesEngine.Accounting
{
	internal class AccountingAccounts
	{
		private Dictionary<int, AccountingAccount> accounts;
		private int contador;
		public const int EXPENSE_ACCOUNT = 0;
		public const int INCOME_ACCOUNT = 1;

		internal AccountingAccounts()
		{
			this.accounts = new Dictionary<int, AccountingAccount>();
		}

		internal void Add(string account, string description)
		{
			if (string.IsNullOrWhiteSpace(account)) throw new ArgumentNullException(nameof(account));
			if (string.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));

			accounts.Add(contador, new AccountingAccount(account, description));
			contador++;
		}

		internal AccountingAccount Find(int number)
		{
			AccountingAccount result;
			accounts.TryGetValue(number, out result);
			if (result == null) throw new GameEngineException($"Number {number} is not a valid accounting account");

			return result;
		}

	}
}
