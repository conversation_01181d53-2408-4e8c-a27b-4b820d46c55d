﻿using GamesEngine.Business;
using GamesEngine.Games.Lotto;
using GamesEngine.Games.Tournaments;
using GamesEngine.Games.Tournaments.MarchMadness.Groups;
using GamesEngine.Gameboards;
using GamesEngine.Gameboards.Lotto;
using GamesEngine.Gameboards.MarchMadness;
using GamesEngine.Gamification.Badges;
using GamesEngine.Marketing.Campaigns;
using GamesEngine.Messaging;
using GamesEngine.Preferences.Lotto;
using GamesEngine.PurchaseOrders;
using GamesEngine.Resources;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using GamesEngine.Preferences.Lines;
using GamesEngine.Games;
using GamesEngine.Domains;
using static GamesEngine.Exchange.PaymentProcessorsAndActionsByDomains;
using static GamesEngine.Finance.PaymentChannels;

namespace GamesEngine.Bets
{
	[Puppet]
	public class Player : O<PERSON><PERSON><PERSON>, Subscriber, IResourceOwner
	{
		private Avatar avatar;
		private readonly Customer customer;
		private string nickname = "";
		private readonly List<Bet> bets = new List<Bet>();
		private readonly List<Ticket> tickets = new List<Ticket>();
		private readonly string id;
		private readonly int consecutive;
		private readonly Messages messages;
		private FavoritesCatalog favorites;
		private LottoPlayerPreferences lottoPreferences;
		private PlayerPreferences preferences;
		private bool hasDefaultNickname = true;
		private readonly List<Badge> badges;

		private readonly PlayerLottoReports playerLottoReports;
		internal PlayerLottoReports PlayerLottoReports
		{
			get
			{
				return playerLottoReports;
			}
		}
		private readonly PlayerLinesReports playerLinesReports;
		internal PlayerLinesReports PlayerLinesReports
		{
			get
			{
				return playerLinesReports;
			}
		}

		internal Player(Customer customer, Agents agent)
		{
			this.customer = customer;

			this.id = customer.Company.EncryptionHelper.Encrypt(customer.AccountNumber);
			this.messages = new Messages(customer.Company.Campaigns, this);
			consecutive = customer.Company.NextPlayerConsecutive();
			playerLottoReports = new PlayerLottoReports(this, tickets, customer);
			playerLinesReports = new PlayerLinesReports(this, bets, customer);
			this.badges = new List<Badge>();
			this.preferences = new PlayerPreferences();
			this.Agent = agent;
		}		

		internal IEnumerable<Game> PopularGames(Domain domain, DateTime date)
		{
			if (domain == null) throw new ArgumentNullException(nameof(domain));

			IEnumerable<Game> popular = Enumerable.Empty<Game>();

			popular = popular.Concat(this.Preferences.GamesReminder.FirstMatchInPreGame(date));
			popular = popular.Concat(this.Preferences.GamesReminder.FirstMatchInPlay(date));

			foreach (var mySport in this.Preferences.Sports.Favorites())
			{
				if (this.Company.Sales.CurrentStore.IsEnabled(mySport, domain))
				{
					popular = popular.Concat(this.Company.Tournaments.GamesInPlayTo(mySport, domain, date));
					popular = popular.Concat(this.Company.Tournaments.FirstGameToPlayInPregame(mySport, date));
				}
			}

			return popular.ToList();
		}

		internal Customer Customer
		{
			get
			{
				return customer;
			}
		}

		internal Company Company
		{
			get
			{
				return customer.Company;
			}
		}
		internal PlayerPreferences Preferences
		{
			get
			{
				return preferences;
			}
		}

		internal string Id
		{
			get
			{
				return id;
			}
		}

		internal int Consecutive
		{
			get
			{
				return consecutive;
			}
		}

		internal Avatar Avatar
		{
			get
			{
				if (avatar == null) throw new Exception("Customer's avatar has not been defined");
				return avatar;
			}
		}

		internal bool HasAvatar()
		{
			return avatar != null;
		}

		internal string Nickname
		{
			get
			{
				if (!hasDefaultNickname) return nickname;

				var result = $"{nickname} {consecutive}";
				return result;
			}
			set
			{
				if (value == null) throw new ArgumentNullException(nameof(value));
				nickname = Business.Accounting.ReplaceAccountNumbers(value);
			}
		}

		internal void ValidateAndUpdateNickname(bool itIsThePresent, string value)
		{
			if (string.IsNullOrWhiteSpace(value)) throw new ArgumentNullException(nameof(value));

			hasDefaultNickname = false;
			this.Nickname = value;
		}

		internal void AddHighest(Badge badge)
		{
			if (badge == null) throw new ArgumentNullException(nameof(badge));
			if (this.badges.Contains(badge)) return;

			var campaign = badge.Badges;
			this.badges.RemoveAll(x => x.Badges == campaign);
			this.badges.Add(badge);
		}

		internal Badge Highest(Badges campaign)
		{
			if (campaign == null) throw new ArgumentNullException(nameof(campaign));
			Badge result = this.badges.FirstOrDefault(x => x.Badges == campaign);
			if (result == null) throw new GameEngineException($"Player {this.AccountNumber} does not have any badge on {campaign.Name}");
			return result;
		}

		internal bool HasBadges(Badges campaign)
		{
			bool result = this.badges.Any(x => x.Badges == campaign);
			return result;
		}

		internal bool HasDefaultNickName
		{
			get
			{
				return hasDefaultNickname;
			}
		}

		internal string AccountNumber
		{
			get
			{
				return customer.AccountNumber;
			}
		}

		[Obsolete]
		internal bool ItsFirstTime()
		{
			return customer.ItsFirstTime();
		}

		internal LottoPlayerPreferences LottoPreferences()
		{
			if (lottoPreferences == null)
			{
				var defaultPreferences = customer.Company.Lotto900().DefaultPreferences;
				lottoPreferences = new LottoPlayerPreferences(defaultPreferences, this);
			}
			return lottoPreferences;
		}

		private LinesPlayerPreferences linesPreferences;
		internal LinesPlayerPreferences LinesPreferences(Tournament tournament)
		{
			if (linesPreferences == null)
			{
				var defaultPreferences = customer.Company.Betboard(tournament).DefaultPreferences;
				linesPreferences = new LinesPlayerPreferences(defaultPreferences);
			}
			return linesPreferences;
		}

		internal FavoritesCatalog FavoritesCatalog()
		{
			if (favorites == null)
			{
				favorites = new FavoritesCatalog(this);
                favorites.CreateRecentNumbers();
            }
			return favorites;
		}

		internal void SaveLastPurchase(DateTime now)
		{
            hasPurchased = true;
            LastPurchaseDate = now;
		}

		bool hasPurchased;
		internal bool HasPurchased()
		{
			return hasPurchased;
		}

		internal DateTime LastPurchaseDate { get; private set; }

        string IResourceOwner.Id => this.id;

		string Subscriber.Id => this.id;

		Messages Subscriber.Messages => messages;

		internal Messages Messages
		{
			get
			{
				return messages;
			}
		}

		internal Agents Agent { get; private set; }
		internal int AgentNumber { get { return (int)Agent;  } }

		internal void Add(Bet bet)
		{
			if (bet == null) throw new ArgumentNullException(nameof(bet));
			//Se podria chequear que >0
			//pensar otras posibles
			//pueden haber 2 apuestas del mismmo gameboard?
			bets.Add(bet);
		}

		internal void Add(Ticket ticket)
		{
			if (ticket == null) throw new ArgumentNullException(nameof(ticket));

			tickets.Add(ticket);
		}

		internal void Remove(Bet bet)
		{
			if (bet == null) throw new ArgumentNullException(nameof(bet));

			bets.Remove(bet);
		}

		internal void Remove(Ticket ticket)
		{
			if (ticket == null) throw new ArgumentNullException(nameof(ticket));

			tickets.Remove(ticket);
		}

		internal void Replace(Bet oldBet, Bet newBet)
		{
			if (oldBet == null) throw new ArgumentNullException(nameof(oldBet));
			if (oldBet.Player != this) throw new GameEngineException("Old bet should belong to this player");
			if (newBet == null) throw new ArgumentNullException(nameof(newBet));
			if (newBet.Player != this) throw new GameEngineException("New bet should belong to this player");
			if (oldBet == newBet) throw new GameEngineException("Bet replace can not be done using the same bet");
			if (oldBet.Number != newBet.Number) throw new GameEngineException("Bet replace can not be done using different bet numbers");

			var index = bets.BinarySearch(oldBet);
			if (index == -1) throw new GameEngineException("Bet to be replaced has not been found");
			bets[index] = newBet;
		}

		internal void ReplaceAtTheEnd(Bet oldBet, Bet newBet)
		{
			if (oldBet == null) throw new ArgumentNullException(nameof(oldBet));
			if (oldBet.Player != this) throw new GameEngineException("Old bet should belong to this player");
			if (newBet == null) throw new ArgumentNullException(nameof(newBet));
			if (newBet.Player != this) throw new GameEngineException("New bet should belong to this player");
			if (oldBet == newBet) throw new GameEngineException("Bet replace can not be done using the same bet");
			if (oldBet.Number != newBet.Number) throw new GameEngineException("Bet replace can not be done using different bet numbers");

			var index = bets.LastIndexOf(oldBet);
			if (index == -1) throw new GameEngineException("Bet to be replaced has not been found");
			bets[index] = newBet;
		}

		[Obsolete("For backward compatibility")]
		internal void SendPositiveFeedback(bool itIsThePresent, MarchMadnessTournament tournament, string body, DateTime now)
		{
			if (String.IsNullOrWhiteSpace(body)) throw new ArgumentNullException(nameof(body));
			if (tournament == null) throw new ArgumentNullException(nameof(tournament));
			FeedbackMessage notification = new ThumbsUpFeedback(this, body);
			notification.CreationDate = now;
			messages.SendMessageTo(itIsThePresent, tournament, notification);
		}

		internal void SendPositiveFeedback(bool itIsThePresent, SuggestionBox suggestion, string body, DateTime now)
		{
			if (String.IsNullOrWhiteSpace(body)) throw new ArgumentNullException(nameof(body));
			FeedbackMessage notification = new ThumbsUpFeedback(this, body);
			notification.CreationDate = now;
			suggestion.Add(itIsThePresent, notification);
		}

		[Obsolete("For backward compatibility")]
		internal void SendNegativeFeedback(bool itIsThePresent, MarchMadnessTournament tournament, string body, DateTime now)
		{
			if (String.IsNullOrWhiteSpace(body)) throw new ArgumentNullException(nameof(body));
			if (tournament == null) throw new ArgumentNullException(nameof(tournament));
			FeedbackMessage notification = new ThumbsDownFeedback(this, body);
			notification.CreationDate = now;
			messages.SendMessageTo(itIsThePresent, tournament, notification);
		}

		internal void SendNegativeFeedback(bool itIsThePresent, SuggestionBox suggestion, string body, DateTime now, string deviceName, string browserName)
		{
			if (String.IsNullOrWhiteSpace(body)) throw new ArgumentNullException(nameof(body));
			FeedbackMessage notification = new ThumbsDownFeedback(this, body, deviceName, browserName);
			notification.CreationDate = now;
			suggestion.Add(itIsThePresent, notification);
		}

		[Obsolete("For backward compatibility")]
		internal void SendPositiveFeedbackToLotteries(bool itIsThePresent, PicksLotteryGame lotteries, string body, DateTime now)
		{
			if (String.IsNullOrWhiteSpace(body)) throw new ArgumentNullException(nameof(body));
			if (lotteries == null) throw new ArgumentNullException(nameof(lotteries));

			FeedbackMessage notification = new ThumbsUpFeedback(this, body);
			notification.CreationDate = now;
			messages.SendMessageTo(itIsThePresent, lotteries, notification);
		}

		internal void SendNegativeFeedbackToLotteries(bool itIsThePresent, PicksLotteryGame lotteries, string body, DateTime now)
		{
			if (String.IsNullOrWhiteSpace(body)) throw new ArgumentNullException(nameof(body));
			if (lotteries == null) throw new ArgumentNullException(nameof(lotteries));

			FeedbackMessage notification = new ThumbsDownFeedback(this, body);
			notification.CreationDate = now;
			messages.SendMessageTo(itIsThePresent, lotteries, notification);
		}

		internal void ShareToThirdParty(string shareItem)
		{
			if (string.IsNullOrWhiteSpace(shareItem)) throw new ArgumentNullException(shareItem);
		}

		internal void InvitePlayers(string emails)
		{
			if (string.IsNullOrWhiteSpace(emails)) throw new ArgumentNullException(emails);
		}

		internal int MarchMadnessBracketsGeneralPoolCount(int year, Round round, decimal fee)
		{
			MarchMadnessEdition rules = customer.Company.GetMarchMadnessEdition(year);
			var pool = rules.GeneralPool(round, fee);
			int bracketCount = MyGameboardsOnPool(pool).Count();
			return bracketCount;
		}

		internal IEnumerable<Gameboard> MyGameboardsOnPool(Pool pool)
		{
			if (pool == null) throw new ArgumentNullException(nameof(pool));
			var result = bets.Where(bet => bet.Reward == pool).Select(bet => bet.Gameboard).OrderByDescending(g => g.Grade()).ThenByDescending(g => g.Id);
			return result;
		}

		internal IEnumerable<Gameboard> Gameboards(Func<Gameboard, bool> match)
		{
			var result = this.Gameboards().Where(match);
			return result;
		}

		internal IEnumerable<Gameboard> Gameboards()
		{
			return this.Company.Gameboards.ByPlayer(this);
		}

		internal double TimeInSecondsToNextDraw(DateTime now)
		{
			double result;
			var ticketsFound = this.tickets.Where(x => x.IsUnprized());
			if (ticketsFound.Any())
			{
				result = ticketsFound.Min(x => x.RemainingTimeInSecondsToDrawDate(now));
			}
			else
			{
				const double IT_WONT_BE_A_NEXT_DRAW = Int32.MaxValue;
				result = IT_WONT_BE_A_NEXT_DRAW;
			}
			return result;
		}

		private readonly Dictionary<Room, Notification> joinNotifications = new Dictionary<Room, Notification>();
		internal void OnJoinToMyRoom(bool itIsThePresent, Subscriber from, Room room, Gameboard gameboard, DateTime now)
		{
			var oldNotification = joinNotifications.FirstOrDefault(x => x.Key == room).Value;
			if (oldNotification != null)
			{
				joinNotifications.Remove(room);
				oldNotification.Kill();
			}
			Subscriber me = (Subscriber)this;
			var notification = new Notification(from,
				$@"A new player has joined your group.<br>Bracket '{gameboard.Name}' joined your group '{room.Name}'"
			);
			notification.CreationDate = now;
			joinNotifications.Add(room, notification);
			me.Messages.SendMessageTo(itIsThePresent, me, notification);
		}

		internal IEnumerable<Gameboard> MyGameboardsOfRound(int year, Round round)
		{
			if (year < MarchMadnessTournament.MIN_MARCH_MADNESS_SECURE_YEAR || year > MarchMadnessTournament.MAX_MARCH_MADNESS_SECURE_YEAR) throw new GameEngineException($"The year must be greater than {MarchMadnessTournament.MIN_MARCH_MADNESS_SECURE_YEAR} and less than {MarchMadnessTournament.MAX_MARCH_MADNESS_SECURE_YEAR}");
			if (round == null) throw new ArgumentNullException(nameof(round));
			MarchMadnessEdition rules = customer.Company.GetMarchMadnessEdition(year);
			var generalPools = rules.GeneralPools();
			var superLeaguePools = rules.GeneralPoolsForSuperLeague();
			var finalFourPools = rules.GeneralPoolsForFinalFour();
			var resultOfGeneralPools = generalPools.Where(x => !superLeaguePools.Contains(x) && !finalFourPools.Contains(x)).ToList();
			var gameboards = bets.
				Where(
					bet => bet.Gameboard.GetType().Equals(typeof(MarchMadnessBracket)) &&
					((MarchMadnessBracket)bet.Gameboard).Year == year &&
					((MarchMadnessBracket)bet.Gameboard).InitialRoundOfPredictions() == round &&
					(!bet.GetType().Equals(typeof(BetCanceled))) &&
					resultOfGeneralPools.Contains((Pool)bet.Reward)
				).
				OrderBy(bet => bet.Contribution).
				Select(bet => bet.Gameboard);
			var result = gameboards.ToList();
			return result;
		}

		internal int CountMyBracketsIn(int year)
		{
			if (year < MarchMadnessTournament.MIN_MARCH_MADNESS_SECURE_YEAR || year > MarchMadnessTournament.MAX_MARCH_MADNESS_SECURE_YEAR) throw new GameEngineException($"The year must be greater than {MarchMadnessTournament.MIN_MARCH_MADNESS_SECURE_YEAR} and less than {MarchMadnessTournament.MAX_MARCH_MADNESS_SECURE_YEAR}");

			MarchMadnessEdition rules = customer.Company.GetMarchMadnessEdition(year);
			var result = bets.
				Where(
					bet => bet.Gameboard.GetType().Equals(typeof(MarchMadnessBracket)) &&
					((MarchMadnessBracket)bet.Gameboard).Year == year &&
					(!bet.GetType().Equals(typeof(BetCanceled))) &&
					rules.GeneralPools().Contains((Pool)bet.Reward)
				).
				Count();
			return result;
		}

		internal IEnumerable<Pool> NonGeneralPools(int year)
		{
			if (year < MarchMadnessTournament.MIN_MARCH_MADNESS_SECURE_YEAR || year > MarchMadnessTournament.MAX_MARCH_MADNESS_SECURE_YEAR) throw new GameEngineException($"The year must be greater than {MarchMadnessTournament.MIN_MARCH_MADNESS_SECURE_YEAR} and less than {MarchMadnessTournament.MAX_MARCH_MADNESS_SECURE_YEAR}");
			MarchMadnessEdition rules = customer.Company.GetMarchMadnessEdition(year);
			var generals = rules.GeneralPools();
			var pools = bets.
				Where(
					bet =>
						bet.Gameboard.GetType().Equals(typeof(MarchMadnessBracket)) &&
						((MarchMadnessBracket)bet.Gameboard).Year == year
				).
				Select(
					bet => (Pool)bet.Reward
				).
				Distinct();
			var nonGeneralPools = pools.Where(pool => !generals.Contains(pool)).ToList();
			return nonGeneralPools;
		}

		internal bool HasReachedLimitOfGroupCreation(int year, Round round)
		{
			if (round == null) throw new ArgumentNullException(nameof(round));
			if (year < MarchMadnessTournament.MIN_MARCH_MADNESS_SECURE_YEAR || year > MarchMadnessTournament.MAX_MARCH_MADNESS_SECURE_YEAR) throw new GameEngineException($"The year must be greater than {MarchMadnessTournament.MIN_MARCH_MADNESS_SECURE_YEAR} and less than {MarchMadnessTournament.MAX_MARCH_MADNESS_SECURE_YEAR}");

			var tournament = customer.Company.Tournaments.GetMarchMadnessTournamentOf(year);
			var rules = customer.Company.GetMarchMadnessEdition(year);
			var myOwnRooms = rules.RoomsOfARoundOwnedByPlayer(this, round);
			var hasReachedLimit = tournament.MaxAmountOfGroupsToBuyPerRound == myOwnRooms.Count();
			return hasReachedLimit;
		}

		internal bool HasGameboardNamed(string name)
		{
			var exists = bets.Exists(b => b.Gameboard.Name.ToLowerInvariant().Equals(name.ToLowerInvariant()));
			return exists;
		}

		internal bool IsPaid(Gameboard gameboard)
		{
			foreach (var bet in bets)
			{
				if (bet.Gameboard == gameboard)
				{
					if (bet is BetPayed)
					{
						return true;
					}
				}
			}
			return false;
		}

		internal Bet FindBet(Gameboard gameboard)
		{
			if (gameboard.Player != this) throw new GameEngineException($"Gameboard {gameboard.Id} does not belong to player with account {this.AccountNumber}");
			var index = BinarySearch(gameboard);
			if (index == -1) throw new GameEngineException($"Gameboard {gameboard.Id} has does not belongs to Player {id}");
			Bet bet = bets[index];
			return bet;
		}

		internal Bet FindBet(Gameboard gameboard, Reward reward)
		{
			if (gameboard.Player != this) throw new GameEngineException($"Gameboard {gameboard.Id} does not belong to player with account {this.AccountNumber}");
			Bet bet = bets.FirstOrDefault(b => b.Gameboard == gameboard && b.Reward == reward);
			if (bet == null) throw new GameEngineException($"Gameboard {gameboard.Id} has does not belongs to Player {id}");
			return bet;
		}

		private int BinarySearch(Gameboard gameboard)
		{
			int L = 0;
			int R = bets.Count - 1;
			while (L <= R)
			{
				int M = (L + R) / 2;
				if (bets[M].Gameboard.Id < gameboard.Id) L = M + 1;
				else if (bets[M].Gameboard.Id > gameboard.Id) R = M - 1;
				else return M;
			}
			return -1;
		}



		internal void RemoveDisposedGameboards()
		{
			bets.RemoveAll(bet => bet.Gameboard.IsReadyToBeDisposed());
		}

		internal void UpdateAvatar(string avatarPath, DateTime now)
		{
			if (String.IsNullOrWhiteSpace(avatarPath)) throw new ArgumentNullException(nameof(avatarPath));
			if (avatar == null)
			{
				avatar = new Avatar(this, avatarPath, now);
			}
			else
			{
				avatar.Update(avatarPath);
			}
		}

		internal void ApproveAvatar(DateTime now)
		{
			avatar.Approve();

			var marketingKPIs = customer.Company.MarketingKPIs();
			marketingKPIs.RegisterAvatarUpdate(this, now);
		}

		internal void DenyAvatar()
		{
			avatar.Deny();
		}

		internal class EncryptionHelper: Objeto
		{
			//TODO: Se podria sacar a un archivo para que IT lo setee a gusto
			private const string ENCRYPTION_KEY = "fhd98943698sdlsajkfhla874385dfkmsji5784353294u85jkhds23647326"; //DO NOT CHANGE THIS BECAUSE ALL PLAYER COULD HAVE DIFFERENT ID.
			internal string Encrypt(string clearText)
			{

				byte[] clearBytes = Encoding.Unicode.GetBytes(clearText);
				using (Aes encryptor = Aes.Create())
				{
					Rfc2898DeriveBytes pdb = new Rfc2898DeriveBytes(ENCRYPTION_KEY, new byte[] { 0x49, 0x76, 0x61, 0x6e, 0x20, 0x4d, 0x65, 0x64, 0x76, 0x65, 0x64, 0x65, 0x76 });
					encryptor.Key = pdb.GetBytes(32);
					encryptor.IV = pdb.GetBytes(16);
					using (MemoryStream ms = new MemoryStream())
					{
						using (CryptoStream cs = new CryptoStream(ms, encryptor.CreateEncryptor(), CryptoStreamMode.Write))
						{
							cs.Write(clearBytes, 0, clearBytes.Length);
							cs.Close();
						}
						clearText = Convert.ToBase64String(ms.ToArray());
					}
				}
				return clearText;
			}

			internal string Decrypt(string cipherText)
			{
				cipherText = cipherText.Replace(" ", "+");
				byte[] cipherBytes = Convert.FromBase64String(cipherText);
				using (Aes encryptor = Aes.Create())
				{
					Rfc2898DeriveBytes pdb = new Rfc2898DeriveBytes(ENCRYPTION_KEY, new byte[] { 0x49, 0x76, 0x61, 0x6e, 0x20, 0x4d, 0x65, 0x64, 0x76, 0x65, 0x64, 0x65, 0x76 });
					encryptor.Key = pdb.GetBytes(32);
					encryptor.IV = pdb.GetBytes(16);
					using (MemoryStream ms = new MemoryStream())
					{
						using (CryptoStream cs = new CryptoStream(ms, encryptor.CreateDecryptor(), CryptoStreamMode.Write))
						{
							cs.Write(cipherBytes, 0, cipherBytes.Length);
							cs.Close();
						}
						cipherText = Encoding.Unicode.GetString(ms.ToArray());
					}
				}
				return cipherText;
			}
		}

		internal void AddMessage(Subscriber fromSubscriber, string message)
		{
			var notification = new Notification(fromSubscriber, message);
			notification.CreationDate = DateTime.Now;
			this.messages.AddMessage(this, notification);
		}

		internal void RemoveMessage(string message)
		{
			this.messages.DeleteMessageByBody(this, message);
		}

		internal void RemoveMessages()
		{
			this.messages.RemoveMessages(this);
		}

		internal int CountMessages()
		{
			return this.messages.CountMessages(this);
		}

		internal int CountMessages(string fromAddress)
		{
			if (string.IsNullOrWhiteSpace(fromAddress)) throw new ArgumentNullException(nameof(fromAddress));
			return this.messages.CountMessages(fromAddress);
		}
	}

	internal class Avatar : Objeto
	{
		private enum ApprovalState
		{
			ToBeApproved,
			Approved,
			Denied
		}

		private ApprovalState approvalState;
		private DateTime lastUpdated;
		private string currentPath;
		private string lastPath;
		private Player owner;

		public Avatar(Player owner, string avatarPath, DateTime now)
		{
			bool isUrlValid = Uri.IsWellFormedUriString(avatarPath, UriKind.RelativeOrAbsolute);
			if (!isUrlValid) throw new GameEngineException("The url is not valid");

			this.approvalState = ApprovalState.Approved;
			this.lastUpdated = now;
			this.currentPath = avatarPath;
			this.owner = owner;
		}

		internal DateTime LastUpdated
		{
			get { return lastUpdated; }
		}

		internal string GameName
		{
			get
			{
				const string PREFIX = "/api/file/";
				int index = currentPath.IndexOf(PREFIX);
				if (index == -1) throw new GameEngineException("There is an inconsistency on avatar's file path");

				index = currentPath.IndexOf('/', index + PREFIX.Length);
				if (index == -1) throw new GameEngineException("There is an inconsistency on avatar's file path");

				string a = currentPath.Substring(index + 1);
				if (string.IsNullOrWhiteSpace(a)) throw new GameEngineException("There is an inconsistency on avatar's file path");
				string[] b = a.Split('/');

				if (b.Length > 0 && b[0] == "Lotto")
					return "Lotto";
				else if (b.Length > 0 && b[0] == "MarchMadness")
					return "MarchMadness";
				else if (b.Length > 0 && b[0] == "Exchange")
					return "Exchange";
				else if (b.Length > 0 && b[0] == "Lines")
					return "Lines";
				else if (b.Length > 0 && b[0] == "Fruits")
					return "Fruits";
				throw new GameEngineException("There is an inconsistency on avatar's file path");
			}
		}

		internal string Path
		{
			get { return IsApproved ? currentPath : lastPath; }
		}

		internal string PathToBeApproved
		{
			get { return currentPath; }
		}

		internal bool IsApproved
		{
			get { return approvalState == ApprovalState.Approved; }
		}

		internal bool IsDenied
		{
			get { return approvalState == ApprovalState.Denied; }
		}

		internal bool IsToBeApproved
		{
			get { return approvalState == ApprovalState.ToBeApproved; }
		}

		internal void Approve()
		{
			this.approvalState = ApprovalState.Approved;
		}

		internal void Deny()
		{
			this.approvalState = ApprovalState.Denied;
		}

		const string PREDEFINED_AND_AUTOAPPROVED_IMAGE_PATHS = "/public/images/avatar-";
		internal void Update(string avatarPath)
		{
			bool isUrlValid = Uri.IsWellFormedUriString(avatarPath, UriKind.RelativeOrAbsolute);
			if (!isUrlValid) throw new GameEngineException("The url is not valid");

			SaveLastPath();
			var isAutoApproved = avatarPath.Contains(PREDEFINED_AND_AUTOAPPROVED_IMAGE_PATHS);
			if (isAutoApproved)
			{
				Approve();
			}
			else
			{
				this.approvalState = ApprovalState.ToBeApproved;
			}

			currentPath = avatarPath;
			this.lastUpdated = DateTime.Now;
		}

		internal double UploadImageElapsedTimeInSeconds()
		{
			double result = 0;
			DateTime currentDate = DateTime.Now;
			result = currentDate.Subtract(this.lastUpdated).TotalSeconds;
			return result;
		}

		private void SaveLastPath()
		{
			if (approvalState == ApprovalState.Approved)
			{
				lastPath = currentPath;
			}
		}
	}


	public class ProfanityMessage : KafkaMessage
	{
		private string value;
		private ProfanityMessageType type;

		internal ProfanityMessage(ProfanityMessageType type, string value)
		{
			this.type = type;
			this.value = value;
		}

		public ProfanityMessage(string message) : base(message)
		{

		}

		public string Value
		{
			get
			{
				return this.value;
			}
		}
		public ProfanityMessageType MessageType
		{
			get
			{
				return this.type;
			}
		}
		protected override void InternalSerialize()
		{
            base.InternalSerialize();
            AddProperty((int)this.MessageType).AddProperty(this.Value);
		}

		protected override void Deserialize(string[] message, out int fieldOrder)
		{
			base.Deserialize(message, out fieldOrder);
			this.type = (ProfanityMessageType)int.Parse(message[fieldOrder++]);
			this.value = message[fieldOrder++];
		}

    }

	public class NickNameMessage : ProfanityMessage
	{
		private string accountNumber;

		internal NickNameMessage(ProfanityMessageType type, string accountNumber, string value) : base(type, value)
		{
			this.accountNumber = accountNumber;
		}

		public NickNameMessage(string serialized) : base(serialized)
		{

		}

		public string AccountNumber
		{
			get
			{
				return this.accountNumber;
			}
		}

		protected override void InternalSerialize()
		{
			base.InternalSerialize();
			AddProperty(this.AccountNumber);
		}

		protected override void Deserialize(string[] message, out int fieldOrder)
		{
			base.Deserialize(message, out fieldOrder);
			this.accountNumber = message[fieldOrder++];
		}
	}

	public class FavoriteNumberMessage : ProfanityMessage
	{
		private string playerId;
		private int favoriteNumberConsecutive;

		internal FavoriteNumberMessage(ProfanityMessageType type, string playerId, int favoriteNumberConsecutive, string listName) : base(type, listName)
		{
			this.playerId = playerId;
			this.favoriteNumberConsecutive = favoriteNumberConsecutive;
		}

		public FavoriteNumberMessage(string serialized) : base(serialized)
		{

		}

		public string PlayerId
		{
			get
			{
				return this.playerId;
			}
		}
		public int FavoriteNumberConsecutive
		{
			get
			{
				return this.favoriteNumberConsecutive;
			}
		}

		protected override void InternalSerialize()
		{
			base.InternalSerialize();
			AddProperty(this.PlayerId)
			.AddProperty(this.FavoriteNumberConsecutive);
		}

		protected override void Deserialize(string[] message, out int fieldOrder)
		{
			base.Deserialize(message, out fieldOrder);
			this.playerId = message[fieldOrder++];
			this.favoriteNumberConsecutive = int.Parse(message[fieldOrder++]);
		}
	}

	public class BracketNameMessage : ProfanityMessage
	{
		private int bracketId;
		private GameAbbreviation abbreviation;

		internal BracketNameMessage(ProfanityMessageType type, int bracketId, string value, GameAbbreviation abbreviation) : base(type, value)
		{
			this.bracketId = bracketId;
			this.abbreviation = abbreviation;
		}

		public BracketNameMessage(string serialized) : base(serialized)
		{

		}

		public int BracketId
		{
			get
			{
				return this.bracketId;
			}
		}

		public GameAbbreviation Abbreviation
		{
			get
			{
				return this.abbreviation;
			}
		}

		public static GameAbbreviation GetTheAbbreviation(string message)
		{
			string[] serializedMessage = KafkaMessage.Split(message);
			return (GameAbbreviation)Enum.Parse(typeof(GameAbbreviation), serializedMessage[3]);
		}

		protected override void InternalSerialize()
		{
			base.InternalSerialize();
			AddProperty(this.BracketId).
			AddProperty((int)this.Abbreviation);
		}

		protected override void Deserialize(string[] message, out int fieldOrder)
		{
			base.Deserialize(message, out fieldOrder);
			this.bracketId = int.Parse(message[fieldOrder++]);
		}
	}

	public class GroupNameMessage : ProfanityMessage
	{
		private int groupId;
		private GameAbbreviation abbreviation;
		internal GroupNameMessage(ProfanityMessageType type, int groupId, string value, GameAbbreviation abbreviation) : base(type, value)
		{
			this.groupId = groupId;
			this.abbreviation = abbreviation;
		}

		public GroupNameMessage(string serialized) : base(serialized)
		{

		}

		public int GroupId
		{
			get
			{
				return this.groupId;
			}
		}

		public GameAbbreviation Abbreviation
		{
			get
			{
				return this.abbreviation;
			}
		}

		public static GameAbbreviation GetTheAbbreviation(string message)
		{
			string[] serializedMessage = KafkaMessage.Split(message);
			return (GameAbbreviation)Enum.Parse(typeof(GameAbbreviation), serializedMessage[3]);
		}

		protected override void InternalSerialize()
		{
			base.InternalSerialize();
			AddProperty(this.GroupId).
			AddProperty((int)this.Abbreviation);
		}

		protected override void Deserialize(string[] message, out int fieldOrder)
		{
			base.Deserialize(message, out fieldOrder);
			this.groupId = int.Parse(message[fieldOrder++]);
		}
	}

	public class PoolNameMessage : ProfanityMessage
	{
		private int poolId;
		private GameAbbreviation abbreviation;

		internal PoolNameMessage(ProfanityMessageType type, int poolId, string value, GameAbbreviation abbreviation) : base(type, value)
		{
			this.poolId = poolId;
			this.abbreviation = abbreviation;
		}

		public PoolNameMessage(string serialized) : base(serialized)
		{

		}

		public int PoolId
		{
			get
			{
				return this.poolId;
			}
		}
		public GameAbbreviation Abbreviation
		{
			get
			{
				return this.abbreviation;
			}
		}

		public static GameAbbreviation GetTheAbbreviation(string message)
		{
			string[] serializedMessage = KafkaMessage.Split(message);
			return (GameAbbreviation)Enum.Parse(typeof(GameAbbreviation), serializedMessage[3]);
		}
		protected override void InternalSerialize()
		{
			base.InternalSerialize();
			AddProperty(this.PoolId).
			AddProperty((int)this.Abbreviation);
		}

		protected override void Deserialize(string[] message, out int fieldOrder)
		{
			base.Deserialize(message, out fieldOrder);
			this.poolId = int.Parse(message[fieldOrder++]);
		}
	}

	public enum ProfanityMessageType
	{
		NICKNAME = 0,
		FAVORITE_NUMBER_NAME = 1,
		BRACKET_NAME = 2,
		GROUP_NAME = 3,
		POOL_NAME = 4
	}
}
