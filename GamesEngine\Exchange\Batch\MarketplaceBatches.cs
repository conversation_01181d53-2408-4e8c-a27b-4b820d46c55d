﻿using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;

namespace GamesEngine.Exchange.Batch
{
	[Puppet]
	internal class MarketplaceBatches : Objeto
	{
		private readonly Marketplace marketplace;
		private List<MarketplaceBatch> readyBatches;
		private List<MarketplaceBatch> openBatches;
		private List<MarketplaceBatch> closeBatches;
		private int batchIdConsecutive = 100000;

		internal MarketplaceBatches(Marketplace marketplace)
		{
			this.marketplace = marketplace;
		}

		internal MarketplaceBatch AddNewBatch(DateTime creationDate, DateTime scheduledDate, int batchNumber, string employeeName, string description)
		{
			if (creationDate == default(DateTime)) throw new ArgumentNullException(nameof(creationDate));
			if (scheduledDate == default(DateTime)) throw new ArgumentNullException(nameof(scheduledDate));
			if (batchNumber <= 0) throw new GameEngineException($"Batch number {batchNumber} is invalid to define a Marketplace batch.");
			if(string.IsNullOrWhiteSpace(nameof(employeeName))) throw new ArgumentNullException(nameof(employeeName));
			if(string.IsNullOrWhiteSpace(nameof(description))) throw new ArgumentNullException(nameof(description));

			if (readyBatches == null) readyBatches = new List<MarketplaceBatch>();
			MarketplaceBatch newBatch = new MarketplaceBatch(this, marketplace, batchNumber, creationDate, scheduledDate, description, employeeName);
			readyBatches.Add(newBatch);

			return newBatch;
		}
		
		//Un batch pasa de Ready a Open cuando un usuario desde una pantalla presiona el boton Open
		internal void OpenMarketplaceBatch(MarketplaceBatch marketplaceBatch)
		{
			if (readyBatches == null) readyBatches = new List<MarketplaceBatch>();
			if (openBatches == null) openBatches = new List<MarketplaceBatch>();
			foreach (MarketplaceBatch batch in readyBatches.ToArray())
			{
				if (batch.BatchNumber == marketplaceBatch.BatchNumber)
				{
					openBatches.Add(batch);
					readyBatches.Remove(batch);
				}
			}
			if (readyBatches.Count == 0) readyBatches = null;
		}

		//Un batch pasa de Open a Close cuando un usuario desde una pantalla presiona el boton Close
		internal void CloseMarketplaceBatch(MarketplaceBatch marketplaceBatch)
		{
			if (openBatches == null) openBatches = new List<MarketplaceBatch>();
			foreach (MarketplaceBatch batch in openBatches.ToArray())
			{
				if (batch.BatchNumber == marketplaceBatch.BatchNumber)
				{
					closeBatches.Add(batch);
					openBatches.Remove(batch);
				}
			}
			if (openBatches.Count == 0) openBatches = null;
		}

		//Un batch pasa de Close a Done cuando un usuario desde una pantalla presiona el boton Confirm para confirmar que todos los movimientos coinciden
		internal void ConfirmClosingMarketplaceBatch(MarketplaceBatch marketplaceBatch)
		{
			foreach (MarketplaceBatch batch in closeBatches.ToArray())
			{
				if (batch.BatchNumber == marketplaceBatch.BatchNumber)
				{
					closeBatches.Remove(batch);
				}
			}
			if (closeBatches.Count == 0) openBatches = null;
			marketplace.PruneUnUsedRates();
		}

		internal int IdentityBatchNumber
		{
			get
			{
				batchIdConsecutive += 1;
				return batchIdConsecutive;
			}
			set
			{
				batchIdConsecutive = value;
			}
		}

		internal IEnumerable<MarketplaceBatch> BatchesReady(DateTime date)
		{
			List<MarketplaceBatch> result = new List<MarketplaceBatch>();

			if (readyBatches == null) readyBatches = new List<MarketplaceBatch>();
			foreach (MarketplaceBatch batch in readyBatches)
			{
				if (batch.ScheduledDate.Date == date.Date)
				{
					result.Add(batch);
				}
			}

			return result;
		}

		internal IEnumerable<MarketplaceBatch> BatchesOpen(DateTime date)
		{
			List<MarketplaceBatch> result = new List<MarketplaceBatch>();

			if (openBatches == null) openBatches = new List<MarketplaceBatch>();
			foreach (MarketplaceBatch batch in openBatches)
			{
				if (batch.ScheduledDate.Date == date.Date)
				{
					result.Add(batch);
				}
			}
			return result;
		}

		internal IEnumerable<MarketplaceBatch> BatchesClosed(DateTime date)
		{
			List<MarketplaceBatch> result = new List<MarketplaceBatch>();

			if (closeBatches == null) closeBatches = new List<MarketplaceBatch>();
			foreach (MarketplaceBatch batch in closeBatches)
			{
				if (batch.ScheduledDate.Date == date.Date)
				{
					result.Add(batch);
				}
			}
			return result;
		}

		internal IEnumerable<MarketplaceBatch> BatchesVerified(DateTime date)
		{
			List<MarketplaceBatch> result = new List<MarketplaceBatch>();

			//TODO ir a consultar a BD, cargar un objeto con la data y retornarlo para la pantalla

			return result;
		}

		internal MarketplaceBatch FindMarketplaceBatch(int batchNumber)
		{
			if (batchNumber <= 0) throw new GameEngineException($"A marketplace with number {batchNumber} does not exist");

			if (openBatches == null) openBatches = new List<MarketplaceBatch>();
			foreach (MarketplaceBatch batch in openBatches)
			{
				if (batch.BatchNumber == batchNumber)
				{
					return batch;
				}
			}

			if (readyBatches == null) readyBatches = new List<MarketplaceBatch>();
			foreach (MarketplaceBatch batch in readyBatches)
			{
				if (batch.BatchNumber == batchNumber)
				{
					return batch;
				}
			}

			if (closeBatches == null) closeBatches = new List<MarketplaceBatch>();
			foreach (MarketplaceBatch batch in closeBatches)
			{
				if (batch.BatchNumber == batchNumber)
				{
					return batch;
				}
			}
			throw new GameEngineException($"A marketplace with number {batchNumber} does not exist");
		}

		internal bool ExistsMarketplaceBatch(int batchNumber)
		{
			if (batchNumber <= 0) return false;

			if (openBatches == null) openBatches = new List<MarketplaceBatch>();
			foreach (MarketplaceBatch batch in openBatches)
			{
				if (batch.BatchNumber == batchNumber)
				{
					return true;
				}
			}

			if (readyBatches == null) readyBatches = new List<MarketplaceBatch>();
			foreach (MarketplaceBatch batch in readyBatches)
			{
				if (batch.BatchNumber == batchNumber)
				{
					return true;
				}
			}

			if (closeBatches == null) closeBatches = new List<MarketplaceBatch>();
			foreach (MarketplaceBatch batch in closeBatches)
			{
				if (batch.BatchNumber == batchNumber)
				{
					return true;
				}
			}
			return false;
		}

		internal BatchSet SearchAgentBatch(string fullName)
		{
			if (string.IsNullOrWhiteSpace(fullName)) throw new ArgumentNullException(nameof(fullName));

			string tail = "";
			string currentName = "";
			MarketplaceBatch.TailOFFullName(fullName, out currentName, out tail);
			bool itsATailToSearch = ! string.IsNullOrEmpty(tail);

			if (openBatches == null) openBatches = new List<MarketplaceBatch>();
			if (readyBatches == null) readyBatches = new List<MarketplaceBatch>();
			if (closeBatches == null) closeBatches = new List<MarketplaceBatch>();

			if (itsATailToSearch)
			{
				foreach (MarketplaceBatch batch in openBatches)
				{
					if (batch.FullName == currentName)
					{
						return batch.SearchBatchTransaction(tail);
					}
				}

				foreach (MarketplaceBatch batch in readyBatches)
				{
					if (batch.FullName == currentName)
					{
						return batch.SearchBatchTransaction(tail);
					}
				}

				foreach (MarketplaceBatch batch in closeBatches)
				{
					if (batch.FullName == currentName)
					{
						return batch.SearchBatchTransaction(tail);
					}
				}
			}
			else
			{
				foreach (MarketplaceBatch batch in openBatches)
				{
					if (batch.FullName == currentName)
					{
						return batch;
					}
				}

				foreach (MarketplaceBatch batch in readyBatches)
				{
					if (batch.FullName == currentName)
					{
						return batch;
					}
				}

				foreach (MarketplaceBatch batch in closeBatches)
				{
					if (batch.FullName == currentName)
					{
						return batch;
					}
				}
			}

			throw new GameEngineException($"Batch name {currentName} does not match with any marketplace batch registered");
		}

		internal BatchSet SearchAgentBatch(string fullName, int batchNumber)
		{
			if (string.IsNullOrWhiteSpace(fullName)) throw new ArgumentNullException(nameof(fullName));
			if (batchNumber <= 0) throw new GameEngineException($"Batch number {batchNumber} does not match with any marketplace batch registered");

			string tail = "";
			string currentName = "";
			MarketplaceBatch.TailOFFullName(fullName, out currentName, out tail);
			bool itsATailToSearch = !string.IsNullOrEmpty(tail);

			if (openBatches == null) openBatches = new List<MarketplaceBatch>();
			if (readyBatches == null) readyBatches = new List<MarketplaceBatch>();
			if (closeBatches == null) closeBatches = new List<MarketplaceBatch>();

			if (itsATailToSearch)
			{
				foreach (MarketplaceBatch batch in openBatches)
				{
					if (batch.FullName == currentName && batch.BatchNumber == batchNumber)
					{
						return batch.SearchBatchTransaction(tail);
					}
				}

				foreach (MarketplaceBatch batch in readyBatches)
				{
					if (batch.FullName == currentName && batch.BatchNumber == batchNumber)
					{
						return batch.SearchBatchTransaction(tail);
					}
				}

				foreach (MarketplaceBatch batch in closeBatches)
				{
					if (batch.FullName == currentName && batch.BatchNumber == batchNumber)
					{
						return batch.SearchBatchTransaction(tail);
					}
				}
			}
			else
			{
				foreach (MarketplaceBatch batch in openBatches)
				{
					if (batch.FullName == currentName && batch.BatchNumber == batchNumber)
					{
						return batch;
					}
				}

				foreach (MarketplaceBatch batch in readyBatches)
				{
					if (batch.FullName == currentName && batch.BatchNumber == batchNumber)
					{
						return batch;
					}
				}

				foreach (MarketplaceBatch batch in closeBatches)
				{
					if (batch.FullName == currentName && batch.BatchNumber == batchNumber)
					{
						return batch;
					}
				}
			}

			throw new GameEngineException($"Batch name {currentName} does not match with any marketplace batch registered");
		}

		internal bool ExistsAgentBatch(string fullName, int batchNumber)
		{
			if (string.IsNullOrWhiteSpace(fullName)) return false;
			if (batchNumber <= 0) return false;

			MarketplaceBatch.TailOFFullName(fullName, out string currentName, out string tail);
			bool itsATailToSearch = !string.IsNullOrWhiteSpace(tail);

			if (openBatches == null) openBatches = new List<MarketplaceBatch>();
			if (readyBatches == null) readyBatches = new List<MarketplaceBatch>();
			if (closeBatches == null) closeBatches = new List<MarketplaceBatch>();

			if (itsATailToSearch)
			{
				foreach (MarketplaceBatch batch in openBatches)
				{
					if (batch.FullName == currentName && batch.BatchNumber == batchNumber)
					{
						return true;
					}
				}

				foreach (MarketplaceBatch batch in readyBatches)
				{
					if (batch.FullName == currentName && batch.BatchNumber == batchNumber)
					{
						return true;
					}
				}

				foreach (MarketplaceBatch batch in closeBatches)
				{
					if (batch.FullName == currentName && batch.BatchNumber == batchNumber)
					{
						return true;
					}
				}
			}
			else
			{
				foreach (MarketplaceBatch batch in openBatches)
				{
					if (batch.FullName == currentName && batch.BatchNumber == batchNumber)
					{
						return true;
					}
				}

				foreach (MarketplaceBatch batch in readyBatches)
				{
					if (batch.FullName == currentName && batch.BatchNumber == batchNumber)
					{
						return true;
					}
				}

				foreach (MarketplaceBatch batch in closeBatches)
				{
					if (batch.FullName == currentName && batch.BatchNumber == batchNumber)
					{
						return true;
					}
				}
			}

			return false;
		}

		internal MarketplaceBatch FirstOpenBatch()
		{
			if (openBatches != null && openBatches.Count > 0) return openBatches[0];

			return null;
		}

		internal bool ExistAgentBatch(string fullName)
		{
			if (string.IsNullOrWhiteSpace(fullName)) throw new ArgumentNullException(nameof(fullName));

			string tail = "";
			string currentName = "";
			MarketplaceBatch.TailOFFullName(fullName, out currentName, out tail);
			bool itsATailToSearch = !string.IsNullOrEmpty(tail);

			if (openBatches == null) openBatches = new List<MarketplaceBatch>();
			if (readyBatches == null) readyBatches = new List<MarketplaceBatch>();
			if (closeBatches == null) closeBatches = new List<MarketplaceBatch>();

			if (itsATailToSearch)
			{
				foreach (MarketplaceBatch batch in openBatches)
				{
					if (batch.FullName == currentName)
					{
						return batch.ExistAgentBatch(tail);
					}
				}

				foreach (MarketplaceBatch batch in readyBatches)
				{
					if (batch.FullName == currentName)
					{
						return batch.ExistAgentBatch(tail);
					}
				}

				foreach (MarketplaceBatch batch in closeBatches)
				{
					if (batch.FullName == currentName)
					{
						return batch.ExistAgentBatch(tail);
					}
				}
			}
			else
			{
				foreach (MarketplaceBatch batch in openBatches)
				{
					if (batch.FullName == currentName)
					{
						return true;
					}
				}

				foreach (MarketplaceBatch batch in readyBatches)
				{
					if (batch.FullName == currentName)
					{
						return true;
					}
				}

				foreach (MarketplaceBatch batch in closeBatches)
				{
					if (batch.FullName == currentName)
					{
						return true;
					}
				}
			}
			return false;
		}

		internal IEnumerable<BatchSet> AllValidPaths()
		{
			List<BatchSet> result = new List<BatchSet>();

			if (openBatches == null) openBatches = new List<MarketplaceBatch>();
			foreach (MarketplaceBatch batch in openBatches)
			{
				result.AddRange(batch.AllValidPaths());
			}

			if (readyBatches == null) readyBatches = new List<MarketplaceBatch>();
			foreach (MarketplaceBatch batch in readyBatches)
			{
				result.AddRange(batch.AllValidPaths());
			}

			if (closeBatches == null) closeBatches = new List<MarketplaceBatch>();
			foreach (MarketplaceBatch batch in closeBatches)
			{
				result.AddRange(batch.AllValidPaths());
			}

			return result;
		}

		internal Marketplace Marketplace
		{
			get
			{
				return this.marketplace;
			}
		}
	}
}
