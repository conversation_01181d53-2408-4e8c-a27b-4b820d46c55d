﻿using GamesEngine.Custodian.Operations;
using GamesEngine.Finance;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;

namespace GamesEngine.Custodian
{
	public class Monitor
	{
		private static Monitor monitor;
		private List<MonitorListener> listeners;
		private Queue qt = new Queue();
		private Monitor()
		{ }


		public static Monitor GetInstance()
		{
			if (monitor == null)
			{
				monitor = new Monitor();
			}

			return monitor;
		}

		public void Resgister(MonitorListener listener)
		{
			if (listener == null) throw new ArgumentException(nameof(listener));
			if (listeners == null) listeners = new List<MonitorListener>();

			listeners.Add(listener);
		}

		public interface MonitorListener
		{
			void NewEvent(long id);
		}

		public abstract class MonitorEvent
		{
			public DateTime Date { get; }
			public long Id { get { return Date.Ticks;  } }

			public MonitorEvent(DateTime today)
			{
				this.Date = today;
			}
		}

		public sealed class PaymentRequest : MonitorEvent
		{
			public PaymentRequest(DateTime today, int disbursementId, string number, Currencies.CODES currencyCode, DisbursmentStatusCode status, decimal amount, DateTime scheduledDate, int transactionId)
				: base(today)
			{
				DisbursementId = disbursementId;
				Number = number;
				CurrencyCode = currencyCode;
				DisbursementStatus = status;
				Amount = amount;
				ScheduledDate = scheduledDate;
				TransactionId = transactionId;
			}

			public int DisbursementId { get; }
			public string Number { get; }
			public Currencies.CODES CurrencyCode { get; }
			internal DisbursmentStatusCode DisbursementStatus { get; }
			public decimal Amount { get; }
			public DateTime ScheduledDate { get; }
			public int TransactionId { get; }
		}

		//public sealed class PaymentResponse : MonitorEvent
		//{

		//}

		internal void WhenNewEvent(MonitorEvent monitorEvent)
		{
			if (listeners == null) return;

			qt.Enqueue(monitorEvent);

			foreach (MonitorListener listener in listeners)
			{
				listener.NewEvent(monitorEvent.Id);
			}
		}

		//internal aaaa Lis()
		//{
		//	foreach (Object obj in qt.)
		//	{
		//		Console.WriteLine(obj);
		//	}
		//}
	}
}
