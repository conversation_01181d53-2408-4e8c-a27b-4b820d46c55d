﻿using Puppeteer.EventSourcing.DB;
using Puppeteer.EventSourcing.Interprete;
using Puppeteer.EventSourcing.Interprete.Libraries;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Reflection;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Puppeteer.EventSourcing
{

	public abstract class Actor
	{
		private readonly TablaDeSimbolos tablaDeSimbolos;
		private readonly String persona;
		private Dairy eventStorage = null;
		private static Dictionary<string, Type> libraries;
		private string commandLineError;
		private FechaHora timeStamp;
		private static object myLock = new object();

		public string Name
		{
			get
			{
				return persona;
			}
		}
        public DateTime DateOfLastActivity
        {
            get
            {
				if (eventStorage == null) return DateTime.MinValue;
				return eventStorage.DateOfLastActivity;
            }
        }


        public Actor(String persona)
		{
			if (String.IsNullOrEmpty(persona)) throw new ArgumentNullException(nameof(persona));
			lock (myLock)
			{
				if (libraries == null)
				{
					Assembly assembly = this.GetType().Assembly;
					libraries = new Dictionary<string, Type>();
					foreach (var unaClase in LoadTypesFromLibrary(assembly))
					{
						if (isPuppet(unaClase) && unaClase.IsSubclassOf(typeof(Objeto)))
						{
							try
							{
								libraries.Add(unaClase.Name.ToLower(), unaClase);
							}
							catch
							{
								throw new LanguageException($"There are many puppets called {unaClase.Name}. Ambiguous references are not allowed.");
							}
						}
					}
				}
			}

			tablaDeSimbolos = new TablaDeSimbolos();
			tablaDeSimbolos.GuardarVariable("ItIsThePresent", Puppeteer.EventSourcing.Libraries.Boolean.True);
			this.persona = persona;
		}

		internal TablaDeSimbolos TablaDeSimbolos { get { return tablaDeSimbolos; } }

		private List<Type> LoadTypesFromLibrary(Assembly assembly)
		{
			List<Type> result = new List<Type>();
			foreach (Type t in assembly.GetTypes())
			{
				if (isPuppet(t) && t.IsSubclassOf(typeof(Objeto)))
				{
					result.Add(t);
				}
			}
			return result;
		}

		public ActorFollower CreateFollower(int followerId)
		{
			ActorFollower result = ActorFollower.CreateFollowerConActor(this, followerId);

			var culture = new CultureInfo("en-US");
			CultureInfo.DefaultThreadCurrentCulture = culture;
			CultureInfo.DefaultThreadCurrentUICulture = culture;

			return result;
		}

		public ActorFollower CreateFollowerSinActor(int followerId)
		{
			ActorFollower result = ActorFollower.CreateFollowerSinActor(this, followerId);

			var culture = new CultureInfo("en-US");
			CultureInfo.DefaultThreadCurrentCulture = culture;
			CultureInfo.DefaultThreadCurrentUICulture = culture;

			return result;
		}

		public string CommandLineError
		{
			get
			{
				return this.commandLineError;
			}
			set { commandLineError = value; }
		}

		public FechaHora CurrentTimeStamp
		{
			get
			{
				return this.timeStamp;
			}
		}

		private bool isPuppet(Type t)
		{
			System.Attribute[] attrs = System.Attribute.GetCustomAttributes(t);
			foreach (System.Attribute attr in attrs)
			{
				if (attr is Puppet) return true;
			}
			return false;
		}

		public void AppendFromStorage(DatabaseType dbType, string connection)
		{
			if (String.IsNullOrEmpty(connection)) throw new ArgumentNullException(nameof(connection));

			Console.WriteLine("Starting to Copy from Event Sourcing Storage");
			var sourceStorage = new Dairy(dbType, connection, persona);

			Salida salida = new Salida();
			salida.SinSalida();
			Parser parser = new Parser(libraries, tablaDeSimbolos, salida);

			rwLock.EnterWriteLock();
			try
			{
				tablaDeSimbolos.RecuperandoElEstado = true;
				tablaDeSimbolos.GuardarVariable("ItIsThePresent", Puppeteer.EventSourcing.Libraries.Boolean.False);

                currentTransition = ActorTransitions.Recovering;

                sourceStorage.AplicarEstadoEn(this, parser);

                currentTransition = ActorTransitions.Recovered;

            }
			finally
			{
				tablaDeSimbolos.RecuperandoElEstado = false;
				tablaDeSimbolos.GuardarVariable("ItIsThePresent", Puppeteer.EventSourcing.Libraries.Boolean.True);

				rwLock.ExitWriteLock();
			}
			Console.WriteLine("Actor State has been copied");
		}

		public void LevantarFollower(DatabaseType dbType, string connection, ActorFollower follower)
		{
			if (String.IsNullOrEmpty(connection)) throw new ArgumentNullException(nameof(connection));

			Console.WriteLine($"Starting {this.GetType()}'s follower");
			var sourceStorage = new Dairy(dbType, connection, persona);

			Salida salida = new Salida();
			salida.SinSalida();
			Parser parser = new Parser(libraries, tablaDeSimbolos, salida);

			rwLock.EnterWriteLock();
			try
			{
				tablaDeSimbolos.RecuperandoElEstado = true;
				tablaDeSimbolos.GuardarVariable("ItIsThePresent", Puppeteer.EventSourcing.Libraries.Boolean.False);

				sourceStorage.AplicarEstadoEnFollower(this, parser, follower);
			}
			finally
			{
				tablaDeSimbolos.RecuperandoElEstado = false;
				tablaDeSimbolos.GuardarVariable("ItIsThePresent", Puppeteer.EventSourcing.Libraries.Boolean.True);

				rwLock.ExitWriteLock();
			}
			Console.WriteLine($"{this.GetType()}'s follower has been finished");
		}

		public delegate void LeaderInitializationHandler();
		public LeaderInitializationHandler OnLeaderInitialization;

		public delegate void AfterRecoveringHandler(DatabaseType dbType, string connection, string persona, int lastRecoveredId);
		public AfterRecoveringHandler OnAfterRecovering;

		private const string PRODUCTION_DOES_NOT_NEED_IT = "PRODUCTION_DOES_NOT_NEED_IT";

		public IEnumerable<string> Variables()
		{
			return tablaDeSimbolos.Symbols;
		}

		public Objeto Value(string variableName)
		{
			return tablaDeSimbolos.Value(variableName);
		}

		public void EventSourcingStorage(DatabaseType dbType, string connection, string scriptBeforeRecovering, string needsUniqueIdentifierForPaymentHub)
		{
			if (String.IsNullOrEmpty(connection)) throw new ArgumentNullException(nameof(connection));

			Salida salida = new Salida();
			salida.SinSalida();
			Parser parser = new Parser(libraries, tablaDeSimbolos, salida);

			//bool qaAndStageNeedsToGenerateAnUniqueReferenceBecauseTheyUseSamePaymentHub = needsUniqueIdentifierForPaymentHub != PRODUCTION_DOES_NOT_NEED_IT;
			//if (!qaAndStageNeedsToGenerateAnUniqueReferenceBecauseTheyUseSamePaymentHub)
			//	Console.WriteLine($"Recovering state in Production mode...");
			//else
			//	Console.WriteLine("Recovering state in NOT Production mode...");

			rwLock.EnterWriteLock();

			eventStorage = new Dairy(dbType, connection, persona);

			try
			{
				tablaDeSimbolos.RecuperandoElEstado = true;
				tablaDeSimbolos.GuardarVariable("ItIsThePresent", Puppeteer.EventSourcing.Libraries.Boolean.False);

				currentTransition = ActorTransitions.Recovering;

                eventStorage.RecuperarEstado(this, parser);

                currentTransition = ActorTransitions.Recovered;
			}
			finally
			{
				tablaDeSimbolos.RecuperandoElEstado = false;
				tablaDeSimbolos.GuardarVariable("ItIsThePresent", Puppeteer.EventSourcing.Libraries.Boolean.True);

				rwLock.ExitWriteLock();
			}
			if (this.OnAfterRecovering != null) this.OnAfterRecovering(dbType, connection, persona, eventStorage.Id);
		}

		public async Task EventSourcingStorageAsync(DatabaseType dbType, string connection, string scriptBeforeRecovering, string needsUniqueIdentifierForPaymentHub)
		{
			if (String.IsNullOrEmpty(connection)) throw new ArgumentNullException(nameof(connection));

			eventStorage = new Dairy(dbType, connection, persona);

			Salida salida = new Salida();
			salida.SinSalida();
			Parser parser = new Parser(libraries, tablaDeSimbolos, salida);

			bool qaAndStageNeedsToGenerateAnUniqueReferenceBecauseTheyUseSamePaymentHub = needsUniqueIdentifierForPaymentHub != PRODUCTION_DOES_NOT_NEED_IT;
			if (!qaAndStageNeedsToGenerateAnUniqueReferenceBecauseTheyUseSamePaymentHub)
				Debug.WriteLine($"Recovering state in Production mode...");
			else
				Debug.WriteLine("Recovering state in NOT Production mode...");

			//rwLock.EnterWriteLock();
			try
			{
				tablaDeSimbolos.RecuperandoElEstado = true;
				tablaDeSimbolos.GuardarVariable("ItIsThePresent", Puppeteer.EventSourcing.Libraries.Boolean.False);

				await eventStorage.RecuperarEstadoAsync(this, parser);
			}
			finally
			{
				tablaDeSimbolos.RecuperandoElEstado = false;
				tablaDeSimbolos.GuardarVariable("ItIsThePresent", Puppeteer.EventSourcing.Libraries.Boolean.True);

				//rwLock.ExitWriteLock();
			}
			if (this.OnAfterRecovering != null) this.OnAfterRecovering(dbType, connection, persona, eventStorage.Id);
		}


		private ReaderWriterLockSlim rwLock = new ReaderWriterLockSlim();
		private static string scriptEnEjecucion = "";

		public string ScriptEnEjecucion
		{
			get
			{
				return scriptEnEjecucion;
			}
		}

		public bool ItsANewOne { get; internal set; }
		public int DairyId { get { return eventStorage.Id; } }

		public string PerformCmd(string script, IpAddress ip, UserInLog user)
		{
			if (script.Length > Lexer.MAX_TAMANO_DE_UN_LEXEMA) throw new LanguageException("Script exceeds the maximun length");
			string result = null;
			Salida salida = new Salida();
			Parser parser = new Parser(libraries, tablaDeSimbolos, salida);
			parser.EstablecerComando(script);
			Programa programa = parser.Procesar(isQuery: false, isCheck: false);
			programa.SetContextInfo();
			var now = DateTime.Now;
			programa.Now = new Libraries.FechaHora(now);
			programa.Ip = ip;
			programa.User = user;
			bool executionError = false;

			bool writeNewEntry = eventStorage != null;
			int nextId = -1;
			try
			{
				rwLock.EnterWriteLock();
				try
				{
					scriptEnEjecucion = script;
					executionError = true;
					if (writeNewEntry) nextId = eventStorage.TakeAndIncrementId();
					result = Perform(programa);
					executionError = false;
					string forDairy = programa.Write();
					if (writeNewEntry && !String.IsNullOrWhiteSpace(forDairy))
					{
						eventStorage.EscribirEnDairy(nextId, forDairy, programa.Ip, programa.User, now);
					}
				}
				catch (Exception executionException)
				{
					if (executionError)
					{
						string forDairy = programa.Write();
						if (writeNewEntry && !String.IsNullOrWhiteSpace(forDairy))
						{
							forDairy = Dairy.EXECUTION_ERROR_TAG + '\r' + forDairy;
							eventStorage.EscribirEnDairy(nextId, forDairy, programa.Ip, programa.User, now);
						}
					}
					if (executionError) throw executionException;
				}
				finally
				{
					rwLock.ExitWriteLock();
					commandLineError = programa.GetCommandErrorLine();
					timeStamp = programa.Now;
				}
			}
			catch (Exception e)
			{
				Debug.WriteLine($"PerformCmd {DateTime.Now} errorType:{e.GetType()} errorDescri:{e.Message} script:{script}");
				throw e;
			}

			return result;
		}

		public async Task<string> PerformCmdAsync(string script, IpAddress ip, UserInLog user)
		{
			if (script.Length > Lexer.MAX_TAMANO_DE_UN_LEXEMA) throw new LanguageException("Script exceeds the maximun length");
			string result = null;
			Salida salida = new Salida();
			Parser parser = new Parser(libraries, tablaDeSimbolos, salida);
			parser.EstablecerComando(script);
			Programa programa = parser.Procesar(isQuery: false, isCheck: false);
			programa.SetContextInfo();
			var now = DateTime.Now;
			programa.Now = new Libraries.FechaHora(now);
			programa.Ip = ip;
			programa.User = user;
			bool executionError = false;
			Exception executionException = null;

			bool writeNewEntry = eventStorage != null;
			int nextId = -1;
			rwLock.EnterWriteLock();
			try
			{
				scriptEnEjecucion = script;
				if (writeNewEntry) nextId = eventStorage.TakeAndIncrementId();
				result = Perform(programa);
			}
			catch (Exception e)
			{
				executionException = e;
				executionError = true;
			}
			finally
			{
				rwLock.ExitWriteLock();
				commandLineError = programa.GetCommandErrorLine();
				timeStamp = programa.Now;
			}

			string forDairy = programa.Write();
			if (writeNewEntry && !String.IsNullOrWhiteSpace(forDairy))
			{
				if (executionError)
				{
					forDairy = Dairy.EXECUTION_ERROR_TAG + '\r' + forDairy;
				}
				await eventStorage.EscribirEnDairyAsync(nextId, forDairy, programa.Ip, programa.User, now);
			}

			if (executionError) throw executionException;

			return result;
		}

		public string CompileQry(string script, IpAddress ip, UserInLog user)
		{
			StringBuilder resultado = new StringBuilder();
			Puppeteer.EventSourcing.Interprete.Parser parser = new Puppeteer.EventSourcing.Interprete.Parser(libraries, this.tablaDeSimbolos, new Salida());
			parser.EstablecerComando(script);
			Programa programa = parser.Procesar(false, false);
			DateTime now = DateTime.Now;
			programa.Now = new Puppeteer.EventSourcing.Libraries.FechaHora(now);
			programa.CalculateTypes();
			var anUUID = System.Guid.NewGuid();
			var uuid = anUUID.ToString().Replace('-', 'x');

			return resultado.Append($@"
						 (Func<Puppeteer.EventSourcing.Actor, Puppeteer.EventSourcing.IpAddress, Puppeteer.EventSourcing.UserInLog, System.Threading.ReaderWriterLockSlim, string>) ((actorParam, ipParam, userParam, rwLockParam) => {{
								Puppeteer.EventSourcing.Interprete.Salida salida{uuid} = new Puppeteer.EventSourcing.Interprete.Salida();
								bool escribiendo{uuid} = salida{uuid}.EstaEscribiendo;
								{programa.Compilar(uuid)}
						 }})").ToString();
		}

		public string DoQry(Func<Puppeteer.EventSourcing.Actor, Puppeteer.EventSourcing.IpAddress, Puppeteer.EventSourcing.UserInLog, System.Threading.ReaderWriterLockSlim, string> function)
		{
			string result = null;

			//rwLock.EnterReadLock();
			try
			{
				result = function(this, IpAddress.DEFAULT, UserInLog.ANONYMOUS, rwLock);
			}
			catch (Exception e)
			{
				Debug.WriteLine($"PerformCmd {DateTime.Now} errorType:{e.GetType()} errorDescri:{e.Message} script:{function.ToString()}");
				throw e;
			}
			//finally
			//{
			//	rwLock.ExitReadLock();
			//}

			return result;
		}


		public string PerformQry(string script, IpAddress ip, UserInLog user)
		{
			if (script.Length > Lexer.MAX_TAMANO_DE_UN_LEXEMA) throw new LanguageException("Script exceeds the maximun length");
			string result = null;
			Salida salida = new Salida();
			if (tablaDeSimbolos.RecuperandoElEstado) salida.SinSalida();
			Parser parser = new Parser(libraries, tablaDeSimbolos, salida);
			parser.EstablecerComando(script);
			Programa programa = parser.Procesar(isQuery: true, isCheck: false);
			programa.SetContextInfo();
			var now = DateTime.Now;
			programa.Now = new Libraries.FechaHora(now);
			programa.Ip = ip;
			programa.User = user;

			rwLock.EnterReadLock();
			try
			{
				result = Perform(programa);
			}
			catch (Exception e)
			{
				Debug.WriteLine($"PerformCmd {DateTime.Now} errorType:{e.GetType()} errorDescri:{e.Message} script:{script}");
				throw e;
			}
			finally
			{
				rwLock.ExitReadLock();
				commandLineError = programa.GetCommandErrorLine();
				timeStamp = programa.Now;
			}

			return result;
		}

		public string PerformChk(string script, IpAddress ip, UserInLog user)
		{
			if (script.Length > Lexer.MAX_TAMANO_DE_UN_LEXEMA) throw new LanguageException("Script exceeds the maximun length");
			string result = null;
			Salida salida = new Salida();
			if (tablaDeSimbolos.RecuperandoElEstado) salida.SinSalida();
			Parser parser = new Parser(libraries, tablaDeSimbolos, salida);
			parser.EstablecerComando(script);
			Programa programa = parser.Procesar(isQuery: false, isCheck: true);
			programa.SetContextInfo();
			var now = DateTime.Now;
			programa.Now = new Libraries.FechaHora(now);
			programa.Ip = ip;
			programa.User = user;

			rwLock.EnterReadLock();
			try
			{
				result = Perform(programa);
			}
			catch (Exception e)
			{
				Debug.WriteLine($"PerformChk {DateTime.Now} errorType:{e.GetType()} errorDescri:{e.Message} script:{script}");
				throw e;
			}
			finally
			{
				rwLock.ExitReadLock();
				commandLineError = programa.GetCommandErrorLine();
				timeStamp = programa.Now;
			}

			return result;
		}

		internal string Perform(Programa programa)
		{
			if (programa == null) throw new ArgumentNullException(nameof(programa));
			tablaDeSimbolos.GuardarVariableConCache("Now", programa.Now);
			tablaDeSimbolos.GuardarVariableConCache("Ip", programa.Ip);
			string resultado = programa.Ejecutar();

			return resultado == "{}" || resultado == null ? "" : resultado;
		}

		public string ComandForDairy(String script, IpAddress ip, UserInLog user)
		{
			Salida salida = new Salida();
			salida.SinSalida();
			Parser parser = new Parser(libraries, tablaDeSimbolos, salida);
			parser.EstablecerComando(script);
			Programa programa = parser.Procesar(isQuery: false, isCheck: false);
			String forDairy = programa.Write();
			return forDairy;
		}

		protected int AplicarEstadoEnRedBlack(int lastRecoveredId)
		{
			Salida salida = new Salida();
			Parser parser = new Parser(libraries, tablaDeSimbolos, salida);

			return eventStorage.AplicarEstadoEnRedBlack(this, parser, lastRecoveredId);
		}

		public void ChangePrimaryKey()
		{
			if (eventStorage == null) throw new Exception("Repository its no configured yet.");

			eventStorage.ChangePrimaryKey();
		}

		private enum ActorTransitions { Recovering, Recovered, Lock, Alive }

		private bool itsRecoverStatusRunning = false;

		private ActorTransitions currentTransition;

        //TOME EL CONTROL Y EJECUTAR LOS ULTIMOS COMANDOS SI HAY--DEBAJO DE LA LINEA VERDE
        public string LockWhileNotSyncronized()
		{
			if (itsRecoverStatusRunning) return $"The follower it's already in {currentTransition} status";
            if (currentTransition == ActorTransitions.Recovering) return $"Invalid transition from {currentTransition} to {ActorTransitions.Recovering}";
            if (currentTransition == ActorTransitions.Lock) return $"Invalid transition from {currentTransition} to {ActorTransitions.Recovering}";

            bool alreadyBlocked = false;

            itsRecoverStatusRunning = true;
			_ = Task.Run(() => {

                rwLock.EnterWriteLock();

                try
				{
					alreadyBlocked = true;
					int lastIdAfterRecoveredState = 0;
					int previousLastIdAfterRecoveredState = 0;

					bool salir = false;
					int reintentos = 0;
                    //while (itsFollowerRunning) && lastIdAfterRecoveredState == al lastIdAfterRecoveredState anterior
                    while (!salir)
					{
						previousLastIdAfterRecoveredState = lastIdAfterRecoveredState;
						lastIdAfterRecoveredState = AplicarEstadoEnRedBlack(eventStorage.Id);

                        Debug.WriteLine("Last ID: " + lastIdAfterRecoveredState);
						Thread.Sleep(TimeSpan.FromSeconds(0.5));

						if (lastIdAfterRecoveredState != previousLastIdAfterRecoveredState)
							reintentos = 0;
						else
							reintentos++;

						bool seAlcanzaron = reintentos >= 3;

                        salir = itsRecoverStatusRunning == false && seAlcanzaron;
                    }

					currentTransition = ActorTransitions.Alive;
				}
				finally
				{
					rwLock.ExitWriteLock();
				}
            });

			while (!alreadyBlocked) ;

			return "Revover status is running";
		}
		public void UnlockAndRunAlive()
		{
			if (!itsRecoverStatusRunning) throw new Exception("The follower it's already stopped.");
            if (currentTransition == ActorTransitions.Recovering) throw new Exception($"Invalid transition from {currentTransition} to {ActorTransitions.Recovering}");
            if (currentTransition == ActorTransitions.Alive) throw new Exception($"Invalid transition from {currentTransition} to {ActorTransitions.Alive}");

            itsRecoverStatusRunning = false;
		}

        public string PerformTrim(DateTime trimmed)
        {
            try
            {
                rwLock.EnterWriteLock();
                try
                {
                    eventStorage.EjecutarTrim(trimmed);
                }
                finally
                {
                    rwLock.ExitWriteLock();
                }
            }
            catch (Exception e)
            {
                Debug.WriteLine($"PerformTrim {DateTime.Now} errorType:{e.GetType()} errorDescri:{e.Message}");
                throw e;
            }

            string result = $"Trimmed = {new FechaHora(trimmed)};";

            return result;
        }

        public MemoryStream PerformArchive(DateTime startDate, DateTime endDate)
        {
            MemoryStream compressedInserts;
            try
            {
                try
                {
                    compressedInserts = eventStorage.EjecutarArchive(startDate, endDate);
                }
                finally
                {

                }
            }
            catch (Exception e)
            {
                Debug.WriteLine($"PerformArchived {DateTime.Now} errorType:{e.GetType()} errorDescription:{e.Message}");
                throw e;
            }

            return compressedInserts;
        }

        public static IEnumerable<string> PerformListActorsToLoad(string dbType, string connectionString, double porcentaMinimoDeAporte)
        {
            if (porcentaMinimoDeAporte < 0 && porcentaMinimoDeAporte > 100) throw new ArgumentException(nameof(porcentaMinimoDeAporte));

            IEnumerable<string> result = null;
            try
            {
                result = Dairy.ListarActoresACargar(dbType, connectionString, porcentaMinimoDeAporte);
            }
            catch (Exception e)
            {
                Debug.WriteLine($"PerformListActorsToLoad {DateTime.Now} errorType:{e.GetType()} errorDescri:{e.Message}");
                throw e;
            }

            return result;
        }

    }
}