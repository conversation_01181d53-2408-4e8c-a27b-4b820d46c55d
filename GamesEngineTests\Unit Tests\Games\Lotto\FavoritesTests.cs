﻿using GamesEngine.Business;
using GamesEngine.Gameboards.Lotto;
using GamesEngine.Games.Lotto;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamesEngineTests.Unit_Tests.Games.Lotto
{
    [TestClass]
    public class FavoritesTests
    {
        [TestMethod]
        public void CreateFavoriteNumbers()
        {
            CurrenciesTest.AddCurrencies();
            Company company = new Company();
            var customer = company.GetOrCreateCustomerById("Test cust");
            var favorites = customer.Player.FavoritesCatalog();

            favorites.CreateFavoriteNumbers("Test Fav", "fav.png");
            var favoritesNumbers = favorites.GetAll;
            Assert.AreEqual(2, favoritesNumbers.Count());
        }

        [TestMethod]
        public void AddFavorites()
        {
            CurrenciesTest.AddCurrencies();
            Company company = new Company();
            var customer = company.GetOrCreateCustomerById("Test cust");
            var favorites = customer.Player.FavoritesCatalog();

            var favorite = favorites.CreateFavoriteNumbers("Test Fav", "fav.png");
            favorite.Add(new List<string> { TicketType.P3S.ToString(), TicketType.P3B.ToString() }, new List<string> { "123", "345" });
            favorite.Add(new List<string> { TicketType.P4S.ToString(), TicketType.P4B.ToString() }, new List<string> { "1234", "3456" });
            var favoriteNumbers = favorite.GetAll;
            Assert.AreEqual(4, favoriteNumbers.Count());
        }

        [TestMethod]
        public void NewNumbersToAdd_bug8768()
        {
            CurrenciesTest.AddCurrencies();
            Company company = new Company();
            var customer = company.GetOrCreateCustomerById("Test cust");
            var favorites = customer.Player.FavoritesCatalog();

            var favorite = (RecentsNumbers)favorites.GetFavoriteNumbers("Recents");
            var newNumbers = favorite.NewNumbersToAdd(new List<string> { TicketType.P4S.ToString(), TicketType.P4B.ToString(), TicketType.P4B.ToString() }, new List<string> { "2345", "4567", "4567" });
            Assert.AreEqual("P4S,2345,P4B,4567", newNumbers);
        }

        [TestMethod]
        public void ExistingNumbersToUpdate()
        {
            CurrenciesTest.AddCurrencies();
            Company company = new Company();
            var customer = company.GetOrCreateCustomerById("Test cust");
            var favorites = customer.Player.FavoritesCatalog();

            var favorite = (RecentsNumbers)favorites.GetFavoriteNumbers("Recents");
            favorite.Add(new List<string> { TicketType.P3S.ToString(), TicketType.P3B.ToString() }, new List<string> { "123", "345" }, DateTime.Now);
            favorite.Add(new List<string> { TicketType.P4S.ToString(), TicketType.P4B.ToString() }, new List<string> { "1234", "3456" }, DateTime.Now);

            var existingNumbers = favorite.ExistingNumbersToUpdate(new List<string> { TicketType.P3S.ToString(), TicketType.P3B.ToString() }, new List<string> { "123", "345" });
            Assert.AreEqual("P3S,123,P3B,345", existingNumbers);
            existingNumbers = favorite.ExistingNumbersToUpdate(new List<string> { TicketType.P3S.ToString(), TicketType.P3S.ToString() }, new List<string> { "123", "345" });
            Assert.AreEqual("P3S,123", existingNumbers);
            existingNumbers = favorite.ExistingNumbersToUpdate(new List<string> { TicketType.P3S.ToString(), TicketType.P4B.ToString() }, new List<string> { "123", "2345" });
            Assert.AreEqual("P3S,123", existingNumbers);
            existingNumbers = favorite.ExistingNumbersToUpdate(new List<string> { TicketType.P3S.ToString(), TicketType.P3S.ToString() }, new List<string> { "123", "123" });
            Assert.AreEqual("P3S,123", existingNumbers);

            favorite.Update(existingNumbers, DateTime.Now);
            var favoriteNumbers = favorite.GetAll;
            Assert.AreEqual(4, favoriteNumbers.Count());
        }

        [TestMethod]
        public void Update()
        {
            CurrenciesTest.AddCurrencies();
            Company company = new Company();
            var customer = company.GetOrCreateCustomerById("Test cust");
            var favorites = customer.Player.FavoritesCatalog();

            var favorite = (RecentsNumbers)favorites.GetFavoriteNumbers("Recents");
            favorite.Add(new List<string> { TicketType.P3S.ToString(), TicketType.P3B.ToString() }, new List<string> { "123", "345" }, DateTime.Now);
            favorite.Add(new List<string> { TicketType.P4S.ToString(), TicketType.P4B.ToString() }, new List<string> { "1234", "3456" }, DateTime.Now);

            var existingNumbers = favorite.ExistingNumbersToUpdate(new List<string> { TicketType.P3S.ToString(), TicketType.P3B.ToString() }, new List<string> { "123", "345" });
            Assert.AreEqual("P3S,123,P3B,345", existingNumbers);

            var date = new DateTime(2024, 1, 1);
            favorite.Update(existingNumbers, date);
            var distinctNumbers = favorite.DistinctNumbersByPick(3);
            foreach (var number in distinctNumbers)
            {
                Assert.AreEqual(date, number.Date);
            }
        }

        [TestMethod]
        public void AddOrUpdate()
        {
            CurrenciesTest.AddCurrencies();
            Company company = new Company();
            var customer = company.GetOrCreateCustomerById("Test cust");
            var favorites = customer.Player.FavoritesCatalog();

            var favorite = (RecentsNumbers)favorites.GetFavoriteNumbers("Recents");
            favorite.AddOrUpdate(TicketType.P3S, "123", DateTime.Now);
            favorite.AddOrUpdate(TicketType.P4S, "1234", DateTime.Now);
            favorite.AddOrUpdate(TicketType.P3S, "123", DateTime.Now);
            favorite.AddOrUpdate(TicketType.P4S, "1234", DateTime.Now);

            var favoriteNumbers = favorite.GetAll;
            Assert.AreEqual(2, favoriteNumbers.Count());
        }
            
        [TestMethod]
        public void Activate_Deactivate()
        {
            CurrenciesTest.AddCurrencies();
            Company company = new Company();
            var customer = company.GetOrCreateCustomerById("Test cust");
            var favorites = customer.Player.FavoritesCatalog();

            var favorite = favorites.CreateFavoriteNumbers("Test Fav", "fav.png");
            favorite.Add(new List<string> { TicketType.P3S.ToString(), TicketType.P3B.ToString() }, new List<string> { "123", "345" });
            favorite.Add(new List<string> { TicketType.P4S.ToString(), TicketType.P4B.ToString() }, new List<string> { "1234", "3456" });
            var favoriteNumbers = favorite.GetAll;
            Assert.AreEqual(4, favoriteNumbers.Count());
            Assert.AreEqual(4, favoriteNumbers.Count(f => f.Enabled));

            favorite.Deactivate(TicketType.P3S, "123");
            favorite.Deactivate(TicketType.P4B, "3456");
            Assert.AreEqual(4, favoriteNumbers.Count());
            Assert.AreEqual(2, favoriteNumbers.Count(f => f.Enabled));

            favorite.Activate(TicketType.P4B, "3456");
            Assert.AreEqual(4, favoriteNumbers.Count());
            Assert.AreEqual(3, favoriteNumbers.Count(f => f.Enabled));

            var enabledNumbers = favorite.EnabledNumbers;
            Assert.AreEqual(3, enabledNumbers.Count());
        }

        [TestMethod]
        public void RemoveFavoriteNumbers()
        {
            CurrenciesTest.AddCurrencies();
            Company company = new Company();
            var customer = company.GetOrCreateCustomerById("Test cust");
            var favorites = customer.Player.FavoritesCatalog();

            favorites.CreateFavoriteNumbers("Test Fav", "fav.png");
            var favoritesNumbers = favorites.GetAll;
            Assert.AreEqual(2, favoritesNumbers.Count());

            favorites.RemoveFavoriteNumbers("Test Fav");
            Assert.AreEqual(1, favoritesNumbers.Count());
        }

        [TestMethod]
        public void RemoveFavoriteNumber()
        {
            CurrenciesTest.AddCurrencies();
            Company company = new Company();
            var customer = company.GetOrCreateCustomerById("Test cust");
            var favorites = customer.Player.FavoritesCatalog();

            var favorite = favorites.CreateFavoriteNumbers("Test Fav", "fav.png");
            favorite.Add(new List<string> { TicketType.P3S.ToString(), TicketType.P3B.ToString() }, new List<string> { "123", "345" });
            favorite.Add(new List<string> { TicketType.P4S.ToString(), TicketType.P4B.ToString() }, new List<string> { "1234", "3456" });
            var favoritesNumbers = favorite.GetAll;
            Assert.AreEqual(4, favoritesNumbers.Count());

            favorite.Remove(TicketType.P3S, "123");
            favoritesNumbers = favorite.GetAll;
            Assert.AreEqual(3, favoritesNumbers.Count());
        }

        [TestMethod]
        public void DistinctNumbersByPick()
        {
            CurrenciesTest.AddCurrencies();
            Company company = new Company();
            var customer = company.GetOrCreateCustomerById("Test cust");
            var favorites = customer.Player.FavoritesCatalog();

            var favorite = (RecentsNumbers)favorites.GetFavoriteNumbers("Recents");
            favorite.Add(new List<string> { TicketType.P3S.ToString(), TicketType.P3B.ToString(), TicketType.P3B.ToString() }, new List<string> { "123", "123", "345" }, DateTime.Now);
            favorite.Add(new List<string> { TicketType.P4S.ToString(), TicketType.P4B.ToString() }, new List<string> { "1234", "3456" }, DateTime.Now);
            var distinctNumbers = favorite.DistinctNumbersByPick(3);
            Assert.AreEqual(2, distinctNumbers.Count());
        }

        [TestMethod]
        public void DistinctNumbersByPick2()
        {
            CurrenciesTest.AddCurrencies();
            Company company = new Company();
            var customer = company.GetOrCreateCustomerById("Test cust");
            var favorites = customer.Player.FavoritesCatalog();

            var favorite = (RecentsNumbers)favorites.GetFavoriteNumbers("Recents");
            var date1 = new DateTime(2024, 1, 1);
            favorite.Add(new List<string> { TicketType.P3B.ToString() }, new List<string> { "123" }, date1);
            var date2 = new DateTime(2024, 1, 2);
            favorite.Add(new List<string> { TicketType.P3B.ToString() }, new List<string> { "124" }, date2);

            var distinctNumbers = favorite.DistinctNumbersByPick(3);
            Assert.AreEqual(2, distinctNumbers.Count());
            Assert.AreEqual(date2, distinctNumbers.First().Date);
        }

        [TestMethod]
        public void IconPath()
        {
            CurrenciesTest.AddCurrencies();
            Company company = new Company();
            var customer = company.GetOrCreateCustomerById("Test cust");
            var favorites = customer.Player.FavoritesCatalog();

            var favorite = favorites.CreateFavoriteNumbers("Test Fav", "fav.png");
            Assert.AreEqual("fav.png", favorite.IconPath);
            favorite.IconPath = "new.png";
            Assert.AreEqual("new.png", favorite.IconPath);
        }

        [TestMethod]
        public void Name()
        {
            CurrenciesTest.AddCurrencies();
            Company company = new Company();
            var customer = company.GetOrCreateCustomerById("Test cust");
            var favorites = customer.Player.FavoritesCatalog();

            var favorite = favorites.CreateFavoriteNumbers("Test Fav", "fav.png");
            Assert.AreEqual("Test Fav", favorite.Name);
            favorite.Name = "Test1";
            Assert.AreEqual("Test1", favorite.Name);
        }

        [TestMethod]
        public void MoveFavoriteNumber()
        {
            CurrenciesTest.AddCurrencies();
            Company company = new Company();
            var customer = company.GetOrCreateCustomerById("Test cust");
            var favorites = customer.Player.FavoritesCatalog();

            var favorite = favorites.CreateFavoriteNumbers("Test Fav", "fav.png");
            favorite.Add(new List<string> { TicketType.P3S.ToString(), TicketType.P3B.ToString() }, new List<string> { "123", "345" });
            favorite.Add(new List<string> { TicketType.P4S.ToString(), TicketType.P4B.ToString() }, new List<string> { "1234", "3456" });

            var favorite2 = favorites.CreateFavoriteNumbers("Test Fav2", "fav2.png");
            favorite2.Add(new List<string> { TicketType.P3S.ToString(), TicketType.P3B.ToString() }, new List<string> { "012", "234" });
            favorite2.Add(new List<string> { TicketType.P4S.ToString(), TicketType.P4B.ToString() }, new List<string> { "0123", "2345" });

            var favoriteNumbers = favorite.GetAll;
            var favorite2Numbers = favorite2.GetAll;
            Assert.AreEqual(4, favoriteNumbers.Count());
            Assert.AreEqual(4, favorite2Numbers.Count());

            favorites.MoveFavoriteNumber("Test Fav", "Test Fav2", TicketType.P3S, "123");
            favoriteNumbers = favorite.GetAll;
            favorite2Numbers = favorite2.GetAll;
            Assert.AreEqual(3, favoriteNumbers.Count());
            Assert.AreEqual(5, favorite2Numbers.Count());
        }
    }
}
