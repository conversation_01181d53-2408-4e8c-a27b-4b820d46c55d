﻿using GamesEngine.Finance;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using static GamesEngine.Exchange.BalanceMessage;

namespace GamesEngine.Exchange.Batch
{
	internal class BatchAccounts : Objeto
	{
		private Dictionary<Coin, BatchAccount> accounts;

		internal BatchAccounts(int batchNumber)
		{
			if (batchNumber < 0) throw new GameEngineException($"{nameof(batchNumber)} must be greater than 0");

			accounts = new Dictionary<Coin, BatchAccount>();
			BatchNumber = batchNumber;
		}

		internal BatchAccount FindAccountByCurrency(Coin coin)
		{
			if (coin == null) throw new ArgumentNullException(nameof(coin));

			BatchAccount result;
			if (!accounts.TryGetValue(coin, out result))
			{
				result = new BatchAccount(this, coin);
				accounts.Add(coin, result);
			}
			return result;
		}

		internal IEnumerable<BatchAccount> ListAccounts()
		{
			return accounts.Values.ToArray();
		}

		internal List<BatchBalancesMessage> CreateMessage()
		{
			List<BatchBalancesMessage> result = new List<BatchBalancesMessage>();
			foreach (BatchAccount account in ListAccounts())
			{
				result.Add(account.CreateMessage());
			}

			return result;
		}

		internal bool HasInitialAmountInAnyCurrency
		{
			get
			{
				var result = false;
				foreach(var account in accounts.Values)
				{
					result = account.HasInitialAmount;
					if (result) break;
				}

				return result;
			}
		}

		internal bool HasAvailableAmountInAnyCurrency
		{
			get
			{
				var result = false;
				foreach(var account in accounts.Values)
				{
					result = account.HasAvailableAmount;
					if (result) break;
				}

				return result;
			}
		}

		public int BatchNumber { get; }
	}

	[Puppet]
	internal class BatchAccount : Objeto
	{
		private BatchAccounts batchAccounts;
		private readonly Currency initial;
		private readonly Currency available;
		private readonly Currency locked;
		private readonly Currency lockedAccumulated;
		private readonly Currency spendAccumulated;
		private Coin coin;
		

		public BatchAccount(BatchAccounts batchAccounts, Coin coin)
		{
			if (batchAccounts == null) throw new ArgumentNullException(nameof(batchAccounts));
			if (coin == null) throw new ArgumentNullException(nameof(coin));

			this.batchAccounts = batchAccounts;
			this.coin = coin;

			initial = Currency.Factory(this.coin.Iso4217Code, 0);
			available = Currency.Factory(this.coin.Iso4217Code, 0);
			locked = Currency.Factory(this.coin.Iso4217Code, 0);
			lockedAccumulated = Currency.Factory(this.coin.Iso4217Code, 0);
			spendAccumulated = Currency.Factory(this.coin.Iso4217Code, 0);
		}

		internal void InitialAmount(Currency amount)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));
			if (amount.Value <= 0) throw new GameEngineException($"Amount must be bigger than 0.");
			if (coin.Iso4217Code != amount.CurrencyCode) throw new GameEngineException($"An account in {coin} can not process transaction in {amount.CurrencyCode}");

			initial.Add(amount);
		}

		internal void DebitInitialAmount(Currency amount)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));
			if (amount.Value <= 0) throw new GameEngineException($"Amount must be bigger than 0.");
			if (coin.Iso4217Code != amount.CurrencyCode) throw new GameEngineException($"An account in {coin} can not process transaction in {amount.CurrencyCode}");

			initial.Subtract(amount);
		}

		internal void Credit(Currency amount)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));
			if (amount.Value <= 0) throw new GameEngineException($"Amount must be bigger than 0.");
			if (coin.Iso4217Code != amount.CurrencyCode) throw new GameEngineException($"An account in {coin} can not process transaction in {amount.CurrencyCode}");

			available.Add(amount);
		}

		internal void Debit(Currency amount)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));
			if (amount.Value <= 0) throw new GameEngineException($"Amount must be bigger than 0.");
			if (coin.Iso4217Code != amount.CurrencyCode) throw new GameEngineException($"An account in {coin} can not process transaction in {amount.CurrencyCode}");

			available.Subtract(amount);
		}

		internal void Lock(Currency amount)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));
			if (amount.Value < 0) throw new GameEngineException($"Amount must be bigger than 0.");
			//if (amount.Value > available.Value) throw new GameEngineException($"There is no enough available in {amount.GetType()}");
			if (coin.Iso4217Code != amount.CurrencyCode) throw new GameEngineException($"An account in {coin} can not process transaction in {amount.CurrencyCode}");

			available.Subtract(amount);
			locked.Add(amount);
		}

		internal void Unlock(Currency amount)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));
			if (amount.Value < 0) throw new GameEngineException($"Amount must be bigger than 0.");
			if (amount.Value > locked.Value) throw new GameEngineException($"There is no enough available in {amount.GetType()}");
			if (locked.CurrencyCode != amount.CurrencyCode) throw new GameEngineException($"An account in {locked.CurrencyCode} can not process transaction in {amount.CurrencyCode}");

			locked.Subtract(amount);
		}

		internal bool HasInitialAmount
		{
			get
			{
				var result = this.initial.Value > 0;
				return result;
			}
		}

		internal bool HasAvailableAmount
		{
			get
			{
				var result = this.available.Value > 0;
				return result;
			}
		}

		internal Currency Initial
		{
			get
			{
				return this.initial;
			}

		}

		internal Currency Available
		{
			get
			{
				return this.available;
			}

		}

		internal Currency Locked
		{
			get
			{
				return this.locked;
			}

		}

		internal Currency LockedAccumulated
		{
			get
			{
				return this.lockedAccumulated;
			}

		}

		internal Currency SpendAccumulated
		{
			get
			{
				return this.spendAccumulated;
			}

		}

		internal string CurrencyCodeAsText
		{
			get
			{
				return coin.Iso4217Code;
			}
		}

		internal BatchAccount AddInLockAccumulated(Currency amount)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));

			lockedAccumulated.Add(amount);
			return this;
		}

		internal BatchAccount SubtractFromLockAccumulated(Currency amount)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));

			lockedAccumulated.Subtract(amount);
			return this;
		}

		internal BatchAccount AddInSpendAcculumated(Currency amount)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));

			spendAccumulated.Add(amount);
			return this;
		}

		internal BatchAccount SubtractFromSpendAcculumated(Currency amount)
		{
			if (amount == null) throw new ArgumentNullException(nameof(amount));

			spendAccumulated.Subtract(amount);
			return this;
		}

		internal BatchBalancesMessage CreateMessage()
		{
			return new BatchBalancesMessage()
			{
				BatchNumber = batchAccounts.BatchNumber,
				CurrencyId = coin.Id,
				Initial = initial.Value,
				Available = available.Value,
				Locked = locked.Value,
				LockedAccumulated = lockedAccumulated.Value,
				SpendAccumulated = spendAccumulated.Value,
			};
		}
	}
}
