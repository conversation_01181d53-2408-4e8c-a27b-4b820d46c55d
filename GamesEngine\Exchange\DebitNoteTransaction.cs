﻿using GamesEngine.Accounting.JournalTemplates;
using GamesEngine.Finance;
using GamesEngine.Settings;
using System;
using System.Collections.Generic;
using System.Text;
using town.connectors.drivers;

namespace GamesEngine.Exchange
{
    internal sealed class DebitNoteTransaction:Transaction
    {
        internal const int TEMPLATE_ID = 4;

        internal DebitNoteTransaction(TransactionDefinition transactionDefinition, IConversionSpread conversionSpread, string employeeName, Currency amount, DateTime now)
            : base(transactionDefinition, conversionSpread, amount, now, TransactionType.DebitNote)
        {
            transactionDefinition.Batch.AddTransactionDenifition(this);
        }

        public static JournalEntryTemplate Template { get; private set; }
        internal static void InitializeTemplate(JournalEntryTemplates templates)
        {
            Template = templates.CreateTemplate(DebitNoteTransaction.TEMPLATE_ID, "Template for " + nameof(DebitNoteTransaction));
            var parameters = Template.Parameters;

            parameters.AddTextParameter("TransactionId");
            parameters.AddTextParameter("Date");
            parameters.AddTextParameter("CustomerNumber");
            parameters.AddTextParameter("AccountNumber");
            parameters.AddTextParameter("Currency");
            parameters.AddAmountParameter("SaleRate");
            parameters.AddAmountParameter("PurchaseRate");
            parameters.AddAmountParameter("TotalAmount");

            Template.AddDebit("100-01", "Dummy Debit Note Transaction {TransactionId}", "TotalAmount");
            Template.AddCredit("200-01", "Dummy Debit Note Transaction {TransactionId}", "TotalAmount");
        }

        protected override void AfterApprove(DateTime date, bool itsThePresent, Currency gross, Currency comission, Currency profit, Currency net, Currency amountToCustomer, string employeeName, TransactionCompleted transactionCompleted, int journalEntryNumber)
        {
            if (!TransactionDefinition.Batch.HasAvailable(amountToCustomer)) throw new GameEngineException($"It's not possible to create a credit note, because there's no exist funds in {amountToCustomer.CurrencyCode}");
            if (!itsThePresent) return;

            CustomerAccount toAccount = TransactionDefinition.Account;
            string description = $"Debit note {amountToCustomer.CurrencyCode} {amountToCustomer.Value} to {toAccount.Identificator}";

            SendWithdrawMessage(
                itsThePresent,
                toAccount.CurrencyCode.ToString(),
                toAccount.CustomerAccountNumber,
                amountToCustomer.Value,
                description,
                this.Id,
                employeeName,
                toAccount.Identificator,
                ProcessorAccountId,
                    PaymentChannels.Agents.INSIDER
                );

            if (Integration.UseKafka || Integration.UseKafkaForAuto)
            {
                if (base.JournalEntryTemplate.IsActive)//TODO: no specific template for this transaction type
                {
                    var saleRate = ConversionSpread is NoConversionSpread ? 0m : ConversionSpread.SaleRate.Price;
                    var purchaseRate = ConversionSpread is NoConversionSpread ? 0m : ConversionSpread.PurchaseRate.Price;
                    Integration.Kafka.Send(
                        itsThePresent,
                        $"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForTransacctions}",
                        new JournalEntryForDebitNoteMessage(
                            base.Id,
                            date,
                            journalEntryNumber,
                            employeeName,
                            saleRate,
                            purchaseRate,
                            toAccount.CustomerAccountNumber,
                            toAccount.Identificator,
                            toAccount.Coin,
                            amountToCustomer.Value
                        )
                    );
                }

                var authorizationId = 0; //TODO
                Integration.Kafka.Send(
                    itsThePresent,
                    $"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForTransacctions}",
                    new ApprovedTransactionMessage(TransactionType.DebitNote, Id, date, profit.Value, net.Value, amountToCustomer.Value, gross.Coin, amountToCustomer.Coin, employeeName, authorizationId, ProcessorAccountId)
                );
            }
        }

        internal override void AfterDeny(DateTime date, bool itsThePresent, string employeeName, string reason)
        {
            TransactionDefinition.Deny(this);

            if (!itsThePresent) return;

            if (Integration.UseKafka || Integration.UseKafkaForAuto)
            {
                Integration.Kafka.Send(
                    itsThePresent,
                    $"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForTransacctions}",
                    new DeniedTransactionMessage(TransactionType.DebitNote, Id, date, reason, employeeName)
                );
            }
        }

        internal override bool isSameCurrencyTransaction()
        {
            return TransactionDefinition.Account.CurrencyCode == Amount.CurrencyCode;
        }

        protected sealed override int TemplateNumber
        {
            get
            {
                return TEMPLATE_ID;
            }
        }

    }

    public class JournalEntryForDebitNoteMessage : JournalEntryMessage
    {
        public string CustomerNumber { get; set; }
        public string AccountNumber { get; set; }
        public Coin Coin { get; set; }
        public string CurrencyCode { get { return Coin.Iso4217Code; } }
        public decimal TotalAmount { get; set; }

        public JournalEntryForDebitNoteMessage(int transactionId, DateTime date, int journalEntryId, string employeeName, decimal saleRate, decimal purchaseRate, string customerNumber, 
            string accountNumber, Coin currencyCode, decimal totalAmount) :
            base(TransactionType.DebitNote, transactionId, date, journalEntryId, employeeName, saleRate, purchaseRate)
        {
            this.CustomerNumber = customerNumber;
            this.AccountNumber = accountNumber;
            this.Coin = currencyCode;
            this.TotalAmount = totalAmount;
        }

        public JournalEntryForDebitNoteMessage(string serialized) : base(serialized)
        {
            
        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty(CustomerNumber).
            AddProperty(AccountNumber).
            AddProperty(CurrencyCode).
            AddProperty(TotalAmount);
        }

        protected override void Deserialize(string [] message, out int fieldOrder)
        {
            base.Deserialize(message, out fieldOrder);
            CustomerNumber = message[fieldOrder++];
            AccountNumber = message[fieldOrder++];
            Coin = Coinage.KafkaProperty2Coin(message[fieldOrder++]);
            TotalAmount = decimal.Parse(message[fieldOrder++]);
        }
    }

}
