﻿using GamesEngine.Bets;
using GamesEngine.Games.Lotto;
using GamesEngine.Tools;
using System;
using System.Linq;

namespace GamesEngine.Gameboards.Lotto
{
    internal abstract class TicketKenoMultiplier : TicketKeno
    {
        internal TicketKenoMultiplier(Player player, decimal baseCost,LotteryKeno lotteryKeno, DateTime drawDate, string drawId, int[] numbers, DateTime creationDate, decimal ticketCost, Prizes prizes) :
           base(player, baseCost, lotteryKeno, drawDate, drawId, numbers, true, false, creationDate, ticketCost, prizes)

        {
        }

    }
    internal sealed class TicketKeno10Multiplier : TicketKenoMultiplier
    {
        internal TicketKeno10Multiplier(Player player, decimal baseCost,LotteryKeno lotteryKeno, DateTime drawdate, string drawId, int[] numbers, DateTime creationDate, decimal ticketCost, Prizes prizes) :
           base(player, baseCost, lotteryKeno, drawdate, drawId, numbers, creationDate, ticketCost, prizes)

        {
        }

        internal override TicketType IdOfType()
        {
            return TicketType.K10;
        }

        internal override string GameTypeForReports()
        {
            return QueryMakerOfHistoricalKeno.ID_K10_MUL_NO_BULL;
        }

    }
    internal sealed class TicketKeno12Multiplier : TicketKenoMultiplier
    {
        internal TicketKeno12Multiplier(Player player, decimal baseCost, LotteryKeno lotteryKeno, DateTime drawDate, string drawId, int[] numbers, DateTime creationDate, decimal ticketCost, Prizes prizes) :
           base(player, baseCost, lotteryKeno, drawDate, drawId, numbers, creationDate, ticketCost, prizes)

        {
        }

        internal override TicketType IdOfType()
        {
            return TicketType.K12;
        }

        internal override string GameTypeForReports()
        {
            return QueryMakerOfHistoricalKeno.ID_K12_MUL_NO_BULL;
        }

    }

}
