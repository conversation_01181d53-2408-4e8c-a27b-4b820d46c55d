﻿using GamesEngine.Accounting.JournalTemplates;
using GamesEngine.Business;
using GamesEngine.Custodian;
using GamesEngine.Finance;
using GamesEngine.Messaging;
using GamesEngine.Settings;
using System;
using town.connectors.drivers;
using static GamesEngine.Business.WholePaymentProcessor;
using static GamesEngine.Exchange.TransactionCompleted;
using static GamesEngine.Finance.PaymentChannels;
using static town.connectors.drivers.Result;

namespace GamesEngine.Exchange
{
	internal class WithdrawalTransaction : Transaction
	{
		private int authorizationId;
		internal const int TEMPLATE_ID = 1;

		internal string RealAccount { get; }
		private readonly PaymentProcessor selectedDriver;
		internal int AuthorizationId
		{
			set
			{
				if (authorizationId != 0) throw new GameEngineException($"The {nameof(AuthorizationId)} only can be load one time.");
				if (value <= 0) throw new GameEngineException($"The {nameof(AuthorizationId)} must be greater than 0.");

				authorizationId = value;
			}

			get
			{
				if (authorizationId == 0) throw new GameEngineException($"The {nameof(AuthorizationId)} mustbe loaded");
				return authorizationId;
			}
		}
		internal WithdrawalTransaction(TransactionDefinition transactionDefinition, bool itsThePresent, IConversionSpread conversionSpread, int authorizationId, string employeeName, Currency amount, DateTime now, 
			string realAccount, TransactionFee fee, int storeId, PaymentProcessor processor)
	: base(transactionDefinition, conversionSpread, amount, now, TransactionType.Withdrawal)
		{
			if (transactionDefinition == null) throw new ArgumentNullException(nameof(transactionDefinition));
			if (amount == null) throw new ArgumentNullException(nameof(amount));
			if (string.IsNullOrEmpty(employeeName)) throw new ArgumentNullException(nameof(employeeName));
			if (realAccount.Length > 34) throw new GameEngineException("IBAN and Wallets accounts are 34 length caracters.");
			if (fee == null) throw new ArgumentNullException(nameof(fee));
			if (processor == null) throw new ArgumentNullException(nameof(processor));
			if (authorizationId <= 0) throw new GameEngineException($"The {nameof(authorizationId)} must be greater than 0.");
			if (storeId <= 0) throw new GameEngineException($"The {nameof(storeId)} must be greater than 0.");

			selectedDriver = processor;
			this.authorizationId = authorizationId;
			RealAccount = realAccount;
			Fees.Add(fee);

			CustomerAccount fromAccount = TransactionDefinition.Account;

			transactionDefinition.Batch.AddTransactionDenifition(this);

			string description;
			if (fee.NoFee)
            {
				description = $"Withdrawal {amount.CurrencyCode} {amount.Value} from {fromAccount.Identificator}";
				SendFragmentsCreationMessage(itsThePresent, description, amount.Value, fromAccount.CustomerAccountNumber, amount.Coin, authorizationId, amount.Value, Agents.INSIDER, now);
			}
			else
            {
				var disbursementAmount = Currency.Factory(amount.CurrencyCode, amount.Value);
				disbursementAmount.Subtract(fee.Value);//TODO cris use BIS compressor
				description = $"Withdrawal request {amount.CurrencyCode} {amount.Value}, less fee {amount.CurrencyCode} {fee.Value}. Amount to be disbursement {disbursementAmount.CurrencyCode} {disbursementAmount.Value}";
				SendFragmentsCreationMessageWithFee(itsThePresent, description, disbursementAmount.Value, fromAccount.CustomerAccountNumber, disbursementAmount.CurrencyCode, authorizationId, fee, amount.Value, PaymentChannels.Agents.INSIDER, now);
			}

            var disbursementAmountWithFees = Currency.Factory(amount.CurrencyCode, amount.Value);
			var user = TransactionDefinition.AssignedAgent.SearchUser(employeeName);
			var willPaymentProcessorDisburse = TransactionDefinition.Marketplace.AreAllWithdrawalsManagedAsDisbursements || !user.IsInRange(disbursementAmountWithFees);
			if (willPaymentProcessorDisburse)
            {
				Currency fees = Fees.Total();
				disbursementAmountWithFees.Subtract(fees.Value);
				description = $"Withdrawal request {amount.CurrencyCode} {amount.Value}, less fee {amount.CurrencyCode} {fees.Value}. Amount to be disbursement {disbursementAmountWithFees.CurrencyCode} {disbursementAmountWithFees.Value}";
				selectedDriver.NotifyWithDraw(itsThePresent, now, transactionDefinition.Id, description, disbursementAmountWithFees, fromAccount.CustomerIdentifier, fromAccount.CustomerAccountNumber, transactionDefinition.Domain, Fees, storeId, employeeName);
				
				TransactionDefinition.Batch.Remove(this);
			}
		}

		internal override TransactionCompleted Approve(DateTime date, bool itsThePresent, string employeeName, int journalEntryNumber)
		{
			if (Fees == null) throw new ArgumentNullException(nameof(Fees));

			Currency gross = null, comission = null, net = null, profit = null, disbursement = null;
			Currency amountToCustomer = CalculateAmounts(out gross, out comission, out net, out profit, out disbursement);
			if (!TransactionDefinition.Batch.HasAvailable(amountToCustomer)) throw new GameEngineException($"It's not possible to make a withdraw, because there's no exist funds in {amountToCustomer.CurrencyCode}");
			Status = TransactionStatus.APPROVED;

			var result = new WithdrawalTransactionResult(net, gross, comission, profit, amountToCustomer, Fees.Total(), disbursement);
			TransactionCompleted transactionCompleted = new TransactionCompleted(result);

			TransactionDefinition.SetJournalEntryId(journalEntryNumber);
			AfterApprove(date, itsThePresent, gross, comission, profit, net, amountToCustomer, employeeName, transactionCompleted, journalEntryNumber);

			TransactionDefinition.Batch.Remove(this);
			TransactionDefinition.Batch.AddTransactionApproval(this, transactionCompleted);

			ConversionSpread.DecreaseTheNumberOfTransactionsUsingIt();

			return transactionCompleted;
		}

		internal Currency CalculateAmounts(out Currency gross, out Currency comission, out Currency net, out Currency profit, out Currency disbursement)
		{
			if (Fees == null) throw new ArgumentNullException(nameof(Fees));

			Currency amountToCustomer = null;
			Currency amount = Amount;
			bool thereIsARateDefined = !(ConversionSpread is NoConversionSpread);
			if (thereIsARateDefined)
			{
				amountToCustomer = ConversionSpread.Sale(amount, out gross, out comission, out net);
				disbursement = Currency.Factory(amount.CurrencyCode, 0);
				profit = TransactionDefinition.Batch.CalculateProfit(ConversionSpread.SaleRate, amountToCustomer);
			}
			else if (!thereIsARateDefined && TransactionDefinition.Account.CurrencyCode == Amount.CurrencyCode)
			{
				gross = amount;
				comission = Currency.Factory(amount.CurrencyCode, 0);
				net = Currency.Factory(amount.CurrencyCode, amount.Value);
				net.Subtract(Fees.Total());
				disbursement = net;
				amountToCustomer = amount;

				FixedExchangeRate noProfitRate = new FixedExchangeRate(amountToCustomer.Coin, amountToCustomer.Coin, 1);
				profit = TransactionDefinition.Batch.CalculateProfit(noProfitRate, amountToCustomer);
			}
			else
			{
				throw new GameEngineException($"There is no a Rate defined for {TransactionDefinition.Account.CurrencyCode}/{Amount.CurrencyCode} ");
			}

			return amountToCustomer;
		}

		internal override void Deny(DateTime date, bool itsThePresent, string employeeName, string reason)
		{
			if (Fees == null) throw new ArgumentNullException(nameof(Fees));

			AfterDeny(date, itsThePresent, employeeName, reason);

			Currency gross = null, comission = null, net = null, profit = null, disbursement = null;
			Currency amountToCustomer = CalculateAmounts(out gross, out comission, out net, out profit, out disbursement);

			var result = new WithdrawalTransactionResult(net, gross, comission, profit, amountToCustomer, disbursement, Fees.Total());
			TransactionCompleted transactionCompleted = new TransactionCompleted(result);

			TransactionDefinition.Batch.AddTransactionRejection(this, transactionCompleted);

			TransactionDefinition.Batch.Remove(this);

			ConversionSpread.DecreaseTheNumberOfTransactionsUsingIt();
		}

		internal override void AfterDeny(DateTime date, bool itsThePresent, string employeeName, string reason)
		{
			TransactionDefinition.Deny(this);

			if (!itsThePresent) return;

			Currency net = Currency.Factory(TransactionDefinition.Account.CurrencyCode, 0);
			string thirdPartyOutAccount = TransactionDefinition.OutAccountFor(TransactionDefinition.Account.Coin);

			CustomerAccount fromAccount = TransactionDefinition.Account;
			var fee = Fees.Total().Value;
			if (Fees.AnyFee())
			{
				SendFragmentPaymentMessageWithFee(
				itsThePresent,
				date,
				fromAccount.CurrencyCode.ToString(),
				fromAccount.CustomerAccountNumber,
				authorizationId, employeeName,
				WagerStatus.X,
				reason,
				net,
				RealAccount,
				thirdPartyOutAccount,
				Type,
				Status,
				fee
				);
			}
			else
			{
				SendFragmentPaymentMessage(
				itsThePresent,
				date,
				fromAccount.CurrencyCode.ToString(),
				fromAccount.CustomerAccountNumber,
				authorizationId, employeeName,
				WagerStatus.X,
				reason,
				net,
				RealAccount,
				thirdPartyOutAccount,
				Type,
				Status
				);
			}

			if (Integration.UseKafka || Integration.UseKafkaForAuto)
			{
				Integration.Kafka.Send(
					itsThePresent,
					$"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForTransacctions}",
					new DeniedTransactionMessage(TransactionType.Withdrawal, Id, date, reason, employeeName)
				);
			}
		}

		protected override void AfterApprove(DateTime date, bool itsThePresent, Currency gross, Currency comission, Currency profit, Currency net, Currency amountToCustomer, string employeeName, TransactionCompleted transactionCompleted, int journalEntryNumber)
		{
			if (!itsThePresent) return;

			if (Integration.UseKafka || Integration.UseKafkaForAuto)
			{
				string thirdPartyOutAccount = TransactionDefinition.OutAccountFor(gross.Coin);
				CustomerAccount fromAccount = TransactionDefinition.Account;

				var fee = Fees.Total().Value;
				string concept = $"Withdrawal {Amount.CurrencyCode} {Amount.Value} from {fromAccount.Identificator}";
				if (Fees.AnyFee())
				{
					SendFragmentPaymentMessageWithFee(
					itsThePresent,
					date,
					amountToCustomer.CurrencyCode.ToString(),
					fromAccount.CustomerAccountNumber,
					authorizationId,
					employeeName,
					WagerStatus.L,
					concept,
					net,
					RealAccount,
					thirdPartyOutAccount,
					Type,
					Status,
					fee
					);
				}
				else
				{
					SendFragmentPaymentMessage(
					itsThePresent,
					date,
					amountToCustomer.CurrencyCode.ToString(),
					fromAccount.CustomerAccountNumber,
					authorizationId,
					employeeName,
					WagerStatus.L,
					concept,
					net,
					RealAccount,
					thirdPartyOutAccount,
					Type,
					Status
					);
				}

				var marketplace = TransactionDefinition.Marketplace;
				var template = marketplace.TransactionOutcomes().TemplateFor(TransactionDefinition.Domain, TransactionType.Withdrawal, amountToCustomer.Coin);
				if (template.IsActive)
				{
					var currencyCost = TransactionDefinition.Batch.CalculateCost(amountToCustomer);
					var saleRate = ConversionSpread is NoConversionSpread ? 0m : ConversionSpread.SaleRate.Price;
					var purchaseRate = ConversionSpread is NoConversionSpread ? 0m : ConversionSpread.PurchaseRate.Price;
					Integration.Kafka.Send(
						itsThePresent,
						$"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForTransacctions}",
						new JournalEntryForWithdrawalMessage(
							base.Id,
							date,
							journalEntryNumber,
							employeeName,
							saleRate,
							purchaseRate,
							authorizationId,
							fromAccount.CustomerAccountNumber,
							fromAccount.Identificator,
							amountToCustomer.Coin,
							amountToCustomer.Value,
							fee,
							currencyCost.Value
						)
					);
				}

				Integration.Kafka.Send(
					itsThePresent,
					$"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForTransacctions}",
					new ApprovedTransactionMessage(TransactionType.Withdrawal, Id, date, profit.Value, net.Value, amountToCustomer.Value, gross.Coin, amountToCustomer.Coin, employeeName, authorizationId, ProcessorAccountId)
				);

				var processors = marketplace.PaymentProcessors();
				if (processors.IsNotificationEnabled(selectedDriver.Driver.Id, WholePaymentProcessor.Instance().SearchTransactionByName(TransactionType.Withdrawal.ToString()), TransactionStatus.APPROVED))
                {
					Subscriber fromSubscriber = marketplace;
					var messageToSend = $"The transaction #{Id} '{concept}' has been approved. Go to History to see the details";
					if (amountToCustomer.Coin == Coinage.Coin(Currencies.CODES.BTC))
					{
						Subscriber toSubscriber = TransactionDefinition.FindOwner().Player;
						Integration.Kafka.Send(itsThePresent, Integration.Kafka.TopicForNotifications,
							new NotificationMessage(NotificationMessageType.ADD, fromSubscriber, toSubscriber, messageToSend));
					}
				}
			}
		}

		public static JournalEntryTemplate Template { get; private set; }
		internal static void InitializeTemplate(JournalEntryTemplates templates)
        {
			Template = templates.CreateTemplate(WithdrawalTransaction.TEMPLATE_ID, "Template for " + nameof(WithdrawalTransaction));
			var parameters = Template.Parameters;

			parameters.AddTextParameter("TransactionId");
			parameters.AddTextParameter("AuthorizationId");
			parameters.AddTextParameter("Date");
			parameters.AddTextParameter("CustomerNumber");
			parameters.AddTextParameter("AccountNumber");
			parameters.AddTextParameter("Currency");
			parameters.AddAmountParameter("SaleRate");
			parameters.AddAmountParameter("PurchaseRate");
			parameters.AddAmountParameter("CurrencyCost");
			parameters.AddAmountParameter("TotalAmount");

			Template.AddDebit("100-01", "Dummy Withdrawal Transaction {TransactionId}", "TotalAmount");
			Template.AddCredit("200-01", "Dummy Withdrawal Transaction {TransactionId}", "TotalAmount");
		}

		internal override bool isSameCurrencyTransaction()
		{
			return TransactionDefinition.Account.CurrencyCode == Amount.CurrencyCode;
		}

		protected sealed override int TemplateNumber
		{
			get
			{
				return TEMPLATE_ID;
			}
		}

		class IncomingWithdrawalMessage : KafkaMessage
		{
			internal IncomingWithdrawalMessage(string trackingCode, Currencies.CODES currencyCode, string destinationAddress, decimal amount, decimal fee)
			{
				TrackingCode = TrackingCode;
				CurrencyCode = currencyCode;
				DestinationAddress = destinationAddress;
				Amount = amount;
				TrackingCode = TrackingCode;
			}

			public IncomingWithdrawalMessage(string msg):base(msg)
			{
			}

			public string DestinationAddress { get; internal set; }
			public string TrackingCode { get; internal set; }
			public Currencies.CODES CurrencyCode { get; private set; }
			public decimal Amount { get; internal set; }
			public decimal Fee { get; internal set; }

			protected override void InternalSerialize()
			{
				base.InternalSerialize();
				AddProperty("trackingCode"). //TODO cris cambiar
				AddProperty(CurrencyCode.ToString()).
				AddProperty(DestinationAddress).
				AddProperty(Amount).
				AddProperty(Fee);
			}

			protected override void Deserialize(string [] serializedMessage, out int fieldOrder)
			{
				base.Deserialize(serializedMessage, out fieldOrder);
				TrackingCode = serializedMessage[fieldOrder++];
				CurrencyCode = (Currencies.CODES)Enum.Parse(typeof(Currencies.CODES), serializedMessage[fieldOrder++]);
				DestinationAddress = serializedMessage[fieldOrder++];
				Amount = decimal.Parse(serializedMessage[fieldOrder++]);
				Fee = decimal.Parse(serializedMessage[fieldOrder++]);
			}
        }
	}

	public class JournalEntryForWithdrawalMessage : JournalEntryMessage
	{
		public int AuthorizationId { get; private set; }
		public string CustomerNumber { get; private set; }
		public string AccountNumber { get; private set; }
		public string CurrencyCode { get { return Coin.Iso4217Code; } }
		public Coin Coin { get; private set; }
		public decimal TotalAmount { get; private set; }
		public decimal Fee { get; private set; }
		public decimal CurrencyCost { get; private set; }

		public JournalEntryForWithdrawalMessage(int transactionId, DateTime date, int journalEntryId, string employeeName, decimal saleRate, decimal purchaseRate, int authorizationId, 
			string customerNumber, string accountNumber, Coin coin, decimal totalAmount, decimal fee, decimal currencyCost) :
			base(TransactionType.Withdrawal, transactionId, date, journalEntryId, employeeName, saleRate, purchaseRate)
		{
			this.AuthorizationId = authorizationId;
			this.CustomerNumber = customerNumber;
			this.AccountNumber = accountNumber;
			this.Coin = coin;
			this.TotalAmount = totalAmount;
			this.Fee = fee;
			CurrencyCost = currencyCost;
		}

		public JournalEntryForWithdrawalMessage(string serialized) : base(serialized)
		{
			
		}

		protected override void InternalSerialize()
		{
			base.InternalSerialize();
			AddProperty(AuthorizationId).
			AddProperty(CustomerNumber).
			AddProperty(AccountNumber).
			AddProperty(CurrencyCode).
			AddProperty(TotalAmount).
			AddProperty(Fee).
			AddProperty(CurrencyCost);
		}

		protected override void Deserialize(string [] message, out int fieldOrder)
		{
			base.Deserialize(message, out fieldOrder);
			AuthorizationId = int.Parse(message[fieldOrder++]);
			CustomerNumber = message[fieldOrder++];
			AccountNumber = message[fieldOrder++];
			Coin = Coinage.KafkaProperty2Coin(message[fieldOrder++]);
			TotalAmount = decimal.Parse(message[fieldOrder++]);
			Fee = decimal.Parse(message[fieldOrder++]);
			CurrencyCost = decimal.Parse(message[fieldOrder++]);
		}
	}
}