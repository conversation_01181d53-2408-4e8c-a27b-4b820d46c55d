﻿using System;
using System.Collections.Generic;
using System.Text;

namespace GamesEngine.Accounting.JournalTemplates
{
	internal struct AccountingMovement
	{
		private string accountNumber;
		private string description;
		private decimal amount;
		private byte sequence;

		internal AccountingMovement(byte sequence, string accountNumber, decimal amount, string description)
		{
			if (string.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));
			if (amount == 0) throw new GameEngineException("Invalid amount, it must be different than zero");
			if (string.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));
			if (sequence <= 0) throw new GameEngineException("Invalid sequence number. Sequences must be greather than zero.");

			this.accountNumber = accountNumber;
			this.amount = amount;
			this.description = description;
			this.sequence = sequence;
		}

		internal string AccountNumber
		{
			get
			{
				return this.accountNumber;
			}
		}

		internal string Description
		{
			get
			{
				return this.description;
			}
		}

		internal decimal Debit
		{
			get
			{
				if (amount < 0) throw new GameEngineException("This is not a debit movement");
				return amount;
			}
		}

		internal decimal Credit
		{
			get
			{
				if (amount > 0) throw new GameEngineException("This is not a credit movement");
				return amount;
			}
		}

		internal byte Sequence
		{
			get
			{
				return sequence;
			}
		}

		internal bool IsCredit
		{
			get
			{
				return amount < 0;
			}
		}
	}
}
