﻿using ExchangeAPI.Service;
using GamesEngine;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace ExchangeAPI.Controllers
{
    public class BatchesController : AuthorizeController
	{
		[HttpGet("api/exchange/transaction/batch/valid/paths")]
		[Authorize(Roles = "c32")]
		public async Task<IActionResult> ListOfValidPathsAsync()
		{
			var result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
				{{
					for(path : marketplace.Batches.AllValidPaths())
					{{
						print path.FullName fullName;
						print path.Level level;
					}}
				}}
			");
			return result;
		}

		[HttpGet("api/exchange/transaction/batches")]
		[Authorize(Roles = "c33")]
		public async Task<IActionResult> ListMarketplaceBatchesAtAsync(string date, string fullName)
		{
			if (!Validator.IsValidDate(date)) return NotFound($"Parameter {nameof(date)} is empty or invaid: {date}");


			IActionResult result;
			if (string.IsNullOrWhiteSpace(fullName))
			{
				result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
				{{
					print marketplace.Batches.Marketplace.Name fullName;
					print Now now;
					for(ready : marketplace.Batches.BatchesReady({date}))
					{{
						print ready.Level level;
						print ready.FullName fullName;
						print ready.BatchNumber batchNumber;
						print ready.Description description;
						print ready.CreationDate creationDate;
						print ready.ScheduledDate scheduledDate;
						if(ready.HasClosedDate())
						{{
							print ready.CloseDate closedDate;
						}}
						else
						{{
							print '-' closedDate;
						}}
						print ready.CreatedBy createdBy;
						print ready.HasAgents() hasAgents;
						print 'Ready' status;
						print ready.HasAvailableAmountInAnyCurrency hasFunds;
						for(outAccount : ready.InitialAccounts())
						{{
							print outAccount.CurrencyCodeAsText currencyCode;
							print outAccount.Initial.Value initial;
							print outAccount.Initial.ToDisplayFormat() initialFormatted;
							print outAccount.Available.Value pending;
							print outAccount.Available.ToDisplayFormat() pendingFormatted;
							print outAccount.LockedAccumulated.Value locked;
							print outAccount.LockedAccumulated.ToDisplayFormat() lockedFormatted;
							print outAccount.SpendAccumulated.Value spend;
							print outAccount.SpendAccumulated.ToDisplayFormat() spendFormatted;
							print ready.TotalAssigned(outAccount.CurrencyCodeAsText).Value totalAssigned;
							print ready.TotalAssigned(outAccount.CurrencyCodeAsText).ToDisplayFormat() totalAssignedFormatted;
							if(ready.HasAgents())
							{{
								allAgenciesBRI = marketplace.Batches.FindMarketplaceBatch(ready.BatchNumber).AllAgencies();
								for(agent : allAgenciesBRI)
								{{
									currentAgentBRI = agent;
									print currentAgentBRI.Name agentName;
									agentOutAccountBRI = currentAgentBRI.SaleAccount(outAccount.CurrencyCodeAsText);
									print agentOutAccountBRI.CurrencyCodeAsText currencyCode;
									print agentOutAccountBRI.Initial.Value initial;
									print agentOutAccountBRI.Initial.ToDisplayFormat() initialFormatted;
									print agentOutAccountBRI.Available.Value available;
									print agentOutAccountBRI.Available.ToDisplayFormat() availableFormatted;
									print agentOutAccountBRI.LockedAccumulated.Value locked;
									print agentOutAccountBRI.LockedAccumulated.ToDisplayFormat() lockedFormatted;
									print agentOutAccountBRI.SpendAccumulated.Value spend;
									print agentOutAccountBRI.SpendAccumulated.ToDisplayFormat() spendFormatted;

									agentInAccountBRI = currentAgentBRI.SaleAccount(outAccount.CurrencyCodeAsText);
									print agentInAccountBRI.CurrencyCodeAsText currencyCode;
									print agentInAccountBRI.Available.Value available;
									print agentInAccountBRI.Available.ToDisplayFormat() availableFormatted;
								}}
							}}
						}}
						for(inAccount : ready.ReceptionAccounts())
						{{
							print inAccount.CurrencyCodeAsText currencyCode;
							print inAccount.Available.Value available;
							print inAccount.Available.ToDisplayFormat() availableFormatted;
							print ready.TotalAvailable(inAccount.CurrencyCodeAsText).Value totalAvailable;
							print ready.TotalAvailable(inAccount.CurrencyCodeAsText).ToDisplayFormat() totalAvailableFormatted;
							if(ready.HasAgents())
							{{
								allAgenciesBRR = marketplace.Batches.FindMarketplaceBatch(ready.BatchNumber).AllAgencies();
								for(agent : allAgenciesBRR)
								{{
									currentAgentBRR = agent;
									print currentAgentBRR.Name agentName;
									agentOutAccountBRR = currentAgentBRR.ReceptionAccount(inAccount.CurrencyCodeAsText);
									print agentOutAccountBRR.CurrencyCodeAsText currencyCode;
									print agentOutAccountBRR.Available.Value available;
									print agentOutAccountBRR.Available.ToDisplayFormat() availableFormatted;
									print agentOutAccountBRR.LockedAccumulated.Value locked;
									print agentOutAccountBRR.LockedAccumulated.ToDisplayFormat() lockedFormatted;
									print agentOutAccountBRR.SpendAccumulated.Value spend;
									print agentOutAccountBRR.SpendAccumulated.ToDisplayFormat() spendFormatted;

									agentInAccountBRR = currentAgentBRR.ReceptionAccount(inAccount.CurrencyCodeAsText);
									print agentInAccountBRR.CurrencyCodeAsText currencyCode;
									print agentInAccountBRR.Available.Value available;
									print agentInAccountBRR.Available.ToDisplayFormat() availableFormatted;
								}}
							}}
						}}
					}}
					for(open : marketplace.Batches.BatchesOpen({date}))
					{{
						print open.Level level;
						print open.FullName fullName;
						print open.BatchNumber batchNumber;
						print open.Description description;
						print open.CreationDate creationDate;
						print open.ScheduledDate scheduledDate;
						if(open.HasClosedDate())
						{{
							print open.CloseDate closedDate;
						}}
						else
						{{
							print '-' closedDate;
						}}
						print open.CreatedBy createdBy;
						print open.HasAgents() hasAgents;
						print 'Open' status;
						print open.HasAvailableAmountInAnyCurrency hasFunds;
						print open.OpenDate openedDate;
						
						for(outAccount : open.InitialAccounts())
						{{
							print outAccount.CurrencyCodeAsText currencyCode;
							print outAccount.Initial.Value initial;
							print outAccount.Initial.ToDisplayFormat() initialFormatted;
							print outAccount.Available.Value pending;
							print outAccount.Available.ToDisplayFormat() pendingFormatted;
							print outAccount.LockedAccumulated.Value locked;
							print outAccount.LockedAccumulated.ToDisplayFormat() lockedFormatted;
							print outAccount.SpendAccumulated.Value spend;
							print outAccount.SpendAccumulated.ToDisplayFormat() spendFormatted;

							print open.TotalAssigned(outAccount.CurrencyCodeAsText).Value totalAssigned;
							print open.TotalAssigned(outAccount.CurrencyCodeAsText).ToDisplayFormat() totalAssignedFormatted;
							if(open.HasAgents())
							{{
								allAgenciesBOI = marketplace.Batches.FindMarketplaceBatch(open.BatchNumber).AllAgencies();
								for(agent : allAgenciesBOI)
								{{
									currentAgentBOI = agent;
									print currentAgentBOI.Name agentName;
									agentOutAccountBOI = currentAgentBOI.SaleAccount(outAccount.CurrencyCodeAsText);
									print agentOutAccountBOI.CurrencyCodeAsText currencyCode;
									print agentOutAccountBOI.Initial.Value initial;
									print agentOutAccountBOI.Initial.ToDisplayFormat() initialFormatted;
									print agentOutAccountBOI.Available.Value available;
									print agentOutAccountBOI.Available.ToDisplayFormat() availableFormatted;
									print agentOutAccountBOI.LockedAccumulated.Value locked;
									print agentOutAccountBOI.LockedAccumulated.ToDisplayFormat() lockedFormatted;
									print agentOutAccountBOI.SpendAccumulated.Value spend;
									print agentOutAccountBOI.SpendAccumulated.ToDisplayFormat() spendFormatted;

									agentInAccountBOI = currentAgentBOI.SaleAccount(outAccount.CurrencyCodeAsText);
									print agentInAccountBOI.CurrencyCodeAsText currencyCode;
									print agentInAccountBOI.Available.Value available;
									print agentInAccountBOI.Available.ToDisplayFormat() availableFormatted;
								}}
							}}
						}}
						for(inAccount : open.ReceptionAccounts())
						{{
							print inAccount.CurrencyCodeAsText currencyCode;
							print inAccount.Available.Value available;
							print inAccount.Available.ToDisplayFormat() availableFormatted;
							print open.TotalAvailable(inAccount.CurrencyCodeAsText).Value totalAvailable;
							print open.TotalAvailable(inAccount.CurrencyCodeAsText).ToDisplayFormat() totalAvailableFormatted;
							if(open.HasAgents())
							{{
								allAgenciesBOR = marketplace.Batches.FindMarketplaceBatch(open.BatchNumber).AllAgencies();
								for(agent : allAgenciesBOR)
								{{
									currentAgentBOR = agent;
									print currentAgentBOR.Name agentName;
									agentOutAccountBOR = currentAgentBOR.ReceptionAccount(inAccount.CurrencyCodeAsText);
									print agentOutAccountBOR.CurrencyCodeAsText currencyCode;
									print agentOutAccountBOR.Available.Value available;
									print agentOutAccountBOR.Available.ToDisplayFormat() availableFormatted;
									print agentOutAccountBOR.LockedAccumulated.Value locked;
									print agentOutAccountBOR.LockedAccumulated.ToDisplayFormat() lockedFormatted;
									print agentOutAccountBOR.SpendAccumulated.Value spend;
									print agentOutAccountBOR.SpendAccumulated.ToDisplayFormat() spendFormatted;

									agentInAccountBOR = currentAgentBOR.ReceptionAccount(inAccount.CurrencyCodeAsText);
									print agentInAccountBOR.CurrencyCodeAsText currencyCode;
									print agentInAccountBOR.Available.Value available;
									print agentInAccountBOR.Available.ToDisplayFormat() availableFormatted;
								}}
							}}
						}}
					}}
					for(closed : marketplace.Batches.BatchesClosed({date}))
					{{
						print closed.Level level;
						print closed.FullName fullName;
						print closed.BatchNumber batchNumber;
						print closed.Description description;
						print closed.CreationDate creationDate;
						print closed.ScheduledDate scheduledDate;
						if(closed.HasClosedDate())
						{{
							print closed.CloseDate closedDate;
						}}
						else
						{{
							print '-' closedDate;
						}}
						print closed.CreatedBy createdBy;
						print closed.HasAgents() hasAgents;
						print 'Closed' status;
						print closed.HasAvailableAmountInAnyCurrency hasFunds;
						for(outAccount : closed.InitialAccounts())
						{{
							print outAccount.CurrencyCodeAsText currencyCode;
							print outAccount.Initial.Value initial;
							print outAccount.Initial.ToDisplayFormat() initialFormatted;
							print outAccount.Available.Value pending;
							print outAccount.Available.ToDisplayFormat() pendingFormatted;
							print outAccount.LockedAccumulated.Value locked;
							print outAccount.LockedAccumulated.ToDisplayFormat() lockedFormatted;
							print outAccount.SpendAccumulated.Value spend;
							print outAccount.SpendAccumulated.ToDisplayFormat() spendFormatted;

							print closed.TotalAssigned(outAccount.CurrencyCodeAsText).Value totalAssigned;
							print closed.TotalAssigned(outAccount.CurrencyCodeAsText).ToDisplayFormat() totalAssignedFormatted;
							if(closed.HasAgents())
							{{
								allAgenciesBCI = marketplace.Batches.FindMarketplaceBatch(closed.BatchNumber).AllAgencies();
								for(agent : allAgenciesBCI)
								{{
									currentAgentBCI = agent;
									print currentAgentBCI.Name agentName;
									agentOutAccountBCI = currentAgentBCI.SaleAccount(outAccount.CurrencyCodeAsText);
									print agentOutAccountBCI.CurrencyCodeAsText currencyCode;
									print agentOutAccountBCI.Initial.Value initial;
									print agentOutAccountBCI.Initial.ToDisplayFormat() initialFormatted;
									print agentOutAccountBCI.Available.Value available;
									print agentOutAccountBCI.Available.ToDisplayFormat() availableFormatted;
									print agentOutAccountBCI.LockedAccumulated.Value locked;
									print agentOutAccountBCI.LockedAccumulated.ToDisplayFormat() lockedFormatted;
									print agentOutAccountBCI.SpendAccumulated.Value spend;
									print agentOutAccountBCI.SpendAccumulated.ToDisplayFormat() spendFormatted;

									agentInAccountBCI = currentAgentBCI.SaleAccount(outAccount.CurrencyCodeAsText);
									print agentInAccountBCI.CurrencyCodeAsText currencyCode;
									print agentInAccountBCI.Available.Value available;
									print agentInAccountBCI.Available.ToDisplayFormat() availableFormatted;
								}}
							}}
						}}
						for(inAccount : closed.ReceptionAccounts())
						{{
							print inAccount.CurrencyCodeAsText currencyCode;
							print inAccount.Available.Value available;
							print inAccount.Available.ToDisplayFormat() availableFormatted;
							print closed.TotalAvailable(inAccount.CurrencyCodeAsText).Value totalAvailable;
							print closed.TotalAvailable(inAccount.CurrencyCodeAsText).ToDisplayFormat() totalAvailableFormatted;
							if(closed.HasAgents())
							{{
								allAgenciesBCR = marketplace.Batches.FindMarketplaceBatch(closed.BatchNumber).AllAgencies();
								for(agent : allAgenciesBCR)
								{{
									currentAgentBCR = agent;
									print currentAgentBCR.Name agentName;
									agentOutAccountBCR = currentAgentBCR.ReceptionAccount(inAccount.CurrencyCodeAsText);
									print agentOutAccountBCR.CurrencyCodeAsText currencyCode;
									print agentOutAccountBCR.Available.Value available;
									print agentOutAccountBCR.Available.ToDisplayFormat() availableFormatted;
									print agentOutAccountBCR.LockedAccumulated.Value locked;
									print agentOutAccountBCR.LockedAccumulated.ToDisplayFormat() lockedFormatted;
									print agentOutAccountBCR.SpendAccumulated.Value spend;
									print agentOutAccountBCR.SpendAccumulated.ToDisplayFormat() spendFormatted;

									agentInAccountBCR = currentAgentBCR.ReceptionAccount(inAccount.CurrencyCodeAsText);
									print agentInAccountBCR.CurrencyCodeAsText currencyCode;
									print agentInAccountBCR.Available.Value available;
									print agentInAccountBCR.Available.ToDisplayFormat() availableFormatted;
								}}
							}}
						}}
					}}
					for(verified : marketplace.Batches.BatchesVerified({date}))
					{{
						print verified.Level level;
						print verified.FullName fullName;
						print verified.BatchNumber batchNumber;
						print verified.Description description;
						print verified.CreationDate creationDate;
						print verified.ScheduledDate scheduledDate;
						if(verified.HasClosedDate())
						{{
							print verified.CloseDate closedDate;
						}}
						else
						{{
							print '-' closedDate;
						}}
						print verified.CreatedBy createdBy;
						print verified.HasAgents() hasAgents;
						print 'Verified' status;
						print verified.HasAvailableAmountInAnyCurrency hasFunds;
						for(outAccount : verified.InitialAccounts())
						{{
							print outAccount.CurrencyCodeAsText currencyCode;
							print outAccount.Initial.Value initial;
							print outAccount.Initial.ToDisplayFormat() initialFormatted;
							print outAccount.Available.Value pending;
							print outAccount.Available.ToDisplayFormat() pendingFormatted;
							print outAccount.LockedAccumulated.Value locked;
							print outAccount.LockedAccumulated.ToDisplayFormat() lockedFormatted;
							print outAccount.SpendAccumulated.Value spend;
							print outAccount.SpendAccumulated.ToDisplayFormat() spendFormatted;

							print verified.TotalAssigned(outAccount.CurrencyCodeAsText).Value totalAssigned;
							print verified.TotalAssigned(outAccount.CurrencyCodeAsText).ToDisplayFormat() totalAssignedFormatted;
							if(verified.HasAgents())
							{{
								allAgenciesBVI = marketplace.Batches.FindMarketplaceBatch(verified.BatchNumber).AllAgencies();
								for(agent : allAgenciesBVI)
								{{
									currentAgentBVI = agent;
									print currentAgentBVI.Name agentName;
									agentOutAccountBVI = currentAgentBVI.SaleAccount(outAccount.CurrencyCodeAsText);
									print agentOutAccountBVI.CurrencyCodeAsText currencyCode;
									print agentOutAccountBVI.Initial.Value initial;
									print agentOutAccountBVI.Initial.ToDisplayFormat() initialFormatted;
									print agentOutAccountBVI.Available.Value available;
									print agentOutAccountBVI.Available.ToDisplayFormat() availableFormatted;
									print agentOutAccountBVI.LockedAccumulated.Value locked;
									print agentOutAccountBVI.LockedAccumulated.ToDisplayFormat() lockedFormatted;
									print agentOutAccountBVI.SpendAccumulated.Value spend;
									print agentOutAccountBVI.SpendAccumulated.ToDisplayFormat() spendFormatted;

									agentInAccountBVI = currentAgentBVI.SaleAccount(outAccount.CurrencyCodeAsText);
									print agentInAccountBVI.CurrencyCodeAsText currencyCode;
									print agentInAccountBVI.Available.Value available;
									print agentInAccountBVI.Available.ToDisplayFormat() availableFormatted;
								}}
							}}
						}}
						for(inAccount : verified.ReceptionAccounts())
						{{
							print inAccount.CurrencyCodeAsText currencyCode;
							print inAccount.Available.Value available;
							print inAccount.Available.ToDisplayFormat() availableFormatted;
							print verified.TotalAvailable(inAccount.CurrencyCodeAsText).Value totalAvailable;
							print verified.TotalAvailable(inAccount.CurrencyCodeAsText).ToDisplayFormat() totalAvailableFormatted;
							if(verified.HasAgents())
							{{
								allAgenciesBVR = marketplace.Batches.FindMarketplaceBatch(verified.BatchNumber).AllAgencies();
								for(agent : allAgenciesBVR)
								{{
									currentAgentBVR = agent;
									print currentAgentBVR.Name agentName;
									agentOutAccountBVR = currentAgentBVR.ReceptionAccount(inAccount.CurrencyCodeAsText);
									print agentOutAccountBVR.CurrencyCodeAsText currencyCode;
									print agentOutAccountBVR.Available.Value available;
									print agentOutAccountBVR.Available.ToDisplayFormat() availableFormatted;
									print agentOutAccountBVR.LockedAccumulated.Value locked;
									print agentOutAccountBVR.LockedAccumulated.ToDisplayFormat() lockedFormatted;
									print agentOutAccountBVR.SpendAccumulated.Value spend;
									print agentOutAccountBVR.SpendAccumulated.ToDisplayFormat() spendFormatted;

									agentInAccountBVR = currentAgentBVR.ReceptionAccount(inAccount.CurrencyCodeAsText);
									print agentInAccountBVR.CurrencyCodeAsText currencyCode;
									print agentInAccountBVR.Available.Value available;
									print agentInAccountBVR.Available.ToDisplayFormat() availableFormatted;
								}}
							}}
						}}
					}}
				}}
            ");
			}
			else
			{
				result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
                {{
					print [fullName] fullName;
					print Now now;
					for(ready : marketplace.Batches.BatchesReady({date}))
					{{
						print ready.FullName fullName;
						print ready.BatchNumber batchNumber;
						print ready.Description description;
						print ready.CreationDate creationDate;
						print ready.ScheduledDate scheduledDate;
						if(ready.HasClosedDate())
						{{
							print ready.CloseDate closedDate;
						}}
						else
						{{
							print '-' closedDate;
						}}
						print ready.CreatedBy createdBy;
						print 'Ready' status;
						pathBR = ready.TailOFFullName('{fullName}');
						if(ready.ExistAgentBatch(pathBR))
						{{
							batchBR = marketplace.Batches.SearchAgentBatch('{fullName}', ready.BatchNumber);
							print batchBR.Level level;
							print batchBR.Name agentName;
							print batchBR.FullName fullName;
							print batchBR.CreationDate creationDate;
							print batchBR.IsInternalAgent isAnEntity;
							print batchBR.IsFinalAgent isAnAgent;
							print batchBR.HasAgents() hasAgents;
							print batchBR.HasSaleAmountInAnyCurrency hasFunds;
							for(outAccount : batchBR.SelfSaleAccounts())
							{{
								print outAccount.CurrencyCodeAsText currencyCode;
								print outAccount.Initial.Value initial;
								print outAccount.Initial.ToDisplayFormat() initialFormatted;
								print outAccount.Available.Value pending;
								print outAccount.Available.ToDisplayFormat() pendingFormatted;
								print outAccount.LockedAccumulated.Value locked;
								print outAccount.LockedAccumulated.ToDisplayFormat() lockedFormatted;
								print outAccount.SpendAccumulated.Value spend;
								print outAccount.SpendAccumulated.ToDisplayFormat() spendFormatted;

								print batchBR.TotalAssigned(outAccount.CurrencyCodeAsText).Value totalAssigned;
								print batchBR.TotalAssigned(outAccount.CurrencyCodeAsText).ToDisplayFormat() totalAssignedFormatted;
								if(batchBR.HasAgents())
								{{
									allAgentsBRS = marketplace.Batches.SearchAgentBatch('{fullName}', ready.BatchNumber).AllAgents();
									for(agent : allAgentsBRS)
									{{
										currentAgentBRS = agent;
										print currentAgentBRS.Name agentName;
										agentOutAccountBRS = currentAgentBRS.SaleAccount(outAccount.CurrencyCodeAsText);
										print agentOutAccountBRS.CurrencyCodeAsText currencyCode;
										print agentOutAccountBRS.Initial.Value initial;
										print agentOutAccountBRS.Initial.ToDisplayFormat() initialFormatted;
										print agentOutAccountBRS.Available.Value available;
										print agentOutAccountBRS.Available.ToDisplayFormat() availableFormatted;
										print agentOutAccountBRS.LockedAccumulated.Value locked;
										print agentOutAccountBRS.LockedAccumulated.ToDisplayFormat() lockedFormatted;
										print agentOutAccountBRS.SpendAccumulated.Value spend;
										print agentOutAccountBRS.SpendAccumulated.ToDisplayFormat() spendFormatted;

										agentInAccountBRS = currentAgentBRS.SaleAccount(outAccount.CurrencyCodeAsText);
										print agentInAccountBRS.CurrencyCodeAsText currencyCode;
										print agentInAccountBRS.Available.Value available;
										print agentInAccountBRS.Available.ToDisplayFormat() availableFormatted;
									}}
								}}
							}}
							for(inAccount : batchBR.SelfReceptionAccounts())
							{{
								print inAccount.CurrencyCodeAsText currencyCode;
								print inAccount.Available.Value available;
								print inAccount.Available.ToDisplayFormat() availableFormatted;
								print batchBR.TotalAvailable(inAccount.CurrencyCodeAsText).Value totalAvailable;
								print batchBR.TotalAvailable(inAccount.CurrencyCodeAsText).ToDisplayFormat() totalAvailableFormatted;
								if(batchBR.HasAgents())
								{{
									allAgentsBRR = marketplace.Batches.SearchAgentBatch('{fullName}', ready.BatchNumber).AllAgents();
									for(agent : allAgentsBRR)
									{{
										currentAgentBRR = agent;
										print currentAgentBRR.Name agentName;
										agentOutAccountBRR = currentAgentBRR.ReceptionAccount(inAccount.CurrencyCodeAsText);
										print agentOutAccountBRR.CurrencyCodeAsText currencyCode;
										print agentOutAccountBRR.Available.Value available;
										print agentOutAccountBRR.Available.ToDisplayFormat() availableFormatted;
										print agentOutAccountBRR.LockedAccumulated.Value locked;
										print agentOutAccountBRR.LockedAccumulated.ToDisplayFormat() lockedFormatted;
										print agentOutAccountBRR.SpendAccumulated.Value spend;
										print agentOutAccountBRR.SpendAccumulated.ToDisplayFormat() spendFormatted;

										agentInAccountBRR = currentAgentBRR.ReceptionAccount(inAccount.CurrencyCodeAsText);
										print agentInAccountBRR.CurrencyCodeAsText currencyCode;
										print agentInAccountBRR.Available.Value available;
										print agentInAccountBRR.Available.ToDisplayFormat() availableFormatted;
									}}
								}}
							}}
						}}
					}}
					for(open : marketplace.Batches.BatchesOpen({date}))
					{{
						print open.FullName fullName;
						print open.BatchNumber batchNumber;
						print open.Description description;
						print open.CreationDate creationDate;
						print open.ScheduledDate scheduledDate;
						if(open.HasClosedDate())
						{{
							print open.CloseDate closedDate;
						}}
						else
						{{
							print '-' closedDate;
						}}
						print open.CreatedBy createdBy;
						print 'Open' status;
						print open.OpenDate openedDate;
						pathBO = open.TailOFFullName('{fullName}');

						if(open.ExistAgentBatch(pathBO))
						{{
							batchBO = marketplace.Batches.SearchAgentBatch('{fullName}', open.BatchNumber);
							print batchBO.Level level;
							print batchBO.Name agentName;
							print batchBO.FullName fullName;
							print batchBO.CreationDate creationDate;
							print batchBO.IsInternalAgent isAnEntity;
							print batchBO.IsFinalAgent isAnAgent;
							print batchBO.HasAgents() hasAgents;
							print batchBO.HasSaleAmountInAnyCurrency hasFunds;
							for(outAccount : batchBO.SelfSaleAccounts())
							{{
								print outAccount.CurrencyCodeAsText currencyCode;
								print outAccount.Initial.Value initial;
								print outAccount.Initial.ToDisplayFormat() initialFormatted;
								print outAccount.Available.Value pending;
								print outAccount.Available.ToDisplayFormat() pendingFormatted;
								print outAccount.LockedAccumulated.Value locked;
								print outAccount.LockedAccumulated.ToDisplayFormat() lockedFormatted;
								print outAccount.SpendAccumulated.Value spend;
								print outAccount.SpendAccumulated.ToDisplayFormat() spendFormatted;

								print batchBO.TotalAssigned(outAccount.CurrencyCodeAsText).Value totalAssigned;
								print batchBO.TotalAssigned(outAccount.CurrencyCodeAsText).ToDisplayFormat() totalAssignedFormatted;
								if(batchBO.HasAgents())
								{{
									allAgentsBOS = marketplace.Batches.SearchAgentBatch('{fullName}', open.BatchNumber).AllAgents();
									for(agent : allAgentsBOS)
									{{
										currentAgentBOS = agent;
										print currentAgentBOS.Name agentName;
										agentOutAccountBOS = currentAgentBOS.SaleAccount(outAccount.CurrencyCodeAsText);
										print agentOutAccountBOS.CurrencyCodeAsText currencyCode;
										print agentOutAccountBOS.Initial.Value initial;
										print agentOutAccountBOS.Initial.ToDisplayFormat() initialFormatted;
										print agentOutAccountBOS.Available.Value available;
										print agentOutAccountBOS.Available.ToDisplayFormat() availableFormatted;
										print agentOutAccountBOS.LockedAccumulated.Value locked;
										print agentOutAccountBOS.LockedAccumulated.ToDisplayFormat() lockedFormatted;
										print agentOutAccountBOS.SpendAccumulated.Value spend;
										print agentOutAccountBOS.SpendAccumulated.ToDisplayFormat() spendFormatted;

										agentInAccountBOS = currentAgentBOS.SaleAccount(outAccount.CurrencyCodeAsText);
										print agentInAccountBOS.CurrencyCodeAsText currencyCode;
										print agentInAccountBOS.Available.Value available;
										print agentInAccountBOS.Available.ToDisplayFormat() availableFormatted;
									}}
								}}
							}}
							for(inAccount : batchBO.SelfReceptionAccounts())
							{{
								print inAccount.CurrencyCodeAsText currencyCode;
								print inAccount.Available.Value available;
								print inAccount.Available.ToDisplayFormat() availableFormatted;
								print batchBO.TotalAvailable(inAccount.CurrencyCodeAsText).Value totalAvailable;
								print batchBO.TotalAvailable(inAccount.CurrencyCodeAsText).ToDisplayFormat() totalAvailableFormatted;
								if(batchBO.HasAgents())
								{{
									allAgentsBOR = marketplace.Batches.SearchAgentBatch('{fullName}', open.BatchNumber).AllAgents();
									for(agent : allAgentsBOR)
									{{
										currentAgentBOR = agent;
										print currentAgentBOR.Name agentName;
										agentOutAccountBOR = currentAgentBOR.ReceptionAccount(inAccount.CurrencyCodeAsText);
										print agentOutAccountBOR.CurrencyCodeAsText currencyCode;
										print agentOutAccountBOR.Available.Value available;
										print agentOutAccountBOR.Available.ToDisplayFormat() availableFormatted;
										print agentOutAccountBOR.LockedAccumulated.Value locked;
										print agentOutAccountBOR.LockedAccumulated.ToDisplayFormat() lockedFormatted;
										print agentOutAccountBOR.SpendAccumulated.Value spend;
										print agentOutAccountBOR.SpendAccumulated.ToDisplayFormat() spendFormatted;

										agentInAccountBOR = currentAgentBOR.ReceptionAccount(inAccount.CurrencyCodeAsText);
										print agentInAccountBOR.CurrencyCodeAsText currencyCode;
										print agentInAccountBOR.Available.Value available;
										print agentInAccountBOR.Available.ToDisplayFormat() availableFormatted;
									}}
								}}
							}}
						}}
					}}
					for(closed : marketplace.Batches.BatchesClosed({date}))
					{{
						print closed.FullName fullName;
						print closed.BatchNumber batchNumber;
						print closed.Description description;
						print closed.CreationDate creationDate;
						print closed.ScheduledDate scheduledDate;
						if(closed.HasClosedDate())
						{{
							print closed.CloseDate closedDate;
						}}
						else
						{{
							print '-' closedDate;
						}}
						print closed.CreatedBy createdBy;
						print 'Closed' status;
						batchBC = marketplace.Batches.SearchAgentBatch('{fullName}', closed.BatchNumber);
						print batchBC.Level level;
						print batchBC.Name agentName;
						print batchBC.FullName fullName;
						print batchBC.CreationDate creationDate;
						print batchBC.IsInternalAgent isAnEntity;
						print batchBC.IsFinalAgent isAnAgent;
						print batchBC.HasAgents() hasAgents;
						for(outAccount : batchBC.SelfSaleAccounts())
						{{
							print outAccount.CurrencyCodeAsText currencyCode;
							print outAccount.Initial.Value initial;
							print outAccount.Initial.ToDisplayFormat() initialFormatted;
							print outAccount.Available.Value pending;
							print outAccount.Available.ToDisplayFormat() pendingFormatted;
							print outAccount.LockedAccumulated.Value locked;
							print outAccount.LockedAccumulated.ToDisplayFormat() lockedFormatted;
							print outAccount.SpendAccumulated.Value spend;
							print outAccount.SpendAccumulated.ToDisplayFormat() spendFormatted;

							print batchBC.TotalAssigned(outAccount.CurrencyCodeAsText).Value totalAssigned;
							print batchBC.TotalAssigned(outAccount.CurrencyCodeAsText).ToDisplayFormat() totalAssignedFormatted;
							if(batchBC.HasAgents())
							{{
								allAgentsBCS = marketplace.Batches.SearchAgentBatch('{fullName}', closed.BatchNumber).AllAgents();
								for(agent : allAgentsBCS)
								{{
									currentAgentBCS = agent;
									print currentAgentBCS.Name agentName;
									agentOutAccountBCS = currentAgentBCS.SaleAccount(outAccount.CurrencyCodeAsText);
									print agentOutAccountBCS.CurrencyCodeAsText currencyCode;
									print agentOutAccountBCS.Initial.Value initial;
									print agentOutAccountBCS.Initial.ToDisplayFormat() initialFormatted;
									print agentOutAccountBCS.Available.Value available;
									print agentOutAccountBCS.Available.ToDisplayFormat() availableFormatted;
									print agentOutAccountBCS.LockedAccumulated.Value locked;
									print agentOutAccountBCS.LockedAccumulated.ToDisplayFormat() lockedFormatted;
									print agentOutAccountBCS.SpendAccumulated.Value spend;
									print agentOutAccountBCS.SpendAccumulated.ToDisplayFormat() spendFormatted;

									agentInAccountBCS = currentAgentBCS.SaleAccount(outAccount.CurrencyCodeAsText);
									print agentInAccountBCS.CurrencyCodeAsText currencyCode;
									print agentInAccountBCS.Available.Value available;
									print agentInAccountBCS.Available.ToDisplayFormat() availableFormatted;
								}}
							}}
						}}
						for(inAccount : batchBC.SelfReceptionAccounts())
						{{
							print inAccount.CurrencyCodeAsText currencyCode;
							print inAccount.Available.Value available;
							print inAccount.Available.ToDisplayFormat() availableFormatted;
							print batchBC.TotalAvailable(inAccount.CurrencyCodeAsText).Value totalAvailable;
							print batchBC.TotalAvailable(inAccount.CurrencyCodeAsText).ToDisplayFormat() totalAvailableFormatted;
							if(batchBC.HasAgents())
							{{
								allAgentsBCR = marketplace.Batches.SearchAgentBatch('{fullName}', closed.BatchNumber).AllAgents();
								for(agent : allAgentsBCR)
								{{
									currentAgentBCR = agent;
									print currentAgentBCR.Name agentName;
									agentOutAccountBCR = currentAgentBCR.ReceptionAccount(inAccount.CurrencyCodeAsText);
									print agentOutAccountBCR.CurrencyCodeAsText currencyCode;
									print agentOutAccountBCR.Available.Value available;
									print agentOutAccountBCR.Available.ToDisplayFormat() availableFormatted;
									print agentOutAccountBCR.LockedAccumulated.Value locked;
									print agentOutAccountBCR.LockedAccumulated.ToDisplayFormat() lockedFormatted;
									print agentOutAccountBCR.SpendAccumulated.Value spend;
									print agentOutAccountBCR.SpendAccumulated.ToDisplayFormat() spendFormatted;


									agentInAccountBCR = currentAgentBCR.ReceptionAccount(inAccount.CurrencyCodeAsText);
									print agentInAccountBCR.CurrencyCodeAsText currencyCode;
									print agentInAccountBCR.Available.Value available;
									print agentInAccountBCR.Available.ToDisplayFormat() availableFormatted;
								}}
							}}
						}}
					}}
					for(verified : marketplace.Batches.BatchesVerified({date}))
					{{
						print verified.FullName fullName;
						print verified.BatchNumber batchNumber;
						print verified.Description description;
						print verified.CreationDate creationDate;
						print verified.ScheduledDate scheduledDate;
						if(verified.HasClosedDate())
						{{
							print verified.CloseDate closedDate;
						}}
						else
						{{
							print '-' closedDate;
						}}
						print verified.CreatedBy createdBy;
						print 'Verified' status;
						batchBV = marketplace.Batches.SearchAgentBatch('{fullName}', verified.BatchNumber);
						print batchBV.Level level;
						print batchBV.Name agentName;
						print batchBV.FullName fullName;
						print batchBV.CreationDate creationDate;
						print batchBV.IsInternalAgent isAnEntity;
						print batchBV.IsFinalAgent isAnAgent;
						print batchBV.HasAgents() hasAgents;
						for(outAccount : batchBV.SelfSaleAccounts())
						{{
							print outAccount.CurrencyCodeAsText currencyCode;
							print outAccount.Initial.Value initial;
							print outAccount.Initial.ToDisplayFormat() initialFormatted;
							print outAccount.Available.Value pending;
							print outAccount.Available.ToDisplayFormat() pendingFormatted;
							print outAccount.LockedAccumulated.Value locked;
							print outAccount.LockedAccumulated.ToDisplayFormat() lockedFormatted;
							print outAccount.SpendAccumulated.Value spend;
							print outAccount.SpendAccumulated.ToDisplayFormat() spendFormatted;

							print batchBV.TotalAssigned(outAccount.CurrencyCodeAsText).Value totalAssigned;
							print batchBV.TotalAssigned(outAccount.CurrencyCodeAsText).ToDisplayFormat() totalAssignedFormatted;
							if(batchBV.HasAgents())
							{{
								allAgentsBVS = marketplace.Batches.SearchAgentBatch('{fullName}', verified.BatchNumber).AllAgents();
								for(agent : allAgentsBVS)
								{{
									currentAgentBVS = agent;
									print currentAgentBVS.Name agentName;
									agentOutAccountBVS = currentAgentBVS.SaleAccount(outAccount.CurrencyCodeAsText);
									print agentOutAccountBVS.CurrencyCodeAsText currencyCode;
									print agentOutAccountBVS.Initial.Value initial;
									print agentOutAccountBVS.Initial.ToDisplayFormat() initialFormatted;
									print agentOutAccountBVS.Available.Value available;
									print agentOutAccountBVS.Available.ToDisplayFormat() availableFormatted;
									print agentOutAccountBVS.LockedAccumulated.Value locked;
									print agentOutAccountBVS.LockedAccumulated.ToDisplayFormat() lockedFormatted;
									print agentOutAccountBVS.SpendAccumulated.Value spend;
									print agentOutAccountBVS.SpendAccumulated.ToDisplayFormat() spendFormatted;

									agentInAccountBVS = currentAgentBVS.SaleAccount(outAccount.CurrencyCodeAsText);
									print agentInAccountBVS.CurrencyCodeAsText currencyCode;
									print agentInAccountBVS.Available.Value available;
									print agentInAccountBVS.Available.ToDisplayFormat() availableFormatted;
								}}
							}}
						}}
						for(inAccount : batchBV.SelfReceptionAccounts())
						{{
							print inAccount.CurrencyCodeAsText currencyCode;
							print inAccount.Available.Value available;
							print inAccount.Available.ToDisplayFormat() availableFormatted;
							print batchBV.TotalAvailable(inAccount.CurrencyCodeAsText).Value totalAvailable;
							print batchBV.TotalAvailable(inAccount.CurrencyCodeAsText).ToDisplayFormat() totalAvailableFormatted;
							if(batchBV.HasAgents())
							{{
								allAgentsBVR = marketplace.Batches.SearchAgentBatch('{fullName}', verified.BatchNumber).AllAgents();
								for(agent : allAgentsBVR)
								{{
									currentAgentBVR = agent;
									print currentAgentBVR.Name agentName;
									agentOutAccountBVR = currentAgentBVR.ReceptionAccount(inAccount.CurrencyCodeAsText);
									print agentOutAccountBVR.CurrencyCodeAsText currencyCode;
									print agentOutAccountBVR.Available.Value available;
									print agentOutAccountBVR.Available.ToDisplayFormat() availableFormatted;
									print agentOutAccountBVR.LockedAccumulated.Value locked;
									print agentOutAccountBVR.LockedAccumulated.ToDisplayFormat() lockedFormatted;
									print agentOutAccountBVR.SpendAccumulated.Value spend;
									print agentOutAccountBVR.SpendAccumulated.ToDisplayFormat() spendFormatted;

									agentInAccountBVR = currentAgentBVR.ReceptionAccount(inAccount.CurrencyCodeAsText);
									print agentInAccountBVR.CurrencyCodeAsText currencyCode;
									print agentInAccountBVR.Available.Value available;
									print agentInAccountBVR.Available.ToDisplayFormat() availableFormatted;
								}}
							}}
						}}
					}}
				}}
            ");
			}

			return result;
		}

		[HttpGet("api/exchange/transaction/batch/{batchNumber}/agencies")]
		[Authorize(Roles = "c34")]
		public async Task<IActionResult> AgenciesFromMarketplaceBatchAsync(int batchNumber, string fullName)
		{
			if (batchNumber <= 0) return NotFound($"Parameter {nameof(batchNumber)} is required");
			if (string.IsNullOrWhiteSpace(fullName)) return NotFound($"Parameter {nameof(fullName)} is required");

			IActionResult result;
			if (fullName.ToLower().Trim() == "all")
			{
				result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
					{{
						allAgents = marketplace.Batches.FindMarketplaceBatch({batchNumber}).AllAgencies();
						for(agent : allAgents)
						{{
							print agent.Level level;
							print agent.Name agentName;
							print agent.FullName fullName;
							print agent.CreationDate creationDate;
							print agent.IsInternalAgent isAnEntity;
							print agent.IsFinalAgent isAnAgent;
							print 'Ready' status;
							for(outAccount : agent.SaleAccounts())
							{{
								print outAccount.CurrencyCodeAsText currencyCode;
								print outAccount.Initial.Value initial;
								print outAccount.Initial.ToDisplayFormat() initialFormatted;
								print outAccount.Available.Value available;
								print outAccount.Available.ToDisplayFormat() availableFormatted;
								print outAccount.Locked.Value locked;
								print outAccount.Locked.ToDisplayFormat() lockedFormatted;
							}}
							for(inAccount : agent.ReceptionAccounts())
							{{
								print inAccount.CurrencyCodeAsText currencyCode;
								print inAccount.Available.Value available;
								print inAccount.Available.ToDisplayFormat() availableFormatted;
							}}
						}}
					}}
				");
			}
			else
			{
				result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
					{{
						agent = marketplace.Batches.SearchAgentBatch({fullName}, {batchNumber});
						print agent.Level level;
						print agent.Name agentName;
						print agent.FullName fullName;
						print agent.CreationDate creationDate;
						print agent.IsInternalAgent isAnEntity;
						print agent.IsFinalAgent isAnAgent;
						print 'Ready' status;
						for(outAccount : agent.SaleAccounts())
						{{
							print outAccount.CurrencyCodeAsText currencyCode;
							print outAccount.Initial.Value initial;
							print outAccount.Initial.ToDisplayFormat() initialFormatted;
							print outAccount.Available.Value available;
							print outAccount.Available.ToDisplayFormat() availableFormatted;
							print outAccount.Locked.Value locked;
							print outAccount.Locked.ToDisplayFormat() lockedFormatted;
						}}
						for(inAccount : agent.ReceptionAccounts())
						{{
							print inAccount.CurrencyCodeAsText currencyCode;
							print inAccount.Available.Value available;
							print inAccount.Available.ToDisplayFormat() availableFormatted;
						}}
					}}
				");
			}

			return result;
		}

		[HttpGet("api/exchange/transaction/batch/{batchNumber}/agents")]
		[Authorize(Roles = "c35")]
		public async Task<IActionResult> AgentsFromMarketplaceBatchAsync(int batchNumber, string fullName)
		{
			if (batchNumber <= 0) return NotFound($"Parameter {nameof(batchNumber)} is required");
			if (string.IsNullOrWhiteSpace(fullName)) return NotFound($"Parameter {nameof(fullName)} is required");

			IActionResult result;
			if (fullName.ToLower().Trim() == "all")
			{
				result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
					{{
						allAgents = marketplace.Batches.FindMarketplaceBatch({batchNumber}).AllAgents();
						for(agent : allAgents)
						{{
							print agent.Level level;
							print agent.Name agentName;
							print agent.FullName fullName;
							print agent.CreationDate creationDate;
							print agent.IsInternalAgent isAnEntity;
							print agent.IsFinalAgent isAnAgent;
							print 'Ready' status;
							for(outAccount : agent.SaleAccounts())
							{{
								print outAccount.CurrencyCodeAsText currencyCode;
								print outAccount.Initial.Value initial;
								print outAccount.Initial.ToDisplayFormat() initialFormatted;
								print outAccount.Available.Value available;
								print outAccount.Available.ToDisplayFormat() availableFormatted;
								print outAccount.Locked.Value locked;
								print outAccount.Locked.ToDisplayFormat() lockedFormatted;
							}}
							for(inAccount : agent.ReceptionAccounts())
							{{
								print inAccount.CurrencyCodeAsText currencyCode;
								print inAccount.Available.Value available;
								print inAccount.Available.ToDisplayFormat() availableFormatted;
							}}
						}}
					}}
				");
			}
			else
			{
				result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
					{{
						agent = marketplace.Batches.SearchAgentBatch({fullName}, {batchNumber});
						print agent.Level level;
						print agent.Name agentName;
						print agent.FullName fullName;
						print agent.CreationDate creationDate;
						print agent.IsInternalAgent isAnEntity;
						print agent.IsFinalAgent isAnAgent;
						print 'Ready' status;
						for(outAccount : agent.SaleAccounts())
						{{
							print outAccount.CurrencyCodeAsText currencyCode;
							print outAccount.Initial.Value initial;
							print outAccount.Initial.ToDisplayFormat() initialFormatted;
							print outAccount.Available.Value available;
							print outAccount.Available.ToDisplayFormat() availableFormatted;
							print outAccount.Locked.Value locked;
							print outAccount.Locked.ToDisplayFormat() lockedFormatted;
						}}
						for(inAccount : agent.ReceptionAccounts())
						{{
							print inAccount.CurrencyCodeAsText currencyCode;
							print inAccount.Available.Value available;
							print inAccount.Available.ToDisplayFormat() availableFormatted;
						}}
					}}
				");
			}

			return result;
		}

		[HttpGet("api/exchange/transaction/batch/{batchNumber}/transactions")]
		[Authorize(Roles = "c36")]
		public async Task<IActionResult> BatchTransactionsForAgentAsync(string fullName, int batchNumber)
		{
			if (string.IsNullOrWhiteSpace(fullName)) return NotFound($"Parameter {nameof(fullName)} is required");
			if (batchNumber <= 0) return NotFound($"Parameter {nameof(batchNumber)} is required");

			var result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
				{{
					agent = marketplace.SearchAgentBatch({fullName});
					batch = agent.SearchBatchTransactions({batchNumber});
					print agent.Name agentName;
					print batch.TransactionsNumber transactionsNumber;
					print batch.CurrentStatusAsStr status;
					print batch.CreationDate creationDate;
					for(outAccount : agent.SaleAccounts())
					{{
						print outAccount.CurrencyCodeAsText currencyCode;
						print outAccount.Initial.Value initial;
						print outAccount.Initial.ToDisplayFormat() initialFormatted;
						print outAccount.Available.Value available;
						print outAccount.Available.ToDisplayFormat() availableFormatted;
						print outAccount.Locked.Value locked;
						print outAccount.Locked.ToDisplayFormat() lockedFormatted;
					}}
					for(inAccount : agent.ReceptionAccounts())
					{{
						print inAccount.CurrencyCodeAsText currencyCode;
						print inAccount.Available.Value available;
						print inAccount.Available.ToDisplayFormat() availableFormatted;
					}}
					for(movement : batch.Movements())
					{{
						print movement.MovementDate date;
						print movement.TransactionNumber transactionNumber;
						print movement.CurrencyAsText currencyCode;
						print movement.Total.Value amount;
						print movement.Total.ToDisplayFormat() amountFormatted;
						print movement.MovementType type;
					}}
                }}
            ");

			return result;
		}

		[HttpPost("api/exchange/transaction/batch/creation")]
		[Authorize(Roles = "c26")]
		public async Task<IActionResult> CreateMarketplaceBatchAsync([FromBody] BatchDefinitionBody body)
		{
			if (body == null) return BadRequest($"Body is required");
			if (string.IsNullOrWhiteSpace(body.ScheduledDate)) return BadRequest($"{nameof(body.ScheduledDate)} is required");
			if (string.IsNullOrWhiteSpace(body.Description)) return BadRequest($"{nameof(body.Description)} is required");
			if (body.ListOfCurrencies == null) return BadRequest($"Currencies are required");

			string employeeName = Security.UserName(HttpContext);
			if (!DateTime.TryParseExact(body.ScheduledDate, "M/d/yyyy H:m:s", Integration.CultureInfoEnUS, DateTimeStyles.None, out DateTime scheduledDate)) return BadRequest($"{nameof(body.ScheduledDate)} does not have a valid format");
			string description = Validator.StringEscape(body.Description);

			StringBuilder initialAmountScript = new StringBuilder();

			foreach (var currency in body.ListOfCurrencies)
			{
				if (currency.Amount != 0)
				{
					initialAmountScript.Append($"marketplaceBatch.InitialAmount(Currency('{currency.CurrencyCode}',{currency.Amount}), '{employeeName}');");
				}
			}

			var result = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, $@"
				{{
					Eval('batchNumber =' + marketplace.Batches.IdentityBatchNumber + ';');
					marketplaceBatch = marketplace.AddNewBatch(Now, {body.ScheduledDate}, batchNumber, '{employeeName}', '{description}');
					{initialAmountScript}
				}}
			");

			return result;
		}

		[HttpPost("api/exchange/transaction/batch/redistribution")]
		[Authorize(Roles = "c27")]
		public async Task<IActionResult> RedistributeAmountMarketplaceBatchAsync([FromBody] BatchRedistributionBody body)
		{
			if (body == null) return BadRequest($"Body is required");
			if (string.IsNullOrWhiteSpace(body.AgentBatchName)) return BadRequest($"{nameof(body.AgentBatchName)} is required");
			if (body.BatchNumber <= 0) return BadRequest($"{nameof(body.BatchNumber)} is required");
			if (body.ListOfCurrencies == null) return BadRequest($"Currencies are required");

			string employeeName = Security.UserName(HttpContext);

			StringBuilder canAssignFundsScript = new StringBuilder();
			StringBuilder receiveAmountScript = new StringBuilder();
			foreach (var currency in body.ListOfCurrencies)
			{
				if (currency.Amount != 0)
				{
					canAssignFundsScript.Append("Check(agencyBatch.CanAssignFunds(Currency('")
						.Append(currency.CurrencyCode)
						.Append("', ")
						.Append(currency.Amount)
						.Append("))) Error 'Batch ")
						.Append(body.BatchNumber)
						.Append(" cannot assign ")
						.Append(currency.CurrencyCode)
						.Append(currency.Amount)
						.Append(" funds.';")
						.AppendLine();

					receiveAmountScript.Append("batch.ReceiveAmount(Currency('")
						  .Append(currency.CurrencyCode)
						  .Append("',")
						  .Append(currency.Amount)
						  .Append("), Dollar(")
						  .Append(currency.CostAmount)
						  .Append("), '")
						  .Append(employeeName)
						  .Append("');")
						  .AppendLine();
				}
			}

			var result = await ExchangeManagerAPI.ExchangeManager.PerformChkThenCmdAsync(HttpContext, $@"
				{{
					existsBatch = marketplace.Batches.ExistsMarketplaceBatch({body.BatchNumber});
					Check(existsBatch) Error 'Batch {body.BatchNumber} does not exist.';
					if (existsBatch)
					{{
						marketplaceBatch = marketplace.FindMarketplaceBatch({body.BatchNumber});
						Check(!marketplaceBatch.ExistsAgent('{body.AgentBatchName}')) Error 'Agent {body.AgentBatchName} already exist.';
						{canAssignFundsScript}
					}}
				}}
			", $@"
				{{
					marketplaceBatch = marketplace.FindMarketplaceBatch({body.BatchNumber});
					batch = marketplaceBatch.AddAgent('{body.AgentBatchName}', Now);
					{receiveAmountScript}
				}}
			");

			return result;
		}

		[HttpPost("api/exchange/transaction/batch/allocation")]
		[Authorize(Roles = "c28")]
		public async Task<IActionResult> AllocateAmountMarketplaceBatchAsync([FromBody] BatchAllocationBody body)
		{
			if (body == null) return BadRequest($"Body is required");
			if (string.IsNullOrWhiteSpace(body.AgentBatchName)) return BadRequest($"{nameof(body.AgentBatchName)} is required");
			if (body.BatchNumber <= 0) return BadRequest($"{nameof(body.BatchNumber)} is required");
			if (string.IsNullOrWhiteSpace(body.BatchFullName)) return BadRequest($"{nameof(body.BatchFullName)} is required");
			if (body.ListOfCurrencies == null) return BadRequest($"Currencies are required");

			string employeeName = Security.UserName(HttpContext);

			StringBuilder canAssignFundsScript = new StringBuilder();
			StringBuilder assignFundsScript = new StringBuilder();
			foreach (var currency in body.ListOfCurrencies)
			{
				if (currency.Amount != 0)
				{
					canAssignFundsScript.Append("Check(agencyBatch.CanAssignFunds(Currency('")
						.Append(currency.CurrencyCode)
						.Append("', ")
						.Append(currency.Amount)
						.Append("))) Error 'Agent batch ")
						.Append(body.BatchFullName)
						.Append(" cannot assign ")
						.Append(currency.CurrencyCode)
						.Append(currency.Amount)
						.Append(" funds.';")
						.AppendLine();

					assignFundsScript.Append("agencyBatch.AssignFunds('")
						.Append(body.AgentBatchName)
						.Append("', ")
						.Append(body.BatchNumber)
						.Append(", Currency('")
						.Append(currency.CurrencyCode)
						.Append("', ")
						.Append(currency.Amount)
						.Append("), '")
						.Append(employeeName)
						.Append("', Now);")
						.AppendLine();
				}
			}

			var result = await ExchangeManagerAPI.ExchangeManager.PerformChkThenCmdAsync(HttpContext, $@"
				{{
					existBatch = marketplace.Batches.ExistAgentBatch('{body.BatchFullName}');
					Check(existBatch) Error 'Agent batch {body.BatchFullName} does not exist.';
					if (existBatch)
					{{
						existsAgentBatch = marketplace.Batches.ExistsAgentBatch('{body.BatchFullName}', {body.BatchNumber});
						Check(existsAgentBatch) Error 'Batch {body.BatchNumber} does not exist.';
						if (existsAgentBatch)
						{{
							agencyBatch = marketplace.SearchAgentBatch('{body.BatchFullName}', {body.BatchNumber});
							Check(!agencyBatch.ExistsAgent('{body.AgentBatchName}')) Error 'Agent {body.AgentBatchName} already exist.';
							{canAssignFundsScript}
						}}
					}}
				}}
			", $@"
				{{
					agencyBatch = marketplace.SearchAgentBatch('{body.BatchFullName}', {body.BatchNumber});
					batch = agencyBatch.AddAgent('{body.AgentBatchName}', Now);
					{assignFundsScript}
				}}
			");

			return result;
		}

		[HttpPost("api/exchange/transaction/batch/owner/open")]
		[Authorize(Roles = "c29")]
		public async Task<IActionResult> MarkBatchAsOpenAsync([FromBody] OpenBatchBody body)
		{
			if (body == null) return BadRequest($"Body is required");
			if (string.IsNullOrEmpty(body.Path)) return BadRequest($"{nameof(body.Path)} is required");

			string employeeName = Security.UserName(HttpContext);

			var result = await ExchangeManagerAPI.ExchangeManager.PerformChkThenCmdAsync(HttpContext, $@"
				{{
					existBatch = marketplace.Batches.ExistAgentBatch('{body.Path}');
					Check(existBatch) Error 'Agent batch {body.Path} does not exist.';
					if (existBatch)
					{{
						agentBatch = marketplace.SearchAgentBatch('{body.Path}');
						Check(!agentBatch.ItsOpen) Error 'Agent batch {body.Path} is already opened.';
					}}
				}}
			", $@"
				{{
					marketplace.SearchAgentBatch('{body.Path}').Open(itIsThePresent, Now, '{employeeName}');
				}}
			");

			return result;
		}

		[HttpPost("api/exchange/transaction/batch/open")]
		[Authorize(Roles = "c30")]
		public async Task<IActionResult> OpenBatchAsync()
		{
			string path = Security.UserPath(HttpContext);
			string employeeName = Security.UserName(HttpContext);
			string batchPath = path + '/' + employeeName;

			var result = await ExchangeManagerAPI.ExchangeManager.PerformChkThenCmdAsync(HttpContext, $@"
				{{
					existBatch = marketplace.Batches.ExistAgentBatch('{batchPath}');
					Check(existBatch) Error 'Agent batch {batchPath} does not exist.';
					if (existBatch)
					{{
						agentBatch = marketplace.SearchAgentBatch('{batchPath}');
						Check(!agentBatch.ItsOpen) Error 'Agent batch {batchPath} is already opened.';
						Check(agentBatch.CanOpen()) Error 'Agent batch {batchPath} cannot be opened.';
					}}
				}}
			", $@"
				{{
					agentBatch = marketplace.SearchAgentBatch('{batchPath}');
					agentBatch.Open(itIsThePresent, Now, '{employeeName}');
				}}
			");

			return result;
		}

		[HttpPost("api/exchange/transaction/batch/owner/close")]
		[Authorize(Roles = "devops,c69")]
		public async Task<IActionResult> MarkBatchAsOpenAsync([FromBody] CloseBatchBody body)
		{
			if (body == null) return BadRequest($"Body is required");
			if (string.IsNullOrWhiteSpace(body.Path)) return BadRequest($"{nameof(body.Path)} is required");

			string employeeName = Security.UserName(HttpContext);

			var result = await ExchangeManagerAPI.ExchangeManager.PerformChkThenCmdAsync(HttpContext, $@"
				{{
					existBatch = marketplace.Batches.ExistAgentBatch('{body.Path}');
					Check(existBatch) Error 'Agent batch {body.Path} does not exist.';
					if (existBatch)
					{{
						agentBatch = marketplace.SearchAgentBatch('{body.Path}');
						Check(!agentBatch.IsClosed) Error 'Agent batch {body.Path} is already closed.';
						Check(!agentBatch.HasUnClosedTransaction()) Error 'Agent batch {body.Path} has children with unclosed transactions.';
					}}
				}}
			", $@"
				{{
					marketplace.SearchAgentBatch('{body.Path}').Close(itIsThePresent, Now, '{employeeName}');
				}}
			");

			return result;
		}

		[HttpPost("api/exchange/transaction/batch/close")]
		[Authorize(Roles = "c86")]
		public async Task<IActionResult> MarkBatchAsCloseAsync()
		{
			string path = Security.UserPath(HttpContext);
			string employeeName = Security.UserName(HttpContext);
			string batchPath = path + '/' + employeeName;

			var result = await ExchangeManagerAPI.ExchangeManager.PerformChkThenCmdAsync(HttpContext, $@"
				{{
					existBatch = marketplace.Batches.ExistAgentBatch('{batchPath}');
					Check(existBatch) Error 'Agent batch {batchPath} does not exist.';
					if (existBatch)
					{{
						agentBatch = marketplace.SearchAgentBatch('{batchPath}');
						Check(!agentBatch.IsClosed) Error 'Agent batch {batchPath} is already closed.';
						Check(!agentBatch.HasUnClosedTransaction()) Error 'Agent batch {batchPath} has children with unclosed transactions.';
					}}
				}}
			", $@"
				{{
					marketplace.SearchAgentBatch('{batchPath}').Close(itIsThePresent, Now, '{employeeName}');
				}}
			");

			return result;
		}

		[HttpPost("api/exchange/transaction/batch/verify")]
		[Authorize(Roles = "devops,c68")]
		public async Task<IActionResult> MarkBatchAsVerifyAsync([FromBody] CloseBatchBody body)
		{
			if (body == null) return BadRequest($"Body is required");
			if (string.IsNullOrWhiteSpace(body.Path)) return BadRequest($"{nameof(body.Path)} is required");

			string employeeName = Security.UserName(HttpContext);

			var result = await ExchangeManagerAPI.ExchangeManager.PerformChkThenCmdAsync(HttpContext, $@"
				{{
					existBatch = marketplace.Batches.ExistAgentBatch('{body.Path}');
					Check(existBatch) Error 'Agent batch {body.Path} does not exist.';
					if (existBatch)
					{{
						agentBatch = marketplace.SearchAgentBatch('{body.Path}');
						Check(agentBatch.IsClosed) Error 'Agent batch {body.Path} is not closed.';
					}}
				}}
			", $@"
				{{
					marketplace.SearchAgentBatch('{body.Path}').Verify(itIsThePresent, Now, '{employeeName}');
				}}
			");

			return result;
		}

		[HttpGet("api/exchange/transaction/batches/verified")]
		[Authorize(Roles = "devops,c67")]
		public async Task<IActionResult> ListVerifiedBatchesAsync(DateTime startDate, DateTime endDate, int initialIndex, int amountOfRows)
		{
			if (startDate == default(DateTime)) return BadRequest($"{nameof(startDate)} is required");
			if (endDate == default(DateTime)) return BadRequest($"{nameof(endDate)} is required");
			if (startDate.Hour != 0 || startDate.Minute != 0 || startDate.Second != 0) return BadRequest($"{nameof(startDate)} {startDate} is not valid");
			if (endDate.Hour != 0 || endDate.Minute != 0 || endDate.Second != 0) return BadRequest($"{nameof(endDate)} {endDate} is not valid");

			var verifiedBatches = Consumers.Storage.ListVerifiedBatches(startDate, endDate, initialIndex, amountOfRows);
			var verifiedBatchesRatesDTO = verifiedBatches.MapToVerifiedbatchesDTO();
			return Ok(verifiedBatchesRatesDTO);
		}

		[HttpGet("api/exchange/transaction/batch/available/{batchNumber}")]
		[Authorize(Roles = "c28")]
		public async Task<IActionResult> PendingToBeAssignedAsync(int batchNumber, string fullName)
		{
			if (batchNumber <= 0) return BadRequest($"Parameter {nameof(batchNumber)} is required");

			IActionResult result;
			if (string.IsNullOrWhiteSpace(fullName))
			{
				result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
					{{
						batch = marketplace.Batches.FindMarketplaceBatch({batchNumber});
						for(outAccount : batch.InitialAccounts())
						{{
							print outAccount.CurrencyCodeAsText currencyCode;
							print outAccount.Available.Value pending;
							print outAccount.Available.ToDisplayFormat() pendingFormatted;
						}}
					}}
				");
			}
			else
			{
				result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
					{{
						batch = marketplace.Batches.SearchAgentBatch({fullName}, {batchNumber});
						for(outAccount : batch.SelfSaleAccounts())
						{{
							print outAccount.CurrencyCodeAsText currencyCode;
							print outAccount.Available.Value pending;
							print outAccount.Available.ToDisplayFormat() pendingFormatted;
						}}
					}}
				");
			}

			return result;
		}

		[DataContract(Name = "BatchDefinitionBody")]
		public class BatchDefinitionBody
		{
			[DataMember(Name = "currencyList")]
			public CurrencyList[] ListOfCurrencies { get; set; }
			[DataMember(Name = "description")]
			public string Description { get; set; }
			[DataMember(Name = "scheduledDate")]
			public string ScheduledDate { get; set; }
		}

		[DataContract(Name = "BatchRedistributionBody")]
		public class BatchRedistributionBody
		{
			[DataMember(Name = "currencyList")]
			public CurrencyList[] ListOfCurrencies { get; set; }
			[DataMember(Name = "agentBatchName")]
			public string AgentBatchName { get; set; }
			[DataMember(Name = "batchNumber")]
			public int BatchNumber { get; set; }
		}

		[DataContract(Name = "BatchAllocationBody")]
		public class BatchAllocationBody
		{
			[DataMember(Name = "currencyList")]
			public CurrencyList[] ListOfCurrencies { get; set; }
			[DataMember(Name = "agentBatchName")]
			public string AgentBatchName { get; set; }
			[DataMember(Name = "batchNumber")]
			public int BatchNumber { get; set; }
			[DataMember(Name = "batchFullName")]
			public string BatchFullName { get; set; }
		}

		[DataContract(Name = "OpenBatchBody")]
		public class OpenBatchBody
		{
			[DataMember(Name = "batchNumber")]
			public int BatchNumber { get; set; }
			[DataMember(Name = "batchFullName")]
			public string BatchFullName { get; set; }
			[DataMember(Name = "path")]
			public string Path { get; set; }
		}

		[DataContract(Name = "existBatch")]
		public class BatchExistence
		{
			[DataMember(Name = "existBatch")]
			public bool ExistBatch { get; set; }
		}

		[DataContract(Name = "ValidateOpen")]
		public class ValidateOpen
		{
			[DataMember(Name = "canOpen")]
			public bool CanOpen { get; set; }
		}

		[DataContract(Name = "closeBatchBody")]
		public class CloseBatchBody
		{
			[DataMember(Name = "path")]
			public string Path { get; set; }
		}

		[DataContract(Name = "currencyList")]
		public class CurrencyList
		{
			[DataMember(Name = "currencyCode")]
			public string CurrencyCode { get; set; }
			[DataMember(Name = "amount")]
			public decimal Amount { get; set; }
			[DataMember(Name = "costAmount")]
			public decimal CostAmount { get; set; }
		}

		[DataContract(Name = "BatchValidation")]
		public class BatchValidation
		{
			[DataMember(Name = "reason")]
			public string Reason { get; set; }
		}
	}
}
