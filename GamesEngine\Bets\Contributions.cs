﻿
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using GamesEngine.Business;

namespace GamesEngine.Bets
{
    internal sealed class Contributions
    {
        private readonly Dictionary<Player, decimal> contributions = new Dictionary<Player, decimal>();

        internal Contributions Add(Player player, decimal amount)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (amount <= 0) throw new GameEngineException("Amount must be greater than zero");
            decimal total = 0;
            if (contributions.TryGetValue(player, out total))
            {
                total = total + amount;
                contributions[player] = total;
            }
            else
            {
                contributions[player] = amount;
            }
            return this;
        }

        internal bool Participates (Player player)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            return contributions.ContainsKey(player);
        }

        internal decimal Total
        {
            get
            {
                decimal total = contributions.Values.Sum(contribution => contribution);
                return total;
            }
        }

        internal int CountOfPlayers()
        {
            int result = contributions.Count;
            return result;
        }

        internal void CancelContribution(Player player, decimal amount)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (amount <= 0) throw new GameEngineException("Amount must be greater than zero");
            decimal total = 0;
            if (contributions.TryGetValue(player, out total))
            {
                total = total - amount;
                if(total > 0)
                {
                    contributions[player] = total;
                }
                else
                {
                    contributions.Remove(player);
                }
            }
            else
                throw new GameEngineException($"There is not a contribution for player {player.Id} for this amount {amount}");
        }
    }
}
