﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using town.connectors.drivers;
using town.connectors.exception;
using static town.connectors.CustomSettings;
using static town.connectors.Variables;

[assembly: InternalsVisibleTo("GamesEngineTests")]
namespace town.connectors
{
    public sealed class CustomSettingsPerType
    {
        private Dictionary<string, CustomSettings> customSettingsPerType = new Dictionary<string, CustomSettings>();

        public void Add(Type type, CustomSettings dgscs)
        {
            customSettingsPerType.Add(type.FullName, dgscs);
        }

        internal CustomSettings Get(Type possibleType)
        {
            return customSettingsPerType[possibleType.FullName];
        }
    }

    public sealed class CustomSettings
    {
        private Dictionary<Variable, CustomSetting> fixedCustomSettings = new Dictionary<Variable, CustomSetting>();
        private Variables variables;

        private List<CustomSetting> _tempBeforePrepare;
        private CustomParameterCollection customParameterCollection;

        internal CustomParameterCollection CustomParameters
        {
            get
            {
                return customParameterCollection;
            }
        }
		public IEnumerable<CustomSetting> Settings 
        { 
            get 
            { 
                return _tempBeforePrepare; 
            }  
        }
        public bool AnyCustomSetting => _tempBeforePrepare != null && _tempBeforePrepare.Count != 0;
        public CustomSettings(Variables variables) 
        {
            this.variables = variables;
            customParameterCollection = new CustomParameterCollection(this);
        }

        public CustomSettings(Variables variables, int capacity)
        {
            this.variables = variables;
            customParameterCollection = new CustomParameterCollection(this, capacity);
        }

        public void Prepare()
        {
            if (_tempBeforePrepare == null) throw new ConnectorException("No parameter has been added.");

            customParameterCollection.Configure(_tempBeforePrepare.ToArray());
            _tempBeforePrepare = null;
        }

        public CustomSetting Get(DateTime now, string key)
        {
            bool changesApplied;
            return Get(now, key, out changesApplied);
        }

        public CustomSetting Get(DateTime now, string key, out bool changesApplied)
        {
            changesApplied = false;
            Variable variable;
            if (!variables.TryGetValue(key, out variable)) throw new ConnectorException($"{nameof(Variable)} {key} does not exists.");
            if (now == default(DateTime)) throw new ArgumentException(nameof(now));
            if (string.IsNullOrEmpty(key)) throw new ArgumentException(nameof(key));

            var custom = fixedCustomSettings[variable];
            changesApplied = custom.ApplyChangeUntil(now);

            custom.Now = now;

            return custom;
        }

        private CustomSetting Get(string key)
        {
            Variable variable;
            if (!variables.TryGetValue(key, out variable)) throw new ConnectorException($"{nameof(Variable)} {key} does not exists.");
            if (string.IsNullOrEmpty(key)) throw new ArgumentException(nameof(key));

            var custom = fixedCustomSettings[variable];

            return custom;
        }


        public RecordSet GetRecordSet()
        {
            RecordSet result = customParameterCollection.NextAvalaible();
            return result;
        }

        public CustomSetting AddVariableParameter(string key)
        {
            if (string.IsNullOrEmpty(key)) throw new ArgumentException(nameof(key));

            if (_tempBeforePrepare == null) _tempBeforePrepare = new List<CustomSetting>();

            CustomSetting valueAlreadyStored = new VariableCustomSetting(this, _tempBeforePrepare.Count(), key, null);
            _tempBeforePrepare.Add(valueAlreadyStored);

            return valueAlreadyStored;
        }
        public CustomSetting AddFixedParameter(DateTime now, string key, object value)
        {
            if (value == null) throw new ArgumentException(nameof(value));
            if (now == default(DateTime)) throw new ArgumentException(nameof(now));
            if (string.IsNullOrEmpty(key)) throw new ArgumentException(nameof(key));

            Variable variable;
            if (!variables.TryGetValue(key, out variable)) variable= variables.Create(key, value.GetType());

            if (variable.Type != value.GetType()) throw new ConnectorException($"{value.GetType()} is not valid");

            if (_tempBeforePrepare == null) _tempBeforePrepare = new List<CustomSetting>();

            CustomSetting valueAlreadyStored = null;
            if (fixedCustomSettings.TryGetValue(variable, out valueAlreadyStored))
            {
                valueAlreadyStored.Val = value;
            }
            else
            {
                valueAlreadyStored = new FixedCustomSetting(this, _tempBeforePrepare.Count(), key, value);
                fixedCustomSettings.Add(variable, valueAlreadyStored);
            }
            valueAlreadyStored.Now = now;

            _tempBeforePrepare.Add(valueAlreadyStored);

            return valueAlreadyStored;
        }

        public CustomSetting ChangeValueStartingOn(DateTime dateToApplyTheChange, string key, object value, string user)
        {
            if (value == null) throw new ArgumentException(nameof(value));
            if (dateToApplyTheChange == default(DateTime)) throw new ArgumentException(nameof(dateToApplyTheChange));
            if (string.IsNullOrEmpty(key)) throw new ArgumentException(nameof(key));

            CustomSetting valueAlreadyStored = Get(key);
            valueAlreadyStored.ChangeTo(dateToApplyTheChange, user, value);

            return valueAlreadyStored;
        }

        public int Count
        {
            get 
            {
                return fixedCustomSettings.Count;
            }
            
        }

        private int amountOfPendingChanges = 0;
        internal int AmountOfPendingChanges 
        {
            get
            { 
                return amountOfPendingChanges;
            }
            private set
            {
                if (value < 0) throw new ConnectorException($" {nameof(AmountOfPendingChanges)} ca not be negative.");
                amountOfPendingChanges = value;
            }
        }
        public bool ThereArePendingChanges { get { return AmountOfPendingChanges > 0; } }

        public abstract class CustomSetting
        {
            public string Key { get; private set; }
            private object val;
            private List<Change> changesSortedByDate = new List<Change> ();
            private List<Change> logs = new List<Change>();

            public override string ToString()
            {
                return $"{Key}:{val.ToString()}";
            }

            public CustomSetting(CustomSettings customSettings, int ordinalPosition, string key, bool isVariable, object val)
            {
                if (string.IsNullOrEmpty(key)) throw new ArgumentException(nameof(key));
                if (customSettings == null) throw new ArgumentException(nameof(customSettings));
                if ( !isVariable && val == null) throw new ArgumentException(nameof(val));

                this.ordinalPosition = ordinalPosition;
                this.Key = key;
                this.val = val;
                Version = 1;
                IsVariable = isVariable;
                CustomSettings=customSettings;
            }

            public string Description { get; set; }
            public bool Enable { get; set; } = true;
            public int AsInt { get { return (int)val; } }
            public float AsFloat { get { return (float)val; } }
            public double AsDouble { get { return Convert.ToDouble(val); } }
            public DateTime AsDateTime { get { return (DateTime)val; } }
            public string AsString { get { return val == null ? String.Empty : val.ToString(); } }
            public Secret AsSecret { get { return (Secret)val; } }
            public bool AsBool { get { return (bool)val; } }
            public byte AsByte { get { return (byte)val; } }
            public char AsChar { get { return (char)val; } }
            public decimal AsDecimal { get { return Convert.ToDecimal(val); } }
            public long AsLong { get { return (long)val; } }
            public short AsShort { get { return (short)val; } }
            public T As<T>()
            {
                return (T)val;
            }
            public T[] AsArray<T>()
            {
                object[] objects = val as object[];
                if (objects == null) throw new ConnectorException($"Value is a not the type.");

                var result = objects.OfType<T>();
                return result.ToArray();
            }
            public int ordinalPosition { get; }

            public object Val 
            {
                get 
                { 
                    return val;
                }
                internal set 
                {
                    if ( val!= null && ! HasValidTypeFor(value)) throw new ConnectorException($"Value is a not the type.");
                    val = value;
                } 
            }

            public object AsObject { get { return val; } }

            public int Version { get; private set; }
            public bool IsVariable { get; }
            public CustomSettings CustomSettings { get; }

            public string Type
            {
                get
                {
                    if (val == null) return nameof(String);
                    return val.GetType().Name;
                }
            }
            internal bool HasValidTypeFor(object value)
            {
                return val.GetType()==value.GetType();
            }
            private int version = 0;
            public void ChangeTo(DateTime dateToChangeValue, string user, object val)
            {
                if (default(DateTime) == dateToChangeValue) throw new ArgumentException(nameof(dateToChangeValue));
                if (Now > dateToChangeValue) throw new ConnectorException($"{nameof(dateToChangeValue)} can not be greater then {Now}");
                if (val ==null) throw new ArgumentException(nameof(val));

                var change = new Change(dateToChangeValue, user, val, ++version);
                changesSortedByDate.Add(change);

                changesSortedByDate.Sort(delegate (Change x, Change y)
                {
                    int compare = x.DateToChangeValue.CompareTo(y.DateToChangeValue);
                    bool areExactlyTheSameDay = compare == 0;
                    return areExactlyTheSameDay ? x.Version - y.Version : compare;
                });

                CustomSettings.AmountOfPendingChanges += 1;
            }

            internal bool ApplyChangeUntil(DateTime now)
            {
                List<Change> changesIndexApplied = new List<Change>();
                for (int i=0; i<changesSortedByDate.Count;i++)
                {
                    var change = changesSortedByDate[i];
                    if (now >= change.DateToChangeValue)
                    {
                        Apply(change);
                        changesIndexApplied.Add(change);
                    }
                    else 
                    {
                        break;
                    }
                    
                }

                Prune(changesIndexApplied);

                int amountOfAppliedChanges = changesIndexApplied.Count;
                CustomSettings.AmountOfPendingChanges -= amountOfAppliedChanges;
                Version = Version + amountOfAppliedChanges;
                
                return amountOfAppliedChanges > 0;
            }

            public DateTime NextDateToChange()
            {
                if (changesSortedByDate.Count == 0) return DateTime.MaxValue;
                return changesSortedByDate.First().DateToChangeValue;
            }

            public object NextValue()
            {
                if (changesSortedByDate.Count == 0) throw new ConnectorException($"No scheduled value yet {Now}");
                return changesSortedByDate.First().Val;
            }

            public bool HasScheduledChange()
            {
                return changesSortedByDate.Count != 0;
            }

            private void Apply(Change change)
            {
                Val = change.Val;
                logs.Add(change);
            }

            private void Prune(List<Change> changesTobeDeleted)
            {
                foreach (Change change in changesTobeDeleted) changesSortedByDate.Remove(change);
            }

            internal IEnumerable<Change> Changes
            {
                get 
                {
                    return changesSortedByDate;
                }
                
            }
            internal IEnumerable<Change> Logs
            {
                get
                {
                    return logs;
                }

            }

            public DateTime Now { get; internal set; }

            internal struct Change
            {
                public Change(DateTime dateToChangeValue, string user, object val, int version)
                {
                    if (dateToChangeValue == default(DateTime)) throw new ArgumentException(nameof(dateToChangeValue));
                    if (string.IsNullOrEmpty(user)) throw new ArgumentException(nameof(user));
                    if (val == null) throw new ArgumentException(nameof(val));

                    DateToChangeValue = dateToChangeValue;
                    Val = val;
                    User = user;
                    Version = version;
                }

                public DateTime DateToChangeValue { get; }
                public object Val { get; }
                public string User { get; }
                public int Version { get; }
            }

            public struct Secret
            {
                private readonly string val;
                public Secret(string val)
                {
                    this.val = val;
                }

                public override string ToString() { return val; }
            }

            internal CustomSetting Clear()
            {
                this.val = null;
                return this;
            }
        }

        public sealed class VariableCustomSetting : CustomSetting
        {
            public VariableCustomSetting(CustomSettings customSettings, int ordinalPosition, string key, object val) :
                base(customSettings, ordinalPosition, key, true, val)
            {
            }
        }
        public sealed class FixedCustomSetting : CustomSetting
        {
            public FixedCustomSetting(CustomSettings customSettings, int ordinalPosition, string key, object val) : 
                base(customSettings, ordinalPosition, key, false, val)
            {
            }
        }
        public CustomSettings ClonenlyFixedParameters()
        {
            CustomSettings result = new CustomSettings(this.variables);
            if (_tempBeforePrepare != null)
            {
                foreach (CustomSetting item in this._tempBeforePrepare)
                {
                    if (! item.IsVariable) result.AddFixedParameter(item.Now, item.Key, item.Val);
                }
            }

            return result;

        }

        public bool ContainsKeyName(string val)
        {
            Variable key = fixedCustomSettings.Keys.FirstOrDefault(key => key.Name == val);
            return key != default(Variable);
        }

        internal class CustomParameterCollection
        {
            private ConcurrentDictionary<RecordSet, bool> poolOfRecordSet = new ConcurrentDictionary<RecordSet, bool>();
            private const int CAPACITY_LENGHT = 10;
            private readonly int capacity;

            private CustomSettings CustomSettings { get; }

            internal IEnumerable<RecordSet> Collection
            {
                get
                {
                    return poolOfRecordSet.Keys;
                }
            }
            public CustomParameterCollection(CustomSettings customSettings) :this(customSettings, CAPACITY_LENGHT)
            {
            }
            public CustomParameterCollection(CustomSettings customSettings, int capacity)
            {
                this.capacity = capacity;
                CustomSettings=customSettings;
            }

            private CustomSetting[] template;

            internal void Configure(CustomSetting[] customSettings)
            {
                this.template = customSettings;

                for (int i = 0; i < capacity; i++)
                {
                    var registro = CreateNewRecordSet();
                    poolOfRecordSet.TryAdd(registro, false); // Se añade como disponible.
                }
            }

            private RecordSet CreateNewRecordSet()
            {
                Dictionary<string, CustomSetting> maps;
                CustomSetting[] newCustomSettings = CloneOnlyVariableValues(this.template, out maps);

                int newId = poolOfRecordSet.Count();

                return new RecordSet(this)
                {
                    CustomSettings = newCustomSettings,
                    //IsBeenUsed = false,
                    Id = newId,
                    Mappings = maps
                }; // Crear una nueva instancia del objeto Registro.
            }

            internal RecordSet NextAvalaible()
            {
                foreach (var kvp in poolOfRecordSet)
                {
                    // Encontrar el primer Registro que esté disponible (false).
                    if (poolOfRecordSet.TryUpdate(kvp.Key, true, false))
                    {
                        // Comentario: Aquí hemos bloqueado el Registro, ya que lo hemos marcado como "en uso" (true).
                        return kvp.Key; // Se retorna el Registro como exclusivo para el flujo actual.
                    }
                }

                // Si no se encontró ninguno disponible, creamos uno nuevo.
                RecordSet newRecordSet = CreateNewRecordSet();
                poolOfRecordSet.TryAdd(newRecordSet, true); // El nuevo registro se marca como en uso.
                                                   // Comentario: Aquí hemos bloqueado el nuevo Registro que acabamos de crear, ya que lo hemos marcado como "en uso" (true).
                return newRecordSet;
            }

            public void ReleaseRecordSet(RecordSet recordSet)
            {
                // Intentar marcar el registro como disponible (false) si existe en el pool.

                ClearVariables(recordSet.CustomSettings);

                poolOfRecordSet.AddOrUpdate(recordSet, false, (key, oldValue) => false);
                // Comentario: Aquí hemos liberado el Registro, ya que lo hemos marcado como disponible (false).
            }

            private CustomSetting[] CloneOnlyVariableValues(CustomSetting[] customSettings, out Dictionary<string, CustomSetting> maps)
            {
                CustomSetting[] newCustomSettings = new CustomSetting[customSettings.Length];
                maps = new Dictionary<string, CustomSetting>();

                for (int i = 0; i < customSettings.Length; i++)
                {
                    if (customSettings[i].IsVariable)
                    {
                        newCustomSettings[i] = new VariableCustomSetting(
                            CustomSettings, 
                            customSettings[i].ordinalPosition,
                            customSettings[i].Key,
                            customSettings[i].Val
                            );
                    }
                    else
                    {
                        newCustomSettings[i] = customSettings[i];
                    }

                    maps.Add(newCustomSettings[i].Key, newCustomSettings[i]);
                }
                return newCustomSettings;
            }

            //private int lastReturnedIndex = 0;

            //internal RecordSet NextAvalaible() 
            //{ 
            //    return NextAvalaible(ref lastReturnedIndex);
            //}

            //private RecordSet NextAvalaible(ref int lastReturnedIndex)
            //{
            //    ObtenerRegistro(customSettings);

            //    int myLastReturnedIndex = lastReturnedIndex;
            //    int currentLenght = poolOfRecordSet.Count();
            //    int index = myLastReturnedIndex;
            //    RecordSet result = default(RecordSet);

            //    bool isTheFirstIteration = myLastReturnedIndex == 0 && !poolOfRecordSet[myLastReturnedIndex].IsBeenUsed;
            //    bool isAtTheEdgeOfTheArray = (myLastReturnedIndex + 1 == currentLenght);
            //    bool nextOptionIsAvailable = isAtTheEdgeOfTheArray
            //        ? !poolOfRecordSet[0].IsBeenUsed
            //        : !poolOfRecordSet[myLastReturnedIndex + 1].IsBeenUsed;

            //    if (isTheFirstIteration)
            //    {
            //        myLastReturnedIndex = 0;
            //        result = poolOfRecordSet[myLastReturnedIndex];
            //        ClearVariables(result.CustomSettings);
            //        result.MarkAsUsed();

            //        lastReturnedIndex = myLastReturnedIndex;

            //        return result;
            //    }
            //    else if (nextOptionIsAvailable)
            //    {
            //        myLastReturnedIndex = (isAtTheEdgeOfTheArray) ? 0 : myLastReturnedIndex + 1;
            //        result = poolOfRecordSet[myLastReturnedIndex];
            //        ClearVariables(result.CustomSettings);
            //        poolOfRecordSet[myLastReturnedIndex].MarkAsUsed();

            //        lastReturnedIndex = myLastReturnedIndex;

            //        return result;
            //    }
            //    else 
            //    {
            //        do
            //        {
            //            if (!poolOfRecordSet[index].IsBeenUsed)
            //            {
            //                result = poolOfRecordSet[index];
            //                ClearVariables(result.CustomSettings);

            //                myLastReturnedIndex = index;

            //                poolOfRecordSet[index].MarkAsUsed();

            //                lastReturnedIndex = myLastReturnedIndex;

            //                return result;
            //            }

            //            index++;
            //            if (index == myLastReturnedIndex)
            //            {
            //                AddNewSpaces(poolOfRecordSet[myLastReturnedIndex].CustomSettings);
            //            }
            //            else if (index == currentLenght)
            //            {
            //                index = 0;
            //            }
            //        } while (index < currentLenght);
            //    }

            //    throw new ConnectorException($"There is no valid {nameof(CustomSetting)} to allocate the new parameters");
            //}

            private void ClearVariables( CustomSetting[] result)
            {
                result.Select(x => x.Clear());
            }
        }

        public sealed class RecordSet : IDisposable
        {
            public CustomSetting[] Parameters { get; internal set; }
            public int Id { get; internal set; }
            public Dictionary<string, CustomSetting> Mappings { get; set; }
            internal CustomSetting[] CustomSettings { get; set; }

            private readonly CustomParameterCollection customParameterCollection;

            internal RecordSet(CustomParameterCollection customParameterCollection) 
            { 
                this.customParameterCollection = customParameterCollection;
            }

            public void Release()
            {
                this.customParameterCollection.ReleaseRecordSet(this);
            }
            public void Dispose()
            {
                Release();
            }


            public void SetParameter(CustomSetting customSetting, object val)
            {
                customSetting.Val = val;
            }

            public void SetParameter(string customSettingName, object val)
            {
                CustomSetting cs;
                if (Mappings.TryGetValue(customSettingName, out cs))
                {
                    cs.Val = val;
                }
            }
            public void SetParameter(int customSettingOrdinalPosition, object val)
            {
                Parameters[customSettingOrdinalPosition].Val = val;
            }

            public bool ContainsKeyName(string v)
            {
                return Mappings.ContainsKey(v);
            }
        }
    }

    public sealed class Variables
    {
        private Dictionary<string, Variable> variables = new Dictionary<string, Variable>();

        internal bool TryGetValue(string key, out Variable variable)
        {
            return variables.TryGetValue(key, out variable);
        }
        internal Variable Create(string key, Type type)
        {
            var result = new Variable(key, type);
            variables.Add(key, result);

            return result;
        }

        internal int Count
        {
            get
            {
                return variables.Count;
            }
            
        }

        public struct Variable
        {
            public Variable(string name, Type type)
            {
                if (string.IsNullOrEmpty(name)) throw new ArgumentException(nameof(name));

                Name = name;
                Type = type;
            }

            public string Name { get; }
            public Type Type { get; }

            public static bool operator ==(Variable c1, Variable c2)
            {
                return c1.Equals(c2);
            }

            public static bool operator !=(Variable c1, Variable c2)
            {
                return !c1.Equals(c2);
            }
        }
    }
}
