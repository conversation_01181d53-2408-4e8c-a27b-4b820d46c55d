{"profiles": {"IIS Express": {"commandName": "IISExpress", "commandLineArgs": "artemis", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "CashierAPI": {"commandName": "Project", "commandLineArgs": "artemis", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "applicationUrl": "http://localhost:5000"}, "Docker": {"commandName": "<PERSON>er", "commandLineArgs": "artemis", "launchBrowser": true, "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}", "publishAllPorts": true}}, "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:5000", "sslPort": 0}}}