using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Runtime.Serialization;
using System.Runtime.Serialization.Json;
using System.Text;
using System.Threading.Tasks;
using GamesEngine;
using GamesEngine.Bets;
using GamesEngine.Business;
using GamesEngine.Domains;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using GamesEngine.Gameboards.Lotto;
using GamesEngine.Games.Lotto;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using GamesEngine.Time;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Puppeteer.EventSourcing;
using town.connectors;
using town.connectors.commons;
using town.connectors.drivers;
using static GamesEngine.Finance.CashierResponse;
using static GamesEngine.Finance.PaymentChannels;
using static GamesEngine.Games.Lotto.Disincentives;
using static town.connectors.CustomSettings;

namespace LottoAPI.Controllers
{
    public class DrawController : AuthorizeController
    {
        [HttpGet("api/lotto/draw")]
        [Authorize(Roles = "Draws")]
        public async Task<IActionResult> AllDrawsPerDayAsync(string date, [FromHeader(Name = "domain-url")] string domain)
        {
            return await DrawsPerDayAsync(true, default(IdOfLotteryGame), date, domain);
        }

        [HttpGet("api/triz/draw")]
        [Authorize(Roles = "Draws")]
        public async Task<IActionResult> TrizDrawsPerDayAsync(string date, [FromHeader(Name = "domain-url")] string domain)
        {
            return await DrawsPerDayAsync(false, IdOfLotteryGame.Triz, date, domain);
        }

        [HttpGet("api/picks/draw")]
        [Authorize(Roles = "Draws")]
        public async Task<IActionResult> PicksDrawsPerDayAsync(string date, [FromHeader(Name = "domain-url")] string domain)
        {
            return await DrawsPerDayAsync(false, IdOfLotteryGame.Picks, date, domain);
        }

        private async Task<IActionResult> DrawsPerDayAsync(bool useAllLotteryGamesFromPool, IdOfLotteryGame idOfLotteryGame, string date, [FromHeader(Name = "domain-url")]string domain)
        {
            if (!Validator.IsValidDate(date)) return NotFound($"Parameter {nameof(date)} is empty or invaid: {date}");
            if (String.IsNullOrWhiteSpace(HttpContext.Request.Headers["domain-url"])) return NotFound("Header 'domain-url' is not sent in request");

            var lastVersionOfDate = date + " 23:59:59";
            var atTheBeginningOfDate = date;

            StringBuilder queryShedulesSource = new StringBuilder();
            StringBuilder queryFinishShedulesSource = new StringBuilder();
            if (useAllLotteryGamesFromPool)
            {
                queryShedulesSource.AppendLine($"schedules = company.LotteryGamesPool.PendingAndNoRegradedSchedulesAt({lastVersionOfDate});");
                queryFinishShedulesSource.AppendLine($"schedules = company.LotteryGamesPool.FinishedAndRegradedSchedulesOf({atTheBeginningOfDate});");
            }
            else
            {
                queryShedulesSource.AppendLine($"lotteryGame = company.LotteryGamesPool.GetLotteryGame({idOfLotteryGame});");
                queryShedulesSource.AppendLine($"schedules = lotteryGame.PendingAndNoRegradedSchedulesAt({lastVersionOfDate});");
                queryFinishShedulesSource.AppendLine($"schedules = lotteryGame.FinishedAndRegradedSchedulesOf({atTheBeginningOfDate});");
            }

            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
            {{
                print Now currentDateTime;
                domain=company.Sales.DomainFrom('{domain}');
                {queryShedulesSource}
                for (scheduledLotteries:schedules)
                {{
                    lottery=scheduledLotteries.Lottery;
                    print scheduledLotteries.GetDescription() description;
                    print lottery.State.Abbreviation state;
                    drawDate = scheduledLotteries.ToDateTime({lastVersionOfDate});
                    isAttachedDate = scheduledLotteries.IsAttachedDate(drawDate);
                    print isAttachedDate isAttachedDate;
                    print drawDate.HHmmAMPM() scheduledHour;
                    print drawDate 'drawDate';
                    gameType = lottery.GameType();
                    print gameType gameType;
                    print lottery.IsEnabledDraw(drawDate, domain) isEnabled;
                    for (domains:company.Sales.AllDomains)
                    {{
                        print domains.Url url;
                        print lottery.IsEnabledDraw(drawDate, domains) isEnabled;
                    }}
                    print company.LotteryGamesPool.GetLotteryGame(lottery).OfficialLinks.Url(lottery.State) url;
                    print lottery.IsScheduleMarkedAsNoActionAt(drawDate) noAction;

                    if (gameType == 'LotteryKeno' || gameType == 'LotteryPowerball' || gameType == 'LotteryTriz')
                    {{
                        print false hasFireBall;
                    }}
                    else
                    {{
                        print lottery.IsFireBallTurnedOn hasFireBall;
                    }}
                }}

                {queryFinishShedulesSource}
                for (finishedLotteries:schedules)
                {{
                    lottery=finishedLotteries.Lottery;
                    print finishedLotteries.GetDescription() description;
                    print lottery.State.Abbreviation state;
                    drawDate = finishedLotteries.ToDateTime({lastVersionOfDate});
                    print drawDate.HHmmAMPM() scheduledHour;
                    print drawDate 'drawDate';
                    gameType = lottery.GameType();
                    print gameType gameType;
                    print lottery.IsEnabledDraw(drawDate, domain) isEnabled;
                    for (domains:company.Sales.AllDomains)
                    {{
                        print domains.Url url;
                        print lottery.IsEnabledDraw(drawDate, domains) isEnabled;
                    }}
                    lotteryGame = company.LotteryGamesPool.GetLotteryGame(lottery);
                    print lotteryGame.OfficialLinks.Url(lottery.State) url;
                    isNoAction = lottery.IsScheduleMarkedAsNoActionAt(drawDate);
                    isRegraded = lottery.IsRegraded(drawDate);
                    print isNoAction noAction;

                    if (gameType == '{LotteryTriz.GAME_TYPE}')
                    {{
                        if (!isNoAction && !isRegraded)
                        {{
                            print lottery.SequenceOfNumbersOfDrawAt(drawDate,finishedLotteries) winnerNumbers;
                        }}
                    }}
                    else
                    {{
                        isFireBallTurnedOn = lottery.IsFireBallTurnedOn;
                        print isFireBallTurnedOn hasFireBall;
                        if (!isNoAction && !isRegraded)
                        {{
                            print lottery.SequenceOfNumbersOfDrawAt(drawDate,finishedLotteries) winnerNumbers;
                            if (lottery.HasFireBall)
                            {{
                                hasFireBallNumber = lottery.FireBallLottery.HasFireBallNumber(drawDate,finishedLotteries);
                                if (hasFireBallNumber)
                                {{
                                    print lottery.FireBallLottery.FireBallNumber(drawDate,finishedLotteries) fireBallNumber;
                                }}
                            }}
                        }}
                    }}
                    print lottery.Changelog(drawDate,finishedLotteries) log;
                }}
            }}
            ");
            return result;
        }

        [HttpPost("api/lotto/draw/pick/{pickNumber:int:min(2):max(5)}/fireBall/on")]
        [Authorize(Roles = "fb")]
        public async Task<IActionResult> OnFireBallAsync(int pickNumber, [FromBody] StateCodeBody body)
        {
            if (pickNumber < 2 || pickNumber > 5) return BadRequest($"Parameter {nameof(pickNumber)} is invalid: {pickNumber}");
            if (body == null) return BadRequest("Body is required");
            if (String.IsNullOrWhiteSpace(body.StateCode)) return BadRequest($"Parameter {nameof(body.StateCode)} is required");

            string employeeName = Security.UserName(HttpContext);
            var result = await LottoAPI.Actor.PerformChkThenCmdAsync(HttpContext, $@"
                {{
                    hasLottery = lotto900.HasLottery({pickNumber}, state{body.StateCode});
                    Check(hasLottery) Error 'Lottery not found for pick number {pickNumber} and state code {body.StateCode}';

                    lottery = lotto900.GetLottery({pickNumber}, state{body.StateCode});
                    Check(!lottery.IsFireBallTurnedOn) Error 'Lottery for pick number {pickNumber} and state code {body.StateCode}, already has a fireball';
                }}", $@"
                {{
                    lottery = lotto900.GetLottery({pickNumber}, state{body.StateCode});
                    lottery.TurnOnFireBall(itIsThePresent, Now, '{employeeName}');
                }}
            ");
            return result;
        }

        [HttpPost("api/lotto/draw/pick/{pickNumber:int:min(2):max(5)}/fireBall/off")]
        [Authorize(Roles = "fb")]
        public async Task<IActionResult> OffFireBallAsync(int pickNumber, [FromBody] StateCodeBody body)
        {
            if (pickNumber < 2 || pickNumber > 5) return BadRequest($"Parameter {nameof(pickNumber)} is invalid: {pickNumber}");
            if (body == null) return BadRequest("Body is required");
            if (String.IsNullOrWhiteSpace(body.StateCode)) return BadRequest($"Parameter {nameof(body.StateCode)} is required");

            string employeeName = Security.UserName(HttpContext);
            var result = await LottoAPI.Actor.PerformChkThenCmdAsync(HttpContext, $@"
                {{
                    hasLottery = lotto900.HasLottery({pickNumber}, state{body.StateCode});
                    Check(hasLottery) Error 'Lottery not found for pick number {pickNumber} and state code {body.StateCode}';

                    lottery = lotto900.GetLottery({pickNumber}, state{body.StateCode});
                    Check(lottery.IsFireBallTurnedOn) Error 'Lottery for pick number {pickNumber} and state code {body.StateCode} must have a fireball';
                }}", $@"
                {{
                    lottery = lotto900.GetLottery({pickNumber}, state{body.StateCode});
                    lottery.TurnOffFireBall(itIsThePresent, Now, '{employeeName}');
                }}
            ");
            return result;
        }

        [HttpGet("api/lotto/draws/keno")]
        [Authorize(Roles = "KDraws")]
        public async Task<IActionResult> KenoDrawsPerDayAsync(string date, [FromHeader(Name = "domain-url")] string domain)
        {
            if (!Validator.IsValidDate(date)) return BadRequest($"Parameter {nameof(date)} is empty or invaid: {date}");
            if (String.IsNullOrWhiteSpace(HttpContext.Request.Headers["domain-url"])) return BadRequest("Header 'domain-url' is not sent in request");

            var lastVersionOfDate = date + " 23:59:59";
            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
            {{
                print Now currentDateTime;
                domain=company.Sales.DomainFrom('{domain}');
                nextDraws = Keno.PendingAndNoRegradedSchedulesAtForKeno({lastVersionOfDate});
                for (scheduledLotteries:nextDraws)
                {{
                    lottery=scheduledLotteries.Lottery;
                    print scheduledLotteries.IdPrefix drawId;
                    print scheduledLotteries.GetDescription() description;
                    drawDate = scheduledLotteries.DrawDate;
                    print drawDate.HHmmAMPM() scheduledHour;
                    print drawDate 'drawDate';
                    print lottery.GameType() gameType;
                    print lottery.IsEnabledDraw(drawDate, domain) isEnabled;
                    for (domains:company.Sales.AllDomains)
                    {{
                        print domains.Url url;
                        print lottery.IsEnabledDraw(drawDate, domains) isEnabled;
                    }}
                    print lotteries.OfficialLinks.Url(lottery.State) url;
                    print lottery.IsScheduleMarkedAsNoActionAt(drawDate) noAction;
                }}

                nextDraws = keno.FinishedAndRegradedKenoSchedulesAt({date});
                for (finishedLotteries:nextDraws)
                {{
                    lottery=finishedLotteries.Lottery;
                    print finishedLotteries.Id drawId;
                    print finishedLotteries.GetDescription() description;
                    drawDate = finishedLotteries.DrawDate;
                    print drawDate.HHmmAMPM() scheduledHour;
                    print drawDate 'drawDate';
                    print lottery.GameType() gameType;
                    print lottery.IsEnabledDraw(drawDate, domain) isEnabled;
                    for (domains:company.Sales.AllDomains)
                    {{
                        print domains.Url url;
                        print lottery.IsEnabledDraw(drawDate, domains) isEnabled;
                    }}
                    print lotteries.OfficialLinks.Url(lottery.State) url;
                    isNoAction = lottery.IsScheduleMarkedAsNoActionAt(drawDate);
                    isRegraded = lottery.IsRegraded(drawDate);
                    print isNoAction noAction;
                    if (!isNoAction && !isRegraded)
                    {{
                        print lottery.MultiplierOfDrawAt(finishedLotteries) multiplier;
                        print lottery.BulleyeOfDrawAt(finishedLotteries) bulleye;
                        winnerNumbersList = lottery.WinnerNumbersOfDraw(finishedLotteries);
                        for (winnerNumbers:winnerNumbersList)
                        {{
                            print winnerNumbers winnerNumber;
                        }}
                    }}
                    print lottery.Changelog(finishedLotteries) log;
                }}
            }}
            ");
            return result;
        }

        public enum StatusKenoDraw
        {
            current,
            played
        }

        [HttpGet("api/lotto/draw/keno")]
        [Authorize(Roles = "Ka12,player")]
        public async Task<IActionResult> DrawForKenoAsync(StatusKenoDraw status, [FromHeader(Name = "domain-url")] string domain)
        {
            if (status == StatusKenoDraw.current)
            {

            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
                {{
                                currentDateTime= Now;
                                print Now currentDateTime;
                                domain=company.Sales.DomainFrom('{domain}');
                                nextDrawDate = lotteries.CalculateNextPendingAndNoRegradedSchedulesAtForKeno(currentDateTime);
                                if(lotteries.IsAValidDateForKeno(nextDrawDate))
                                {{
                                    nextDraw = lotteries.NextPendingAndNoRegradedSchedulesAtForKeno(currentDateTime);
                                    print nextDraw.IdPrefix draw;
                                    lottery = nextDraw.Lottery;
                                    print lottery.State.Abbreviation state;
                                    drawDate = nextDraw.DrawDate;
                                    print drawDate.HHmmAMPM() scheduledHour;
                                    print drawDate 'drawDate';
                                    print lottery.GameType() gameType;
                                    print lottery.IsEnabledDraw(drawDate, domain) isEnabled;
                                    for (domains:company.Sales.AllDomains)
                                    {{
                                        print domains.Url url;
                                        print lottery.IsEnabledDraw(drawDate, domains) isEnabled;
                                    }}
                                    print lotteries.OfficialLinks.Url(lottery.State) url;
                                    print lottery.IsScheduleMarkedAsNoActionAt(drawDate) noAction;
                                }}
                }}
                ");
                return result;
            }
            //numbersList = { { 10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29} };
            //for (numbers:numbersList)
            //{
            //    {
            //        print numbers number;
            //    }
            //}
            //print 12345 draw;
            //print 2 multiplier;
            //print 15 bullsEye;
            else if (status == StatusKenoDraw.played)
            {
                var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
                    {{
                        currentDateTime= Now;
                        print Now currentDateTime;
                        schedules=lotteries.FinishedAndRegradedSchedulesOfKeno(currentDateTime);
                        for (finishedLotteries:schedules)
                        {{
                            lottery=finishedLotteries.Lottery;
                            print lottery.State.Abbreviation state;
                            drawDate = finishedLotteries.ToDateTime(currentDateTime);
                            print drawDate.HHmmAMPM() scheduledHour;
                            print drawDate 'drawDate';
                            print lottery.GameType() gameType;
                            print lottery.IsEnabledDraw(drawDate, domain) isEnabled;
                            for (domains:company.Sales.AllDomains)
                            {{
                                print domains.Url url;
                                print lottery.IsEnabledDraw(drawDate, domains) isEnabled;
                            }}
                            print lotteries.OfficialLinks.Url(lottery.State) url;
                            isNoAction = lottery.IsScheduleMarkedAsNoActionAt(drawDate);
                            isRegraded = lottery.IsRegraded(drawDate);
                            print isNoAction noAction;
                            if (!isNoAction && !isRegraded)
                            {{
                                print lottery.SequenceOfNumbersOfDrawAt(drawDate,finishedLotteries) winnerNumbers;
                                print lottery.MultiplierOfDrawAt(drawDate,finishedLotteries) multiplier;
                            }}
                            print lottery.Changelog(drawDate,finishedLotteries) log;
                        }}
                    }}
                ");
                return result;
            }
            return BadRequest($"{nameof(status)} is not valid");
        }


        [HttpGet("api/lotto/draw/powerball")]
        [Authorize(Roles = "a12")]
        public async Task<IActionResult> DrawsPerDayForPowerBallAsync(string date, [FromHeader(Name = "domain-url")]string domain)
        {
            if (!Validator.IsValidDate(date)) return NotFound($"Parameter {nameof(date)} is empty or invaid: {date}");
            if (String.IsNullOrWhiteSpace(HttpContext.Request.Headers["domain-url"])) return NotFound("Header 'domain-url' is not sent in request");

            var lastVersionOfDate = date + " 23:59:59";
            var atTheBeginningOfDate = date;
            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
            {{
                print Now currentDateTime;
                domain=company.Sales.DomainFrom('{domain}');
                schedules=lotto900.PendingAndNoRegradedSchedulesAtForPowerBall({lastVersionOfDate});
                for (scheduledLotteries:schedules)
                {{
                    lottery=scheduledLotteries.Lottery;
                    print lottery.State.Abbreviation state;
                    drawDate = scheduledLotteries.ToDateTime({lastVersionOfDate});
                    print drawDate.HHmmAMPM() scheduledHour;
                    print drawDate 'drawDate';
                    print lottery.GameType() gameType;
                    print lottery.IsEnabledDraw(drawDate, domain) isEnabled;
                    for (domains:company.Sales.AllDomains)
                    {{
                        print domains.Url url;
                        print lottery.IsEnabledDraw(drawDate, domains) isEnabled;
                    }}
                    print lotto900.OfficialLinks.Url(lottery.State) url;
                    print lottery.IsScheduleMarkedAsNoActionAt(drawDate) noAction;
                }}

                schedules=lotto900.FinishedAndRegradedSchedulesOfPowerBall({atTheBeginningOfDate});
                for (finishedLotteries:schedules)
                {{
                    lottery=finishedLotteries.Lottery;
                    print lottery.State.Abbreviation state;
                    drawDate = finishedLotteries.ToDateTime({atTheBeginningOfDate});
                    print drawDate.HHmmAMPM() scheduledHour;
                    print drawDate 'drawDate';
                    print lottery.GameType() gameType;
                    print lottery.IsEnabledDraw(drawDate, domain) isEnabled;
                    for (domains:company.Sales.AllDomains)
                    {{
                        print domains.Url url;
                        print lottery.IsEnabledDraw(drawDate, domains) isEnabled;
                    }}
                    print lotto900.OfficialLinks.Url(lottery.State) url;
                    isNoAction = lottery.IsScheduleMarkedAsNoActionAt(drawDate);
                    isRegraded = lottery.IsRegraded(drawDate);
                    print isNoAction noAction;
                    if (!isNoAction && !isRegraded)
                    {{
                        print lottery.SequenceOfNumbersOfDrawAt(drawDate,finishedLotteries) winnerNumbers;
                        print lottery.MultiplierOfDrawAt(drawDate,finishedLotteries) multiplier;
                    }}
                    print lottery.Changelog(drawDate,finishedLotteries) log;
                }}
            }}
            ");
            return result;
        }

        [HttpGet("api/lotto/draw/limits")]
        [Authorize(Roles = "c3")]
        public async Task<IActionResult> LimitsPerLotteriesAsync(string domain)
        {
            if (string.IsNullOrWhiteSpace(domain)) return BadRequest($"{nameof(domain)} is required");

            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
				{{
                    domain = company.Sales.DomainFrom('{domain}');
                    risk = lotto900.RiskProfiles.GetRiskProfile(domain).Risks.Risk;
                    print risk.GetMaximumToWin(domain, 2) maxLimitPick2;
                    print risk.GetMaximumToWin(domain, 3) maxLimitPick3;
                    print risk.GetMaximumToWin(domain, 4) maxLimitPick4;
                    print risk.GetMaximumToWin(domain, 5) maxLimitPick5;
				}}
            ");
            return result;
        }

        [HttpGet("api/lotto/draw/limits/toleranceFactor")]
        public async Task<IActionResult> RiskToleranceFactorAsync(string domain)
        {
            if (string.IsNullOrWhiteSpace(domain)) return BadRequest($"{nameof(domain)} is required");

            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
				{{
                    domain = company.Sales.DomainFrom('{domain}');
                    risk = lotto900.RiskProfiles.GetRiskProfile(domain).Risks.Risk;
                    print risk.RiskToleranceFactor riskToleranceFactor;
				}}
            ");
            return result;
        }

        [HttpPut("api/lotto/draw/limits/toleranceFactor")]
        public async Task<IActionResult> UpdateRiskToleranceFactorAsync(string domain, [FromBody] RiskToleranceFactorBody body)
        {
            if (body == null) return BadRequest("Body is required");
            if (string.IsNullOrWhiteSpace(domain)) return BadRequest($"{nameof(domain)} is required");
            if (body.RiskToleranceFactor <= 0 || body.RiskToleranceFactor > 1) return BadRequest($"{nameof(body.RiskToleranceFactor)} {body.RiskToleranceFactor} is not valid");

            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
				{{
                    domain = company.Sales.DomainFrom('{domain}');
                    risk = lotto900.RiskProfiles.GetRiskProfile(domain).Risks.Risk;
                    risk.RiskToleranceFactor = {body.RiskToleranceFactor};
				}}
            ");
            return result;
        }

        [HttpGet("api/lotto/draw/triz/limit/amountSpent")]
        [Authorize(Roles = "z11")]
        public async Task<IActionResult> TrizListDrawLimitAsync(string riskProfile, DateTime drawDate)
        {
            if (string.IsNullOrWhiteSpace(riskProfile)) return BadRequest($"{nameof(riskProfile)} is required");
            if (drawDate == default(DateTime)) return BadRequest($"{nameof(drawDate)} is required");

            var stringDate = $"{drawDate.Month}/{drawDate.Day}/{drawDate.Year} {drawDate.Hour}:{drawDate.Minute}:{drawDate.Second}";
            IActionResult result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
                {{
                    risk = company.LotteryGamesPool.RiskProfiles.GetRiskProfile('{riskProfile}').Risks.Risk;
                    print risk.GetMaximumToWin(2) maxLimitPick2;
                    print risk.GetMaximumToWin(3) maxLimitPick3;
                    print risk.GetMaximumToWin(4) maxLimitPick4;
                    print risk.GetMaximumToWin(5) maxLimitPick5;

                    mainLottery = company.LotteryGamesPool.TrizLotteryGame.GetLottery();
                    riskPerLotteriesList = mainLottery.GetRiskPerLotteries(risk);
                    for (riskPerLotteries:riskPerLotteriesList)
                    {{
                        riskPerLottery = riskPerLotteries;
                        print riskPerLottery.Lottery.TypeNumberSequenceAsText() position;
                        print riskPerLottery.Lottery.PickNumber pick;
                        totalRisk = riskPerLottery.AccumulatedToWinAt({stringDate});
                        print totalRisk totalRisk;
                        riskPerSubtickets = riskPerLottery.ToWinPerSubticketsAt({stringDate});
                        print riskPerSubtickets.Count() count;
                        for (risks:riskPerSubtickets)
                        {{
                            riskPick = risks;
                            print riskPick.Number number;
                            print riskPick.Amount amount;
                            print riskPick.PurchasesCount purchasesCount;
                        }}
                    }}
                }}
            ");
            return result;
        }

        [HttpGet("api/lotto/draw/pick/{pickNumber:int:min(2):max(5)}/limit/amountSpent")]
        [Authorize(Roles = "z11")]
        public async Task<IActionResult> PicksListDrawLimitAsync(string riskProfile, int pickNumber, string state, DateTime drawDate)
        {
            if (string.IsNullOrWhiteSpace(riskProfile)) return BadRequest($"{nameof(riskProfile)} is required");
            if (string.IsNullOrWhiteSpace(state)) return BadRequest($"Parameter {nameof(state)} is required");
            if (drawDate == default(DateTime)) return BadRequest($"Parameter {nameof(drawDate)} is required");

            var stringDate = $"{drawDate.Month}/{drawDate.Day}/{drawDate.Year} {drawDate.Hour}:{drawDate.Minute}:{drawDate.Second}";
            IActionResult result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
                {{
                    risk = lotto900.RiskProfiles.GetRiskProfile('{riskProfile}').Risks.Risk;
                    lottery = lotto900.GetLottery({pickNumber}, state{state});
                    print risk.GetMaximumToWin({pickNumber}) maxLimit;
                    riskPerLottery = risk.GetRiskPerLottery({pickNumber}, lottery);
                    riskPerSubtickets = riskPerLottery.ToWinPerSubticketsAt({stringDate});
                    totalRisk = riskPerLottery.AccumulatedToWinAt({stringDate});
                    print totalRisk totalRisk;
                    print riskPerSubtickets.Count() count;
                    for (risks:riskPerSubtickets)
                    {{
                        risk = risks;
                        print risk.Number number;
                        print risk.Amount amount;
                        print risk.PurchasesCount purchasesCount;
                    }}
                }}
            ");
            return result;
        }

        [HttpGet("api/lotto/draw/triz/subticketsExceedingRisk")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> TrizNumberAvailabilityAsync([FromHeader(Name = "domain-url")] string domain, DateTime drawDate, string numbers, decimal betAmount, string ticketTypes)
        {
            if (string.IsNullOrWhiteSpace(HttpContext.Request.Headers["domain-url"])) return BadRequest("Header 'domain-url' is not sent in request");
            if (string.IsNullOrWhiteSpace(domain)) return BadRequest($"{nameof(domain)} is required");
            if (drawDate == default(DateTime)) return BadRequest($"{nameof(drawDate)} is required");
            if (drawDate.Second != 0 || drawDate.Millisecond != 0) return BadRequest($"{nameof(drawDate)} must have 0 seconds");
            if (string.IsNullOrWhiteSpace(numbers)) return BadRequest($"{nameof(numbers)} is required");
            if (betAmount <= 0) return BadRequest($"{nameof(betAmount)} is required");
            if (string.IsNullOrWhiteSpace(ticketTypes)) return BadRequest($"{nameof(ticketTypes)} is required");

            var numbersAsText = string.Join("','", numbers.Split(','));
            var ticketTypeAsText = string.Join("','", ticketTypes.Split(','));
            var stringDate = $"{drawDate.Month}/{drawDate.Day}/{drawDate.Year} {drawDate.Hour}:{drawDate.Minute}:{drawDate.Second}";
            IActionResult result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
                {{
                    domain = company.Sales.DomainFrom('{domain}');
                    riskProfile = company.LotteryGamesPool.RiskProfiles.GetRiskProfile(domain);
                    prizes = riskProfile.Prizes;
                    lottery = company.LotteryGamesPool.TrizLotteryGame.GetLottery();
                    subticketsExceedingToWin = lottery.SubticketsExceedingToWin(prizes, {{'{numbersAsText}'}}, {stringDate}, domain, {betAmount}, {{'{ticketTypeAsText}'}});
                    for (subticketsExceedingRisk:subticketsExceedingToWin)
                    {{
                        subticket = subticketsExceedingRisk;
                        print subticket.Number number;
                        print subticket.Available available;
                    }}
                }}
            ");
            return result;
        }

        [HttpGet("api/lotto/draw/pick/{pickNumber:int:min(2):max(5)}/subticketsExceedingRisk")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> PicksNumberAvailabilityAsync([FromHeader(Name = "domain-url")] string domain, int pickNumber, string state, DateTime drawDate, string numbers, decimal betAmount, TicketType? ticketType)
        {
            if (string.IsNullOrWhiteSpace(HttpContext.Request.Headers["domain-url"])) return BadRequest("Header 'domain-url' is not sent in request");
            if (string.IsNullOrWhiteSpace(domain)) return BadRequest($"{nameof(domain)} is required");
            if (string.IsNullOrWhiteSpace(state)) return BadRequest($"{nameof(state)} is required");
            if (drawDate == default(DateTime)) return BadRequest($"{nameof(drawDate)} is required");
            if (drawDate.Second != 0 || drawDate.Millisecond != 0) return BadRequest($"{nameof(drawDate)} must have 0 seconds");
            if (string.IsNullOrWhiteSpace(numbers)) return BadRequest($"{nameof(numbers)} is required");
            if (betAmount <= 0) return BadRequest($"{nameof(betAmount)} is required");
            if (!ticketType.HasValue) return BadRequest($"{nameof(ticketType)} is required");

            var numbersAsText = string.Join("','",numbers.Split(','));
            var stringDate = $"{drawDate.Month}/{drawDate.Day}/{drawDate.Year} {drawDate.Hour}:{drawDate.Minute}:{drawDate.Second}";
            IActionResult result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
                {{
                    domain = company.Sales.DomainFrom('{domain}');
                    riskProfile = lotto900.RiskProfiles.GetRiskProfile(domain);
                    prizes = riskProfile.Prizes;
                    risk = riskProfile.Risks.Risk;
                    lottery = lotto900.GetLottery({pickNumber}, state{state});
                    riskPerLottery = risk.GetRiskPerLottery({pickNumber}, lottery);
                    schedule = lottery.FindScheduleAt({stringDate});
                    subticketsExceedingToWin = riskPerLottery.SubticketsExceedingToWin(prizes, schedule, {{'{numbersAsText}'}}, {stringDate}, domain, {betAmount}, {ticketType.Value});
                    for (subticketsExceedingRisk:subticketsExceedingToWin)
                    {{
                        subticket = subticketsExceedingRisk;
                        print subticket.Number number;
                        print subticket.Available available;
                    }}
                }}
            ");
            return result;
        }

        [HttpGet("api/lotto/draw/pick/{pickNumber:int:min(2):max(5)}/numbersAvailability")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> NumbersAvailabilityAsync([FromHeader(Name = "domain-url")] string domain, int pickNumber, string state, string drawDates, string numbers, decimal betAmount, TicketType? ticketType)
        {
            if (string.IsNullOrWhiteSpace(HttpContext.Request.Headers["domain-url"])) return BadRequest("Header 'domain-url' is not sent in request");
            if (string.IsNullOrWhiteSpace(domain)) return BadRequest($"{nameof(domain)} is required");
            if (string.IsNullOrWhiteSpace(state)) return BadRequest($"{nameof(state)} is required");
            if (string.IsNullOrWhiteSpace(drawDates)) return BadRequest($"{nameof(drawDates)} is required");
            if (string.IsNullOrWhiteSpace(numbers)) return BadRequest($"{nameof(numbers)} is required");
            if (betAmount <= 0) return BadRequest($"{nameof(betAmount)} is required");
            if (!ticketType.HasValue) return BadRequest($"{nameof(ticketType)} is required");

            var datesList = drawDates.Split(',');
            var numbersAsText = string.Join("','", numbers.Split(','));
            var stringDates = new List<string>();

            foreach (var date in datesList)
            {
                if (!DateTime.TryParse(date, out DateTime parsedDate)) return BadRequest($"Invalid date format in {nameof(drawDates)}: {date}");
                if (parsedDate.Second != 0 || parsedDate.Millisecond != 0) return BadRequest($"Date of Lottery drawings can not have seconds: {date}");

                stringDates.Add($"{parsedDate.Month}/{parsedDate.Day}/{parsedDate.Year} {parsedDate.Hour}:{parsedDate.Minute}:{parsedDate.Second}");
            }

            var stringDatesList = string.Join("','", stringDates);

            IActionResult result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
                {{
                    domain = company.Sales.DomainFrom('{domain}');
                    riskProfile = lotto900.RiskProfiles.GetRiskProfile(domain);
                    risk = riskProfile.Risks.Risk;
                    lottery = lotto900.GetLottery({pickNumber}, state{state});
                    riskPerLottery = risk.GetRiskPerLottery({pickNumber}, lottery);

                    numbersAvailability = riskPerLottery.QuoteNumbers(lottery, {{'{numbersAsText}'}}, {{'{stringDatesList}'}}, domain, {betAmount}, {ticketType.Value}, Now);
                    print numbersAvailability.UnavailableNumbers unavailableNumbers;
                    print numbersAvailability.AvailableNumbers availableNumbers;
                    print numbersAvailability.Issued issued;
                }}
            ");
            return result;
        }

        [HttpPut("api/lotto/draw/pick/{pickNumber:int:min(2):max(5)}/limit")]
		[Authorize(Roles = "c4")]
		public async Task<IActionResult> UpdateDrawLimitAsync(string domain, int pickNumber, [FromBody]DrawLimit body)
		{
			if (body == null) return BadRequest("Body is required");
            if (string.IsNullOrWhiteSpace(domain)) return BadRequest($"{nameof(domain)} is required");
            if (body.LimitAmount < 0) return BadRequest($"Parameter {nameof(body.LimitAmount)} is required");
			if (body.LimitAmount < 0.25m) return BadRequest($"Parameter {nameof(body.LimitAmount)} exceeds the minimun amount allowed");

            IActionResult result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
                {{
                    domain = company.Sales.DomainFrom('{domain}');
                    risk = lotto900.RiskProfiles.GetRiskProfile(domain).Risks.Risk;
                    risk.UpdateToWin(domain, {pickNumber}, {body.LimitAmount});
                }}
            ");
			return result;
		}

		[HttpGet("api/lotto/draw/tobeconfirmed")]
        [Authorize(Roles = "DrawsConfirmation")]
        public async Task<IActionResult> DrawsToBeConfirmedAsync()
        {
            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
            {{
                drawsToBeConfirmed = company.LotteryGamesPool.DrawsToBeConfirmed();

                for (drawsToBeConfirmed:drawsToBeConfirmed)
                {{
                    print drawsToBeConfirmed.Date 'drawDate';
                    print drawsToBeConfirmed.State.Abbreviation state;
                    print drawsToBeConfirmed.GameType gameType;
                    print drawsToBeConfirmed.Date.HHmmAMPM() scheduledHour;
                    lotteryGame = company.LotteryGamesPool.GetLotteryGame(drawsToBeConfirmed.Lottery);
                    print lotteryGame.OfficialLinks.Url(drawsToBeConfirmed.State) officialLink;
                    grading = drawsToBeConfirmed.GradingAsString;
                    print grading grading;
                    if(grading == 'GRADED')
                    {{
                        lottery = drawsToBeConfirmed.Lottery;
                        print drawsToBeConfirmed.WhoGraded whoEnteredLastChanged;
                        print drawsToBeConfirmed.SequenceOfNumbers winnerNumbers;

                        gameType = lottery.GameType();
                        if (gameType != '{LotteryTriz.GAME_TYPE}')
                        {{
                            hasFireBall = lottery.HasFireBall;
                            if (hasFireBall)
                            {{
                                hasFireBallNumber = lottery.FireBallLottery.HasFireBallNumber(drawsToBeConfirmed.Date,drawsToBeConfirmed.Schedule);
                                print hasFireBallNumber withFireBall;
                                if (hasFireBallNumber)
				                {{
					                print lottery.FireBallLottery.FireBallNumber(drawsToBeConfirmed.Date,drawsToBeConfirmed.Schedule) fireBallNumber;
				                }}
                            }}  
                        }}
                    }}
                    else if (grading == 'NOACTION')
                    {{
                        print drawsToBeConfirmed.WhoSetNoAction whoEnteredLastChanged;
                    }}
                    else if (grading == 'REGRADED')
                    {{
                        print drawsToBeConfirmed.WhoRegraded whoEnteredLastChanged;
                    }}
                }}
            }}
            ");
            return result;
        }

        [HttpGet("api/lotto/draw/tobeconfirmed/powerball")]
        [Authorize(Roles = "a10")]
        public async Task<IActionResult> DrawsToBeConfirmedForPowerBallAsync()
        {
            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
            {{
                for (drawsToBeConfirmed:lotto900.DrawsToBeConfirmedForPowerBall())
                {{
                    print drawsToBeConfirmed.Date 'drawDate';
                    print drawsToBeConfirmed.MultiplierNumber multiplier;
                    print drawsToBeConfirmed.State.Abbreviation state;
                    print drawsToBeConfirmed.GameType gameType;
                    print drawsToBeConfirmed.Date.HHmmAMPM() scheduledHour;
                    print drawsToBeConfirmed.SequenceOfNumbers winnerNumbers;
                    print drawsToBeConfirmed.WhoGraded whoEnteredLastChanged;
                }}
            }}
            ");
            return result;
        }

        [HttpGet("api/lotto/draws/upcoming")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> UpcomingSchedulesAsync([FromHeader(Name = "domain-url")] string domain, string number)
        {
            if (string.IsNullOrWhiteSpace(HttpContext.Request.Headers["domain-url"])) return BadRequest("Header 'domain-url' is not sent in request");
            if (string.IsNullOrWhiteSpace(number)) return BadRequest($"Parameter {nameof(number)} is required");

            string playerId = Validator.StringEscape(Security.PlayerId(User));
            var pickNumber = number.Length;
            var ticketType = PicksLotteryGame.TicketTypeStraight(pickNumber);
            var subticket = SubTicket<IPick>.SubticketFromNumber(number);
            var prizeCriteriaId = Disincentives.WayOfSubticket(ticketType, subticket);
            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
            {{
                player = company.CustomerByPlayerId('{playerId}').Player;
                print Now now;
                print {PicksLotteryGame.DEFAULT_MIN_BET_FOR_RR} minBetAmount;
                domain = company.Sales.DomainFrom('{domain}');
                schedules = lotto900.UpcomingSchedules(Now, domain, '{number}', player);
                for (scheduledLotteries:schedules)
                {{
                    lottery = scheduledLotteries.Lottery;
                    print lottery.State.Abbreviation state;
                    print lottery.State.Name stateName;
                    drawDate = lottery.NextValidDrawDate(scheduledLotteries,Now);
                    print drawDate.HHmmAMPM() scheduledHour;
                    print drawDate 'drawDate';
                    print drawDate.MMddyyyyOrTodayOrTomorrow(Now) drawDateFormatted;
                    print scheduledLotteries.GetDescription() description;
                    print lottery.GameType() gameType;
					print lottery.RemainingTimeInSecondsToNextDraw(scheduledLotteries, Now) timeInSecondsToNextDraw;

                    risk = lotto900.RiskProfiles.GetRiskProfile(domain).Risks.Risk;
                    riskPerLottery = risk.GetRiskPerLottery({pickNumber}, lottery);
                    maxAvailableBetAmount = riskPerLottery.MaxAvailableBetAmount(scheduledLotteries, {prizeCriteriaId}, {RuleType.Straight}, '{number}', drawDate, domain);
                    print maxAvailableBetAmount maxAvailableBetAmount;
                }}
            }}
            ");
            return result;
        }

        [HttpGet("api/picks/draw/byTime")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> PicksDrawsOrderedByTimeAsync([FromHeader(Name = "domain-url")]string domain, string countryCode)
        {
            if (String.IsNullOrWhiteSpace(HttpContext.Request.Headers["domain-url"])) return NotFound("Header 'domain-url' is not sent in request");

            if (String.IsNullOrWhiteSpace(countryCode))
            {
                countryCode = CountryTimeZone.DEFAULT.CountryCode;
            }

            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
            {{
                print Now now;
                print company.Sales.CurrentStore.TimeInSecondsToCloseStore timeInSecondsToCloseStore;
                domain=company.Sales.DomainFrom('{domain}');
                schedules=company.LotteryGamesPool.PicksLotteryGame.SchedulesByTimeOf(Now, domain);
                for (scheduledLotteries:schedules)
                {{
                    lottery=scheduledLotteries.Lottery;
                    print lottery.State.Abbreviation state;
                    print lottery.State.Name stateName;
                    drawDate = lottery.NextValidDrawDate(scheduledLotteries, Now, '{countryCode}');
                    print drawDate.HHmmAMPM() scheduledHour;
                    print drawDate 'drawDate';
                    print drawDate.MMddyyyyOrTodayOrTomorrow(Now) drawDateFormatted;
                    print scheduledLotteries.GetDescription() description;
                    print lottery.GameType() gameType;
                    print lottery.IsAlreadyDrawnAt(scheduledLotteries,Now) isAlreadyDrawnToday;
                    print lottery.IsScheduleMarkedAsNoActionAt(scheduledLotteries,Now) noAction;
                    print scheduledLotteries.IsScheduledAt(Now) isForToday;
                    print scheduledLotteries.IsScheduledSaturday() isScheduledSaturday;
                    print scheduledLotteries.IsScheduledSunday() isScheduledSunday;
                    print scheduledLotteries.ScheduledDays() daysOfWeek;
                    print scheduledLotteries.UniqueId uniqueDrawingId;
					print lottery.RemainingTimeInSecondsToNextDraw(scheduledLotteries, Now) timeInSecondsToNextDraw;

                    print lottery.IsFireBallTurnedOn hasFireBall;
                }}
            }}
            ");
            return result;
        }

        [HttpGet("api/triz/draw/byTime")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> TrizDrawsOrderedByTimeAsync([FromHeader(Name = "domain-url")] string domain, string countryCode)
        {
            if (String.IsNullOrWhiteSpace(HttpContext.Request.Headers["domain-url"])) return NotFound("Header 'domain-url' is not sent in request");

            if (String.IsNullOrWhiteSpace(countryCode))
            {
                countryCode = CountryTimeZone.DEFAULT.CountryCode;
            }

            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
            {{
                print Now now;
                print company.Sales.CurrentStore.TimeInSecondsToCloseStore timeInSecondsToCloseStore;
                domain=company.Sales.DomainFrom('{domain}');
                schedules=company.LotteryGamesPool.TrizLotteryGame.SchedulesByTimeOf(Now, domain);
                for (scheduledLotteries:schedules)
                {{
                    lottery=scheduledLotteries.Lottery;
                    print lottery.State.Abbreviation state;
                    print lottery.State.Name stateName;
                    drawDate = lottery.NextValidDrawDate(scheduledLotteries, Now, '{countryCode}');
                    print drawDate.HHmmAMPM() scheduledHour;
                    print drawDate 'drawDate';
                    print drawDate.MMddyyyyOrTodayOrTomorrow(Now) drawDateFormatted;
                    print scheduledLotteries.GetDescription() description;
                    print lottery.GameType() gameType;
                    print lottery.IsAlreadyDrawnAt(scheduledLotteries,Now) isAlreadyDrawnToday;
                    print lottery.IsScheduleMarkedAsNoActionAt(scheduledLotteries,Now) noAction;
                    print scheduledLotteries.IsScheduledAt(Now) isForToday;
                    print scheduledLotteries.IsScheduledSaturday() isScheduledSaturday;
                    print scheduledLotteries.IsScheduledSunday() isScheduledSunday;
                    print scheduledLotteries.ScheduledDays() daysOfWeek;
                    print scheduledLotteries.UniqueId uniqueDrawingId;
					print lottery.RemainingTimeInSecondsToNextDraw(scheduledLotteries, Now) timeInSecondsToNextDraw;
                }}
            }}
            ");
            return result;
        }

        [HttpGet("api/lotto/lotterydraws")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> AllOrderedByStateAsync([FromHeader(Name = "domain-url")] string domain)
        {
            return await DrawsOrderedByStateAsync(true, default(IdOfLotteryGame), domain);
        }


        [HttpGet("api/triz/draw/byState")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> TrizDrawsOrderedByStateAsync([FromHeader(Name = "domain-url")] string domain)
        {
            return await DrawsOrderedByStateAsync(false, IdOfLotteryGame.Triz, domain);
        }

        [HttpGet("api/picks/draw/byState")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> PicksDrawsOrderedByStateAsync([FromHeader(Name = "domain-url")] string domain)
        {
            return await DrawsOrderedByStateAsync(false, IdOfLotteryGame.Picks, domain);
        }

        private async Task<IActionResult> DrawsOrderedByStateAsync(bool useAllLotteryGamesFromPool, IdOfLotteryGame idOfLotteryGame, [FromHeader(Name = "domain-url")]string domain)
        {
            if (String.IsNullOrWhiteSpace(HttpContext.Request.Headers["domain-url"])) return NotFound("Header 'domain-url' is not sent in request");

            StringBuilder queryShedulesSource = new StringBuilder();
            if (useAllLotteryGamesFromPool)
            {
                queryShedulesSource.AppendLine($"schedules = company.LotteryGamesPool.SchedulesByState(Now, domain);");
            }
            else
            {
                queryShedulesSource.AppendLine($"lotteryGame = company.LotteryGamesPool.GetLotteryGame({idOfLotteryGame});");
                queryShedulesSource.AppendLine($"schedules = lotteryGame.SchedulesByState(Now, domain);");
            }

            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
            {{
                print Now now;
                print company.Sales.CurrentStore.TimeInSecondsToCloseStore timeInSecondsToCloseStore;
                domain = company.Sales.DomainFrom('{domain}');

                {queryShedulesSource}
                for (scheduledLotteries:schedules)
                {{
                    lottery=scheduledLotteries.Lottery;
                    print lottery.State.Abbreviation state;
                    print lottery.State.Name stateName;
                    drawDate = lottery.NextValidDrawDate(scheduledLotteries,Now);
                    print drawDate.HHmmAMPM() scheduledHour;
                    print drawDate 'drawDate';
                    print scheduledLotteries.GetDescription() description;
                    print lottery.GameType() gameType;
                    print lottery.IsAlreadyDrawnAt(scheduledLotteries,Now) isAlreadyDrawnToday;
                    print lottery.IsScheduleMarkedAsNoActionAt(scheduledLotteries,Now) noAction;
                    print scheduledLotteries.IsScheduledAt(Now) isForToday;
                    print scheduledLotteries.IsScheduledSaturday() isScheduledSaturday;
                    print scheduledLotteries.IsScheduledSunday() isScheduledSunday;
                    print scheduledLotteries.ScheduledDays() daysOfWeek;
                    print scheduledLotteries.UniqueId uniqueDrawingId;
					print lottery.RemainingTimeInSecondsToNextDraw(scheduledLotteries, Now) timeInSecondsToNextDraw;

                    idOfLotteryGame = '{idOfLotteryGame}';
                    if (idOfLotteryGame == '{IdOfLotteryGame.Picks}')
                    {{
                        print lottery.HasFireBall hasFireBall;
                        if (lottery.HasFireBall)
                        {{
                            print scheduledLotteries.FireballUniqueId fireballUniqueId;
                        }}
                    }}
                }}
            }}
            ");
            return result;
        }

        [HttpGet("api/triz/schedule/byState")]
        [Authorize(Roles = "DrawsDefinition")]
        public async Task<IActionResult> TrizSchedulesByStateAsync()
        {
            return await SchedulesByStateAsync(IdOfLotteryGame.Triz);
        }

        [HttpGet("api/picks/schedule/byState")]
        [Authorize(Roles = "DrawsDefinition")]
        public async Task<IActionResult> PicksSchedulesByStateAsync()
        {
            return await SchedulesByStateAsync(IdOfLotteryGame.Picks);
        }

        private async Task<IActionResult> SchedulesByStateAsync(IdOfLotteryGame idOfLotteryGame)
        {
            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
            {{
                lotteryGame = company.LotteryGamesPool.GetLotteryGame({idOfLotteryGame});
                lottoStates = lotteryGame.StatesWithLotteries();
                lastVersionOfDate = Now.Manana(); 
                for (states:lottoStates)
                {{
                    print states.Abbreviation state;
                    for (scheduledLotteries:lotteryGame.SchedulesBy(states))
                    {{
                        lottery = scheduledLotteries.Lottery;
                        drawDate = scheduledLotteries.ToDateTime(lastVersionOfDate);
                        print drawDate.HHmmAMPM() scheduledHour;
                        print lottery.GameType() gameType;
                        print scheduledLotteries.Id scheduleId;
                        print scheduledLotteries.ScheduledDays() daysOfWeek;
                        print scheduledLotteries.GetDescription() description;
                        print scheduledLotteries.WasDeleted() wasDeleted;
                        print lottery.LogFor(scheduledLotteries) log;

                        idOfLotteryGame = '{idOfLotteryGame}';
                        if (idOfLotteryGame == '{IdOfLotteryGame.Picks}')
                        {{
                            print lottery.IsFireBallTurnedOn hasFireBall;
                        }}
                    }}
                }}
            }}
            ");
            return result;
        }

        [HttpGet("api/lotto/schedule/powerball")]
        [Authorize(Roles = "a2")]
        public async Task<IActionResult> SchedulesForPowerBallAsync()
        {
            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
            {{
                lastVersionOfDate= Now.Manana();
                for (scheduledLotteries:lotto900.SchedulesForPowerBall())
                {{
                    lottery=scheduledLotteries.Lottery;
                    drawDate = scheduledLotteries.ToDateTime(lastVersionOfDate);
                    print drawDate.HHmmAMPM() scheduledHour;
                    print lottery.GameType() gameType;
                    print scheduledLotteries.Id scheduleId;
                    print scheduledLotteries.ScheduledDays() daysOfWeek;
                    print scheduledLotteries.GetDescription() description;
                    print scheduledLotteries.WasDeleted() wasDeleted;
                }}
            }}
            ");
            return result;
        }


        [HttpGet("api/lotto/schedule/byState/byHour")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> ScheduleWithGameTypesByStateAsync()
        {
            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
            {{
                lottoStates=lotto900.StatesWithLotteries();
                for (states:lottoStates)
                {{
                    print states.Abbreviation state;
                    for (scheduledLotteries:lotto900.ScheduleWithGameTypesBy(states))
                    {{
                        print scheduledLotteries.Hour scheduledHour;
                        for (gameTypes:scheduledLotteries.GameTypes)
                        {{
                            print gameTypes gameType;
                        }}
                    }}
                }}
            }}
            ");
            return result;
        }

        [HttpPost("api/lotto/draw/triz/confirmation")]
        [Authorize(Roles = "ConfirmDraw")]
        public async Task<IActionResult> TrizConfirmDrawAsync(int pickNumber, [FromBody] DrawConfirmation body)
        {
            return await ConfirmDrawAsync(IdOfLotteryGame.Triz, int.MinValue, body);
        }

        [HttpPost("api/lotto/draw/pick/{pickNumber:int:min(2):max(5)}/confirmation")]
        [Authorize(Roles = "ConfirmDraw")]
        public async Task<IActionResult> PickConfirmDrawAsync(int pickNumber, [FromBody] DrawConfirmation body)
        {
            return await ConfirmDrawAsync(IdOfLotteryGame.Picks, pickNumber, body);
        }

        private async Task<IActionResult> ConfirmDrawAsync(IdOfLotteryGame idOfLotteryGame, int pickNumber, [FromBody]DrawConfirmation body)
        {
            if (body == null) return BadRequest("Body is required");

            var drawDate = DateTime.ParseExact(body.DrawDate, "M/d/yyyy HH:mm:ss", Integration.CultureInfoEnUS);
            var stringDate = $"{drawDate.Month}/{drawDate.Day}/{drawDate.Year} {drawDate.Hour}:{drawDate.Minute}:00";

            StringBuilder lotteryCommand = new StringBuilder();
            lotteryCommand.AppendLine($"lotteryGame = company.LotteryGamesPool.GetLotteryGame({idOfLotteryGame});");
            string msgCannotBeConfirmed = string.Empty;
            string msgAlreadyConfirmed = string.Empty;
            if (idOfLotteryGame == IdOfLotteryGame.Picks)
            {
                if (string.IsNullOrWhiteSpace(body.State)) return BadRequest($"{nameof(body.State)} is required");
                if (pickNumber < 2 || pickNumber > 5) return BadRequest("Invalid pick number");
                lotteryCommand.AppendLine($"lottery = lotteryGame.GetLottery({pickNumber}, state{body.State});");
                msgCannotBeConfirmed = $"Drawing cannot be confirmed for pick {pickNumber}, state {body.State} and date {drawDate.Month}/{drawDate.Day}/{drawDate.Year} {drawDate.Hour}:{drawDate.Minute} {drawDate.ToString("tt")}";
                msgAlreadyConfirmed = $"Drawing is already confirmed for pick {pickNumber}, state {body.State} and date {drawDate.Month}/{drawDate.Day}/{drawDate.Year} {drawDate.Hour}:{drawDate.Minute} {drawDate.ToString("tt")}";
            }
            else if (idOfLotteryGame == IdOfLotteryGame.Triz)
            {
                lotteryCommand.AppendLine($"lottery = lotteryGame.GetLottery();");
                msgCannotBeConfirmed = $"Drawing cannot be confirmed for {IdOfLotteryGame.Triz} {drawDate.Hour}:{drawDate.Minute} {drawDate.ToString("tt")} and date {drawDate.Month}/{drawDate.Day}/{drawDate.Year}";
                msgAlreadyConfirmed = $"Drawing is already confirmed for {IdOfLotteryGame.Triz} {drawDate.Hour}:{drawDate.Minute} {drawDate.ToString("tt")} and date {drawDate.Month}/{drawDate.Day}/{drawDate.Year}";
            }
            else
            {
                return BadRequest("Invalid lottery game");
            }
            string employeeName = Security.UserName(HttpContext);
            var result = await LottoAPI.Actor.PerformChkThenCmdAsync(HttpContext, $@"
                {{
                    {lotteryCommand}
                    canBeConfirmed = lottery.CanBeConfirmed({stringDate});
                    Check(canBeConfirmed) Error '{msgCannotBeConfirmed}';
                    if (canBeConfirmed) 
                    {{
	                    wasAlreadyConfirmed = lottery.WasAlreadyConfirmed({stringDate});
	                    Check(!wasAlreadyConfirmed) Error '{msgAlreadyConfirmed}';
                    }}
                }}", $@"
                    {{
                    {lotteryCommand}
                    report = lottery.ConfirmDraw({stringDate}, Now, '{employeeName}', itIsThePresent, true);
                    print report.TotalPlayers totalPlayers;
                    print report.TotalWinners totalWinners;
                    print report.TotalTickets totalTickets;
                    print report.TotalTicketAmount totalTicketAmount;
                    print report.TotalPrize totalPrize;
                    print report.Profit profit;
                    print report.IsAlreadyGradedWithTheSameNumbers isAlreadyGradedWithTheSameNumbers;

                    gameType = lottery.GameType();
                    if (gameType != '{LotteryTriz.GAME_TYPE}')
                    {{
                        print report.WithFireBall withFireBall;
                        if (report.WithFireBall)
                        {{
                            print report.Fireball fireBall;
                        }}
                    }}
                }}
            ");
            return result;
        }

        [HttpPost("api/lotto/draw/powerball/confirmation")]
        [Authorize(Roles = "a11")]
        public async Task<IActionResult> ConfirmDrawPowerballAsync([FromBody]DrawConfirmationPowerball body)
        {
            if (body == null) return NotFound("Body is required");
            if (String.IsNullOrWhiteSpace(body.EmployeeName)) return NotFound($"Parameter {nameof(body.EmployeeName)} is required");

            var drawDate = DateTime.ParseExact(body.DrawDate, "M/d/yyyy HH:mm:ss", Integration.CultureInfoEnUS);
            var stringDate = $"{drawDate.Month}/{drawDate.Day}/{drawDate.Year} {drawDate.Hour}:{drawDate.Minute}:{drawDate.Second}";

            var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
                {{
                    lottery = lotto900.GetPowerball();
                    canBeConfirmed = lottery.CanBeConfirmed({stringDate});
                    print canBeConfirmed canBeConfirmed;
                    if (canBeConfirmed)
                    {{
                        report = lottery.ConfirmDraw({stringDate}, Now, '{body.EmployeeName}', itIsThePresent);
                        print report.TotalPlayers totalPlayers;
                        print report.TotalWinners totalWinners;
                        print report.TotalTickets totalTickets;
                        print report.TotalTicketAmount totalTicketAmount;
                        print report.TotalPrize totalPrize;
                        print report.Profit profit;
                        print report.IsAlreadyGradedWithTheSameNumbers isAlreadyGradedWithTheSameNumbers;
                    }}
                }}
            ");
            return result;
        }

        [HttpPost("api/lotto/draw/triz/noaction")]
        [Authorize(Roles = "NoAction")]
        public async Task<IActionResult> NoActionForDrawingTrizAsync([FromBody] DrawNoAction body)
        {
            return await NoActionForDrawingAsync(IdOfLotteryGame.Triz, int.MinValue, body);
        }

        [HttpPost("api/lotto/draw/pick/{pickNumber:int:min(2):max(5)}/noaction")]
        [Authorize(Roles = "NoAction")]
        public async Task<IActionResult> NoActionForDrawingPicksAsync(int pickNumber, [FromBody]DrawNoAction body)
        {
            return await NoActionForDrawingAsync(IdOfLotteryGame.Picks, pickNumber, body);
        }

        private async Task<IActionResult> NoActionForDrawingAsync(IdOfLotteryGame idOfLotteryGame, int pickNumber, [FromBody] DrawNoAction body)
        {
            if (body == null) return NotFound("Body is required");
            if (String.IsNullOrWhiteSpace(body.State)) return NotFound($"Parameter {nameof(body.State)} is required");
            if (String.IsNullOrWhiteSpace(body.EmployeeName)) return NotFound($"Parameter {nameof(body.EmployeeName)} is required");
            if (String.IsNullOrWhiteSpace(body.DrawDate)) return NotFound($"Parameter {nameof(body.DrawDate)} is required");

            string format = "M/d/yyyy HH:mm:ss";
            if (!DateTime.TryParseExact(body.DrawDate, format, Integration.CultureInfoEnUS, DateTimeStyles.None, out DateTime drawDate))
            {
                return BadRequest($"Invalid date format '{body.DrawDate}' with format '{format}'");
            }
            var stringDate = $"{drawDate.Month}/{drawDate.Day}/{drawDate.Year} {drawDate.Hour}:{drawDate.Minute}:{drawDate.Second}";

            StringBuilder cmdNoAction = new StringBuilder();
            cmdNoAction.AppendLine($"lotteryGame = company.LotteryGamesPool.GetLotteryGame({idOfLotteryGame});");
            if (idOfLotteryGame == IdOfLotteryGame.Picks)
            {
                if (pickNumber < 2 || pickNumber > 5) return BadRequest("Invalid pick number");
                cmdNoAction.AppendLine($"lottery = lotteryGame.GetLottery({pickNumber}, state{body.State});");
            }
            else if (idOfLotteryGame == IdOfLotteryGame.Triz)
            {
                cmdNoAction.AppendLine($"lottery = lotteryGame.GetLottery();");
            }
            else
            {
                return BadRequest("Invalid lottery game");
            }
            cmdNoAction.AppendLine($"report = lottery.SetNoAction(itIsThePresent, {stringDate}, Now, '{body.EmployeeName}', true);");

            var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
                {{
                    {cmdNoAction}
                    print report.TotalTickets totalTickets;
                }}
            ");
            return result;
        }

        [HttpPost("api/lotto/draw/powerball/noaction")]
        [Authorize(Roles = "a7")]
        public async Task<IActionResult> NoActionForDrawingPowerballAsync([FromBody]DrawNoActionPowerball body)
        {
            if (body == null) return NotFound("Body is required");
            if (String.IsNullOrWhiteSpace(body.EmployeeName)) return NotFound($"Parameter {nameof(body.EmployeeName)} is required");
            if (String.IsNullOrWhiteSpace(body.DrawDate)) return NotFound($"Parameter {nameof(body.DrawDate)} is required");

            var drawDate = DateTime.ParseExact(body.DrawDate, "M/d/yyyy HH:mm:ss", Integration.CultureInfoEnUS);
            var stringDate = $"{drawDate.Month}/{drawDate.Day}/{drawDate.Year} {drawDate.Hour}:{drawDate.Minute}:{drawDate.Second}";
            var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
                {{
                    lottery = lotto900.GetPowerball();
                    report = lottery.SetNoAction(itIsThePresent, {stringDate}, Now, '{body.EmployeeName}');
                    print report.TotalTickets totalTickets;
                }}
            ");
            return result;
        }

        [HttpPost("api/lotto/draw/keno/noaction")]
        [Authorize(Roles = "Ka7")]
        public async Task<IActionResult> NoActionForDrawingKenoAsync([FromBody] DrawNoActionKeno body)
        {
            if (body == null) return BadRequest("Body is required");
            if (String.IsNullOrWhiteSpace(body.DrawDate)) return BadRequest($"Parameter {nameof(body.DrawDate)} is required");

            string employeeName = Security.UserName(HttpContext);
            var drawDate = DateTime.ParseExact(body.DrawDate, "M/d/yyyy HH:mm:ss", Integration.CultureInfoEnUS);
            var stringDate = $"{drawDate.Month}/{drawDate.Day}/{drawDate.Year} {drawDate.Hour}:{drawDate.Minute}:{drawDate.Second}";
            var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
                {{
                    report = keno.SetNoAction(itIsThePresent, {stringDate}, Now, '{employeeName}');
                    print report.TotalTickets totalTickets;
                }}
            ");
            return result;
        }

        [HttpPost("api/lotto/draw/triz/noaction/preview")]
        [Authorize(Roles = "NoAction")]
        public async Task<IActionResult> TrizPreviewNoActionForDrawingAsync([FromBody] DrawNoActionPreview body)
        {
            return await PreviewNoActionForDrawingAsync(IdOfLotteryGame.Triz, int.MinValue, body);
        }

        [HttpPost("api/lotto/draw/pick/{pickNumber:int:min(2):max(5)}/noaction/preview")]
        [Authorize(Roles = "NoAction")]
        public async Task<IActionResult> PickPreviewNoActionForDrawingAsync(int pickNumber, [FromBody] DrawNoActionPreview body)
        {
            return await PreviewNoActionForDrawingAsync(IdOfLotteryGame.Picks, pickNumber, body);
        }

        private async Task<IActionResult> PreviewNoActionForDrawingAsync(IdOfLotteryGame idOfLotteryGame, int pickNumber, [FromBody]DrawNoActionPreview body)
        {
            if (body == null) return NotFound("Body is required");
            if (String.IsNullOrWhiteSpace(body.DrawDate)) return NotFound($"Parameter {nameof(body.DrawDate)} is required");

            StringBuilder previewNoActionCmd = new StringBuilder();
            previewNoActionCmd.AppendLine($"lotteryGame = company.LotteryGamesPool.GetLotteryGame({idOfLotteryGame});");
            if (idOfLotteryGame == IdOfLotteryGame.Picks)
            {
                if (pickNumber < 2 || pickNumber > 5) return BadRequest("Invalid pick number");
                if (String.IsNullOrWhiteSpace(body.State)) return NotFound($"Parameter {nameof(body.State)} is required");
                previewNoActionCmd.AppendLine($"lottery = lotteryGame.GetLottery({pickNumber}, state{body.State});");
            }
            else if (idOfLotteryGame == IdOfLotteryGame.Triz)
            {
                previewNoActionCmd.AppendLine($"lottery = lotteryGame.GetLottery();");
            }
            else
            {
                return BadRequest("Invalid lottery game");
            }

            var drawDate = DateTime.ParseExact(body.DrawDate, "M/d/yyyy HH:mm:ss", Integration.CultureInfoEnUS);
            var stringDate = $"{drawDate.Month}/{drawDate.Day}/{drawDate.Year} {drawDate.Hour}:{drawDate.Minute}:{drawDate.Second}";
            var result = await LottoAPI.Actor.PerformQryAsync(/*This is a query because only have prints*/HttpContext, $@"
                {{
                    {previewNoActionCmd}
                    report = lottery.PreviewNoAction({stringDate});
                    print report.TotalTickets totalTickets;
                }}
            ");
            return result;
        }

        [HttpPost("api/lotto/draw/powerball/noaction/preview")]
        [Authorize(Roles = "a7")]
        public async Task<IActionResult> PreviewNoActionForDrawingPowerballAsync([FromBody]DrawNoActionPreviewPowerball body)
        {
            if (body == null) return NotFound("Body is required");
            if (String.IsNullOrWhiteSpace(body.DrawDate)) return NotFound($"Parameter {nameof(body.DrawDate)} is required");

            var drawDate = DateTime.ParseExact(body.DrawDate, "M/d/yyyy HH:mm:ss", Integration.CultureInfoEnUS);
            var stringDate = $"{drawDate.Month}/{drawDate.Day}/{drawDate.Year} {drawDate.Hour}:{drawDate.Minute}:{drawDate.Second}";
            var result = await LottoAPI.Actor.PerformQryAsync(/*This is a query because only have prints*/HttpContext, $@"
                {{
                    lottery = lotto900.GetPowerball();
                    report = lottery.PreviewNoAction({stringDate});
                    print report.TotalTickets totalTickets;
                }}
            ");
            return result;
        }

        [HttpPost("api/lotto/draw/triz")]
        [Authorize(Roles = "Grade")]
        public async Task<IActionResult> DrawTrizLotteryAsync([FromBody] DrawnLottery body)
        {
            return await DrawLotteryAsync(IdOfLotteryGame.Triz, int.MinValue, body);
        }

        [HttpPost("api/lotto/draw/pick/{pickNumber:int:min(2):max(5)}")]
        [Authorize(Roles = "Grade")]
        public async Task<IActionResult> DrawPicksLotteryAsync(int pickNumber, [FromBody] DrawnLottery body)
        {
            return await DrawLotteryAsync(IdOfLotteryGame.Picks, pickNumber, body);
        }

        private async Task<IActionResult> DrawLotteryAsync(IdOfLotteryGame idOfLotteryGame, int pickNumber, [FromBody]DrawnLottery body)
        {
            if (body == null) return NotFound("Body is required");
            var winnerNumbers = Validator.StringEscape(body.WinnerNumbers);
            if (String.IsNullOrWhiteSpace(body.State)) return NotFound($"Parameter {nameof(body.State)} is required");
            if (String.IsNullOrWhiteSpace(winnerNumbers)) return NotFound($"Parameter {winnerNumbers} is required");
            if (String.IsNullOrWhiteSpace(body.EmployeeName)) return NotFound($"Parameter {nameof(body.EmployeeName)} is required");

            string format = "M/d/yyyy HH:mm:ss";
            if (!DateTime.TryParseExact(body.DrawDate, format, Integration.CultureInfoEnUS, DateTimeStyles.None, out DateTime drawDate))
            {
                return BadRequest($"Invalid date format '{body.DrawDate}' with format '{format}'");
            }
            var stringDate = $"{drawDate.Month}/{drawDate.Day}/{drawDate.Year} {drawDate.Hour}:{drawDate.Minute}:{drawDate.Second}";

            StringBuilder checkDrawLottery = new StringBuilder();
            StringBuilder cmdDrawLottery = new StringBuilder();
            checkDrawLottery.AppendLine($"lotteryGame = company.LotteryGamesPool.GetLotteryGame({idOfLotteryGame});");
            cmdDrawLottery.AppendLine($"lotteryGame = company.LotteryGamesPool.GetLotteryGame({idOfLotteryGame});");

            bool withFireBall = body.FireBallNumber != LotteryDraw.WITHOUT_FIREBALL;
            if (withFireBall)
            {
                if (body.FireBallNumber < 0 || body.FireBallNumber > 9) return BadRequest($"Parameter {nameof(body.FireBallNumber)} not valid, check range from 0 to 9.");
                
                if (idOfLotteryGame == IdOfLotteryGame.Picks)
                {
                    checkDrawLottery.AppendLine($"lottery = lotteryGame.GetLottery({pickNumber}, state{body.State});");
                    checkDrawLottery.AppendLine(@$"needFireBallNumber = lottery.IsFireBallTurnedOn;");
                    checkDrawLottery.AppendLine(@$"Check(needFireBallNumber) Error 'Fireball number is not required in lottery with pick number {pickNumber} in state {body.State}.';");
                    cmdDrawLottery.AppendLine($"lottery = lotteryGame.GetLottery({pickNumber}, state{body.State});");
                    cmdDrawLottery.AppendLine($"report = lottery.DrawPicks({stringDate}, '{winnerNumbers}', {body.FireBallNumber}, Now, ItIsThePresent, '{body.EmployeeName}', true);");
                }
                else
                {
                    return BadRequest("Invalid Lottery Game");
                }
            }
            else
            {
                if (idOfLotteryGame == IdOfLotteryGame.Picks)
                {
                    checkDrawLottery.AppendLine($"lottery = lotteryGame.GetLottery({pickNumber}, state{body.State});");
                    checkDrawLottery.AppendLine(@$"needFireBallNumber = lottery.IsFireBallTurnedOn;");
                    checkDrawLottery.AppendLine(@$"Check(!needFireBallNumber) Error 'Fireball number is required in lottery with pick number {pickNumber} in state {body.State}.';");
                    cmdDrawLottery.AppendLine($"lottery = lotteryGame.GetLottery({pickNumber}, state{body.State});");
                    cmdDrawLottery.AppendLine($"report = lottery.DrawPicks({stringDate}, '{winnerNumbers}', Now, ItIsThePresent, '{body.EmployeeName}', true);");
                }
                else if (idOfLotteryGame == IdOfLotteryGame.Triz)
                {
                    if (winnerNumbers.Length != 5) return BadRequest($"Parameter {winnerNumbers} is not valid, triz length is 5 numbers.");
                    cmdDrawLottery.AppendLine($"lottery = lotteryGame.GetLottery();");
                    cmdDrawLottery.AppendLine($"report = lottery.DrawTriz({stringDate}, '{winnerNumbers}', Now, ItIsThePresent, '{body.EmployeeName}', true);");
                }
                else
                {
                    return BadRequest("Invalid Lottery Game");
                }
            }

            var result = await LottoAPI.Actor.PerformChkThenCmdAsync(HttpContext, $@"
                {{
                    {checkDrawLottery}
                }}
                ", $@"
                {{
                    {cmdDrawLottery}
                    print report.TotalPlayers totalPlayers;
                    print report.TotalWinners totalWinners;
                    print report.TotalTickets totalTickets;
                    print report.TotalTicketAmount totalTicketAmount;
                    print report.TotalPrize totalPrize;
                    print report.Profit profit;
                    print report.IsAlreadyGradedWithTheSameNumbers isAlreadyGradedWithTheSameNumbers;
                }}
            ");
            return result;
        }

        [HttpPost("api/lotto/draw/keno")]
        [Authorize(Roles = "Ka6")]
        public async Task<IActionResult> DrawLotteryKenoAsync([FromBody] DrawnLotteryKeno body)
        {
            if (body == null) return BadRequest("Body is required");

            string employeeName = Security.UserName(HttpContext);

            var winners = string.Join(",", body.WinnerNumbers);

            if (string.IsNullOrWhiteSpace(winners)) return BadRequest($"Parameter {winners} is required");

            var result = LottoAPI.Actor.PerformQry($@"
                        {{
                            isAlreadyGradedWithTheSameNumbers = keno.IsAlreadyGradedWithTheSameNumbers('{body.DrawPrefixId}',{{{winners}}},{body.Multiplier},{body.Bulleye}, Now);
                            isAlreadyGraded = keno.IsAlreadyGraded('{body.DrawPrefixId}',{{{winners}}},{body.Multiplier},{body.Bulleye}, Now);
                            print isAlreadyGradedWithTheSameNumbers isAlreadyGradedWithTheSameNumbers;
                            print isAlreadyGraded isAlreadyGraded;
                        }}
                    ");

            if (!(result is OkObjectResult)) throw new Exception(((ObjectResult)result).ToString());

            OkObjectResult o = (OkObjectResult)result;
            string json = o.Value.ToString();
            DrawResponse existCustomer = JsonConvert.DeserializeObject<DrawResponse>(json);

            if (existCustomer.IsAlreadyGraded )
                throw new GameEngineException($"{body.DrawPrefixId} is already Graded.");
            if (existCustomer.IsAlreadyGraded && !existCustomer.IsAlreadyGradedWithTheSameNumbers)
                throw new GameEngineException($"{body.DrawPrefixId} is already Graded with differente numbers.");

            result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
                {{
                    report = keno.DrawKeno('{body.DrawPrefixId}','{winners}',{body.Multiplier},{body.Bulleye}, Now, ItIsThePresent, '{employeeName}');
                    print report.TotalPlayers totalPlayers;
                    print report.TotalWinners totalWinners;
                    print report.TotalTickets totalTickets;
                    print report.TotalTicketAmount totalTicketAmount;
                    print report.TotalPrize totalPrize;
                    print report.Profit profit;
                    print report.IsAlreadyGradedWithTheSameNumbers isAlreadyGradedWithTheSameNumbers;
                }}
            ");
            return result;
        }

        [HttpPost("api/lotto/draw/powerball")]
        [Authorize(Roles = "a6")]
        public async Task<IActionResult> DrawLotteryPowerballAsync([FromBody]DrawnLotteryPowerball body)
        {
            if (body == null) return NotFound("Body is required");
            var winnerNumbers = body.WinnerNumbers.Replace("\\", @"\u005C").Replace("'", "\\\'");
            if (String.IsNullOrWhiteSpace(winnerNumbers)) return NotFound($"Parameter {winnerNumbers} is required");
            if (String.IsNullOrWhiteSpace(body.EmployeeName)) return NotFound($"Parameter {body.EmployeeName} is required");

            var drawDate = DateTime.ParseExact(body.DrawDate, "M/d/yyyy HH:mm:ss", Integration.CultureInfoEnUS);
            var stringDate = $"{drawDate.Month}/{drawDate.Day}/{drawDate.Year} {drawDate.Hour}:{drawDate.Minute}:{drawDate.Second}";
            var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
                {{
                    lottery = lotto900.GetPowerball();
                    report = lottery.DrawPowerBall({stringDate},'{winnerNumbers}',{body.Multiplier}, Now, ItIsThePresent, '{body.EmployeeName}');
                    print report.TotalPlayers totalPlayers;
                    print report.TotalWinners totalWinners;
                    print report.TotalTickets totalTickets;
                    print report.TotalTicketAmount totalTicketAmount;
                    print report.TotalPrize totalPrize;
                    print report.Profit profit;
                    print report.IsAlreadyGradedWithTheSameNumbers isAlreadyGradedWithTheSameNumbers;
                }}
            ");
            return result;
        }

        [HttpPost("api/lotto/draw/regrade/triz")]
        [Authorize(Roles = "Regrade")]
        public async Task<IActionResult> TrizRegradeDrawAsync(int pickNumber, [FromBody] RegradedLottery body)
        {
            return await RegradeDrawAsync(IdOfLotteryGame.Triz, int.MinValue, body);
        }

        [HttpPost("api/lotto/draw/regrade/pick/{pickNumber:int:min(2):max(5)}")]
        [Authorize(Roles = "Regrade")]
        public async Task<IActionResult> PicksRegradeDrawAsync(int pickNumber, [FromBody] RegradedLottery body)
        {
            return await RegradeDrawAsync(IdOfLotteryGame.Picks, pickNumber, body);
        }

        private async Task<IActionResult> RegradeDrawAsync(IdOfLotteryGame idOfLotteryGame, int pickNumber, [FromBody]RegradedLottery body)
        {
            if (body == null) return NotFound("Body is required");
            if (String.IsNullOrWhiteSpace(body.State)) return NotFound($"Parameter {body.State} is required");
            if (String.IsNullOrWhiteSpace(body.EmployeeName)) return NotFound($"Parameter {body.EmployeeName} is required");

            var drawDate = DateTime.ParseExact(body.DrawDate, "M/d/yyyy HH:mm:ss", Integration.CultureInfoEnUS);
            var stringDate = $"{drawDate.Month}/{drawDate.Day}/{drawDate.Year} {drawDate.Hour}:{drawDate.Minute}:{drawDate.Second}";

            StringBuilder cmdRegradeLottery = new StringBuilder();
            cmdRegradeLottery.AppendLine($"lotteryGame = company.LotteryGamesPool.GetLotteryGame({idOfLotteryGame});");
            switch (idOfLotteryGame)
            {
                case IdOfLotteryGame.Triz:
                    cmdRegradeLottery.AppendLine($"lottery = lotteryGame.GetLottery();");
                    break;
                case IdOfLotteryGame.Picks:
                    cmdRegradeLottery.AppendLine($"lottery = lotteryGame.GetLottery({pickNumber}, state{body.State});");
                    break;
                default:
                    return BadRequest("Invalid Lottery Game");
            }
            cmdRegradeLottery.AppendLine($"report = lottery.Regrade(ItIsThePresent, {stringDate}, Now, '{body.EmployeeName}', true);");

            var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
                {{
                    {cmdRegradeLottery}
                    print report.TotalPlayers totalPlayers;
                    print report.TotalWinners totalWinners;
                    print report.TotalTickets totalTickets;
                    print report.TotalTicketAmount totalTicketAmount;
                }}
            ");
            return result;
        }

        [HttpPost("api/lotto/draw/regrade/powerball")]
        [Authorize(Roles = "a5")]
        public async Task<IActionResult> RegradeDrawPowerballAsync([FromBody]RegradedLotteryPowerball body)
        {
            if (body == null) return NotFound("Body is required");
            if (body.Multiplier <= 0) return NotFound($"Parameter {body.Multiplier} is required");
            if (String.IsNullOrWhiteSpace(body.EmployeeName)) return NotFound($"Parameter {body.EmployeeName} is required");

            var drawDate = DateTime.ParseExact(body.DrawDate, "M/d/yyyy HH:mm:ss", Integration.CultureInfoEnUS);
            var stringDate = $"{drawDate.Month}/{drawDate.Day}/{drawDate.Year} {drawDate.Hour}:{drawDate.Minute}:{drawDate.Second}";
            var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
                {{
                    lottery = lotto900.GetPowerball();
                    report = lottery.Regrade(ItIsThePresent, {stringDate}, {body.Multiplier}, Now, '{body.EmployeeName}');
                    print report.TotalPlayers totalPlayers;
                    print report.TotalWinners totalWinners;
                    print report.TotalTickets totalTickets;
                    print report.TotalTicketAmount totalTicketAmount;
                    print report.TotalPrize totalPrize;
                    print report.Profit profit;
                }}
            ");
            return result;
        }

        [HttpPost("api/lotto/draw/keno/regrade")]
        [Authorize(Roles = "Ka5")]
        public async Task<IActionResult> RegradeDrawKenoAsync([FromBody] RegradedLotteryKeno body)
        {
            if (body == null) return BadRequest("Body is required");

            string employeeName = Security.UserName(HttpContext);
            var drawDate = DateTime.ParseExact(body.DrawDate, "M/d/yyyy HH:mm:ss", Integration.CultureInfoEnUS);
            var stringDate = $"{drawDate.Month}/{drawDate.Day}/{drawDate.Year} {drawDate.Hour}:{drawDate.Minute}:{drawDate.Second}";
            string drawPrefixId = KenoNextDraw.Date2PrefixId(drawDate);

            if (string.IsNullOrEmpty(body.DrawPrefixId))
            {
                drawPrefixId = KenoNextDraw.Date2PrefixId(drawDate);
            }

            if (string.IsNullOrEmpty(drawPrefixId)) return BadRequest($"The atributes {nameof(KenoNextDraw.Date2PrefixId)} or {nameof(body.DrawDate)} are required.");

            var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
                {{
                    report = keno.RegradeKeno(ItIsThePresent, {stringDate}, Now, '{employeeName}');
                    print report.TotalPlayers totalPlayers;
                    print report.TotalWinners totalWinners;
                    print report.TotalTickets totalTickets;
                    print report.TotalTicketAmount totalTicketAmount;
                    print report.TotalPrize totalPrize;
                    print report.Profit profit;
                }}
            ");
            return result;
        }

        public static DateTime UpdateTime(DateTime date, string timeString)
        {
            if (DateTime.TryParseExact(timeString, "h:m tt", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime parsedTime))
            {
                return new DateTime(date.Year, date.Month, date.Day, parsedTime.Hour, parsedTime.Minute, date.Second);
            }
            return DateTime.MinValue;
        }

        [HttpPost("api/lotto/draw/triz/offsetdate")]
        [Authorize(Roles = "c2")]
        public async Task<IActionResult> TrizAttachDateAsync([FromBody] ScheduleDate body)
        {
            return await AttachDateAsync(IdOfLotteryGame.Triz, int.MinValue, body);
        }

        [HttpPost("api/lotto/draw/pick/{pickNumber:int:min(2):max(5)}/offsetdate")]
        [Authorize(Roles = "c2")]
        public async Task<IActionResult> PickAttachDateAsync(int pickNumber, [FromBody] ScheduleDate body)
        {
            return await AttachDateAsync(IdOfLotteryGame.Picks, pickNumber, body);
        }

        private async Task<IActionResult> AttachDateAsync(IdOfLotteryGame idOfLotteryGame, int pickNumber, [FromBody] ScheduleDate body)
        {
            if (body == null) return NotFound("Body is required");
            if (String.IsNullOrWhiteSpace(body.DrawDate)) return NotFound($"Parameter {nameof(body.DrawDate)} is required");
            if (String.IsNullOrWhiteSpace(body.State)) return NotFound($"Parameter {nameof(body.State)} is required");
            if (String.IsNullOrWhiteSpace(body.EmployeeName)) return NotFound($"Parameter {nameof(body.EmployeeName)} is required");

            StringBuilder lotteryCommand = new StringBuilder();
            lotteryCommand.AppendLine($"lotteryGame = company.LotteryGamesPool.GetLotteryGame({idOfLotteryGame});");
            switch (idOfLotteryGame)
            {
                case IdOfLotteryGame.Picks:
                    if (pickNumber < 2 || pickNumber > 5) return BadRequest("Invalid pick number");
                    lotteryCommand.AppendLine($"lottery = lotteryGame.GetLottery({pickNumber}, state{body.State});");
                    break;
                case IdOfLotteryGame.Triz:
                    lotteryCommand.AppendLine($"lottery = lotteryGame.GetLottery();");
                    break;
                default:
                    return BadRequest("Invalid Lottery Game");
            }

            var drawDate = DateTime.ParseExact(body.DrawDate, "M/d/yyyy HH:mm:ss", Integration.CultureInfoEnUS);
            var stringDate = $"{drawDate.Month}/{drawDate.Day}/{drawDate.Year} {drawDate.Hour}:{drawDate.Minute}:{drawDate.Second}";

            DateTime buildNewDrawDate = UpdateTime(drawDate, body.ScheduledHour);
            if (buildNewDrawDate == DateTime.MinValue) return BadRequest("Invalid time format for scheduled hour");

            var stringNewDate = $"{buildNewDrawDate.Month}/{buildNewDrawDate.Day}/{buildNewDrawDate.Year} {buildNewDrawDate.Hour}:{buildNewDrawDate.Minute}:{buildNewDrawDate.Second}";
            var result = await LottoAPI.Actor.PerformChkThenCmdAsync(HttpContext, $@"
                {{
                    {lotteryCommand}
                    hasSchedule = lottery.AnyScheduleAt({stringDate});
                    Check(hasSchedule) Error 'There is no draw or schedule for date {stringDate}.';
                    existPendingTickets = lottery.ExistPendingTicketsAt({stringDate});
                    Check(!existPendingTickets) Error 'There are pending tickets for date {stringDate}.';
                    sameDate = ({stringDate} == {stringNewDate});
                    Check(!sameDate) Error 'The new date is the same as the draw date.';
                    newDateLowerThanNow = ({stringNewDate} < Now);
                    Check(!newDateLowerThanNow) Error 'The new date is lower than the server date.';
                }}
            ", $@"
                {{
                    {lotteryCommand}
                    lottery.AttachToDate({stringDate}, {stringNewDate}, Now, '{body.EmployeeName}');
                }}
            ");
            return result;
        }

        [HttpPut("api/lotto/draw/triz/offsetdate")]
        [Authorize(Roles = "c2")]
        public async Task<IActionResult> EditTrizAttachDateAsync([FromBody] ScheduleDate body)
        {
            return await EditAttachDateAsync(IdOfLotteryGame.Triz, int.MinValue, body);
        }

        [HttpPut("api/lotto/draw/pick/{pickNumber:int:min(2):max(5)}/offsetdate")]
        [Authorize(Roles = "c2")]
        public async Task<IActionResult> EditPickAttachDateAsync(int pickNumber, [FromBody] ScheduleDate body)
        {
            return await EditAttachDateAsync(IdOfLotteryGame.Picks, pickNumber, body);
        }

        private async Task<IActionResult> EditAttachDateAsync(IdOfLotteryGame idOfLotteryGame, int pickNumber, [FromBody] ScheduleDate body)
        {
            if (body == null) return NotFound("Body is required");
            if (String.IsNullOrWhiteSpace(body.DrawDate)) return NotFound($"Parameter {nameof(body.DrawDate)} is required");
            if (String.IsNullOrWhiteSpace(body.State)) return NotFound($"Parameter {nameof(body.State)} is required");
            if (String.IsNullOrWhiteSpace(body.EmployeeName)) return NotFound($"Parameter {nameof(body.EmployeeName)} is required");

            StringBuilder lotteryCommand = new StringBuilder();
            lotteryCommand.AppendLine($"lotteryGame = company.LotteryGamesPool.GetLotteryGame({idOfLotteryGame});");
            switch (idOfLotteryGame)
            {
                case IdOfLotteryGame.Picks:
                    if (pickNumber < 2 || pickNumber > 5) return BadRequest("Invalid pick number");
                    lotteryCommand.AppendLine($"lottery = lotteryGame.GetLottery({pickNumber}, state{body.State});");
                    break;
                case IdOfLotteryGame.Triz:
                    lotteryCommand.AppendLine($"lottery = lotteryGame.GetLottery();");
                    break;
                default:
                    return BadRequest("Invalid Lottery Game");
            }

            var drawDate = DateTime.ParseExact(body.DrawDate, "M/d/yyyy HH:mm:ss", Integration.CultureInfoEnUS);
            var stringDate = $"{drawDate.Month}/{drawDate.Day}/{drawDate.Year} {drawDate.Hour}:{drawDate.Minute}:{drawDate.Second}";

            DateTime buildNewDrawDate = UpdateTime(drawDate, body.ScheduledHour);
            if (buildNewDrawDate == DateTime.MinValue) return BadRequest("Invalid time format for scheduled hour");

            var stringNewDate = $"{buildNewDrawDate.Month}/{buildNewDrawDate.Day}/{buildNewDrawDate.Year} {buildNewDrawDate.Hour}:{buildNewDrawDate.Minute}:{buildNewDrawDate.Second}";
            var result = await LottoAPI.Actor.PerformChkThenCmdAsync(HttpContext, $@"
                {{
                    {lotteryCommand}
                    hasSchedule = lottery.AnyScheduleAt({stringDate});
                    Check(hasSchedule) Error 'There is no draw or schedule for date {stringDate}.';
                    existPendingTickets = lottery.ExistPendingTicketsAt({stringDate});
                    Check(!existPendingTickets) Error 'There are pending tickets for date {stringDate}.';
                    sameDate = ({stringDate} == {stringNewDate});
                    Check(!sameDate) Error 'The new date is the same as the draw date.';
                    newDateLowerThanNow = ({stringNewDate} < Now);
                    Check(!newDateLowerThanNow) Error 'The new date is lower than the server date.';
                }}
            ", $@"
                {{
                    {lotteryCommand}
                    lottery.EditAttachToDate({stringDate}, {stringNewDate}, Now, '{body.EmployeeName}');
                }}
            ");
            return result;
        }

        [HttpPost("api/lotto/draw/pick/{pickNumber:int:min(2):max(5)}/enabled")]
        [Authorize(Roles = "EnableDisableStatus")]
        public async Task<IActionResult> PickEnableDrawAsync(int pickNumber, [FromBody]EnabledDraw body)
        {
            if (body == null) return NotFound("Body is required");
            if (String.IsNullOrWhiteSpace(body.DrawDate)) return NotFound($"Parameter {nameof(body.DrawDate)} is required");
            if (String.IsNullOrWhiteSpace(body.State)) return NotFound($"Parameter {nameof(body.State)} is required");
            if (String.IsNullOrWhiteSpace(body.Url)) return NotFound($"Parameter {nameof(body.Url)} is required");
            if (String.IsNullOrWhiteSpace(body.EmployeeName)) return NotFound($"Parameter {nameof(body.EmployeeName)} is required");

            var drawDate = DateTime.ParseExact(body.DrawDate, "M/d/yyyy HH:mm:ss", Integration.CultureInfoEnUS);
            var stringDate = $"{drawDate.Month}/{drawDate.Day}/{drawDate.Year} {drawDate.Hour}:{drawDate.Minute}:{drawDate.Second}";
            var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
                {{
                    domain = company.Sales.DomainFrom('{body.Url}');
                    lotteryGame = company.LotteryGamesPool.GetLotteryGame({IdOfLotteryGame.Picks});
                    lottery = lotteryGame.GetLottery({pickNumber}, state{body.State});
                    lottery.EnableDraw({stringDate}, domain, Now, '{body.EmployeeName}');
                }}
            ");
            return result;
        }

        [HttpPost("api/lotto/draw/triz/enabled")]
        [Authorize(Roles = "EnableDisableStatus")]
        public async Task<IActionResult> TrizEnableDrawAsync([FromBody] EnabledDraw body)
        {
            if (body == null) return NotFound("Body is required");
            if (String.IsNullOrWhiteSpace(body.DrawDate)) return NotFound($"Parameter {nameof(body.DrawDate)} is required");
            if (String.IsNullOrWhiteSpace(body.Url)) return NotFound($"Parameter {nameof(body.Url)} is required");
            if (String.IsNullOrWhiteSpace(body.EmployeeName)) return NotFound($"Parameter {nameof(body.EmployeeName)} is required");

            var drawDate = DateTime.ParseExact(body.DrawDate, "M/d/yyyy HH:mm:ss", Integration.CultureInfoEnUS);
            var stringDate = $"{drawDate.Month}/{drawDate.Day}/{drawDate.Year} {drawDate.Hour}:{drawDate.Minute}:{drawDate.Second}";
            var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
                {{
                    domain = company.Sales.DomainFrom('{body.Url}');
                    lotteryGame = company.LotteryGamesPool.GetLotteryGame({IdOfLotteryGame.Triz});
                    lottery = lotteryGame.GetLottery();
                    lottery.EnableDraw({stringDate}, domain, Now, '{body.EmployeeName}');
                }}
            ");
            return result;
        }

        [HttpPost("api/lotto/draw/powerball/enabled")]
        [Authorize(Roles = "a9")]
        public async Task<IActionResult> EnableDrawPowerballAsync([FromBody]EnabledDrawPowerball body)
        {
            if (body == null) return NotFound("Body is required");
            if (String.IsNullOrWhiteSpace(body.DrawDate)) return NotFound($"Parameter {nameof(body.DrawDate)} is required");
            if (String.IsNullOrWhiteSpace(body.Url)) return NotFound($"Parameter {nameof(body.Url)} is required");
            if (String.IsNullOrWhiteSpace(body.EmployeeName)) return NotFound($"Parameter {nameof(body.EmployeeName)} is required");

            var drawDate = DateTime.ParseExact(body.DrawDate, "M/d/yyyy HH:mm:ss", Integration.CultureInfoEnUS);
            var stringDate = $"{drawDate.Month}/{drawDate.Day}/{drawDate.Year} {drawDate.Hour}:{drawDate.Minute}:{drawDate.Second}";
            var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
                {{
                    domain = company.Sales.DomainFrom('{body.Url}');
                    lottery = lotto900.GetPowerball();
                    lottery.EnableDraw({stringDate}, domain, Now, '{body.EmployeeName}');
                }}
            ");
            return result;
        }

        [HttpPost("api/lotto/draw/pick/{pickNumber:int:min(2):max(5)}/disabled")]
        [Authorize(Roles = "EnableDisableStatus")]
        public async Task<IActionResult> PickDisableDrawAsync(int pickNumber, [FromBody]EnabledDraw body)
        {
            if (body == null) return NotFound("Body is required");
            if (String.IsNullOrWhiteSpace(body.DrawDate)) return NotFound($"Parameter {nameof(body.DrawDate)} is required");
            if (String.IsNullOrWhiteSpace(body.Url)) return NotFound($"Parameter {nameof(body.Url)} is required");
            if (String.IsNullOrWhiteSpace(body.EmployeeName)) return NotFound($"Parameter {nameof(body.EmployeeName)} is required");

            var drawDate = DateTime.ParseExact(body.DrawDate, "M/d/yyyy HH:mm:ss", Integration.CultureInfoEnUS);
            var stringDate = $"{drawDate.Month}/{drawDate.Day}/{drawDate.Year} {drawDate.Hour}:{drawDate.Minute}:{drawDate.Second}";
            var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
                {{
                    domain = company.Sales.DomainFrom('{body.Url}');
                    lotteryGame = company.LotteryGamesPool.GetLotteryGame({IdOfLotteryGame.Picks});
                    lottery = lotteryGame.GetLottery({pickNumber}, state{body.State});
                    lottery.DisableDraw({stringDate}, domain, Now, '{body.EmployeeName}');
                }}
            ");
            return result;
        }

        [HttpPost("api/lotto/draw/triz/disabled")]
        [Authorize(Roles = "EnableDisableStatus")]
        public async Task<IActionResult> TrizDisableDrawAsync([FromBody] EnabledDraw body)
        {
            if (body == null) return NotFound("Body is required");
            if (String.IsNullOrWhiteSpace(body.DrawDate)) return NotFound($"Parameter {nameof(body.DrawDate)} is required");
            if (String.IsNullOrWhiteSpace(body.State)) return NotFound($"Parameter {nameof(body.State)} is required");
            if (String.IsNullOrWhiteSpace(body.Url)) return NotFound($"Parameter {nameof(body.Url)} is required");
            if (String.IsNullOrWhiteSpace(body.EmployeeName)) return NotFound($"Parameter {nameof(body.EmployeeName)} is required");

            var drawDate = DateTime.ParseExact(body.DrawDate, "M/d/yyyy HH:mm:ss", Integration.CultureInfoEnUS);
            var stringDate = $"{drawDate.Month}/{drawDate.Day}/{drawDate.Year} {drawDate.Hour}:{drawDate.Minute}:{drawDate.Second}";
            var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
                {{
                    domain = company.Sales.DomainFrom('{body.Url}');
                    lotteryGame = company.LotteryGamesPool.GetLotteryGame({IdOfLotteryGame.Triz});
                    lottery = lotteryGame.GetLottery();
                    lottery.DisableDraw({stringDate}, domain, Now, '{body.EmployeeName}');
                }}
            ");
            return result;
        }

        [HttpPost("api/lotto/draw/powerball/disabled")]
        [Authorize(Roles = "a9")]
        public async Task<IActionResult> DisableDrawPowerballAsync([FromBody]EnabledDrawPowerball body)
        {
            if (body == null) return NotFound("Body is required");
            if (String.IsNullOrWhiteSpace(body.DrawDate)) return NotFound($"Parameter {nameof(body.DrawDate)} is required");
            if (String.IsNullOrWhiteSpace(body.Url)) return NotFound($"Parameter {nameof(body.Url)} is required");
            if (String.IsNullOrWhiteSpace(body.EmployeeName)) return NotFound($"Parameter {nameof(body.EmployeeName)} is required");

            var drawDate = DateTime.ParseExact(body.DrawDate, "M/d/yyyy HH:mm:ss", Integration.CultureInfoEnUS);
            var stringDate = $"{drawDate.Month}/{drawDate.Day}/{drawDate.Year} {drawDate.Hour}:{drawDate.Minute}:{drawDate.Second}";
            var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
                {{
                    domain = company.Sales.DomainFrom('{body.Url}');
                    lottery = lotto900.GetPowerball();
                    lottery.DisableDraw({stringDate}, domain, Now, '{body.EmployeeName}');
                }}
            ");
            return result;
        }

        [HttpDelete("api/lotto/draw/triz")]
        [Authorize(Roles = "RemoveDraw")]
        public async Task<IActionResult> TrizRemoveDrawAsync(string state, int idSchedule, string employeeName)
        {
            return await RemoveDrawAsync(IdOfLotteryGame.Triz, int.MinValue, state, idSchedule, employeeName);
        }

        [HttpDelete("api/lotto/draw/pick/{pickNumber:int:min(2):max(5)}")]
        [Authorize(Roles = "RemoveDraw")]
        public async Task<IActionResult> PicksRemoveDrawAsync(int pickNumber, string state, int idSchedule, string employeeName)
        {
            return await RemoveDrawAsync(IdOfLotteryGame.Picks, pickNumber, state, idSchedule, employeeName);
        }

        private async Task<IActionResult> RemoveDrawAsync(IdOfLotteryGame idOfLotteryGame, int pickNumber, string state, int idSchedule, string employeeName)
        {
            if (String.IsNullOrWhiteSpace(state)) return NotFound($"Parameter {nameof(state)} is required");
            if (idSchedule <= 0) return NotFound($"Parameter {nameof(idSchedule)} is required");
            if (String.IsNullOrWhiteSpace(employeeName)) return NotFound($"Parameter {nameof(employeeName)} is required");

            StringBuilder cmdRemoveDraw = new StringBuilder();
            cmdRemoveDraw.AppendLine($"lotteryGame = company.LotteryGamesPool.GetLotteryGame({idOfLotteryGame});");
            if (idOfLotteryGame == IdOfLotteryGame.Picks)
            {
                if (pickNumber < 2 || pickNumber > 5) return BadRequest("Invalid pick number");
                cmdRemoveDraw.AppendLine($"lottery = lotteryGame.GetLottery({pickNumber}, state{state});");
            }
            else if (idOfLotteryGame == IdOfLotteryGame.Triz)
            {
                cmdRemoveDraw.AppendLine($"lottery = lotteryGame.GetLottery();");
            }
            else
            {
                return BadRequest("Invalid lottery game");
            }

            var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
                {{
                    {cmdRemoveDraw}
                    schedule = lottery.GetScheduleId({idSchedule});
                    lottery.Cancel(schedule, Now, '{employeeName}');
                }}
            ");
            return result;
        }

        [HttpGet("api/triz/draw/disabled")]
        [Authorize(Roles = "DisabledDraws")]
        public async Task<IActionResult> TrizDisabledDrawsAsync()
        {
            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
                {{
                    trizLotteryGame = company.LotteryGamesPool.TrizLotteryGame;

                    lotteryTriz = trizLotteryGame.GetLottery();
                    for (disabledDrawsTriz : lotteryTriz.DisabledDraws())
                    {{
                        print disabledDrawsTriz.Date date;
                        for ( disabledDomains : disabledDrawsTriz.DisabledDomains)
                        {{
                            print disabledDomains.Url url;
                        }}
                    }}
                }}
            ");
            return result;
        }

        [HttpGet("api/picks/draw/disabled")]
        [Authorize(Roles = "DisabledDraws")]
        public async Task<IActionResult> PicksDisabledDrawsAsync(string state)
        {
            if (String.IsNullOrWhiteSpace(state)) return BadRequest($"Parameter {nameof(state)} is required");

            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
                {{
                    picksLotteryGame = company.LotteryGamesPool.PicksLotteryGame;

                    if(picksLotteryGame.HasLottery(2, state{state}))
                    {{
                        lotteryPick2 = picksLotteryGame.GetLottery(2, state{state});
                        for (disabledDrawsPick2 : lotteryPick2.DisabledDraws())
                        {{
                            print disabledDrawsPick2.Date date;
                            for ( disabledDomains : disabledDrawsPick2.DisabledDomains)
                            {{
                                print disabledDomains.Url url;
                            }}
                        }}
                    }}
                    if(picksLotteryGame.HasLottery(3, state{state}))
                    {{
                        lotteryPick3 = picksLotteryGame.GetLottery(3, state{state});
                        for (disabledDrawsPick3 : lotteryPick3.DisabledDraws())
                        {{
                            print disabledDrawsPick3.Date date;
                            for ( disabledDomains : disabledDrawsPick3.DisabledDomains)
                            {{
                                print disabledDomains.Url url;
                            }}
                        }}
                    }}
                    if(picksLotteryGame.HasLottery(4, state{state}))
                    {{
                        lotteryPick4 = picksLotteryGame.GetLottery(4, state{state});
                        for ( disabledDrawsPick4 : lotteryPick4.DisabledDraws())
                        {{
                            print disabledDrawsPick4.Date date;
                            for ( disabledDomains : disabledDrawsPick4.DisabledDomains)
                            {{
                                print disabledDomains.Url url;
                            }}
                        }}
                    }}
                    if(picksLotteryGame.HasLottery(5, state{state}))
                    {{
                        lotteryPick5 = picksLotteryGame.GetLottery(5, state{state});
                        for ( disabledDrawsPick5 : lotteryPick5.DisabledDraws())
                        {{
                            print disabledDrawsPick5.Date date;
                            for ( disabledDomains : disabledDrawsPick5.DisabledDomains)
                            {{
                                print disabledDomains.Url url;
                            }}
                        }}
                    }}
                    for ( disabledDraws : picksLotteryGame.GetPowerball().DisabledDraws())
                    {{
                        print disabledDraws.Date date;
                        for ( disabledDomains : disabledDraws.DisabledDomains)
                        {{
                            print disabledDomains.Url url;
                        }}
                    }}
                }}
            ");
            return result;
        }

        [HttpPost("api/triz/draws/nextDates")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> TrizNextDrawDatesAsync([FromBody] DrawDatesByPick body, [FromHeader(Name = "domain-url")] string domain)
        {
            return await NextDrawDatesAsync(IdOfLotteryGame.Triz, body, domain);
        }

        [HttpPost("api/pick/draws/nextDates")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> PicksNextDrawDatesAsync([FromBody] DrawDatesByPick body, [FromHeader(Name = "domain-url")] string domain)
        {
            return await NextDrawDatesAsync(IdOfLotteryGame.Picks, body, domain);
        }

        private async Task<IActionResult> NextDrawDatesAsync(IdOfLotteryGame idOfLotteryGame, [FromBody] DrawDatesByPick body, [FromHeader(Name = "domain-url")] string domain)
        {
            if (body == null) return BadRequest("Body is required");
            if (body.DrawSchedules == null) return BadRequest($"{nameof(body.DrawSchedules)} is required");
            if (body.DrawSchedules.Length == 0) return BadRequest($"{nameof(body.DrawSchedules)} is required");
            if (string.IsNullOrWhiteSpace(body.Date)) return BadRequest($"{nameof(body.Date)} is required");
            if (string.IsNullOrWhiteSpace(HttpContext.Request.Headers["domain-url"])) return BadRequest("Header 'domain-url' is not sent in request");
            if (!DateTime.TryParseExact(body.Date, "M/d/yyyy", Integration.CultureInfoEnUS, DateTimeStyles.None, out DateTime date)) return BadRequest("Invalid date format");

            var dateFormatted = $"{date.Month}/{date.Day}/{date.Year}";
            var statesBuilder = new StringBuilder();
            var withFireBallBuilder = new StringBuilder();
            var hoursBuilder = new StringBuilder();
            var picksBuilder = new StringBuilder();
            var isPresentPlayerBeforeToCloseStoreBuilder = new StringBuilder();
            var useNextDateBuilder = new StringBuilder();
            foreach (var drawSchedule in body.DrawSchedules)
            {
                if (picksBuilder.Length != 0) picksBuilder.Append(',');
                picksBuilder.Append(drawSchedule.Pick);

                if (statesBuilder.Length != 0) statesBuilder.Append("','");
                statesBuilder.Append(drawSchedule.State);

                if (hoursBuilder.Length != 0) hoursBuilder.Append("','");
                hoursBuilder.Append(drawSchedule.Hour);

                if (idOfLotteryGame == IdOfLotteryGame.Triz && drawSchedule.WithFireBall) return BadRequest("Triz does not have fireball");
                if (withFireBallBuilder.Length != 0) withFireBallBuilder.Append("','");
                withFireBallBuilder.Append(drawSchedule.WithFireBall);

                if (isPresentPlayerBeforeToCloseStoreBuilder.Length != 0) isPresentPlayerBeforeToCloseStoreBuilder.Append("','");
                isPresentPlayerBeforeToCloseStoreBuilder.Append(false);

                if (useNextDateBuilder.Length != 0) useNextDateBuilder.Append("','");
                useNextDateBuilder.Append(false);
            }

            var result = await LottoAPI.Actor.PerformQryAsync(/*This is a query because only have prints*/HttpContext, $@"
            {{
                lotteryGame = company.LotteryGamesPool.GetLotteryGame({idOfLotteryGame});

                domain=company.Sales.DomainFrom('{domain}');
                anyScheduleHasChanged=lotteryGame.AnyScheduleHasChanged({{{picksBuilder}}}, {{'{statesBuilder}'}}, {{'{hoursBuilder}'}}, {{'{withFireBallBuilder}'}});
                print anyScheduleHasChanged anyScheduleHasChanged;
                if ( ! anyScheduleHasChanged)
                {{
                    nextDatesAccumulator = NextDatesAccumulator(lotteryGame, domain, Now, {{{picksBuilder}}}, '{dateFormatted}', {{'{statesBuilder}'}}, {{'{hoursBuilder}'}}, {{'{withFireBallBuilder}'}}, {{'{isPresentPlayerBeforeToCloseStoreBuilder}'}}, {{'{useNextDateBuilder}'}});
                    for (nextDates:nextDatesAccumulator.GetAll)
                    {{
                        print nextDates.State.Abbreviation state;
                        print nextDates.DesiredDate desiredDate;
                        print nextDates.Date nextDate;
                        print nextDates.NextDate nextDateOfNextDate;
                        print nextDates.Date.HHmmAMPM() hour;
                        print nextDates.Pick pick;
                        print nextDates.WithFireBall withFireBall;
                    }}
                }}
            }}
            ");
            return result;
        }

        [HttpPost("api/lotto/draw/pick/{pickNumber:int:min(2):max(5)}/nextdate")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> NextDrawDatesAsync(int pickNumber, [FromBody]DrawDates body, [FromHeader(Name = "domain-url")]string domain)
        {
            if (body == null) return NotFound("Body is required");
            if (body.DrawSchedules == null) return NotFound($"{nameof(body.DrawSchedules)} is required");
            if (body.DrawSchedules.Length == 0) return NotFound($"{nameof(body.DrawSchedules)} is required");
            if (body.Dates == null) return NotFound($"{nameof(body.Dates)} is required");
            if (body.Dates.Length == 0) return NotFound($"{nameof(body.Dates)} is required");
            if (body.Dates.Length > 90) return NotFound($"Lottery only allow to buy tickets of 3 months or earlier dates");
            if (String.IsNullOrWhiteSpace(HttpContext.Request.Headers["domain-url"])) return NotFound("Header 'domain-url' is not sent in request");

            var dates = string.Join(",", body.Dates);
            var states = string.Join(",", body.DrawSchedules.Select(x => x.State));
            var withFireBalls = string.Join(",", body.DrawSchedules.Select(x => x.WithFireBall));
            var hours = string.Join(",", body.DrawSchedules.Select(x => x.Hour));
            var isPresentPlayerBeforeToCloseStore = string.Join(",", body.DrawSchedules.Select(x => false));
            var result = await LottoAPI.Actor.PerformQryAsync(/*This is a query because only have prints*/HttpContext, $@"
            {{
                domain=company.Sales.DomainFrom('{domain}');
                anyScheduleHasChanged=lotto900.AnyScheduleHasChanged({pickNumber}, '{states}', '{hours}', '{withFireBalls}');
                print anyScheduleHasChanged anyScheduleHasChanged;
                nextDatesAccumulator = NextDatesAccumulator(lotto900, domain, Now, 'pick{pickNumber}', '{dates}', '{states}', '{hours}', '{withFireBalls}', '{isPresentPlayerBeforeToCloseStore}', '{NextDatesAccumulator.WILDCARD_FOR_ALL_USE_NEXT_DATE}');
                for (nextDates:nextDatesAccumulator.GetAll)
                {{
                    print nextDates.State.Abbreviation state;
                    print nextDates.DesiredDate desiredDate;
                    print nextDates.Date nextDate;
                    print nextDates.NextDate nextDateOfNextDate;
                    print nextDates.Date.HHmmAMPM() hour;
                    print nextDates.WithFireBall withFireBall;
                }}
            }}
            ");
            return result;
        }

        [HttpPost("api/lotto/schedule/pick/{pickNumber:int:min(2):max(5)}")]
        [Authorize(Roles = "SaveDraw")]
        public async Task<IActionResult> CreateScheduleAsync(int pickNumber, [FromBody]Schedule body, [FromHeader(Name = "domain-url")]string domain)
        {
            if (body == null) return NotFound("Body is required");
            if (String.IsNullOrWhiteSpace(body.State)) return NotFound($"Parameter {nameof(body.State)} is required");
            if (String.IsNullOrWhiteSpace(body.Description)) return NotFound($"Parameter {nameof(body.Description)} is required");
            if (String.IsNullOrWhiteSpace(body.EmployeeName)) return NotFound($"Parameter {nameof(body.EmployeeName)} is required");
            var description = Validator.StringEscape(body.Description);
            var drawHour = DateTime.ParseExact(body.DrawHour, "h:m tt", Integration.CultureInfoEnUS);
            var hourFormatted24H = drawHour.ToString("HH");

            var linesForEveryDate = new StringBuilder();
            foreach (int dayOfWeek in body.DaysOfWeek)
            {
                linesForEveryDate.Append(dayOfWeek);
            }
            if (string.IsNullOrWhiteSpace(linesForEveryDate.ToString())) return NotFound("Drawing must have at least a day of week marked.");

            IActionResult result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
                {{
                    lottery = lotto900.GetOrCreateLottery({pickNumber}, state{body.State});
                    lottery.ForEach({hourFormatted24H},{drawHour.Minute},'{linesForEveryDate.ToString()}', Now, '{body.EmployeeName}');
                    schedule = lottery.GetSchedule({hourFormatted24H},{drawHour.Minute});
                    schedule.UpdateDescription(itIsThePresent,'{description}');
                }}
            ");
            return result;
        }

        [HttpPut("api/lotto/schedule/triz")]
        [Authorize(Roles = "SaveDraw")]
        public async Task<IActionResult> TrizUpdateScheduleAsync([FromBody] Schedule body)
        {
            return await UpdateScheduleAsync(IdOfLotteryGame.Triz, int.MinValue, body);
        }

        [HttpPut("api/lotto/schedule/pick/{pickNumber:int:min(2):max(5)}")]
        [Authorize(Roles = "SaveDraw")]
        public async Task<IActionResult> PickUpdateScheduleAsync(int pickNumber, [FromBody] Schedule body)
        {
            return await UpdateScheduleAsync(IdOfLotteryGame.Picks, pickNumber, body);
        }

        private async Task<IActionResult> UpdateScheduleAsync(IdOfLotteryGame idOfLotteryGame, int pickNumber, [FromBody]Schedule body)
        {
            if (body == null) return NotFound("Body is required");
            if (body.Id < 0) return NotFound("Id is required");
            if (String.IsNullOrWhiteSpace(body.State)) return NotFound($"Parameter {nameof(body.State)} is required");
            if (String.IsNullOrWhiteSpace(body.Description)) return NotFound($"Parameter {nameof(body.Description)} is required");
            if (String.IsNullOrWhiteSpace(body.EmployeeName)) return NotFound($"Parameter {nameof(body.EmployeeName)} is required");
            var description = Validator.StringEscape(body.Description);
            var drawHour = DateTime.ParseExact(body.DrawHour, "h:m tt", Integration.CultureInfoEnUS);
            var hourFormatted24H = drawHour.ToString("HH");

            var linesForEveryDate = new StringBuilder();
            foreach (int dayOfWeek in body.DaysOfWeek)
            {
                linesForEveryDate.Append(dayOfWeek);
            }
            if (string.IsNullOrWhiteSpace(linesForEveryDate.ToString())) return NotFound("Drawing must have at least a day of week marked.");

            StringBuilder cmdUpdateSchedule = new StringBuilder();
            cmdUpdateSchedule.AppendLine($"lotteryGame = company.LotteryGamesPool.GetLotteryGame({idOfLotteryGame});");
            if (idOfLotteryGame == IdOfLotteryGame.Picks)
            {
                if (pickNumber < 2 || pickNumber > 5) return BadRequest("Invalid pick number");
                cmdUpdateSchedule.Append("lottery = lotteryGame.GetLottery(").Append(pickNumber).Append(", state").Append(body.State).Append(");");
                cmdUpdateSchedule.Append("schedule = lottery.GetScheduleId(").Append(body.Id).Append(");");
                cmdUpdateSchedule.Append("schedule.Update(itIsThePresent, '").Append(description).Append("', '").Append(linesForEveryDate.ToString()).Append("', ").Append(hourFormatted24H).Append(", ").Append(drawHour.Minute).Append(", Now, '").Append(body.EmployeeName).Append("');");
            }
            else if (idOfLotteryGame == IdOfLotteryGame.Triz)
            {
                cmdUpdateSchedule.Append("lottery = lotteryGame.GetLottery();");
                cmdUpdateSchedule.Append("lottery.UpdateSchedule(itIsThePresent, '").Append(description).Append("', '").Append(linesForEveryDate.ToString()).Append("', ").Append(hourFormatted24H).Append(", ").Append(drawHour.Minute).Append(", Now, '").Append(body.EmployeeName).Append("');");
            }
            else
            {
                return BadRequest("Invalid lottery game");
            }

            IActionResult result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
                {{
                    {cmdUpdateSchedule}
                }}
            ");
            return result;
        }

        [HttpPut("api/lotto/schedule/powerball")]
        [Authorize(Roles = "a3")]
        public async Task<IActionResult> UpdateScheduleForPowerBallAsync([FromBody]Schedule body)
        {
            if (body == null) return NotFound("Body is required");
            if (body.Id < 0) return NotFound("Id is required");
            if (body.DaysOfWeek == null || body.DaysOfWeek.Count() != 1) return NotFound("Drawing must have a day of week marked.");
            var description = Validator.StringEscape(body.Description);
            var drawHour = DateTime.ParseExact(body.DrawHour, "h:m tt", Integration.CultureInfoEnUS);
            var hourFormatted24H = drawHour.ToString("HH");

            string linesForEveryDate = "";
            foreach (int dayOfWeek in body.DaysOfWeek)
            {
                linesForEveryDate += dayOfWeek;
            }

            IActionResult result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
                {{
                    lottery = lotto900.GetPowerball();
                    schedule = lottery.GetScheduleId({body.Id});
                    schedule.Update(itIsThePresent, '{description}', '{linesForEveryDate.ToString()}', {hourFormatted24H}, {drawHour.Minute}, Now, '{body.EmployeeName}');
                }}
            ");
            return result;
        }


        [HttpGet("api/lotto/schedule/triz/pending/ticket")]
        [Authorize(Roles = "SaveDraw")]
        public async Task<IActionResult> TrizExistsPendingTicketsOnScheduleAsync(int idSchedule, string state)
        {
            return await ExistsPendingTicketsOnScheduleAsync(IdOfLotteryGame.Triz, int.MinValue, idSchedule, state);
        }

        [HttpGet("api/lotto/schedule/pick/{pickNumber:int:min(2):max(5)}/pending/ticket")]
        [Authorize(Roles = "SaveDraw")]
        public async Task<IActionResult> PickExistsPendingTicketsOnScheduleAsync(int pickNumber, int idSchedule, string state)
        {
            return await ExistsPendingTicketsOnScheduleAsync(IdOfLotteryGame.Picks, pickNumber, idSchedule, state);
        }

        private async Task<IActionResult> ExistsPendingTicketsOnScheduleAsync(IdOfLotteryGame idOfLotteryGame, int pickNumber, int idSchedule, string state)
        {
            if (idSchedule <= 0) return NotFound("Parameter idSchedule is required");

            StringBuilder cmdExists = new StringBuilder();
            cmdExists.AppendLine($"lotteryGame = company.LotteryGamesPool.GetLotteryGame({idOfLotteryGame});");
            if (idOfLotteryGame == IdOfLotteryGame.Picks)
            {
                if (String.IsNullOrWhiteSpace(state)) return NotFound($"Parameter {nameof(state)} is required");
                if (pickNumber < 2 || pickNumber > 5) return BadRequest("Invalid pick number");
                cmdExists.AppendLine($"lottery = lotteryGame.GetLottery({pickNumber}, state{state});");
            }
            else if (idOfLotteryGame == IdOfLotteryGame.Triz)
            {
                cmdExists.AppendLine($"lottery = lotteryGame.GetLottery();");
            }
            else
            {
                return BadRequest("Invalid lottery game");
            }

            IActionResult result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
                {{
                    {cmdExists}
                    schedule = lottery.GetScheduleId({idSchedule});
                    print lottery.HasPendingTickets(schedule) hasPendingTickets;
                }}
            ");
            return result;
        }

        [HttpGet("api/lotto/schedule/powerball/pending/ticket")]
        [Authorize(Roles = "a2")]
        public async Task<IActionResult> ExistsPendingTicketsOnScheduleForPowerBallAsync(int idSchedule)
        {
            if (idSchedule < 0) return NotFound("Parameter idSchedule is required");

            IActionResult result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
                {{
                    lottery = lotto900.GetPowerball();
                    schedule = lottery.GetScheduleId({idSchedule});
                    print lottery.HasPendingTickets(schedule) hasPendingTickets;
                }}
            ");
            return result;
        }

        [HttpGet("api/lotto/draw/result")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> DrawnLotteriesBetweenDatesAsync(string date, string dates)
        {
            if (String.IsNullOrWhiteSpace(date)) return NotFound($"Parameter {nameof(date)} is required");

            var linesToGetResults = new StringBuilder();
            switch (date)
            {
                case "this_week":
                    {
                        linesToGetResults.Append("startedDate = Now.InicioDeActualSemana();");
                        linesToGetResults.Append("endedDate = startedDate.EnUnaSemana();");
                        linesToGetResults.Append("drawnLotteriesBetweenDates=lotto900.DrawnLotteriesBetween(startedDate, endedDate);");
                        break;
                    }
                case "yesterday":
                    {
                        linesToGetResults.Append("startedDate=Now.Ayer();");
                        linesToGetResults.Append("drawnLotteriesBetweenDates=lotto900.DrawnLotteriesBetween(startedDate, Now);");
                        break;
                    }
                case "last_week":
                    {
                        linesToGetResults.Append("startedDate=Now.AnteriorSemana();");
                        linesToGetResults.Append("drawnLotteriesBetweenDates=lotto900.DrawnLotteriesBetween(startedDate, Now);");
                        break;
                    }
                case "last_month":
                    {
                        linesToGetResults.Append("startedDate=Now.AnteriorMes();");
                        linesToGetResults.Append("drawnLotteriesBetweenDates=lotto900.DrawnLotteriesBetween(startedDate, Now);");
                        break;
                    }
                case "specific_day":
                    {
                        if (String.IsNullOrWhiteSpace(dates)) return BadRequest($"Parameter {nameof(dates)} is required");
                        linesToGetResults.Append($"drawnLotteriesBetweenDates=lotto900.DrawnLotteriesBetween('{dates}');");
                        break;
                    }
                default:
                    {
                        return BadRequest($"There is no date for label {date}");
                    }
            }

            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
            {{
                {linesToGetResults}
                print drawnLotteriesBetweenDates.Count() total;
                for (drawResults:drawnLotteriesBetweenDates)
                {{
                    print drawResults.Date drawDate;
                    print drawResults.State.Abbreviation state;
                    print drawResults.GameType gameType;
                    print drawResults.Date.HHmmAMPM() drawHour;
                    print drawResults.SequenceOfNumbers numbers;
                }}
            }}
            ");
            return result;
        }

        [HttpGet("api/lotto/draw/winnerNumbers")]
        [AllowAnonymous]
        public async Task<IActionResult> WinnerNumbersForAsync(string state, DateTime date)
        {
            if (String.IsNullOrWhiteSpace(state)) return BadRequest($"Parameter {nameof(state)} is required");
            if (date == default(DateTime)) return BadRequest($"Parameter {nameof(date)} is required");
            var normalizedDate = $"{date.Month}/{date.Day}/{date.Year}";

            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
            {{
                drawnLotteriesAtNight = lotto900.SearchWinnerNumbers(state{state}, {normalizedDate}, Night);
                for (drawResultsAtNight:drawnLotteriesAtNight)
                {{
                    drawResult = drawResultsAtNight;
                    print 'Night' partOfDay;
                    print drawResult.Date drawDate;
                    print drawResult.State.Abbreviation state;
                    print drawResult.GameType gameType;
                    print drawResult.Date.HHmmAMPM() drawHour;
                    print drawResult.SequenceOfNumbers numbers;
                }}

                drawnLotteriesAtEvening = lotto900.SearchWinnerNumbers(state{state}, {normalizedDate}, Evening);
                for (drawResultsAtEvening:drawnLotteriesAtEvening)
                {{
                    drawResult = drawResultsAtEvening;
                    print 'Evening' partOfDay;
                    print drawResult.Date drawDate;
                    print drawResult.State.Abbreviation state;
                    print drawResult.GameType gameType;
                    print drawResult.Date.HHmmAMPM() drawHour;
                    print drawResult.SequenceOfNumbers numbers;
                }}

                drawnLotteriesAtMidday = lotto900.SearchWinnerNumbers(state{state}, {normalizedDate}, Midday);
                for (drawResultsAtMidday:drawnLotteriesAtMidday)
                {{
                    drawResult = drawResultsAtMidday;
                    print 'Midday' partOfDay;
                    print drawResult.Date drawDate;
                    print drawResult.State.Abbreviation state;
                    print drawResult.GameType gameType;
                    print drawResult.Date.HHmmAMPM() drawHour;
                    print drawResult.SequenceOfNumbers numbers;
                }}

                drawnLotteriesAtMorning = lotto900.SearchWinnerNumbers(state{state}, {normalizedDate}, Morning);
                for (drawResultsAtMorning:drawnLotteriesAtMorning)
                {{
                    drawResult = drawResultsAtMorning;
                    print 'Morning' partOfDay;
                    print drawResult.Date drawDate;
                    print drawResult.State.Abbreviation state;
                    print drawResult.GameType gameType;
                    print drawResult.Date.HHmmAMPM() drawHour;
                    print drawResult.SequenceOfNumbers numbers;
                }}
            }}
            ");
            return result;
        }

        [HttpGet("api/lotto/draw/pick/{pickNumber:int:min(2):max(5)}/matchingWinnerNumbers")]
        [AllowAnonymous]
        public async Task<IActionResult> CheckWinnerNumbersForAsync(int pickNumber, string numbers)
        {
            if (String.IsNullOrWhiteSpace(numbers)) return BadRequest($"Parameter {nameof(numbers)} is required");

            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
            {{
                startedDate = Now.AnteriorMes();
                drawsMatchingDesiredNumber = lotto900.CheckDrawsMatchingWithDesiredNumber({pickNumber}, startedDate, Now, '{numbers}');
                for (draws:drawsMatchingDesiredNumber.ListWinnerNumbers())
                {{
                    winnerNumber = draws;
                    print winnerNumber.DrawDate drawDate;
                    print winnerNumber.StateAbbWhereBelongs state;
                    print winnerNumber.DrawDate.HHmmAMPM() drawHour;
                    print winnerNumber.SequenceOfNumbers numbers;
                    print winnerNumber.ScheduleDescriptionWhereBelongs description;
                    print drawsMatchingDesiredNumber.DesiredNumberMatchesInAnyOrderThis(winnerNumber) isBoxed;
                    print drawsMatchingDesiredNumber.DesiredNumberMatchesInTheSameOrderThis(winnerNumber) isStraight;
                }}
            }}
            ");
            return result;
        }

        [HttpGet("api/lotto/draw/pending")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> PendingDrawsAsync()
        {
            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
            {{
                lottoPendingDraws = lotto900.PendingDraws();
                print lottoPendingDraws.Count() count;
                for (pendingDraws:lottoPendingDraws)
                {{
                    print pendingDraws.DrawDate drawDate;
                    print pendingDraws.State.Abbreviation state;
                    print pendingDraws.GameType gameType;
                    print pendingDraws.DrawDate.HHmmAMPM() drawHour;
                }}
            }}
            ");
            return result;
        }

        [HttpGet("api/lotto/draw/completed")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> CompletedDrawsAsync()
        {
            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
            {{
                lottoCompletedDraws = lotto900.CompletedDraws();
                print lottoCompletedDraws.Count() count;
                for (completedDraws:lottoCompletedDraws)
                {{
                    print completedDraws.Date drawDate;
                    print completedDraws.State.Abbreviation state;
                    print completedDraws.GameType gameType;
                    print completedDraws.Date.HHmmAMPM() drawHour;
                }}
            }}
            ");
            return result;
        }

        [HttpGet("api/lotto/draw/triz/existence")]
        [Authorize(Roles = "resend")]
        public async Task<IActionResult> TrizExistsDrawAsync(string state, string drawDate)
        {
            return await ExistsDrawAsync(IdOfLotteryGame.Triz, int.MinValue, state, drawDate);
        }

        [HttpGet("api/lotto/draw/pick/{pickNumber:int:min(2):max(5)}/existence")]
        [Authorize(Roles = "resend")]
        public async Task<IActionResult> PickExistsDrawAsync(int pickNumber, string state, string drawDate)
        {
            return await ExistsDrawAsync(IdOfLotteryGame.Picks, pickNumber, state, drawDate);
        }

        private async Task<IActionResult> ExistsDrawAsync(IdOfLotteryGame idOfLotteryGame, int pickNumber, string state, string drawDate)
        {
            if (string.IsNullOrWhiteSpace(state)) return BadRequest($"{nameof(state)} is required");
            if (string.IsNullOrWhiteSpace(drawDate)) return BadRequest($"{nameof(drawDate)} is required");

            var exactDrawDate = DateTime.ParseExact(drawDate, "M/d/yyyy HH:mm:ss", cultureInfo);
            var stringDate = $"{exactDrawDate.Month}/{exactDrawDate.Day}/{exactDrawDate.Year} {exactDrawDate.Hour}:{exactDrawDate.Minute}:{exactDrawDate.Second}";

            StringBuilder queryExistsDraw = new StringBuilder();
            queryExistsDraw.AppendLine($"lotteryGame = company.LotteryGamesPool.GetLotteryGame({idOfLotteryGame});");
            if (idOfLotteryGame == IdOfLotteryGame.Picks)
            {
                if (pickNumber < 2 || pickNumber > 5) return BadRequest("Invalid pick number");
                queryExistsDraw.AppendLine($"lottery = lotteryGame.GetLottery({pickNumber}, state{state});");
            }
            else if (idOfLotteryGame == IdOfLotteryGame.Triz)
            {
                queryExistsDraw.AppendLine($"lottery = lotteryGame.GetLottery();");
            }
            else
            {
                return BadRequest("Invalid lottery game");
            }

            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
            {{
                {queryExistsDraw}
                print lottery.ExistsPostedDrawInMemory({stringDate}) existsDraw;
            }}
            ");
            return result;
        }

        CultureInfo cultureInfo = Integration.CultureInfoEnUS;

        [HttpPost("api/lotto/draw/triz/forwarding")]
        [Authorize(Roles = "resend")]
        public async Task<IActionResult> TrizResendDrawToAccountingAsync([FromBody] ForwardingDrawBody body)
        {
            return await ResendDrawToAccountingAsync(IdOfLotteryGame.Triz, int.MinValue, body);
        }

        [HttpPost("api/lotto/draw/pick/{pickNumber:int:min(2):max(5)}/forwarding")]
        [Authorize(Roles = "resend")]
        public async Task<IActionResult> PickResendDrawToAccountingAsync(int pickNumber, [FromBody] ForwardingDrawBody body)
        {
            return await ResendDrawToAccountingAsync(IdOfLotteryGame.Picks, pickNumber, body);
        }

        private async Task<IActionResult> ResendDrawToAccountingAsync(IdOfLotteryGame idOfLotteryGame, int pickNumber, [FromBody]ForwardingDrawBody body)
        {
            if (body == null) return BadRequest("Body is required");
            if (string.IsNullOrWhiteSpace(body.State)) return BadRequest($"{nameof(body.State)} is required");

            var drawDate = DateTime.ParseExact(body.DrawDate, "M/d/yyyy HH:mm:ss", cultureInfo);
            var stringDate = $"{drawDate.Month}/{drawDate.Day}/{drawDate.Year} {drawDate.Hour}:{drawDate.Minute}:{drawDate.Second}";
            Agents agent = Security.Agent(User);
            IActionResult result;

            StringBuilder actorQuery = new StringBuilder();
            StringBuilder actorCommand = new StringBuilder();

            actorQuery.AppendLine($"lotteryGame = company.LotteryGamesPool.GetLotteryGame({idOfLotteryGame});");
            actorCommand.AppendLine($"lotteryGame = company.LotteryGamesPool.GetLotteryGame({idOfLotteryGame});");
            if (idOfLotteryGame == IdOfLotteryGame.Picks)
            {
                if (pickNumber < 2 || pickNumber > 5) return BadRequest("Invalid pick number");
                actorQuery.AppendLine($"lottery = lotteryGame.GetLottery({pickNumber}, state{body.State});");
                actorCommand.AppendLine($"lottery = lotteryGame.GetLottery({pickNumber}, state{body.State});");
            }
            else if (idOfLotteryGame == IdOfLotteryGame.Triz)
            {
                actorQuery.AppendLine($"lottery = lotteryGame.GetLottery();");
                actorCommand.AppendLine($"lottery = lotteryGame.GetLottery();");
            }
            else
            {
                return BadRequest("Invalid lottery game");
            }

            if (agent == Agents.ARTEMIS)
            {
                string employeeName = Security.UserName(HttpContext);
                result = await LottoAPI.Actor.PerformChkThenCmdAsync(HttpContext, $@"
                    {{
                        {actorQuery}
                        Check(lottery.ExistsPostedDrawInMemory({stringDate})) Error 'Draw does not exist in memory';
                        Check(lottery.WasAlreadyConfirmed({stringDate})) Error 'Draw is not confirmed yet';
                    }}", $@"
                    {{
                        {actorCommand}
                        gradeFreeFormWagers = lottery.ResendToExternalAccounting({stringDate}, Now, itIsThePresent, '{employeeName}');
                        for (wagers:gradeFreeFormWagers)
                        {{
                            wager = wagers;
                            print wager.AdjustedLossAmount adjustedLossAmount;
                            print wager.AdjustedWinAmount adjustedWinAmount;
                            print wager.DailyFigureDate_YYYYMMDD dailyFigureDate_YYYYMMDD;
                            print wager.IsValidTicketNumber isValidTicketNumber;
                            print wager.Outcome outcome;
                            print wager.TicketNumber ticketNumber;
                            print wager.WagerNumber wagerNumber;
                            print wager.AgentId agentId;
                        }}
                    }}"
                );
                return result;
            }
            else
            {
                /*This is a query because it is not neccesary in dairy. It is only for resync wagers*/
                IActionResult resultForGradeFreeFormWagers = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
                    {{
                        {actorCommand}
                        gradeFreeFormWagers = lottery.CreateMessagesToResendDraw({stringDate}, Now);
                        for (wagers:gradeFreeFormWagers)
                        {{
                            wager = wagers;
                            print wager.AdjustedLossAmount adjustedLossAmount;
                            print wager.AdjustedWinAmount adjustedWinAmount;
                            print wager.DailyFigureDate_YYYYMMDD dailyFigureDate_YYYYMMDD;
                            print wager.IsValidTicketNumber isValidTicketNumber;
                            print wager.Outcome outcome;
                            print wager.TicketNumber ticketNumber;
                            print wager.WagerNumber wagerNumber;
                        }}
                    }}
                ");

                if (!(resultForGradeFreeFormWagers is OkObjectResult)) throw new Exception(((ObjectResult)resultForGradeFreeFormWagers).ToString());
                string value = ((OkObjectResult)resultForGradeFreeFormWagers).Value.ToString();
                var gradeFreeFormWagers = JsonConvert.DeserializeObject<GradeFreeFormWagers>(value);

                if (gradeFreeFormWagers != null && gradeFreeFormWagers.Wagers.Count() != 0)//Arregla el Id 463395 de la BD pues fue una compra sin wagers
                {
                    var paymentProcessor = WholePaymentProcessor.Instance().SearchGradeProcessorBy();
                    GradeFreeFormWagersResponse response;
                    using (RecordSet recordSet = paymentProcessor.GetRecordSet())
                    {
                        recordSet.SetParameter("wagers", gradeFreeFormWagers.Wagers.ToList());

                        response = paymentProcessor.Execute<GradeFreeFormWagersResponse>(DateTime.Now, recordSet);
                    }

                    var scriptWagersWithProblems = new StringBuilder();
                    foreach (var wager in response.Wagers)
                    {
                        if (!wager.IsValidTicketNumber)
                        {
                            scriptWagersWithProblems.Append($"lotteryGame.AddWagerGradedWithProblemAboutIsValidTicket({wager.TicketNumber}, {wager.WagerNumber});");
                        }
                    }

                    if (scriptWagersWithProblems.Length != 0)
                    {
                        result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
                        {{
                            {scriptWagersWithProblems.ToString()}
                        }}"
                        );
                        if (!(result is OkObjectResult)) throw new Exception(((ObjectResult)result).ToString());
                    }
                }
                return resultForGradeFreeFormWagers;
            }
        }

        [HttpPost("api/lotto/disincentives/pick/{pickNumber:int:min(2):max(5)}")]
        [Authorize(Roles = "n2")]
        public async Task<IActionResult> AddDisincentiveAsync(string riskProfile, int pickNumber, [FromBody] DisincentiveBody body)
        {
            if (body == null) return BadRequest("Body is required");
            if (string.IsNullOrWhiteSpace(riskProfile)) return BadRequest($"{nameof(riskProfile)} is required");
            if (string.IsNullOrWhiteSpace(body.State)) return BadRequest($"{nameof(body.State)} is required");
            if (string.IsNullOrWhiteSpace(body.PrizeCriteriaName)) return BadRequest($"{nameof(body.PrizeCriteriaName)} is required");
            if (body.UniqueDrawingId <= 0) return BadRequest($"{nameof(body.UniqueDrawingId)} is not valid");
            if (!body.RuleType.HasValue) return BadRequest($"{nameof(body.RuleType)} is required");
            if (body.DisincentivePercentage < 0 || body.DisincentivePercentage > 100) return BadRequest($"{nameof(body.DisincentivePercentage)} is not valid");

            var prizeCriteriaId = PrizeCriteriaIdBasedOn(body.PrizeCriteriaName);
            var result = await LottoAPI.Actor.PerformChkThenCmdAsync(HttpContext, $@"
				{{
                    existsSchedule = company.LotteryGamesPool.ExistsInternalSchedule({body.UniqueDrawingId});
                    if (existsSchedule)
                    {{
                        schedule = company.LotteryGamesPool.GetInternalSchedule({body.UniqueDrawingId});
                        existDisincentive = company.LotteryGamesPool.RiskProfiles.GetRiskProfile('{riskProfile}').Risks.Disincentives.HasDisincentive(schedule, {prizeCriteriaId}, {body.RuleType.Value});
					    Check(!existDisincentive) Error 'Disincentive already exists';
                    }}
                    Check(existsSchedule) Error 'Schedule does not exist';
				}}
				", $@"
                {{
                    schedule = company.LotteryGamesPool.GetInternalSchedule({body.UniqueDrawingId});
					company.LotteryGamesPool.RiskProfiles.GetRiskProfile('{riskProfile}').Risks.Disincentives.NewDisincentive(schedule, {prizeCriteriaId}, {body.RuleType.Value}, {body.DisincentivePercentage});
				}}
            ");

            return result;
        }

        [HttpGet("api/lotto/disincentives/pick/{pickNumber:int:min(2):max(5)}")]
        [Authorize(Roles = "n1")]
        public async Task<IActionResult> GetDisincentivesAsync(string riskProfile, int pickNumber)
        {
            if (string.IsNullOrWhiteSpace(riskProfile)) return BadRequest($"{nameof(riskProfile)} is required");

            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
                {{
					allDisincentives = company.LotteryGamesPool.RiskProfiles.GetRiskProfile('{riskProfile}').Risks.Disincentives.AllDisincentives({pickNumber});
                    for(disincentives : allDisincentives)
                    {{
                        disincentive = disincentives;
                        print disincentive.PickNumber pick;
                        print disincentive.State.Abbreviation state;
                        print disincentive.Schedule.UniqueId uniqueDrawingId;
                        print disincentive.PrizeCriteriaName() prizeCriteriaName;
                        print disincentive.RuleTypeAsText ruleType;
                        print disincentive.DisincentivePercentage disincentive;
                        print disincentive.Schedule.GetDescription() description;
                    }}

				}}
            ");

            return result;
        }

        [HttpDelete("api/lotto/disincentives/pick/{pickNumber:int:min(2):max(5)}")]
        [Authorize(Roles = "n4")]
        public async Task<IActionResult> DeleteDisincentiveAsync(string riskProfile, int pickNumber, string state, int uniqueDrawingId, string prizeCriteriaName, RuleType? ruleType)
        {
            if (string.IsNullOrWhiteSpace(riskProfile)) return BadRequest($"{nameof(riskProfile)} is required");
            if (string.IsNullOrWhiteSpace(prizeCriteriaName)) return BadRequest($"{nameof(prizeCriteriaName)} is required");
            if (uniqueDrawingId <= 0) return BadRequest($"{nameof(uniqueDrawingId)} is not valid");
            if (!ruleType.HasValue) return BadRequest($"{nameof(ruleType)} is required");

            var prizeCriteriaId = PrizeCriteriaIdBasedOn(prizeCriteriaName);
            var result = await LottoAPI.Actor.PerformChkThenCmdAsync(HttpContext, $@"
                {{
                    existsSchedule = company.LotteryGamesPool.ExistsInternalSchedule({uniqueDrawingId});
                    if (existsSchedule)
                    {{
                        schedule = company.LotteryGamesPool.GetInternalSchedule({uniqueDrawingId});
                        existDisincentive = company.LotteryGamesPool.RiskProfiles.GetRiskProfile('{riskProfile}').Risks.Disincentives.HasDisincentive(schedule, {prizeCriteriaId}, {ruleType.Value});
					    Check(existDisincentive) Error 'Disincentive with pick number {pickNumber}, drawing id {uniqueDrawingId}, prize criteria name {prizeCriteriaName} and {ruleType.Value} does not exist';
                    }}
                    Check(existsSchedule) Error 'Schedule does not exist';
				}}
				", $@"
                {{
                    schedule = company.LotteryGamesPool.GetInternalSchedule({uniqueDrawingId});
					company.LotteryGamesPool.RiskProfiles.GetRiskProfile('{riskProfile}').Risks.Disincentives.DeleteDisincentive(schedule, {prizeCriteriaId}, {ruleType.Value});
				}}
            ");

            return result;
        }

        [HttpPut("api/lotto/disincentives/pick/{pickNumber:int:min(2):max(5)}")]
        [Authorize(Roles = "n3")]
        public async Task<IActionResult> UpdateDisincentiveAsync(string riskProfile, int pickNumber, [FromBody] DisincentiveBody body)
        {
            if (body == null) return BadRequest("Body is required");
            if (string.IsNullOrWhiteSpace(riskProfile)) return BadRequest($"{nameof(riskProfile)} is required");
            if (string.IsNullOrWhiteSpace(body.PrizeCriteriaName)) return BadRequest($"{nameof(body.PrizeCriteriaName)} is required");
            if (body.UniqueDrawingId <= 0) return BadRequest($"{nameof(body.UniqueDrawingId)} is not valid");
            if (!body.RuleType.HasValue) return BadRequest($"{nameof(body.RuleType)} is required");
            if (body.DisincentivePercentage < 0 || body.DisincentivePercentage > 100) return BadRequest($"{nameof(body.DisincentivePercentage)} is not valid");

            var prizeCriteriaId = PrizeCriteriaIdBasedOn(body.PrizeCriteriaName);
            var result = await LottoAPI.Actor.PerformChkThenCmdAsync(HttpContext, $@"
                {{
                    existsSchedule = company.LotteryGamesPool.ExistsInternalSchedule({body.UniqueDrawingId});
                    if (existsSchedule)
                    {{
                        schedule = company.LotteryGamesPool.GetInternalSchedule({body.UniqueDrawingId});
                        existDisincentive = company.LotteryGamesPool.RiskProfiles.GetRiskProfile('{riskProfile}').Risks.Disincentives.HasDisincentive(schedule, {prizeCriteriaId}, {body.RuleType.Value});
					    Check(existDisincentive) Error 'Disincentive with pick number {pickNumber}, drawing id {body.UniqueDrawingId}, prize criteria name {body.PrizeCriteriaName} and {body.RuleType.Value} does not exist';
                    }}
                    Check(existsSchedule) Error 'Schedule does not exist';
				}}
				", $@"
                {{
                    schedule = company.LotteryGamesPool.GetInternalSchedule({body.UniqueDrawingId});
					company.LotteryGamesPool.RiskProfiles.GetRiskProfile('{riskProfile}').Risks.Disincentives.UpdateDisincentive(schedule, {prizeCriteriaId}, {body.RuleType.Value}, {body.DisincentivePercentage});
				}}
            ");

            return result;
        }

        private PrizeCriteriaIds PrizeCriteriaIdBasedOn(string prettyCriteriaName)
        {
            switch (prettyCriteriaName)
            {
                case "Straight":
                    return PrizeCriteriaIds.PRIZE_CRITERIA_ALL_NUMBERS_ARE_THE_SAME;
                case "2-way":
                    return PrizeCriteriaIds.PRIZE_CRITERIA_2_WAY;
                case "3-way":
                    return PrizeCriteriaIds.PRIZE_CRITERIA_3_WAY;
                case "6-way":
                    return PrizeCriteriaIds.PRIZE_CRITERIA_6_WAY;
                case "4-way":
                    return PrizeCriteriaIds.PRIZE_CRITERIA_4_WAY;
                case "12-way":
                    return PrizeCriteriaIds.PRIZE_CRITERIA_12_WAY;
                case "24-way":
                    return PrizeCriteriaIds.PRIZE_CRITERIA_24_WAY;
                case "5-way":
                    return PrizeCriteriaIds.PRIZE_CRITERIA_5_WAY;
                case "10-way":
                    return PrizeCriteriaIds.PRIZE_CRITERIA_10_WAY;
                case "20-way":
                    return PrizeCriteriaIds.PRIZE_CRITERIA_20_WAY;
                case "30-way":
                    return PrizeCriteriaIds.PRIZE_CRITERIA_30_WAY;
                case "60-way":
                    return PrizeCriteriaIds.PRIZE_CRITERIA_60_WAY;
                case "120-way":
                    return PrizeCriteriaIds.PRIZE_CRITERIA_120_WAY;
                default:
                    throw new GameEngineException($"There is no implementation for {nameof(prettyCriteriaName)} {prettyCriteriaName}.");
            }
        }

        public void CreateConsumerForTopics()
        {
            int localizationId = (int)Integration.Localization;
            new TransactionConsumer(Integration.Kafka.Group, Integration.Kafka.TopicForTransacctions).StartListening();
			new GradeConsumer(Integration.Kafka.Group, Integration.Kafka.TopicForAllGrades).StartListening();
			new GradeConsumer(Integration.Kafka.Group, Integration.Kafka.TopicForGradesWinners).StartListening();
			new ProfanityConsumer($"{KafkaMessage.TRANSACTION_LOTTO_PREFIX}{Integration.Kafka.Group}", Integration.Kafka.TopicForProfanity).StartListening();
            new FragmentPaymentsCallbackConsumer(Integration.Kafka.Group, Integration.Kafka.TopicForFragmentPaymentsCallback(Store.STORES_SEQUENCE_LOTTO)).StartListening(localizationId);
            new SmartContractConsumer(Integration.Kafka.Group, Integration.Kafka.TopicForKenoGrades).StartListening();
            new TicketsResenderCallbackConsumer(Integration.Kafka.Group, $"{Integration.Kafka.TopicForLottoGrading}{KafkaMessage.RESENDING_LOTTO_TICKETS_SUFFIX}").StartListening();
            new StoreRegistrationCallbackConsumer(Integration.Kafka.Group, $"{Integration.Kafka.TopicForStoreRegistration}{KafkaMessage.CATALOG_CALLBACK_CONSUMER_SUFFIX}").StartListening();
            new CatalogConsumer(ValidateMessageOwnership, GetActor, $"{KafkaMessage.LOTTO_CONSUMER_PREFIX}{Integration.Kafka.Group}", Integration.Kafka.TopicForCatalog).StartListening();
            new CustomSettingsConsumer($"{KafkaMessage.LOTTO_CONSUMER_PREFIX}{Integration.Kafka.Group}", Integration.Kafka.TopicForCustomSettings).StartListening();
        }

        bool ValidateMessageOwnership(string storeAlias)
        {
            var result = LottoAPI.Actor.PerformQry(@"
                    {{
					    print company.Sales.CurrentStore.Alias storeAlias;
                    }}"
                );
            if (!(result is OkObjectResult))
            {
                throw new Exception($@"Error:{((ObjectResult)result).Value}");
            }
            var o = (OkObjectResult)result;
            var json = o.Value.ToString();
            var storeData = JsonConvert.DeserializeObject<StoreData>(json);
            return storeData.storeAlias == storeAlias;
        }
        RestAPIActorAsync GetActor()
        {
            return LottoAPI.Actor;
        }

        public void CreateConsumerForTopics(string tenantName, int storeId)
        {
            int localizationId = (int)Integration.Localization;
            new FragmentCreationCallbackConsumer(Integration.Kafka.Group, Integration.Kafka.TopicForFragmentsCreationCallback(storeId)).StartListening(localizationId);
            new LotteryDrawConsumer($"{Integration.Kafka.Group}_Tenant{tenantName}Store{storeId}", $"{KafkaMessage.LOTTERY_DRAW_CONSUMER_PREFIX}{Integration.Kafka.TopicForAllGrades}").StartListening();
        }

        private class ProfanityConsumer : GamesEngine.Settings.ProfanityConsumerValidator
        {

            private readonly HttpRestClientConfiguration client;

            public ProfanityConsumer(string group, string topic) : base(group, topic)
            {
                client = HttpRestClientConfiguration.GetInstance();
            }

            public override void OnMessageBeforeCommit(string message)
            {
                if (String.IsNullOrEmpty(message)) throw new Exception(nameof(message));
                if (!Settings.IsWebProfanityConfigured) { return; }

                ProfanityMessageType messageType = new ProfanityMessage(message).MessageType;

                if (messageType == ProfanityMessageType.FAVORITE_NUMBER_NAME)
                {
                    var profanityMessage = new FavoriteNumberMessage(message);
                    string profanityResultText = profanityMessage.Value;

                    try
                    {
                        string profanityUrl = string.Format(Settings.WebProfanityUrl, Settings.WebProfanityKey, Uri.EscapeDataString(profanityMessage.Value));
                        IActionResult result = client.Get(profanityUrl);
                        profanityResultText = ValidateResponseFromWebProfanity(message, profanityUrl, (result as ObjectResult), profanityMessage.Value);
                    }
                    catch
                    {
                        //TODO: Erick, Message Profanity failed
                    }

                    if (profanityMessage.Value == profanityResultText) { return; }
                    profanityResultText = Validator.StringEscape(profanityResultText);
                    LottoAPI.Actor.PerformCmd($@"
                        {{
                            player = company.Players.SearchPlayer('{profanityMessage.PlayerId}');
                            playerFavorites = player.Favorites();
                            favorite = playerFavorites.GetFavorite({profanityMessage.FavoriteNumberConsecutive});
                                favorite.Name ='{profanityResultText}';
                        }}"
                    );
                }
				else
				{
                    throw new Exception("Unhandled profanity message type");
				}
            }

        }

        private class FragmentCreationCallbackConsumer : Consumer
        {

            public FragmentCreationCallbackConsumer(string group, string topic) : base(group, topic)
            {

            }

            public override void OnMessageBeforeCommit(string message)
            {
                if (String.IsNullOrEmpty(message)) throw new Exception(nameof(message));

                FragmentCreationResponse fragmentLockResponse = new FragmentCreationResponse(message);
                IActionResult result;

                if (LottoAPI.Actor.IsKenoActor())
                {
                    result = LottoAPI.Actor.PerformCmd($@"
                    {{
                        lotteries.CreateWagers(ItIsThePresent,{fragmentLockResponse.TheLowestBetId}, {fragmentLockResponse.TheHighestBetId}, {fragmentLockResponse.TheLowestWagerNumber}, {fragmentLockResponse.TheHighestWagerNumber}, {fragmentLockResponse.AuthorizationNumber}, {fragmentLockResponse.OrderNumber}, Now);
                    }}"
                );
                }
                else if(LottoAPI.Actor.IsLottoActor())
                {
                    result = LottoAPI.Actor.PerformCmd($@"
                    {{
                        lotto900.CreateWagers(ItIsThePresent,{fragmentLockResponse.TheLowestBetId}, {fragmentLockResponse.TheHighestBetId}, {fragmentLockResponse.TheLowestWagerNumber}, {fragmentLockResponse.TheHighestWagerNumber}, {fragmentLockResponse.AuthorizationNumber}, {fragmentLockResponse.OrderNumber}, Now);
                    }}"
                );
                }
                else
                    throw new Exception("Actor type is not implemented");

                if (!(result is OkObjectResult)) throw new Exception(((ObjectResult)result).ToString());
            }
        }

        private class SmartContractConsumer : GamesEngine.Settings.Consumer
        {
            public SmartContractConsumer(string group, string topic) : base(group, topic)
            {
            }

            public override void OnMessageBeforeCommit(string message)
            {
                if (String.IsNullOrEmpty(message)) throw new Exception(nameof(message));

                KenoWinnerNumberMessage transaction = new KenoWinnerNumberMessage(message);

                if (! string.IsNullOrEmpty(transaction.Error))
                {
                    ErrorsSender.Send(transaction.Error, "Solana Contract Error");
                }
                else
                {
                    var multiplier = transaction.Multipler == MultiplierDraw.NO_MULTIPLIER ? 1 : int.Parse(transaction.Multipler);
                    var winners = string.Join(",", transaction.Winner);
                    var result = LottoAPI.Actor.PerformQry($@"
                        {{
                            isAlreadyGradedWithTheSameNumbers = keno.IsAlreadyGradedWithTheSameNumbers('{transaction.DrawPrefixId}',{{{winners}}},{multiplier},{transaction.Bulleye}, Now);
                            isAlreadyGraded = keno.IsAlreadyGraded('{transaction.DrawPrefixId}',{{{winners}}},{multiplier},{transaction.Bulleye}, Now);
                            print isAlreadyGradedWithTheSameNumbers isAlreadyGradedWithTheSameNumbers;
                            print isAlreadyGraded isAlreadyGraded;
                        }}
                    ");
                    if (!(result is OkObjectResult)) throw new Exception(((ObjectResult)result).ToString());

                    OkObjectResult o = (OkObjectResult)result;
                    string json = o.Value.ToString();
                    DrawResponse existCustomer = JsonConvert.DeserializeObject<DrawResponse>(json);

                    if (existCustomer.IsAlreadyGraded && !existCustomer.IsAlreadyGradedWithTheSameNumbers)
                        throw new GameEngineException($"{transaction.DrawPrefixId} is alreadyGraded with differente numbers. {nameof(SmartContractConsumer)} fails.");

                    result = LottoAPI.Actor.PerformCmd($@"
                        {{
                            report = keno.DrawKeno('{transaction.DrawPrefixId}','{string.Join(",", transaction.Winner)}',{multiplier},{transaction.Bulleye}, Now, ItIsThePresent, '{"Solana Network"}');
                            report = keno.ConfirmDraw('{transaction.DrawPrefixId}', Now, 'Solana', itIsThePresent);
                        }}
                    ");
                    if (!(result is OkObjectResult)) throw new Exception(((ObjectResult)result).ToString());
            }

        }
        }

        [DataContract(Name = "stateCodeBody")]
        public class StateCodeBody
        {
            [DataMember(Name = "stateCode")]
            public string StateCode { get; set; }
        }

        [DataContract(Name = "Draw")]
        public class DrawResponse
        {
            [DataMember(Name = "isAlreadyGradedWithTheSameNumbers")]
            public bool IsAlreadyGradedWithTheSameNumbers { get; set; }
            [DataMember(Name = "isAlreadyGraded")]
            public bool IsAlreadyGraded { get; set; }

        }

        [Obsolete("No se elimina porque pueden quedar mensajes en la cola y hay que procesarlos antes de eliminar estas funciones.")]
        private class TransactionConsumer : GamesEngine.Settings.Consumer
        {
            [Obsolete("No se elimina porque pueden quedar mensajes en la cola y hay que procesarlos antes de eliminar estas funciones.")]
            public TransactionConsumer(string group, string topic) : base(group, topic)
            {
            }

            public override void OnMessageBeforeCommit(string message)
            {
                if (String.IsNullOrEmpty(message)) throw new Exception(nameof(message));

                IActionResult result = null;
                WagersOrderTransaction transaction = new WagersOrderTransaction(message);

                result = LottoAPI.Actor.PerformQry($@"
                    {{
                        wagers = lotto900.TakeWagersBetweenBetIds({transaction.TheLowestBetId},{transaction.TheHighestBetId});
                        for(postFreeFormWagers:wagers)
                        {{
                            print postFreeFormWagers.BetDescription betDescription;
                            print postFreeFormWagers.Risk risk;
                            print postFreeFormWagers.ToWin toWin;
                            print postFreeFormWagers.ReferenceNumber referenceNumber;
                            print postFreeFormWagers.WagerNumber wagerNumber;
                        }}
                    }}
                    ");

                if (!(result is OkObjectResult)) throw new Exception(((ObjectResult)result).ToString());
                var postFreeFormWagers = new PostFreeFormWagersBody();
                var bytes = Encoding.Unicode.GetBytes(((OkObjectResult)result).Value.ToString());
                using (MemoryStream mst = new MemoryStream(bytes))
                {
                    var serializer = new DataContractJsonSerializer(typeof(PostFreeFormWagersBody));
                    postFreeFormWagers = (PostFreeFormWagersBody)serializer.ReadObject(mst);
                }

                int theLowestWagerNumber = 0;
                int theHighestWagerNumber = 0;
                if (Build.IsInDevelopmentMode)
                {
                    var wagerNumbers = new List<int>();
                    var referenceNumbers = postFreeFormWagers.PostFreeFormWagers.Select(x => x.ReferenceNumber);
                    foreach (var referenceNumber in referenceNumbers)
                    {
                        var betNumberAndWagerPosition = referenceNumber.Split("-");
                        var wagerConsecutive = int.Parse(betNumberAndWagerPosition[1]);
                        wagerNumbers.Add(wagerConsecutive);
                    }

                    theLowestWagerNumber = wagerNumbers.Min();
                    theHighestWagerNumber = wagerNumbers.Max();
                }
                else
                {
                    var paymentProcessor = WholePaymentProcessor.Instance().SearchFragmentProcessor();
                    PostFreeFormWagerCollectionSuccessResponse response;
                    using (RecordSet recordSet = paymentProcessor.GetRecordSet())
                    {
                        recordSet.SetParameter("ticketNumber", transaction.TicketNumber);
                        recordSet.SetParameter("customerId", transaction.AccountNumber);
                        recordSet.SetParameter("wagers", postFreeFormWagers.PostFreeFormWagers);

                        response = paymentProcessor.Execute<PostFreeFormWagerCollectionSuccessResponse>(DateTime.Now, recordSet);
                    }

                    var wagerNumbers = response.Wagers.Select(x => int.Parse(x.WagerNumber));
                    theLowestWagerNumber = wagerNumbers.Min();
                    theHighestWagerNumber = wagerNumbers.Max();
                }

                result = LottoAPI.Actor.PerformCmd($@"
                    {{
                        lotto900.CreateWagers(ItIsThePresent,{transaction.TheLowestBetId}, {transaction.TheHighestBetId}, {theLowestWagerNumber}, {theHighestWagerNumber}, {transaction.TicketNumber}, {transaction.OrderNumber}, Now);
                    }}"
                );

                if (!(result is OkObjectResult)) throw new Exception(((ObjectResult)result).ToString());

            }
        }

        [Obsolete("No se elimina porque pueden quedar mensajes en la cola y hay que procesarlos antes de eliminar estas funciones.")]
        private class GradeConsumer : GamesEngine.Settings.Consumer
        {
			private string topic;
            [Obsolete("No se elimina porque pueden quedar mensajes en la cola y hay que procesarlos antes de eliminar estas funciones.")]
            public GradeConsumer(string group, string topic) : base(group, topic)
            {
				this.topic = topic;

			}

            public override void OnMessageBeforeCommit(string msg)
            {
                if (String.IsNullOrEmpty(msg)) throw new Exception(nameof(msg));
                if (!Build.IsInDevelopmentMode)
                {
                    var gradeFreeFormWagers = new List<PayFragmentsMessage>();
                    string[] messages = KafkaMessages.Split(msg);
                    foreach (string message in messages)
                    {
                        var gradedWager = new GradedWagerMessage(message);
                        var isWinningPrizeExactlyRisk = gradedWager.AdjustedWinAmount == 0 && gradedWager.AdjustedLossAmount == 0 && gradedWager.Status == WagerStatus.W;
                        var isAdjustWinAmountRequired = gradedWager.AdjustedWinAmount != 0 && gradedWager.Status == WagerStatus.W;
                        var isAdjustLossAmountRequired = isWinningPrizeExactlyRisk || (gradedWager.AdjustedLossAmount != 0 && gradedWager.Status == WagerStatus.L);

                        var gradeFreeFormWager = new PayFragmentsMessage()
                        {
                            AdjustedLossAmount = isAdjustLossAmountRequired ? gradedWager.AdjustedLossAmount.ToString() : string.Empty,
                            AdjustedWinAmount = isAdjustWinAmountRequired ? gradedWager.AdjustedWinAmount.ToString() : string.Empty,
                            DailyFigureDate_YYYYMMDD = gradedWager.Now.ToString("yyyyMMdd"),
                            IsValidTicketNumber = true,
                            Outcome = isWinningPrizeExactlyRisk ? WagerStatus.L.ToString() : gradedWager.Status.ToString(),
                            TicketNumber = gradedWager.TicketNumber.ToString(),
                            WagerNumber = gradedWager.WagerNumber.ToString()
                        };
                        if (gradeFreeFormWager.WagerNumber != "0")//Arregla el Id 463395 de la BD pues fue una compra sin wagers
                        {
                            gradeFreeFormWagers.Add(gradeFreeFormWager);
                        }
                    }

                    if (gradeFreeFormWagers.Count != 0)//Arregla el Id 463395 de la BD pues fue una compra sin wagers
                    {
                        var paymentProcessor = WholePaymentProcessor.Instance().SearchGradeProcessorBy();
                        GradeFreeFormWagersResponse response;
                        using (RecordSet recordSet = paymentProcessor.GetRecordSet())
                        {
                            recordSet.SetParameter("wagers", gradeFreeFormWagers);

                            response = paymentProcessor.Execute<GradeFreeFormWagersResponse>(DateTime.Now, recordSet);
                        }

                        var scriptWagersWithProblems = new StringBuilder();
                        foreach (var wager in response.Wagers)
                        {
                            if (!wager.IsValidTicketNumber)
                            {
                                scriptWagersWithProblems.Append($"lotto900.AddWagerGradedWithProblemAboutIsValidTicket({wager.TicketNumber}, {wager.WagerNumber});");
                            }
                        }
                        IActionResult result;
                        if (scriptWagersWithProblems.Length != 0)
                        {
                            result = LottoAPI.Actor.PerformCmd($@"
                                {{
                                    {scriptWagersWithProblems.ToString()}
                                }}"
                            );
                            if (!(result is OkObjectResult)) throw new Exception(((ObjectResult)result).ToString());
                        }

                        KafkaMessages buffer = new KafkaMessages();
                        GradedWagerMessage gradedWagerMessage;
                        foreach (var gradeFreeFormWager in response.FailedWagers)
                        {
                            var ticketNumber = int.Parse(gradeFreeFormWager.TicketNumber);
                            var wagerNumber = int.Parse(gradeFreeFormWager.WagerNumber);
                            Enum.TryParse(gradeFreeFormWager.Outcome, out WagerStatus status);
                            var now = DateTime.ParseExact(gradeFreeFormWager.DailyFigureDate_YYYYMMDD, "yyyyMMdd", CultureInfo.InvariantCulture);
                            var adjustedWinAmount = string.IsNullOrEmpty(gradeFreeFormWager.AdjustedWinAmount) ? 0m : decimal.Parse(gradeFreeFormWager.AdjustedWinAmount);
                            var adjustedLossAmount = string.IsNullOrEmpty(gradeFreeFormWager.AdjustedLossAmount) ? 0m : decimal.Parse(gradeFreeFormWager.AdjustedLossAmount);
                            gradedWagerMessage = new GradedWagerMessage(ticketNumber, wagerNumber, status, now, adjustedWinAmount, adjustedLossAmount);

                            var message = gradedWagerMessage.Serialize();
                            if (buffer.ItsEnoughSpace(message))
                            {
                                buffer.Add(message);
                            }
                            else
                            {
                                if (buffer.ItsNotEmpty()) Integration.Kafka.Send(true, this.topic, buffer);
                                    buffer.Clear();
                                buffer.Add(message);
                            }
                        }
                        if (buffer.ItsNotEmpty())
                        {
                            Integration.Kafka.Send(true, this.topic, buffer);
                                buffer.Clear();
                        }
                    }
                }
            }
        }


        private class FragmentPaymentsCallbackConsumer : GamesEngine.Settings.Consumer
        {
            public FragmentPaymentsCallbackConsumer(string group, string topic) : base(group, topic)
            {
            }

            public override void OnMessageBeforeCommit(string msg)
            {
                if (String.IsNullOrEmpty(msg)) throw new Exception(nameof(msg));

                FragmentPaymentsWithProblemsResponse payFragmentsWithProblemsResponse = new FragmentPaymentsWithProblemsResponse(msg);

                if (payFragmentsWithProblemsResponse.ResponseType == CashierResponse.CashierResponseType.PAY_FRAGMENT_WITH_PROBLEMS)
                {

                    if (payFragmentsWithProblemsResponse.HasItems())
                    {
                        var scriptWagersWithProblems = new StringBuilder();
                        foreach (AuthorizationAndFragmentPair authorizationAndFragment in payFragmentsWithProblemsResponse.ListItems())
                        {
                            scriptWagersWithProblems.Append($"lotto900.AddWagerGradedWithProblemAboutIsValidTicket({authorizationAndFragment.Authorization}, {authorizationAndFragment.Fragment});");
                        }

                        LottoAPI.Actor.PerformCmd($@"
                            {{
                                {scriptWagersWithProblems.ToString()}
                            }}"
                         );
                    }
                }
                else
                {
                    throw new Exception("Unhandled payment message type");
                }

            }
        }

        class LotteryDrawConsumer : Consumer
        {
            public LotteryDrawConsumer(string group, string topic) : base(group, topic)
            {
            }

            LotteryDrawType GetLotteryDrawType(string message)
            {
                return (LotteryDrawType)(int)message[0];
            }

            GenericGameType GetGameType(string message)
            {
                return (GenericGameType)int.Parse(message[2] + "");
            }

            int GetPickNumber(GenericGameType gameType)
            {
                switch(gameType)
                {
                    case GenericGameType.P2:
                        return 2;
                    case GenericGameType.P3:
                        return 3;
                    case GenericGameType.P4:
                        return 4;
                    case GenericGameType.P5:
                        return 5;
                    default:
                        throw new Exception($"No implementation for game type {gameType}");
                }
            }

            public override void OnMessageBeforeCommit(string msg)
            {
                if (string.IsNullOrWhiteSpace(msg)) throw new Exception(nameof(msg));

                var type = GetLotteryDrawType(msg);
                var gameType = GetGameType(msg);
                switch(gameType)
                {
                    case GenericGameType.P2:
                    case GenericGameType.P3:
                    case GenericGameType.P4:
                    case GenericGameType.P5:
                        var pickNumber = GetPickNumber(gameType);
                        PerformLotteryDraw(msg, type, IdOfLotteryGame.Picks, pickNumber);
                        break;
                    case GenericGameType.TZ:
                        PerformLotteryDraw(msg, type, IdOfLotteryGame.Triz);
                        break;
                    default:
                        throw new Exception($"No implementation for game type {gameType}");
                }
            }

            private string GetLotteryScript(IdOfLotteryGame idOfLotteryGame, string state, int pickNumber)
            {
                StringBuilder lotteryGameCmd = new StringBuilder();

                lotteryGameCmd.AppendLine($"lotteryGame = company.LotteryGamesPool.GetLotteryGame({idOfLotteryGame});");
                switch(idOfLotteryGame)
                {
                    case IdOfLotteryGame.Picks:
                        lotteryGameCmd.Append($"lottery = lotteryGame.GetLottery({pickNumber}, {state});");
                        break;
                    case IdOfLotteryGame.Triz:
                        lotteryGameCmd.Append("lottery = lotteryGame.GetLottery();");
                        break;
                    default:
                        throw new Exception($"No implementation for lottery game {idOfLotteryGame}");
                }

                return lotteryGameCmd.ToString();
            }

            void PerformLotteryDraw(string msg, LotteryDrawType type, IdOfLotteryGame idOfLotteryGame, int pickNumber = int.MinValue)
            {
                IActionResult result = null;
                string state;
                string lotteryLine;
                switch (type)
                {
                    case LotteryDrawType.GRADE:
                        var gradeMessage = new LotteryDrawGradeMessage(msg);
                        if (gradeMessage.Localization == Integration.Localization) return;

                        state = $"state{gradeMessage.State}";
                        lotteryLine = GetLotteryScript(idOfLotteryGame, state, pickNumber);

                        var dateAsText = gradeMessage.DrawDate.ToString("MM/dd/yyyy HH:mm:ss");
                        var resultQry = LottoAPI.Actor.PerformQry($@"
                            {{
                                {lotteryLine}
                                schedule = lottery.GetScheduleByUniqueId({gradeMessage.ScheduleId});
                                print lottery.IsGradedAt({dateAsText}, schedule) isAlreadyGraded;
                                print schedule.Hour.Hour drawDateHour;
                                print schedule.Hour.Minute drawDateMinute;
                            }}
                        ");
                        if (!(resultQry is OkObjectResult))
                        {
                            throw new GameEngineException($@"Error:{((ObjectResult)resultQry).Value.ToString()}");
                        }

                        OkObjectResult o = (OkObjectResult)resultQry;
                        string json = o.Value.ToString();
                        var gradeExistence = JsonConvert.DeserializeObject<GradeExistence>(json);
                        if (!gradeExistence.isAlreadyGraded)
                        {
                            if (idOfLotteryGame == IdOfLotteryGame.Picks)
                            {
                                bool hasFireball = gradeMessage.FireBallNumber != LotteryDraw.WITHOUT_FIREBALL;
                                if (hasFireball)
                                {
                                    result = LottoAPI.Actor.PerformCmd($@"
                                        {{
				                            {lotteryLine}
                                            lottery.DrawPicks({dateAsText},'{gradeMessage.WinnerNumbers}', {gradeMessage.FireBallNumber}, Now, ItIsThePresent, '{gradeMessage.EmployeeName}', false);
                                        }}
				                    ");
                                }
                                else
                                {
                                    result = LottoAPI.Actor.PerformCmd($@"
                                        {{
				                            {lotteryLine}
                                            lottery.DrawPicks({dateAsText},'{gradeMessage.WinnerNumbers}', Now, ItIsThePresent, '{gradeMessage.EmployeeName}', false);
                                        }}
				                    ");
                                }
                            }
                            else if (idOfLotteryGame == IdOfLotteryGame.Triz)
                            {
                                result = LottoAPI.Actor.PerformCmd($@"
                                    {{
				                        {lotteryLine}
                                        lottery.DrawTriz({dateAsText},'{gradeMessage.WinnerNumbers}', Now, ItIsThePresent, '{gradeMessage.EmployeeName}', false);
                                    }}
				                ");
                            }
                            else
                            {
                                throw new GameEngineException($"No implementation for lottery game {idOfLotteryGame}");
                            }
                        }
                        break;
                    case LotteryDrawType.REGRADE:
                        var regradeMessage = new LotteryDrawRegradeMessage(msg);
                        if (regradeMessage.Localization == Integration.Localization) return;

                        state = $"state{regradeMessage.State}";
                        lotteryLine = GetLotteryScript(idOfLotteryGame, state, pickNumber);

                        dateAsText = regradeMessage.DrawDate.ToString("MM/dd/yyyy HH:mm:ss");
                        resultQry = LottoAPI.Actor.PerformQry($@"
                            {{
                                {lotteryLine}
                                schedule = lottery.GetScheduleByUniqueId({regradeMessage.ScheduleId});
                                print lottery.IsRegraded({dateAsText}, schedule) isAlreadyRegraded;
                                print schedule.Hour.Hour drawDateHour;
                                print schedule.Hour.Minute drawDateMinute;
                            }}
                        ");
                        if (!(resultQry is OkObjectResult))
                        {
                            throw new GameEngineException($@"Error:{((ObjectResult)resultQry).Value.ToString()}");
                        }

                        o = (OkObjectResult)resultQry;
                        json = o.Value.ToString();
                        var regradeExistence = JsonConvert.DeserializeObject<RegradeExistence>(json);
                        if (!regradeExistence.isAlreadyRegraded)
                        {
                            result = LottoAPI.Actor.PerformCmd($@"
                            {{
				                {lotteryLine}
                                lottery.Regrade(ItIsThePresent, {dateAsText}, Now, '{regradeMessage.EmployeeName}', false);
                            }}
				            ");
                        }
                        break;
                    case LotteryDrawType.NOACTION:
                        var noactionMessage = new LotteryDrawNoActionMessage(msg);
                        if (noactionMessage.Localization == Integration.Localization) return;

                        state = $"state{noactionMessage.State}";
                        lotteryLine = GetLotteryScript(idOfLotteryGame, state, pickNumber);

                        dateAsText = noactionMessage.DrawDate.ToString("MM/dd/yyyy HH:mm:ss");
                        resultQry = LottoAPI.Actor.PerformQry($@"
                            {{
                                {lotteryLine}
                                schedule = lottery.GetScheduleByUniqueId({noactionMessage.ScheduleId});
                                print lottery.IsNoActionAt({dateAsText}, schedule) isAlreadyNoAction;
                                print schedule.Hour.Hour drawDateHour;
                                print schedule.Hour.Minute drawDateMinute;
                            }}
                        ");
                        if (!(resultQry is OkObjectResult))
                        {
                            throw new GameEngineException($@"Error:{((ObjectResult)resultQry).Value.ToString()}");
                        }

                        o = (OkObjectResult)resultQry;
                        json = o.Value.ToString();
                        var noactionExistence = JsonConvert.DeserializeObject<NoActionExistence>(json);
                        if (!noactionExistence.isAlreadyNoAction)
                        {
                            result = LottoAPI.Actor.PerformCmd($@"
                            {{
				                {lotteryLine}
                                lottery.SetNoAction(itIsThePresent, {dateAsText}, Now, '{noactionMessage.EmployeeName}', false);
                            }}
				            ");
                        }
                        break;
                    case LotteryDrawType.CONFIRMATION:
                        var confirmationMessage = new LotteryDrawConfirmationMessage(msg);
                        if (confirmationMessage.Localization == Integration.Localization) return;

                        state = $"state{confirmationMessage.State}";
                        lotteryLine = GetLotteryScript(idOfLotteryGame, state, pickNumber);

                        dateAsText = confirmationMessage.DrawDate.ToString("MM/dd/yyyy HH:mm:ss");
                        resultQry = LottoAPI.Actor.PerformQry($@"
                            {{
                                {lotteryLine}
                                schedule = lottery.GetScheduleByUniqueId({confirmationMessage.ScheduleId});
                                print lottery.WasAlreadyConfirmed({dateAsText}) isAlreadyConfirmed;
                                print schedule.Hour.Hour drawDateHour;
                                print schedule.Hour.Minute drawDateMinute;
                            }}
                        ");
                        if (!(resultQry is OkObjectResult))
                        {
                            throw new GameEngineException($@"Error:{((ObjectResult)resultQry).Value.ToString()}");
                        }

                        o = (OkObjectResult)resultQry;
                        json = o.Value.ToString();
                        var confirmationExistence = JsonConvert.DeserializeObject<ConfirmationExistence>(json);
                        if (!confirmationExistence.isAlreadyConfirmed)
                        {
                            result = LottoAPI.Actor.PerformCmd($@"
                            {{
				                {lotteryLine}
                                canBeConfirmed = lottery.CanBeConfirmed({dateAsText});
                                if (canBeConfirmed)
                                {{
                                    lottery.ConfirmDraw({dateAsText}, Now, '{confirmationMessage.EmployeeName}', itIsThePresent, false);
                                }}
                            }}
				            ");
                        }
                        break;
                    default:
                        throw new GameEngineException($"{nameof(type)} {type} is not implemented");
                }
                if (result != null && !(result is OkObjectResult))
                {
                    throw new GameEngineException($@"Error:{((ObjectResult)result).Value.ToString()}");
                }
            }
        }

        class TicketsResenderCallbackConsumer : Consumer
        {
            public TicketsResenderCallbackConsumer(string group, string topic) : base(group, topic)
            {
            }

            public override void OnMessageBeforeCommit(string msg)
            {
                if (string.IsNullOrWhiteSpace(msg)) throw new Exception(nameof(msg));

                var message = new TicketsStreamEndingMessage(msg);
                var lotteryLine = GetLotteryLine(message.IdPick, message.StateAbb);
                var drawDate = message.DrawDate;
                var stringDate = $"{drawDate.Month}/{drawDate.Day}/{drawDate.Year} {drawDate.Hour}:{drawDate.Minute}:{drawDate.Second}";
                IActionResult result = LottoAPI.Actor.PerformQry($@"
                {{
                    {lotteryLine}
                    lottery.ResendTicketsToHistorical({stringDate}, ItIsThePresent);
                }}
                ");
                if (!(result is OkObjectResult)) throw new Exception(((ObjectResult)result).ToString());
            }

            string GetLotteryLine(IdOfLottery idPick, string stateAbb)
            {
                switch (idPick)
                {
                    case IdOfLottery.P2:
                        return $"lottery = lotto900.GetLottery(2, state{stateAbb});";
                    case IdOfLottery.P3:
                        return $"lottery = lotto900.GetLottery(3, state{stateAbb});";
                    case IdOfLottery.P4:
                        return $"lottery = lotto900.GetLottery(4, state{stateAbb});";
                    case IdOfLottery.P5:
                        return $"lottery = lotto900.GetLottery(5, state{stateAbb});";
                    case IdOfLottery.PB:
                        return $"lottery = lotto900..GetPowerball();";
                    default:
                        throw new Exception($"No implementation for id pick '{idPick}'");
                }
            }
        }


        class StoreRegistrationCallbackConsumer : Consumer
        {
            public StoreRegistrationCallbackConsumer(string group, string topic) : base(group, topic)
            {
            }

            public override void OnMessageBeforeCommit(string msg)
            {
                if (string.IsNullOrWhiteSpace(msg)) throw new Exception(nameof(msg));

                var storeMsg = new StoreRegistrationCallbackMessage(msg);
                var result = LottoAPI.Actor.PerformCmd($@"
                    company.ReplaceStoreIdAndCreateTenant('{storeMsg.TenantName}', {storeMsg.TenantId}, {storeMsg.StoreId});
				");

                if (!(result is OkObjectResult))
                {
                    throw new Exception($@"Error:{((ObjectResult)result).Value}");
                }
                else
                {
                    new DrawController().CreateConsumerForTopics(storeMsg.TenantName, storeMsg.StoreId);
                }
            }
        }

        class CustomSettingsConsumer : Consumer
        {
            public CustomSettingsConsumer(string group, string topic) : base(group, topic)
            {
            }

            public override void OnMessageBeforeCommit(string msg)
            {
                if (string.IsNullOrWhiteSpace(msg)) throw new Exception(nameof(msg));

                var customSettingMsg = new CustomSettingMessage(msg);
                var result = LottoAPI.Actor.PerformQry(@"
                    {{
					    print company.Sales.CurrentStore.Id storeId;
                    }}"
                );
                var o = (OkObjectResult)result;
                var json = o.Value.ToString();
                var storeData = JsonConvert.DeserializeObject<StoreData>(json);

                var thisMessageBelongsToTheCurrentStore = storeData.storeId == customSettingMsg.OriginStoreId;
                if (thisMessageBelongsToTheCurrentStore) return;

                var commands = new StringBuilder();
                bool existVariableName = false;
                if (customSettingMsg.SettingType.HasValue)
                {
                    existVariableName = true;
                    if (customSettingMsg.SettingType == CustomSettingType.String)
                        commands.AppendLine($"cs = customSettings.ChangeValueStartingOn({customSettingMsg.DateToApplyTheChange}, '{customSettingMsg.Key}', '{customSettingMsg.Value}', '{customSettingMsg.EmployeeName}');");
                    else
                        commands.AppendLine($"cs = customSettings.ChangeValueStartingOn({customSettingMsg.DateToApplyTheChange}, '{customSettingMsg.Key}', {customSettingMsg.Value}, '{customSettingMsg.EmployeeName}');");
                }
                if (!string.IsNullOrWhiteSpace(customSettingMsg.Description))
                {
                    if (!existVariableName)
                    {
                        commands.AppendLine($"cs = customSettings.Get(Now, '{customSettingMsg.Key}');");
                        existVariableName = true;
                    }
                    commands.AppendLine($"cs.Description = '{customSettingMsg.Description}';");
                }
                if (customSettingMsg.Enabled.HasValue)
                {
                    if (!existVariableName)
                    {
                        commands.AppendLine($"cs = customSettings.Get(Now, '{customSettingMsg.Key}');");
                        existVariableName = true;
                    }
                    commands.AppendLine($"cs.Enabled = {customSettingMsg.Enabled.Value};");
                }

                result = LottoAPI.Actor.PerformCmd($@"
                        {{
                            {commands}
                        }}
                        ");
                if (!(result is OkObjectResult))
                {
                    throw new Exception($@"Error:{((ObjectResult)result).Value}");
                }
            }
        }



        struct StoreData
        {
            public int storeId { get; set; }
            public string storeAlias { get; set; }
        }

        [DataContract(Name = "drawnLottery")]
        public class DrawnLottery
        {
            [DataMember(Name = "drawDate")]
            public string DrawDate { get; set; }
            [DataMember(Name = "state")]
            public string State { get; set; }
            [DataMember(Name = "winnerNumbers")]
            public string WinnerNumbers { get; set; }
            [DataMember(Name = "fireballNumber")]
            public int FireBallNumber { get; set; } = LotteryDraw.WITHOUT_FIREBALL;
            [DataMember(Name = "employeeName")]
            public string EmployeeName { get; set; }
        }

        [DataContract(Name = "drawnLotteryPowerball")]
        public class DrawnLotteryPowerball
        {
            [DataMember(Name = "drawDate")]
            public string DrawDate { get; set; }
            [DataMember(Name = "winnerNumbers")]
            public string WinnerNumbers { get; set; }
            [DataMember(Name = "multiplier")]
            public string Multiplier { get; set; }
            [DataMember(Name = "employeeName")]
            public string EmployeeName { get; set; }
        }
        [DataContract(Name = "drawnLotteryKeno")]
        public class DrawnLotteryKeno
        {
            [DataMember(Name = "drawPrefixId")]
            public string DrawPrefixId { get; set; }
            [DataMember(Name = "winnerNumbers")]
            public int[] WinnerNumbers { get; set; }
            [DataMember(Name = "multiplier")]
            public int Multiplier { get; set; }
            [DataMember(Name = "bulleye")]
            public int Bulleye { get; set; }
        }
        [DataContract(Name = "regradedLottery")]
        public class RegradedLottery
        {
            [DataMember(Name = "drawDate")]
            public string DrawDate { get; set; }
            [DataMember(Name = "state")]
            public string State { get; set; }
            [DataMember(Name = "employeeName")]
            public string EmployeeName { get; set; }
        }

        [DataContract(Name = "regradedLotteryPowerball")]
        public class RegradedLotteryPowerball
        {
            [DataMember(Name = "drawDate")]
            public string DrawDate { get; set; }
            [DataMember(Name = "multiplier")]
            public int Multiplier { get; set; }
            [DataMember(Name = "employeeName")]
            public string EmployeeName { get; set; }
        }

        [DataContract(Name = "regradedLotteryKeno")]
        public class RegradedLotteryKeno
        {
            [DataMember(Name = "drawDate")]
            public string DrawDate { get; set; }
            [DataMember(Name = "drawPrefixId")]
            public string DrawPrefixId { get; internal set; }
        }

        [DataContract(Name = "enabledDraw")]
        public class EnabledDraw
        {
            [DataMember(Name = "drawDate")]
            public string DrawDate { get; set; }
            [DataMember(Name = "state")]
            public string State { get; set; }
            [DataMember(Name = "employeeName")]
            public string EmployeeName { get; set; }
            [DataMember(Name = "url")]
            public string Url { get; set; }
        }

        [DataContract(Name = "scheduleDate")]
        public class ScheduleDate
        {
            [DataMember(Name = "drawDate")]
            public string DrawDate { get; set; }
            [DataMember(Name = "scheduledHour")]
            public string ScheduledHour { get; set; }
            [DataMember(Name = "state")]
            public string State { get; set; }
            [DataMember(Name = "employeeName")]
            public string EmployeeName { get; set; }
        }

        [DataContract(Name = "enabledDrawPowerball")]
        public class EnabledDrawPowerball
        {
            [DataMember(Name = "drawDate")]
            public string DrawDate { get; set; }
            [DataMember(Name = "employeeName")]
            public string EmployeeName { get; set; }
            [DataMember(Name = "url")]
            public string Url { get; set; }
        }

        [DataContract(Name = "drawConfirmation")]
        public class DrawConfirmation
        {
            [DataMember(Name = "drawDate")]
            public string DrawDate { get; set; }
            [DataMember(Name = "state")]
            public string State { get; set; }
        }

		[DataContract(Name = "drawLimit")]
		public class DrawLimit
		{
			[DataMember(Name = "limitAmount")]
			public decimal LimitAmount { get; set; }
		}

        [DataContract(Name = "RiskToleranceFactorBody")]
        public class RiskToleranceFactorBody
        {
            [DataMember(Name = "riskToleranceFactor")]
            public decimal RiskToleranceFactor { get; set; }
        }

        [DataContract(Name = "drawConfirmationPowerball")]
        public class DrawConfirmationPowerball
        {
            [DataMember(Name = "drawDate")]
            public string DrawDate { get; set; }
            [DataMember(Name = "employeeName")]
            public string EmployeeName { get; set; }
        }

        [DataContract(Name = "drawNoAction")]
        public class DrawNoAction
        {
            [DataMember(Name = "drawDate")]
            public string DrawDate { get; set; }
            [DataMember(Name = "state")]
            public string State { get; set; }
            [DataMember(Name = "employeeName")]
            public string EmployeeName { get; set; }
        }

        [DataContract(Name = "drawNoActionPowerball")]
        public class DrawNoActionPowerball
        {
            [DataMember(Name = "drawDate")]
            public string DrawDate { get; set; }
            [DataMember(Name = "multiplier")]
            public int Multiplier { get; set; }
            [DataMember(Name = "employeeName")]
            public string EmployeeName { get; set; }
        }

        [DataContract(Name = "drawNoActionKeno")]
        public class DrawNoActionKeno
        {
            [DataMember(Name = "drawDate")]
            public string DrawDate { get; set; }
        }

        [DataContract(Name = "drawNoActionPreview")]
        public class DrawNoActionPreview
        {
            [DataMember(Name = "drawDate")]
            public string DrawDate { get; set; }
            [DataMember(Name = "state")]
            public string State { get; set; }
        }

        [DataContract(Name = "drawNoActionPreviewPowerball")]
        public class DrawNoActionPreviewPowerball
        {
            [DataMember(Name = "drawDate")]
            public string DrawDate { get; set; }
            [DataMember(Name = "multiplier")]
            public int Multiplier { get; set; }
        }

        [DataContract(Name = "schedule")]
        public class Schedule
        {
            [DataMember(Name = "id")]
            public int Id { get; set; }
            [DataMember(Name = "state")]
            public string State { get; set; }
            [DataMember(Name = "drawHour")]
            public string DrawHour { get; set; }
            [DataMember(Name = "daysOfWeek")]
            public int[] DaysOfWeek { get; set; }
            [DataMember(Name = "daysOfWeekString")]
            public String DaysOfWeekString { get; set; }
            [DataMember(Name = "type")]
            public string Type { get; set; }
            [DataMember(Name = "description")]
            public string Description { get; set; }
            [DataMember(Name = "employeeName")]
            public string EmployeeName { get; set; }

            public void FillDaysOfWeek()
            {
                string arrayWithOutBrackets = DaysOfWeekString.Trim().Substring(1, DaysOfWeekString.Length - 2);
                string[] values = arrayWithOutBrackets.Split(',');

                DaysOfWeek = Array.ConvertAll<string, int>(values, int.Parse);
            }
            public bool IsEqualsTo(Schedule other)
            {
                if (this.Id != other.Id)
                {
                    return false;
                }
                else if (!this.State.Equals(other.State))
                {
                    return false;
                }
                else if (!this.DrawHour.Equals(other.DrawHour))
                {
                    return false;
                }
                else if (this.DaysOfWeek.Length != other.DaysOfWeek.Length)
                {
                    return false;
                }
                else if (!this.Type.Equals(other.Type))
                {
                    return false;
                }

                for (int index = 0; index < this.DaysOfWeek.Length; index++)
                {
                    if (this.DaysOfWeek[index] != other.DaysOfWeek[index])
                    {
                        return false;
                    }
                }

                return true;
            }
        }

        [DataContract(Name = "DrawDatesByPick")]
        public class DrawDatesByPick
        {
            [DataMember(Name = "date")]
            public string Date { get; set; }
            [DataMember(Name = "drawSchedules")]
            public DrawScheduleByPick[] DrawSchedules { get; set; }
        }

        [DataContract(Name = "DrawScheduleByPick")]
        public class DrawScheduleByPick
        {
            [DataMember(Name = "state")]
            public string State { get; set; }
            [DataMember(Name = "hour")]
            public string Hour { get; set; }
            [DataMember(Name = "pick")]
            public int Pick { get; set; }
            [DataMember(Name = "withFireBall")]
            public bool WithFireBall { get; set; }
        }

        [DataContract(Name = "drawDates")]
        public class DrawDates
        {
            [DataMember(Name = "dates")]
            public string[] Dates { get; set; }
            [DataMember(Name = "drawSchedules")]
            public DrawSchedule[] DrawSchedules { get; set; }
        }

        [DataContract(Name = "drawSchedule")]
        public class DrawSchedule
        {
            [DataMember(Name = "state")]
            public string State { get; set; }
            [DataMember(Name = "hour")]
            public string Hour { get; set; }
            [DataMember(Name = "withFireBall")]
            public bool WithFireBall { get; set; }
        }

        [DataContract(Name = "ForwardingDrawBody")]
        public class ForwardingDrawBody
        {
            [DataMember(Name = "drawDate")]
            public string DrawDate { get; set; }
            [DataMember(Name = "state")]
            public string State { get; set; }
        }

        [DataContract(Name = "DisincentiveBody")]
        public class DisincentiveBody
        {
            [DataMember(Name = "state")]
            public string State { get; set; }
            [DataMember(Name = "uniqueDrawingId")]
            public int UniqueDrawingId { get; set; }
            [DataMember(Name = "prizeCriteriaName")]
            public string PrizeCriteriaName { get; set; }
            [DataMember(Name = "ruleType")]
            public RuleType? RuleType { get; set; }
            [DataMember(Name = "disincentivePercentage")]
            public int DisincentivePercentage { get; set; }
        }

        public class GradeExistence
        {
            public bool isAlreadyGraded { get; set; }
            public int drawDateHour { get; set; }
            public int drawDateMinute { get; set; }
        }
        public class RegradeExistence
        {
            public bool isAlreadyRegraded { get; set; }
            public int drawDateHour { get; set; }
            public int drawDateMinute { get; set; }
        }
        public class NoActionExistence
        {
            public bool isAlreadyNoAction { get; set; }
            public int drawDateHour { get; set; }
            public int drawDateMinute { get; set; }
        }
        public class ConfirmationExistence
        {
            public bool isAlreadyConfirmed { get; set; }
            public int drawDateHour { get; set; }
            public int drawDateMinute { get; set; }
        }
    }
}


