﻿using Connectors.town.connectors.driver.transactions;
using Puppeteer.EventSourcing;
using RestSharp;
using System;
using System.Diagnostics;
using System.Net.Http;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.commons;
using town.connectors.drivers;
using town.connectors.drivers.hades;
using static town.connectors.CustomSettings;
using static town.connectors.drivers.Result;

namespace Connectors.town.connectors.drivers.hades
{
	public class Deposit : ASIProcessorDriver
	{
		private const float VERSION = 1.0F;
		private RestClient _gradeFreeFormWagerCollectionClient;
		private string SystemId;
		private string SystemPassword;

		public override string Description => $"Hades {nameof(Deposit)} driver {VERSION}";

		public Deposit()
			: base(TransactionType.Deposit, VERSION)
		{
        }

		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet) 
        {
			if (_gradeFreeFormWagerCollectionClient== null)
			{
				string companyBaseUrlServices = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
				_gradeFreeFormWagerCollectionClient = new RestClient(companyBaseUrlServices);
			}

			if (CustomSettings.ThereArePendingChanges)
			{
				bool changeApplied = false;
				string companyBaseUrlServices = CustomSettings.Get(now, "CompanyBaseUrlServices", out changeApplied).AsString;
				if (changeApplied)
				{
					_gradeFreeFormWagerCollectionClient = new RestClient(companyBaseUrlServices);
				}

				SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
				SystemPassword = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
			}

			CustomSetting CustomerId = recordSet.Mappings["customerId"];
			CustomSetting Amount = recordSet.Mappings["amount"];
			CustomSetting Description = recordSet.Mappings["description"];
			CustomSetting ReferenceID = recordSet.Mappings["referenceID"];

			var authorizationNumber = MakeADeposit(now, CustomerId.AsString, Amount.AsDecimal, Description.AsString, ReferenceID.AsString);
			var result = new DepositTransaction(authorizationNumber, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes);
			return (T)Convert.ChangeType(result, typeof(T));
		}

		private int MakeADeposit(DateTime now, string customerId, decimal amount, string description, string referenceID)
		{
			if (String.IsNullOrWhiteSpace(customerId)) throw new ArgumentNullException(nameof(customerId));
			if (String.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));
			if (String.IsNullOrWhiteSpace(referenceID)) throw new ArgumentNullException(nameof(referenceID));
			if (amount <= 0) throw new Exception($"Amount {amount} is not valid to send request {nameof(Deposit)}");

			Debug.WriteLine($"Accounting service {nameof(Deposit)} received {nameof(customerId)}:{customerId} {nameof(amount)}:{amount}");
			var result = TriggerDeposit(now, customerId, amount, description, referenceID);
			return result;
		}

		private int TriggerDeposit(DateTime now, string customerId, decimal amount, string description, string referenceID)
		{
			if (String.IsNullOrWhiteSpace(customerId)) throw new ArgumentNullException(nameof(customerId));
			if (String.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));
			if (String.IsNullOrWhiteSpace(referenceID)) throw new ArgumentNullException(nameof(referenceID));
			if (referenceID.Length >= 31) throw new Exception($"Reference {referenceID} its longer than 31 characters.");

			if (amount <= 0) throw new Exception($"{nameof(amount)} {amount} is not valid to send request {nameof(Deposit)}");

			var strAmount = amount.ToString();
			var values = new CreditMessage()
			{
				SystemId = SystemId,
				SystemPassword = SystemPassword,
				CustomerId = customerId,
				Amount = strAmount,
				Description = description,
				ReferenceID = referenceID
			};

			const string url = "/v1/5dimesAPI/PostTransactionWReference";
			var jsonString = Commons.ToJson(values);

			string responseString = "";

			string valuesWithHiddenFields = ASIJsonUtils.HideSensitiveData(values, Loggers.GetIntance().AccountingServicesASIPostTransactionWRef);

			try
			{
				Loggers.GetIntance().AccountingServicesASIPostTransactionWRef.Debug($@"url:{url} data:{valuesWithHiddenFields}");

				var request = new RestRequest(url, Method.POST);
				request.AddHeader("Content-Type", "application/json");
				request.AddParameter("application/json", jsonString, ParameterType.RequestBody);
				var response = _gradeFreeFormWagerCollectionClient.Execute(request);
				responseString = response.Content;

				int authorizationNumber = FAKE_DOCUMENT_NUMBER;

				bool parsingResult = int.TryParse(responseString, out authorizationNumber);

				// EL servicio redonde a 2 decimales
				// punto para los decimales
				//success *********
				// -1 Reference ya esta usado en otra transacion. Mientras el Reference sea distinto se pueden enviar tantas veces el servicio como se quiera
				// -1 La cuenta no existe
				// -1 systemID
				// -1 sReferen es muy grande.
				// -1 si tranCodeno es valido
				// -1 Negativo en amount
				// -1 Si debido más de lo que hay en la cuenta.

				Loggers.GetIntance().AccountingServicesASIPostTransactionWRef.Debug($@"response:{responseString}");

				if (!parsingResult || authorizationNumber == FAKE_DOCUMENT_NUMBER)
				{
					NotifyWarn(nameof(Deposit), $"Url:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}", $"{nameof(Deposit)} fails for {customerId} ref {referenceID}");
				}

				return authorizationNumber;
			}
			catch (Exception e)
			{
				Loggers.GetIntance().AccountingServicesASIPostTransactionWRef.Error($@"url:{url} data:{valuesWithHiddenFields} type:{e.GetType()} error:{e.Message}", e);

				var extraErrorMessage = string.Empty;
				if (e.InnerException is HttpRequestException) extraErrorMessage = "There was a problem getting the response from server";
				InternalOnError(nameof(Deposit), 1, e, $"Url:{url}", $"Request: {valuesWithHiddenFields}", $"Response: {responseString}", extraErrorMessage);

				return FAKE_DOCUMENT_NUMBER;
			}
		}

		public override Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet) 
        {
            throw new NotImplementedException();
        }

		public override void Prepare(DateTime now)
        {
			CustomSettings.AddVariableParameter("customerId");
			CustomSettings.AddVariableParameter("amount");
			CustomSettings.AddVariableParameter("description");
			CustomSettings.AddVariableParameter("referenceID");

		}
    }
}
