﻿using GamesEngine.Finance;
using GamesEngine.PurchaseOrders;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System.Collections.Generic;
using System.Linq;

namespace GamesEngine.Exchange
{
	internal class FavoriteAccounts : Objeto
    {
        private readonly List<FavoriteAccount> internalFavorites = new List<FavoriteAccount>();
        private readonly List<FavoriteAccount> externalFavorites = new List<FavoriteAccount>();
        private readonly Customer owner;

        internal FavoriteAccounts(Customer owner, IEnumerable<CustomerAccount> defaultAccounts)
        {
            this.owner = owner;

            foreach (var account in defaultAccounts)
            {
                Add(account, account.Alias);
            }
        }

        internal IEnumerable<FavoriteAccount> GetFavoriteAccounts(int initialIndex, int amountOfElements)
        {
            var result = internalFavorites.Concat(externalFavorites).Skip(initialIndex).Take(amountOfElements).ToList();
            return result;
        }

        internal void Add(CustomerAccount account, string alias)
        {
            var favorite = new InternalFavoriteAccount(account, alias);
            internalFavorites.Add(favorite);
        }

        internal void Add(Coin currencyCode, string accountNumber, string customerIdentifier, string alias)
        {
            var favorite = new ExternalFavoriteAccount(currencyCode, accountNumber, customerIdentifier, alias);
            externalFavorites.Add(favorite);
        }

        internal void RemoveInternalAccount(string accountNumber)
        {
            for (int index = internalFavorites.Count-1; index >=0 ; index--)
            {
                var favorite = internalFavorites[index];
                if (favorite.AccountNumber == accountNumber)
                {
                    internalFavorites.Remove(favorite);
                }
            }
        }

        internal void RemoveExternalAccount(string accountNumber)
        {
            for (int index = externalFavorites.Count - 1; index >= 0; index--)
            {
                var favorite = externalFavorites[index];
                if (favorite.AccountNumber == accountNumber)
                {
                    externalFavorites.Remove(favorite);
                }
            }
        }

        internal bool HasInternalAccount(string accountNumber)
        {
            foreach (var favorite in internalFavorites)
            {
                if (favorite.AccountNumber == accountNumber)
                {
                    return true;
                }
            }
            return false;
        }

        internal bool HasExternalAccount(string accountNumber)
        {
            foreach (var favorite in externalFavorites)
            {
                if (favorite.AccountNumber == accountNumber)
                {
                    return true;
                }
            }
            return false;
        }
    }

    [Puppet]
    internal abstract class FavoriteAccount : Objeto
    {
        public Coin Coin { get; protected set; }
        
        public string CurrencyCode { get { return Coin.Iso4217Code; } }
        internal string CurrencyCodeAsText
        {
            get
            {
                return CurrencyCode.ToString();
            }
        }

        public abstract string AccountNumber { get; }
        public abstract string CustomerIdentifier { get; }
        public string Alias { get; protected set; }
        internal abstract string Type();
    }

    internal class InternalFavoriteAccount : FavoriteAccount
    {
        private readonly CustomerAccount account;

        public override string AccountNumber 
        { 
            get 
            { 
                return account.Identificator; 
            } 
        }

        public override string CustomerIdentifier => account.CustomerIdentifier;

        internal InternalFavoriteAccount(CustomerAccount account, string alias)
        {
            this.account = account;
            Coin = account.Coin;
            Alias = alias;
        }

        internal override string Type()
        {
            return "Internal";
        }
    }

    internal class ExternalFavoriteAccount : FavoriteAccount
    {
        private readonly string accountNumber;

        public override string AccountNumber
        {
            get
            {
                return accountNumber;
            }
        }

        public override string CustomerIdentifier { get; }

        internal ExternalFavoriteAccount(Coin currencyCode, string accountNumber, string customerIdentifier, string alias)
        {
            Coin = currencyCode;
            this.accountNumber = accountNumber;
            CustomerIdentifier = customerIdentifier;
            Alias = alias;
        }

        internal override string Type()
        {
            return "External";
        }
    }


}
