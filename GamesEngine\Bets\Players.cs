﻿using GamesEngine.Games;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GamesEngine.Bets
{
    internal class Players: Objeto
    {
        private readonly Dictionary<string, Player> players = new Dictionary<string, Player>();
        private int lastPlayerConsecutive = 0;

        internal void Add(Player player)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));

            players.Add(player.Id, player);
            lastPlayerConsecutive = player.Consecutive;
        }

        internal IEnumerable<Player> GetAll
        {
            get
            {
                return players.Values;
            }
        }

        internal int Count
		{
			get
			{
                return this.players.Count;
			}
		}

        internal IEnumerable<Player> WithAvatarsToBeApproved(string gameName, int initialIndex, int finalIndex)
        {
            if (string.IsNullOrWhiteSpace(gameName)) throw new ArgumentNullException(nameof(gameName));

            var selectedAmount = finalIndex - initialIndex;
            var playersWithAvatarsToBeApproved = GetAll.Where(item => item.HasAvatar() && item.Avatar.IsToBeApproved && item.Avatar.GameName == gameName)
                .Skip(initialIndex)
                .Take(selectedAmount).ToList();
            return playersWithAvatarsToBeApproved;
        }

        internal int CountAvatarsToBeApproved(string gameName)
        {
            if (string.IsNullOrWhiteSpace(gameName)) throw new ArgumentNullException(nameof(gameName));

            var result = GetAll.Count(item => item.HasAvatar() && item.Avatar.IsToBeApproved && item.Avatar.GameName == gameName);
            return result;
        }

        internal IEnumerable<Player> WithThisIds(string listOdPlayersIdsSerializated)
        {
            if (String.IsNullOrEmpty(listOdPlayersIdsSerializated)) throw new ArgumentException(nameof(listOdPlayersIdsSerializated));

            List<string> playersAlreadyChecked = new List<string>();

            string[] listOfPlayersIds = listOdPlayersIdsSerializated.Split(",");
            List<Player> result = new List<Player>();
            foreach (string playerId in listOfPlayersIds)
            {
                if (playersAlreadyChecked.Contains(playerId)) continue;

                players.TryGetValue(playerId, out Player playerFound);
                if (playerFound == null) throw new GameEngineException($"Player with ID {playerId} does not exist");
                result.Add(playerFound);

                playersAlreadyChecked.Add(playerId);
            }
            return result;
        }

        internal void Forget(Game game)
        {
            if (game == null) throw new ArgumentNullException(nameof(game));

            foreach (var player in GetAll)
            {
                player.Preferences.GamesReminder.Remove(game);
            }
        }

        internal int NextConsecutive()
        {
            return lastPlayerConsecutive + 1;
        }

        internal Player SearchPlayer(string playerId)
        {
            if (string.IsNullOrWhiteSpace(playerId)) throw new ArgumentNullException("The player id is required.");
            players.TryGetValue(playerId, out Player playerFound);
            if (playerFound == null) throw new GameEngineException($"Player with ID {playerId} does not exist");
            return playerFound;
        }
    }
}
