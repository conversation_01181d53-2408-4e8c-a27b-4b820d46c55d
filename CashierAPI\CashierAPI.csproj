﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="wwwroot\**" />
    <Content Remove="wwwroot\**" />
    <EmbeddedResource Remove="wwwroot\**" />
    <None Remove="wwwroot\**" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="..\ExchangeAPI\Controllers\CatalogConsumer.cs" Link="Controllers\CatalogConsumer.cs" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Elastic.Apm.NetCoreAll" Version="1.8.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="3.1.7" />
    <PackageReference Include="Microsoft.AspNetCore.Http" Version="2.2.2" />
    <PackageReference Include="Microsoft.AspNetCore.HttpOverrides" Version="2.1.1" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="3.1.7" />
    <PackageReference Include="Microsoft.UnitTestFramework.Extensions" Version="2.0.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.14.0" />
    <PackageReference Include="Microsoft.VisualStudio.Threading.Analyzers" Version="16.4.43" />
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="2.0.4" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="5.5.0" />
  </ItemGroup>
  <ItemGroup>
    <DotNetCliToolReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Tools" Version="2.0.3" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\GamesEngineMocks\GamesEngineMocks.csproj" />
    <ProjectReference Include="..\GamesEngine\GamesEngine.csproj" />
  </ItemGroup>
</Project>