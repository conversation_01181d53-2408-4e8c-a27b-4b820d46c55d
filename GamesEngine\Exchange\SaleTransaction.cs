﻿using GamesEngine.Accounting.JournalTemplates;
using GamesEngine.Exchange.Batch;
using GamesEngine.Finance;
using GamesEngine.Settings;
using System;
using town.connectors.drivers;

namespace GamesEngine.Exchange
{
	internal class SaleTransaction : Transaction
	{
		internal const int TEMPLATE_ID = 5;

		internal SaleTransaction(TransactionDefinition transactionDefinition, IConversionSpread conversionSpread, CustomerAccount account, string employeeName, Currency amount, DateTime now)
	: base(transactionDefinition, conversionSpread, amount, now, TransactionType.Sale)
		{
			transactionDefinition.Batch.AddTransactionDenifition(this);
		}

		public static JournalEntryTemplate Template { get; private set; }
		internal static void InitializeTemplate(JournalEntryTemplates templates)
		{
			Template = templates.CreateTemplate(SaleTransaction.TEMPLATE_ID, "Template for " + nameof(SaleTransaction));
			var parameters = Template.Parameters;

			parameters.AddTextParameter("TransactionId");
			parameters.AddTextParameter("Date");
			parameters.AddTextParameter("Currency");
			parameters.AddAmountParameter("SaleRate");
			parameters.AddAmountParameter("PurchaseRate");
			parameters.AddAmountParameter("TotalAmount");

			Template.AddDebit("100-01", "Dummy Sale Transaction {TransactionId}", "TotalAmount");
			Template.AddCredit("200-01", "Dummy Sale Transaction {TransactionId}", "TotalAmount");
		}

		protected override void AfterApprove(DateTime date, bool itsThePresent, Currency gross, Currency comission, Currency profit, Currency net, Currency amountToCustomer, string employeeName, TransactionCompleted transactionCompleted, int journalEntryNumber)
		{
			if (Integration.UseKafka || Integration.UseKafkaForAuto)
			{
				if (base.JournalEntryTemplate.IsActive)//TODO: no specific template for this transaction type
				{
					var saleRate = ConversionSpread is NoConversionSpread ? 0m : ConversionSpread.SaleRate.Price;
					var purchaseRate = ConversionSpread is NoConversionSpread ? 0m : ConversionSpread.PurchaseRate.Price;
					Integration.Kafka.Send(
						itsThePresent,
						$"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForTransacctions}",
						new JournalEntryForSaleMessage(
							base.Id,
							date,
							journalEntryNumber,
							employeeName,
							saleRate,
							purchaseRate,
							TransactionDefinition.Account.Coin,
							amountToCustomer.Value
						)
					);
				}
			}
		}

		internal override void AfterDeny(DateTime date, bool itsThePresent, string employeeName, string reason)
		{
		}

		internal override bool isSameCurrencyTransaction()
		{
			return TransactionDefinition.Account.CurrencyCode == Amount.CurrencyCode;
		}

		protected sealed override int TemplateNumber
		{
			get
			{
				return TEMPLATE_ID;
			}
		}
	}

	public class JournalEntryForSaleMessage : JournalEntryMessage
	{
		public Coin Coin { get; set; }
		public string CurrencyCode { get { return Coin.Iso4217Code; } }
		public decimal TotalAmount { get; set; }

		public JournalEntryForSaleMessage(int transactionId, DateTime date, int journalEntryId, string employeeName, decimal saleRate, decimal purchaseRate, Coin currencyCode, decimal totalAmount) : 
			base(TransactionType.Sale, transactionId, date, journalEntryId, employeeName, saleRate, purchaseRate)
		{
			this.Coin = currencyCode;
			this.TotalAmount = totalAmount;
		}

		public JournalEntryForSaleMessage(string serialized) : base(serialized)
		{
			
		}

		protected override void InternalSerialize()
		{
			base.InternalSerialize();
			AddProperty(CurrencyCode).
			AddProperty(TotalAmount);
		}

		protected override void Deserialize(string [] message, out int fieldOrder)
		{
			base.Deserialize(message, out fieldOrder);
			Coin = Coinage.KafkaProperty2Coin(message[fieldOrder++]);
			TotalAmount = decimal.Parse(message[fieldOrder++]);
		}
	}
}
