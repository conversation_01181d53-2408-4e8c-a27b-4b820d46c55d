﻿using GamesEngine.Custodian;
using GamesEngine.Finance;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamesEngine.Exchange
{
    abstract class User : Objeto
    {
        internal string Name { get; set; }

        internal bool HasProfile => Profile != null;
        internal Profile Profile { get; set; }

        public User(string name)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

            Name = name;
        }

        private AgentAmountRanges agentAmountRanges = new AgentAmountRanges();
        internal void SetAmountRanges(AgentAmountRanges agentAmountRanges)
        {
            if (agentAmountRanges == null) throw new ArgumentNullException(nameof(agentAmountRanges));

            this.agentAmountRanges = agentAmountRanges;
        }

        internal void CopyAmountRangesFrom(User user, DateTime now, string employeeName)
        {
            if (user == null) throw new ArgumentNullException(nameof(user));
            if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));
            if (user.agentAmountRanges.IsEmpty()) throw new GameEngineException($"{nameof(agentAmountRanges)} to copy is empty.");

            agentAmountRanges.Clear();
            foreach (var amountRangeByCurrencies in user.agentAmountRanges.ListAmountRangesByRiskRating())
            {
                var transactionTypes = amountRangeByCurrencies.ListTransactionTypes().ToList();
                var newAmountRangeByCurrencies = agentAmountRanges.CreateAmountRangeByCurrencies(amountRangeByCurrencies.RiskRating, transactionTypes);
                foreach (var amountRange in amountRangeByCurrencies.ListAmountRanges())
                {
                    newAmountRangeByCurrencies.SetAmountRange(amountRange.MaxAmount, amountRange.MinAmount, now, employeeName);
                }
            }
        }

        internal IEnumerable<AmountRangeByCurrencies> ListAmountRangesByRiskRating()
        {
            return agentAmountRanges.ListAmountRangesByRiskRating();
        }

        internal bool IsInRange(Currency amount)
        {
            if (amount == null) throw new ArgumentNullException(nameof(amount));
            if (amount.Value < 0) throw new GameEngineException($"{nameof(amount)} must be greater or equal than 0");

            return agentAmountRanges.IsInRange(amount);
        }

        internal void CopyFrom(User user)
        {
            if (user == null) throw new ArgumentNullException(nameof(user));

            Name = user.Name;
            Profile = user.Profile;
            SetAmountRanges(user.agentAmountRanges);
        }
    }

    internal class AgentAmountRanges : AmountRanges { }

    [Puppet]
    internal class AmountRanges : Objeto
    {
        private readonly List<AmountRangeByCurrencies> amountRangesByRiskRatings = new List<AmountRangeByCurrencies>();

        internal IEnumerable<AmountRangeByCurrencies> ListAmountRangesByRiskRating()
        {
            return amountRangesByRiskRatings;
        }

        internal AmountRangeByCurrencies CreateAmountRangeByCurrencies(RiskRating riskRating, List<string> transactionTypes)
        {
            if (riskRating == null) throw new ArgumentNullException(nameof(riskRating));
            if (transactionTypes == null || transactionTypes.Count == 0) throw new GameEngineException($"{nameof(transactionTypes)} cannot be empty");

            var amountRangeByCurrencies = new AmountRangeByCurrencies(riskRating, transactionTypes);
            amountRangesByRiskRatings.Add(amountRangeByCurrencies);
            return amountRangeByCurrencies;
        }

        internal void Clear()
        {
            amountRangesByRiskRatings.Clear();
        }

        internal bool IsEmpty()
        {
            return amountRangesByRiskRatings.Count == 0;
        }

        internal bool IsInRange(Currency amount)
        {
            if (amount == null) throw new ArgumentNullException(nameof(amount));
            if (amount.Value < 0) throw new GameEngineException($"{nameof(amount)} must be greater or equal than 0");

            foreach (var amountRangesByCurrencies in amountRangesByRiskRatings)
            {
                if (amountRangesByCurrencies.IsInRange(amount)) return true;
            }
            return false;
        }
    }

    class Manager : User
    {
        public Manager(string name):base(name)
        {

        }
    }

    class Cashier : User
    {
        public Cashier(string name) : base(name)
        {

        }
    }
}
