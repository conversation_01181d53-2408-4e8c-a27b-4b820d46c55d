﻿using Cassandra.Data.Linq;
using GamesEngine.Accounting.JournalTemplates;
using GamesEngine.Business;
using GamesEngine.Custodian;
using GamesEngine.Domains;
using GamesEngine.Exchange.Batch;
using GamesEngine.Finance;
using GamesEngine.Marketing.Campaigns;
using GamesEngine.Marketing.Messaging;
using GamesEngine.Messaging;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using town.connectors.drivers;
using static GamesEngine.Business.WholePaymentProcessor;
using static GamesEngine.Exchange.Batch.BatchSet;
using static GamesEngine.Exchange.FloatingExchangeRate;
using static GamesEngine.Finance.Currencies;

namespace GamesEngine.Exchange
{
	[Puppet]
	internal class Marketplace : Objeto, Subscriber
	{
		private readonly ExchangeRates rates;
		private readonly Country country;
		private readonly Company company;
		private readonly MarketplaceBatches batches;
		private int transationIdConsecutive = 0;
		private int conversionSpreadConsecutive = 0;

		private int accountsCounter = 0;
		private Dictionary<string, Exchange.CustomerAccount> accountsIndex = new Dictionary<string, Exchange.CustomerAccount>();
		private MarketplaceAgent user;
		private Settings settings;
		private readonly JournalEntryTemplates journalEntryTemplates;
		private readonly string subscriberId;
		internal UserTypes UserTypes { get; }

		internal PaymentProcessorsAndActionsByDomains PaymentProcessors()
		{
			PaymentProcessorsAndActionsByDomains PaymentProcessorsAndActionsByDomains = Company.System.PaymentProcessor.PaymentProcessorsAndActionsByDomains;
			return PaymentProcessorsAndActionsByDomains;
		}

		private TransactionOutcomes transactionOutcomes;
		internal TransactionOutcomes TransactionOutcomes()
		{
			if (transactionOutcomes == null)
			{
				transactionOutcomes = new TransactionOutcomes(this, journalEntryTemplates);
			}
			return transactionOutcomes;
		}

		internal Marketplace(Company company, string countryName)
		{
			this.company = company;
			this.country = new Country(countryName);
			rates = new ExchangeRates(this);
			batches = new MarketplaceBatches(this);
			user = new MarketplaceAgent(this);
			this.settings = new Settings(this);

			messages = new Messages(company.Campaigns, this);
			journalEntryTemplates = new JournalEntryTemplates();
			InitializeTransactionsTemplates();
			this.subscriberId = nameof(Marketplace) + company.Id + country.CountryName;
			UserTypes = new UserTypes();
		}

		internal Company Company
		{
			get
			{
				return this.company;
			}
		}

		private void InitializeTransactionsTemplates()
        {
			DepositTransaction.InitializeTemplate(journalEntryTemplates);
			WithdrawalTransaction.InitializeTemplate(journalEntryTemplates);
			TransferTransaction.InitializeTemplate(journalEntryTemplates);
			SaleTransaction.InitializeTemplate(journalEntryTemplates);
			DebitNoteTransaction.InitializeTemplate(journalEntryTemplates);
			CreditNoteTransaction.InitializeTemplate(journalEntryTemplates);
		}

		internal RegularConversionSpread CreateConversionSpread(int id, DateTime today, bool itIsThePresent, string source, string target, decimal salePrice, decimal purchasePrice, string employeeName)
		{
			return CreateConversionSpread(id, today, itIsThePresent, Coinage.Coin(source), Coinage.Coin(target), salePrice, purchasePrice, employeeName);
		}

		internal RegularConversionSpread CreateConversionSpread(int id, DateTime today, bool itIsThePresent, Coin source, Coin target, decimal salePrice, decimal purchasePrice, string employeeName)
		{
			if (today == default(DateTime)) throw new ArgumentNullException(nameof(today));
			if (salePrice <= 0) throw new ArgumentNullException(nameof(salePrice));
			if (purchasePrice <= 0) throw new ArgumentNullException(nameof(purchasePrice));

			SaleRate saleRate = FloatingExchangeRate.CreateSaleRate<SaleRate>(rates, today, source, target, salePrice);
			PurchaseRate purchaseRate = FloatingExchangeRate.CreatePurchaseRate<PurchaseRate>(rates, today, source, target, purchasePrice);

			conversionSpreadConsecutive = id;
			RegularConversionSpread rate = rates.Add(conversionSpreadConsecutive, saleRate, purchaseRate);

			if ((itIsThePresent && Integration.UseKafka) || Integration.UseKafkaForAuto)
			{
				Integration.Kafka.Send(
					itIsThePresent,
					$"{KafkaMessage.CONVERSION_SPREAD_EXCHANGE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForTransacctions}",
					new ConversionSpreadMessage(conversionSpreadConsecutive, today, source, target, purchasePrice, salePrice, employeeName)
				);
			}
			
			return rate;
		}
		internal RegularConversionSpread CreateANewConversionSpread(DateTime today, Coin source, Coin target, decimal salePrice, decimal purchasePrice)
		{
			if (salePrice < 0) throw new ArgumentNullException(nameof(salePrice));
			if (purchasePrice < 0) throw new ArgumentNullException(nameof(purchasePrice));

			SaleRate saleRate = FloatingExchangeRate.CreateSaleRate<SaleRate>(rates, today, source, target, salePrice);
			PurchaseRate purchaseRate = FloatingExchangeRate.CreatePurchaseRate<PurchaseRate>(rates, today, source, target, purchasePrice);

			return new RegularConversionSpread(0, rates, saleRate, purchaseRate);
		}

		internal RateResult SearchRate(DateTime today, string source, string target)
		{
			return SearchRate(today, Coinage.Coin(source), Coinage.Coin(target));
		}
		internal RateResult SearchRate(DateTime today, Coin source, Coin target)
		{
			IConversionSpread rate = rates.At(today, source, target);

			return rate.GenerateRateResult();
		}

		internal bool ExistsRate(DateTime today, string source, string target)
		{
			IConversionSpread rate = rates.At(today, Coinage.Coin(source), Coinage.Coin(target));
			return !(rate is NoConversionSpread);
		}

		internal Currency Convert(DateTime today, string source, string target, decimal amount)
		{
			return Convert(today, Coinage.Coin(source), Coinage.Coin(target), amount);
		}
		internal Currency Convert(DateTime today, Coin source, Coin target, decimal amount)
		{
			Currency gross = null;
			Currency comission = null;
			Currency net = null;

			IConversionSpread rate = rates.At(today, source, target);

			Currency result = rate.Sale(Currency.Factory(source, amount), out gross, out comission, out net);
			return result;
		}

		internal IConversionSpread ConversionSpreadAt(DateTime date, Coin source, Coin target)
		{
			return rates.At(date, source, target);
		}

		internal IConversionSpread SearchSpreadFor(DateTime today, Coin source, Coin target)
		{
			return rates.SearchSpreadFor(today, source, target);
		}

		internal TransactionCompleted Approve(DateTime date, bool itsThePresent, Transaction transaction, int authorizationId, string employeeName)
		{
			if (transaction == null) throw new ArgumentNullException(nameof(transaction));
			var journalEntryNumber = NewJournalEntryNumber();
			TransactionCompleted tc = transaction.Approve(date, itsThePresent, employeeName, journalEntryNumber);

			return tc;
		}

		internal TransactionDefinition From(int transactionNumber, CustomerAccount account, string agentPath, string userName, Domains.Domain domain)
		{
			if (transactionNumber == 0) throw new GameEngineException($"The id it's generated with method {nameof(NewTransationNumber)} ");

            BatchTransactions batch = null;
            var fullPath = agentPath + '/' + userName;
			if (batches.ExistAgentBatch(fullPath))
			{
				BatchSet agent = batches.SearchAgentBatch(fullPath);
				batch = agent.BatchTransactions;
			}
			
			return From(transactionNumber, account, batch, agentPath, domain);
		}

        internal TransactionDefinition From(int transactionNumber, CustomerAccount account, string agentPath, Domain domain)
        {
            if (transactionNumber == 0) throw new GameEngineException($"The id it's generated with method {nameof(NewTransationNumber)} ");

            BatchTransactions batch = null;
            //if (batches.ExistAgentBatch(agentPath))
            {
                BatchSet agent = batches.SearchAgentBatch(agentPath);
                batch = agent.BatchTransactions;
            }

            return From(transactionNumber, account, batch, agentPath, domain);
        }

        internal TransactionDefinition From(int transactionNumber, CustomerAccount account, BatchSet batch, string agentPath, Domains.Domain domain)
		{
			if (transactionNumber == 0) throw new GameEngineException($"The id it's generated with method {nameof(NewTransationNumber)} ");
			if (account == null) throw new ArgumentNullException(nameof(account));
			if (batch == null) throw new ArgumentNullException(nameof(batch));

			transationIdConsecutive = transactionNumber;
			return new TransactionDefinition(transactionNumber, this, account, batch.BatchTransactions, agentPath, domain);
		}

		private TransactionDefinition From(int transactionNumber, CustomerAccount account, BatchTransactions batch, string agentPath, Domains.Domain domain)
		{
			if (transactionNumber == 0) throw new GameEngineException($"The id it's generated with method {nameof(NewTransationNumber)} ");
			if (account == null) throw new ArgumentNullException(nameof(account));
			if (batch == null) throw new ArgumentNullException(nameof(batch));
			if (domain == null) throw new ArgumentNullException(nameof(domain));

			transationIdConsecutive = transactionNumber;
			return new TransactionDefinition(transactionNumber, this, account, batch, agentPath, domain);
		}

		internal TransactionDefinition SearchTemporaryDefinition(int transactionNumber, string agentPath, string userName)
		{
			if (transactionNumber == 0) throw new GameEngineException($"The id it's generated with method {nameof(NewTransationNumber)} ");
			if (string.IsNullOrWhiteSpace(agentPath)) throw new ArgumentNullException(nameof(agentPath));
			if (string.IsNullOrWhiteSpace(userName)) throw new ArgumentNullException(nameof(userName));

            BatchTransactions batch = null;
            var fullPath = agentPath + '/' + userName;
			if (batches.ExistAgentBatch(fullPath))
			{
				BatchSet agent = batches.SearchAgentBatch(fullPath);
				batch = agent.BatchTransactions;
				return batch.SearchTemporaryDefinition(transactionNumber);
			}
			throw new GameEngineException($"There is no batch for path {fullPath} ");
		}

        internal TransactionDefinition SearchTemporaryDefinition(int transactionNumber, string agentPath)
        {
            if (transactionNumber == 0) throw new GameEngineException($"The id it's generated with method {nameof(NewTransationNumber)} ");
            if (string.IsNullOrWhiteSpace(agentPath)) throw new ArgumentNullException(nameof(agentPath));

			BatchTransactions batch = null;
            if (batches.ExistAgentBatch(agentPath))
            {
                BatchSet agent = batches.SearchAgentBatch(agentPath);
                batch = agent.BatchTransactions;
                return batch.SearchTemporaryDefinition(transactionNumber);
            }
            throw new GameEngineException($"There is no batch for path {agentPath} ");
        }

        internal int NewTransationNumber()
		{
			transationIdConsecutive += 1;
			return transationIdConsecutive;
		}

		internal int NewConversionSpreadNumber()
		{
			conversionSpreadConsecutive += 1;
			return conversionSpreadConsecutive;
		}

		internal int NewJournalEntryNumber()
		{
			var consecutive = journalEntryTemplates.NewJournalEntryNumber();
			return consecutive;
		}

		internal void SetJournalEntryId(int consecutive)
		{
			journalEntryTemplates.JournalEntryIdConsecutive = consecutive;
		}

		internal MarketplaceBatch AddNewBatch(DateTime creationDate, DateTime scheduledDate, int batchNumber, string employeeName, string description)
		{
			if (creationDate == default(DateTime)) throw new ArgumentNullException(nameof(creationDate));
			if (scheduledDate == default(DateTime)) throw new ArgumentNullException(nameof(scheduledDate));
			if (creationDate.Date > scheduledDate.Date) throw new GameEngineException($"Batch cannot be scheduled for previous days. Creation date is {creationDate} and scheduled date is {scheduledDate}");
			if (batchNumber <= 0) throw new GameEngineException($"Batch number {batchNumber} is invalid to define a Marketplace batch.");
			if (string.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));

			batches.IdentityBatchNumber = batchNumber;
			MarketplaceBatch result = batches.AddNewBatch(creationDate, scheduledDate, batchNumber, employeeName, description);
			return result;
		}

		internal MarketplaceBatch FindMarketplaceBatch(int batchNumber)
		{
			if (batchNumber <= 0) throw new GameEngineException($"A marketplace with number {batchNumber} does not exist");

			MarketplaceBatch batch = this.batches.FindMarketplaceBatch(batchNumber);
			return batch;
		}

		internal BatchSet SearchAgentBatch(string fullName, int batchNumber)
		{
			if (string.IsNullOrWhiteSpace(fullName)) throw new ArgumentNullException(nameof(fullName));

			BatchSet result = this.batches.SearchAgentBatch(fullName, batchNumber);
			return result;
		}

		internal BatchSet SearchAgentBatch(string fullName)
		{
			if (string.IsNullOrWhiteSpace(fullName)) throw new ArgumentNullException(nameof(fullName));

			#region Esto es solo para poner las trasancciones de blocks sin que den problemas.
			const string BATCH_FOR_CUSTOMER_APP = "CR/blocks/blocks";
			if (fullName == BATCH_FOR_CUSTOMER_APP && !batches.ExistAgentBatch(fullName))
			{
				var today = DateTime.Now;
				int batchNumber;

				MarketplaceBatch marcketPlaceBatch = batches.FirstOpenBatch();
				if (marcketPlaceBatch == null)
				{
					batchNumber = Batches.IdentityBatchNumber + 99;
					marcketPlaceBatch = Batches.AddNewBatch(DateTime.Now, DateTime.Now, batchNumber, "Blocks", "Batch para el 05/22/2020");
				}
				else
				{
					batchNumber = marcketPlaceBatch.BatchNumber;
				}
				marcketPlaceBatch.InitialAmount(new Dollar(1000000), "Exchange Owner");
				marcketPlaceBatch.InitialAmount(new Btc(10000), "Exchange Owner");
				if(marcketPlaceBatch.Status == BatchStatus.READY) marcketPlaceBatch.Open(true, DateTime.Now, "N/A");

				var blocksMiddleNode = marcketPlaceBatch.AddAgent("blocks", today);
				blocksMiddleNode.ReceiveAmount(new Dollar(5000000), new Dollar(5000000), "N/A");
				blocksMiddleNode.ReceiveAmount(new Btc(10000), new Dollar(100000000), "N/A");
				blocksMiddleNode.Open(true, today, "N/A");

				AgentBatch blockEndNode = blocksMiddleNode.AddAgent("blocks", today);
				blocksMiddleNode.AssignFunds("blocks", marcketPlaceBatch.BatchNumber, new Dollar(1000000), "N/A", today);
				blocksMiddleNode.AssignFunds("blocks", marcketPlaceBatch.BatchNumber, new Btc(10000), "N/A", today);
				blockEndNode.Open(true, today, "N/A");
						
				return this.batches.SearchAgentBatch(fullName, batchNumber);
			}
			#endregion


			BatchSet result = this.batches.SearchAgentBatch(fullName );
			return result;
		}

		internal MarketplaceBatches Batches
		{
			get
			{
				return this.batches;
			}
		}

		internal CustomerAccount AccountByNumber(string accountNumber)
		{
			if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException("The accountNumber is required.");

			CustomerAccount result = null;
			accountsIndex.TryGetValue(accountNumber, out result);

			if (result == null) throw new GameEngineException($"The is no an account for number {accountNumber}.");

			return result;
		}

		internal bool AccountAlreadyExists(string accountNumber)
		{
			if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException("The accountNumber is required.");

			return accountsIndex.ContainsKey(accountNumber);
		}

		internal int NextAccountConsecutive()
		{
			return accountsCounter + 1;
		}

		internal void RegisterNewAccount(int accountConsecutive, Exchange.CustomerAccount account)
		{
			this.accountsCounter = accountConsecutive;
			string accountNumberWithFormat = accountsCounter.ToString("0000");
			string currencyFormat = ((int)account.Coin.Id).ToString("0000");

			string number = $"{country.CountryName}{accountNumberWithFormat}{currencyFormat}".ToLower();
			account.Identificator = number;
			accountsIndex.Add(account.Identificator, account);
		}

		internal ExchangeUser AddAgent(string name)
		{
			if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

			return this.user.AddAgent(name);
		}

		internal ExchangeUser SearchAgent(string fullName)
		{
			if (string.IsNullOrWhiteSpace(fullName)) throw new ArgumentNullException(nameof(fullName));

			string tail = string.Empty;
			string agentName;
			int indexSeparator = fullName.IndexOf('/');
			if (indexSeparator < 0)
			{
				agentName = fullName;
				if (agentName != this.user.Name) throw new GameEngineException($"Marketplace agent {agentName} does not exist");
				return user;
			}
            else
            {
				agentName = fullName.Substring(0, indexSeparator);
				tail = fullName.Substring(indexSeparator + 1);
				if (agentName != this.user.Name) throw new GameEngineException($"Marketplace agent {agentName} does not exist");
			}
			ExchangeUser result = this.user.SearchAgent(tail);
			return result;
		}
		
		internal ExchangeUser SearchAgentInFirstLevel(string fullName)
		{
			if (string.IsNullOrWhiteSpace(fullName)) throw new ArgumentNullException(nameof(fullName));

			string agentName;
			int indexSeparator = fullName.IndexOf('/');
			if (indexSeparator < 0)
			{
				agentName = fullName;
				if (user.HasAgent(agentName)) return user.SearchAgent(agentName);
				throw new GameEngineException($"Marketplace agent {agentName} does not exist");
			}
			else
			{
				agentName = fullName.Substring(0, indexSeparator);
				var tail = fullName.Substring(indexSeparator + 1);
				return user.SearchAgent(tail);
			}
		}
		
		internal bool ExistsAgent(string fullName)
		{
			if (string.IsNullOrWhiteSpace(fullName)) return false;

			int indexSeparator = fullName.IndexOf('/');
			string agentName = fullName.Substring(0, indexSeparator);
			string tail = fullName.Substring(indexSeparator + 1);
			if (agentName != this.user.Name) return false;

			var result = this.user.ExistsAgent(tail);
			return result;
		}

		internal IEnumerable<ExchangeUser> AllAgents()
		{
			List<ExchangeUser> result = new List<ExchangeUser>();
			result.AddRange(this.user.AllAgents());
			return result;
		}

		internal string Name
		{
			get
			{
				return this.country.CountryName;
			}
		}

		internal SetOfBalances RealAccounts
		{ 
			get
			{
				if (settings.RealAccounts == null) throw new GameEngineException($"Set {nameof(settings.RealAccounts)} first.");
				return settings.RealAccounts;
			}
			set
			{
				if (value == null) throw new GameEngineException($" {nameof(value)} can not be null.");

				settings.RealAccounts = value;
			}
		}

		private struct Country
		{
			private readonly string countryName;

			internal Country(string name)
			{
				if (string.IsNullOrEmpty(name)) throw new GameEngineException(nameof(name));
				this.countryName = name;
			}

			internal string CountryName
			{
				get
				{
					return this.countryName;
				}
			}
		}

		internal Transaction FindDraftTransaction(int id, string agentPath, string userName)
		{
			if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");
			if (string.IsNullOrWhiteSpace(agentPath)) throw new ArgumentNullException(nameof(agentPath));
			if (string.IsNullOrWhiteSpace(userName)) throw new ArgumentNullException(nameof(userName));

			BatchTransactions batch = null;
			var fullPath = agentPath + '/' + userName;
			if (batches.ExistAgentBatch(fullPath))
			{
				BatchSet agent = batches.SearchAgentBatch(fullPath);
				batch = agent.BatchTransactions;
                if (ExistsDraftTransaction(batch, id)) return FindDraftTransaction(batch, id);
            }

            fullPath = "CR/Cartago/FW";
            if (batches.ExistAgentBatch(fullPath))
            {
                BatchSet agent = batches.SearchAgentBatch(fullPath);
                batch = agent.BatchTransactions;
            }
            return FindDraftTransaction(batch, id);
		}

		internal Transaction FindDraftTransaction(BatchTransactions batch, int id)
		{
			return batch.FindDraftTransaction(id);
		}

		internal bool ExistsDraftTransaction(int id, string agentPath, string userName)
		{
            BatchTransactions batch = null;
            var fullPath = agentPath + '/' + userName;
            if (batches.ExistAgentBatch(fullPath))
            {
                BatchSet agent = batches.SearchAgentBatch(fullPath);
                batch = agent.BatchTransactions;
                if (ExistsDraftTransaction(batch, id)) return true;
            }

            fullPath = "CR/Cartago/FW";
            if (batches.ExistAgentBatch(fullPath))
            {
                BatchSet agent = batches.SearchAgentBatch(fullPath);
                batch = agent.BatchTransactions;
                return ExistsDraftTransaction(batch, id);
            }
			return false;
        }

		internal bool ExistsDraftTransaction(BatchTransactions batch, int id)
		{
			return batch.ExistsDraftTransaction(id);
		}
		internal int AmountOfRatesInMemory()
		{
			return rates.AmountOfRatesInMemory();
		}
		internal void PruneUnUsedRates()
		{
			rates.PruneUnUsedRates();
		}

		internal bool InitialAccountItsNotConfiguredFor(Coin code)
		{
			return settings.InitialAccountItsNotConfiguredFor( code);
		}

		internal bool AreAllWithdrawalsManagedAsDisbursements 
		{ 
			get 
			{
				return settings.AreAllWithdrawalsManagedAsDisbursements;
			}
			set
			{
				settings.AreAllWithdrawalsManagedAsDisbursements = value;
			}
		}

		internal Customer FindOwner(TransactionDefinition transaction)
        {
			var identificationNumber = transaction.Account.CustomerIdentifier;
			var customer = company.CustomerByIdentifier(identificationNumber);
			return customer;
		}

		string Subscriber.Id => this.subscriberId;

		private readonly Messages messages;
		Messages Subscriber.Messages => messages;
		internal Messages Messages
		{
			get
			{
				return messages;
			}
		}

		internal JournalEntryTemplates JournalEntryTemplates
		{
			get
			{
				return journalEntryTemplates;
			}
		}
	}
	
	internal sealed class RateResult : Objeto
	{
		internal RateResult(bool exists, long id, string rate, string saleRateBaseCurrency, string saleRateQouteCurrency, decimal saleRatePrice)
		{
			Exists = exists;
			int parsedId;
			if (!int.TryParse(id.ToString(), out parsedId)) throw new GameEngineException($"{nameof(id)} {id} is too big. It cannot cast in int");
			Id = parsedId;

			Rate = rate;
			SaleRateBaseCurrency = saleRateBaseCurrency;
			SaleRateQouteCurrency = saleRateQouteCurrency;
			SaleRatePrice = saleRatePrice;
		}

		internal bool Exists { get; }
		internal int Id { get; }
		internal string Rate { get; }
		internal string SaleRateBaseCurrency { get; }
		internal string SaleRateQouteCurrency { get; }
		internal decimal SaleRatePrice { get; }
	}

	internal sealed class Settings
	{
		private readonly Marketplace marketplace;
		internal bool AreAllWithdrawalsManagedAsDisbursements { get; set; }

		internal Settings(Marketplace marketplace)
		{
			this.marketplace = marketplace;
		}

		internal SetOfBalances RealAccounts { get; set; }

		internal bool InitialAccountItsNotConfiguredFor(Coin code)
		{
			return RealAccounts == null || RealAccounts.ItsConfiguredNotFor(code);
		}

	}

	[Puppet]
	internal class AllowedTransactions : Objeto
	{
		private readonly Dictionary<TransactionType, bool> enabledPerTransacction = new Dictionary<TransactionType, bool>();
		internal AllowedTransactions()
		{
			foreach (TransactionType transactionType in (TransactionType[])Enum.GetValues(typeof(TransactionType)))
			{
				enabledPerTransacction.Add(transactionType, true);
			}
		}

		internal bool ItsEnabled(TransactionType transactionType)
		{
			return enabledPerTransacction[transactionType];
		}

		internal void Enable(TransactionType transactionType)
		{
			enabledPerTransacction[transactionType] = true;
		}
		internal void Disable(TransactionType transactionType)
		{
			enabledPerTransacction[transactionType] = false;
		}
		internal IEnumerable<AllowedTransactionsReport> List()
		{
			List<AllowedTransactionsReport> list = new List<AllowedTransactionsReport>();
			foreach (KeyValuePair<TransactionType, bool> pair in enabledPerTransacction )
			{
				list.Add(new AllowedTransactionsReport(pair.Key.ToString(), pair.Value));
			}

			return list;
		}

		[Puppet]
		internal class AllowedTransactionsReport : Objeto
		{

			internal AllowedTransactionsReport(string type, bool enabled)
			{
				Type = type;
				Enabled = enabled;
			}

			internal string Type { get; }
			internal bool Enabled { get; }
		}
	}

	public class ConversionSpreadMessage : KafkaMessage
	{
		public int Id { get; set; }
		public DateTime Date
		{
			get
			{
				return new DateTime(DateTicks);
			}
		}
		public long DateTicks { get; set; }
		public Coin FromCurrencyCode { get; set; }
		public Coin ToCurrencyCode { get; set; }
		public decimal PurchasePrice { get; set; }
		public decimal SalePrice { get; set; }
		public string EmployeeName { get; set; }

		internal ConversionSpreadMessage(int id, DateTime date, Coin fromCurrencyCode, Coin toCurrencyCode, decimal purchasePrice, decimal salePrice, string employeeName)
		{
			Id = id;
			DateTicks = date.Ticks;
			FromCurrencyCode = fromCurrencyCode;
			ToCurrencyCode = toCurrencyCode;
			PurchasePrice = purchasePrice;
			SalePrice = salePrice;
			EmployeeName = employeeName;
		}

		public ConversionSpreadMessage(string message) : base(message)
		{
		}

		protected override void Deserialize(string [] message, out int fieldOrder)
		{
			base.Deserialize(message, out fieldOrder);
			Id = int.Parse(message[fieldOrder++]);
			DateTicks = long.Parse(message[fieldOrder++]);
			FromCurrencyCode = Coinage.KafkaProperty2Coin(message[fieldOrder++]);
			ToCurrencyCode = Coinage.KafkaProperty2Coin(message[fieldOrder++]);
			PurchasePrice = decimal.Parse(message[fieldOrder++]);
			SalePrice = decimal.Parse(message[fieldOrder++]);
			EmployeeName = message[fieldOrder++];
		}

        protected override void InternalSerialize()
        {
			base.InternalSerialize();
			AddProperty(Id).
			AddProperty(DateTicks).
			AddProperty(FromCurrencyCode.Iso4217Code).
			AddProperty(ToCurrencyCode.Iso4217Code).
			AddProperty(PurchasePrice).
			AddProperty(SalePrice).
			AddProperty(EmployeeName);
		}
    }
}
