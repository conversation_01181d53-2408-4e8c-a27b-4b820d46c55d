﻿using ExchangeAPI.ExchangeEntities;
using ExchangeAPI.Model;
using ExchangeAPI.Service;
using GamesEngine;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ExchangeAPI.Logic
{
    public class Transactions
    {
        public static TransactionDTO LastTransactionFor(string identificationNumber)
        {
            if (string.IsNullOrWhiteSpace(identificationNumber)) throw new ArgumentNullException(nameof(identificationNumber));

            TransactionDTO transaction = null;
            using (var context = new ExchangeContext())
            {
                var lastDeposit = context.Deposit
                    .Include(deposit => deposit.WhocreatedNavigation)
                    .Where(deposit => ! deposit.Deleted && deposit.Tocustomer == identificationNumber)
                    .AsEnumerable()
                    .LastOrDefault();
                if (lastDeposit != null)
                {
                    transaction = lastDeposit.MapToTransactionDTO();
                }

                var lastWithdrawal = context.Withdrawal
                    .Include(withdrawal => withdrawal.WhocreatedNavigation)
                    .Where(withdrawal => ! withdrawal.Deleted && withdrawal.Fromcustomer == identificationNumber)
                    .AsEnumerable()
                    .LastOrDefault();
                if (lastWithdrawal != null && lastWithdrawal.Datecreated > transaction.Date)
                {
                    transaction = lastWithdrawal.MapToTransactionDTO();
                }

                var lastTransfer = context.Transfer
                    .Include(transfer => transfer.WhocreatedNavigation)
                    .Where(transfer => ! transfer.Deleted && transfer.Fromcustomer == identificationNumber)
                    .AsEnumerable()
                    .LastOrDefault();
                if (lastTransfer != null && lastTransfer.Datecreated > transaction.Date)
                {
                    transaction = lastTransfer.MapToTransactionDTO();
                }
                return transaction;
            }
        }

        private const string ALL_SELECTED = "all";
        private const int MAXIMUM_AMOUNT_OF_ROWS_ALLOWED = 100;

        public static HistoricalTransactionsDTO TransactionsPerDay(DateTime date, string identificationNumber, string currencyCode, string account, string state, int initialIndex, int amountOfRows)
        {
            if (date == default(DateTime)) throw new GameEngineException($"{nameof(date)} cannot have default value");
            if (date.Hour != 0 || date.Minute != 0 || date.Second != 0) throw new GameEngineException($"{nameof(date)} {date} is not valid");
            if (string.IsNullOrWhiteSpace(identificationNumber)) throw new ArgumentNullException(nameof(identificationNumber));
            if (string.IsNullOrWhiteSpace(account)) throw new ArgumentNullException(nameof(account));
            if (string.IsNullOrWhiteSpace(currencyCode)) throw new ArgumentNullException(nameof(currencyCode));
            if (string.IsNullOrWhiteSpace(state)) throw new ArgumentNullException(nameof(state));
            if (initialIndex < 0) throw new GameEngineException($"{nameof(initialIndex)} {initialIndex} is not valid");
            if (amountOfRows <= 0) throw new GameEngineException($"{nameof(amountOfRows)} {amountOfRows} is not valid");

            if (amountOfRows > MAXIMUM_AMOUNT_OF_ROWS_ALLOWED)
            {
                amountOfRows = MAXIMUM_AMOUNT_OF_ROWS_ALLOWED;
            }

            var allCurrencyCode = currencyCode.ToLower().Trim() == ALL_SELECTED;
            var allAccount = account.ToLower().Trim() == ALL_SELECTED;
            var allStatesSelected = state.ToLower().Trim() == ALL_SELECTED;

            using (var context = new ExchangeContext())
            {
                var deposits = context.Deposit
                    .Include(deposit => deposit.Fromcurrency)
                    .Include(deposit => deposit.Tocurrency)
                    .Where(deposit => !deposit.Deleted && deposit.Tocustomer == identificationNumber 
                    && deposit.Datecreated.Date == date
                    && (allCurrencyCode || deposit.Fromcurrency.Isocode == currencyCode || deposit.Tocurrency.Isocode == currencyCode)
                    && (allAccount || deposit.Account == account)
                    && (allStatesSelected || (deposit.Approvals == deposit.Approvalsrequired && state == TransactionState.Approved.ToString()) || (deposit.Rejections > 0 && state == TransactionState.Rejected.ToString()) || (deposit.Approvals == 0 && deposit.Rejections == 0 && state == TransactionState.Pending.ToString()))
                    )
                    .Select(deposit => deposit.MapToHistoricalTransactionDTO());

                var withdrawals = context.Withdrawal
                    .Include(withdrawal => withdrawal.Fromcurrency)
                    .Include(withdrawal => withdrawal.Tocurrency)
                    .Where(withdrawal => !withdrawal.Deleted && withdrawal.Fromcustomer == identificationNumber 
                    && withdrawal.Datecreated.Date == date
                    && (allCurrencyCode || withdrawal.Fromcurrency.Isocode == currencyCode || withdrawal.Tocurrency.Isocode == currencyCode)
                    && (allAccount || withdrawal.Account == account)
                    && (allStatesSelected || (withdrawal.Approvals == withdrawal.Approvalsrequired && state == TransactionState.Approved.ToString()) || (withdrawal.Rejections > 0 && state == TransactionState.Rejected.ToString()) || (withdrawal.Approvals == 0 && withdrawal.Rejections == 0 && state == TransactionState.Pending.ToString()))
                    )
                    .Select(withdrawal => withdrawal.MapToHistoricalTransactionDTO());

                var transfers = context.Transfer
                    .Include(transfer => transfer.Fromcurrency)
                    .Include(transfer => transfer.Tocurrency)
                    .Where(transfer => !transfer.Deleted && transfer.Fromcustomer == identificationNumber 
                    && transfer.Datecreated.Date == date
                    && (allCurrencyCode || transfer.Fromcurrency.Isocode == currencyCode || transfer.Tocurrency.Isocode == currencyCode)
                    && (allAccount || transfer.Account == account || transfer.Targetaccount == account)
                    && (allStatesSelected || (transfer.Approvals == transfer.Approvalsrequired && state == TransactionState.Approved.ToString()) || (transfer.Rejections > 0 && state == TransactionState.Rejected.ToString()) || (transfer.Approvals == 0 && transfer.Rejections == 0 && state == TransactionState.Pending.ToString()))
                    )
                    .Select(transfer => transfer.MapToHistoricalTransactionDTO(identificationNumber, currencyCode));

                var creditNotes = context.CreditNote
                    .Include(creditNote => creditNote.Currency)
                    .Where(creditNote => !creditNote.Deleted && creditNote.Customer == identificationNumber 
                    && creditNote.Datecreated.Date == date
                    && (allCurrencyCode || creditNote.Currency.Isocode == currencyCode)
                    && (allAccount || creditNote.Account == account)
                    && (allStatesSelected || (creditNote.Approvals == creditNote.Approvalsrequired && state == TransactionState.Approved.ToString()) || (creditNote.Rejections > 0 && state == TransactionState.Rejected.ToString()) || (creditNote.Approvals == 0 && creditNote.Rejections == 0 && state == TransactionState.Pending.ToString()))
                    )
                    .Select(creditNote => creditNote.MapToHistoricalTransactionDTO());
                
                var debitNotes = context.DebitNote
                    .Include(debitNote => debitNote.Currency)
                    .Where(debitNote => !debitNote.Deleted && debitNote.Customer == identificationNumber 
                    && debitNote.Datecreated.Date == date
                    && (allCurrencyCode || debitNote.Currency.Isocode == currencyCode)
                    && (allAccount || debitNote.Account == account)
                    && (allStatesSelected || (debitNote.Approvals == debitNote.Approvalsrequired && state == TransactionState.Approved.ToString()) || (debitNote.Rejections > 0 && state == TransactionState.Rejected.ToString()) || (debitNote.Approvals == 0 && debitNote.Rejections == 0 && state == TransactionState.Pending.ToString()))
                    )
                    .Select(debitNote => debitNote.MapToHistoricalTransactionDTO());

                var transactions = deposits.Take(amountOfRows).AsEnumerable()
                    .Concat(withdrawals.Take(amountOfRows).AsEnumerable())
                    .Concat(transfers.Take(amountOfRows).AsEnumerable())
                    .Concat(creditNotes.Take(amountOfRows).AsEnumerable())
                    .Concat(debitNotes.Take(amountOfRows).AsEnumerable())
                    .OrderByDescending(transaction => transaction.Date);

                var transactionsPaged = transactions
                    .Skip(initialIndex)
                    .Take(amountOfRows);
                var transactionsDTO = transactionsPaged.MapToHistoricalTransactionsDTO();
                transactionsDTO.TotalRecords = deposits.Count() + withdrawals.Count() + transfers.Count() + creditNotes.Count() + debitNotes.Count();
                return transactionsDTO;
            }
        }

        public static HistoricalTransactionsDTO LastTransactionsFor(string identificationNumber, int initialIndex, int amountOfRows)
        {
            if (string.IsNullOrWhiteSpace(identificationNumber)) throw new ArgumentNullException(nameof(identificationNumber));
            if (initialIndex < 0) throw new GameEngineException($"{nameof(initialIndex)} {initialIndex} is not valid");
            if (amountOfRows <= 0) throw new GameEngineException($"{nameof(amountOfRows)} {amountOfRows} is not valid");

            if (amountOfRows > MAXIMUM_AMOUNT_OF_ROWS_ALLOWED)
            {
                amountOfRows = MAXIMUM_AMOUNT_OF_ROWS_ALLOWED;
            }

            using (var context = new ExchangeContext())
            {
                var deposits = context.Deposit
                    .Include(deposit => deposit.Fromcurrency)
                    .Include(deposit => deposit.Tocurrency)
                    .Where(deposit => !deposit.Deleted && deposit.Tocustomer == identificationNumber)
                    .Select(deposit => deposit.MapToHistoricalTransactionDTO());

                var withdrawals = context.Withdrawal
                    .Include(withdrawal => withdrawal.Fromcurrency)
                    .Include(withdrawal => withdrawal.Tocurrency)
                    .Where(withdrawal => !withdrawal.Deleted && withdrawal.Fromcustomer == identificationNumber)
                    .Select(withdrawal => withdrawal.MapToHistoricalTransactionDTO());

                var transfers = context.Transfer
                    .Include(transfer => transfer.Fromcurrency)
                    .Include(transfer => transfer.Tocurrency)
                    .Where(transfer => !transfer.Deleted && transfer.Fromcustomer == identificationNumber)
                    .Select(transfer => transfer.MapToHistoricalTransactionDTO(identificationNumber, ALL_SELECTED));

                var creditNotes = context.CreditNote
                    .Include(creditNote => creditNote.Currency)
                    .Where(creditNote => !creditNote.Deleted && creditNote.Customer == identificationNumber)
                    .Select(creditNote => creditNote.MapToHistoricalTransactionDTO());

                var debitNotes = context.DebitNote
                    .Include(debitNote => debitNote.Currency)
                    .Where(debitNote => !debitNote.Deleted && debitNote.Customer == identificationNumber)
                    .Select(debitNote => debitNote.MapToHistoricalTransactionDTO());

                var transactions = deposits.Take(amountOfRows).AsEnumerable()
                    .Concat(withdrawals.Take(amountOfRows).AsEnumerable())
                    .Concat(transfers.Take(amountOfRows).AsEnumerable())
                    .Concat(creditNotes.Take(amountOfRows).AsEnumerable())
                    .Concat(debitNotes.Take(amountOfRows).AsEnumerable())
                    .OrderByDescending(transaction => transaction.Date);

                var transactionsPaged = transactions
                    .Skip(initialIndex)
                    .Take(amountOfRows);
                var transactionsDTO = transactionsPaged.MapToHistoricalTransactionsDTO();
                transactionsDTO.TotalRecords = deposits.Count() + withdrawals.Count() + transfers.Count() + creditNotes.Count() + debitNotes.Count();
                return transactionsDTO;
            }
        }
    }
}
