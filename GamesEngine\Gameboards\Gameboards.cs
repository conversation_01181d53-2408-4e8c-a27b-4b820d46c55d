﻿using GamesEngine.Gameboards.MarchMadness;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace GamesEngine.Gameboards
{
    [Puppet]
    internal class Gameboards : Objeto
    {
        private GameboardsByPlayer gameboardsByPlayer;
        private int lastSequence;

        internal Gameboards()
        {
            //TODO xxxx
            //LastSequence hay que crearlo con sequence correcto. En MM, la sequence arranca en 1 para cada juego de brackets porque no hay skips
            //En la Lottery ahora si tiene una sequence para todos los tickets, pero arranca en 1 cada vez que se resetea el actor y esta arrancando en 1 para los wagers
            Reset();
        }

        internal void Reset()
        {
            gameboardsByPlayer = new GameboardsByPlayer();
            this.lastSequence = 0;
        }

        internal void Add(Gameboard gameboard)
        {
            if (gameboard == null) throw new ArgumentNullException(nameof(gameboard));

            Bets.Player player = gameboard.Player;
            gameboardsByPlayer.Add(player, gameboard);
            lastSequence++;
        }

        internal void Remove(Bets.Player player, Gameboard gameboard)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (gameboard == null) throw new ArgumentNullException(nameof(gameboard));
            gameboardsByPlayer.Remove(player, gameboard);
        }

        internal void RemoveDisposedGameboards(Bets.Player player)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            gameboardsByPlayer.RemoveDisposedGameboards(player);
        }

        internal int NextSequence()
        {
            var next = lastSequence + 1;
            return next;
        }

        internal IEnumerable<Gameboard> ByPlayer(Bets.Player player)
        {
            return gameboardsByPlayer.ByPlayer(player);
        }

        protected IEnumerable<Gameboard> List()
		{
            var result = gameboardsByPlayer.List();
            return result;
		}

        protected IEnumerable<Gameboard> List(Func<Gameboard, bool> match)
        {
            var result = gameboardsByPlayer.List(match);
            return result;
        }

        protected bool Any(Func<Gameboard, bool> match)
        {
            var result = gameboardsByPlayer.Any(match);
            return result;
        }

        internal IEnumerable<Bets.Player> Players
		{
			get
			{
                return this.gameboardsByPlayer.Players;
			}
		}
    }

    class GroupOFGameboards
    {
        private readonly List<Gameboard> boards = new List<Gameboard>();
        private readonly Bets.Player owner;

        internal GroupOFGameboards(Bets.Player owner)
        {
            this.owner = owner;
        }

        internal GroupOFGameboards Add(Gameboard gameboard)
        {
            if (gameboard == null) throw new ArgumentNullException(nameof(gameboard));

            boards.Add(gameboard);
            return this;
        }

        internal void Remove(Gameboard gameboard)
        {
            if (gameboard == null) throw new ArgumentNullException(nameof(gameboard));

            boards.Remove(gameboard);
        }

        internal void RemoveDisposedGameboards()
        {
            boards.RemoveAll(gameboard => gameboard.IsReadyToBeDisposed());
        }

        internal bool Contains(Gameboard gameboard)
        {
            return boards.Contains(gameboard);
        }

        internal Bets.Player Owner
        {
            get { return this.owner; }
        }
        internal IEnumerable<Gameboard> List()
        {
            return boards;
        }
        
        internal IEnumerable<Gameboard> List(Func<Gameboard, bool> match)
        {
            return boards.Where(match);
        }

        internal bool Any(Func<Gameboard, bool> match)
        {
            return boards.Any(match);
        }
    }

    class GameboardsByPlayer
    {
        private readonly Dictionary<Bets.Player, GroupOFGameboards> gameboardsByPlayer = new Dictionary<Bets.Player, GroupOFGameboards>();

        internal void Add(Bets.Player player, Gameboard gameboard)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (gameboard == null) throw new ArgumentNullException(nameof(gameboard));

            GroupOFGameboards gameboards;
            if (!gameboardsByPlayer.TryGetValue(player, out gameboards))
            {
                gameboardsByPlayer.Add(player, new GroupOFGameboards(player).Add(gameboard));
            }
            else
            {
                gameboards.Add(gameboard);
            }
        }

        internal void Remove(Bets.Player player, Gameboard gameboard)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (gameboard == null) throw new ArgumentNullException(nameof(gameboard));
            GroupOFGameboards gameboards = gameboardsByPlayer.GetValueOrDefault(player);
            gameboards.Remove(gameboard);
        }

        internal void RemoveDisposedGameboards(Bets.Player player)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            GroupOFGameboards gameboards = gameboardsByPlayer.GetValueOrDefault(player);
            if(gameboards != null)
			{
                gameboards.RemoveDisposedGameboards();
                if (gameboards.List().Count() == 0)
                {
                    gameboardsByPlayer.Remove(player);
                }
            }
        }

        internal IEnumerable<Gameboard> ByPlayer(Bets.Player player)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));

            GroupOFGameboards gameboards;
            if (!gameboardsByPlayer.TryGetValue(player, out gameboards))
            {
                throw new GameEngineException("This is not gameboards for this player yet.");
            }
            else
            {
                return gameboards.List();
            }
        }

        internal IEnumerable<Gameboard> List()
        {
            var listaDeGrupos = gameboardsByPlayer.Values;
            IEnumerable<Gameboard> result = Enumerable.Empty<Gameboard>();
            foreach (GroupOFGameboards group in listaDeGrupos)
            {
                result = result.Concat(group.List());
            }

            return result;
        }

        internal IEnumerable<Gameboard> List(Func<Gameboard, bool> match)
        {
            var listaDeGrupos = gameboardsByPlayer.Values;
            IEnumerable<Gameboard> result = Enumerable.Empty<Gameboard>();
            foreach (GroupOFGameboards group in listaDeGrupos)
            {
                result = result.Concat(group.List(match));
            }

            return result;
        }

        internal bool Any(Func<Gameboard, bool> match)
        {
            var listaDeGrupos = gameboardsByPlayer.Values;
            foreach (GroupOFGameboards group in listaDeGrupos)
            {
                if (group.Any(match)) return true;
            }

            return false;
        }

        internal IEnumerable<Bets.Player> Players
		{
			get
			{
                return this.gameboardsByPlayer.Keys;
			}
		}
    }
}
