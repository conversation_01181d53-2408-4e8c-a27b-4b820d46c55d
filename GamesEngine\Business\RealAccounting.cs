﻿using Confluent.Kafka;
using ExternalServices;
using GamesEngine.Finance;
using GamesEngine.Games.Lines;
using GamesEngine.Games.Lotto;
using GamesEngine.Gameboards;
using GamesEngine.Gameboards.Lines;
using GamesEngine.Gameboards.Lotto;
using GamesEngine.Gameboards.MarchMadness;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using GamesEngine.Tools;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;
using town.connectors.drivers;
using GamesEngine.MessageQueuing;
using GamesEngine.Bets;

namespace GamesEngine.Business
{
	[Puppet]
	internal sealed class RealAccounting : Accounting
	{

		internal RealAccounting() { }

		internal override bool CustomerHasFoundsFor(Order order, decimal founds)
		{
			if (order == null) throw new ArgumentNullException(nameof(order));

			var hasFounds = founds >= order.Total();
			return hasFounds;
		}

		internal override void Purchase(bool itIsThePresent, OrderCompleted order, DateTime now)
		{
			if (order == null) throw new ArgumentNullException(nameof(order));
			if (order.Customer == null) throw new ArgumentNullException(nameof(order.Customer));
			var customer = order.Customer;
			if (String.IsNullOrWhiteSpace(customer.AccountNumber)) throw new ArgumentNullException(nameof(customer.AccountNumber));
			decimal total = order.Total();
			if (total <= 0) throw new GameEngineException("Total in order must be greater than 0");

			int orderNumber = order.Number;
			int lowBetId = order.LookForTheLowestBetNumber();
			int highBetId = order.LookForTheHighestBetNumber();
			if (lowBetId <= 0) throw new GameEngineException($"{nameof(lowBetId)} must be greater than 0");
			if (highBetId <= 0) throw new GameEngineException($"{nameof(highBetId)} must be greater than 0");

			bool itemCouldBeATicketBracketOrLine = lowBetId == highBetId;
			if (itemCouldBeATicketBracketOrLine)
			{
				var betNumber = lowBetId;
				var company = order.Customer.Company;
				var gameboard = company.FindBetById(betNumber).Gameboard;
				switch (gameboard)
				{
					case Ticket t:
						PicksLotteryGame lotto900 = customer.Company.Lotto900();
						SendFragmentsCreation(itIsThePresent, customer, t.AuthorizationId, lowBetId, highBetId, orderNumber, order.Coin, lotto900, total, order.Customer.Player.Agent, now);
						break;
					case Bracket b:
						string description = $"Brackets: Bracket # {b.Id} Purchase";
						string additionalInfo = $"Bracket # + {b.Id} price {total}";

						Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForTransacctions, new OrderTransaction(orderNumber, customer.AccountNumber, total, description, additionalInfo, now, betNumber));
						break;
					case Wager w:
						//TODO xxxx
						break;
					default:
						throw new GameEngineException($"This gameboard {gameboard.GetType().Name} is not handled yet");
				}
			}
			else
			{
				PicksLotteryGame lotto900 = customer.Company.Lotto900();
				if (order.IsMultipleAuthorization)
                {
					var company = order.Customer.Company;
					var bets = company.FindBetsInRange(lowBetId, highBetId);
					var ticketWagers = bets.Select(b => b.Gameboard as Ticket).SelectMany(t => t.Wagers);
					
                    SendFragmentsCreation(itIsThePresent, customer, ticketWagers, orderNumber, order.Coin, lotto900, total, order.Customer.Player.Agent, now, order.ProcessorKey);
                }
                else
                {
                    int ticketNumber = order.AuthorizationId;
					SendFragmentsCreation(itIsThePresent, customer, ticketNumber, lowBetId, highBetId, orderNumber, order.Coin, lotto900, total, order.Customer.Player.Agent, now);
				}
			}
		}

		internal override void Purchase(bool itIsThePresent, OrderCompleted order, DateTime now, IEnumerable<Ticket> tickets)
		{
			PicksLotteryGame lotto900 = order.Customer.Company.Lotto900();
            var ticketWagers = tickets.SelectMany(t => t.Wagers);
			var total = order.Total();
            SendFragmentsCreation(itIsThePresent, order.Customer, ticketWagers, order.Number, order.Coin, lotto900, total, order.Customer.Player.Agent, now, order.ProcessorKey);
		}

		internal override void GradeTicketWager(bool itIsThePresent, TicketWager wager, DateTime now, string employeeName)
		{
			if (!Integration.UseKafka || !Integration.UseKafkaForAuto) return;

			var ticket = wager.Ticket;
			var storeId = wager.Ticket.SoldBy.Id;
			Action<KafkaMessages> beforeSend = (KafkaMessages kafkaMessages) =>
			{
                kafkaMessages.Add((int)Integration.Localization);
                kafkaMessages.Add(employeeName);
				kafkaMessages.Add(storeId);
				kafkaMessages.Add(ticket.Draw.SometimeWereTicketsSentToAccounting);
			};

			using (FragmentPaymentCompressor bufferForAll = new FragmentPaymentCompressor(itIsThePresent, Integration.Kafka.TopicForFragmentPaymentsForAll))
			{
				bufferForAll.BeforeSendTheFirstMessage = bufferForAll.GenerateHeader(employeeName, storeId, WholePaymentProcessor.NoPaymentProcessor, ticket.Draw.SometimeWereTicketsSentToAccounting, now);

				using (KafkaMessagesBuffer bufferForWinners = new KafkaMessagesBuffer(itIsThePresent, Integration.Kafka.TopicForFragmentPaymentsForAll))
				{
					bufferForWinners.BeforeSendTheFirstMessage = beforeSend;

					FragmentPaymentMessage gradedWagerMessage;
					var hasSequenceOfNumbers = ticket.Draw.HasSequenceOfNumbers;

					switch (ticket.Prizing)
					{
						case GameboardStatus.LOSER:
							string message = string.Empty;
							gradedWagerMessage = wager.FragmentPaymentMessage(now); 

							bufferForAll.Send(gradedWagerMessage);

							break;
						case GameboardStatus.WINNER:
							var isWinnerWager = hasSequenceOfNumbers && wager.IsWinner();
							if (isWinnerWager)
							{
								gradedWagerMessage = wager.FragmentPaymentMessage(now);
								bufferForWinners.Send(gradedWagerMessage);
							}
							else
							{
								gradedWagerMessage = wager.FragmentPaymentMessage(now);
								bufferForAll.Send(gradedWagerMessage);
							}
							break;
						default:
							throw new GameEngineException("At this point ticket must be winner or loser");
					}
				}
			}
		}

		internal override void DeleteTickets(bool itIsThePresent, IEnumerable<Ticket> tickets, Store store, DateTime now, string employeeName)
		{
			if (!itIsThePresent) return;
			using (FragmentPaymentCompressor buffer = new FragmentPaymentCompressor(itIsThePresent, Integration.Kafka.TopicForFragmentPaymentsForAll))
			{
				buffer.BeforeSendTheFirstMessage = buffer.GenerateHeader(employeeName, store.Id, WholePaymentProcessor.NoPaymentProcessor, false, now);

				FragmentPaymentMessage gradedWagerMessage;
				foreach (var ticket in tickets)
				{
					foreach (var wager in ticket.Wagers)
					{
						gradedWagerMessage = wager.GenerateDeletedFragmentMessage(now); 
						buffer.Send(gradedWagerMessage);
					}
				}
			}
		}

		internal override void DeleteWager(bool itIsThePresent, TicketWager wager, DateTime now, string employeeName)
		{
			if (!itIsThePresent) return;
			using (FragmentPaymentCompressor buffer = new FragmentPaymentCompressor(itIsThePresent, Integration.Kafka.TopicForFragmentPaymentsForAll))
			{
				buffer.BeforeSendTheFirstMessage = buffer.GenerateHeader(employeeName, wager.Ticket.SoldBy.Id, WholePaymentProcessor.NoPaymentProcessor, false, now);

				FragmentPaymentMessage gradedWagerMessage = wager.GenerateDeletedFragmentMessage(now);
				buffer.Send(gradedWagerMessage);
			}
		}

		internal override void PayPrize(bool itIsThePresent, int betId, string accountNumber, decimal prize, string description, string additionalInfo, DateTime now)
		{
			if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));
			if (String.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));
			if (prize <= 0) throw new GameEngineException($"{nameof(prize)} must be greater than 0");
            Tools.Commons.ValidateAmount(prize);

			Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForTransacctions, new BetsTransaction( TransactionType.PayPrize, betId, accountNumber, prize, description, additionalInfo, now));
		}

		internal override void Refund(bool itIsThePresent, int betId, string accountNumber, decimal amount, string description, string additionalInfo, DateTime now)
		{
			if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));
			if (String.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));
			if (amount <= 0) throw new GameEngineException($"{nameof(amount)} must be greater than 0");
            Tools.Commons.ValidateAmount(amount);

			Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForTransacctions, new BetsTransaction(TransactionType.Refund, betId, accountNumber, amount, description, additionalInfo, now));
		}

		internal override void ReturnPrize(bool itIsThePresent, int betId, string accountNumber, decimal amount, string description, string additionalInfo, DateTime now)
		{
			if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));
			if (String.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));
			if (amount <= 0) throw new GameEngineException($"{nameof(amount)} must be greater than 0");
            Tools.Commons.ValidateAmount(amount);

			Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForTransacctions, new BetsTransaction(TransactionType.ReturnPrize, betId, accountNumber, amount, description, additionalInfo, now));
		}

		internal override void RevertRefund(bool itIsThePresent, int betId, string accountNumber, decimal amount, string description, string additionalInfo, DateTime now)
		{
			if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));
			if (String.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));
			if (amount <= 0) throw new GameEngineException($"{nameof(amount)} must be greater than 0");
            Tools.Commons.ValidateAmount(amount);

			Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForTransacctions, new BetsTransaction(TransactionType.RevertRefund, betId, accountNumber, amount, description, additionalInfo, now));        
		}
	}

	public enum WagerStatus
	{
		W=0,
		L=1,
		X=2,
		D=3
	}
	internal static class WagerStatusExtention
	{
		internal static void StatusFrom(this ref WagerStatus status, char statusAsCode)
		{
			switch (statusAsCode)
			{
				case 'W':
					status = WagerStatus.W;
					break;
				case 'L':
					status = WagerStatus.L;
					break;
				case 'X':
					status = WagerStatus.X;
					break;
				case 'D':
					status = WagerStatus.D;
					break;
				default:
					throw new GameEngineException($"{nameof(status)} {statusAsCode} is unknown");
			}
		}
	}

	internal abstract class TransactionMessage : TypedMessage
	{
		private string description;
		private decimal amount;
		private string accountNumber;
		private string additionalInfo;
		internal string Description
		{
			get
			{
				return description;
			}
		}
		internal decimal Amount
		{
			get
			{
				return amount;
			}
		}
		internal string AccountNumber
		{
			get
			{
				return accountNumber;
			}
		}
		internal string AdditionalInfo
		{
			get
			{
				return additionalInfo;
			}
		}
		internal TransactionType Type
		{
			get
			{
				return (TransactionType)(int)base.MessageTypeSelector;
			}
		}

		internal TransactionMessage(TransactionType type, string accountNumber, decimal amount, string description, string additionalInfo): base((char)type)
		{
			if (string.IsNullOrEmpty(accountNumber)) throw new ArgumentException(nameof(accountNumber));
			if (string.IsNullOrEmpty(description)) throw new ArgumentException(nameof(type));

			this.accountNumber = accountNumber;
			this.amount = amount;
			this.description = description;
			const string ADDITIONAL_INFORMATION_EMPTY = "-";
			this.additionalInfo = string.IsNullOrEmpty(additionalInfo) ? ADDITIONAL_INFORMATION_EMPTY : additionalInfo;
		}

		public TransactionMessage(string serialized):base(serialized)
		{
			
		}

		protected override void Deserialize(string [] serializedMessage, out int fieldOrder)
		{
			base.Deserialize(serializedMessage, out fieldOrder);
			accountNumber = serializedMessage[fieldOrder++];
			amount = Decimal.Parse(serializedMessage[fieldOrder++]);
			description = serializedMessage[fieldOrder++];
			additionalInfo = serializedMessage[fieldOrder++];
		}

		protected override void InternalSerialize()
		{
			base.InternalSerialize();
			AddProperty(AccountNumber).
			AddProperty(Amount).
			AddProperty(Description).
			AddProperty(AdditionalInfo);
		}
	}


	internal sealed class OrderTransaction : TransactionMessage
	{
		private int orderNumber;
		private DateTime now;
		private int betNumber;

		internal int OrderNumber
		{
			get
			{
				return orderNumber;
			}
		}

		internal DateTime Now
		{
			get
			{
				return now;
			}
		}

		internal int BetNumber
		{
			get
			{
				return betNumber;
			}
		}

		internal OrderTransaction(int orderNumber, string accountNumber, decimal amount, string description, string additionalInfo, DateTime now, int betNumber) : 
			base(TransactionType.Purchase, accountNumber, amount, description, additionalInfo)
		{
			this.orderNumber = orderNumber;
			this.now = now;
			this.betNumber = betNumber;
		}

		internal OrderTransaction(string message):base(message) { }

		protected override void Deserialize(string [] serializedMessage, out int fieldOrder)
		{
			base.Deserialize(serializedMessage, out fieldOrder);
			this.orderNumber = int.Parse(serializedMessage[fieldOrder++]);
			this.now = new DateTime(
				int.Parse(serializedMessage[fieldOrder++]),
				int.Parse(serializedMessage[fieldOrder++]),
				int.Parse(serializedMessage[fieldOrder++]));
			this.betNumber = int.Parse(serializedMessage[fieldOrder++]);
		}

		protected override void InternalSerialize()
		{
			base.InternalSerialize();
			AddProperty(this.orderNumber).
			AddProperty(this.now.Year).
			AddProperty(this.now.Month).
			AddProperty(this.now.Day).
			AddProperty(this.betNumber);
		}
	}

	[Obsolete("No se elimina porque pueden quedar mensajes en la cola. Y hay que procesarlos antes de eliminar estas funciones.")]
	public sealed class GradedWagerMessage : KafkaMessage
	{
		public int TicketNumber { get; set; }
		public int WagerNumber { get; set; }
		public WagerStatus Status { get; set; }
		public DateTime Now { get; set; }
		public decimal AdjustedWinAmount { get; set; }
		public decimal AdjustedLossAmount { get; set; }

		internal GradedWagerMessage(int ticketNumber, int wagerNumber, WagerStatus status, DateTime now, decimal adjustedWinAmount, decimal adjustedLossAmount)
		{
			TicketNumber = ticketNumber;
			WagerNumber = wagerNumber;
			Status = status;
			Now = now;
			AdjustedWinAmount = adjustedWinAmount;
			AdjustedLossAmount = adjustedLossAmount;
		}

		public GradedWagerMessage(string message) : base(message) { }

		protected override void Deserialize(string [] serializedMessage, out int fieldOrder)
		{
			base.Deserialize(serializedMessage, out fieldOrder);
			TicketNumber = int.Parse(serializedMessage[fieldOrder++]);
			WagerNumber = int.Parse(serializedMessage[fieldOrder++]);
			Status = (WagerStatus)Enum.Parse(typeof(WagerStatus), serializedMessage[fieldOrder++]);
			Now = new DateTime(
				int.Parse(serializedMessage[fieldOrder++]),
				int.Parse(serializedMessage[fieldOrder++]),
				int.Parse(serializedMessage[fieldOrder++]));
			AdjustedWinAmount = decimal.Parse(serializedMessage[fieldOrder++]);
			AdjustedLossAmount = decimal.Parse(serializedMessage[fieldOrder++]);
		}

        protected override void InternalSerialize()
        {
			base.InternalSerialize();
			AddProperty(TicketNumber).
			AddProperty(WagerNumber).
			AddProperty(Status.ToString()).
			AddProperty(Now.Year).
			AddProperty(Now.Month).
			AddProperty(Now.Day).
			AddProperty(AdjustedWinAmount).
			AddProperty(AdjustedLossAmount);
		}
    }

 
	public sealed class WagersOrderTransaction : KafkaMessage
	{
		public int TicketNumber { get; set; }
		public string AccountNumber { get; set; }
		public TransactionType Type { get; set; }
		public int OrderNumber { get; set; }
		public int TheLowestBetId { get; set; }
		public int TheHighestBetId { get; set; }

		internal WagersOrderTransaction(string accountNumber, int ticketNumber, int orderNumber, int theLowestBetId, int theHighestBetId)
		{
			if (string.IsNullOrEmpty(accountNumber)) throw new ArgumentException(nameof(accountNumber));

			Type = TransactionType.PurchaseTicket;
			AccountNumber = accountNumber;
			TicketNumber = ticketNumber;
			OrderNumber = orderNumber;
			this.TheLowestBetId = theLowestBetId;
			this.TheHighestBetId = theHighestBetId;
		}

		public WagersOrderTransaction(string message) : base(message) { }

		protected override void Deserialize(string [] serializedMessage, out int fieldOrder)
		{
			base.Deserialize(serializedMessage, out fieldOrder);
			Type = (TransactionType)Enum.Parse(typeof(TransactionType), serializedMessage[fieldOrder++]);
			AccountNumber = serializedMessage[fieldOrder++];
			TicketNumber = int.Parse(serializedMessage[fieldOrder++]);
			OrderNumber = int.Parse(serializedMessage[fieldOrder++]);
			TheLowestBetId = int.Parse(serializedMessage[fieldOrder++]);
			TheHighestBetId = int.Parse(serializedMessage[fieldOrder++]);
		}

        protected override void InternalSerialize()
        {
			base.InternalSerialize();
			AddProperty(Type.ToString()).
			AddProperty(AccountNumber).
			AddProperty(TicketNumber).
			AddProperty(OrderNumber).
			AddProperty(TheLowestBetId).
			AddProperty(TheHighestBetId);
		}
    }

	internal sealed class BetsTransaction : TransactionMessage
	{
		private DateTime now;
		private int betNumber;
		internal DateTime Now
		{
			get
			{
				return now;
			}
		}
		internal int BetNumber
		{
			get
			{
				return betNumber;
			}
		}

		internal BetsTransaction(TransactionType type, int betNumber, string accountNumber, decimal amount, string description, string additionalInfo, DateTime now) : 
			base(type, accountNumber, amount, description, additionalInfo)
		{
			this.now = now;
			this.betNumber = betNumber;
		}

		internal BetsTransaction(string message) : base(message) { }

		protected override void Deserialize(string [] serializedMessage, out int fieldOrder)
		{
			base.Deserialize(serializedMessage, out fieldOrder);
			this.now = new DateTime(
				int.Parse(serializedMessage[fieldOrder++]),
				int.Parse(serializedMessage[fieldOrder++]),
				int.Parse(serializedMessage[fieldOrder++]));
			this.betNumber = int.Parse(serializedMessage[fieldOrder++]);
		}

		protected override void InternalSerialize()
		{
			base.InternalSerialize();
			AddProperty(this.now.Year).
			AddProperty(this.now.Month).
			AddProperty(this.now.Day).
			AddProperty(this.betNumber);
		}
	}


/*    public class PairLowHigh
	{
		public int lowBetId;
		public int theHighestBetId;
		public int number;

		public PairLowHigh(int lowBetId, int theHighestBetId)
		{
			this.lowBetId = lowBetId;
			this.theHighestBetId = theHighestBetId;
		}

		public PairLowHigh(int lowBetId, int theHighestBetId, int number) : this(lowBetId, theHighestBetId)
		{
			this.number = number;
		}
	}*/
}
