﻿using ExchangeAPI.ExchangeEntities;
using Microsoft.EntityFrameworkCore;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ExchangeTests.Tests
{
    public class DBExchangeCreator
    {
        public DbContextOptions<freshContext> ContextOptions { get; }

        public DBExchangeCreator(DbContextOptions<freshContext> contextOptions)
        {
            ContextOptions = contextOptions;

            Seed();
        }

        private void Seed()
        {
            //using (var context = new ExchangeContext(ContextOptions))
            //{
            //    context.Database.EnsureDeleted();
            //    context.Database.EnsureCreated();

            //    var user = new Exchangeuser()
            //    {
            //        Name = "Bart Simpson"
            //    };

            //    var exchangeRate = new Exchangerate()
            //    {
            //        Date = new DateTime(2020, 5, 5, 8, 0, 0),
            //        Fromcurrencyid = 2,
            //        Tocurrencyid = 3,
            //        Purchaseprice = 0.00014m,
            //        Saleprice = 0.00015m,
            //        WhocreatedNavigation = user
            //    };

            //    var exchangeRate2 = new Exchangerate()
            //    {
            //        Date = new DateTime(2020, 5, 5, 8, 0, 0),
            //        Fromcurrencyid = 3,
            //        Tocurrencyid = 2,
            //        Purchaseprice = 7000m,
            //        Saleprice = 7500m,
            //        WhocreatedNavigation = user
            //    };

            //    var dailyProfits = new List<Dailyprofits>();
            //    for (DateTime date = new DateTime(2019, 1, 1); date <= new DateTime(2020, 12, 12); date = date.AddDays(1))
            //    {
            //        var dailyProfit = new Dailyprofits()
            //        {
            //            Day = date,
            //            Profits = 5,
            //            Purchases = 15,
            //            Sales = 25,
            //            Currencyid = 3
            //        };
            //        dailyProfits.Add(dailyProfit);
            //    }

            //    context.Dailyprofits.AddRange(dailyProfits);
            //    context.AddRange(user, exchangeRate, exchangeRate2);

            //    context.SaveChanges();
            //}
        }

        
    }

    
}
