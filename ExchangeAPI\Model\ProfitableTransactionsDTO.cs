﻿using GamesEngine.Finance;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Threading.Tasks;

namespace ExchangeAPI.Model
{
	[DataContract(Name = "ProfitableTransactionsDTO")]
	public class ProfitableTransactionsDTO
	{
		[DataMember(Name = "profitableTransactions")]
		public List<ProfitableTransactionDTO> ProfitableTransactions { get; set; } = new List<ProfitableTransactionDTO>();

		[DataMember(Name = "totalRecords")]
		public int TotalRecords { get; set; }

		[DataMember(Name = "totalProfits")]
		public decimal TotalProfits { get; set; }

		[DataMember(Name = "totalGross")]
		public decimal TotalGross { get; set; }

		[DataMember(Name = "totalNet")]
		public decimal TotalNet { get; set; }

		[DataMember(Name = "totalAmountReceived")]
		public decimal TotalAmountReceived { get; set; }

		internal void Add(ProfitableTransactionDTO profitableTransactionDTO)
		{
			ProfitableTransactions.Add(profitableTransactionDTO);
		}

	}

	[DataContract(Name = "ProfitableTransactionDTO")]
	public class ProfitableTransactionDTO
	{
		[DataMember(Name = "date")]
		public string Date { get; set; }
		[DataMember(Name = "fromCurrencyCode")]
		public string FromCurrencyCode { get; set; }
		[DataMember(Name = "toCurrencyCode")]
		public string ToCurrencyCode { get; set; }

		[DataMember(Name = "gross")]
		public decimal Gross { get; set; }
		[DataMember(Name = "grossFormatted")]
		public string GrossFormatted { get { return Currency.Factory(GrossCurrencyCode, Gross).ToDisplayFormat(); } }
		[DataMember(Name = "grossCurrencyCode")]
		public string GrossCurrencyCode { get; set; }
		[DataMember(Name = "net")]
		public decimal Net { get; set; }
		[DataMember(Name = "netFormatted")]
		public string NetFormatted { get { return Currency.Factory(NetCurrencyCode, Net).ToDisplayFormat(); } }
		[DataMember(Name = "netCurrencyCode")]
		public string NetCurrencyCode { get; set; }
		[DataMember(Name = "profit")]
		public decimal Profit { get; set; }
		[DataMember(Name = "profitFormatted")]
		public string ProfitFormatted { get { return Currency.Factory(ProfitCurrencyCode, Profit).ToDisplayFormat(); } }
		[DataMember(Name = "profitCurrencyCode")]
		public string ProfitCurrencyCode { get; set; }
		[DataMember(Name = "amountReceived")]
		public decimal AmountReceived { get; set; }
		[DataMember(Name = "amountReceivedFormatted")]
		public string AmountReceivedFormatted { get { return Currency.Factory(AmountCurrencyCode, AmountReceived).ToDisplayFormat(); } }
		[DataMember(Name = "amountCurrencyCode")]
		public string AmountCurrencyCode { get; set; }

		[DataMember(Name = "id")]
		public string ID { get; set; }
		[DataMember(Name = "batchNumber")]
		public string BatchNumber { get; set; }
		[DataMember(Name = "type")]
		public string Type { get; set; }
		[DataMember(Name = "sourceAddress")]
		public string FromCustomer { get; set; }
		[DataMember(Name = "destinationAddress")]
		public string ToCustomer { get; set; }
		[DataMember(Name = "sourceAccount")]
		public string Account { get; set; }
		[DataMember(Name = "destinationAccount")]
		public string TargetAccount { get; set; }
		[DataMember(Name = "whoApproved")]
		public string WhoApproved { get; set; }
	}
}
