﻿using GamesEngine.Bets;
using GamesEngine.Gameboards.Lotto;
using GamesEngine.Games.Lotto;
using GamesEngine.Games.Tournaments;
using GamesEngine.Gamification.Leaderboards;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Numerics;

namespace GamesEngine.Gameboards
{
	[Puppet]
	public abstract class Gameboard : Objeto, IParticipant
	{
		protected enum State { OPEN, LOCKED, DISPOSED }
		protected State state = State.OPEN;

		private readonly int id;
		private readonly Bets.Player player;
		private string name;
		private OrderCompleted order;
		private readonly DateTime creationDate;
		private GameboardStatus gameboardStatus;
		private GameboardStatus gameboardStatusPosted;

		internal Gameboard(Bets.Player player, Gameboards allGameboards, DateTime creationDate)
		{
			if (id < 0) throw new GameEngineException($"Gameboard Id {id} must be greater than zero");
			if (player == null) throw new ArgumentNullException(nameof(player));
			if (allGameboards == null) throw new ArgumentNullException(nameof(allGameboards));

			this.player = player;
			this.creationDate = creationDate;
			this.gameboardStatus.Init();
			this.gameboardStatusPosted.Init();
			this.id = allGameboards.NextSequence();
			allGameboards.Add(this);
		}

		internal Gameboard(Bets.Player player, Gameboards allGameboards, DateTime creationDate, Prizes prizes)
		{
			if (id < 0) throw new GameEngineException($"Gameboard Id {id} must be greater than zero");
			if (player == null) throw new ArgumentNullException(nameof(player));
			if (allGameboards == null) throw new ArgumentNullException(nameof(allGameboards));
			if (prizes == null) throw new ArgumentNullException(nameof(prizes));

			this.player = player;
			this.creationDate = creationDate;
			this.gameboardStatus.Init();
			this.gameboardStatusPosted.Init();
			this.id = allGameboards.NextSequence();
			allGameboards.Add(this);
			this.prizes = prizes;
		}

		internal virtual String Name
		{
			get
			{
				if (String.IsNullOrWhiteSpace(name)) throw new GameEngineException("Gameboard Name has not been defined");
				return name;
			}
			set
			{
				if (String.IsNullOrWhiteSpace(value)) throw new ArgumentNullException(nameof(Name));
				name = Business.Accounting.ReplaceAccountNumbers(value);
			}
		}

		internal void UpdateName(bool itIsThePresent, string name, GameAbbreviation abbreviation)
		{
			if (String.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(Name));
			this.Name = name;
			Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForProfanity,
				new BracketNameMessage(ProfanityMessageType.BRACKET_NAME, this.id, this.Name, abbreviation));
		}

		internal void Rename(string name)
		{
			int consecutiveNumber = 2;
			string resultName = name;
			while (this.player.HasGameboardNamed(resultName))
			{
				resultName = $@"{name}_{consecutiveNumber++}";
			}
			this.Name = resultName;
		}

		internal OrderCompleted Order
		{
			set
			{
				if (value == null) throw new ArgumentNullException(nameof(order));
				this.order = value;
			}
			get
			{
				if (this.order == null) throw new GameEngineException("Order has not been set, use WasPurchased before call this method");
				return this.order;
			}
		}

		internal bool WasPurchased
		{
			get
			{
				return this.order != null;
			}
		}

		internal Store SoldBy
		{
			get
			{
				if (!WasPurchased) throw new GameEngineException("This ticket was not purchased");
				return this.order.Company.Sales.CurrentStore;
			}
		}

		internal void MarkAccountingSettleAsDone()
		{
			var after = gameboardStatus;
			var before = gameboardStatusPosted;

			if (!before.IsRefunded())
			{
				if (after.IsGraded())
				{
					if (before.IsGraded() || before.IsNoAction()) before.ChangeToRegraded();
					switch (after.PrizingStatus())
					{
						case GameboardStatus.WINNER:
							if (!before.IsWinner()) before.GradeAsWinner();
							break;
						case GameboardStatus.LOSER:
							if (!before.IsLoser()) before.GradeAsLoser();
							break;
						case GameboardStatus.UNPRIZED:
							if (!before.IsRefunded()) before.ChangeToRefunded();
							break;
					}
				}
				else if (after.IsRegraded())
				{
					before.ChangeToRegraded();
				}
				else if (after.IsRefunded())
				{
					before.ChangeToRefunded();
				}
				else if (after.IsNoAction())
				{
					before.ChangeToNoAction();
				}
				else
				{
					throw new GameEngineException("Invalid transition");
				}
				gameboardStatusPosted = before;
			}
		}

		internal bool NeedsAccountingSettle
		{
			get
			{
				var before = gameboardStatusPosted;
				var after = this.gameboardStatus;
				if (before.IsRefunded())
				{
					return false;
				}
				else if(before.IsPending() && after.IsRegraded()) 
				{
					return false;
				}
				else if (before.IsPending() && !after.IsPending())
				{
					return true;
				}
				else if(before.IsGraded() && after.IsGraded())
				{
					return true;
				}
				else if (before.IsGraded() && after.IsNoAction())
				{
					return true;
				}
				else if(before.IsGraded() && after.IsRegraded())
				{
					var needsToChange = after.IsWinner() || before.IsWinner() || after.IsRefunded();
					return needsToChange;
				}
				else if (before.IsNoAction() && !after.IsNoAction())
				{
					return true;
				}
				else if (before.IsRegraded())
				{
					var needsToChange = after.IsWinner() || after.IsNoAction() || after.IsRefunded();
					return needsToChange;
				}
				else
				{
					throw new GameEngineException("Invalid transition");
				}
			}
		}

		internal bool IsHighPrioritySettle
		{
			get
			{
				var nowIsWinner = this.gameboardStatus.PrizingStatus().IsWinner();
				var previouslyWasWinner = gameboardStatusPosted.PrizingStatus().IsWinner();
				var result = (previouslyWasWinner || nowIsWinner);
				return result;
			}
		}

		internal bool WasHighPrioritySettle
		{
			get
			{
				var previouslyWasWinner = gameboardStatusPosted.PrizingStatus().IsWinner();
				return previouslyWasWinner;
			}
		}

		internal Bets.Player Player
		{
			get
			{
				return player;
			}
		}

		string IParticipant.Name => Name;
		int IParticipant.Id => Id;
		string IParticipant.Player => player.Id;
		decimal IParticipant.Score => Grade();
		BigInteger IParticipant.ScoreToUntie => GradeToUntie();

		/// <summary>
		/// This method returns the score achieved for the bracket.
		/// </summary>
		public abstract decimal Grade();

		public abstract BigInteger GradeToUntie();

		internal int Id
		{
			get
			{
				return id;
			}
		}

		internal void Dispose()
		{
			this.state = State.DISPOSED;
			this.order.ActiveGameboards--;
		}

		internal bool IsReadyToBeDisposed()
		{
			return this.state == State.DISPOSED;
		}

		internal string AccountNumber
		{
			get
			{
				return this.player.AccountNumber;
			}
		}



		internal DateTime CreationDate
		{
			get
			{
				return this.creationDate;
			}
		}
		internal string PostedDateAsString()
		{
			var postedDate = CreationDate.ToString("M/d/yyyy h:mm tt");
			return postedDate;
		}

		internal GameboardStatus Grading
		{
			get
			{
				return gameboardStatus.GradingStatus();
			}
		}

		internal GameboardStatus Prizing
		{
			get
			{
				return gameboardStatus.PrizingStatus();
			}
		}

		internal bool IsGraded()
		{
			var result = gameboardStatus.IsGraded();
			return result;
		}

		internal bool IsRegraded()
		{
			var result = gameboardStatus.IsRegraded();
			return result;
		}

		internal bool IsNoAction()
		{
			var result = gameboardStatus.IsNoAction();
			return result;
		}

		internal bool IsPending()
		{
			var result = gameboardStatus.IsPending();
			return result;
		}

		internal bool IsUnprized()
		{
			var result = gameboardStatus.IsUnprized();
			return result;
		}

		internal bool IsRefunded()
		{
			var result = gameboardStatus.IsRefunded();
			return result;
		}

		internal bool IsWinner()
		{
			var result = gameboardStatus.IsWinner();
			return result;
		}

		internal bool IsLoser()
		{
			var result = gameboardStatus.IsLoser();
			return result;
		}

		internal bool PreviouslyWasPending()
		{
			var result = gameboardStatus.PreviouslyWasPending();
			return result;
		}

		internal bool PreviouslyWasWinner()
		{
			var result = gameboardStatus.PreviouslyWasWinner();
			return result;
		}

		internal bool PreviouslyWasLoser()
		{
			var result = gameboardStatus.PreviouslyWasLoser();
			return result;
		}

		internal bool PreviouslyWasNoAction()
		{
			var result = gameboardStatus.PreviouslyWasNoAction();
			return result;
		}

		internal void GradeAsWinner()
		{
			this.gameboardStatus.GradeAsWinner();
		}

		internal void GradeAsLoser()
		{
			this.gameboardStatus.GradeAsLoser();
		}

		internal void ChangeToNoAction()
		{
			this.gameboardStatus.ChangeToNoAction();
		}

		internal void ChangeToRegraded()
		{
			this.gameboardStatus.ChangeToRegraded();
		}
		
		internal void ChangeToRefunded()
		{
			this.gameboardStatus.ChangeToRefunded();
		}

		internal void RestorePreviousGameboardStatus()
		{
			this.gameboardStatus.RestorePreviousValue();
		}

		internal string GradingAsString
		{
			get
			{
				var g = gameboardStatus.GradingStatus();
				string result;
				switch (g)
				{
					case GameboardStatus.PENDING:
						result = GameboardStatus.PENDING.ToString();
						break;
					case GameboardStatus.GRADED:
						result = GameboardStatus.GRADED.ToString();
						break;
					case GameboardStatus.REGRADED:
						result = GameboardStatus.REGRADED.ToString();
						break;
					case GameboardStatus.NOACTION:
						result = GameboardStatus.NOACTION.ToString();
						break;
					default:
						throw new GameEngineException("There is a unhandled grading case");
				}
				return result;
			}
		}

		Prizes prizes;
		internal virtual Prizes Prizes
		{
			get
			{
				return prizes;
			}
		}
	}

	internal interface ISinglePool
	{
		int AuthorizationId { get; }

		Bet Bet { get; }
	}
}
