﻿using Connectors.town.connectors.driver.transactions;
using GamesEngine;
using GamesEngine.Finance;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;
using static town.connectors.drivers.fiero.DepositThenLockForMultipleAuthorizations;
using static town.connectors.drivers.Result;

namespace town.connectors.drivers.fiero
{
    internal class DepositThenLock : FierroProcessorDriver
	{
		private const float VERSION = 1.01F;
		public override string Description => $"Fiero {nameof(DepositThenLock)} driver {VERSION}";

		public DepositThenLock()
			: base(TransactionType.Deposit, VERSION)
        {
        }
		public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			throw new NotImplementedException();
		}
		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			var Actor = recordSet.Mappings["actor"];
			var Message = recordSet.Mappings["fragmentMessage"];
            var ProcessorKey = recordSet.Mappings["processorKey"];
            RestAPISpawnerActor actor = Actor.As<RestAPISpawnerActor>();
			FragmentsCreationBody fragmentMessage = Message.As<FragmentsCreationBody>();
            var processorKey = ProcessorKey.AsString;

            var result = actor.PerformQry(MovementStorage.ATADDRESS_GENERAL, $@"
				{{
                    processorAccount = guardian.Accounts().SearchByProcessor('{processorKey}');
                    print processorAccount.Id processorAccountId;
                }}");
            if (!(result is OkObjectResult))
            {
                var auth1 = new AuthorizationTransaction(-1, TransactionStatus.DENIED, Entity, PaymentMethod, CurrencyIsoCodes);
                return (T)Convert.ChangeType(auth1, typeof(T));
            }

            OkObjectResult o = (OkObjectResult)result;
            string json = o.Value.ToString();
            var processorAccountInfo = JsonConvert.DeserializeObject<ProcessorAccountInfo>(json);

            Currency purchaseTotal = Currency.Factory(fragmentMessage.CurrencyCode, fragmentMessage.Total);
            var normalizedUseless = fragmentMessage.Useless.ToString("MM/dd/yyyy HH:mm:ss");
            string command = $@"
							{{
								balance = atAddress.CreateAccountIfNotExists('{fragmentMessage.CurrencyCode}', '{fragmentMessage.CurrencyCode.ToString()}').Balance;
								Eval('available = '+balance.Available+';');
								balance.SetInitialBalance(itIsThePresent, available);
								store = company.Sales.StoreById({fragmentMessage.StoreId.ToString()});
								balance.Accredit(itIsThePresent, Now, Currency('{fragmentMessage.CurrencyCode}', {fragmentMessage.Total}), '{Users.ME}', '{fragmentMessage.AuthorizationNumber}', store, '', '', {processorAccountInfo.processorAccountId});
								authorization = atAddress.CreateAuthorization(itIsThePresent,Now, Currency('{fragmentMessage.CurrencyCode}', {purchaseTotal.Value}), {fragmentMessage.AuthorizationNumber}, store, '', '', '{fragmentMessage.CurrencyCode}', {normalizedUseless}, {processorAccountInfo.processorAccountId});
							}}";
			result = actor.PerformCmd(fragmentMessage.AtAddress, command);
			if (!(result is OkObjectResult))
			{
				var auth1 = new AuthorizationTransaction(-1, TransactionStatus.DENIED, Entity, PaymentMethod, CurrencyIsoCodes);
				return (T)Convert.ChangeType(auth1, typeof(T));
			}

			var auth = new AuthorizationTransaction(1, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes);
			return (T)Convert.ChangeType(auth, typeof(T));
		}

		public override void Prepare(DateTime now)
		{
			CustomSettings.AddVariableParameter("actor");
			CustomSettings.AddVariableParameter("fragmentMessage");
			CustomSettings.AddVariableParameter("processorKey");
		}

	}
}
