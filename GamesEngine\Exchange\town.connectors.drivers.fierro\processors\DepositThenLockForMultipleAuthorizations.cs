﻿using Connectors.town.connectors.driver.transactions;
using GamesEngine.Business;
using GamesEngine.Finance;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using town.connectors.drivers.fiero;
using town.connectors.drivers;
using town.connectors;
using static town.connectors.drivers.Result;
using Newtonsoft.Json;
using GamesEngine.PurchaseOrders;

namespace town.connectors.drivers.fiero
{
    internal class DepositThenLockForMultipleAuthorizations: FierroProcessorDriver
    {
        private const float VERSION = 1.02F;
        public override string Description => $"Fiero {nameof(DepositThenLockForMultipleAuthorizations)} driver {VERSION}";

        public DepositThenLockForMultipleAuthorizations()
            : base(TransactionType.Deposit, VERSION)
        {
        }
        public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            throw new NotImplementedException();
        }
        public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            var Actor = recordSet.Mappings["actor"];
            var Message = recordSet.Mappings["fragmentMessage"];
            var ProcessorKey = recordSet.Mappings["processorKey"];
            RestAPISpawnerActor actor = Actor.As<RestAPISpawnerActor>();
            FragmentsCreationBody fragmentMessage = Message.As<FragmentsCreationBody>();
            var processorKey = ProcessorKey.AsString;

            var result = actor.PerformQry(MovementStorage.ATADDRESS_GENERAL, $@"
				{{
                    processorAccount = guardian.Accounts().SearchByProcessor('{processorKey}');
                    print processorAccount.Id processorAccountId;
                }}");
            if (!(result is OkObjectResult))
            {
                var auth1 = new AuthorizationTransaction(-1, TransactionStatus.DENIED, Entity, PaymentMethod, CurrencyIsoCodes);
                return (T)Convert.ChangeType(auth1, typeof(T));
            }

            OkObjectResult o = (OkObjectResult)result;
            string json = o.Value.ToString();
            var processorAccountInfo = JsonConvert.DeserializeObject<ProcessorAccountInfo>(json);

            StringBuilder commandForAuthorizations = new StringBuilder();
            AuthorizationsNumbers.BuildCommand(fragmentMessage.Fragments.Select(x => x.TicketNumber), ref commandForAuthorizations);

            Currency purchaseTotal = Currency.Factory(fragmentMessage.CurrencyCode, fragmentMessage.Total);
            var dateAsText = fragmentMessage.Date.ToString("MM/dd/yyyy HH:mm:ss");
            var normalizedUseless = fragmentMessage.Useless.ToString("MM/dd/yyyy HH:mm:ss");
            string commandForCreateAuthorization;
            if (fragmentMessage.AllRisksAreEquals)
            {
                var currencyByAuthorization = Currency.Factory(fragmentMessage.CurrencyCode, decimal.Parse(fragmentMessage.Fragments.First().Risk));
                commandForCreateAuthorization = $"balance.Accredit(itIsThePresent, {dateAsText}, Currency('{fragmentMessage.CurrencyCode}', {purchaseTotal.Value}), '{Users.ME}', auths.Numbers, store, '', '', {processorAccountInfo.processorAccountId});";
                commandForCreateAuthorization += $"atAddress.CreateAuthorization(itIsThePresent,{dateAsText}, Currency('{currencyByAuthorization.CurrencyCode}', {currencyByAuthorization.Value}), auths.Numbers, store, '', '', '{fragmentMessage.CurrencyCode}', {normalizedUseless}, {processorAccountInfo.processorAccountId});";
            }
            else
            {
                var risks = string.Join("','", fragmentMessage.Fragments.Select(f => f.Risk));
                commandForCreateAuthorization = $"balance.Accredit(itIsThePresent, {dateAsText}, {{'{risks}'}}, '{Users.ME}', auths.Numbers, store, '', '', {processorAccountInfo.processorAccountId});";
                commandForCreateAuthorization += $"atAddress.CreateAuthorization(itIsThePresent,{dateAsText}, '{fragmentMessage.CurrencyCode}', {{'{risks}'}}, auths.Numbers, store, '', '', '{fragmentMessage.CurrencyCode}', {normalizedUseless}, {processorAccountInfo.processorAccountId});";
            }

            string command = $@"
				{{
					balance = atAddress.CreateAccountIfNotExists('{fragmentMessage.CurrencyCode}', '{fragmentMessage.CurrencyCode}').Balance;
                    Eval('available = '+balance.Available+';');
					balance.SetInitialBalance(itIsThePresent, available);
					store = company.Sales.StoreById({fragmentMessage.StoreId});
                    {commandForAuthorizations}
					{commandForCreateAuthorization}
				}}";
            result = actor.PerformCmd(fragmentMessage.AtAddress, command);
            if (!(result is OkObjectResult))
            {
                var auth1 = new AuthorizationTransaction(-1, TransactionStatus.DENIED, Entity, PaymentMethod, CurrencyIsoCodes);
                return (T)Convert.ChangeType(auth1, typeof(T));
            }

            var auth = new AuthorizationTransaction(1, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes);
            return (T)Convert.ChangeType(auth, typeof(T));
        }

        public override void Prepare(DateTime now)
        {
            CustomSettings.AddVariableParameter("actor");
            CustomSettings.AddVariableParameter("fragmentMessage");
            CustomSettings.AddVariableParameter("processorKey");
        }

        public struct ProcessorAccountInfo
        {
            public int processorAccountId { get; set; }
        }
    }
}
