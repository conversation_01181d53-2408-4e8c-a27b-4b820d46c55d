﻿using GamesEngine.Games.Lotto;
using GamesEngine.Preferences.Lotto;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Finance
{
	class Authorizations
	{
		private Dictionary<int, Authorization> authorizations = new Dictionary<int, Authorization>();
		private AtAddress atAddressWhereBelongs { get; set; }

		internal Authorizations(AtAddress atAddress)
		{
			if (atAddress == null) throw new ArgumentException(nameof(atAddress));
			atAddressWhereBelongs = atAddress;
		}

		internal enum Purpose { ForUpdate, ForDisplay };
		internal Authorization Search(int number, Purpose purpose)
		{
			Authorization authorization = null;
			bool found = authorizations.TryGetValue(number, out authorization);
			if (!found)
			{
				authorization = Authorization.Load(number, atAddressWhereBelongs, purpose);
			}
			return authorization;
		}

		internal AuthorizationsMultiple Search(List<int> numbers, Purpose purpose)
		{
            var authorizationsMultiple = new AuthorizationsMultiple(atAddressWhereBelongs);

            foreach (int number in numbers)
			{
                Authorization authorization;
                bool found = authorizations.TryGetValue(number, out authorization);
                if (!found)
				{
					authorization = Authorization.Load(number, atAddressWhereBelongs, purpose);
				}
				authorizationsMultiple.Add(authorization);
			}
			return authorizationsMultiple;
		}

		internal Authorization Add(Authorization auth)
		{
			if (auth == null || authorizations.ContainsKey(auth.Number)) throw new GameEngineException($"There is an authorization already stored with number {auth.Number} at atAddress {atAddressWhereBelongs.Number}.");

			authorizations.Add(auth.Number, auth);

			return auth;
		}

		internal void Remove(Authorization authorization)
		{
			authorizations.Remove(authorization.Number);
		}
	}

    class AuthorizationsMultiple : Objeto
	{
		private List<Authorization> authorizations = new List<Authorization>();
		private AtAddress atAddressWhereBelongs { get; }

		internal AuthorizationsMultiple(AtAddress atAddress)
		{
			if (atAddress == null) throw new ArgumentException(nameof(atAddress));
			atAddressWhereBelongs = atAddress;
		}

		internal void Add(Authorization auth)
		{
            if (auth == null) throw new ArgumentNullException(nameof(auth));

            authorizations.Add(auth);
        }

        internal void CreateFragments()
        {
			var currentFragmentNumber = 0;
            foreach (Authorization authorization in authorizations)
			{
                authorization.CreateFragments(1, 1);
				currentFragmentNumber++;
            }
			if (currentFragmentNumber != authorizations.Count) throw new GameEngineException($"The number of fragments created is different than the number of authorizations. The number of fragments created is {currentFragmentNumber} and the number of authorizations is {authorizations.Count}");
        }

        internal void AddFragments(int authorizationIndex, IEnumerable<string> references, IEnumerable<string> descriptions, decimal risk, decimal toWin)
        {
			var countFragments = references.Count();
			if (countFragments != descriptions.Count()) throw new GameEngineException($"The number of descriptions is different than the number of authorizations. The number of descriptions is {descriptions.Count()} and the number of authorizations is {authorizations.Count}");

			var fragmentIndex = 0;
			foreach (Authorization authorization in authorizations)
			{
				var reference = references.ElementAt(fragmentIndex);
				var description = descriptions.ElementAt(fragmentIndex);
				authorization.AddFragments(1, 1, Enumerable.Empty<string>().Append(reference), Enumerable.Empty<string>().Append(description), risk, toWin);
				fragmentIndex++;
			}
        }

        internal void AddFragments(int authorizationIndex, IEnumerable<string> references, IEnumerable<string> descriptions, decimal risk, IEnumerable<decimal> toWins)
        {
            var countFragments = references.Count();
            if (countFragments != descriptions.Count()) throw new GameEngineException($"The number of descriptions is different than the number of authorizations. The number of descriptions is {descriptions.Count()} and the number of authorizations is {authorizations.Count}");
			if (countFragments != toWins.Count()) throw new GameEngineException($"The number of toWins is different than the number of authorizations. The number of toWins is {toWins.Count()} and the number of authorizations is {authorizations.Count}");

            var fragmentIndex = 0;
            foreach (Authorization authorization in authorizations)
            {
                var reference = references.ElementAt(fragmentIndex);
                var description = descriptions.ElementAt(fragmentIndex);
				var toWin = toWins.ElementAt(fragmentIndex);
                authorization.AddFragments(1, 1, Enumerable.Empty<string>().Append(reference), Enumerable.Empty<string>().Append(description), risk, Enumerable.Empty<decimal>().Append(toWin));
                fragmentIndex++;
            }
        }

        internal void AddFragments(int authorizationIndex, IEnumerable<string> references, IEnumerable<string> descriptions, IEnumerable<decimal> risks, decimal toWin)
        {
            var countFragments = references.Count();
            if (countFragments != descriptions.Count()) throw new GameEngineException($"The number of descriptions is different than the number of authorizations. The number of descriptions is {descriptions.Count()} and the number of authorizations is {authorizations.Count}");
			if (countFragments != risks.Count()) throw new GameEngineException($"The number of risks is different than the number of authorizations. The number of risks is {risks.Count()} and the number of authorizations is {authorizations.Count}");

            var fragmentIndex = 0;
            foreach (Authorization authorization in authorizations)
            {
                var reference = references.ElementAt(fragmentIndex);
                var description = descriptions.ElementAt(fragmentIndex);
                var risk = risks.ElementAt(fragmentIndex);
                authorization.AddFragments(1, 1, Enumerable.Empty<string>().Append(reference), Enumerable.Empty<string>().Append(description), Enumerable.Empty<decimal>().Append(risk), toWin);
                fragmentIndex++;
            }
        }

        internal void AddFragments(int authorizationIndex, IEnumerable<string> references, IEnumerable<string> descriptions, IEnumerable<decimal> risks, IEnumerable<decimal> toWins)
        {
            var countFragments = references.Count();
            if (countFragments != descriptions.Count()) throw new GameEngineException($"The number of descriptions is different than the number of authorizations. The number of descriptions is {descriptions.Count()} and the number of authorizations is {authorizations.Count}");
			if (countFragments != risks.Count()) throw new GameEngineException($"The number of risks is different than the number of authorizations. The number of risks is {risks.Count()} and the number of authorizations is {authorizations.Count}");
            if (countFragments != toWins.Count()) throw new GameEngineException($"The number of toWins is different than the number of authorizations. The number of toWins is {toWins.Count()} and the number of authorizations is {authorizations.Count}");

            var fragmentIndex = 0;
            foreach (Authorization authorization in authorizations)
            {
                var reference = references.ElementAt(fragmentIndex);
                var description = descriptions.ElementAt(fragmentIndex);
				var risk = risks.ElementAt(fragmentIndex);
                var toWin = toWins.ElementAt(fragmentIndex);
                authorization.AddFragments(1, 1, Enumerable.Empty<string>().Append(reference), Enumerable.Empty<string>().Append(description), Enumerable.Empty<decimal>().Append(risk), Enumerable.Empty<decimal>().Append(toWin));
                fragmentIndex++;
            }
        }

        internal void InitFragmentCreation()
        {
            var currentFragmentNumber = 0;
            foreach (Authorization authorization in authorizations)
            {
                authorization.InitFragmentCreation(1, 1);
                currentFragmentNumber++;
            }
            if (currentFragmentNumber != authorizations.Count) throw new GameEngineException($"The number of fragments created is different than the number of authorizations. The number of fragments created is {currentFragmentNumber} and the number of authorizations is {authorizations.Count}");
        }

        internal void AppendFragments(IEnumerable<string> references, IEnumerable<string> descriptions, decimal risk, decimal toWin)
        {
            var countFragments = references.Count();
            if (countFragments != descriptions.Count()) throw new GameEngineException($"The number of descriptions is different than the number of authorizations. The number of descriptions is {descriptions.Count()} and the number of authorizations is {authorizations.Count}");

            var fragmentIndex = 0;
            foreach (Authorization authorization in authorizations)
            {
                var reference = references.ElementAt(fragmentIndex);
                var description = descriptions.ElementAt(fragmentIndex);
                authorization.AppendFragments(Enumerable.Empty<string>().Append(reference), Enumerable.Empty<string>().Append(description), risk, toWin);
                fragmentIndex++;
            }
        }

        internal void AppendFragments(IEnumerable<string> references, IEnumerable<string> descriptions, decimal risk, IEnumerable<decimal> toWins)
        {
            var countFragments = references.Count();
            if (countFragments != descriptions.Count()) throw new GameEngineException($"The number of descriptions is different than the number of authorizations. The number of descriptions is {descriptions.Count()} and the number of authorizations is {authorizations.Count}");
            if (countFragments != toWins.Count()) throw new GameEngineException($"The number of toWins is different than the number of authorizations. The number of toWins is {toWins.Count()} and the number of authorizations is {authorizations.Count}");

            var fragmentIndex = 0;
            foreach (Authorization authorization in authorizations)
            {
                var reference = references.ElementAt(fragmentIndex);
                var description = descriptions.ElementAt(fragmentIndex);
                var toWin = toWins.ElementAt(fragmentIndex);
                authorization.AppendFragments(Enumerable.Empty<string>().Append(reference), Enumerable.Empty<string>().Append(description), risk, Enumerable.Empty<decimal>().Append(toWin));
                fragmentIndex++;
            }
        }

        internal void AppendFragments(IEnumerable<string> references, IEnumerable<string> descriptions, IEnumerable<decimal> risks, decimal toWin)
        {
            var countFragments = references.Count();
            if (countFragments != descriptions.Count()) throw new GameEngineException($"The number of descriptions is different than the number of authorizations. The number of descriptions is {descriptions.Count()} and the number of authorizations is {authorizations.Count}");
            if (countFragments != risks.Count()) throw new GameEngineException($"The number of risks is different than the number of authorizations. The number of risks is {risks.Count()} and the number of authorizations is {authorizations.Count}");

            var fragmentIndex = 0;
            foreach (Authorization authorization in authorizations)
            {
                var reference = references.ElementAt(fragmentIndex);
                var description = descriptions.ElementAt(fragmentIndex);
                var risk = risks.ElementAt(fragmentIndex);
                authorization.AppendFragments(Enumerable.Empty<string>().Append(reference), Enumerable.Empty<string>().Append(description), Enumerable.Empty<decimal>().Append(risk), toWin);
                fragmentIndex++;
            }
        }

        internal void AppendFragments(IEnumerable<string> references, IEnumerable<string> descriptions, IEnumerable<decimal> risks, IEnumerable<decimal> toWins)
        {
            var countFragments = references.Count();
            if (countFragments != descriptions.Count()) throw new GameEngineException($"The number of descriptions is different than the number of authorizations. The number of descriptions is {descriptions.Count()} and the number of authorizations is {authorizations.Count}");
            if (countFragments != risks.Count()) throw new GameEngineException($"The number of risks is different than the number of authorizations. The number of risks is {risks.Count()} and the number of authorizations is {authorizations.Count}");
            if (countFragments != toWins.Count()) throw new GameEngineException($"The number of toWins is different than the number of authorizations. The number of toWins is {toWins.Count()} and the number of authorizations is {authorizations.Count}");

            var fragmentIndex = 0;
            foreach (Authorization authorization in authorizations)
            {
                var reference = references.ElementAt(fragmentIndex);
                var description = descriptions.ElementAt(fragmentIndex);
                var risk = risks.ElementAt(fragmentIndex);
                var toWin = toWins.ElementAt(fragmentIndex);
                authorization.AppendFragments(Enumerable.Empty<string>().Append(reference), Enumerable.Empty<string>().Append(description), Enumerable.Empty<decimal>().Append(risk), Enumerable.Empty<decimal>().Append(toWin));
                fragmentIndex++;
            }
        }

        internal void CommitFragmentCreation()
        {
            foreach (Authorization authorization in authorizations)
            {
                authorization.CommitFragmentCreation();
            }
        }
    }
}