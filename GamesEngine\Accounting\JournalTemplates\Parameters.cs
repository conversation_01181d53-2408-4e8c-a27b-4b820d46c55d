﻿using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using System.Text;

namespace GamesEngine.Accounting.JournalTemplates
{
	internal class Parameters:Objeto
	{
		private readonly List<Parameter> parameters = new List<Parameter>();

        public IEnumerable<Parameter> GetAll 
		{ 
			get 
			{
				return parameters;
			} 
		}

		internal void AddTextParameter(TextParameter parameter)
		{
			if (parameter == null) throw new ArgumentNullException(nameof(parameter));

			parameters.Add(parameter);
		}

		internal void AddTextParameter(string name)
		{
			if (string.IsNullOrEmpty(name)) throw new ArgumentNullException(nameof(name));
			if (Exists(name)) throw new GameEngineException($"Parameter '{name}' is already included on this list of parameters. They must be has a unique name");

			Parameter parameter = new TextParameter(name);
			this.parameters.Add(parameter);
		}

		internal void AddTextParameter(string name, string description)
		{
			if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
			if (string.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));
			if (Exists(name)) throw new GameEngineException($"Parameter '{name}' is already included on this list of parameters. They must be has a unique name");

			Parameter parameter = new TextParameter(name, description);
			this.parameters.Add(parameter);
		}

		internal void UpdateTextParameter(string currentName, string description)
		{
			if (string.IsNullOrWhiteSpace(currentName)) throw new ArgumentNullException(nameof(currentName));
			if (string.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));
			if (! Exists(currentName)) throw new GameEngineException($"Parameter '{currentName}' does not exist on this list of parameters.");

			((TextParameter)this[currentName]).Update(description);
		}

		internal void AddAmountParameter(AmountParameter parameter)
		{
			if (parameter == null) throw new ArgumentNullException(nameof(parameter));

			parameters.Add(parameter);
		}

		internal void AddAmountParameter(string name)
		{
			if (string.IsNullOrEmpty(name)) throw new ArgumentNullException(nameof(name));
			if (Exists(name)) throw new GameEngineException($"Parameter '{name}' is already included on this list of parameters. They must be has a unique name");

			Parameter parameter = new AmountParameter(name);
			this.parameters.Add(parameter);
		}

		internal void AddAmountParameter(string name, string description, string formula)
		{
			if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
			if (string.IsNullOrWhiteSpace(formula)) throw new ArgumentNullException(nameof(formula));
			if (string.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));
			if (Exists(name)) throw new GameEngineException($"Parameter '{name}' is already included on this list of parameters. They must be has a unique name");

			Parameter parameter = new AmountParameter(name, description, formula);
			this.parameters.Add(parameter);
		}

		internal void UpdateAmountParameter(string currentName, string description, string formula)
		{
			if (string.IsNullOrWhiteSpace(currentName)) throw new ArgumentNullException(nameof(currentName));
			if (string.IsNullOrWhiteSpace(formula)) throw new ArgumentNullException(nameof(formula));
			if (string.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));
			if (!Exists(currentName)) throw new GameEngineException($"Parameter '{currentName}' does not exist on this list of parameters.");

			((AmountParameter)this[currentName]).Update(description, formula);
		}

		internal bool Exists(string name)
        {
			if (string.IsNullOrWhiteSpace(name)) return false;

			foreach (Parameter p in parameters)
            {
				if (p.Identifier == name) return true;
			}

			return false;
		}

		internal Parameter this[string name]
        {
            get
            {
				if (string.IsNullOrEmpty(name)) throw new ArgumentNullException(nameof(name));
				foreach(Parameter p in parameters) 
					if (p.Identifier == name) 
						return p;
				throw new GameEngineException($"Parameter {name} is unknown"); 
			}
        }

		internal void Add(Parameters parameters)
		{
			if (parameters == null) throw new ArgumentNullException(nameof(parameters));

			foreach (var parameter in parameters.GetAll)
            {
				if (parameter is AmountParameter)
                {
					if (parameter.HasDescription)
                    {
						AddAmountParameter(parameter.Identifier, parameter.Description, parameter.Formula);
					}
					else
                    {
						AddAmountParameter(parameter.Identifier);
					}
                }
				else if (parameter is TextParameter)
				{
					if (parameter.HasDescription)
					{
						AddTextParameter(parameter.Identifier, parameter.Description);
					}
					else
					{
						AddTextParameter(parameter.Identifier);
					}
				}
				else
                {
					throw new GameEngineException($"Parameter type '{parameter.GetType()}' is unknown");
				}
			}
		}
	}

	internal class FeeParameters
	{
		private readonly List<AmountParameter> parameters = new List<AmountParameter>();

		internal void Add(AmountParameter parameter)
		{
			parameters.Add(parameter);
		}

		internal void Remove(AmountParameter parameter)
		{
			parameters.Remove(parameter);
		}

		internal bool Contains(AmountParameter parameter)
		{
			return parameters.Contains(parameter);
		}
	}
}
