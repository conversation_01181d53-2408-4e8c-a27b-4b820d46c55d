﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace GamesEngine.Bets
{
	internal sealed class Pool : Reward
    {
        private enum State { OPEN, LOCKED }
        private State state = State.OPEN;
        private Dictionary<Player, decimal> prices;
        private readonly decimal? fee;
        
        internal Pool (int id, String name, decimal fee) : base(id, name) 
        {
            if (fee <= 0) throw new GameEngineException($"Fee {fee} should be greater than zero.");
            this.fee = fee;
        }

        internal Pool (int id, String name) : base(id, name)
        {
        }

        internal decimal Fee
        {
            get
            {
                if (!fee.HasValue) throw new GameEngineException("This pool does not hava a fixed fee");
                return fee.Value;
            }
        }

        internal override void Contribute(Player player, decimal amount)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (fee.HasValue && amount != this.fee) throw new GameEngineException($"This pool requires that participants contribute with ${this.fee}.");
            if (! fee.HasValue && amount <= 0) throw new GameEngineException($"This pool requires that participants contribute with an amount grather than zero.");
            base.Contribute(player, amount);
        }

        internal void Winners (params Player[] winners)
        {
            if (state != State.OPEN) throw new GameEngineException("Pool is already locked. Winners were defined once and they can not be overridden");
            if (winners == null || winners.Length == 0) throw new ArgumentNullException(nameof(winners));
            foreach (Player player in winners)
            {
                if ( ! Participates(player) ) throw new GameEngineException($"Player '{player.Nickname}' is not a member of this pool. Therefore, He or She can not be a winner");
            }
            prices = new Dictionary<Player, decimal>();
            decimal remaining = this.GrandPrice;
            decimal portion = remaining / winners.Length;
            
            foreach (Player player in winners)
            {
                if (remaining < portion)
                {
                    prices.Add(player, remaining);
                    remaining = 0;
                }
                else
                {
                    prices.Add(player, portion);
                    remaining = remaining - portion;
                }
            }
            if (remaining != 0) throw new GameEngineException($"The complete Price must be splitted, but {remaining} remains to be shared");
            state = State.LOCKED;
        }

        internal bool IsLocked 
        {
            get
            {
                return state == State.LOCKED;
            }
        }

        internal decimal PriceFor (Player player)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if ( ! Participates(player)) throw new GameEngineException($"Player '{player.Nickname}' is not a member of this pool. Therefore, He or She can not be a winner.");
            if (state != State.LOCKED) throw new GameEngineException("Game is still on process. Players can not know their price until game is finished.");
            decimal price = prices.Where(x => x.Key == player).Sum(x => x.Value);
            return price;
        }
    }
}
