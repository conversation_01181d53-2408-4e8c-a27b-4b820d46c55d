﻿using GamesEngine;
using GamesEngine.Settings;
using Microsoft.Extensions.Configuration;
using MySql.Data.MySqlClient;
using Puppeteer;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Interprete;
using Puppeteer.EventSourcing.Interprete.Libraries;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;

namespace CashierAPI.CashierFollower
{
    public class CashierActorFollower : ActorFollower
    {
        public CashierActorFollower(CashierActor actor, int followerId) : base(actor, followerId, conEstado: false)
        {

        }

        public override void CaptureException(Exception ex)
        {
            APMHelper.CaptureException(ex);
        }

        public override void CaptureException(Exception ex, string parent)
        {
            APMHelper.CaptureException(ex, parent);
        }

        public override void StartSpan(string spanName, string spanType)
        {
            APMHelper.StartSpan(spanName, spanType);
        }

        public override void EndSpan(string spanName)
        {
            APMHelper.EndSpan(spanName);
        }

        public override void DisposeSpan(string spanName)
        {
            APMHelper.DisposeSpan(spanName);
        }

    }
    public class CashierFollower
    {
        private IConfiguration configuration;

        public static BlockingCollection<string> sqlQueue;

        public static void MainFollower(string daysRange)
        {
            APMHelper.CurrentTransaction();

            try
            {
                var followerImp = new CashierFollower();
                var culture = new CultureInfo("en-US");
                CultureInfo.DefaultThreadCurrentCulture = culture;
                CultureInfo.DefaultThreadCurrentUICulture = culture;

            //MovementDetailsInfo.DB_Load_Accounts_LRU_SP(followerImp.Dbtype, followerImp.ConnectionString);

                DBHelperCashierFollower.LoadDbConnection(followerImp.Dbtype, followerImp.ConnectionString);

                IEnumerable<string> allAccounts = GetAccountForSkip(daysRange, followerImp.Dbtype, followerImp.ConnectionString);
                if (allAccounts == null || Enumerable.Count(allAccounts) == 0) return;

                Console.WriteLine("Cashier Follower is running.");
                Console.WriteLine("Press Ctrl+C to finish correctly the follower.");
                int count = 0;

                using (sqlQueue = new BlockingCollection<string>())
                {
                    _ = Task.Run(() =>
                    {
                        try
                        {
                            foreach (var account in allAccounts)
                            {
                                APMHelper.StartSpan(account, account);

                                MovementDetailsInfo.CreateTableAffectedIds(followerImp.Dbtype);

                                count++;

                                var actor = new GamesEngine.CashierActor(account);
                                var follower = new CashierActorFollower(actor, 1);
                                DBHelperCashierFollower.InitStatusForAccount(account);
                                LastIdRule.GetLastId(followerImp.ConnectionString, followerImp.Dbtype, account);
                                InitialBalanceHandler.FetchLastIds(followerImp.ConnectionString, followerImp.Dbtype, account);

                                Console.WriteLine($"Processing {count} of {allAccounts.Count()} actor: " + account);
                                Console.CancelKeyPress += delegate
                                {
                                    follower.ForceToEnd();
                                    Console.WriteLine("Finish message has been received (Ctrl+C) wait a minute to normal exit...");
                                };

                                follower.AddRule(new InitialBalanceFPRule());
                                follower.AddRule(new AccreditFPRule());
                                follower.AddRule(new NewAccreditRule());
                                follower.AddRule(new WithdrawRule());
                                follower.AddRule(new CreateAuthorizationRule());
                                follower.AddRule(new NewCreateAuthorizationRule());
                                follower.AddRule(new FragmentCreationRule());
                                follower.AddRule(new FragmentCreationRuleWithFragment());
                                follower.AddRule(new FragmentCreationRuleWithInitFragment());
                                follower.AddRule(new FragmentPaymentRule());
                                follower.AddRule(new FragmentsStatusRule());
                                follower.AddRule(new ExecutionErrorRule());
                                follower.AddRule(new RemoveExpiredFragmentsRule());

                                follower.Run(followerImp.Dbtype, followerImp.ConnectionString);

                                MovementDetailsInfo.UpdateSkipIds(account, followerImp.Dbtype);

                                APMHelper.EndSpan(account);
                                APMHelper.DisposeSpan(account);
                            }
                        }
                        catch (Exception e) //As is inside a fire and forget, this is needed to see the error and execute the CompleteAdding unlocking the main process.
                        {
                            Console.WriteLine(e);
                            APMHelper.CaptureException(e);
                        }
                        finally
                        {
                            sqlQueue.CompleteAdding();
                        }
                    });

                    if (followerImp.Dbtype == DatabaseType.MySQL)
                    {
                        using (MySqlConnection connection = new MySqlConnection(followerImp.ConnectionString))
                        {
                            APMHelper.StartSpan(nameof(DatabaseType.MySQL), "db");
                            foreach (var sql in sqlQueue.GetConsumingEnumerable())
                            {
                                try
                                {
                                    connection.Open();
                                    using (MySqlCommand command = new MySqlCommand(sql, connection))
                                    {
                                        command.CommandType = CommandType.Text;
                                        command.ExecuteNonQuery();
                                    }
                                }
                                catch(Exception e)
                                {
                                    APMHelper.CaptureException(e);
                                }
                                finally
                                {
                                    connection.Close();
                                }
                            }
                            APMHelper.EndSpan(nameof(DatabaseType.MySQL));
                            APMHelper.DisposeSpan(nameof(DatabaseType.MySQL));
                        }
                    }
                    else if (followerImp.Dbtype == DatabaseType.SQLServer)
                    {
                        List<Task> sqlTasks = new List<Task>();

                        foreach (var sql in sqlQueue.GetConsumingEnumerable())
                        {
                            var task = Task.Run(() =>
                            {
                                using (SqlConnection connection = new SqlConnection(followerImp.ConnectionString))
                                {
                                    try
                                    {
                                        connection.Open();
                                        using (SqlCommand command = new SqlCommand(sql, connection))
                                        {
                                            command.CommandType = CommandType.Text;
                                            command.ExecuteNonQuery();
                                        }
                                    }
                                    catch(Exception e)
                                    {
                                        APMHelper.CaptureException(e);
                                    }
                                    finally
                                    {
                                        connection.Close();
                                    }
                                }
                            });

                            sqlTasks.Add(task);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                APMHelper.CaptureException(ex);
            }
            finally
            {
                APMHelper.EndTransaction();
            }
        }

        private static IEnumerable<string> GetAccountForSkip(string daysRange, DatabaseType dbType, string connectionString)
        {
            IEnumerable<string> allAccounts = new List<string>();

            if (String.IsNullOrEmpty(daysRange)) return null;

            int minimumActityDays = 0;

            bool hasDaysRange = Int32.TryParse(daysRange, out minimumActityDays);
            if (hasDaysRange)
            {
                allAccounts = DBHelperCashierFollower.CashierAccountsRecentlyUse(dbType, connectionString, minimumActityDays);
            }
            else if (daysRange.IndexOf("all") != -1)
            {
                allAccounts = DBHelperCashierFollower.CashierAccountsRecentlyUse(dbType, connectionString);
            }
            else
            {
                Console.WriteLine("Arguments are not valid, using default to run Cashier.");
            }

            return allAccounts;
        }

        private DatabaseType Dbtype
        {
            get
            {
                switch (Integration.DbSelected)
                {
                    case "SQLServer":
                        return DatabaseType.SQLServer;
                    case "MySQL":
                        return DatabaseType.MySQL;
                    default:
                        throw new Exception($"There is no connection for {Integration.DbSelected}");
                }
            }
        }

        private string ConnectionString
        {
            get
            {
                switch (Dbtype)
                {
                    case DatabaseType.SQLServer:
                        var sqlServer = Integration.SqlServer;
                        return sqlServer;
                    case DatabaseType.MySQL:
                        var mySQL = Integration.MySQL;
                        return mySQL;
                    default:
                        throw new Exception($"There is no connection for {Dbtype}");
                }
            }
        }

        private string ScriptBeforeRecovering
        {
            get
            {
                var scriptBeforeRecovering = configuration.GetValue<string>("ActionsBeforeRecovering");
                return scriptBeforeRecovering;
            }
        }
    }
}
