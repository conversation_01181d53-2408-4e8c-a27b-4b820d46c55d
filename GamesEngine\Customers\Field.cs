﻿using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
[assembly: InternalsVisibleTo("KnowYourCustomerAPI")]

namespace GamesEngine.Customers
{
    [Puppet]
    public abstract class Field : Objeto
    {
        internal string Label { get; }
        internal string Id { get; }
        internal string Notes { get; private set; } = string.Empty;
        internal bool Verifiable { get; set; }
        internal bool Unique { get; set; }

        public Field(string label, string id)
        {
            if (string.IsNullOrWhiteSpace(label)) throw new ArgumentNullException(nameof(label));
            if (string.IsNullOrWhiteSpace(id)) throw new ArgumentNullException(nameof(id));

            Label = label;
            Id = id;
        }

        internal abstract bool HasMultipleInputs();

        internal void AddNotes(string notes)
        {
            Notes = notes;
        }
    }

    [Puppet]
    internal sealed class SingleField : Field
    {
        internal Input Input { get; }

        public SingleField(string label, string id, Input input) : base (label, id)
        {
            if (input == null) throw new ArgumentNullException(nameof(input));

            Input = input;
        }

        internal override bool HasMultipleInputs()
        {
            return false;
        }
    }

    [Puppet]
    internal sealed class MultipleField : Field
    {
        private readonly Fields fields;

        public MultipleField(string label, string id, Fields fields) : base(label, id)
        {
            if (fields == null) throw new ArgumentNullException(nameof(fields));
            if (fields.Count() < 2) throw new GameEngineException($"{nameof(fields)} collector contains {fields.Count()} fields. It must contain at least 2 fields");

            this.fields = fields;
        }

        internal override bool HasMultipleInputs()
        {
            return true;
        }

        internal IEnumerable<Field> SubFields()
        {
            return fields.List();
        }
    }

    [Puppet]
    public sealed class Fields:Objeto
    {
        public const string PREFIX_VERIFIABLE = "verifiable_";
        public const string ID_IDENTIFICATION_NUMBER = "identification_number";
        public const string ID_ACCOUNT_NUMBER = "account_number";
        public const string ID_AVATAR = "avatar";
        public const string ID_USERNAME = "username";
        public const string ID_PASSWORD = "password";
        public const string ID_NICKNAME = "nick_name";
        public const string ID_EMAIL = "email";
        public const string ID_BIRTHDATE = "DOB";
        public const string ID_FIRST_NAME = "first_name";
        public const string ID_LAST_NAME = "last_name";
        public const string ID_DOCUMENTS_SUBMITTED = "documents_submitted";
        public const string ID_PROFILE = "profile_id";

        private readonly Dictionary<string, Field> fields = new Dictionary<string, Field>();

        internal Field CreateSingleField(string label, string id, Input input)
        {
            if (string.IsNullOrWhiteSpace(label)) throw new ArgumentNullException(nameof(label));
            if (string.IsNullOrWhiteSpace(id)) throw new ArgumentNullException(nameof(id));
            if (fields.ContainsKey(id)) throw new GameEngineException($"{nameof(id)} '{id}' already exists");
            if (input == null) throw new ArgumentNullException(nameof(input));

            var field = new SingleField(label, id, input);
            fields.Add(id, field);
            return field;
        }

        internal Field CreateMultipleField(string label, string id, Fields fields)
        {
            if (string.IsNullOrWhiteSpace(label)) throw new ArgumentNullException(nameof(label));
            if (string.IsNullOrWhiteSpace(id)) throw new ArgumentNullException(nameof(id));
            if (this.fields.ContainsKey(id)) throw new GameEngineException($"{nameof(id)} '{id}' already exists");
            if (fields == null) throw new ArgumentNullException(nameof(fields));
            if (fields.Count() < 2) throw new GameEngineException($"{nameof(fields)} collector contains {fields.Count()} fields. It must contain at least 2 fields");

            var field = new MultipleField(label, id, fields);
            this.fields.Add(id, field);
            return field;
        }

        internal Field SearchFieldById(string id)
        {
            if (string.IsNullOrWhiteSpace(id)) throw new ArgumentNullException(nameof(id));
            var field = fields.GetValueOrDefault(id);
            if (field == null) throw new GameEngineException($"{nameof(field)} '{field}' does not exist");
            return field;
        }

        internal IEnumerable<Field> List()
        {
            return fields.Values.ToList();
        }

        internal int Count()
        {
            return fields.Count;
        }
    }
}
