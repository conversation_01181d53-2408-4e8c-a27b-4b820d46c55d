﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using GamesEngine.Business;
using GamesEngine.Games.Lotto;
using GamesEngine.Gameboards;
using GamesEngine.Tools;
using Puppeteer.EventSourcing.Libraries;

namespace GamesEngine.Bets
{
    internal abstract class Bet : Objeto, IComparable<Bet>
    {
        internal enum BetState { ALIVE, LOCKED, PUNCHED, PAYED, CANCELED }

        private readonly Player player;
        private readonly Gameboard gameboard;
        private readonly Reward reward;
        private readonly DateTime creationDate;
        protected int authorizationId;
        private int number;
        private decimal contribution;

        protected bool Invalid { get; set; } = false;

		[Obsolete("Do not use it is for legacy purposes")]
        protected Bet(Player player, Gameboard gameboard, Reward reward, DateTime creationDate)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (gameboard == null) throw new ArgumentNullException(nameof(gameboard));
            if (reward == null) throw new ArgumentNullException(nameof(reward));
            if (creationDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");

            this.player = player;
            this.gameboard = gameboard;
            this.reward = reward;
            this.creationDate = creationDate;
            number = player.Company.NextBetNumber();
        }

		protected Bet(Player player, Gameboard gameboard, Reward reward, DateTime creationDate, int nextBetNumber)
		{
			if (player == null) throw new ArgumentNullException(nameof(player));
			if (gameboard == null) throw new ArgumentNullException(nameof(gameboard));
			if (reward == null) throw new ArgumentNullException(nameof(reward));
			if (creationDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");

			this.player = player;
			this.gameboard = gameboard;
			this.reward = reward;
			this.creationDate = creationDate;
			number = nextBetNumber;
		}

		protected Bet(Bet bet)
        {
            if (bet.Contribution <= 0) throw new GameEngineException($"Bet amount can not be {bet.Contribution}. Amount must be greater than zero");
            this.contribution = bet.Contribution;
            this.number = bet.number;
            this.authorizationId = bet.authorizationId;

            this.player = bet.player;
            this.gameboard = bet.gameboard;
            this.reward = bet.reward;
            this.creationDate = bet.creationDate;
        }

        internal void Invalidate()
        {
            Invalid = true;
        }

        internal Player Player
        {
            get
            {
                return player;
            }
        }

        internal Reward Reward
        {
            get
            {
                return reward;
            }
        }

        internal Gameboard Gameboard
        {
            get
            {
                return gameboard;
            }
        }

        internal DateTime CreationDate
        {
            get
            {
                return creationDate;
            }
        }

        protected void SetContribution(decimal contribution)
        {
            if (contribution <= 0) throw new GameEngineException($"Bet amount can not be {contribution}. Amount must be greater than zero");
            if (State != BetState.ALIVE) throw new GameEngineException($"Bet state is {State} but it can only changed when it's on {BetState.ALIVE}.");
            Commons.ValidateAmount(contribution);
            this.contribution = contribution;
        }

		internal decimal Contribution
        {
            get
            { 
                return contribution;
            }
            set
            {
                if (value <= 0) throw new GameEngineException($"{nameof(contribution)} must be greater than zero");
                contribution =value;
            }
        }

        internal virtual BetState State
        {
            get
            {
                throw new GameEngineException("State must be implement by classes that Inherit from Bet.");
            }
        }

        internal int AuthorizationId
        {
            get
            {
                return authorizationId;
            }
            set
            {
                this.authorizationId = value;
            }
        }

        protected int lastAuthorizationIdOfReturnedPrize;
        internal int LastAuthorizationIdOfReturnedPrize
        {
            get
            {
                return lastAuthorizationIdOfReturnedPrize;
            }
            set
            {
                lastAuthorizationIdOfReturnedPrize = value;
            }
        }

        internal int Number
        {
            get
            {
                return number;
            }
			set
			{
				PicksLotteryGame.correctos[value] =  this.number;
			}
		}

        int IComparable<Bet>.CompareTo(Bet other)
        {
            return this.number - other.number;
        }
    }
}
