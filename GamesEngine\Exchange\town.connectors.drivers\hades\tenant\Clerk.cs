﻿using Puppeteer.EventSourcing;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.commons;
using town.connectors.drivers;
using town.connectors.drivers.hades;

namespace Connectors.town.connectors.drivers.hades
{
	public class Clerk : ASITenantDriver
	{
		private RestClient _getCasinoInfo;
		private string SystemId;
        private string SystemPassword;
		public Clerk() : base(Tenant_Actions.Others)
		{
		}
		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
			if (_getCasinoInfo == null)
			{
				string companyBaseUrlServices = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
				_getCasinoInfo = new RestClient(companyBaseUrlServices);
			}

			if (CustomSettings.ThereArePendingChanges)
			{
				bool changeApplied = false;
				string companyBaseUrlServices = CustomSettings.Get(now, "CompanyBaseUrlServices", out changeApplied).AsString;
				if (changeApplied)
				{
					_getCasinoInfo = new RestClient(companyBaseUrlServices);
				}

				SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
				SystemPassword = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
			}

			var CustomerId = recordSet.Mappings["customerId"];

			var result= RequestInetTag(now, CustomerId.AsString);
			return (T)Convert.ChangeType(result, typeof(T));
		}

		public override void Prepare(DateTime now)
		{
			CustomSettings.AddVariableParameter("customerId");

			//CustomSettings.Prepare();

			SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
			SystemPassword = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
		}
		public override Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet) 
		{
			throw new NotImplementedException();
		}
		private string RequestInetTag(DateTime now, string customerId)
		{
			if (String.IsNullOrWhiteSpace(customerId)) throw new ArgumentNullException(nameof(customerId));

			CasinoInfoResponse result = GetCustomerInfoCasino(now, customerId);
			var inetName = (result == null || string.IsNullOrWhiteSpace(result.InetTarget)) ? "Unknow" : result.InetTarget;
			return inetName;
		}
		private CasinoInfoResponse GetCustomerInfoCasino(DateTime now, string customerId)
		{
			if (String.IsNullOrWhiteSpace(customerId)) throw new ArgumentNullException(nameof(customerId));

			var values = new CustomerCasino()
			{
				SystemId = SystemId,
				SystemPassword = SystemPassword,
				CustomerId = customerId
			};

			const string url = "/v1/5dimesAPI/GetCustomerInfoCasino";
			var jsonString = Commons.ToJson(values);
			var httpContent = new StringContent(jsonString, Encoding.UTF8, "application/json");

			string valuesWithHiddenFields = ASIJsonUtils.HideSensitiveData(values, Loggers.GetIntance().AccountingServicesASI);

			string responseString = "";
			int retryNumber = 0;
			while (true)
			{
				try
				{
					var request = new RestRequest(url, Method.POST);
					request.AddHeader("Content-Type", "application/json");
					request.AddParameter("application/json", jsonString, ParameterType.RequestBody);
					var response = _getCasinoInfo.Execute(request);
					responseString = response.Content;

					return Commons.FromJson<CasinoInfoResponse>(responseString);
				}
				catch (Exception e)
				{
					Loggers.GetIntance().AccountingServicesASI.Error($@"url:{url} data:{valuesWithHiddenFields} type:{e.GetType()} error:{e.Message}", e);

					retryNumber++;

					Thread.Sleep(5000);
					if (retryNumber == MAX_RETRIES)
					{
						NotifyWarn(nameof(GetCustomerInfoCasino), $"Url:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}", $"Response can not be empty");
						throw e;
					}
				}
			}

		}
    }
}
