﻿using GamesEngine.MessageQueuing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using town.connectors.drivers;

namespace GamesEngine.Business
{
    internal class WholePaymentMethods : WholeCatalog
    {
        internal WholePaymentMethods(Company company) : base(company)
        {
        }

        internal CatalogMember Add(bool itIsThePresent, string name)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (Exists(name)) throw new GameEngineException($"{nameof(name)} '{name}' is already used");
            
            nextConsecutive = NextConsecutive();
            var method = new ProcessorPaymentMethod(nextConsecutive, name);
            Add(method);
            if (itIsThePresent && (Settings.Integration.UseKafka || Settings.Integration.UseKafkaForAuto))
            {
                var msg = new PaymentMethodAddMessage(nextConsecutive, name);
                Settings.Integration.Kafka.Send(itIsThePresent, Settings.Integration.Kafka.TopicForCatalog, msg);
            }
            return method;
        }

        override internal CatalogMember Add(int id, string name)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} {id} must be greater than 0");
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (Exists(name)) throw new GameEngineException($"{nameof(name)} '{name}' is already used");
            if (Exists(id)) throw new GameEngineException($"{nameof(id)} '{id}' is already used");

            if (id > nextConsecutive) nextConsecutive = id;
            var method = new ProcessorPaymentMethod(id, name);
            members.Add(method);
            return method;
        }

        internal class ProcessorPaymentMethod : CatalogMember
        {
            List<ProviderPaymentMethod> providers;
            internal IEnumerable<ProviderPaymentMethod> Providers
            {
                get
                {
                    return providers;
                }
            }

            public ProcessorPaymentMethod(int id, string name) : base(id, name)
            {

            }

            internal bool HasProviders()
            {
                return providers != null && providers.Count > 0;
            }

            internal void AddProvider(int id, string name)
            {
                if (providers == null) providers = new List<ProviderPaymentMethod>();
                providers.Add(new ProviderPaymentMethod(id, name));
            }

            internal PaymentMethod AsEnum()
            {
                var successful = Enum.TryParse(Name, out PaymentMethod paymentMethod);
                if (!successful) throw new GameEngineException($"{nameof(paymentMethod)} '{paymentMethod}' does not exist as enum");
                return paymentMethod;
            }
        }

        internal class ProviderPaymentMethod : Objeto
        {
            internal int Id { get; private set; }
            internal string Name { get; private set; }

            public ProviderPaymentMethod(int id, string name)
            {
                Id = id;
                Name = name;
            }


        }
    }
}
