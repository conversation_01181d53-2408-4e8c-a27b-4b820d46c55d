﻿using ExchangeAPI.ExchangeEntities;
using ExchangeAPI.Model;
using ExchangeAPI.Service;
using GamesEngine;
using GamesEngine.Exchange.Persistance;
using GamesEngine.Settings;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ExchangeAPI.Logic
{
    public class Deposits : IDeposits
    {
        private const string ALL_SELECTED = "all";
        private const int MAXIMUM_AMOUNT_OF_ROWS_ALLOWED = 100;

        public static DepositsDTO FilteredBy(DateTime startDate, DateTime endDate, string state, string identificationNumber, string transactionId, string cashierName, string domain, 
            int initialIndex, int amountOfRows)
        {
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate.Hour != 0 || startDate.Minute != 0 || startDate.Second != 0) throw new GameEngineException($"{nameof(startDate)} {startDate} is not valid");
            if (endDate.Hour != 0 || endDate.Minute != 0 || endDate.Second != 0) throw new GameEngineException($"{nameof(endDate)} {endDate} is not valid");
            if (string.IsNullOrWhiteSpace(state)) throw new ArgumentNullException(nameof(state));
            if (string.IsNullOrWhiteSpace(identificationNumber)) throw new ArgumentNullException(nameof(identificationNumber));
            if (string.IsNullOrWhiteSpace(transactionId)) throw new ArgumentNullException(nameof(transactionId));
			if (string.IsNullOrWhiteSpace(cashierName)) throw new ArgumentNullException(nameof(cashierName));
            if (string.IsNullOrWhiteSpace(domain)) throw new ArgumentNullException(nameof(domain));
            if (initialIndex < 0) throw new GameEngineException($"{nameof(initialIndex)} {initialIndex} is not valid");
            if (amountOfRows <= 0) throw new GameEngineException($"{nameof(amountOfRows)} {amountOfRows} is not valid");

            if (amountOfRows > MAXIMUM_AMOUNT_OF_ROWS_ALLOWED)
            {
                amountOfRows = MAXIMUM_AMOUNT_OF_ROWS_ALLOWED;
            }

            var allStatesSelected = state.ToLower().Trim() == ALL_SELECTED;
            var allIdentificationNumberSelected = identificationNumber.ToLower().Trim() == ALL_SELECTED;
            var allTransactionIdsSelected = transactionId.ToLower().Trim() == ALL_SELECTED;
            var allCashiersSelected = cashierName.ToLower().Trim() == ALL_SELECTED;
            var allDomainsSelected = domain.ToLower().Trim() == ALL_SELECTED;

            using (var context = new ExchangeContext())
            {
                var deposits = context.Deposit
                    .Include(deposit => deposit.WhocreatedNavigation)
                    .Include(deposit => deposit.DomainNavigation)
                    .Include(deposit => deposit.Fromcurrency)
                    .Include(deposit => deposit.Tocurrency)
                    .Where(deposit => !deposit.Deleted && deposit.Datecreated.Date >= startDate && deposit.Datecreated.Date <= endDate
                        && (allStatesSelected || (deposit.Approvals == deposit.Approvalsrequired && state == TransactionState.Approved.ToString()) || (deposit.Rejections > 0 && state == TransactionState.Rejected.ToString()) || (deposit.Approvals == 0 && deposit.Rejections == 0 && state == TransactionState.Pending.ToString()))
                        && (allIdentificationNumberSelected || deposit.Tocustomer == identificationNumber)
                        && (allTransactionIdsSelected || deposit.Id.ToString() == transactionId)
                        && (allCashiersSelected || deposit.WhocreatedNavigation.Name == cashierName)
                        && (allDomainsSelected || deposit.DomainNavigation.Domain == domain)
                        )
                    .OrderByDescending(deposit => deposit.Datecreated);
                var depositsPaged = deposits
                    .Skip(initialIndex)
                    .Take(amountOfRows);
                var depositsDTO = depositsPaged.MapToDepositsDTO();
                depositsDTO.TotalRecords = deposits.Count();
                return depositsDTO;
            }
        }

		private static Deposit DepositFor(int id)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");

            using (var context = new ExchangeContext())
            {
                var depositFound = context.Deposit
                    .Include(transfer => transfer.Exchangerate)
                    .Where(deposit => ! deposit.Deleted && deposit.Id == id)
                    .FirstOrDefault();
                return depositFound;
            }
        }

        void IDeposits.Save(Deposit deposit)
        {
            if (deposit == null) throw new ArgumentNullException(nameof(deposit));

            using (var context = new ExchangeContext())
            {
                context.Deposit.Add(deposit);
                context.SaveChanges();
            }
        }

        public static Deposit UpdateAsApproved(ExchangeContext context, int id, int authorizationId, DateTime date, decimal profit, decimal purchase, decimal sale, string employeeName, int processorId)
        {
            if (context == null) throw new ArgumentNullException(nameof(context));
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");
            if (authorizationId <= 0) throw new GameEngineException($"{nameof(authorizationId)} must be greater than 0");

            var user = Integration.ExchangeUsers.User(employeeName);
            var deposit = DepositFor(id);
            if (deposit == null) throw new ArgumentNullException(nameof(deposit));

            deposit.Authorizationid = authorizationId;
            deposit.Approvals = 1;
            deposit.Whoapproved = user.Id;
            deposit.Dateapproved = date;
            deposit.Net = purchase;
            deposit.Profit = profit;
            deposit.Amount = sale;
            deposit.Processorid = processorId;
            context.Deposit.Update(deposit);
            context.SaveChanges();
            return deposit;
        }

        public static Deposit UpdateJournalEntry(ExchangeContext context, int id, string journalEntryDetailId)
        {
            if (context == null) throw new ArgumentNullException(nameof(context));
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(journalEntryDetailId)) throw new ArgumentNullException(nameof(journalEntryDetailId));

            var deposit = DepositFor(id);
            if (deposit == null) throw new ArgumentNullException(nameof(deposit));

            deposit.Journalentrydetailid = journalEntryDetailId;
            context.Deposit.Update(deposit);
            context.SaveChanges();
            return deposit;
        }

        public static Deposit UpdateAsRejected(int id, DateTime date, string rejectionReason, string employeeName)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");

            var user = Integration.ExchangeUsers.User(employeeName);
            var deposit = DepositFor(id);
            if (deposit == null) throw new ArgumentNullException(nameof(deposit));

            deposit.Rejections = 1;
            deposit.Whorejected = user.Id;
            deposit.Daterejected = date;
            deposit.Rejectionreason = rejectionReason;
            using (var context = new ExchangeContext())
            {
                context.Deposit.Update(deposit);
                context.SaveChanges();
            }
            return deposit;
        }

        public static Deposit UpdateVoucherUrl(int id, string voucherUrl)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(voucherUrl)) throw new ArgumentNullException(nameof(voucherUrl));

            var deposit = DepositFor(id);
            if (deposit == null) throw new ArgumentNullException(nameof(deposit));

            deposit.Voucherurl = voucherUrl;
            using (var context = new ExchangeContext())
            {
                context.Deposit.Update(deposit);
                context.SaveChanges();
            }
            return deposit;
        }
    }
}
