﻿using GamesEngine.Gameboards;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamesEngine.Bets
{
    internal sealed class BetPayed : Bet
    {
        private int authorizationIdForPrize;
        private readonly decimal amountToPay;
        private readonly decimal amountApproved;

        internal decimal AmountToPay
        {
            get { return amountToPay; }
        }

        internal decimal AmountApproved
        {
            get { return amountApproved; }
        }

        internal BetPayed(BetLocked bet) : base(bet)
        {
            bet.Invalidate();
            amountApproved = bet.AmountApproved;
            amountToPay = bet.AmountToPay;
            this.authorizationIdForPrize = bet.AuthorizationId;
        }

        internal override BetState State
        {
            get
            {
                return BetState.PAYED;
            }
        }

        internal Bet Lock()
        {
            if (Invalid) throw new GameEngineException("This bet was already processed and it can not be processed twice.");
            
            return new BetLocked(this);
        }

        internal new int AuthorizationId
        {
            get
            {
                return authorizationIdForPrize;
            }
            set
            {
                this.authorizationIdForPrize = value;
            }
        }
    }
}
