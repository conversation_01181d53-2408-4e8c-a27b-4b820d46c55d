﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace GamesEngine.Settings
{
	public class RestAPIActorAsync : RestAPIActor
	{
		public bool AreMovementsAndFragmentsTablesCreated { get; set; }

		public RestAPIActorAsync(Actor actor):base(actor){}

		public DateTime DateOfLastActivity
		{
			get
			{
				return base.actor.DateOfLastActivity;
			}
		}

		public bool ItsANewOne
		{
			get
			{
				return base.actor.ItsANewOne;
			}
		}

		public Actor Actor
		{
			get
			{
				return base.actor;
			}
		}

        public bool IsKenoActor()
        {
            return base.actor is KenoActor;
		}

        public bool IsLottoActor()
        {
            return base.actor is LottoActor;
        }


        public string LockWhileNotSyncronized()
        {
            return actor.LockWhileNotSyncronized();
        }

        public void UnlockAndRunAlive()
        {
            actor.UnlockAndRunAlive();
        }


        public new void EventSourcingStorage(DatabaseType dbType, string connection, string scriptBeforeRecovering, string needsUniqueIdentifierForPaymentHub)
		{
			base.actor.EventSourcingStorage(dbType, connection, scriptBeforeRecovering, needsUniqueIdentifierForPaymentHub);
		}

		public async Task<IActionResult> PerformCmdAsync(HttpContext context, string script)
		{
			IpAddress ip = RestAPIActor.SearchUserIp(context);
			UserInLog user = RestAPIActor.SearchUser(context);
			return await PerformAsync(ip, user, false, context, null, script);
		}

		public async Task<IActionResult> PerformChkThenCmdAsync(HttpContext context, string validation, string script)
		{
			if (String.IsNullOrWhiteSpace(validation)) return new NotFoundErrorObjectResult();

			IpAddress ip = RestAPIActor.SearchUserIp(context);
			UserInLog user = RestAPIActor.SearchUser(context);

			return await PerformAsync(ip, user, false, context, validation, script);
		}

		internal async Task<IActionResult> PerformChkThenCmdAsync(string validation, string script)
		{
			if (String.IsNullOrWhiteSpace(validation)) return new NotFoundErrorObjectResult();

			IpAddress ip = IpAddress.DEFAULT;
			UserInLog user = UserInLog.ANONYMOUS;

			return await PerformAsync(ip, user, false, null, validation, script);
		}

		public async Task<IActionResult> PerformChkThenQryAsync(HttpContext context, string validation, string script)
		{
			if (String.IsNullOrWhiteSpace(validation)) return new NotFoundErrorObjectResult();

			IpAddress ip = RestAPIActor.SearchUserIp(context);
			UserInLog user = RestAPIActor.SearchUser(context);

			return await PerformAsync(ip, user, true, context, validation, script);
		}

		//Versiones con chkscript y no chkscript 
		public IActionResult PerformCmd(string script)
		{
			return base.Perform(IpAddress.DEFAULT, UserInLog.ANONYMOUS, false, null, null, script);
		}

		public IActionResult PerformQry(string script)
		{
			return base.Perform(IpAddress.DEFAULT, UserInLog.ANONYMOUS, true, null, null, script);
		}

		public async Task<IActionResult> PerformQryAsync(string script)
		{
			return await PerformAsync(IpAddress.DEFAULT, UserInLog.ANONYMOUS, true, null, null, script);
		}

		public async Task<IActionResult> PerformQryAsync(HttpContext context, string script)
		{
			IpAddress ip = RestAPIActor.SearchUserIp(context);
			UserInLog user = RestAPIActor.SearchUser(context);
			return await PerformAsync(ip, user, true, context, null, script);
		}

		internal async Task<IActionResult> PerformQryAsync(IpAddress ip, UserInLog user, HttpContext context, string script)
		{
			return await PerformAsync(ip, user, true, context, null, script);
		}

		internal IActionResult PerformTrim(HttpContext context, DateTime trimmedDown)
		{
			IpAddress ip = SearchUserIp(context);
			UserInLog user = SearchUser(context);

			return PerformTrim(ip, user, trimmedDown);
		}

		public IActionResult PerformArchive(HttpContext context, DateTime startDate, DateTime endDate)
		{
			IpAddress ip = RestAPIActor.SearchUserIp(context);
			UserInLog user = RestAPIActor.SearchUser(context);

			return PerformArchive(ip, user, startDate, endDate);
		}

		public IActionResult DoQry(HttpContext context, Func<Puppeteer.EventSourcing.Actor, Puppeteer.EventSourcing.IpAddress, Puppeteer.EventSourcing.UserInLog, System.Threading.ReaderWriterLockSlim, string> go)
		{
			IpAddress ip = SearchUserIp(context);
			UserInLog user = SearchUser(context);
			return base.DoQry(ip, user, context, go);
		}

		public IActionResult DoQry(Func<Puppeteer.EventSourcing.Actor, Puppeteer.EventSourcing.IpAddress, Puppeteer.EventSourcing.UserInLog, System.Threading.ReaderWriterLockSlim, string> go)
		{
			return base.DoQry(IpAddress.DEFAULT, UserInLog.ANONYMOUS, null, go);
		}

		private IActionResult PerformArchive(IpAddress ip, UserInLog user, DateTime startDate, DateTime endDate)
		{
			try
			{
				var output = base.actor.PerformArchive(startDate, endDate);

				return new FileContentResult(output.ToArray(), "application/zip") { FileDownloadName = actor.Name + '_' + endDate.ToString("yyyyMMdd") + "_bak.zip" };
			}
			catch (Exception e)
			{
				bool isQuery = true;

				string message = (e.InnerException == null) ? e.Message : e.InnerException.Message;

				const string SERVER_ERROR_MESSAGE_TO_THE_END_USER = "An error has been detected. Our support team was notified and they are working on it.";
				try
				{
					string stack = (e.StackTrace != null) ? e.StackTrace : (e.InnerException != null) ? e.InnerException.StackTrace : "no stack";

					var httpMethod = "no http";
					var httpPath = "";
					var httpParameters = "";

					var errorInfo = $"Method:{httpMethod}, Path:{httpPath}, WithTheFollowingParameters:[{{{httpParameters}}}]";
					var comandoConError = actor.CommandLineError;
					ApiError error = new ApiError(message, stack, comandoConError, errorInfo);

					if (e.InnerException is GameEngineException || e.InnerException.InnerException is ArgumentNullException)
					{
						var contentResult = new ContentResult
						{
							ContentType = "application/json",
							Content = JsonConvert.SerializeObject(error),
							StatusCode = 400
						};
						ErrorsSender.SendWithActor(actor.CurrentTimeStamp, e, isQuery, ip, user, "GameEngineException: " + endDate, contentResult.Content, "");
						return new BadRequestObjectResult(contentResult);
					}
					else //else if language exception
					{
						error.message = SERVER_ERROR_MESSAGE_TO_THE_END_USER; //ajustar el mensaje de excepción
						var contentResult = new ContentResult
						{
							ContentType = "application/json",
							Content = JsonConvert.SerializeObject(error),
							StatusCode = 500
						};
						return new InternalServerErrorObjectResult(contentResult);
					}
				}
				catch (Exception e2)
				{
					return new NotFoundErrorObjectResult();
				}
			}
		}

		private IActionResult PerformTrim(IpAddress ip, UserInLog user, DateTime trimmedDown)
		{
			try
			{
				var output = actor.PerformTrim(trimmedDown);

				return new OkObjectResult(output);
			}
			catch (Exception e)
			{
				bool isQuery = true;

				string message = (e.InnerException == null) ? e.Message : e.InnerException.Message;

				const string SERVER_ERROR_MESSAGE_TO_THE_END_USER = "An error has been detected. Our support team was notified and they are working on it.";
				try
				{
					string stack = (e.StackTrace != null) ? e.StackTrace : (e.InnerException != null) ? e.InnerException.StackTrace : "no stack";

					var httpMethod = "no http";
					var httpPath = "";
					var httpParameters = "";

					var errorInfo = $"Method:{httpMethod}, Path:{httpPath}, WithTheFollowingParameters:[{{{httpParameters}}}]";
					var comandoConError = actor.CommandLineError;
					ApiError error = new ApiError(message, stack, comandoConError, errorInfo);

					if (e.InnerException is GameEngineException || e.InnerException.InnerException is ArgumentNullException)
					{
						var contentResult = new ContentResult
						{
							ContentType = "application/json",
							Content = JsonConvert.SerializeObject(error),
							StatusCode = 400
						};
						ErrorsSender.SendWithActor(actor.CurrentTimeStamp, e, isQuery, ip, user, "GameEngineException: " + trimmedDown, contentResult.Content, "");
						return new BadRequestObjectResult(contentResult);
					}
					else //else if language exception
					{
						error.message = SERVER_ERROR_MESSAGE_TO_THE_END_USER; //ajustar el mensaje de excepción
						var contentResult = new ContentResult
						{
							ContentType = "application/json",
							Content = JsonConvert.SerializeObject(error),
							StatusCode = 500
						};
						return new InternalServerErrorObjectResult(contentResult);
					}
				}
				catch (Exception e2)
				{
					return new NotFoundErrorObjectResult();
				}
			}
		}

		private async Task<IActionResult> PerformAsync(IpAddress ip, UserInLog user, bool isQuery, HttpContext context, string validation, string script)
		{
			if (String.IsNullOrEmpty(script)) return new NotFoundErrorObjectResult();

			try
			{
				if (!String.IsNullOrWhiteSpace(validation))
				{
					var checkOutput = base.actor.PerformChk(validation, ip, user);

					bool hayEWI = checkOutput.IndexOf("\"EWI\"") != -1;
					if (hayEWI)
					{
						ApiPrecondition error = new ApiPrecondition(checkOutput);

						var contentResult = new ContentResult
						{
							ContentType = "application/json",
							Content = JsonConvert.SerializeObject(error)
						};

						var preconditionResponse = new BadRequestObjectResult(contentResult);
						preconditionResponse.StatusCode = 428;

						return preconditionResponse;
					}
				}

				string output = null;
				if (isQuery)
				{
					output = base.actor.PerformQry(script, ip, user);
				}
				else 
				{
					output = await base.actor.PerformCmdAsync(script, ip, user);
				}

				return new OkObjectResult(output);
			}
			catch (Exception e)
			{
				string message = (e.InnerException == null) ? e.Message : e.InnerException.Message;
				const string SERVER_ERROR_MESSAGE_TO_THE_END_USER = "An error has been detected. Our support team was notified and they are working on it.";
				try
				{
					string stack = (e.StackTrace != null) ? e.StackTrace : (e.InnerException != null) ? e.InnerException.StackTrace : "no stack";

					var httpMethod = "no http";
					var httpPath = "";
					var httpParameters = "";
					if (context != null)
					{
						httpMethod = context.Request.Method;
						httpPath = context.Request.Path;
						if (context.Request.Body.CanSeek)
						{
							context.Request.Body.Position = 0;
						}
						httpParameters = await new StreamReader(context.Request.Body).ReadToEndAsync();
					}

					var errorInfo = $"Method:{httpMethod}, Path:{httpPath}, WithTheFollowingParameters:[{{{httpParameters}}}]";
					var comandoConError = base.actor.CommandLineError;
					ApiError error = new ApiError(message, stack, comandoConError, errorInfo);

					if (e.InnerException is GameEngineException || e.InnerException is ArgumentNullException)
					{
						var contentResult = new ContentResult
						{
							ContentType = "application/json",
							Content = JsonConvert.SerializeObject(error),
							StatusCode = 400
						};
						await ErrorsSender.SendAsync(base.actor.CurrentTimeStamp, e, isQuery, ip, user, "GameEngineException: " + script, contentResult.Content);
						return contentResult;
					}
					else
					{
						error.message = SERVER_ERROR_MESSAGE_TO_THE_END_USER;
						var contentResult = new ContentResult
						{
							ContentType = "application/json",
							Content = JsonConvert.SerializeObject(error),
							StatusCode = 500
						};
						await ErrorsSender.SendAsync(base.actor.CurrentTimeStamp, e, isQuery, ip, user, script, contentResult.Content);
						return contentResult;
					}
				}
				catch (Exception e2)
				{
					await ErrorsSender.SendAsync(actor.CurrentTimeStamp, e2, isQuery, ip, user, script, "Exception processing an error in Lotto");
					return new NotFoundErrorObjectResult();
				}
			}
		}

		internal async Task<IActionResult> DoQryAsync(IpAddress ip, UserInLog user, HttpContext context, Func<Puppeteer.EventSourcing.Actor, Puppeteer.EventSourcing.IpAddress, Puppeteer.EventSourcing.UserInLog, System.Threading.ReaderWriterLockSlim, string> function)
		{
			try
			{
				var output = base.actor.DoQry(function);

				return new OkObjectResult(output);
			}
			catch (Exception e)
			{
				string message = (e.InnerException == null) ? e.Message : e.InnerException.Message;
				const string SERVER_ERROR_MESSAGE_TO_THE_END_USER = "An error has been detected. Our support team was notified and they are working on it.";
				try
				{
					string stack = (e.StackTrace != null) ? e.StackTrace : (e.InnerException != null) ? e.InnerException.StackTrace : "no stack";

					var httpMethod = "no http";
					var httpPath = "";
					var httpParameters = "";
					if (context != null)
					{
						httpMethod = context.Request.Method;
						httpPath = context.Request.Path;
						if (context.Request.Body.CanSeek)
						{
							context.Request.Body.Position = 0;
						}
						httpParameters = await new StreamReader(context.Request.Body).ReadToEndAsync();
					}

					var errorInfo = $"Method:{httpMethod}, Path:{httpPath}, WithTheFollowingParameters:[{{{httpParameters}}}]";
					var comandoConError = base.actor.CommandLineError;
					ApiError error = new ApiError(message, stack, comandoConError, errorInfo);

					if (e.InnerException is GameEngineException || e.InnerException is ArgumentNullException)
					{
						var contentResult = new ContentResult
						{
							ContentType = "application/json",
							Content = JsonConvert.SerializeObject(error),
							StatusCode = 400
						};
						await ErrorsSender.SendAsync(base.actor.CurrentTimeStamp, e, true, ip, user, "GameEngineException: No script", contentResult.Content);
						return contentResult;
					}
					else
					{
						error.message = SERVER_ERROR_MESSAGE_TO_THE_END_USER;
						var contentResult = new ContentResult
						{
							ContentType = "application/json",
							Content = JsonConvert.SerializeObject(error),
							StatusCode = 500
						};
						await ErrorsSender.SendAsync(base.actor.CurrentTimeStamp, e, true, ip, user, "GameEngineException: No script", contentResult.Content);
						return contentResult;
					}
				}
				catch (Exception e2)
				{
					await ErrorsSender.SendAsync(base.actor.CurrentTimeStamp, e2, true, ip, user, "GameEngineException: No script", $"Exception processing an error in {base.actor.Name}");
					return new NotFoundErrorObjectResult();
				}
			}
		}
	}
}
