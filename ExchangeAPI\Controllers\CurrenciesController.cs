﻿using GamesEngine.Settings;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ExchangeAPI.Controllers
{
    public class CurrenciesController : AuthorizeController
    {
        [HttpGet("api/currencies/all")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> AllCurrenciesAsync()
        {
            var result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
            {{
                for (currencies:company.System.Coins)
                {{
                    currency = currencies;
					print currency.Id id;
                    print currency.Iso4217Code currency;
                    print currency.UnicodeAsText unicode;
                    print currency.Name name;
					print currency.TypeAsText type;
                    print currency.DecimalPrecision decimalPrecision;
                }}
            }}
            ");
            return result;
        }

        [HttpGet("api/currencies/enabled")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> EnabledCurrenciesAsync()
        {
            var result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
            {{
                for (currencies:company.System.EnabledCoins)
                {{
                    currency = currencies;
					print currency.Id id;
                    print currency.Iso4217Code currency;
                    print currency.UnicodeAsText unicode;
                    print currency.Name name;
					print currency.TypeAsText type;
                }}
            }}
            ");
            return result;
        }

        [HttpGet("api/stores/{storeId}/currencies/activations")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> CurrenciesByStoreAsync(int storeId)
        {
            if (storeId <= 0) return BadRequest($"{nameof(storeId)} must be greater than 0");

            var result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
            {{
                store = company.Sales.StoreById({storeId});
                currenciesList = company.Sales.AvailableCurrencies;
                for (currencies:currenciesList)
                {{
                    currency = currencies;
                    print currency.Iso4217Code currencyCode;
                    print currency.UnicodeAsText unicode;
                    print currency.Name name;
                    print store.IsEnabled(currency) enabled;
                }}
                logEntries = store.CurrenciesLog.LastEntries(5);
				for (log:logEntries)
				{{
					print log.DateFormattedAsText date;
					print log.Who who;
					print log.Message message;
				}}
            }}
            ");
            return result;
        }

        [HttpPost("api/stores/{storeId}/currencies/{currencyCode}/activation")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> EnableCurrencyAsync(int storeId, string currencyCode)
        {
            if (storeId <= 0) return BadRequest($"{nameof(storeId)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(currencyCode)) return BadRequest($"{nameof(currencyCode)} is required");

            string employeeName = Security.UserName(HttpContext);
            string msg = $"Enabled currency {currencyCode}";
            var result = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, $@"
            {{
                store = company.Sales.StoreById({storeId});
                activation = store.EnableCurrency('{currencyCode}');
                activation.AddAnnotation(store.Id.ToString(), '{msg}', '{employeeName}', Now);
            }}
            ");
            return result;
        }

        [HttpPost("api/stores/{storeId}/currencies/{currencyCode}/deactivation")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> DisableCurrencyAsync(int storeId, string currencyCode)
        {
            if (storeId <= 0) return BadRequest($"{nameof(storeId)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(currencyCode)) return BadRequest($"{nameof(currencyCode)} is required");

            string employeeName = Security.UserName(HttpContext);
            string msg = $"Disabled currency {currencyCode}";
            var result = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, $@"
            {{
                store = company.Sales.StoreById({storeId});
                activation = store.DisableCurrency('{currencyCode}');
                activation.AddAnnotation(store.Id.ToString(), '{msg}', '{employeeName}', Now);
            }}
            ");
            return result;
        }

        [HttpGet("api/domains/{domainId}/currencies/activations")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> CurrenciesByDomainAsync(int domainId)
        {
            if (domainId <= 0) return BadRequest($"{nameof(domainId)} must be greater than 0");

            var result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
            {{
                domain = company.Sales.DomainFrom({domainId});
                currenciesList = company.Sales.AvailableCurrencies;
                for (currencies:currenciesList)
                {{
                    currency = currencies;
                    print currency.Iso4217Code currencyCode;
                    print currency.UnicodeAsText unicode;
                    print currency.Name name;
                    print company.Sales.IsEnabled(currency, domain) enabled;
                }}
                logEntries = company.Sales.CurrenciesLog(domain).LastEntries(5);
				for (log:logEntries)
				{{
					print log.DateFormattedAsText date;
					print log.Who who;
					print log.Message message;
				}}
            }}
            ");
            return result;
        }

        [HttpPost("api/domains/{domainId}/currencies/{currencyCode}/activation")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> EnableCurrencyInDomainAsync(int domainId, string currencyCode)
        {
            if (domainId <= 0) return BadRequest($"{nameof(domainId)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(currencyCode)) return BadRequest($"{nameof(currencyCode)} is required");

            string employeeName = Security.UserName(HttpContext);
            string msg = $"Enabled currency {currencyCode}";
            var result = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, $@"
            {{
                domain = company.Sales.DomainFrom({domainId});
                activation = company.Sales.EnableCurrency('{currencyCode}', domain);
                activation.AddAnnotation(domain.Id.ToString(), '{msg}', '{employeeName}', Now);
            }}
            ");
            return result;
        }

        [HttpPost("api/domains/{domainId}/currencies/{currencyCode}/deactivation")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> DisableCurrencyInDomainAsync(int domainId, string currencyCode)
        {
            if (domainId <= 0) return BadRequest($"{nameof(domainId)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(currencyCode)) return BadRequest($"{nameof(currencyCode)} is required");

            string employeeName = Security.UserName(HttpContext);
            string msg = $"Disabled currency {currencyCode}";
            var result = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, $@"
            {{
                domain = company.Sales.DomainFrom({domainId});
                activation = company.Sales.DisableCurrency('{currencyCode}', domain);
                activation.AddAnnotation(domain.Id.ToString(), '{msg}', '{employeeName}', Now);
            }}
            ");
            return result;
        }
    }
}
