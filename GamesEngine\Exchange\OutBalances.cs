﻿using GamesEngine.Exchange.Batch;
using GamesEngine.Finance;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Text;

namespace GamesEngine.Exchange
{
	internal class OutBalances : Balances
	{
		private readonly BatchSet batch;

		internal OutBalances(int batchNumber, BatchSet batch, SetOfBalances initialAccount) : base(batchNumber, Types.OUT, initialAccount)
		{
			this.batch = batch;
		}

		internal void InitialAmount(Currency amount, string employeeName)
		{
			if (InitialAccountItsNotConfiguredFor(amount.Coin)) throw new GameEngineException($"There are no {nameof(SetOfBalances)} for the marketplace yet.");

			//TODO como manejar la auditoria... Como guardar quien lo creo y quien lo deposito
			BatchAccount result = FindAccountByCurrency(amount.Coin);
			result.InitialAmount(amount);
		}

		internal void DebitInitialAmount(Currency amount, string employeeName)
		{
			if (InitialAccountItsNotConfiguredFor(amount.Coin)) throw new GameEngineException($"There are no {nameof(SetOfBalances)} for the marketplace yet.");

			//TODO como manejar la auditoria... Como guardar quien lo creo y quien lo deposito
			BatchAccount result = FindAccountByCurrency(amount.Coin);
			result.DebitInitialAmount(amount);
		}

		internal void Deposit(Currency amount, string employeeName)
		{
			if (InitialAccountItsNotConfiguredFor(amount.Coin)) throw new GameEngineException($"There are no {nameof(SetOfBalances)} for the marketplace yet.");

			//TODO como manejar la auditoria... Como guardar quien lo creo y quien lo deposito
			BatchAccount result = FindAccountByCurrency(amount.Coin);
			result.Credit(amount);
		}

		internal void Deposit(Currency amount, Currency cost, string employeeName)
		{
			if (InitialAccountItsNotConfiguredFor(amount.Coin)) throw new GameEngineException($"There are no {nameof(SetOfBalances)} for the marketplace yet.");

			//TODO como manejar la auditoria... Como guardar quien lo creo y quien lo deposito
			BatchAccount result = FindAccountByCurrency(amount.Coin);
			result.Credit(amount);
		}

		internal void WithDraw(Currency amount, string employeeName)
		{
			if (InitialAccountItsNotConfiguredFor(amount.Coin)) throw new GameEngineException($"There are no {nameof(SetOfBalances)} for the marketplace yet.");

			BatchAccount result = FindAccountByCurrency(amount.Coin);
			result.Lock(amount);
		}

		internal void UnLock(Currency amount, string employeeName)
		{
			if (InitialAccountItsNotConfiguredFor(amount.Coin)) throw new GameEngineException($"There are no {nameof(SetOfBalances)} for the marketplace yet.");

			BatchAccount result = FindAccountByCurrency(amount.Coin);
			result.Unlock(amount);
		}

		internal Currency Available(Coin currencyCode)
		{
			BatchAccount result = FindAccountByCurrency(currencyCode);
			return result.Available;
		}

		internal Currency Initial(Coin currencyCode)
		{
			BatchAccount result = FindAccountByCurrency(currencyCode);
			return result.Initial;
		}

		internal BatchAccount FindAccountByCurrency(Coin currencyCode)
		{
			return accounts.FindAccountByCurrency(currencyCode);
		}

		internal IEnumerable<BatchAccount> Accounts()
		{
			var result = this.accounts.ListAccounts();
			return result;
		}

		internal Currency CalculateProfit(FloatingExchangeRate saleRate, Currency amountToCustomer)
		{
			if (!(batch is AgentBatch)) throw new GameEngineException($"There is not possible to calcute a profit from {nameof(AgentBatch)} becasue it hasn't  costs. ");

			AgentBatch agentBatch = (AgentBatch)batch;
			FixedExchangeRate rateCost = agentBatch.CostsFor(amountToCustomer.Coin);
			decimal difference = saleRate.Minus(rateCost);

			decimal product = amountToCustomer.Value * difference;

			return Currency.Factory(rateCost.BaseCurrency, product);
		}

		internal Currency CalculateProfit(FixedExchangeRate purchaseRate, Currency amountToCustomer)
		{
			if (!(batch is AgentBatch)) throw new GameEngineException($"There is not possible to calcute a profit from {nameof(AgentBatch)} becasue it hasn't costs. ");

			AgentBatch agentBatch = (AgentBatch)batch;
			FixedExchangeRate rateCost = agentBatch.CostsFor(amountToCustomer.Coin);
			decimal difference = purchaseRate.Minus(rateCost);

			decimal product = amountToCustomer.Value * difference;

			return Currency.Factory(amountToCustomer.CurrencyCode, product);
		}

		internal Currency CalculateCost(Currency amountToCustomer)
		{
			if (!(batch is AgentBatch)) throw new GameEngineException($"There is not possible to calcute a profit from {nameof(AgentBatch)} becasue it hasn't  costs. ");

			AgentBatch agentBatch = (AgentBatch)batch;
			FixedExchangeRate rateCost = agentBatch.CostsFor(amountToCustomer.Coin);

			decimal product = amountToCustomer.Value * rateCost.Price;

			return Currency.Factory(amountToCustomer.CurrencyCode, product);
		}

		internal OutBalances UnLock(Currency amount, Transaction transaction, BatchMovements movements)
		{
			if (InitialAccountItsNotConfiguredFor(amount.Coin)) throw new GameEngineException($"There are no {nameof(SetOfBalances)} for the marketplace yet.");

			BatchAccount result = FindAccountByCurrency(amount.Coin);
			result.Unlock(amount);

			movements.CreateNewUnLockMovement(transaction.Id, amount, transaction.TransactionDate, Balances.Types.OUT);

			return this;
		}

		internal OutBalances Lock(Currency amount, Transaction transaction, BatchMovements movements)
		{
			if (InitialAccountItsNotConfiguredFor(amount.Coin)) throw new GameEngineException($"There are no {nameof(SetOfBalances)} for the marketplace yet.");

			BatchAccount result = FindAccountByCurrency(amount.Coin);
			result.Lock(amount);

			movements.CreateNewLockMovement(transaction.Id, amount, transaction.TransactionDate, Balances.Types.OUT);

			return this;
		}

		internal OutBalances Accredit(Currency amount, Transaction transaction, BatchMovements movements)
		{
			if (InitialAccountItsNotConfiguredFor(amount.Coin)) throw new GameEngineException($"There are no {nameof(SetOfBalances)} for the marketplace yet.");

			BatchAccount result = FindAccountByCurrency(amount.Coin);
			result.Credit(amount);

			movements.CreateNewCreditMovement(transaction.Id, amount, transaction.TransactionDate, Balances.Types.OUT);

			return this;
		}

		internal OutBalances Debit(Currency amount, Transaction transaction, BatchMovements movements)
		{
			if (InitialAccountItsNotConfiguredFor(amount.Coin)) throw new GameEngineException($"There are no {nameof(SetOfBalances)} for the marketplace yet.");

			BatchAccount result = FindAccountByCurrency(amount.Coin);
			result.Debit(amount);

			movements.CreateNewDebitMovement(transaction.Id, amount, transaction.TransactionDate, Balances.Types.OUT);

			return this;
		}

		internal bool HasExceededAvailable(Currency amount)
		{
			BatchAccount account = FindAccountByCurrency(amount.Coin);
			var result = account.Available.Value < amount.Value;
			return result;
		}

		internal bool CanAssignFunds(Currency amount)
		{
			return !HasExceededAvailable(amount);
		}

		internal bool HasInitialAmountInAnyCurrency
		{
			get
			{
				return accounts.HasInitialAmountInAnyCurrency;
			}
		}

		internal bool HasAvailableAmountInAnyCurrency
		{
			get
			{
				return accounts.HasAvailableAmountInAnyCurrency;
			}
		}

		internal BatchAccount AddInLockAccumulated(Currency amount)
		{
			BatchAccount result = FindAccountByCurrency(amount.Coin);
			return result.AddInLockAccumulated(amount);
		}

		internal BatchAccount SubtractFromLockAccumulated(Currency amount)
		{
			BatchAccount result = FindAccountByCurrency(amount.Coin);
			return result.SubtractFromLockAccumulated(amount);
		}
		internal BatchAccount AddInSpendAcculumated(Currency amount)
		{
			BatchAccount result = FindAccountByCurrency(amount.Coin);
			return result.AddInSpendAcculumated(amount);
		}

		internal BatchAccount SubtractFromSpendAcculumated(Currency amount)
		{
			BatchAccount result = FindAccountByCurrency(amount.Coin);
			return result.SubtractFromSpendAcculumated(amount);
		}
	}

	internal class BatchMovements
	{
		private List<BatchMovement> movements = new List<BatchMovement>();
		internal BatchLockMovement CreateNewLockMovement(int id, Currency amount, DateTime movementDate, Balances.Types balanceType)
		{
			BatchLockMovement movement = new BatchLockMovement(id, amount, movementDate, balanceType);
			movements.Add(movement);

			return movement;
		}

		internal void Add(BatchMovements draftMovements)
		{
			this.movements.AddRange(draftMovements.movements);
		}

		internal BatchUnLockMovement CreateNewUnLockMovement(int id, Currency amount, DateTime movementDate, Balances.Types balanceType)
		{
			BatchUnLockMovement movement = new BatchUnLockMovement(id, amount, movementDate, balanceType);
			movements.Add(movement);

			return movement;
		}

		internal BatchDebitMovement CreateNewDebitMovement(int id, Currency amount, DateTime movementDate, Balances.Types balanceType)
		{
			BatchDebitMovement movement = new BatchDebitMovement(id, amount, movementDate, balanceType);
			movements.Add(movement);

			return movement;
		}

		internal BatchAccreditMovement CreateNewCreditMovement(int id, Currency amount, DateTime movementDate, Balances.Types balanceType)
		{
			BatchAccreditMovement movement = new BatchAccreditMovement(id, amount, movementDate, balanceType);
			movements.Add(movement);

			return movement;
		}
		internal void CreateNewUnProfitMovement(int id, Currency amountToCustomer, DateTime movementDate, Balances.Types balanceType)
		{
			BatchUnProfitMovement movement = new BatchUnProfitMovement(id, amountToCustomer, movementDate, balanceType);
			movements.Add(movement);
		}

		internal void CreateNewUnProfitApprovalMovement(int id, Currency amountToCustomer, DateTime movementDate, Balances.Types balanceType)
		{
			UnProfitApprovalMovement movement = new UnProfitApprovalMovement(id, amountToCustomer, movementDate, balanceType);
			movements.Add(movement);
		}
		internal void CreateNewUnProfitRejectionMovement(int id, Currency amountToCustomer, DateTime movementDate, Balances.Types balanceType)
		{
			UnProfitRejectionMovement movement = new UnProfitRejectionMovement(id, amountToCustomer, movementDate, balanceType);
			movements.Add(movement);
		}
		internal IEnumerable<BatchMovement> List()
		{
			return movements;
		}

		internal BatchMovements SubstractInLock(int id, Currency amountToCustomer, DateTime movementDate, Balances.Types balanceType)
		{
			SubstractFromLockAccumulatedMovement movement = new SubstractFromLockAccumulatedMovement(id, amountToCustomer, movementDate, balanceType);
			movements.Add(movement);

			return this;
		}
		internal BatchMovements AccumulateInSpend(int id, Currency amountToCustomer, DateTime movementDate, Balances.Types balanceType)
		{
			AccumulateInSpendAccumulatedMovement movement = new AccumulateInSpendAccumulatedMovement(id, amountToCustomer, movementDate, balanceType);
			movements.Add(movement);

			return this;
		}

		internal BatchMovements AccumulateInLock(int id, Currency amountToCustomer, DateTime movementDate, Balances.Types balanceType)
		{
			AccumulateInLockAccumulatedMovement movement = new AccumulateInLockAccumulatedMovement(id, amountToCustomer, movementDate, balanceType);
			movements.Add(movement);

			return this;
		}
	}
	internal abstract class BatchMovement 
		: Objeto
	{
		internal Currency Amount { get;}
		private int id;
		private readonly DateTime movementDate;
		private readonly string movementType;

		internal Balances.Types BalanceType { get; }

		protected BatchMovement(int id, Currency amount, DateTime movementDate, string movementType, Balances.Types balanceType)
		{
			if (movementDate == default(DateTime)) throw new ArgumentNullException(nameof(movementDate));
			if (string.IsNullOrWhiteSpace(movementType)) throw new ArgumentNullException(nameof(movementType));

			this.id = id;
			Amount = amount;
			this.movementDate = movementDate;
			this.movementType = movementType;
			BalanceType = balanceType;
		}

		internal string CurrencyAsText
		{
			get
			{
				return this.Amount.CurrencyCode.ToString();
			}
		}

		internal Currency Total
		{
			get
			{
				return this.Amount;
			}
		}

		internal int TransactionNumber
		{
			get
			{
				return this.id;
			}
		}

		internal DateTime MovementDate
		{
			get
			{
				return this.movementDate;
			}
		}

		internal string MovementType
		{
			get
			{
				return this.movementType;
			}
		}
	}

	
	internal sealed class AccumulateInSpendAccumulatedMovement : BatchMovement
	{
		internal AccumulateInSpendAccumulatedMovement(int id, Currency amount, DateTime movementDate, Balances.Types balanceType) : base(id, amount, movementDate, "Accumulate in spend accumulated", balanceType) { }
	}
	internal sealed class AccumulateInLockAccumulatedMovement : BatchMovement
	{
		internal AccumulateInLockAccumulatedMovement(int id, Currency amount, DateTime movementDate, Balances.Types balanceType) : base(id, amount, movementDate, "Accumulate in lock accumulated", balanceType) { }
	}
	internal sealed class SubstractFromLockAccumulatedMovement : BatchMovement
	{
		internal SubstractFromLockAccumulatedMovement(int id, Currency amount, DateTime movementDate, Balances.Types balanceType) : base(id, amount, movementDate, "Substract from lock accumulated", balanceType) { }
	}
	internal sealed class UnProfitApprovalMovement : BatchMovement
	{
		internal UnProfitApprovalMovement(int id, Currency amount, DateTime movementDate, Balances.Types balanceType) : base(id, amount, movementDate, "Unprofit approval", balanceType) { }
	}
	internal sealed class UnProfitRejectionMovement : BatchMovement
	{
		internal UnProfitRejectionMovement(int id, Currency amount, DateTime movementDate, Balances.Types balanceType) : base(id, amount, movementDate, "Unprofit Rejection", balanceType) { }
	}
	internal sealed class BatchLockMovement : BatchMovement
	{
		internal BatchLockMovement(int id, Currency amount, DateTime transactionDate, Balances.Types balanceType) : base(id, amount, transactionDate, "Lock", balanceType) {}
	}
	internal sealed class BatchUnProfitMovement : BatchMovement
	{
		internal BatchUnProfitMovement(int id, Currency amount, DateTime movementDate, Balances.Types balanceType) : base(id, amount, movementDate, "Unprofit", balanceType) { }
	}
	
	internal sealed class BatchUnLockMovement : BatchMovement
	{
		internal BatchUnLockMovement(int id, Currency amount, DateTime transactionDate, Balances.Types balanceType) : base(id, amount, transactionDate, "Unlock", balanceType) { }
	}
	internal sealed class BatchAccreditMovement : BatchMovement
	{
		internal BatchAccreditMovement(int id, Currency amount, DateTime transactionDate, Balances.Types balanceType) : base(id, amount, transactionDate, "Credit", balanceType) { }
	}
	internal sealed class BatchDebitMovement : BatchMovement
	{
		internal BatchDebitMovement(int id, Currency amount, DateTime transactionDate, Balances.Types balanceType) : base(id, amount, transactionDate, "Debit", balanceType) { }
	}
}
