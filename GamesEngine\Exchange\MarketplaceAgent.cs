﻿using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Text;

namespace GamesEngine.Exchange
{
	[Puppet]
	internal class MarketplaceAgent: ExchangeUser
	{
		private readonly Marketplace marketplace;

		internal MarketplaceAgent(Marketplace marketplace):base(marketplace)
		{
			Name = marketplace.Name;
			this.marketplace = marketplace;
		}

		internal override string FullName
		{
			get
			{
				return Name;
			}
		}
	}
}
