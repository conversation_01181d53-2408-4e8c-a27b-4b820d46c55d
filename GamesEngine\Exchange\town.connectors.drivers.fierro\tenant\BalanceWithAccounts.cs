﻿using GamesEngine.Finance;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;

using System.Text;
using System.Threading.Tasks;

namespace town.connectors.drivers.fiero
{
	internal class BalanceWithAccounts: FieroTenantDriver
	{
		public BalanceWithAccounts()
			: base(Tenant_Actions.Balance)
		{
		}
		public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			var result = await BalancesWithAccountsAsync(now, recordSet);

			return (T)Convert.ChangeType(result, typeof(T));
		}
		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			throw new NotImplementedException();
		}
		public override void Prepare(DateTime now)
		{
			CustomSettings.AddVariableParameter("httpContext");
			CustomSettings.AddVariableParameter("processorAlias");
			CustomSettings.AddVariableParameter("atAddress");
			CustomSettings.AddVariableParameter("currencyCode");
			CustomSettings.AddVariableParameter("actor");
			//CustomSettings.Prepare();
		}

		public async Task<BalanceResponse> BalancesWithAccountsAsync(DateTime now, CustomSettings.RecordSet recordSet)
		{
			var HttpContext = recordSet.Mappings["httpContext"];
			var ProcessorAlias = recordSet.Mappings["processorAlias"];
			var AtAddress = recordSet.Mappings["atAddress"];
			var CurrencyCode = recordSet.Mappings["currencyCode"];
			var Actor = recordSet.Mappings["actor"];

			HttpContext httpContext = HttpContext.As<HttpContext>();
			string processorAlias = ProcessorAlias.AsString;
			string atAddress = AtAddress.AsString;
			string currencyCode = CurrencyCode.AsString;
			RestAPISpawnerActor actor = Actor.As<RestAPISpawnerActor>();

			StringBuilder script = new StringBuilder();
			script.AppendLine($@"
				{{
					print '{processorAlias}' alias;
					accumulatedBalance = atAddress.GetAccumulatedBalanceWithAccounts('{currencyCode}');
					print atAddress.Number atAddress;
					currencyCode = accumulatedBalance.CurrencyCodeAsText;
					print currencyCode code;
					print accumulatedBalance.Locked locked;
					print accumulatedBalance.Available balance;
					for (accounts : accumulatedBalance.Accounts)
					{{
						account = accounts;
						print account.Number accountNumber;
						print currencyCode code;
						print account.Balance.Available available;
					}}
				}}
			");

			var result = await actor.PerformQryAsync(atAddress, httpContext, script.ToString());
			if (!(result is OkObjectResult))
			{
				ErrorsSender.Send($"atAddress:{atAddress} Command:{script} is failing.", $"Balance for atAddress:{atAddress} its not loading.");
				return new BalanceResponse();
			}

			string resultToString = ((OkObjectResult)result).Value.ToString();
			var response = JsonConvert.DeserializeObject<BalanceResponse>(resultToString);
			return response;
		}
	}
}
