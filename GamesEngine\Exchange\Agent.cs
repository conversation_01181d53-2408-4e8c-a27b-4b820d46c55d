﻿using GamesEngine.Custodian;
using GamesEngine.Finance;
using GamesEngine.Games.Lotto;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Exchange
{
    [Puppet]
    internal class Agent : ExchangeUser
    {
        
        private readonly ExchangeUser parent;

        internal Agent(ExchangeUser parent, string name) : base(parent.Marketplace)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

            this.parent = parent;
            Name = name;
        }

        internal override string FullName
        {
            get
            {
                return parent.FullName + USER_SEPARATOR + Name;
            }
        }

        Prizes currentPrizes;
        internal Prizes CurrentPrizes
        {
            get
            {
                if (currentPrizes == null) return parent.Marketplace.Company.Lotto900().Prizes;
                return currentPrizes;
            }
            set
            {
                if (currentPrizes != null && value.VersionNumber <= currentPrizes.VersionNumber) throw new GameEngineException($"New prizes must have a {nameof(currentPrizes.VersionNumber)} greater than previous version");
                if (currentPrizes != null && currentPrizes.GetType() != value.GetType()) throw new GameEngineException($"New prizes have a different type from the original");
                currentPrizes = value;
            }
        }

        List<Manager> managers;
        public IEnumerable<Manager> Managers 
        { 
            get
            {
                return managers;
            }
        }
        List<Cashier> cashiers;
        public IEnumerable<Cashier> Cashiers
        {
            get
            {
                return cashiers;
            }
        }
        internal bool HasManagers => managers != null && managers.Count > 0;
        internal bool HasCashiers => cashiers != null && cashiers.Count > 0;
        internal int ManagersCount => managers == null ? 0 : managers.Count;
        internal int CashiersCount => cashiers == null ? 0 : cashiers.Count;

        internal Manager AddManager(string name)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (ExistsCashier(name)) throw new GameEngineException($"{nameof(Cashier)} {name} already exist in agency {FullName}");
            if (ExistsManager(name)) throw new GameEngineException($"{nameof(Manager)} {name} already exist in agency {FullName}");
            if (!Validator.AreAllLettersOrDigitsOrSpaces(name)) throw new GameEngineException($"Name '{name}' has invalid characters. Only letters, digits and spaces are allowed.");

            var user = new Manager(name);
            if (managers == null) managers = new List<Manager>();
            if (managers.Contains(user)) throw new GameEngineException($"{nameof(Manager)} {user.Name} already exist in agency {FullName}");
            managers.Add(user);

            return user;
        }

        internal Cashier AddCashier(string name)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (ExistsCashier(name)) throw new GameEngineException($"{nameof(Cashier)} {name} already exist in agency {FullName}");
            if (ExistsManager(name)) throw new GameEngineException($"{nameof(Manager)} {name} already exist in agency {FullName}");
            if (!Validator.AreAllLettersOrDigitsOrSpaces(name)) throw new GameEngineException($"Name '{name}' has invalid characters. Only letters, digits and spaces are allowed.");

            var user = new Cashier(name);
            if (cashiers == null) cashiers = new List<Cashier>();
            if (cashiers.Contains(user)) throw new GameEngineException($"{nameof(Cashier)} {user.Name} already exist in agency {FullName}");
            cashiers.Add(user);

            return user;
        }

        internal User AddUser(string userTypeName, string name)
        {
            if (string.IsNullOrWhiteSpace(userTypeName)) throw new ArgumentNullException(nameof(userTypeName));
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

            switch (userTypeName)
            {
                case nameof(Cashier):
                    return AddCashier(name);
                case nameof(Manager):
                    return AddManager(name);
                default:
                    throw new GameEngineException($"{nameof(userTypeName)} {userTypeName} is not implemented");
            }
        }

        internal User ChangeUserType(string newUserTypeName, User user)
        {
            if (string.IsNullOrWhiteSpace(newUserTypeName)) throw new ArgumentNullException(nameof(newUserTypeName));
            if (user == null) throw new ArgumentNullException(nameof(user));

            switch (newUserTypeName)
            {
                case nameof(Cashier):
                    if (user is Manager) managers.Remove((Manager)user);
                    var newCashier = AddCashier(user.Name);
                    newCashier.CopyFrom(user);
                    return newCashier;
                case nameof(Manager):
                    if (user is Cashier) cashiers.Remove((Cashier)user);
                    var newManager = AddManager(user.Name);
                    newManager.CopyFrom(user);
                    return newManager;
                default:
                    throw new GameEngineException($"{nameof(newUserTypeName)} {newUserTypeName} is not implemented");
            }
        }

        internal Cashier SearchCashier(string name)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

            var result = cashiers?.FirstOrDefault(cashier => cashier.Name.Equals(name, StringComparison.OrdinalIgnoreCase));
            if (result == null) throw new GameEngineException($"{nameof(Cashier)} {name} does not exist in agency {Name}");
            return result;
        }

        internal Manager SearchManager(string name)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

            var result = managers?.FirstOrDefault(manager => manager.Name.Equals(name, StringComparison.OrdinalIgnoreCase));
            if (result == null) throw new GameEngineException($"{nameof(Cashier)} {name} does not exist in agency {Name}");
            return result;
        }

        internal User SearchUser(string name)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

            User result = cashiers?.FirstOrDefault(cashier => cashier.Name.Equals(name, StringComparison.OrdinalIgnoreCase));
            if (result != null) return result;
            result = managers?.FirstOrDefault(manager => manager.Name.Equals(name, StringComparison.OrdinalIgnoreCase));
            if (result == null) throw new GameEngineException($"{nameof(User)} {name} does not exist in agency {Name}");
            return result;
        }

        internal bool ExistsCashier(string name)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

            var result = cashiers?.Exists(cashier => cashier.Name.Equals(name, StringComparison.OrdinalIgnoreCase));
            if (result == null) return false;
            return result.Value;
        }

        internal bool ExistsManager(string name)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

            var result = managers?.Exists(manager => manager.Name.Equals(name, StringComparison.OrdinalIgnoreCase));
            if (result == null) return false;
            return result.Value;
        }
    }
}
