﻿using GamesEngine.Finance;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.drivers;
using static GamesEngine.Business.WholePaymentMethods;
using static GamesEngine.Finance.PaymentChannels;
using ProcessorPaymentMethod = GamesEngine.Business.WholePaymentMethods.ProcessorPaymentMethod;

using static town.connectors.CustomSettings;
using static town.connectors.CustomSettings.CustomSetting;
using Puppeteer.EventSourcing;
using GamesEngine.Exchange;
using town.connectors.drivers.hades;
using GamesEngine.Custodian.Operations;
using GamesEngine.Settings;
using Path = System.IO.Path;
using GamesEngine.Domains;
using GamesEngine.Custodian;
using System.Runtime.CompilerServices;
using static GamesEngine.Exchange.PaymentProcessorsAndActionsByDomains;
using GamesEngine.MessageQueuing;

[assembly: InternalsVisibleTo("GamesEngineMocks")]
namespace GamesEngine.Business
{
    internal class WholePaymentProcessor : WholeCatalog
    {
        internal const int NoPaymentProcessor = 0;
        #region Processor Indexes
        private IndexedProcessors indexedBalanceProcessor;
        private IndexedProcessors indexedValidateProcessor;
        private IndexedProcessors indexedFragmentProcessor;
        private IndexedProcessors otherProcessors;
        private IndexedProcessors indexedDespositProcessor;
        private IndexedProcessors indexedSaleProcessor;
        private IndexedProcessors indexedWithdrawalProcessor;
        private IndexedProcessors indexedGradeProcessor;
        private IndexedProcessors indexedAuthorizationProcessor;
        private List<IndexedProcessors> allIndexes = new List<IndexedProcessors>();
        public MultipleProcessors Values { get; } = new MultipleProcessors();
        #endregion

        private PaymentProcessorsAndActionsByDomains _paymentProcessorsAndActionsByDomains;
        internal PaymentProcessorsAndActionsByDomains PaymentProcessorsAndActionsByDomains { get { return _paymentProcessorsAndActionsByDomains; } }

        private static WholePaymentProcessor _wholePaymentProcessor;
        internal static WholePaymentProcessor Instance()
        {
            if (_wholePaymentProcessor == null) throw new GameEngineException("Set the processors first.");

            return _wholePaymentProcessor;
        }

        internal WholePaymentProcessor(Company company) : base(company)
        {
            if (_wholePaymentProcessor != null) return;
            _wholePaymentProcessor = this;

            _paymentProcessorsAndActionsByDomains = new PaymentProcessorsAndActionsByDomains(this);
        }
        internal Company Company { get { return company; } }

        private List<Driver> Drivers { get; set; }
        

        internal CatalogMember Add(Entity entity, ProcessorPaymentMethod paymentMethod, string iso4217Code, ProcessorTransaction transactionType, Driver implementation)
        {
            if (entity == null) throw new ArgumentException(nameof(entity));
            if (paymentMethod == null) throw new ArgumentException(nameof(paymentMethod));
            if (string.IsNullOrEmpty(iso4217Code)) throw new ArgumentException(nameof(iso4217Code));
            if (transactionType == null) throw new ArgumentException(nameof(transactionType));

            GroupOFTransactions transactions = new GroupOFTransactions();
            transactions.Add(transactionType);

            var coin = company.System.Coins.SearchByIsoCode(iso4217Code);
            nextConsecutive = NextConsecutive();
            var processor = new PaymentProcessor(nextConsecutive, $"{nameof(PaymentProcessor)} for {entity.Name}.", entity, paymentMethod, coin, transactions, implementation);

            return Add(processor);
        }

        internal CatalogMember Add(PaymentProcessor processor)
        {
           if(members.Contains(processor)) throw new GameEngineException($"The processor {processor.Name} is already added.");

            members.Add(processor);
            Values.Addon(processor);

            processor.Visible = true;
            processor.Enabled = true;

            CreateIndex(processor);

            return processor;
        }

        private void CreateIndex(PaymentProcessor processor)
        {
            if (IsABalanceDriver(processor.Driver))
            {
                if (indexedBalanceProcessor.IsACollectionNeeded())
                    indexedBalanceProcessor = indexedBalanceProcessor.ChangeToMultipleIndexProcessor();

                indexedBalanceProcessor.Addon(processor);
            }
            else if (IsADepositDriver(processor.Driver))
            {
                if (indexedDespositProcessor.IsACollectionNeeded())
                    indexedDespositProcessor = indexedDespositProcessor.ChangeToMultipleIndexProcessor();

                indexedDespositProcessor.Addon(processor);
            }
            else if (IsASaleDriver(processor.Driver))
            {
                if (indexedSaleProcessor.IsACollectionNeeded())
                    indexedSaleProcessor = indexedSaleProcessor.ChangeToMultipleIndexProcessor();

                indexedSaleProcessor.Addon(processor);
            }
            else if (IsAWithdrawalDriver(processor.Driver))
            {
                if (indexedWithdrawalProcessor.IsACollectionNeeded())
                    indexedWithdrawalProcessor = indexedWithdrawalProcessor.ChangeToMultipleIndexProcessor();

                indexedWithdrawalProcessor.Addon(processor);
            }
            else if (IsAValidateDriver(processor.Driver))
            {
                if (indexedValidateProcessor.IsACollectionNeeded())
                    indexedValidateProcessor = indexedValidateProcessor.ChangeToMultipleIndexProcessor();

                indexedValidateProcessor.Addon(processor);
            }
            else if (IsAFragmentDriver(processor.Driver))
            {
                if (indexedFragmentProcessor.IsACollectionNeeded())
                    indexedFragmentProcessor = indexedFragmentProcessor.ChangeToMultipleIndexProcessor();

                indexedFragmentProcessor.Addon(processor);
            }
            else if (IsAnOtherDriver(processor.Driver))
            {
                if (otherProcessors.IsACollectionNeeded())
                    otherProcessors = otherProcessors.ChangeToMultipleIndexProcessor();

                otherProcessors.Addon(processor);
            }
            else if (IsAGradeDriver(processor.Driver))
            {
                if (indexedGradeProcessor.IsACollectionNeeded())
                    indexedGradeProcessor = indexedGradeProcessor.ChangeToMultipleIndexProcessor();

                indexedGradeProcessor.Addon(processor);
            }
            else if (IsAnAuthorizationDriver(processor.Driver))
            {
                if (indexedAuthorizationProcessor.IsACollectionNeeded())
                    indexedAuthorizationProcessor = indexedAuthorizationProcessor.ChangeToMultipleIndexProcessor();

                indexedAuthorizationProcessor.Addon(processor);
            }
        }

        internal override CatalogMember Add(int id, string name)
        {
            throw new NotImplementedException();
        }

        internal void SearchProcessor(int entityId, int paymentMethodId, int transactionTypeId, string iso4217Code, out PaymentProcessor paymentProcessor)
        {
            if (entityId <= 0) throw new GameEngineException($"{nameof(entityId)} must be greater than 0");
            if (paymentMethodId <= 0) throw new GameEngineException($"{nameof(paymentMethodId)} must be greater than 0");
            if (transactionTypeId <= 0) throw new GameEngineException($"{nameof(transactionTypeId)} must be greater than 0");
            if (string.IsNullOrEmpty(iso4217Code)) throw new ArgumentException(nameof(iso4217Code));

            paymentProcessor = null;
            foreach (PaymentProcessor processor in members)
            {
                if (processor.Entity.Id == entityId && processor.Group.Id == paymentMethodId && processor.Transactions.Any(transactionTypeId) && processor.Coin.Iso4217Code == iso4217Code && processor.Driver is ProcessorDriver)
                {
                    paymentProcessor = processor;
                    break;
                }
            }
        }

        internal IEnumerable<PaymentProcessor> SearchProcessors(int entityId, int paymentMethodId, int transactionTypeId, int coinId)
        {
            if (entityId <= 0) throw new GameEngineException($"{nameof(entityId)} must be greater than 0");
            if (paymentMethodId <= 0) throw new GameEngineException($"{nameof(paymentMethodId)} must be greater than 0");
            if (transactionTypeId <= 0) throw new GameEngineException($"{nameof(transactionTypeId)} must be greater than 0");
            if (coinId < 0) throw new GameEngineException($"{nameof(coinId)} must be greater or equal than 0");

            IEnumerable<PaymentProcessor> processors = null;
            var transaction = (ProcessorTransaction)company.System.TransactionTypes.Find(transactionTypeId);
            if (transaction.Name == TransactionType.Deposit.ToString())
                processors = indexedDespositProcessor.SearchBy(TransactionType.Deposit, entityId, paymentMethodId, coinId);
            else if (transaction.Name == TransactionType.Withdrawal.ToString())
                processors = indexedWithdrawalProcessor.SearchBy(TransactionType.Withdrawal, entityId, paymentMethodId, coinId);
            else if (transaction.Name == TransactionType.Sale.ToString())
                processors = indexedSaleProcessor.SearchBy(TransactionType.Sale, entityId, paymentMethodId, coinId);
            else
                processors = Values.SearchBy(transaction, entityId, paymentMethodId, coinId);

            if (processors == null || !processors.Any()) throw new GameEngineException($"No processors with {nameof(entityId)} '{entityId}' {nameof(paymentMethodId)} '{paymentMethodId}' {nameof(transactionTypeId)} '{transactionTypeId}' {nameof(coinId)} '{coinId}'");
            return processors.ToList();
        }

        internal IEnumerable<PaymentProcessor> SearchProcessorsWithDistinctKey()
        {
            var processors = new Dictionary<string, PaymentProcessor>();
            foreach (var processor in Values)
            {
                var processorFound = processors.GetValueOrDefault(processor.ProcessorKey);
                if (processorFound == null) processors.Add(processor.ProcessorKey, processor);
                else
                {
                    if (processor.Driver.Version > processorFound.Driver.Version)
                    {
                        processors[processor.ProcessorKey] = processor;
                    }
                }
            }
            return processors.Values.ToList();
        }

        internal PaymentProcessor SearchBalanceProcesorBy(string iso4217Code)
        {
            return indexedBalanceProcessor.SearchBy(iso4217Code);
        }
        internal PaymentProcessor SearchBalanceProcesorBy(Type t)
        {
            return indexedBalanceProcessor.SearchBy(t);
        }

        internal PaymentProcessor SearchValidateProcesorBy(string iso4217Code)
        {
            return indexedValidateProcessor.SearchBy(iso4217Code);
        }

        internal PaymentProcessor SearchSaleProcessorBy(string iso4217Code)
        {
            return indexedSaleProcessor.SearchBy(iso4217Code);
        }

        internal PaymentProcessor SearchSaleProcessorBy(string iso4217Code, Type type)
        {
            return indexedSaleProcessor.SearchBy(TransactionType.Sale, iso4217Code, type);
        }

        internal PaymentProcessor SearchSaleProcessorBy(Type type)
        {
            return indexedSaleProcessor.SearchBy(type);
        }

        internal PaymentProcessor SearchWithdrawalProcessorBy(string iso4217Code)
        {
            return indexedWithdrawalProcessor.SearchBy(iso4217Code);
        }

        internal PaymentProcessor SearchWithdrawalProcessorBy(string iso4217Code, Type type)
        {
            return indexedWithdrawalProcessor.SearchBy(TransactionType.Withdrawal, iso4217Code, type);
        }

        internal PaymentProcessor SearchWithdrawalProcessorBy(Type type)
        {
            return indexedWithdrawalProcessor.SearchBy(type);
        }

        private PaymentProcessor SearchWithdrawalProcessorBy(Func<PaymentProcessor, bool> filter)
        {
            return indexedWithdrawalProcessor.SearchBy(filter);
        }
        private PaymentProcessor SearchDepositProcessorBy(Func<PaymentProcessor, bool> filter)
        {
            return indexedDespositProcessor.SearchBy(filter);
        }

        internal PaymentProcessor SearchGradeProcessorBy()
        {
            return indexedGradeProcessor.FirstOne();
        }

        internal PaymentProcessor SearchGradeProcessorBy(Type type)
        {
            return indexedGradeProcessor.SearchBy(type);
        }

        internal PaymentProcessor SearchAuthorizationProcessorBy(Type type)
        {
            return indexedAuthorizationProcessor.SearchBy(type);
        }

        internal PaymentProcessor SearchDepositProcessorBy(string iso4217Code)
        {
            return indexedDespositProcessor.SearchBy(iso4217Code);
        }
        internal PaymentProcessor SearchDepositProcessorBy(string iso4217Code, Type type)
        {
            return indexedDespositProcessor.SearchBy(TransactionType.Deposit, iso4217Code, type);
        }

        internal PaymentProcessor SearchDepositProcessorBy(Type type)
        {
            return indexedDespositProcessor.SearchBy(type);
        }

        internal PaymentProcessor SearchBy(TransactionType transactionType, string iso4217Code)
        {
            return Values.SearchByTransactionType(transactionType, iso4217Code);
        }

        internal PaymentProcessor SearchBy(TransactionType transactionType, string iso4217Code, Type processorDriverType)
        {
            return Values.SearchBySpecificClass(transactionType, iso4217Code, processorDriverType);
        }

        internal PaymentProcessor SearchBy(Tenant_Actions processorDriverType)
        {
            return Values.SearchBySpecificClass(processorDriverType);
        }

        internal PaymentProcessor SearchFragmentProcessor()
        {
            return indexedFragmentProcessor.SearchBy(Tenant_Actions.Fragment);
        }

        internal PaymentProcessor SearchFragmentProcessor(Type type)
        {
            return indexedFragmentProcessor.SearchBy(type);
        }

        internal IEnumerable<PaymentProcessor> SearchFragmentProcessors()
        {
            return indexedFragmentProcessor.SearchProcessorsBy(Tenant_Actions.Fragment);
        }

        internal PaymentProcessor SearchOtherProcessorBy(Type processorDriverType)
        {
            return otherProcessors.SearchBy(processorDriverType);
        }

        internal IEnumerable<CatalogMember> Entities()
        {
            var result = new HashSet<CatalogMember>();
            foreach (var processor in Values)
            {
                if (ExistsEnabledEntity(processor.Entity)) result.Add(processor.Entity);
            }
            return result.ToList();
        }

        internal IEnumerable<CatalogMember> Entities(ProcessorTransaction transactionType, ProcessorCoin coin)
        {
            if (transactionType == null) throw new ArgumentNullException(nameof(transactionType));
            if (coin == null) throw new ArgumentNullException(nameof(coin));

            var result = new HashSet<CatalogMember>();
            foreach (var processor in Values)
            {
                if (processor.Coin == coin.Coin && processor.Transactions.Contains(transactionType) && ExistsEnabledEntity(processor.Entity)) result.Add(processor.Entity);
            }
            return result.ToList();
        }

        internal IEnumerable<Entity> EntitiesForCashAndCreditCard(ProcessorTransaction transactionType)
        {
            if (transactionType == null) throw new ArgumentNullException(nameof(transactionType));

            var result = new HashSet<Entity>();
            var paymentMethodCash = SearchPaymentMethodByName(PaymentMethod.Cash.ToString());
            var paymentMethodCreditCard = SearchPaymentMethodByName(PaymentMethod.Creditcard.ToString());
            foreach (var processor in Values)
            {
                if ((processor.Group == paymentMethodCash || processor.Group == paymentMethodCreditCard) && processor.Transactions.Contains(transactionType) && ExistsEnabledEntity(processor.Entity)) 
                    result.Add(processor.Entity);
            }
            return result.ToList();
        }

        internal IEnumerable<CatalogMember> EntitiesPerformingDeposit(ProcessorCoin coin)
        {
            if (coin == null) throw new ArgumentNullException(nameof(coin));
            if (indexedDespositProcessor.IsEmpty()) return Enumerable.Empty<Entity>().ToList();

            var result = new HashSet<CatalogMember>();
            if (indexedDespositProcessor is MultipleProcessors)
            {
                foreach (var processor in (MultipleProcessors)indexedDespositProcessor)
                {
                    if (processor.Coin == coin.Coin && ExistsEnabledEntity(processor.Entity)) result.Add(processor.Entity);
                }
            }
            else
            {
                var firstProcessor = indexedDespositProcessor.FirstOne();
                if (firstProcessor.Coin == coin.Coin && ExistsEnabledEntity(firstProcessor.Entity)) result.Add(firstProcessor.Entity);
            }
            
            return result.ToList();
        }

        internal IEnumerable<PaymentProcessor> ProcessorsForDeposit(Coin coin)
        {
            return indexedDespositProcessor is MultipleProcessors ?((MultipleProcessors)indexedDespositProcessor).SearchByCoin(coin) : new PaymentProcessor[] { indexedDespositProcessor.FirstOne() };
        }

        internal IEnumerable<CatalogMember> EntitiesPerformingWithdrawal(ProcessorCoin coin)
        {
            if (coin == null) throw new ArgumentNullException(nameof(coin));
            if (indexedWithdrawalProcessor.IsEmpty()) return Enumerable.Empty<Entity>().ToList();

            var result = new HashSet<CatalogMember>();
            if (indexedWithdrawalProcessor is MultipleProcessors)
            {
                foreach (var processor in (MultipleProcessors)indexedWithdrawalProcessor)
                {
                    if (processor.Coin == coin.Coin && ExistsEnabledEntity(processor.Entity)) result.Add(processor.Entity);
                }
            }
            else
            {
                var firstProcessor = indexedWithdrawalProcessor.FirstOne();
                if (firstProcessor.Coin == coin.Coin && ExistsEnabledEntity(firstProcessor.Entity)) result.Add(firstProcessor.Entity);
            }

            return result.ToList();
        }

        bool ExistsEnabledEntity(Entity entityToSearch)
        {
            var result = company.System.Entities.Any(entity => entity == entityToSearch && entity.Visible && entity.Enabled);
            return result;
        }

        internal IEnumerable<CatalogMember> PaymentMethods(Entity entity)
        {
            if (entity == null) throw new ArgumentNullException(nameof(entity));

            var result = new HashSet<CatalogMember>();
            foreach (var processor in Values)
            {
                if (processor.Entity == entity)
                {
                    var paymentMethod = company.System.PaymentMethods.FirstOrDefault(method => method == processor.Group && method.Visible && method.Enabled);
                    if (paymentMethod != null) result.Add(paymentMethod);
                }
            }
            return result.ToList();
        }

        internal IEnumerable<ProcessorPaymentMethod> PaymentMethodsForCashAndCreditCard(Entity entity, ProcessorTransaction transactionType)
        {
            if (entity == null) throw new ArgumentNullException(nameof(entity));
            if (transactionType == null) throw new ArgumentNullException(nameof(transactionType));

            var result = new HashSet<ProcessorPaymentMethod>();
            var paymentMethodCash = SearchPaymentMethodByName(PaymentMethod.Cash.ToString());
            var paymentMethodCreditCard = SearchPaymentMethodByName(PaymentMethod.Creditcard.ToString());
            foreach (var processor in Values)
            {
                if (processor.Entity == entity && processor.Transactions.Contains(transactionType))
                {
                    var paymentMethods = company.System.PaymentMethods.Where(method => (method == paymentMethodCash || method == paymentMethodCreditCard) && method == processor.Group && method.Visible && method.Enabled);
                    foreach (var paymentMethod in paymentMethods)
                    {
                        result.Add((ProcessorPaymentMethod)paymentMethod);
                    }
                }
            }
            return result.ToList();
        }

        internal IEnumerable<CatalogMember> TransactionTypes(Entity entity, ProcessorPaymentMethod paymentMethod)
        {
            if (entity == null) throw new ArgumentNullException(nameof(entity));
            if (paymentMethod == null) throw new ArgumentNullException(nameof(paymentMethod));

            var result = new HashSet<CatalogMember>();
            foreach (var processor in Values)
            {
                if (processor.Entity == entity && processor.Group == paymentMethod)
                {
                    var transaction = company.System.TransactionTypes.FirstOrDefault(type => processor.Transactions.Contains(type) && type.Visible && type.Enabled);
                    if (transaction != null) result.Add(transaction);
                }
            }
            return result.ToList();
        }

        internal IEnumerable<CatalogMember> Coins()
        {
            var result = new HashSet<CatalogMember>();
            foreach (var processor in Values)
            {
                var coinFound = company.System.Coins.FirstOrDefault(coin => processor.Coin == ((ProcessorCoin)coin).Coin && coin.Visible && coin.Enabled);
                if (coinFound != null) result.Add(coinFound);
            }
            return result.ToList();
        }

        internal IEnumerable<CatalogMember> Coins(Entity entity, ProcessorPaymentMethod paymentMethod, ProcessorTransaction transactionType)
        {
            if (entity == null) throw new ArgumentNullException(nameof(entity));
            if (paymentMethod == null) throw new ArgumentNullException(nameof(paymentMethod));
            if (transactionType == null) throw new ArgumentNullException(nameof(transactionType));

            var result = new HashSet<CatalogMember>();
            foreach (var processor in Values)
            {
                if (processor.Entity == entity && processor.Group == paymentMethod && processor.Transactions.Contains(transactionType))
                {
                    var coinFound = company.System.Coins.FirstOrDefault(coin => processor.Coin == ((ProcessorCoin)coin).Coin && coin.Visible && coin.Enabled);
                    if (coinFound != null) result.Add(coinFound);
                }
            }
            return result.ToList();
        }

        internal IEnumerable<CatalogMember> Coins(ProcessorTransaction transactionType)
        {
            if (transactionType == null) throw new ArgumentNullException(nameof(transactionType));

            var result = new HashSet<CatalogMember>();
            foreach (var processor in Values)
            {
                if (processor.Transactions.Contains(transactionType))
                {
                    var coinFound = company.System.Coins.FirstOrDefault(coin => processor.Coin == ((ProcessorCoin)coin).Coin && coin.Visible && coin.Enabled);
                    if (coinFound != null) result.Add(coinFound);
                }
            }
            return result.ToList();
        }

        internal void ReorderEntities(ProcessorTransaction transactionType, ProcessorCoin coin, List<int> entityIdsToReorder)
        {
            if (transactionType == null) throw new ArgumentNullException(nameof(transactionType));
            if (coin == null) throw new ArgumentNullException(nameof(coin));
            if (entityIdsToReorder == null || entityIdsToReorder.Count == 0) throw new ArgumentNullException(nameof(coin));
            if (entityIdsToReorder.Count == 1) throw new GameEngineException($"It cannot reorder only one element");
            var anyDuplicate = entityIdsToReorder.GroupBy(id => id).Any(id => id.Count() > 1);
            if (anyDuplicate) throw new GameEngineException($"Any entity id is repeated");
            if (Entities().Count() != entityIdsToReorder.Count) throw new GameEngineException($"Amount of entities ids do not match with amount of entities found for {transactionType} and {coin}");

            Values.ReorderEntities(transactionType, coin.Coin, entityIdsToReorder);
            foreach (var index in allIndexes)
            {
                if (index is MultipleProcessors && index.FirstOne().ContainsTransactionType(transactionType))
                {
                    ((MultipleProcessors)index).ReorderEntities(transactionType, coin.Coin, entityIdsToReorder);
                }
            }
            _paymentProcessorsAndActionsByDomains.ReorderEnabledProcessors(transactionType, coin.Coin, entityIdsToReorder);
        }

        internal IEnumerable<Entity> EntitiesWithProviders(ProcessorTransaction transactionType)
        {
            if (transactionType == null) throw new ArgumentNullException(nameof(transactionType));

            var result = _paymentProcessorsAndActionsByDomains.EntitiesWithProviders(transactionType);
            return result.ToList();
        }

        internal IEnumerable<ProcessorPaymentMethod> PaymentMethodsWithProviders(Entity entity, ProcessorTransaction transactionType)
        {
            if (entity == null) throw new ArgumentNullException(nameof(entity));
            if (transactionType == null) throw new ArgumentNullException(nameof(transactionType));

            var result = _paymentProcessorsAndActionsByDomains.PaymentMethodsWithProviders(entity, transactionType);
            return result.ToList();
        }

        internal PaymentProcessor SearchProcessorWithHigherVersion(int entityId, int paymentMethodId, int transactionTypeId, int coinId)
        {
            if (entityId <= 0) throw new GameEngineException($"{nameof(entityId)} must be greater than 0");
            if (paymentMethodId <= 0) throw new GameEngineException($"{nameof(paymentMethodId)} must be greater than 0");
            if (transactionTypeId <= 0) throw new GameEngineException($"{nameof(transactionTypeId)} must be greater than 0");
            if (coinId < 0) throw new GameEngineException($"{nameof(coinId)} must be greater or equal than 0");

            var processors = SearchProcessors(entityId, paymentMethodId, transactionTypeId, coinId);
            PaymentProcessor processorWithHigherVersion = processors.First();
            foreach (var paymentProcessor in processors.Skip(1))
            {
                if (paymentProcessor.Driver.Version > processorWithHigherVersion.Driver.Version)
                    processorWithHigherVersion = paymentProcessor;
            }
            
            return processorWithHigherVersion;
        }

        internal bool AllProcessorsWithTheSameVersion(int entityId, int paymentMethodId, int transactionTypeId, int coinId)
        {
            if (entityId <= 0) throw new GameEngineException($"{nameof(entityId)} must be greater than 0");
            if (paymentMethodId <= 0) throw new GameEngineException($"{nameof(paymentMethodId)} must be greater than 0");
            if (transactionTypeId <= 0) throw new GameEngineException($"{nameof(transactionTypeId)} must be greater than 0");
            if (coinId < 0) throw new GameEngineException($"{nameof(coinId)} must be greater or equal than 0");

            var processors = SearchProcessors(entityId, paymentMethodId, transactionTypeId, coinId);
            float version = processors.First().Driver.Version;
            foreach (var paymentProcessor in processors.Skip(1))
            {
                if (paymentProcessor.Driver.Version != version) return false;
            }

            return true;
        }

        internal void EnableProcessor(int entityId, int paymentMethodId, int transactionTypeId, int coinId)
        {
            if (entityId <= 0) throw new GameEngineException($"{nameof(entityId)} must be greater than 0");
            if (paymentMethodId <= 0) throw new GameEngineException($"{nameof(paymentMethodId)} must be greater than 0");
            if (transactionTypeId <= 0) throw new GameEngineException($"{nameof(transactionTypeId)} must be greater than 0");
            if (coinId < 0) throw new GameEngineException($"{nameof(coinId)} must be greater or equal than 0");

            var processorWithHigherVersion = SearchProcessorWithHigherVersion(entityId, paymentMethodId, transactionTypeId, coinId);
            if (processorWithHigherVersion.Enabled) throw new GameEngineException($"{nameof(processorWithHigherVersion)} is already enabled");
            processorWithHigherVersion.Enabled = true;
        }

        internal void DisableProcessor(int entityId, int paymentMethodId, int transactionTypeId, int coinId)
        {
            if (entityId <= 0) throw new GameEngineException($"{nameof(entityId)} must be greater than 0");
            if (paymentMethodId <= 0) throw new GameEngineException($"{nameof(paymentMethodId)} must be greater than 0");
            if (transactionTypeId <= 0) throw new GameEngineException($"{nameof(transactionTypeId)} must be greater than 0");
            if (coinId < 0) throw new GameEngineException($"{nameof(coinId)} must be greater or equal than 0");

            var processorWithHigherVersion = SearchProcessorWithHigherVersion(entityId, paymentMethodId, transactionTypeId, coinId);
            if (!processorWithHigherVersion.Enabled) throw new GameEngineException($"{nameof(processorWithHigherVersion)} is already disabled");
            processorWithHigherVersion.Enabled = false;
        }

        internal void UpdateAlias(int entityId, int paymentMethodId, int transactionTypeId, int coinId, string alias)
        {
            if (entityId <= 0) throw new GameEngineException($"{nameof(entityId)} must be greater than 0");
            if (paymentMethodId <= 0) throw new GameEngineException($"{nameof(paymentMethodId)} must be greater than 0");
            if (transactionTypeId <= 0) throw new GameEngineException($"{nameof(transactionTypeId)} must be greater than 0");
            if (coinId < 0) throw new GameEngineException($"{nameof(coinId)} must be greater or equal than 0");
            if (string.IsNullOrEmpty(alias)) throw new ArgumentNullException(nameof(alias));

            var processorWithHigherVersion = SearchProcessorWithHigherVersion(entityId, paymentMethodId, transactionTypeId, coinId);
            processorWithHigherVersion.Alias = alias;
        }

        public class PaymentProcessor : CatalogMember
        {
            public PaymentProcessor(int id, string name, Entity entity, ProcessorPaymentMethod paymentMethod, ProcessorCoin coin, GroupOFTransactions transactions, Driver implementation) :
                base(id, name)
            {
                Entity = entity;
                Group = paymentMethod;
                ProcessorCoin = coin;
                Transactions = transactions;
                Driver = implementation;
                ProcessorKey = $"{Entity.Name}_{Group.Name}_{Transactions.TransactionsAsText}_{ProcessorCoin.Iso4217Code}";
                Alias = ProcessorKey;
            }

            internal CustomSettingsCollection CustomSettings { get; set; }
            public Entity Entity { get; }
            public ProcessorPaymentMethod Group { get; }
            public string PaymentMethodAsText => Group.Name;
            internal town.connectors.drivers.PaymentMethod PaymentMethodType { get { return Driver.PaymentMethod; } }
            public Coin Coin => ProcessorCoin.Coin;
            public ProcessorCoin ProcessorCoin { get; }
            public GroupOFTransactions Transactions { get; }
            public Driver Driver { get; }
            public string DriverId => Driver.Id;
            public string Description => Driver.Description;
            public string Version => string.Format("{0:N2}", Driver.Version);
            public string CurrencyIso4217Code { get { return Coin.Iso4217Code; } }
            public string Alias { get; set; }
            public string ProcessorKey { get; }

            public T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
            {
                if (Driver is ProcessorDriver)
                {
                    var result = (Driver as ProcessorDriver).Execute<T>(now, recordSet);
                    if (!(result is Result)) throw new GameEngineException($"{nameof(ProcessorDriver)}s can only return {nameof(Result)} subclases");
                    return result;
                }
                else if (Driver is TenantDriver)
                {
                    return (Driver as TenantDriver).Execute<T>(now, recordSet);
                }
                else
                {
                    throw new GameEngineException($"Driver {Driver.GetType().Name} is not configured.");
                }
            }

            public async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
            {
                if (Driver is ProcessorDriver)
                {
                    var result = await (Driver as ProcessorDriver).ExecuteAsync<T>(now, recordSet);
                    if (!(result is Result)) throw new GameEngineException($"{nameof(ProcessorDriver)}s can only return {nameof(Result)} subclases");
                    return result;
                }
                else if (Driver is TenantDriver)
                {
                    return await  (Driver as TenantDriver).ExecuteAsync<T>(now, recordSet);
                }
                else
                {
                    throw new GameEngineException($"Driver {Driver.GetType().Name} is not configured.");
                }
            }
            internal bool ContainsTransactionType(string transactionTypeName)
            {
                return Transactions.FirstOrDefault(x => x.Visible && x.Enabled && x.ToString() == transactionTypeName ) != null;
            }
            internal bool ContainsTransactionType(ProcessorTransaction transaction)
            {
                return Transactions.Any(transaction);
            }

            internal RecordSet GetRecordSet()
            {
                return Driver.CustomSettings.GetRecordSet();
            }

            internal void Prepare(DateTime now)
            {
                Driver.Prepare(now);
            }

            internal bool UseDriver(Type type)
            {
                return Driver.GetType() == type;
            }

            public void NotifyDeposit(bool itsThePresent, DateTime creationDate, string accountNumber, int AuthorizationId, string description, Currency amount, string identificationDocumentNumber, 
                string customerAccountNumber, int domainId, string domainUrl, TransactionFees fees, int storeId, string employeeName)
            {
                NotifyDeposit(itsThePresent, creationDate, accountNumber, AuthorizationId, description, amount, identificationDocumentNumber, customerAccountNumber, domainId, domainUrl, fees, storeId, employeeName, default(Execution));
            }
            public void NotifyDeposit(bool itsThePresent, DateTime creationDate, string accountNumber, int AuthorizationId, string description, Currency amount, string identificationDocumentNumber, 
                string customerAccountNumber, int domainId, string domainUrl, TransactionFees fees, int storeId, string employeeName, Execution execution)
            {
                if (!Transactions.Search(TransactionType.Deposit.ToString())) throw new GameEngineException($"{TransactionType.Deposit.ToString()} it's not allowed for {this.GetType().Name}.");
                if (CurrencyIso4217Code != amount.CurrencyCode) throw new GameEngineException($"{nameof(PaymentProcessor)} doesn't support transactions in {amount.CurrencyCode.ToString()}");
                if (string.IsNullOrEmpty(description)) throw new ArgumentNullException(nameof(description));
                if (string.IsNullOrEmpty(domainUrl)) throw new ArgumentNullException(nameof(domainUrl));
                if (fees == null) throw new ArgumentNullException(nameof(fees));

                if (itsThePresent)
                {
                    OperationMessage msn = new OperationMessage(
                        accountNumber,
                        TransactionType.Deposit,
                        creationDate,
                        AuthorizationId,
                        description,
                        amount,
                        Group,
                        Driver.Id,
                        domainId,
                        domainUrl,
                        fees,
                        execution,
                        identificationDocumentNumber, 
                        storeId,
                        employeeName
                        );

                    Integration.Kafka.Send(itsThePresent, Integration.Kafka.TopicForGuardianOperations, msn);
                }
            }
            public void NotifyPay()
            {
                if (Transactions.Search(TransactionType.Sale.ToString())) throw new GameEngineException($"{TransactionType.Sale.ToString()} it's not allowed for {this.GetType().Name}.");

                throw new NotImplementedException();
            }
            public void NotifyWithDraw(bool itsThePresent, DateTime creationDate, int transactionId, string description, Currency disbursementAmount, string identificationDocumentNumber, 
                string customerAccountNumber, Domain domain, TransactionFees fees, int storeId, string employeeName)
            {
                NotifyWithDraw(itsThePresent, creationDate, transactionId, description, disbursementAmount, identificationDocumentNumber, customerAccountNumber, domain, fees, storeId, employeeName, default(Execution));
            }
            public void NotifyWithDraw(bool itsThePresent, DateTime creationDate, int transctionId, string description, Currency disbursementAmount, string identificationDocumentNumber, 
                string customerAccountNumber, Domain domain, TransactionFees fees, int storeId, string employeeName, Execution execution)
            {
                if (!Transactions.Search(TransactionType.Withdrawal.ToString())) throw new GameEngineException($"{TransactionType.Withdrawal.ToString()} it's not allowed for {this.GetType().Name}.");
                if (CurrencyIso4217Code != disbursementAmount.CurrencyCode) throw new GameEngineException($"{nameof(PaymentProcessor)} doesn't support transactions in {disbursementAmount.CurrencyCode.ToString()}");
                if (string.IsNullOrEmpty(description)) throw new ArgumentNullException(nameof(description));
                if (domain == null) throw new ArgumentNullException(nameof(domain));
                if (fees == null) throw new ArgumentNullException(nameof(fees));

                if (itsThePresent)
                {
                    OperationMessage msn = new OperationMessage(
                        identificationDocumentNumber,
                        TransactionType.Withdrawal,
                        creationDate,
                        transctionId,
                        description,
                        disbursementAmount,
                        Group,
                        Driver.Id,
                        domain.Id,
                        domain.Url,
                        fees,
                        execution,
                        identificationDocumentNumber,
                        storeId,
                        employeeName
                        );

                    Integration.Kafka.Send(itsThePresent, Integration.Kafka.TopicForGuardianOperations, msn);
                }
            }

        }

        internal ProcessorTransaction SearchTransactionByName(string type)
        {
            if (string.IsNullOrWhiteSpace(type)) throw new ArgumentNullException(nameof(type));

            var transaction = company.System.TransactionTypes.SearchByName<ProcessorTransaction>(type);
            return transaction;
        }

        internal ProcessorPaymentMethod SearchPaymentMethodByName(string method)
        {
            if (string.IsNullOrWhiteSpace(method)) throw new ArgumentNullException(nameof(method));

            var result = company.System.PaymentMethods.SearchByName<ProcessorPaymentMethod>(method);
            return result;
        }

        struct SelectedDriver
        {
            public ProcessorPaymentMethod PaymentMethod { get; set; }
            public string CurrencyIso4217Code { get; set; }
            public bool IsProcessorDriver { get; set; }
            public ProcessorTransaction TransactionType { get; set; }
            public Entity Entity { get; set; }
            public Driver Implementation { get; set; }
            public SelectedDriver(ProcessorPaymentMethod PaymentMethod, string CurrencyIso4217Code, bool IsProcessorDriver, ProcessorTransaction TransactionType, Entity Entity, Driver Implementation)
            {
                this.PaymentMethod = PaymentMethod;
                this.CurrencyIso4217Code = CurrencyIso4217Code;
                this.IsProcessorDriver = IsProcessorDriver;
                this.TransactionType = TransactionType;
                this.Entity = Entity;
                this.Implementation = Implementation;
            }
        }

        internal bool Load(bool itIsThePresent, DateTime now, CustomSettingsCollection cs)
        {
            string path = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
            string logs = "";
            Drivers = Driver.LoadAllConnectorsFrom(path, out logs);
            
            Reload(now, cs);

            return Values.Count() > 0;
        }
        internal void Reload()
        {
            Reload(DateTime.Now, null);
        }
        internal void Reload(DateTime now, CustomSettingsCollection customSettings)
        {
            bool loadWasCalledAtLeastOneTime = customSettings != null && Drivers != null;
            if (!loadWasCalledAtLeastOneTime) return;

            allIndexes.Clear();
            members.Clear();
            Values.Clear();
            indexedBalanceProcessor = new SingleProcessor();
            indexedValidateProcessor = new SingleProcessor();
            indexedGradeProcessor = new SingleProcessor();
            indexedFragmentProcessor = new SingleProcessor();
            indexedAuthorizationProcessor = new SingleProcessor();
            otherProcessors = new SingleProcessor();
            indexedDespositProcessor = new SingleProcessor();
            indexedSaleProcessor = new SingleProcessor();
            indexedWithdrawalProcessor = new SingleProcessor();

            var selectedDrivers = new List<SelectedDriver>();
            foreach (var driver in Drivers)
            {
                foreach (var isoCode in driver.CurrencyIsoCodes)
                {
                    selectedDrivers.Add(new SelectedDriver(
                        (ProcessorPaymentMethod)company.System.PaymentMethods.
                            FirstOrDefault(x => x.Visible && x.Enabled &&
                            x.Name == driver.PaymentMethod.ToString()),
                        (company.System.Coins.
                        FirstOrDefault(x => x.Visible && x.Enabled &&
                        (x as ProcessorCoin).Coin.Iso4217Code == isoCode) as ProcessorCoin)?.Coin.Iso4217Code,
                        driver is ProcessorDriver,
                        (ProcessorTransaction)company.System.TransactionTypes.
                            FirstOrDefault(x => x.Visible && x.Enabled &&
                            (driver is TenantDriver ||
                            (driver is ProcessorDriver &&
                            (driver is town.connectors.drivers.fiero.DepositThenLock || driver is town.connectors.drivers.fiero.DepositThenLockForMultipleAuthorizations) ?
                                x.Name.StartsWith($"{TransactionType.Deposit.ToString()} then Lock") :
                                x.Name == (driver as ProcessorDriver).TransactionType.ToString()))),
                        (Entity)company.System.Entities.
                            FirstOrDefault(x => x.Visible && x.Enabled &&
                            x.Name == driver.Entity),
                        driver)
                    );
                }
            }

            foreach (var driver in selectedDrivers)
            {

                if (driver.Entity == null
                    || driver.PaymentMethod == null
                    || driver.CurrencyIso4217Code == null
                    || driver.TransactionType == null
                    || driver.Implementation == null)
                    continue;

                bool deactivateProcessorAdded = false;
                if (driver.IsProcessorDriver)
                {
                    company.System.PaymentProcessor.SearchProcessor(driver.Entity.Id, driver.PaymentMethod.Id, driver.TransactionType.Id, driver.CurrencyIso4217Code, out PaymentProcessor processorFoundWithTheSameParams);
                    if (processorFoundWithTheSameParams != null)
                    {
                        if (driver.Implementation.Version >= processorFoundWithTheSameParams.Driver.Version) processorFoundWithTheSameParams.Enabled = false;
                        if (driver.Implementation.Version <= processorFoundWithTheSameParams.Driver.Version) 
                            deactivateProcessorAdded = true;
                    }
                }
                
                var processorAdded = (PaymentProcessor)company.System.PaymentProcessor.Add(
                driver.Entity,
                driver.PaymentMethod,
                driver.CurrencyIso4217Code,
                driver.TransactionType,
                driver.Implementation);
                if (deactivateProcessorAdded) processorAdded.Enabled = false;
                processorAdded.CustomSettings = new CustomSettingsCollection(company);
                processorAdded.CustomSettings.CopyFrom(customSettings);

                driver.Implementation.ConfigureThenPrepare(now, customSettings.Values);

            }
            allIndexes.AddRange(new IndexedProcessors[] {
                indexedBalanceProcessor,
                indexedValidateProcessor,
                indexedFragmentProcessor,
                otherProcessors,
                indexedDespositProcessor,
                indexedSaleProcessor,
                indexedWithdrawalProcessor,
                indexedGradeProcessor,
                indexedAuthorizationProcessor
            }
            );
            ReloadActionAndDomains();

        }

        internal void ReloadActionAndDomains()
        {
            var actionsWithDomainAndDriver = Enumerable.Empty<PaymentProcessor>();
            if (_paymentProcessorsAndActionsByDomains != null) actionsWithDomainAndDriver = _paymentProcessorsAndActionsByDomains.ActionsWithDomainAndDriver;

            _paymentProcessorsAndActionsByDomains = new PaymentProcessorsAndActionsByDomains(this);
            _paymentProcessorsAndActionsByDomains.Reload(actionsWithDomainAndDriver, Values);
        }
        private bool IsABalanceDriver(Driver driver)
        {
            return driver is TenantDriver &&
                    (driver as TenantDriver).TenantAction == Tenant_Actions.Balance;
        }
        private bool IsASaleDriver(Driver driver)
        {
            return driver is ProcessorDriver &&
                    (driver as ProcessorDriver).TransactionType == TransactionType.Sale;
        }
        private bool IsAWithdrawalDriver(Driver driver)
        {
            return driver is ProcessorDriver &&
                    (driver as ProcessorDriver).TransactionType == TransactionType.Withdrawal;
        }

        private bool IsADepositDriver(Driver driver)
        {
            return driver is ProcessorDriver &&
                    (driver as ProcessorDriver).TransactionType == TransactionType.Deposit;
        }
        private bool IsAValidateDriver(Driver driver)
        {
            return driver is TenantDriver &&
                    (driver as TenantDriver).TenantAction == Tenant_Actions.Validate;
        }
        private bool IsAFragmentDriver(Driver driver)
        {
            return driver is TenantDriver &&
                    (driver as TenantDriver).TenantAction == Tenant_Actions.Fragment;
        }
        private bool IsAnOtherDriver(Driver driver)
        {
            return driver is TenantDriver &&
                    (driver as TenantDriver).TenantAction == Tenant_Actions.Others;
        }

        private bool IsAGradeDriver(Driver driver)
        {
            return driver is TenantDriver &&
                    (driver as TenantDriver).TenantAction == Tenant_Actions.Grade;
        }

        private bool IsAnAuthorizationDriver(Driver driver)
        {
            return driver is TenantDriver &&
                    (driver as TenantDriver).TenantAction == Tenant_Actions.AuthorizationInternal;
        }

        internal void TryGetValue(string id, out PaymentProcessor result)
        {
            result = Values.FirstOrDefault(x => x.Driver.Id == id);
        }
    }

    [Puppet]
    internal class CustomSettingsCollection : Objeto, IEnumerable<CustomSetting>
    {
        Dictionary<string, CustomSetting> customSettings = new Dictionary<string, CustomSetting>();
        internal IEnumerable<CustomSetting> CustomSettings 
        {
            get
            {
                return customSettings.Values.ToList();
            }
        }

        internal CustomSettings Values { get; }
        Company company;
        public CustomSettingsCollection(Company company)
        {
            this.company = company;
            Variables variable = new Variables();
            Values = new CustomSettings(variable);
        }

        public void CopyFrom(CustomSettingsCollection customSettingsCollection)
        {
            if (customSettingsCollection == null) throw new ArgumentNullException(nameof(customSettingsCollection));

            foreach (var customSetting in customSettingsCollection.customSettings.Values)
            {
                CustomSetting cs;
                if (customSetting.IsVariable)
                {
                    cs = AddVariableParameter(customSetting.Key);
                }
                else if (customSetting.Type == nameof(Secret))
                {
                    cs = AddSecretParameter(customSetting.StartDate, customSetting.Key, customSetting.AsString);
                }
                else
                {
                    cs = AddFixedParameter(customSetting.StartDate, customSetting.Key, customSetting.AsString);
                }
                cs.Description = customSetting.Description;
                cs.Enabled = customSetting.Enabled;
            }
        }

        internal CustomSetting Get(DateTime now, string key)
        {
            if (now == default(DateTime)) throw new ArgumentNullException(nameof(now));
            if (string.IsNullOrEmpty(key)) throw new ArgumentNullException(nameof(key));

            Values.Get(now, key);
            var result = customSettings[key];
            return result;
        }

        internal CustomSetting AddFixedParameter(DateTime now, string key, object value)
        {
            if (value == null) throw new ArgumentNullException(nameof(value));
            if (now == default(DateTime)) throw new ArgumentNullException(nameof(now));
            if (string.IsNullOrWhiteSpace(key)) throw new ArgumentNullException(nameof(key));

            var setting = Values.AddFixedParameter(now, key, value);
            var myCustomSetting = new CustomSetting(setting);
            this.customSettings.Add(myCustomSetting.Key, myCustomSetting);
            return myCustomSetting;
        }

        internal CustomSetting AddVariableParameter(string key)
        {
            if (string.IsNullOrWhiteSpace(key)) throw new ArgumentNullException(nameof(key));

            var setting = Values.AddVariableParameter(key);
            var myCustomSetting = new CustomSetting(setting);
            this.customSettings.Add(myCustomSetting.Key, myCustomSetting);
            return myCustomSetting;
        }

        internal CustomSetting AddSecretParameter(DateTime now, string key, object value)
        {
            if (value == null) throw new ArgumentNullException(nameof(value));
            if (now == default(DateTime)) throw new ArgumentNullException(nameof(now));
            if (string.IsNullOrWhiteSpace(key)) throw new ArgumentNullException(nameof(key));

            var setting = Values.AddFixedParameter(now, key, new Secret(value.ToString()));
            var myCustomSetting = new CustomSetting(setting);
            this.customSettings.Add(myCustomSetting.Key, myCustomSetting);
            return myCustomSetting;
        }

        internal CustomSetting ChangeValueStartingOn(DateTime dateToApplyTheChange, string key, object value, string user)
        {
            if (value == null) throw new ArgumentException(nameof(value));
            if (dateToApplyTheChange == default(DateTime)) throw new ArgumentException(nameof(dateToApplyTheChange));
            if (string.IsNullOrEmpty(key)) throw new ArgumentException(nameof(key));
            if (! customSettings.ContainsKey(key)) throw new GameEngineException($"{nameof(customSettings)} does not contain setting with key '{key}'");

            Values.ChangeValueStartingOn(dateToApplyTheChange, key, value, user);
            var result = customSettings[key];
            return result;
        }

        internal CustomSetting ChangeValueStartingOn(bool itIsThePresent, DateTime dateToApplyTheChange, string key, object value, string employeeName)
        {
            if (value == null) throw new ArgumentException(nameof(value));
            if (dateToApplyTheChange == default(DateTime)) throw new ArgumentException(nameof(dateToApplyTheChange));
            if (string.IsNullOrWhiteSpace(key)) throw new ArgumentException(nameof(key));
            if (!customSettings.ContainsKey(key)) throw new GameEngineException($"{nameof(customSettings)} does not contain setting with key '{key}'");

            Values.ChangeValueStartingOn(dateToApplyTheChange, key, value, employeeName);
            var result = customSettings[key];
            if (itIsThePresent && (Integration.UseKafka || Integration.UseKafkaForAuto))
            {
                var currentTenant = company.System.Tenants.CurrentTenant;
                var currentStore = company.Sales.CurrentStore;
                CustomSettingMessage msg;
                if (value.GetType() == typeof(int))
                {
                    msg = new CustomSettingMessage(currentTenant.Id, currentStore.Id, currentTenant.Name, currentStore.Alias, key, CustomSettingType.Integer, dateToApplyTheChange.ToString("MM/dd/yyyy HH:mm:ss"), employeeName, value.ToString());
                }
                else if (value.GetType() == typeof(bool))
                {
                    msg = new CustomSettingMessage(currentTenant.Id, currentStore.Id, currentTenant.Name, currentStore.Alias, key, CustomSettingType.Boolean, dateToApplyTheChange.ToString("MM/dd/yyyy HH:mm:ss"), employeeName, value.ToString());
                }
                else
                {
                    msg = new CustomSettingMessage(currentTenant.Id, currentStore.Id, currentTenant.Name, currentStore.Alias, key, CustomSettingType.String, dateToApplyTheChange.ToString("MM/dd/yyyy HH:mm:ss"), employeeName, value.ToString());
                }
                Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForCustomSettings, msg);
            }
            
            return result;
        }

        internal CustomSetting ChangeSecretStartingOn(DateTime dateToApplyTheChange, string key, object value, string user)
        {
            if (value == null) throw new ArgumentException(nameof(value));
            if (dateToApplyTheChange == default(DateTime)) throw new ArgumentException(nameof(dateToApplyTheChange));
            if (string.IsNullOrWhiteSpace(key)) throw new ArgumentException(nameof(key));
            if (!customSettings.ContainsKey(key)) throw new GameEngineException($"{nameof(customSettings)} does not contain setting with key '{key}'");

            Values.ChangeValueStartingOn(dateToApplyTheChange, key, new Secret(value.ToString()), user);
            var result = customSettings[key];
            return result;
        }

        internal CustomSetting ChangeSecretStartingOn(bool itIsThePresent, DateTime dateToApplyTheChange, string key, object value, string employeeName)
        {
            if (value == null) throw new ArgumentException(nameof(value));
            if (dateToApplyTheChange == default(DateTime)) throw new ArgumentException(nameof(dateToApplyTheChange));
            if (string.IsNullOrWhiteSpace(key)) throw new ArgumentException(nameof(key));
            if (!customSettings.ContainsKey(key)) throw new GameEngineException($"{nameof(customSettings)} does not contain setting with key '{key}'");

            Values.ChangeValueStartingOn(dateToApplyTheChange, key, new Secret(value.ToString()), employeeName);
            var result = customSettings[key];
            if (itIsThePresent && (Integration.UseKafka || Integration.UseKafkaForAuto))
            {
                var currentTenant = company.System.Tenants.CurrentTenant;
                var currentStore = company.Sales.CurrentStore;
                var msg = new CustomSettingMessage(currentTenant.Id, currentStore.Id, currentTenant.Name, currentStore.Alias, key, CustomSettingType.Secret, dateToApplyTheChange.ToString("MM/dd/yyyy HH:mm:ss"), employeeName, value.ToString());
                Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForCustomSettings, msg);
            }
            return result;
        }

        internal CustomSetting UpdateDescription(bool itIsThePresent, string key, string description)
        {
            if (string.IsNullOrWhiteSpace(key)) throw new ArgumentException(nameof(key));
            if (string.IsNullOrWhiteSpace(description)) throw new ArgumentException(nameof(description));
            if (!customSettings.ContainsKey(key)) throw new GameEngineException($"{nameof(customSettings)} does not contain setting with key '{key}'");

            var result = customSettings[key];
            result.Description = description;
            if (itIsThePresent && (Integration.UseKafka || Integration.UseKafkaForAuto))
            {
                var currentTenant = company.System.Tenants.CurrentTenant;
                var currentStore = company.Sales.CurrentStore;
                var msg = new CustomSettingMessage(currentTenant.Id, currentStore.Id, currentTenant.Name, currentStore.Alias, key, description);
                Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForCustomSettings, msg);
            }
            return result;
        }

        internal CustomSetting Enable(bool itIsThePresent, string key)
        {
            if (string.IsNullOrWhiteSpace(key)) throw new ArgumentException(nameof(key));
            if (!customSettings.ContainsKey(key)) throw new GameEngineException($"{nameof(customSettings)} does not contain setting with key '{key}'");

            var result = customSettings[key];
            result.Enabled = true;
            if (itIsThePresent && (Integration.UseKafka || Integration.UseKafkaForAuto))
            {
                var currentTenant = company.System.Tenants.CurrentTenant;
                var currentStore = company.Sales.CurrentStore;
                var msg = new CustomSettingMessage(currentTenant.Id, currentStore.Id, currentTenant.Name, currentStore.Alias, key, true);
                Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForCustomSettings, msg);
            }
            return result;
        }

        internal CustomSetting Disable(bool itIsThePresent, string key)
        {
            if (string.IsNullOrWhiteSpace(key)) throw new ArgumentException(nameof(key));
            if (!customSettings.ContainsKey(key)) throw new GameEngineException($"{nameof(customSettings)} does not contain setting with key '{key}'");

            var result = customSettings[key];
            result.Enabled = false;
            if (itIsThePresent && (Integration.UseKafka || Integration.UseKafkaForAuto))
            {
                var currentTenant = company.System.Tenants.CurrentTenant;
                var currentStore = company.Sales.CurrentStore;
                var msg = new CustomSettingMessage(currentTenant.Id, currentStore.Id, currentTenant.Name, currentStore.Alias, key, false);
                Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForCustomSettings, msg);
            }
            return result;
        }

        internal bool IsValidNextDateToChange(DateTime now, DateTime date)
        {
            return now < date;
        }

        public IEnumerator<CustomSetting> GetEnumerator()
        {
            return customSettings.Values.GetEnumerator();
        }

        IEnumerator IEnumerable.GetEnumerator()
        {
            return customSettings.GetEnumerator();
        }
    }

    public class CustomSetting : Objeto
    {
        internal string Key => customSetting.Key;
        internal string AsString => customSetting.AsString;
        internal string Type
        {
            get
            {
                if (string.Equals(customSetting.Type, "Hilera", StringComparison.OrdinalIgnoreCase)) return nameof(String);
                return customSetting.Type;
            }
        }

        internal DateTime StartDate => customSetting.Now;
        internal DateTime NextDateToChange => customSetting.NextDateToChange();
        internal string NextValue => customSetting.NextValue().ToString();
        internal bool HasScheduledChange => customSetting.HasScheduledChange();
        internal bool IsVariable => customSetting.IsVariable;
        internal bool Enabled
        {
            get
            {
                return customSetting.Enable;
            }
            set
            {
                customSetting.Enable = value;
            }
        }
        internal string Description 
        { 
            get
            {
                return customSetting.Description;
            }
            set
            {
                customSetting.Description = value;
            }
        }

        private const int MAX_NUMBER_OF_ENTRIES = 5;
        internal Logs.Log Log { get; }
        CustomSettings.CustomSetting customSetting;
        public CustomSetting(CustomSettings.CustomSetting customSetting)
        {
            if (customSetting == null) throw new ArgumentNullException(nameof(customSetting));

            this.customSetting = customSetting;
            Log = new Logs.Log(customSetting.Key, MAX_NUMBER_OF_ENTRIES);
        }

        public void AddAnnotation(string message, string who, DateTime now)
        {
            if (string.IsNullOrWhiteSpace(message)) throw new ArgumentNullException(nameof(message));
            if (string.IsNullOrWhiteSpace(who)) throw new ArgumentNullException(nameof(who));

            Log.AddEntry(now, who, $"{who} set {message} at {now.ToString("dd/MM/yyyy hh:mm tt")}.");
        }

        public void AddAnnotation(string message, string who, DateTime now, DateTime dateToApply)
        {
            if (string.IsNullOrWhiteSpace(message)) throw new ArgumentNullException(nameof(message));
            if (string.IsNullOrWhiteSpace(who)) throw new ArgumentNullException(nameof(who));

            Log.AddEntry(now, who, $"{who} set {message} at {now.ToString("dd/MM/yyyy hh:mm tt")}. It will be applied at {dateToApply.ToString("dd/MM/yyyy hh:mm tt")}.");
        }
    }

}
