﻿using GamesEngine.Games.Lines;
using GamesEngine.Gameboards.Lines;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GamesEngine.Bets
{
	internal class RiskTags : Objeto
	{
		private readonly Book book;
		private readonly TagCollection tags;
		private readonly TaggedGroup<Player> taggedPlayers;
		private readonly List<Wager> activity;

		internal RiskTags(Book book)
		{
			if (book == null) throw new ArgumentNullException(nameof(book));

			this.book = book;
			this.tags = new TagCollection();
			this.taggedPlayers = new TaggedGroup<Player>();
			this.activity = new List<Wager>();
		}

		internal Tag CreateTag(string label, string imageUrl)
		{
			if (string.IsNullOrWhiteSpace(label)) throw new ArgumentNullException(nameof(label));
			if (string.IsNullOrWhiteSpace(imageUrl)) throw new ArgumentNullException(nameof(imageUrl));

			if (Enumerable.Any(tags, x => x.Label == label)) throw new GameEngineException($"Book already contains a tag with label {label}");
			Tag tag = new Tag(label, imageUrl);
			this.tags.Add(tag);

			return tag;
		}

		internal void RemoveTag(string label)
		{
			if (string.IsNullOrWhiteSpace(label)) throw new ArgumentNullException(nameof(label));
			if (! Enumerable.Any(tags, x => x.Label == label)) throw new GameEngineException($"Book does not contain a tag with label {label}");

			Tag tag = FindTag(label);
			this.tags.Remove(tag);
			var playersWithTag = Players(tag);
            foreach (var player in playersWithTag)
            {
				this.taggedPlayers.Untag(player, tag);
			}
		}

		internal Tag FindTag(string label) 
		{
			if (string.IsNullOrWhiteSpace(label)) throw new ArgumentNullException(nameof(label));

			var result = this.tags.Find(label);
			return result;
		}

		internal bool Has(Tag tag)
		{
			if (tag == null) throw new ArgumentNullException(nameof(tag));

			bool result = this.tags.Has(tag);
			return result;
		}

		internal bool Has(Player player)
		{
			if (player == null) throw new ArgumentNullException(nameof(player));

			var result = this.taggedPlayers.Tags(player).Count() != 0;
			return result;
		}

		internal void Tag(Player player, Tag tag)
		{
			if (player == null) throw new ArgumentNullException(nameof(player));
			if (tag == null) throw new ArgumentNullException(nameof(tag));
			if (!this.Has(tag)) throw new GameEngineException($"Tag {tag.Label} must be defined on book's tag list");
			if (this.taggedPlayers.HasTag(player, tag)) throw new GameEngineException($"Player {player.AccountNumber} already has tag {tag.Label}");

			this.taggedPlayers.Tag(player, tag);
		}

		internal void Untag(Player player, Tag tag)
		{
			if (player == null) throw new ArgumentNullException(nameof(player));
			if (tag == null) throw new ArgumentNullException(nameof(tag));
			if (!this.Has(tag)) throw new GameEngineException($"Tag {tag.Label} must be defined on book's tag list");
			if (!this.taggedPlayers.HasTag(player, tag)) throw new GameEngineException($"Player {player.AccountNumber} does not have tag {tag.Label}");

			this.taggedPlayers.Untag(player, tag);
		}

		internal void Untags(Player player)
		{
			if (player == null) throw new ArgumentNullException(nameof(player));

			this.taggedPlayers.Untags(player);
		}

		internal void RecordActivity(Wager wager, DateTime timestamp)
		{
			if (wager == null) throw new ArgumentNullException(nameof(wager));

			this.activity.Add(wager);
			var tags = Tags(wager.Player);
			PlatformEvent riskEvent;
			switch (wager.Line)
			{
				case SpreadLine _:
					riskEvent = new RiskTagsSpreadLineEvent(timestamp, wager, tags);
					break;
				case MoneyLine _:
					riskEvent = new RiskTagsMoneyLineEvent(timestamp, wager, tags);
					break;
				case TotalPointsLine _:
					riskEvent = new RiskTagsTotalPointsLineEvent(timestamp, wager, tags);
					break;
				case MoneyDrawLine _:
					riskEvent = new RiskTagsMoneyDrawLineEvent(timestamp, wager, tags);
					break;
				case YesNoLine _:
					riskEvent = new RiskTagsYesNoLineEvent(timestamp, wager, tags);
					break;
				case OverUnderLine _:
					riskEvent = new RiskTagsOverUnderLineEvent(timestamp, wager, tags);
					break;
				case FixedLine _:
					riskEvent = new RiskTagsFixedLineEvent(timestamp, wager, tags);
					break;
				default:
					throw new GameEngineException("Unhandled line type");
			}

			PlatformMonitor.GetInstance().WhenNewEvent(riskEvent);
		}

		internal IEnumerable<Tag> Tags(Player player)
		{
			if (player == null) throw new ArgumentNullException(nameof(player));

			var result = this.taggedPlayers.Tags(player).ToList();
			return result;
		}

		internal IEnumerable<Player> Players(Tag tag)
		{
			if (tag == null) throw new ArgumentNullException(nameof(tag));

			var result = this.taggedPlayers.WhoHas(tag);
			return result;
		}

		internal IEnumerable<Tag> Tags()
		{
			return this.tags.ToList();
		}
	}
}
