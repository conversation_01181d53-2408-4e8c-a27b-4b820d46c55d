﻿using System;
using System.Collections.Generic;

namespace GamesEngine.Exchange
{
	public abstract class PaymentProcessorInstance
	{
		private string id;
		private string name;

		internal PaymentProcessorInstance(string id, string name)
		{
			if (string.IsNullOrEmpty(id)) throw new ArgumentException(nameof(id));
			if (string.IsNullOrEmpty(name)) throw new ArgumentException(nameof(name));

			this.id = id;
			this.name = name;
		}
	}

	public interface IProcessorConfiguration{}

	public class PaymentProcessorInstances
	{
		private List<PaymentProcessorInstance> processors = new List<PaymentProcessorInstance>();

		public void Add(PaymentProcessorInstance processor)
		{
			if (processor == null) throw new ArgumentException(nameof(processor));

			processors.Add(processor);
		}

		internal IEnumerable<PaymentProcessorInstance> ListAllBanksProcessor()
		{
			List<PaymentProcessorInstance> result = new List<PaymentProcessorInstance>();
			foreach (PaymentProcessorInstance processor in processors)
			{
				if (processor is BankProcessorInstance) result.Add(processor);
			}

			return result;
		}

		internal IEnumerable<PaymentProcessorInstance> ListAllCardsProcessor()
		{
			List<PaymentProcessorInstance> result = new List<PaymentProcessorInstance>();
			foreach (PaymentProcessorInstance processor in processors)
			{
				if (processor is CardsProcessorInstance) result.Add(processor);
			}

			return result;
		}

		internal IEnumerable<PaymentProcessorInstance> ListAllBtcProcessor()
		{
			List<PaymentProcessorInstance> result = new List<PaymentProcessorInstance>();
			foreach (PaymentProcessorInstance processor in processors)
			{
				if (processor is BtcProcessorInstance) result.Add(processor);
			}

			return result;
		}
	}

	public sealed class CardsProcessorInstance : PaymentProcessorInstance
	{
		public CardsProcessorInstance(string id, string name) : base(id, name)
		{
		}
	}
	public sealed class BankProcessorInstance : PaymentProcessorInstance
	{
		public BankProcessorInstance(string id, string name) : base(id, name)
		{
		}
	}
	public sealed class BtcProcessorInstance : PaymentProcessorInstance
	{
		public BtcProcessorInstance(string id, string name, BTCProcessorInstanceConfiguration configuration) : base(id, name)
		{
			Configuration = configuration;
		}

		public BTCProcessorInstanceConfiguration Configuration { get; }

		public class BTCProcessorInstanceConfiguration : IProcessorConfiguration
		{
			public BTCProcessorInstanceConfiguration( string user, string password, string wallet, string connectionString, string seed)
			{
				if (string.IsNullOrEmpty(user)) throw new ArgumentException(nameof(user));
				if (string.IsNullOrEmpty(password)) throw new ArgumentException(nameof(password));
				if (string.IsNullOrEmpty(wallet)) throw new ArgumentException(nameof(wallet));
				if (string.IsNullOrEmpty(connectionString)) throw new ArgumentException(nameof(connectionString));
				if (string.IsNullOrEmpty(seed)) throw new ArgumentException(nameof(seed));

				User = user;
				Password = password;
				Wallet = wallet;
				ConnectionString = connectionString;
				Seed = seed;
			}

			public string User { get; }
			public string Password { get; }
			public string Wallet { get; }
			public string ConnectionString { get; }
			public string Seed { get; }
		}
	}


}
