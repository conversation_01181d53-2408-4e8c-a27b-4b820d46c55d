﻿using GamesEngine.Bets;
using MySql.Data.MySqlClient;
using Puppeteer;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Interprete.Libraries;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CashierAPI.CashierFollower
{
	internal class FragmentCreationRuleWithFragment : FragmentCreationRule
	{
        public override void Then(Script script)
		{
            int fragmentsSize = FragmentsSize(script.Text);
            if (script.Text.IndexOf(START_TEXT_LINE1) != -1)
            {
                var authorizationNumber = AuthorizationNumber(script.Text);
                if (!string.IsNullOrEmpty(authorizationNumber) && fragmentsSize > 0)
                {
                    DBHelperCashierFollower.UpdateAuthorizationInfoForFragmentCreationWithFragments(authorizationNumber, fragmentsSize, script.DairyId);
                }
            }
            else if (script.Text.IndexOf(START_TEXT_LINE1AlternativeB) != -1)
            {
                var authorizationNumbers = AuthorizationNumbersWithClass(script.Text);
                if (authorizationNumbers != null)
                {
                    foreach (var authorizationNumber in authorizationNumbers)
                    {
                        var authorization = authorizationNumber.Trim();
                        DBHelperCashierFollower.UpdateAuthorizationInfoForFragmentCreationWithFragments(authorization, fragmentsSize, script.DairyId);
                    }
                }
            }
            else if (script.Text.IndexOf(START_TEXT_LINE1AlternativeA) != -1)
            {
                var authorizationNumbers = AuthorizationNumbers(script.Text);
                if (authorizationNumbers != null)
                {
                    foreach (var authorizationNumber in authorizationNumbers)
                    {
                        var authorization = authorizationNumber.Trim();
                        DBHelperCashierFollower.UpdateAuthorizationInfoForFragmentCreationWithFragments(authorization, fragmentsSize, script.DairyId);
                    }
                }
            }
		}

		public override void When(Script script)
		{
			if ((script.Text.IndexOf(START_TEXT_LINE1) != -1 || script.Text.IndexOf(START_TEXT_LINE1AlternativeA) != -1 || script.Text.IndexOf(START_TEXT_LINE1AlternativeB) != -1) && 
                (script.Text.IndexOf(START_TEXT_LINE2) != -1 || script.Text.IndexOf(START_TEXT_LINE2Alternative) != -1) && 
                (script.Text.IndexOf(START_TEXT_LINE3) == -1 || script.Text.IndexOf(START_TEXT_LINE3Alternative) == -1))
			{
				Then(script);
			}
		}

        public int FragmentsSize(string script)
        {
            if (script.IndexOf(START_TEXT_LINE2) != -1)
            {
                UtilStringRules.SetHeaderText(START_TEXT_LINE2, END_TEXT_LINE);
                string[] argumentos = UtilStringRules.GetBodyArgText(script).Split(',');

                int fragmentsSize = int.Parse(argumentos[1].Trim());
                return fragmentsSize;
            }
            else
            {
                return 1;
            }
        }

    }
}
