﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Threading.Tasks;

namespace ExchangeAPI.Model
{
	[DataContract(Name = "VerifiedBatchesDTO")]
	public class VerifiedBatchesDTO
	{
		[DataMember(Name = "verifiedBatches")]
		public List<VerifyBatchDTO> VerifiedBatches { get; set; } = new List<VerifyBatchDTO>();

		internal void Add(VerifyBatchDTO batch)
		{
			VerifiedBatches.Add(batch);
		}
	}


	[DataContract(Name = "VerifyBatchDTO")]
	public class VerifyBatchDTO
	{
		[DataMember(Name = "date")]
		public string Date { get; set; }
		[DataMember(Name = "currencyCode")]
		public string CurrencyCode { get; internal set; }
		[DataMember(Name = "initial")]
		public string Initial { get; internal set; }
		[DataMember(Name = "available")]
		public string Available { get; internal set; }
		[DataMember(Name = "locked")]
		public string Locked { get; internal set; }
		[DataMember(Name = "lockedAccumulated")]
		public string LockedAccumulated { get; internal set; }
		[DataMember(Name = "spendAccumulated")]
		public string SpendAccumulated { get; internal set; }
		[DataMember(Name = "batchNumber")]
		public long BatchNumber { get; internal set; }
	}
}
