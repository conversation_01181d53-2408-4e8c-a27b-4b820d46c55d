﻿using GamesEngine.Games;
using GamesEngine.Games.Tournaments;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Gameboards.Lines
{
	[Puppet]
	public abstract class WagerAnswer:Objeto
	{
		internal abstract string Text { get; }
		internal abstract string ChosenAnswerAsText();
	}

	internal class ABAnswer : WagerAnswer
	{
		private readonly Game game;
		private readonly Team choosenTeam;

		internal ABAnswer(Game game, Team choosenTeam)
		{
			if (game == null) throw new ArgumentNullException(nameof(game));
			if (choosenTeam == null) throw new ArgumentNullException(nameof(choosenTeam));
			if (game.TeamA != choosenTeam && game.TeamB != choosenTeam) throw new GameEngineException($"Choosen team {choosenTeam.Name} is not a participant of the game {this.game.ToString()}");

			this.game = game;
			this.choosenTeam = choosenTeam;
		}

		internal Team ChosenTeam
		{
			get
			{
				return this.choosenTeam;
			}
		}

		internal override string Text
		{
			get
			{
				string result = choosenTeam.ShortName;
				return result;
			}
		}

        internal override string ChosenAnswerAsText()
        {
			var result = choosenTeam == game.TeamA ? "A" : "B";
			return result;
		}
    }

	internal class DrawAnswer : WagerAnswer
	{
		private readonly Game game;

		internal DrawAnswer(Game game, Team choosenTeam1, Team choosenTeam2)
		{
			if (game == null) throw new ArgumentNullException(nameof(game));
			if (choosenTeam1 == null) throw new ArgumentNullException(nameof(choosenTeam1));
			if (choosenTeam2 == null) throw new ArgumentNullException(nameof(choosenTeam2));
			if (game.TeamA != choosenTeam1 && game.TeamB != choosenTeam1) throw new GameEngineException($"Choosen team {choosenTeam1.Name} is not a participant of the game {this.game.ToString()}");
			if (game.TeamA != choosenTeam2 && game.TeamB != choosenTeam2) throw new GameEngineException($"Choosen team {choosenTeam2.Name} is not a participant of the game {this.game.ToString()}");
			if (choosenTeam1 == choosenTeam2) throw new GameEngineException("Teams must be different");

			this.game = game;
		}

		internal override string Text
		{
			get
			{
				string result = "Draw";
				return result;
			}
		}

		internal override string ChosenAnswerAsText()
		{
			var result = "D";
			return result;
		}
	}


	internal class YesNoAnswer : WagerAnswer
	{
		internal static YesNoAnswer YES = new YesNoAnswer();
		internal static YesNoAnswer NO = new YesNoAnswer();

		private YesNoAnswer()
		{

		}

		internal override string Text
		{
			get
			{
				string result = this == YES ? "Yes" : "No";				
				return result;
			}
		}

		internal override string ChosenAnswerAsText()
		{
			var result = this == YES ? "Y" : "N";
			return result;
		}
	}

	internal class OverUnderAnswer : WagerAnswer
	{
		internal static OverUnderAnswer OVER = new OverUnderAnswer();
		internal static OverUnderAnswer UNDER = new OverUnderAnswer();
		internal static OverUnderAnswer NO_ACTION = new OverUnderAnswer();

		private OverUnderAnswer()
		{

		}

		internal override string Text
		{
			get
			{
				string result = "";
				if (this == OVER) result = "O";
				else if (this == UNDER) result = "U";
				else if (this == NO_ACTION) result = "Refund";
				return result;
			}
		}

		internal override string ChosenAnswerAsText()
		{
			var result = this == OVER ? "O" : "U";
			return result;
		}
	}

	internal class MultipleOptions : IEnumerable<FixedAnswer>
	{
		private readonly Game game;
		private readonly IEnumerable<string> options;
		private readonly FixedAnswer[] answers;

		internal MultipleOptions(Game game, IEnumerable<string> options)
		{
			if (game == null) throw new ArgumentNullException(nameof(game));
			if (options == null) throw new ArgumentNullException(nameof(options));
			if (options.Any(x => string.IsNullOrWhiteSpace(x))) throw new ArgumentNullException(nameof(options));

			this.game = game;
			this.options = options;
			var count = options.Count();
			answers = new FixedAnswer[count];
			for(int i = 0; i < count; i++)
			{
				answers[i] = new FixedAnswer(game, options.ElementAt(i), i);
			}
		}

		public IEnumerator<FixedAnswer> GetEnumerator()
		{
			return this.GetEnumerator();
		}

		IEnumerator IEnumerable.GetEnumerator()
		{
			return this.answers.GetEnumerator();
		}

		internal FixedAnswer GetAnswer(string option)
		{
			if (string.IsNullOrWhiteSpace(option)) throw new ArgumentNullException(nameof(option));
			for (int i = 0; i < options.Count(); i++)
			{
				if(this.options.ElementAt(i) == option)
				{
					return answers[i];
				}
			}
			throw new GameEngineException($"Option {option} does not belong to this line");
		}

		internal bool ExistsOption(string option)
		{
			if (string.IsNullOrWhiteSpace(option)) throw new ArgumentNullException(nameof(option));
			for (int i = 0; i < options.Count(); i++)
			{
				if (this.options.ElementAt(i) == option)
				{
					return true;
				}
			}
			return false;
		}
	}

	internal class FixedAnswer : WagerAnswer
	{
		private readonly Game game;
		private readonly string option;
		private readonly int ordinal;

		internal FixedAnswer(Game game, string option, int ordinal)
		{
			if (game == null) throw new ArgumentNullException(nameof(game));
			if (string.IsNullOrWhiteSpace(option)) throw new ArgumentNullException(nameof(option));

			this.game = game;
			this.option = option;
			this.ordinal = ordinal;
		}

		internal string ChosenOption
		{
			get
			{
				return this.option;
			}
		}

		internal int Ordinal
		{
			get
			{
				return this.ordinal;
			}
		}

		internal override string Text => this.option;

		internal override string ChosenAnswerAsText()
		{
			return option;
		}
	}
}
