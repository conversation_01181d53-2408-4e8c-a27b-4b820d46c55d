﻿using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GamesEngine.Finance
{
	[Puppet]
	internal class Sources : Objeto
	{
		private List<Source> sources = new List<Source>();
		private AtAddress owner;
		internal const int NO_SOURCE_ID = 0;
		internal Sources(AtAddress owner)
		{
			if (owner == null) throw new ArgumentException(nameof(owner));

			this.owner = owner;
		}

		internal IEnumerable<Source> List()
		{
			return sources;
		}

		internal Source CreateNewSource(bool itIsThePresent, DateTime creationDate, int sourceNumber, Coin coin)
		{
			Source a = new Source(itIsThePresent, creationDate, sourceNumber, coin, owner);
			if (sources.Any(x => x.Number == sourceNumber)) throw new GameEngineException($"Source number {sourceNumber} already exist");
			sources.Add(a);
			return a;
		}

		internal Source SearchByNumber(int number)
		{
			Source result= sources.Where(account =>  account.Number == number ).FirstOrDefault();
			return result;
		}
	}
}
