﻿using GamesEngine.Custodian.Operations;
using GamesEngine.Finance;
using GamesEngine.Settings;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using town.connectors.drivers;

namespace GamesEngine.Custodian.Persistance
{
	public class Transactions
	{
		#region Singleton
		private static Transactions instance = new Transactions();

		public static Transactions GetInstance()
		{
			return instance;
		}

		private Transactions()
		{
			if (Integration.Db.DBSelected == "MySQL")
			{
				storage = new MysqlConnection(Integration.Db.MySQL);
			}
			else if (string.IsNullOrEmpty(Integration.Db.DBSelected))
			{
				storage = new StorageInMemory();
			}
			else
			{
				throw new GameEngineException($"There is no implementation for {Integration.Db.DBSelected} yet.");
			}
		}
		#endregion

		private Storage storage;
		internal void CreateSchema()
		{
			storage.Create();
		}

		public void Save(InternalOperationUpdateMessage operation)
		{
			long domainId;

			domainId = storage.SaveDomainIfNotExist(operation.Domain);
			if (operation.Abbreviation == TransactionType.Withdrawal && (operation.Status != Operation.StatusCodes.PENDING || operation.Scheduled)) storage.IncreaseTimestamp(operation.TransactionId);
			storage.InsertOperation(operation, domainId);
		}

		public void Save(InternalOperationUpdateMessageWithProfiles operation)
		{
			long domainId;

			domainId = storage.SaveDomainIfNotExist(operation.Domain);
			storage.IncreaseTimestamp(operation.TransactionId);
			storage.InsertOperationWithProfiles(operation, domainId);
		}

		public async Task<IEnumerable<StoredOperation>> ListPendingOperationsAsync(int approver, OperationSchedule scheduled, DateTime initDate, DateTime finalDate, int amountOfRows, long inicialIndex)
		{
			return await storage.ListPendingOperationsAsync(approver, scheduled, initDate, finalDate, amountOfRows, inicialIndex);
		}
		public async Task<IEnumerable<StoredOperation>> ListInprocessOperationsAsync(int approver, OperationExecution executed, DateTime initDate, DateTime finalDate, int amountOfRows, long inicialIndex)
		{
			return await storage.ListInprocessOperationsAsync(approver, executed, initDate, finalDate, amountOfRows, inicialIndex);
		}
		public async Task<IEnumerable<StoredOperation>> ListCompleteOperationsAsync(int approver, DateTime initDate, DateTime finalDate, int amountOfRows, long inicialIndex, TransactionType[] transactionTypes)
		{
			return await storage.ListCompleteOperationsAsync(approver, initDate, finalDate, amountOfRows, inicialIndex, transactionTypes);
		}
		public async Task<IEnumerable<ApproverStored>> ListPendingOperationApproversAsync(int pendeingOperationId)
		{
			return await storage.ListPendingOperationApproversAsync(pendeingOperationId);
		}
		internal void Save(List<DisbursementExecutionMessage> disbursementExecutionMessages)
		{
			storage.Save(disbursementExecutionMessages);
		}
		internal void SaveApprover(ApproverCreationMessage operationMessage)
		{
			storage.SaveApprover(operationMessage);
		}
		internal void SaveProfile(ProfileCreationMessage operationMessage)
		{
			storage.SaveProfile(operationMessage);
		}
		internal async Task<DisbursementExecutedResponse> DisbursementExecutedResponseAsync(int transactionId)
		{
			DisbursementExecutedResponse disbursementExecutedResponse = await storage.DisbursementExecutedResponseAsync(transactionId);
			return disbursementExecutedResponse;
		}

		internal async Task<List<ProfileCreationMessage>> ListProfileAsync()
		{
			return await storage.ListProfileAsync();
		}

		internal void SaveAccountNumber(AccountNumberCreationMessage operationMessage)
		{
			storage.SaveAccountNumber(operationMessage);
		}

		internal void SaveCurrency(CoinCreationMessage msg)
		{
			storage.SaveCurrency(msg);
		}
	}
}
