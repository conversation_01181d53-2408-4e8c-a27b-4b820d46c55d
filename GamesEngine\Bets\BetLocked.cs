﻿using GamesEngine.Business;
using GamesEngine.Gameboards;
using GamesEngine.Tools;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamesEngine.Bets
{
    internal sealed class BetLocked : Bet
    {
        private decimal amountToPay;
        private decimal amountApproved;

        internal decimal AmountToPay
        {
            get
            {
                return amountToPay;
            }
            set
            {
                if (value <= 0) throw new GameEngineException($"{nameof(amountToPay)} must be greater than zero.");
                amountToPay = Math.Round(value, 2);
            }
        }

        internal decimal AmountApproved
        {
            get
            {
                return amountApproved;
            }
            set
            {
                if (value <= 0) throw new GameEngineException($"{nameof(amountToPay)} must be greater than zero.");
                amountApproved = Math.Round(value, 2);
            }
        }

        internal BetLocked(BetAlive bet): base(bet)
        {
            bet.Invalidate();
        }

        internal BetLocked(BetPayed bet) : base(bet)
        {
            amountApproved = bet.AmountApproved;
            amountToPay = bet.AmountToPay;
        }

        internal BetLocked(BetPunched bet) : base(bet)
        {
        }

        internal BetLocked(BetCanceled bet) : base(bet)
        {
        }

        internal Bet Punch()
        {
            if (Invalid) throw new GameEngineException("This bet was already processed and it can not be processed twice.");
            return new BetPunched(this);
        }

        internal Bet Cancel()
        {
            if (Invalid) throw new GameEngineException("This bet was already processed and it can not be processed twice.");
            
            return new BetCanceled(this);
        }

        internal Bet Pay(decimal prize)
        {
            if (Invalid) throw new GameEngineException("This bet was already processed and it can not be processed twice.");
            Commons.ValidateAmount(prize);

			amountApproved = prize;
            return new BetPayed(this);
        }

        internal override BetState State
        {
            get
            {
                return BetState.LOCKED;
            }
        }
    }
}
