﻿using ExchangeAPI.ExchangeEntities;
using System;
using System.Collections.Generic;
using System.Text;

namespace GamesEngine.Exchange.Persistance
{
	public interface IDomains
	{
		ExchangeAPI.ExchangeEntities.Domains Domain(string domain);
	}
	public interface IExchangeUsers
	{
		Exchangeuser User(string name);
	}
	public interface IExchangeRates
	{
		void Save(Exchangerate exchangerate);

		Exchangerate CurrentExchangeRateFor(string fromCurrencyCode, string toCurrencyCode, DateTime date);
	}
	public interface IDeposits
	{
		void Save(ExchangeAPI.ExchangeEntities.Deposit deposit);
	}
	public interface IWithdrawals
	{
		void Save(Withdrawal withdrawal);
	}
	public interface ITransfers
	{
		void Save(Transfer transfer);
	}

}
