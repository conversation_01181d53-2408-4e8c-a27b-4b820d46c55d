﻿using GamesEngine.PurchaseOrders;
using Newtonsoft.Json;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using System.Text;
using static GamesEngine.PurchaseOrders.Customer;

namespace GamesEngine.Customers
{
	public class CustomerForm: Objeto
    {
        public const string PROPERTY_CUSTOMER_TYPE_ID = "customer_type_id";
        public const string PROPERTY_CUSTOMER_ACCOUNT_NUMBER = "account_number";
        public const string PROPERTY_TEMPLATE_VERSION = "Template";
        public const string PROPERTY_TEMPLATE_ID = "templateId";
        public const string PROPERTY_CREATION_DATE = "creation_date";
        public const string PROPERTY_LAST_UPDATE = "last_update";
        public const string PROPERTY_APPROVAL_DATE = "approval_date";
        public const string PROPERTY_REJECTION_DATE = "rejection_date";
        public const string PROPERTY_WHO_CREATED = "who_created";
        public const string PROPERTY_WHO_APPROVED = "who_approved";
        public const string PROPERTY_WHO_REJECTED = "who_rejected";
        public const string PROPERTY_STATUS = "status";
        public const string PROPERTY_KEY = "key";
        public const string PROPERTY_ID = "id";

        internal Dictionary<string, FieldData> FieldsData { get;} = new Dictionary<string, FieldData>();
        private Customer Customer { get; }
        private Template Template { get; }
        internal bool IsCustomerAutoApproved { get; private set; }
        internal int TemplateId => Template.Id;
        private readonly StringBuilder logBuilder = new StringBuilder();

        internal string Log 
        { 
            get 
            { 
                return logBuilder.ToString(); 
            } 
        }

        internal bool HasFields
        {
            get
            {
                return FieldsData.Count != 0;
            }
        }

        internal CustomerForm(Template template)
        {
            if (template == null) throw new ArgumentNullException(nameof(template));

            Template = template;
        }

        internal CustomerForm(Customer customer, Template template):this(template)
        {
            if (customer == null) throw new ArgumentNullException(nameof(customer));
            
            Customer = customer;
        }

        internal void AddFields(List<FieldData> fields)
        {
            if (fields.Count == 0) throw new GameEngineException($"{nameof(fields)} to add cannot be empty");

            foreach (var field in fields)
            {
                FieldsData.Add(field.Name, field);
            }
        }

        internal string GetUniqueValue()
        {
            var keyValue = FieldsData[PROPERTY_KEY].Value;
            var keys = keyValue.Split(',');

            string result = null;
            foreach (var key in keys)
            {
                if (result==null) 
                    result = FieldsData[key].Value;
                else
                    result += $",{FieldsData[key].Value}";
            }
            return result;
        }

        internal bool AnyFieldContainsKey(string key)
        {
            var result = Template.AnyFieldContainsKey(key);
            return result;
        }

        internal bool IsRequiredKey(string key)
        {
            var result = key.Equals(PROPERTY_TEMPLATE_ID, StringComparison.OrdinalIgnoreCase) || 
                key.Equals(PROPERTY_TEMPLATE_VERSION, StringComparison.OrdinalIgnoreCase) ||
                key.Equals(Fields.ID_AVATAR, StringComparison.OrdinalIgnoreCase) ||
                key.Equals(Fields.ID_NICKNAME, StringComparison.OrdinalIgnoreCase);
            return result;
        }

        internal bool IsIgnoredKey(string key)
        {
            var result = key.Equals(PROPERTY_CREATION_DATE, StringComparison.OrdinalIgnoreCase) ||
                key.Equals(PROPERTY_LAST_UPDATE, StringComparison.OrdinalIgnoreCase) ||
                key.Equals(PROPERTY_APPROVAL_DATE, StringComparison.OrdinalIgnoreCase) ||
                key.Equals(PROPERTY_REJECTION_DATE, StringComparison.OrdinalIgnoreCase) ||
                key.Equals(PROPERTY_WHO_CREATED, StringComparison.OrdinalIgnoreCase) ||
                key.Equals(PROPERTY_WHO_APPROVED, StringComparison.OrdinalIgnoreCase) ||
                key.Equals(PROPERTY_WHO_REJECTED, StringComparison.OrdinalIgnoreCase) ||
                key.Equals(PROPERTY_STATUS, StringComparison.OrdinalIgnoreCase) ||
                key.Equals(PROPERTY_KEY, StringComparison.OrdinalIgnoreCase) ||
                key.Equals(PROPERTY_CUSTOMER_TYPE_ID, StringComparison.OrdinalIgnoreCase) ||
                key.Equals(PROPERTY_CUSTOMER_ACCOUNT_NUMBER, StringComparison.OrdinalIgnoreCase)
                ;
            return result;
        }

        internal bool IsVerifiableKey(string key)
        {
            var result = key.StartsWith(Fields.PREFIX_VERIFIABLE);
            return result;
        }

        internal bool IsVerifiableField(string key)
        {
            var result = Template.IsVerifiableField(key);
            return result;
        }

        internal bool IsUniqueField(string key)
        {
            var result = Template.IsUniqueField(key);
            return result;
        }

        private string FormatDate(DateTime date)
        {
            return date.ToString("MM/dd/yyyy HH:mm:ss");
        }

        private CustomerStatus? GetStatusOrNull()
        {
            FieldData currentFieldData;
            FieldsData.TryGetValue(PROPERTY_STATUS, out currentFieldData);
            
            if (currentFieldData == null) return null;
            CustomerStatus status;
            Enum.TryParse(currentFieldData.Value, out status);
            return status;
        }

        private bool HasStatus()
        {
            return GetStatusOrNull() != null;
        }

        private bool IsDrafted()
        {
            var statusOrNull = GetStatusOrNull();
            if (! statusOrNull.HasValue) return false;

            var status = statusOrNull.Value;
            return status == CustomerStatus.Drafted;
        }

        internal void SetAsNew(DateTime creationDate, string whoCreated, int defaultCustomerTypeId)
        {
            if (string.IsNullOrWhiteSpace(whoCreated)) throw new ArgumentNullException(nameof(whoCreated));
            if (HasStatus()) throw new GameEngineException($"This {nameof(CustomerForm)} already has a status. It is not new");

            var creationDateFormattedAsText = FormatDate(creationDate);
            FieldsData.Add(PROPERTY_CREATION_DATE, new FieldData(PROPERTY_CREATION_DATE, creationDateFormattedAsText));
            FieldsData.Add(PROPERTY_WHO_CREATED, new FieldData(PROPERTY_WHO_CREATED, whoCreated));
            FieldsData.Add(PROPERTY_STATUS, new FieldData(PROPERTY_STATUS, CustomerStatus.Drafted.ToString()));
            if (! FieldsData.ContainsKey(PROPERTY_CUSTOMER_TYPE_ID)) FieldsData.Add(PROPERTY_CUSTOMER_TYPE_ID, new FieldData(PROPERTY_CUSTOMER_TYPE_ID, defaultCustomerTypeId.ToString()));

            if (Customer != null)
                logBuilder.AppendLine($"{whoCreated} created customer with account number {Customer.AccountNumber}.");
            else
                logBuilder.AppendLine($"{whoCreated} created customer.");
        }

        internal void SetAsUpdate(DateTime updateDate, string whoUpdated, CustomerForm customerFormWithModifiedFields)
        {
            if (string.IsNullOrWhiteSpace(whoUpdated)) throw new ArgumentNullException(nameof(whoUpdated));
            if (customerFormWithModifiedFields == null) throw new ArgumentNullException(nameof(customerFormWithModifiedFields));

            var updateDateFormattedAsText = FormatDate(updateDate);
            FieldData currentFieldData;
            FieldsData.TryGetValue(PROPERTY_LAST_UPDATE, out currentFieldData);
            if (currentFieldData == null)
            {
                FieldsData.Add(PROPERTY_LAST_UPDATE, new FieldData(PROPERTY_LAST_UPDATE, updateDateFormattedAsText));
            }
            else
            {
                FieldsData[PROPERTY_LAST_UPDATE] = new FieldData(PROPERTY_LAST_UPDATE, updateDateFormattedAsText);
            }

            if (Customer != null)
                logBuilder.AppendLine($"{whoUpdated} changed customer with account number {Customer.AccountNumber}. Detail: ");
            else
                logBuilder.AppendLine($"{whoUpdated} changed customer. Detail: ");

            Update(customerFormWithModifiedFields, whoUpdated);
        }

        private void Update(CustomerForm customerFormWithModifiedFields, string whoUpdated)
        {
            foreach (var newFieldData in customerFormWithModifiedFields.FieldsData.Values)
            {
                if (newFieldData.Name == PROPERTY_LAST_UPDATE) continue;

                FieldData currentFieldData;
                FieldsData.TryGetValue(newFieldData.Name, out currentFieldData);
                var isNewField = currentFieldData == null;
                if (isNewField)
                {
                    FieldsData.Add(newFieldData.Name, newFieldData);
                    logBuilder.AppendLine($"{whoUpdated} added '{newFieldData.Name}' field.");
                }
                else if (currentFieldData.Value.ToString() != newFieldData.Value.ToString())
                {
                    FieldsData[newFieldData.Name] = newFieldData;
                    logBuilder.AppendLine($"{whoUpdated} changed '{newFieldData.Name}' field: Old value: '{currentFieldData.Value}'. New value: '{newFieldData.Value.ToString()}'.");
                }
            }
        }

        internal void VerifyIfCustomerIsAutoApproved()
        {
            if (Customer == null) return;

            bool isCustomerReadyToBeAutoApproved = true;
            bool anyFieldVerifiable = false;
            foreach (var field in FieldsData)
            {
                if (field.Key.StartsWith(Fields.PREFIX_VERIFIABLE))
                {
                    if (field.Value.Value.ToLower().Trim() == "false")
                        isCustomerReadyToBeAutoApproved = false;
                    anyFieldVerifiable = true;
                }
            }

            if (anyFieldVerifiable)
            {
                var isApproved = Customer.IsApproved();

                IsCustomerAutoApproved = isCustomerReadyToBeAutoApproved;
                var isRequiredToRevertApprovalStatus = isApproved && ! isCustomerReadyToBeAutoApproved;
                if (isRequiredToRevertApprovalStatus)
                {
                    if (! FieldsData.ContainsKey(PROPERTY_STATUS))
                    {
                        FieldsData.Add(PROPERTY_STATUS, new FieldData(PROPERTY_STATUS, CustomerStatus.Drafted.ToString()));
                    }
                }
            }
            else
            {
                IsCustomerAutoApproved = false;
            }
        }

        internal void SetAsApproved(DateTime approvalDate, string whoApproved)
        {
            if (string.IsNullOrWhiteSpace(whoApproved)) throw new ArgumentNullException(nameof(whoApproved));
            if (!FieldsData.ContainsKey(PROPERTY_STATUS)) throw new GameEngineException($"This {nameof(CustomerForm)} does not have a status. Use {nameof(SetAsNew)} method first");

            var approvalDateFormattedAsText = FormatDate(approvalDate);
            FieldsData.Add(PROPERTY_APPROVAL_DATE, new FieldData(PROPERTY_APPROVAL_DATE, approvalDateFormattedAsText));
            FieldsData.Add(PROPERTY_WHO_APPROVED, new FieldData(PROPERTY_WHO_APPROVED, whoApproved));
            FieldsData[PROPERTY_STATUS] = new FieldData(PROPERTY_STATUS, CustomerStatus.Approved.ToString());

            if (Customer != null)
                logBuilder.AppendLine($"{whoApproved} approved customer with account number {Customer.AccountNumber}");
            else
                logBuilder.AppendLine($"{whoApproved} approved customer");
        }

        internal void SetAsRejected(DateTime rejectionDate, string whoRejected)
        {
            if (string.IsNullOrWhiteSpace(whoRejected)) throw new ArgumentNullException(nameof(whoRejected));
            if (!FieldsData.ContainsKey(PROPERTY_STATUS)) throw new GameEngineException($"This {nameof(CustomerForm)} does not have a status. Use {nameof(SetAsNew)} method first");

            var rejectionDateFormattedAsText = FormatDate(rejectionDate);
            FieldsData.Add(PROPERTY_REJECTION_DATE, new FieldData(PROPERTY_REJECTION_DATE, rejectionDateFormattedAsText));
            FieldsData.Add(PROPERTY_WHO_REJECTED, new FieldData(PROPERTY_WHO_REJECTED, whoRejected));
            FieldsData[PROPERTY_STATUS] = new FieldData(PROPERTY_STATUS, CustomerStatus.Rejected.ToString());

            if (Customer != null) 
                logBuilder.AppendLine($"{whoRejected} rejected customer with account number {Customer.AccountNumber}");
            else
                logBuilder.AppendLine($"{whoRejected} rejected customer");
        }

        internal string Serialize()
        {
            var logJson = new CustomerFormJson();
            foreach (var entry in FieldsData.Values)
            {
                logJson.Document.Add(entry.Name, entry.Value);
            }

            var result = JsonConvert.SerializeObject(logJson.Document);
            return result;
        }

        [DataContract(Name = "CustomerFormJson")]
        public class CustomerFormJson
        {
            [DataMember(Name = "document")]
            public IDictionary<string, object> Document { get; set; } = new Dictionary<string, object>();
        }
    }
}
