﻿using System;
using System.Collections.Generic;

namespace ExchangeAPI.ExchangeEntities
{
    public partial class DebitNote
    {
        public DebitNote()
        {
            DebitNoteAttachment = new HashSet<DebitNoteAttachment>();
        }

        public long Id { get; set; }
        public int Currencyid { get; set; }
        public decimal Amount { get; set; }
        public long Batchnumber { get; set; }
        public string Customer { get; set; }
        public string Account { get; set; }
        public DateTime Datecreated { get; set; }
        public long Whocreated { get; set; }
        public DateTime? Dateapproved { get; set; }
        public long? Whoapproved { get; set; }
        public DateTime? Daterejected { get; set; }
        public long? Whorejected { get; set; }
        public long Referenceid { get; set; }
        public sbyte Approvals { get; set; }
        public sbyte Approvalsrequired { get; set; }
        public sbyte Rejections { get; set; }
        public string Description { get; set; }
        public bool Deleted { get; set; }
        public string Voucherurl { get; set; }
        public string Rejectionreason { get; set; }
        public long? Processorid { get; set; }
        public int Domain { get; set; }
        public string Journalentrydetailid { get; set; }

        public virtual Currency Currency { get; set; }
        public virtual Domains DomainNavigation { get; set; }
        public virtual Journalentry Journalentrydetail { get; set; }
        public virtual Exchangeuser WhoapprovedNavigation { get; set; }
        public virtual Exchangeuser WhocreatedNavigation { get; set; }
        public virtual Exchangeuser WhorejectedNavigation { get; set; }
        public virtual ICollection<DebitNoteAttachment> DebitNoteAttachment { get; set; }
    }
}
