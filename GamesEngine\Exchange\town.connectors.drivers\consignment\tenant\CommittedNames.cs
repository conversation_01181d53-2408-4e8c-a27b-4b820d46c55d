﻿using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;
using town.connectors;
using town.connectors.drivers;

namespace Connectors.town.connectors.drivers.consignment
{
    public class CommittedNames: ConsignmentTenantDriver
    {
        public CommittedNames() : base(Tenant_Actions.Others)
        {

        }

        public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            throw new NotImplementedException();
        }

        public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            if (CustomSettings.ThereArePendingChanges)
            {
                ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
                SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
                Password = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
            }

            var result = await GetCommittedNamesAsync(recordSet);
            return (T)Convert.ChangeType(result, typeof(T));
        }

        async Task<string> GetCommittedNamesAsync(CustomSettings.RecordSet recordSet)
        {
            ResponseCommittedNamesEnvelope responseXml = null;
            string responseString = string.Empty;
            string xmlWithHiddenFields = string.Empty;
            var url = $"{ServicesUrl}?op=getCommittedNames";
            try
            {
                string xmlString = CreateSoapEnvelope(recordSet, out xmlWithHiddenFields);
                var webRequest = new HttpRequestMessage(HttpMethod.Post, url)
                {
                    Content = new StringContent(xmlString, Encoding.UTF8, "text/xml"),
                };

                Loggers.GetIntance().AccountingConsignmentGetCommittedNames.Debug($@"url:{url} data:{xmlWithHiddenFields}");
                using (HttpResponseMessage response = await httpClient.SendAsync(webRequest, HttpCompletionOption.ResponseHeadersRead))
                {
                    responseString = await response.Content.ReadAsStringAsync();
                }
                Loggers.GetIntance().AccountingConsignmentGetCommittedNames.Debug($@"response:{responseString}");

                XmlSerializer xmls = new XmlSerializer(typeof(ResponseCommittedNamesEnvelope));
                responseXml = (ResponseCommittedNamesEnvelope)xmls.Deserialize(new StringReader(responseString));
            }
            catch (Exception e)
            {
                Loggers.GetIntance().AccountingConsignmentGetCommittedNames.Error($@"url:{url} data:{xmlWithHiddenFields} type:{e.GetType()} error:{e.Message}", e);

                var extraErrorMessage = string.Empty;
                InternalOnError(nameof(GetCommittedNamesAsync), 1, e, $"Url:{url}", $"Request: {xmlWithHiddenFields}", $"Response: {responseString}");
            }

            if (responseXml == null || string.IsNullOrWhiteSpace(responseXml.Body.getCommittedNamesResponse.getCommittedNamesResult))
            {
                NotifyWarn(nameof(GetCommittedNamesAsync), $"Url:{url}\nResponse: {responseString}", $"Response cannot be empty");
            }
            else
            {
                return responseXml.Body.getCommittedNamesResponse.getCommittedNamesResult;
            }

            return string.Empty;
        }

        string CreateSoapEnvelope(CustomSettings.RecordSet recordSet, out string xmlWithHiddenFields)
        {
            var xmlBuilder = new StringBuilder();
            xmlBuilder.AppendLine("<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">");
            xmlBuilder.AppendLine("<soap:Body>");
            xmlBuilder.AppendLine("<getCommittedNames xmlns=\"http://tempuri.org/\">");
            xmlBuilder.Append("<user>").Append(SystemId).AppendLine("</user>");
            xmlBuilder.Append("<password>").Append(Password).AppendLine("</password>");
            xmlBuilder.AppendLine("</getCommittedNames>");
            xmlBuilder.AppendLine("</soap:Body>");
            xmlBuilder.AppendLine("</soap:Envelope>");

            var xmlBuilderWithHiddenFields = new StringBuilder();
            xmlBuilderWithHiddenFields.AppendLine("<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">");
            xmlBuilderWithHiddenFields.AppendLine("<soap:Body>");
            xmlBuilderWithHiddenFields.AppendLine("<getCommittedNames xmlns=\"http://tempuri.org/\">");
            xmlBuilderWithHiddenFields.AppendLine("<user>XXXX</user>");
            xmlBuilderWithHiddenFields.AppendLine("<password>XXXX</password>");
            xmlBuilderWithHiddenFields.AppendLine("</getCommittedNames>");
            xmlBuilderWithHiddenFields.AppendLine("</soap:Body>");
            xmlBuilderWithHiddenFields.AppendLine("</soap:Envelope>");

            xmlWithHiddenFields = xmlBuilderWithHiddenFields.ToString();
            return xmlBuilder.ToString();
        }

        [XmlRoot("Envelope", Namespace = "http://schemas.xmlsoap.org/soap/envelope/")]
        public class ResponseCommittedNamesEnvelope
        {
            public ResponseCommittedNamesBody Body { get; set; }
        }

        public class ResponseCommittedNamesBody
        {
            [XmlElement(ElementName = "getCommittedNamesResponse", Namespace = "http://tempuri.org/")]
            public GetCommittedNamesResponse getCommittedNamesResponse { get; set; }
        }

        public class GetCommittedNamesResponse
        {
            public string getCommittedNamesResult { get; set; }
        }

        public override void Prepare(DateTime now)
        {
            SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
            Password = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
            ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
        }
    }
}
