﻿using GamesEngine.Custodian.Operations;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using GamesEngine.PurchaseOrders;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using town.connectors.drivers;
using static GamesEngine.Custodian.Operations.DisbursementExecutionResponse;
using static GamesEngine.Custodian.Operations.Operation;
using static GamesEngine.Exchange.PaymentProcessorsAndActionsByDomains;
using static town.connectors.drivers.Result;

namespace GamesEngine.Custodian
{

	[Puppet]
	internal class PaymentsToBeExecuted: Objeto
	{
		private Guardian guardian;
		private List<Pair> pairs;
		internal PaymentsToBeExecuted(Guardian guardian)
		{
			if (guardian == null) throw new ArgumentNullException(nameof(guardian));

			this.guardian = guardian;
		}
		internal void Add(int transactionId, List<int> disbusmentIds)
		{
			if (pairs == null) pairs = new List<Pair>();

			Operation operation =  guardian.Operations().SearchFor(transactionId);
			if (!(operation is InprocessWithDrawal)) throw new GameEngineException($"There is no transaction approved with id {transactionId}");
			InprocessWithDrawal inprocessOperation = (InprocessWithDrawal)operation;

			foreach (int disbusmentId in disbusmentIds)
			{
				Disbursement disbursement = inprocessOperation.SearchDisbusmentById(disbusmentId);
				Add(inprocessOperation, disbursement);
			}
		}

		internal void Add(InprocessWithDrawal inprocessOperation,  Disbursement disbursement)
		{
			if (inprocessOperation== null) throw new ArgumentNullException(nameof(inprocessOperation));
			if (!(disbursement is DisbursementUnpayed)) throw new GameEngineException($"{nameof(Disbursement)} with id {disbursement.Id} for {nameof(PendingWithDrawal)} with id {inprocessOperation.TransactionId} can not be payed.");

			pairs.Add(new Pair(inprocessOperation, (DisbursementUnpayed)disbursement));
		}

		internal PaymentResult Execute(bool itsThePresent, DateTime now, string who, int storeId, string agentPath)
		{
			PaymentResult paymentResult = new PaymentResult(now, who);
			List<PlatformEvent> monitorEvents = new List<PlatformEvent>();

			using (KafkaMessagesBuffer bufferForApproval = new KafkaMessagesBuffer(itsThePresent, $"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForWithdrawals}"))
			using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itsThePresent, Integration.Kafka.TopicForWithdrawals))
			{
				using (KafkaMessagesBuffer buffer2 = new KafkaMessagesBuffer(itsThePresent, Integration.Kafka.TopicForWithdrawalsDisbursementExecution))
				{
					int originalTransactionId = 0;
					string processorKey = string.Empty;
					string originalEmployeeName = string.Empty;
					bool allDisbursementsExecuted = false;
					foreach (Pair pair in pairs)
					{
						if (originalTransactionId != 0 && originalTransactionId != pair.InprocessOperation.ReferenceNumber) throw new GameEngineException($"{nameof(Disbursement)} has a different {nameof(originalTransactionId)}");
						if (!string.IsNullOrWhiteSpace(originalEmployeeName) && originalEmployeeName != pair.InprocessOperation.EmployeeName) throw new GameEngineException($"{nameof(Disbursement)} has a different {nameof(originalEmployeeName)}");
						if (!string.IsNullOrWhiteSpace(processorKey) && processorKey != pair.InprocessOperation.Processor.ProcessorKey) throw new GameEngineException($"{nameof(Disbursement)} has a different {nameof(processorKey)}");
						originalTransactionId = pair.InprocessOperation.ReferenceNumber;
						originalEmployeeName = pair.InprocessOperation.EmployeeName;
						processorKey = pair.InprocessOperation.Processor.ProcessorKey;
						allDisbursementsExecuted = pair.InprocessOperation.DisbursementAmount.Value == pair.InprocessOperation.Disbursements.ExecutedAmount + pair.Disbursement.Amount;
						DisbursementExecutionResponse disbursementExecution = pair.Disbursement.Pay(guardian.Company, pair.InprocessOperation, itsThePresent, now);
						DisbursementExecuted disbursementExecuted = disbursementExecution.DisbursementExecuted;

						paymentResult.Add(disbursementExecution, pair.InprocessOperation.DisbursementAmount.Coin);

						if (disbursementExecution.Status == TransactionStatus.APPROVED)
						{
							pair.InprocessOperation.ReplaceDisbursment(pair.Disbursement, disbursementExecuted);
							pair.InprocessOperation.DisbursementHasBeenARemoved(disbursementExecuted.ScheduledDate, pair.Disbursement);
							disbursementExecuted.SaveLastExecution(disbursementExecution.Execution(now));

							if (pair.InprocessOperation.TransactionItsCompletelyPayed)
							{
								CompleteWithdrawal completeWithdrawal = pair.InprocessOperation.Complete();
								Integration.Kafka.Send(itsThePresent, Integration.Kafka.TopicForGuardianInternalOperations, completeWithdrawal.GenerateMessage(now));

								guardian.RemoveTransaction(pair.InprocessOperation);
							}
							else if (pair.InprocessOperation.Status == StatusCodes.IN_PROCESS )
							{
								pair.InprocessOperation.ChangeToHasAtLeastOneExecution();
								Integration.Kafka.Send(itsThePresent, Integration.Kafka.TopicForGuardianInternalOperations, pair.InprocessOperation.GenerateMessage(now));
							}

							var processorAccount = guardian.Accounts().SearchBy(pair.InprocessOperation.Processor);
							WithdrawMessage message = new WithdrawMessage(
								processorAccount.Number,
								pair.InprocessOperation.DisbursementAmount.Coin,
								storeId,
								disbursementExecuted.Amount,
								who,
								$"Withdrawal Transaction: {pair.InprocessOperation.TransactionId} disbursment:{disbursementExecuted.TransactionId}",//TODO cris cambiar esta descripción
								"" + pair.InprocessOperation.ReferenceNumber,
								disbursementExecuted.Account.Number,
								processorAccount.Id,
								processorKey,
								PaymentChannels.Agents.INSIDER
							);
							buffer.Send(message);
						}
						else
						{
							pair.Disbursement.SaveLastExecution(disbursementExecution.Execution(now));
						}
						
						buffer2.Send(disbursementExecution.GenerateMessage(now, who));

						monitorEvents.Add(new DisbursmentEvent(
							now,
							disbursementExecuted.Id,
							disbursementExecuted.Account.Number,
							disbursementExecuted.Account.Coin,
							disbursementExecuted.Status,
							disbursementExecuted.Amount,
							disbursementExecuted.ScheduledDate,
							disbursementExecuted.TransactionId,
							disbursementExecuted.ProcessorId));
					}
					if (allDisbursementsExecuted)
                    {
						var msg = new ApprovalForWithdrawalsMessage(originalTransactionId, agentPath, originalEmployeeName, processorKey);
						bufferForApproval.Send(msg);
					}
				}
			}
			
			PlatformMonitor.GetInstance().WhenNewEvents(monitorEvents);
			return paymentResult;
		}
		private class Pair
		{
			internal Pair(InprocessWithDrawal inprocessOperation, DisbursementUnpayed disbursement)
			{
				InprocessOperation = inprocessOperation;
				Disbursement = disbursement;
			}
			internal InprocessWithDrawal InprocessOperation { get; }
			internal DisbursementUnpayed Disbursement { get; }
		}
	}

	[Puppet]
	internal class PaymentResult : Objeto
	{
		internal DateTime Date { get; }
		internal string ExecutedBy { get; }
		internal int Transactions { get; set; }
		internal int TransactionWithErrors { get; set; }
		private Dictionary<Coin, Currency> allTotalAmounts = new Dictionary<Coin, Currency>();
		internal List<DisbursementExecuted> DisbursementsExecutedWithError { get; set; } = new List<DisbursementExecuted>();
		internal List<Currency> TotalAmounts { get { return allTotalAmounts.Values.ToList(); } }
		internal PaymentResult(DateTime now, string who)
		{
			Date = now;
			ExecutedBy = who;
		}
		internal void Add(DisbursementExecutionResponse disbursementExecution, Coin currencyCode)
		{
			Currency.Factory(currencyCode.Iso4217Code, disbursementExecution.DisbursementExecuted.Amount);
			if (disbursementExecution.Status == TransactionStatus.DENIED )
			{
				TransactionWithErrors += 1;
				DisbursementsExecutedWithError.Add(disbursementExecution.DisbursementExecuted);
			}

			Currency totalAmount;
			bool exists = allTotalAmounts.TryGetValue(currencyCode, out totalAmount);
			if ( !exists )
			{
				totalAmount = Currency.ZeroFactory(currencyCode.Iso4217Code);
				allTotalAmounts.Add(currencyCode, totalAmount);
			}
			totalAmount.Add(disbursementExecution.DisbursementExecuted.Amount);
			
			Transactions += 1;
		}
	}

	public class PaymentsToBeExecutedModel
	{
		public List<Operation> Operations { set; get; }
		public class Operation
		{
			public int Id { set; get; }
			public List<int> DisbusmentIds { set; get; }
		}

		internal bool HasAnItem()
		{
			if (Operations != null && Operations.Count()>0)
			{
				Operation operation = Operations.FirstOrDefault();
				List<int> disbusments = operation.DisbusmentIds;
				if (disbusments != null && disbusments.Count() >0 )
				{
					return true;
				}
			}
			return false;
		}
	}
}
