﻿using GamesEngine.Business;
using GamesEngine.Custodian.Operations;
using GamesEngine.Domains;
using GamesEngine.Exchange;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using static GamesEngine.Business.WholePaymentProcessor;

namespace GamesEngine.Custodian
{
	[Puppet]
	internal class Guardian : Objeto
	{
		private readonly Operations.Operations operations;
		internal Domains.Domains Domains { get; }
		private Profiles profiles;
		private AccountNumbers accounts;
		private Approvers approvers;
		private RiskRating riskRating;
		private RiskAssignments riskAssignments;
		private DisbursmentsByDate disbursmentsByDate;
		private readonly Company company;

		internal Guardian(Company company)
		{
			if (company == null) throw new ArgumentNullException(nameof(company));

			this.company = company;
			operations = new Operations.Operations(this);
            Domains = new Domains.Domains(company);
		}

		internal PaymentProcessorsAndActionsByDomains PaymentProcessors()
		{
			return company.System.PaymentProcessor.PaymentProcessorsAndActionsByDomains;
		}

		internal IEnumerable<PaymentProcessor> PaymentProcessorsWithoutAccounts()
		{
			var processors = new Dictionary<string, PaymentProcessor>();
            foreach (var processor in company.System.PaymentProcessor.Values)
            {
				if (!processors.ContainsKey(processor.ProcessorKey) && !Accounts().ContainsProcessor(processor.ProcessorKey)) processors.Add(processor.ProcessorKey, processor);
			}
			return processors.Values.ToList();
		}

		internal AccountNumbers Accounts()
		{
			if (accounts == null)
			{
				accounts = new AccountNumbers();
			}
			return accounts;
		}

		internal Approvers Approvers()
		{
			if (approvers == null)
			{
				approvers = new Approvers();
			}
			return approvers;
		}

		internal RiskRating RiskRating()
		{
			if (riskRating == null)
			{
				RiskRatings riskRatings = new RiskRatings();
				riskRating = riskRatings.NewRiskRating("Custodian Risk Rate", "This is the custodian risk rate.", 1);
			}
			return riskRating;
		}
		internal RiskAssignments RiskAssignments()
		{
			if (riskAssignments == null)
			{
				riskAssignments = new RiskAssignments();
			}
			return riskAssignments;
		}

		internal Operations.Operations Operations()
		{
			return operations;
		}

		internal Profiles Profiles()
		{
			if (profiles == null)
			{
				profiles = new Profiles();
			}
			return profiles;
		}

		internal DisbursmentsByDate DisbursmentsByDate()
		{
			if (disbursmentsByDate == null) this.disbursmentsByDate = new DisbursmentsByDate();

			return disbursmentsByDate;
		}

		internal void RemoveTransaction(GamesEngine.Custodian.Operations.Operation operation)
		{
			bool result = operations.Remove(operation);
			if (!result) throw new GameEngineException($"There is no transaction with id {operation.TransactionId} to be removed.");
		}

		internal Company Company
		{
			get
			{
				return this.company;
			}
		}
	}

	[DataContract(Name = "ProcessorsWithoutAccounts")]
	public class ProcessorsWithoutAccounts
	{
		[DataMember(Name = "accountId")]
		public int AccountId { get; set; }
		[DataMember(Name = "processors")]
		public List<ProcessorWithoutAccounts> Processors { get; set; }
	}

	[DataContract(Name = "ProcessorWithoutAccounts")]
	public class ProcessorWithoutAccounts
	{
		[DataMember(Name = "entity")]
		public string Entity { get; set; }
		[DataMember(Name = "paymentMethod")]
		public string PaymentMethod { get; set; }
		[DataMember(Name = "transactionType")]
		public string TransactionType { get; set; }
		[DataMember(Name = "currencyCode")]
		public string CurrencyCode { get; set; }
		public string ProcessorKey
		{
			get
			{
				return $"{Entity}_{PaymentMethod}_{TransactionType}_{CurrencyCode}";
			}
		}

		public string ProcessorAccount(int accountId)
		{
			return $"{Entity[0]}_{PaymentMethod[0]}_{TransactionType[0]}_{CurrencyCode}_{accountId}";
		}
	}
}
