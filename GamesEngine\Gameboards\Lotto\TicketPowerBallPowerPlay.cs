﻿using GamesEngine.Bets;
using GamesEngine.Games.Lotto;
using GamesEngine.Tools;
using System;
using System.Linq;

namespace GamesEngine.Gameboards.Lotto
{
    internal sealed class TicketPowerBallPowerPlay : TicketPowerBall
    {

        internal TicketPowerBallPowerPlay(Player player, Lottery lottery, DateTime drawDate, string number1, string number2, string number3, string number4, string number5, string powerBall, DateTime creationDate, decimal ticketCost, Prizes prizes) :
            base(player, lottery, drawDate, number1, number2, number3, number4, number5, powerBall, creationDate, ticketCost, prizes)
        { }

        internal TicketPowerBallPowerPlay(Player player, Lottery lottery, DateTime drawDate, string numbers, DateTime creationDate, decimal ticketCost, Prizes prizes) :
            base(player, lottery, drawDate, numbers, creationDate, ticketCost, prizes)
        { }

        internal const decimal FACTOR_OF_A_POWER_PLAY_TICKET = 2m;

        internal override void GenerateWagers()
        {
            var betAmount = BetAmount() * FACTOR_OF_A_POWER_PLAY_TICKET;
            GenerateWagers(betAmount);
        }

        internal override void GenerateWagers(decimal betAmount)
        {
            decimal prize = base.Prizes.MaxPrizeFor(TicketType.PBP);
            prize *= betAmount;

            var prizeToWin = prize - betAmount;
            prizeToWin = prizeToWin < 0 ? 0 : prizeToWin;

            var strNumbers = AsStringForAccounting();
            AddWager(betAmount, prizeToWin, strNumbers, SubTickets().ElementAt(0));
        }

        internal sealed override TicketType IdOfType()
        {
            return TicketType.PBP;
        }

        internal override decimal BetAmount()
        {
            const decimal FACTOR_OF_A_POWER_PLAY_TICKET = 2m;
            var result = TicketAmount() / Count / FACTOR_OF_A_POWER_PLAY_TICKET;
            Commons.ValidateAmount(result);
            return result;
        }

        internal override string GameTypeForReports()
        {
            return QueryMakerOfHistoricalPicks.ID_POWERBALL;
        }

        internal override decimal PowerplayCost()
        {
            return BetAmount();
        }
    }
}
