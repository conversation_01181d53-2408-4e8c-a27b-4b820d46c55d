﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static GamesEngine.Finance.Currencies;

namespace ExchangeAPI.Controllers
{
    public class ReportController : AuthorizeController
	{
		[HttpGet("api/exchange/report/dailyProfits")]
		[Authorize(Roles = "f1")]
		public IActionResult DailyProfitsReport(DateTime startDate, DateTime endDate, string currencyCode, int initialIndex, int amountOfRows)
		{
			if (startDate == default(DateTime)) return NotFound($"{nameof(startDate)} is required");
			if (endDate == default(DateTime)) return NotFound($"{nameof(endDate)} is required");
			if (startDate.Hour != 0 || startDate.Minute != 0 || startDate.Second != 0) return NotFound($"{nameof(startDate)} {startDate} is not valid");
			if (endDate.Hour != 0 || endDate.Minute != 0 || endDate.Second != 0) return NotFound($"{nameof(endDate)} {endDate} is not valid");
			if (string.IsNullOrWhiteSpace(currencyCode)) return NotFound($"{nameof(currencyCode)} is required");
			if (initialIndex < 0) return NotFound($"{nameof(initialIndex)} {initialIndex} is not valid");
			if (amountOfRows <= 0) return NotFound($"{nameof(amountOfRows)} {amountOfRows} is not valid");

			var dailyProfits = Logic.DailyProfits.FilterBy(startDate, endDate, currencyCode, initialIndex, amountOfRows);
			return Ok(dailyProfits);
		}

		[HttpGet("api/exchange/report/dailyProfits/detail")]
		[Authorize(Roles = "f2")]
		public IActionResult DetailDailyProfitsReport(DateTime startDate, DateTime endDate, int initialIndex, int amountOfRows)
		{
			if (startDate == default(DateTime)) return BadRequest($"{nameof(startDate)} is required");
			if (endDate == default(DateTime)) return BadRequest($"{nameof(endDate)} is required");
			if (startDate.Hour != 0 || startDate.Minute != 0 || startDate.Second != 0) return BadRequest($"{nameof(startDate)} {startDate} is not valid");
			if (endDate.Hour != 0 || endDate.Minute != 0 || endDate.Second != 0) return BadRequest($"{nameof(endDate)} {endDate} is not valid");
			if (initialIndex < 0) return BadRequest($"{nameof(initialIndex)} {initialIndex} is not valid");
			if (amountOfRows <= 0) return BadRequest($"{nameof(amountOfRows)} {amountOfRows} is not valid");

			var result = Logic.ProfitableTransactions.FilterBy(startDate, endDate, initialIndex, amountOfRows);
			return Ok(result);
		}
	}
}
