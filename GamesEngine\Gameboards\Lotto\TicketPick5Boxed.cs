﻿using GamesEngine.Bets;
using GamesEngine.Business;
using GamesEngine.Games.Lotto;
using GamesEngine.Tools;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using static GamesEngine.Finance.PaymentChannels;

namespace GamesEngine.Gameboards.Lotto
{
	internal sealed class TicketPick5Boxed : TicketPick<Pick5>
	{
		internal TicketPick5Boxed(Player player, Lottery lottery, DateTime drawDate, string number1, string number2, string number3, string number4, string number5, Selection selectionMode, DateTime creationDate, decimal ticketCost, Prizes prizes) :
			base(player, lottery, drawDate, new Pick5(number1, number2, number3, number4, number5), selectionMode, creationDate, ticketCost, prizes)
		{
		}

		internal TicketPick5Boxed(Player player, Lottery lottery, DateTime drawDate, string [] numbers, Selection selectionMode, DateTime creationDate, decimal ticketCost, Prizes prizes) :
			base(player, lottery, drawDate, numbers, selectionMode, creationDate, ticketCost, prizes)
		{
		}

		protected sealed override void GradeNumber(Pick5 winnerNumber, int fireBallNumber = int.MinValue)
		{
			var withFireBall = BelongsToFireBallDraw;
            if (withFireBall && fireBallNumber == int.MinValue) throw new GameEngineException("FireBall number is required");
            if (withFireBall && (fireBallNumber < 0 || fireBallNumber > 9)) throw new GameEngineException($"FireBall number {fireBallNumber} must be between 1 and 2");
            if (winnerNumber.Count != 1) throw new GameEngineException($"Winner {winnerNumber.AsString()} must be a single number");
			int digit1 = winnerNumber[1].SingleDigit;
			int digit2 = winnerNumber[2].SingleDigit;
			int digit3 = winnerNumber[3].SingleDigit;
			int digit4 = winnerNumber[4].SingleDigit;
			int digit5 = winnerNumber[5].SingleDigit;
			bool won = false;
			payout = 0;
			PrizesPicks currPrizes = (PrizesPicks)base.Prizes;
			foreach (Pick5 pick in base.Numbers)
			{
				if (!withFireBall)
				{
                    if (IsWinner(pick, digit1, digit2, digit3, digit4, digit5))
                    {
                        var prizeCriteria = PrizesPicks.WayOfSubticket(digit1, digit2, digit3, digit4, digit5);
                        decimal prize = currPrizes.Prize(TicketType.P5B, prizeCriteria);
                        var rawPrice = prize * BetAmount(PickPortion.NORMAL_LEVEL);
                        Commons.ValidateAmount(rawPrice);
                        payout = payout + rawPrice;

                        won = true;
                    }
                }
				else
				{
                    if (IsWinner(pick, digit1, digit2, digit3, digit4, digit5))
                    {
                        var prizeCriteria = PrizesPicks.WayOfSubticket(digit1, digit2, digit3, digit4, digit5);
                        decimal prize = currPrizes.Prize(TicketType.P5B, prizeCriteria);
                        var rawPrice = RoundToNearestFiveCents(prize * BetAmount(PickPortion.NORMAL_LEVEL));
                        Commons.ValidateAmount(rawPrice);
                        payout = payout + rawPrice;

                        won = true;
                    }

                    if (IsWinner(pick, fireBallNumber, digit2, digit3, digit4, digit5))
					{
                        var prizeCriteria = PrizesPicks.WayOfSubticket(fireBallNumber, digit2, digit3, digit4, digit5);
                        decimal prize = currPrizes.Prize(TicketType.P5B, prizeCriteria);
                        var rawPrice = RoundToNearestFiveCents(prize * BetAmount(PickPortion.LEVEL1_PORTION));
                        Commons.ValidateAmount(rawPrice);
                        payout = payout + rawPrice;

                        won = true;
                    }

                    if (IsWinner(pick, digit1, fireBallNumber, digit3, digit4, digit5))
                    {
                        var prizeCriteria = PrizesPicks.WayOfSubticket(digit1, fireBallNumber, digit3, digit4, digit5);
                        decimal prize = currPrizes.Prize(TicketType.P5B, prizeCriteria);
                        var rawPrice = RoundToNearestFiveCents(prize * BetAmount(PickPortion.LEVEL2_PORTION));
                        Commons.ValidateAmount(rawPrice);
                        payout = payout + rawPrice;

                        won = true;
                    }

                    if (IsWinner(pick, digit1, digit2, fireBallNumber, digit4, digit5))
                    {
                        var prizeCriteria = PrizesPicks.WayOfSubticket(digit1, digit2, fireBallNumber, digit4, digit5);
                        decimal prize = currPrizes.Prize(TicketType.P5B, prizeCriteria);
                        var rawPrice = RoundToNearestFiveCents(prize * BetAmount(PickPortion.LEVEL3_PORTION));
                        Commons.ValidateAmount(rawPrice);
                        payout = payout + rawPrice;

                        won = true;
                    }

                    if (IsWinner(pick, digit1, digit2, digit3, fireBallNumber, digit5))
                    {
                        var prizeCriteria = PrizesPicks.WayOfSubticket(digit1, digit2, digit3, fireBallNumber, digit5);
						decimal prize = currPrizes.Prize(TicketType.P5B, prizeCriteria);
                        var rawPrice = RoundToNearestFiveCents(prize * BetAmount(PickPortion.LEVEL4_PORTION));
                        Commons.ValidateAmount(rawPrice);
                        payout = payout + rawPrice;

                        won = true;
                    }

                    if (IsWinner(pick, digit1, digit2, digit3, digit4, fireBallNumber))
                    {
                        var prizeCriteria = PrizesPicks.WayOfSubticket(digit1, digit2, digit3, digit4, fireBallNumber);
                        decimal prize = currPrizes.Prize(TicketType.P5B, prizeCriteria);
                        var rawPrice = RoundToNearestFiveCents(prize * BetAmount(PickPortion.LEVEL5_PORTION));
                        Commons.ValidateAmount(rawPrice);
                        payout = payout + rawPrice;

                        won = true;
                    }
                }
			}

			if (won)
			{
				base.GradeAsWinner();
			}
			else
			{
				base.GradeAsLoser();
			}
		}

        protected override decimal BetAmount(PickPortion pickPortion)
        {
            var originalValue = TicketAmount() / Count;
            Commons.ValidateAmount(originalValue);
            if (!BelongsToFireBallDraw) return originalValue;

            decimal porcionSinFireBall = originalValue / 2;
            if (pickPortion == PickPortion.NORMAL_LEVEL) return porcionSinFireBall;

            decimal porcionCombinacion1 = originalValue - porcionSinFireBall;
            porcionCombinacion1 = porcionCombinacion1 / 5;
            if (pickPortion == PickPortion.LEVEL1_PORTION) return porcionCombinacion1;

            decimal porcionCombinacion2 = originalValue - porcionSinFireBall - porcionCombinacion1;
            porcionCombinacion2 = porcionCombinacion2 / 4;
            if (pickPortion == PickPortion.LEVEL2_PORTION) return porcionCombinacion2;

            decimal porcionCombinacion3 = originalValue - porcionSinFireBall - porcionCombinacion1 - porcionCombinacion2;
            porcionCombinacion3 = porcionCombinacion3 / 3;
            if (pickPortion == PickPortion.LEVEL3_PORTION) return porcionCombinacion3;

            decimal porcionCombinacion4 = originalValue - porcionSinFireBall - porcionCombinacion1 - porcionCombinacion2 - porcionCombinacion3;
            porcionCombinacion4 = porcionCombinacion4 / 2;
            if (pickPortion == PickPortion.LEVEL4_PORTION) return porcionCombinacion4;

            decimal porcionCombinacion5 = originalValue - porcionSinFireBall - porcionCombinacion1 - porcionCombinacion2 - porcionCombinacion3 - porcionCombinacion4;
            if (pickPortion == PickPortion.LEVEL5_PORTION) return porcionCombinacion5;

            throw new GameEngineException("Invalid PickPortion");
        }

        private bool IsWinner(Pick5 pick, int digit1, int digit2, int digit3, int digit4, int digit5)
		{
			int[] idx = new int[5];
			int[] values = new int[] { digit1, digit2, digit3, digit4, digit5 };
			for (idx[0] = 0; idx[0] < 5; idx[0]++)
			{
				for (idx[1] = 0; idx[1] < 5; idx[1]++)
				{
					for (idx[2] = 0; idx[2] < 5; idx[2]++)
					{
						for (idx[3] = 0; idx[3] < 5; idx[3]++)
						{
							for (idx[4] = 0; idx[4] < 5; idx[4]++)
							{
								if (pick.allIndexesAreDifferent(idx) &&
									pick.IsMarked(values[idx[0]], values[idx[1]], values[idx[2]], values[idx[3]], values[idx[4]]) &&
									!IsExcluded(values[idx[0]], values[idx[1]], values[idx[2]], values[idx[3]], values[idx[4]])
									)
								{
									return true;
								}
							}
						}
					}
				}
			}
			return false;
		}

        internal override TicketType IdOfType()
		{
			return TicketType.P5B;
		}

		internal override string GameTypeForReports()
		{
			return QueryMakerOfHistoricalPicks.ID_PICK5;
		}

		internal override IEnumerable<TicketByPrize> TicketsByPrize()
		{
			var tickets = new TicketByPrize[7] {
				new TicketByPrizePick5Boxed(this, PrizesPicks.PRIZE_CRITERIA_ALL_NUMBERS_ARE_THE_SAME),
				new TicketByPrizePick5Boxed(this, PrizesPicks.PRIZE_CRITERIA_5_WAY),
				new TicketByPrizePick5Boxed(this, PrizesPicks.PRIZE_CRITERIA_10_WAY),
				new TicketByPrizePick5Boxed(this, PrizesPicks.PRIZE_CRITERIA_20_WAY),
				new TicketByPrizePick5Boxed(this, PrizesPicks.PRIZE_CRITERIA_30_WAY),
				new TicketByPrizePick5Boxed(this, PrizesPicks.PRIZE_CRITERIA_60_WAY),
				new TicketByPrizePick5Boxed(this, PrizesPicks.PRIZE_CRITERIA_120_WAY)
			};
			PrizesPicks currPrizes = (PrizesPicks)base.Prizes;
			foreach (SubTicket<IPick> subTicket in SubTickets())
			{
				var prizeCriteria = PrizesPicks.WayOfSubticket(subTicket[1], subTicket[2], subTicket[3], subTicket[4], subTicket[5]);
				switch (prizeCriteria)
				{
					case PrizesPicks.PRIZE_CRITERIA_ALL_NUMBERS_ARE_THE_SAME:
						tickets[0].Add(subTicket);
						break;
					case PrizesPicks.PRIZE_CRITERIA_5_WAY:
						tickets[1].Add(subTicket);
						break;
					case PrizesPicks.PRIZE_CRITERIA_10_WAY:
						tickets[2].Add(subTicket);
						break;
					case PrizesPicks.PRIZE_CRITERIA_20_WAY:
						tickets[3].Add(subTicket);
						break;
					case PrizesPicks.PRIZE_CRITERIA_30_WAY:
						tickets[4].Add(subTicket);
						break;
					case PrizesPicks.PRIZE_CRITERIA_60_WAY:
						tickets[5].Add(subTicket);
						break;
					case PrizesPicks.PRIZE_CRITERIA_120_WAY:
						tickets[6].Add(subTicket);
						break;
					default:
						throw new GameEngineException($"Prize {prizeCriteria} is not valid.");
				}
			}

			var result = tickets.Where(x => x.Count > 0);
			CheckIfItIsOnlyOnePrize(result);
			return result;
		}

		private void CheckIfItIsOnlyOnePrize(IEnumerable<TicketByPrize> ticketsByPrize)
		{
			if (ticketsByPrize.Count() == 1)
			{
				foreach (TicketByPrizePick5Boxed ticketByPrize in ticketsByPrize)
				{
					ticketByPrize.HasOnlyOnePrize = true;
				}
			}
		}

		internal override void GenerateWagers()
		{
			var betAmount = BetAmount();
			GenerateWagers(betAmount);
		}

		internal override void GenerateWagers(decimal betAmount)
		{
			PrizesPicks currPrizes = (PrizesPicks)base.Prizes;
			foreach (SubTicket<IPick> subticket in SubTickets())
			{
				var prizeCriteria = PrizesPicks.WayOfSubticket(subticket[1], subticket[2], subticket[3], subticket[4], subticket[5]);
				decimal prize = currPrizes.Prize(TicketType.P5B, prizeCriteria);

				prize *= betAmount;
				var prizeToWin = prize - betAmount;
				prizeToWin = prizeToWin < 0 ? 0 : prizeToWin;
				var strNumbers = subticket.AsStringForAccounting();
				AddWager(betAmount, prizeToWin, strNumbers, subticket);
			}
		}
	}

	internal class TicketByPrizePick5Boxed : TicketByPrize
	{
		internal bool HasOnlyOnePrize { get; set; }

		internal TicketByPrizePick5Boxed(Ticket ticket, int prizeCriteria) : base(ticket, prizeCriteria)
		{

		}

		internal override bool HasNumber(int[] numbers)
		{
			var result = Subtickets.Cast<SubTicket<IPick>>().Any(x => x.IsTheSameNumberWithDifferentOrder(numbers[0], numbers[1], numbers[2], numbers[3], numbers[4]));
			return result;
		}

		internal override string AsString()
		{
			if (HasOnlyOnePrize)
			{
				return ((TicketPick5Boxed)Ticket).AsString();
			}

			StringBuilder result = new StringBuilder();
			result.Append(Id);
			foreach (var subticket in Subtickets)
			{
				result.Append(subticket.AsString());
			}
			return result.ToString();
		}
	}
}
