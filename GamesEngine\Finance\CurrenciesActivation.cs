﻿using GamesEngine.Logs;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Finance
{
    [Puppet]
    class CurrenciesActivation : Objeto, ILogs
    {
        internal const byte MAX_LOG_CAPACITY = 5;
        internal Logs.Log Log { get;  }

        readonly Dictionary<Coin, bool> enabledCoins = new Dictionary<Coin, bool>();
        readonly byte maxLogCapacity;

        internal CurrenciesActivation() : this(MAX_LOG_CAPACITY)
        {
            
        }

        internal CurrenciesActivation(byte maxLogCapacity)
        {
            this.maxLogCapacity = maxLogCapacity;
            var uid = System.Guid.NewGuid();
            Log = new Log(uid.ToString(), maxLogCapacity);
        }

        internal void Include(Coin coin, bool enabled = true)
        {
            enabledCoins.Add(coin, enabled);
        }

        internal bool IsEnabled(Coin coin)
        {
            bool result;
            if (!enabledCoins.TryGetValue(coin, out result)) return false;
            return result;
        }

        internal void EnableDomain(Coin coin)
        {
            enabledCoins[coin] = true;
        }

        internal void DisableDomain(Coin coin)
        {
            enabledCoins[coin] = false;
        }

        internal bool AnyEnabledDomain()
        {
            var result = enabledCoins.Any(kv => kv.Value == true);
            return result;
        }

        public void AddAnnotation(string id, string message, string who, DateTime now)
        {
            if (string.IsNullOrEmpty(id)) throw new ArgumentNullException(nameof(id));
            if (string.IsNullOrEmpty(message)) throw new ArgumentNullException(nameof(message));
            if (string.IsNullOrEmpty(who)) throw new ArgumentNullException(nameof(who));

            Log.AddEntry(now, who, $"{who} set as {message} at {now.ToString("dd/MM/yyyy hh:mm tt")}");
        }

    }
}
