﻿using GamesEngine.Custodian.Operations;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Custodian
{
	[Puppet]
	public class Profiles : Objeto
	{
		private int profilesIdConsecutive = 0;
		private List<Profile> profiles;

		internal int NextProfileId()
		{
			profilesIdConsecutive += 1;
			return profilesIdConsecutive;
		}

		internal Profile CreateProfile(bool itsThePresent, int profeileId, string name)
		{
			if (string.IsNullOrEmpty(name)) throw new GameEngineException($"{nameof(name)} it's required.");
			if (Exists(name)) throw new GameEngineException($"{name} already exists.");

			if(profiles == null) profiles = new List<Profile>();

			profilesIdConsecutive = profeileId;
			Profile profile= new Profile(profeileId, name);
			profiles.Add(profile);

			if (itsThePresent) Integration.Kafka.Send(itsThePresent, Integration.Kafka.TopicForGuardianInternalOperations, profile.GenerateMessage());

			return profile;
		}

		internal Owner CreateOwnerProfile(bool itsThePresent, int profeileId, string name)
		{
			if (string.IsNullOrEmpty(name)) throw new GameEngineException($"{nameof(name)} it's required.");
			if (Exists(name)) throw new GameEngineException($"{name} already exists.");

			if (profiles == null) profiles = new List<Profile>();

			profilesIdConsecutive = profeileId;
			Owner profile = new Owner(profeileId, name);
			profiles.Add(profile);

			if (itsThePresent) Integration.Kafka.Send(itsThePresent, Integration.Kafka.TopicForGuardianInternalOperations, profile.GenerateMessage());

			return profile;
		}
		

		internal Profile SearchById(int profileId)
		{
			if (profiles == null) throw new GameEngineException($"There is no  {nameof(Profile)} with the id {profileId}.");

			Profile  profile = profiles.FirstOrDefault(profile => profile.Id == profileId);
			
			if (profile == null) throw new GameEngineException($"There is no  {nameof(Profile)} with the id {profileId}.");

			return profile;
		}

		internal bool Exists(int id)
		{
			if (profiles == null) return false;

			return profiles.FirstOrDefault(profile => profile.Id == id) != null;
		}

		private bool Exists(string name)
		{
			if (profiles == null) return false;

			return profiles.FirstOrDefault(profil => profil.Name == name) != null;
		}

		private bool Exists(Profile profile)
		{
			if (profiles == null) return false;

			return profiles.FirstOrDefault(storedProfile => storedProfile == profile) != null;
		}

		internal int Count()
		{
			if (profiles == null) return 0;

			return profiles.Count;
		}

		internal bool CanBeApprovedBy(Approver approver)
		{
			if (profiles == null) return false;
			foreach (Profile profile in profiles)
			{
				if (!profile.CanBeApprovedBy(approver)) return false;
			}

			return true;
		}

		internal IEnumerable<Profile> List()
		{
			if (profiles == null) return new List<Profile>();

			return profiles;
		}

		internal void Add(Profile profile)
		{
			if (profile == null) throw new GameEngineException($"{nameof(profile)} it's required.");
			if (Exists(profile)) throw new GameEngineException($"{profile.Name} already exists.");

			if (profiles == null) profiles = new List<Profile>();

			profiles.Add(profile);
		}

		internal Approvers Approvers()
		{
			Approvers result = new Approvers();
			foreach (Profile profile in profiles)
			{
				foreach (Approver approver in profile.ListApprovers())
				{
					if (!result.Exists(approver)) result.Add(approver);
				}
			}
			return result;
		}

		internal bool HasAnOwner
		{
			get
			{
				foreach (Profile profile in profiles)
				{
					if (profile is Owner) return true;
				}
				return false;
			}
		}

		internal Profile SearchByName(string name)
		{
			if (string.IsNullOrEmpty(name)) throw new ArgumentNullException($"{nameof(name)} it's required.");
			if (profiles == null) throw new GameEngineException($"There is no {nameof(Approver)}  with name {name} in {nameof(Profiles)}.");

			foreach (Profile profile in profiles)
			{
				bool found = profile.Name == name;
				if (found) return profile;
			}
		
			throw new GameEngineException($"There is no {nameof(Approver)}  with name {name} in {nameof(Profiles)}.");
		}

		internal void AddIfNotExists(Profiles profilesArg)
		{
			if (profilesArg == null) throw new ArgumentNullException(nameof(profilesArg));
			if (profiles == null) profiles = new List<Profile>();
            
			if (!profilesArg.IsEmpty()) 
			{ 
				foreach (Profile profile in profilesArg.profiles)
				{
					bool found = profiles.Contains(profile);
					if ( ! found)
					{
						profiles.Add(profile);
					}
				}
            }
        }

		internal Profiles Intersection(Profiles profilesArg)
		{
			Profiles result = new Profiles();

			foreach (Profile profile in profilesArg.List())
			{
				if ( profiles.Contains(profile) )
				{
					result.Add(profile);
				}
			}
			return result;
		}

		internal void Remove(Profile profile)
		{
			if (profile == null) throw new ArgumentNullException(nameof(profile));
			if (!Exists(profile)) throw new GameEngineException($"{nameof(profile)} {profile.Name} does not exist.");

			profiles.Remove(profile);
		}

		internal bool IsEmpty()
		{
            if (profiles == null) return true;

            return profiles.Count == 0;
        }
	}
	[Puppet]
	public class Profile : Objeto
	{
		private string name;
		internal int Id { get; }
		internal string Name 
		{
			get
			{
				return name;
			}
		}

		internal void ChangeName(bool itsThePresent, string name)
		{
			if (string.IsNullOrEmpty(name)) throw new GameEngineException($"{nameof(name)} it's required.");

			this.name = name;

			if (itsThePresent) Integration.Kafka.Send(itsThePresent, Integration.Kafka.TopicForGuardianInternalOperations, this.GenerateMessage());
		}

		private Approvers approvers;

		internal string Emails 
		{
			get
			{
				if (this.approvers == null || this.approvers.Count()==0 ) return "";
				
				StringBuilder builder = new StringBuilder();

				foreach (Approver approver in approvers.List())
				{
					builder.Append(approver.EmailAddress);

					if (builder.Length >= 97)
					{
						builder.Length = 97;
						builder.Append("...");
						return builder.ToString();
					}
					else
					{
						builder.Append(",");
					}
				}

				builder.Length = builder.Length -1;
				return builder.ToString();
			}
		}

		internal Profile(int profeileId, string name)
		{
			if (string.IsNullOrEmpty(name)) throw new GameEngineException($"{nameof(name)} it's required.");
			if (profeileId <= 0) throw new GameEngineException($"{nameof(profeileId)} it's required.");

			Id = profeileId;
			this.name = name;
		}
		public override string ToString()
		{
			return Name;
		}
		
		internal IEnumerable<Approver> ListApprovers()
		{
			if (approvers == null) return new List<Approver>();

			return approvers.List();
		}

		internal void Add(Approver approver)
		{
			if (approver == null) throw new GameEngineException($"{nameof(approver)} it's required.");
			if (approvers != null && approvers.Exists(approver)) throw new GameEngineException($"{approver.EmailAddress} already exists.");

			if(approvers == null) approvers = new Approvers();
			approvers.Add(approver);
			approver.LinkTo(this);
		}

		internal void Remove(Approver approver)
		{
			if (approver == null) throw new GameEngineException($"{nameof(approver)} it's required.");

			if (approvers == null) approvers = new Approvers();
			approvers.Remove(approver);
		}

		internal bool IfFound(string emailAddres, out Approver result)
		{
			result = null;
			foreach (Approver approver in approvers.List())
			{
				if (approver.EmailAddress== emailAddres)
				{
					result = approver;
					return true;
				}
			}

			return false;
		}

		internal bool Exists(Approver approverArg)
		{
			if (approvers == null) return false;
			foreach (Approver approver in approvers.List())
			{
				if (approver.EmailAddress == approverArg.EmailAddress)
				{
					return true;
				}
			}
			return false;
		}

		internal KafkaMessage GenerateMessage()
		{
			return new ProfileCreationMessage(Id, Name);
		}

		internal bool CanBeApprovedBy(Approver approverArg)
		{
			if (approverArg.ItsAnOwner) return true;
			if (approverArg == null) throw new ArgumentNullException(nameof(approverArg));
			if (approvers == null) return false;
			
			foreach (Approver approver in approvers.List())
			{
				if (approver == approverArg)
				{
					return true;
				}
			}
			return false;
		}
	}

	internal class Owner : Profile
	{
		internal Owner(int profeileId, string name) :
			base(profeileId, name)
		{ 
		}

		public override string ToString()
		{
			return Name+" "+nameof(Owner);
		}
	}
	public class ProfileChanges 
	{
		public bool ItsOwner { get; set; }
		public string Name { get; set; }
		public string[] EmailAddressToBeAdded { get; set; } = new string[0];
		public bool NameItsEmpty
		{
			get
			{
				return string.IsNullOrEmpty(Name);
			}
		}
		public string[] EmailAddressToBeRemoved { get; set; } = new string[0];
		public bool CheckIfAllEmailsAreValid
		{
			get
			{
				if (EmailAddressToBeAdded != null)
				{
					foreach (string emailAddress in EmailAddressToBeAdded)
					{
						if (!Approver.ItsAValidEmailsAddress(emailAddress)) return false;
					}
				}
				if (EmailAddressToBeRemoved != null)
				{
					foreach (string emailAddress in EmailAddressToBeRemoved)
					{
						if (!Approver.ItsAValidEmailsAddress(emailAddress)) return false;
					}
				}
				
				return true;
			}
		}
	}

	public class ProfileChangeCreation
	{
		public bool ItsOwner { get; set; }
		public string Name { get; set; }
		public string[] EmailAddressToBeAdded { get; set; } = new string[0];
		public virtual bool CheckIfAllEmailsAreValid
		{
			get
			{
				foreach (string emailAddress in EmailAddressToBeAdded)
				{
					if (!Approver.ItsAValidEmailsAddress(emailAddress)) return false;
				}
				return true;
			}
		}
		public bool NameItsEmpty
		{
			get
			{
				return string.IsNullOrEmpty(Name);
			}
		}
	}

	public class ProfileStored
	{
		public int Id { get; set; }
		public string Name { get; set; }
		public bool itAlreadyApproved { get; set; }

	}
}
