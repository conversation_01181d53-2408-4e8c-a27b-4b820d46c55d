﻿using GamesEngine.Finance;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Threading.Tasks;

namespace ExchangeAPI.Model
{
	[DataContract(Name = "DebitNotesDTO")]
	public class DebitNotesDTO
	{
		[DataMember(Name = "debitNotes")]
		public List<DebitNoteDTO> DebitNotes { get; set; } = new List<DebitNoteDTO>();

		[DataMember(Name = "totalRecords")]
		public int TotalRecords { get; set; }

		[DataMember(Name = "totalAmount")]
		public decimal TotalAmount { get; set; }

		[DataMember(Name = "totalAmountFormatted")]
		public string TotalAmountFormatted { get; set; }

		internal void Add(DebitNoteDTO creditNoteDTO)
		{
			DebitNotes.Add(creditNoteDTO);
		}

	}

	[DataContract(Name = "DebitNoteDTO")]
	public class DebitNoteDTO
	{
		[DataMember(Name = "date")]
		public string Date { get; set; }
		[DataMember(Name = "domain")]
		public string Domain { get; set; }
		[DataMember(Name = "description")]
		public string Description { get; set; }
		[DataMember(Name = "currencyCode")]
		public string CurrencyCode { get; set; }
		[DataMember(Name = "amountReceived")]
		public decimal AmountReceived { get; set; }
		[DataMember(Name = "amountReceivedFormatted")]
		public string AmountReceivedFormatted { get
			{ 
				return Currency.Factory(CurrencyCode, AmountReceived).ToDisplayFormat();
			}
		}

		[DataMember(Name = "approvals")]
		public int Approvals { get; set; }
		[DataMember(Name = "rejections")]
		public int Rejections { get; set; }
		[DataMember(Name = "approvalsRequired")]
		public int ApprovalsRequired { get; set; }
		public bool IsPending
		{
			get
			{
				return State == TransactionState.Pending;
			}
		}

		[DataMember(Name = "referenceId")]
		public string ReferenceId { get; set; }
		[DataMember(Name = "id")]
		public string ID { get; set; }
		[DataMember(Name = "batchNumber")]
		public string BatchNumber { get; set; }
		[DataMember(Name = "state")]
		public string StateAsString
		{
			get
			{
				return State.ToString();
			}
		}

		internal TransactionState State
		{
			get
			{
				if (Approvals == ApprovalsRequired) return TransactionState.Approved;
				if (Rejections == ApprovalsRequired) return TransactionState.Rejected;
				return TransactionState.Pending;
			}
		}

		[DataMember(Name = "address")]
		public string Customer { get; set; }
		[DataMember(Name = "account")]
		public string Account { get; set; }
		[DataMember(Name = "voucherUrl")]
		public string VoucherUrl { get; set; }
		[DataMember(Name = "reason")]
		public string RejectionReason { get; set; }
		[DataMember(Name = "whoCreated")]
		public string WhoCreated { get; set; }

		[DataMember(Name = "attachments")]
		public List<string> Attachments { get; set; } = new List<string>();
		[DataMember(Name = "processorAccountId")]
		public int ProcessorAccountId { get; set; }
		[DataMember(Name = "journalDetailEntryId")]
		public string JournalDetailEntryId { get; set; }
	}
}
