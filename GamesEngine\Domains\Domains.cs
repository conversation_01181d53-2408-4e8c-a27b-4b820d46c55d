﻿using GamesEngine.Business;
using GamesEngine.Logs;
using System;
using System.Collections.Generic;
using System.Text;
using static GamesEngine.Exchange.PaymentProcessorsAndActionsByDomains;
using static GamesEngine.Finance.PaymentChannels;

namespace GamesEngine.Domains
{
    internal class Domains:BaseDomains, ILogs
    {
        private readonly Company company;
        private readonly Logs.Logs logs = new Logs.Logs();

        internal Domains(Company company) : base(company)
        {
            this.company = company;
        }

        private const int MAX_NUMBER_OF_ENTRIES = 5;

        internal Domain CreateDomain(int id, string url, Agents agent)
        {
            bool avoidResendingALoopMessageForDomain = false;
            return CreateDomain(avoidResendingALoopMessageForDomain, id, url, agent);
        }

        internal Domain CreateDomain(bool itIsThePresent, int id, string url, Agents agent)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(url)) throw new ArgumentNullException(nameof(url));

            var normalizedUrl = Domain.NormalizeUrl(url);
            var log = logs.CreateLog(id.ToString(), MAX_NUMBER_OF_ENTRIES);
            var domain = base.CreateDomain(itIsThePresent, id, normalizedUrl, agent, log);

            Consecutive = id;
            return domain;
        }

        internal void UpdateDomain(int id, string newUrl, string employeeName, DateTime now)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(newUrl)) throw new ArgumentNullException(nameof(newUrl));
            if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

            var currentDomain = DomainFrom(id);
            var normalizedNewUrl = Domain.NormalizeUrl(newUrl);

            if (currentDomain.Url == normalizedNewUrl) throw new GameEngineException($"Both urls are the same. It cannot be updated");
            if (!Exists(id)) throw new GameEngineException($"Domain with {nameof(id)} '{id}' does not exist or it is disabled");

            UpdateDomainUrl(false, currentDomain.Url, normalizedNewUrl);

            var log = logs.SearchLog(id.ToString());
            log.AddEntry(now, employeeName, $"Last update by {employeeName} at {now.ToString("dd/MM/yyyy hh:mm tt")}");
        }

        internal void EnableDomain(int id, string employeeName, DateTime now)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));
            if (!company.Sales.ExistsDomain(id)) throw new GameEngineException($"Domain with {nameof(id)} '{id}' does not exist");

            var domain = company.Sales.DomainFrom(id);
            Add(domain);

            var log = logs.SearchLog(id.ToString());
            log.AddEntry(now, employeeName, $"Enabled by {employeeName} at {now.ToString("dd/MM/yyyy hh:mm tt")}");
        }

        internal void DisableDomain(int id, string employeeName, DateTime now)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));
            if (!Exists(id)) throw new GameEngineException($"Domain with {nameof(id)} '{id}' does not exist");

            var domain = company.Sales.DomainFrom(id);
            Remove(domain);

            var log = logs.SearchLog(id.ToString());
            log.AddEntry(now, employeeName, $"Disabled by {employeeName} at {now.ToString("dd/MM/yyyy hh:mm tt")}");
        }

        internal Log SearchLog(string id)
        {
            if (string.IsNullOrWhiteSpace(id)) throw new ArgumentNullException(nameof(id));

            var log = logs.SearchLog(id);
            return log;
        }

        public void AddAnnotation(string id, string message, string who, DateTime now)
        {
            if (!int.TryParse(id, out int domainId)) throw new GameEngineException($"{nameof(id)} is not an integer");

            var domain = DomainFrom(domainId);
            domain.AddAnnotation(message, who, now);
        }
    }
}
