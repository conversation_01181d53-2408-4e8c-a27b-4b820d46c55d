﻿using GamesEngine.Bets;
using MySql.Data.MySqlClient;
using Puppeteer;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Interprete.Libraries;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CashierAPI.CashierFollower
{
	internal class FragmentPaymentRule : RuleWithoutActor
	{
		protected const string START_TEXT_LINE1 = "balance.Accredit(";
		protected const string START_TEXT_LINE2 = "balance.Withdraw";
		protected const string END_TEXT_LINE = ");";

        public override void Then(Script script)
		{
            var authorizationNumber = AuthorizationNumber(script.Text);
			if (!string.IsNullOrEmpty(authorizationNumber))
			{
				bool canBeAcreditAfterDeleteAuthorization = true;
				DBHelperCashierFollower.UpdateAuthorizationInfoForFragmentPayment(authorizationNumber, script.DairyId, canBeAcreditAfterDeleteAuthorization);
			}
		}

		public override void When(Script script)
		{
			if (script.Text.IndexOf(START_TEXT_LINE1) != -1 && script.Text.IndexOf(START_TEXT_LINE2) != -1)
			{
				if (script.Text.IndexOf("Lotto Win for") != -1 || script.Text.IndexOf("Lotto Refund for") != -1 || 
					script.Text.IndexOf("Lotto FB Win for") != -1 || script.Text.IndexOf("Lotto FB Refund for") != -1 ||
                    script.Text.IndexOf("Lotto Free Play Win") != -1 || script.Text.IndexOf("Lotto Free Play No Action") != -1 ||
                    script.Text.IndexOf("Lotto FB Free Play Win") != -1 || script.Text.IndexOf("Lotto FB Free Play No Action") != -1) 
				{
					Then(script);
				}
			}
		}

		public string AuthorizationNumber(string script)
		{
			UtilStringRules.SetHeaderText(START_TEXT_LINE1, END_TEXT_LINE);
			string[] argumentos = UtilStringRules.GetBodyArgText(script).Split(", ");
			string authorizationNumberText = argumentos[7];
			
			string[] refrerenceWithAuthorization = authorizationNumberText.Split('-');
			string authorizationNumber = refrerenceWithAuthorization[0].Substring(1);

            return authorizationNumber.Trim();
		}

        public override void FinalizeRule()
        {
            return;
        }
    }
}
