﻿using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamesEngine.Finance
{
    public class TotalizerSales : Objeto
    {
        private SalesStorage storageCampaignSales;

        private CampaignSaleSummary campaignDailySummary;

        private int maxOldPurchaseDays = 3;

        internal TotalizerSales(SalesStorage storageCampaignSales)
        {
            this.storageCampaignSales = storageCampaignSales;
            campaignDailySummary = new CampaignSaleSummary();

            StartUpUpdateSales();
        }

        internal int MaxOldPurchaseDays
        {
            get
            {
                return maxOldPurchaseDays;
            }
            set
            {
                if (value < 1) throw new GameEngineException($"{nameof(MaxOldPurchaseDays)} must be greater than 0");
                maxOldPurchaseDays = value;
            }
        }

        private DateTime lastSaleDate;
        internal DateTime LastSaleDate
        {
            get
            {
                if (lastSaleDate == DateTime.MinValue)
                {
                    lastSaleDate = this.storageCampaignSales.LastSaleDate();
                }
                return lastSaleDate;
            }
            set
            {
                if (value == DateTime.MinValue) throw new GameEngineException($"{nameof(value)} must be greater than {DateTime.MinValue}");

                if (value.Date > lastSaleDate.Date)
                {
                    lastSaleDate = value.Date;
                }
            }
        }

        internal DateTime lastDailySaleDate;
        internal DateTime LastDailySaleDate
        {
            get
            {
                if (lastDailySaleDate == DateTime.MinValue)
                {
                    lastDailySaleDate = this.storageCampaignSales.LastDailySaleDate();
                }

                return lastDailySaleDate;
            }
            set
            {
                if (value == DateTime.MinValue) throw new GameEngineException($"{nameof(value)} must be greater than {DateTime.MinValue}");

                if (value.Date > lastDailySaleDate.Date)
                {
                    lastDailySaleDate = value.Date;
                }
            }
        }

        internal void StartUpUpdateSales()
        {
            if (LastSaleDate == DateTime.MinValue)
            {
                // if this happend, means there is no sales in the DB, so no need to do the StartUpUpdateSales, cause there is no data to update.
                return;
            }

            if (LastDailySaleDate == DateTime.MinValue)
            {
                // if this happend, means there is no daily sales in the DB, will set the LastSaleDate
                LastDailySaleDate = LastSaleDate.Date;
            }

            TimeSpan difDays = LastDailySaleDate - LastSaleDate;
            int totalDays = (int)difDays.TotalDays;
            if (totalDays > 0)
            {
                // New Day, Update previous day
                for (int i = 0; i < totalDays; i++)
                {
                    DateTime iteratorSaleDate = LastSaleDate.AddDays(i);
                    storageCampaignSales.UpdateDailySalesDate(iteratorSaleDate);
                }

                // Update Last Sale Date
                LastDailySaleDate = LastSaleDate.Date;
            }
        }

        internal void UpdateSales(DateTime purchaseDate)
        {
            if (purchaseDate == DateTime.MinValue) throw new GameEngineException($"{nameof(purchaseDate)} must be greater than {DateTime.MinValue}");

            TimeSpan difDays = purchaseDate.Date - LastSaleDate;
            int totalDays = (int)difDays.TotalDays;
            if (totalDays > 0)
            {
                // New Day, Update previous day
                storageCampaignSales.UpdateDailySalesDate(LastSaleDate);

                // Update Last Sale Date
                LastSaleDate = purchaseDate.Date;
            }
            else if (totalDays >= (-maxOldPurchaseDays) && totalDays < 0)
            {
                // Old Date, Update previous days, since maxOldPurchaseDays
                DateTime dateToUpdate = LastSaleDate.AddDays(totalDays);
                storageCampaignSales.UpdateDailySalesDate(dateToUpdate);
            }
            else
            {
                // Same Day or Day excede maxOldPurchaseDays, Then do nothing
            }
        }

        internal void StorageCampaignDailySale(bool itIsThePresent, int campaignId, DateTime purchaseDate, Currencies.CODES currencyCode, decimal amount, int domainId, int storeId)
        {
            if (campaignDailySummary.LastPurchaseDate(campaignId) == DateTime.MinValue)
            {
                campaignDailySummary.CampaignLastPurchaseDate(campaignId, purchaseDate.Date);
            }

            if (purchaseDate == DateTime.MinValue) throw new GameEngineException($"{nameof(purchaseDate)} must be greater than {DateTime.MinValue}");
            if (amount < 0) throw new GameEngineException($"{nameof(amount)} must be greater than 0");
            if (domainId <= 0) throw new GameEngineException($"{nameof(domainId)} must be greater than 0");
            if (storeId <= 0) throw new GameEngineException($"{nameof(storeId)} must be greater than 0");

            TimeSpan difDays = purchaseDate.Date - campaignDailySummary.LastPurchaseDate(campaignId);
            int totalDays = (int)difDays.TotalDays;
            if (totalDays >= (-maxOldPurchaseDays))
            {
                ReleaseAllCampaignDailySummary(itIsThePresent, purchaseDate, campaignId, currencyCode, amount, domainId, storeId);
            }

            campaignDailySummary.CampaignLastPurchaseDate(campaignId, purchaseDate.Date);
        }

        internal void ReleaseAllCampaignDailySummary(bool itIsThePresent, DateTime now, int campaignId, Currencies.CODES currency, decimal amount, int domainId, int storeId)
        {
            if (itIsThePresent)
            {
                if (this.storageCampaignSales == null) this.storageCampaignSales = SalesStorage.CreateStorageConnection();
                this.storageCampaignSales.ReleasePlayerDailySummary(campaignId, currency, now, domainId, storeId, amount);
            }
        }

        internal class CampaignSaleSummary
        {
            private Dictionary<int, DateTime> lastPurchaseDateCampaign;

            internal CampaignSaleSummary()
            {
                lastPurchaseDateCampaign = new Dictionary<int, DateTime>();
            }

            internal DateTime LastPurchaseDate(int campaignId)
            {
                DateTime lastPurchaseDate;
                if (!lastPurchaseDateCampaign.TryGetValue(campaignId, out lastPurchaseDate))
                {
                    lastPurchaseDateCampaign.Add(campaignId, DateTime.MinValue);
                    lastPurchaseDate = lastPurchaseDateCampaign[campaignId];
                }
                return lastPurchaseDate.Date;
            }

            internal void CampaignLastPurchaseDate(int campaignId, DateTime purchaseDate)
            {
                if (lastPurchaseDateCampaign.ContainsKey(campaignId))
                {
                    // update only if the new date is greater than the old date
                    if (purchaseDate.Date > lastPurchaseDateCampaign[campaignId].Date)
                    {
                        lastPurchaseDateCampaign[campaignId] = purchaseDate.Date;
                    }
                }
                else
                {
                    lastPurchaseDateCampaign.Add(campaignId, purchaseDate.Date);
                }
            }
        }
    }

    public static class DateTimeExtensions
    {
        public static int WeekOfYear(this DateTime date)
        {
            CultureInfo culture = CultureInfo.CurrentCulture;
            CalendarWeekRule rule = culture.DateTimeFormat.CalendarWeekRule;
            DayOfWeek firstDayOfWeek = culture.DateTimeFormat.FirstDayOfWeek;
            //DayOfWeek firstDayOfWeek = DayOfWeek.Monday;

            return culture.Calendar.GetWeekOfYear(date, rule, firstDayOfWeek);
        }
    }
}
