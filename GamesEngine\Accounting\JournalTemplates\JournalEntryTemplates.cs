﻿using GamesEngine.Business;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Text;

namespace GamesEngine.Accounting.JournalTemplates
{
	internal class JournalEntryTemplates
	{
		private readonly List<JournalEntryTemplate> templates = new List<JournalEntryTemplate>();
		private int journalEntryIdConsecutive = 0;
		internal int JournalEntryIdConsecutive
        {
			set
            {
				if (value < journalEntryIdConsecutive) throw new GameEngineException($"Current {nameof(journalEntryIdConsecutive)} is greater than {value}");
				journalEntryIdConsecutive = value;
			}
        }

		internal JournalEntryTemplate CreateTemplate(int id, string name)
		{
			if (id <= 0) throw new GameEngineException($"Id {nameof(id)} must be greater than 0");
			if (templates.Exists(t => t.Id == id)) new GameEngineException($"There is already a template identify by Id {id}. Template Ids must be unique");
			if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

			var template = new JournalEntryTemplate(id, name);
			templates.Add(template);
			return template;
		}

		internal JournalEntryTemplate this[int id]
        {
            get
            {
				foreach(var template in templates)
                {
					if (template.Id == id) return template;
                }
				throw new GameEngineException($"Template Id {id} can not be found");
			}
        }

		internal int NewJournalEntryNumber()
		{
			journalEntryIdConsecutive++;
			return journalEntryIdConsecutive;
		}
	}
}
