﻿using GamesEngine.Games;
using GamesEngine.Games.Tournaments;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using System.Text;

namespace GamesEngine.Gameboards.MarchMadness
{
    [System.Diagnostics.DebuggerDisplay("{ToString()}")]
    [Puppet]
    internal sealed class Predictions : Objeto
    {
        private readonly List<GameInThisPrediction> results = new List<GameInThisPrediction>();
        private readonly Tournament tournament;
        private readonly Bracket bracket;
        private readonly Round roundWhereStartPreditions;
        private readonly Versions<PredictionVersionInfo> resultVersions = new Versions<PredictionVersionInfo>();

        internal Predictions(Tournament tournament, Bracket bracket) : this(tournament)
        {
            if (bracket == null) throw new ArgumentNullException(nameof(bracket));
            this.bracket = bracket;
        }

        internal Predictions(Tournament tournament)
        {
            if (tournament == null) throw new ArgumentNullException(nameof(tournament));
            this.tournament = tournament;
            roundWhereStartPreditions = tournament.CurrentRound();
            foreach (Game game in tournament.GamesFrom(roundWhereStartPreditions).List())
            {
                if (game.Number == 0) throw new GameEngineException("This game has no Number configured.");
                if (game.NextGame != null && game.NextGame.Number == 0) throw new GameEngineException("This game has no Number configured.");
                results.Add(new GameInThisPrediction(this, game));
            }
        }

        internal Tournament Tournament
        {
            get
            {
                return this.tournament;
            }
        }

        internal DateTime CreatedOn
        {
            get
            {
                return this.resultVersions.StartedOn;
            }
        }

        internal bool ArePerfect()
        {
            var hasAllPredictionsOfTournament = Count() == tournament.CountGames();
            if (!hasAllPredictionsOfTournament) return false;
            var notPerfect = results.Exists(p => p.Game.IsGameOver() && p.PredictedWinner() != p.Game.Winner());
            return !notPerfect;
        }

        internal Round RoundWhereStartPreditions
        {
            get
            {
                return roundWhereStartPreditions;
            }
        }

        internal delegate int PointsCriteria(Game game, ScorePair official, ScorePair prediction);
        internal delegate BigInteger UntieCriteria();

        internal List<GamePredictionAffected> AffectedByCheckingAllGames(List<GameInThisPrediction> newPredictions)
        {
            if (newPredictions == null) throw new ArgumentNullException(nameof(newPredictions));
            var gamesInTournament = newPredictions.Select(g => g.Game).ToList();
            if (!tournament.Contains(gamesInTournament)) throw new GameEngineException("Tournament does not contain some games");
            if (!Contains(gamesInTournament)) throw new GameEngineException("Predictions does not contain some games");

            var gamesModified = GamesModifiedInThisPrediction(newPredictions);
            var result = (gamesModified.Count == 0) ? new List<GamePredictionAffected>() : AffectedBy(gamesModified);
            return result;
        }

        internal List<GamePredictionAffected> AffectedBy(List<Game> gamesModified)
        {
            if (gamesModified == null) throw new ArgumentNullException(nameof(gamesModified));
            if (!tournament.Contains(gamesModified)) throw new GameEngineException(string.Format("Tournament does not contain some games"));
            if (!Contains(gamesModified)) throw new GameEngineException(string.Format("Predictions does not contain some games"));

            var tournamentGamesAffected = tournament.AffectedBy(gamesModified);
            var gamesInThisPredictionAffected = new List<GamePredictionAffected>();
            foreach (var tournamentGameAffected in tournamentGamesAffected)
            {
                var originalPrediction = FindInternalGameBy(tournamentGameAffected.Game);
                var clonOriginalPrediction = new GamePredictionAffected(originalPrediction, false);
                clonOriginalPrediction.SetPredecessorGame(tournamentGameAffected.PredecessorGame);

                bool existPredictionWithTheSamePredecessor = gamesInThisPredictionAffected.Exists(g => g.PredecessorGame == clonOriginalPrediction.PredecessorGame);
                if (!existPredictionWithTheSamePredecessor)
                {
                    gamesInThisPredictionAffected.Add(clonOriginalPrediction);
                }
            }
            return gamesInThisPredictionAffected;
        }

        internal void CopyFrom(Predictions other)
        {
            if (other == null) throw new ArgumentNullException(nameof(other));
            if (results.Exists(p=>p.Game.IsInPlay() || p.Game.IsGameOver())) throw new GameEngineException("Some game has started or ended");
            if (other.results.Exists(p => p.Game.IsInPlay() || p.Game.IsGameOver())) throw new GameEngineException("Some game has started or ended");

            this.results.Clear();

            foreach (GameInThisPrediction internalGame in other.results)
            {
                GameInThisPrediction temp = new GameInThisPrediction(this, internalGame.Game);
                temp.TeamA = internalGame.TeamA;
                temp.TeamB = internalGame.TeamB;
                temp.WriteANewResult(internalGame.TeamA, internalGame.Scores.ScoreTeamA, internalGame.TeamB, internalGame.Scores.ScoreTeamB);
                this.results.Add(temp);
            }
        }

        private bool Contains(List<Game> games)
        {
            var gamesFound = true;
            foreach (var game in games)
            {
                gamesFound = ExistGameInPredictions(game);
                if (!gamesFound) break;
            }
            return gamesFound;
        }

        private GameInThisPrediction PredictionOfGame(Game game)
        {
            GameInThisPrediction temp = results.Find(g => g.Game == game);
            if (temp == null) throw new GameEngineException("There is no prediction for this game.");

            return temp;
        }

        internal GameInThisPrediction PredictionOf(int idOfTheGame)
        {
            var game = tournament.GetGameNumber(idOfTheGame);
            return PredictionOfGame(game);
        }

        internal bool FinalPredictionWith(string playerShortName)
        {
            var lastPrediction = results[results.Count - 1];
            var hasPlayerWithName = lastPrediction.TeamA.ShortName == playerShortName || lastPrediction.TeamB.ShortName == playerShortName;
            return hasPlayerWithName;
        }

        internal Team PredictedTournamentWinner()
        {
            var lastPrediction = results[results.Count - 1];
            var team = lastPrediction.PredictedWinner();
            return team;
        }

        internal void WritePointsOfTheGame(GameInThisPrediction game, Team teamA, int pointsOfTeamA, Team teamB, int pointsOfTeamB)
        {
            if (game == null) throw new ArgumentNullException(nameof(game));
            if (teamA == null) throw new ArgumentNullException(nameof(teamA));
            if (teamB == null) throw new ArgumentNullException(nameof(teamB));
            if (game.Game.CurrentRound.Number < roundWhereStartPreditions.Number) throw new GameEngineException("Predictions includes games of rounds that already happend");

            WriteWhoWins(
                game,
                teamA,
                Score.WithPoints(pointsOfTeamA),
                teamB,
                Score.WithPoints(pointsOfTeamB));
        }

        internal Predictions WritePointsOfTheGame(int idOfTheGame, int pointsOfPlayerA, int pointsOfPlayerB)
        {
            var gameInThisPrediction = PredictionOf(idOfTheGame);
            WritePointsOfTheGame(gameInThisPrediction, gameInThisPrediction.TeamA, pointsOfPlayerA, gameInThisPrediction.TeamB, pointsOfPlayerB);
            return this;
        }

        internal void WriteWhoWinsTheGame(GameInThisPrediction game, Team teamA, bool teamAWins, Team teamB, bool teamBWins)
        {
            if (teamAWins == teamBWins) throw new GameEngineException("One of players must win or loss the game.");
            if (game == null) throw new ArgumentNullException(nameof(game));
            if (teamA == null) throw new ArgumentNullException(nameof(teamA));
            if (teamB == null) throw new ArgumentNullException(nameof(teamB));
            if (game.Game.CurrentRound.Number < roundWhereStartPreditions.Number) throw new GameEngineException("Predictions includes games of rounds that already happend");

            WriteWhoWins(
                game,
                teamA,
                (teamAWins) ? Score.WIN : Score.LOSS,
                teamB,
                (teamBWins) ? Score.WIN : Score.LOSS);
        }

        internal Predictions WriteWhoWinsTheGame(int idOfTheGame, bool playerAWins, bool playerBWins)
        {
            var gameInThisPrediction = PredictionOf(idOfTheGame);
            WriteWhoWinsTheGame(gameInThisPrediction, gameInThisPrediction.TeamA, playerAWins, gameInThisPrediction.TeamB, playerBWins);
            return this;
        }

        internal void CommitVersion(DateTime now)
        {
            var initialRound = results.Select(x => x.Game.CurrentRound.Number).Min();
            for (int i = initialRound; i <= tournament.Rounds().Count(); i++)
            {
                var roundPredictions = results.Where(x => x.Game.CurrentRound.Number == i);
                var countGames = tournament.GetRoundNumber(i).CountGames();
                var countPredictions = roundPredictions.Count(x => x.HasResult());
                if (countPredictions != countGames) throw new GameEngineException($"Round {i} has {countGames} games but bracket only has predictions for {countPredictions} games");

                var firstPrediction = roundPredictions.First();
                bool isUsingPoints = firstPrediction.Scores.IsUsingPoints();
                var allAreUsingSameCriteria = roundPredictions.All(x => x.Scores.IsUsingPoints() == isUsingPoints);
                if (!allAreUsingSameCriteria) throw new GameEngineException($"Predictions of round {i} contains a mixed points criteria");
            }
            PredictionVersionInfo newVersion = new PredictionVersionInfo(results, bracket, now);
            resultVersions.AppendNewVersion(newVersion);
        }

        internal IEnumerable<PredictionVersionInfo> VersionsOfChanges()
        {
            var result = resultVersions.Modifications();
            return result;
        }

        internal bool AreTheSameScores(Predictions predictions)
        {
            var areEquals = true;
            foreach (var prediction in results)
            {
                areEquals = predictions.results.Exists(g => g.Game == prediction.Game && g.PredictedWinner() == prediction.PredictedWinner() && g.Scores.Equals(prediction.Scores));
                if (!areEquals) break;
            }
            return areEquals;
        }

        /// <summary>
        /// This method lets the user creates a Result if canWriteAResult returns true.
        /// </summary>
        private void WriteWhoWins(GameInThisPrediction gameInThisPrediction, Team teamA, Score scoreA, Team teamB, Score scoreB)
        {
            if (teamA == null || teamB == null) throw new ArgumentNullException(nameof(Team));
            var game = gameInThisPrediction.Game;
            if (game == null) throw new ArgumentNullException(nameof(game));

            bool AisUsingPoints = scoreA.IsUsingPoints();
            bool BisUsingPoints = scoreB.IsUsingPoints();

            if (AisUsingPoints && scoreA.Points > 500 || BisUsingPoints && scoreB.Points > 500)
                throw new GameEngineException("Never more than 500 points have been made in a basketball game.");
            else if (game.CurrentRound is FinalRound && (!AisUsingPoints || !BisUsingPoints))
                throw new GameEngineException("Score in the final round  must have points.");
            else if (!(game.CurrentRound is FinalRound || game.CurrentRound.Number >= 4) && (AisUsingPoints || BisUsingPoints))
                throw new GameEngineException("Score in a non final round  must have a winner or loser.");

            if (gameInThisPrediction.Game.CurrentRound.Number < roundWhereStartPreditions.Number) throw new GameEngineException("Predictions includes games of rounds that already happend");

            Tournament tournament = game.Tournament;
            Round currentRoundOfTheTournament = tournament.CurrentRound();
            Round roundOfTheGame = game.CurrentRound;

            if (tournament.IsEnded() || Tournament.IsReadyToPay())
            {
                throw new GameEngineException("It is not allowed to bet on a tournament that has started or ended.");
            }
            else if (currentRoundOfTheTournament.IsStarted() || currentRoundOfTheTournament.IsClosed())
            {
                throw new GameEngineException("It is not allowed to bet on a round that has started or ended.");
            }
            else if (!tournament.Contains(teamA) || !tournament.Contains(teamB))
            {
                throw new GameEngineException("This team is not part of the tournament.");
            }
            if (game.IsInPlay() || game.IsGameOver())
            {
                throw new GameEngineException("It is not allowed to bet on a started game or if the game is over.");
            }
            else if (roundOfTheGame.IsStarted() || roundOfTheGame.IsClosed())
            {
                throw new GameEngineException("Is not possible to bet in this round.");
            }
            else if (!tournament.AreSeedsFixed())
            {
                throw new GameEngineException("The tournament is not complete with all the teams.");
            }

            GameInThisPrediction prediction = PredictionOfGame(game);
            prediction.WriteANewResult(teamA, scoreA, teamB, scoreB);

            var nextGame = gameInThisPrediction.Game.NextGame;
            if (nextGame != null)
            {
                GameInThisPrediction nextGameInThisPrediction = PredictionOfGame(nextGame);

                Team teamWhoWins = prediction.Scores.TeamAisWinner() ? teamA : teamB;

                if (gameInThisPrediction.Game.Number % 2 == 0)
                {
                    nextGameInThisPrediction.TeamB = teamWhoWins;
                }
                else
                {
                    nextGameInThisPrediction.TeamA = teamWhoWins;
                }
            }

        }

        private GameInThisPrediction FindInternalGameBy(Game game)
        {
            if (game == null) throw new ArgumentNullException(nameof(game));
            GameInThisPrediction temp = results.Find(g => g.Game.Equals(game));
            if (temp == null)
            {
                throw new GameEngineException("There is no prediction for this game.");
            }

            return temp;
        }

        internal IEnumerable<GameInThisPrediction> ListAllPredictions()
        {
            return results;
        }

        internal new string ToString()
        {
            StringBuilder result = new StringBuilder();
            foreach (var prediction in results)
            {
                result.Append("[");
                result.Append(prediction.Game.Number);
                result.Append("=>");
                result.Append(prediction.Scores.ToString());
                result.Append("]");
            }
            return result.ToString();
        }

        private int grade = 0;
        internal int Grade(Game game, PointsCriteria criteria)
        {
            if (criteria == null) throw new ArgumentNullException(nameof(criteria));

            if (!game.IsPregame())
            {
                var gameInThisPrediction = this.PredictionOfGame(game);
                var previousPoints = gameInThisPrediction.Points();
                var currentPoints = gameInThisPrediction.GradeAndSetPoints(criteria);
                grade = grade - previousPoints + currentPoints;
            }
            return grade;
        }

        internal BigInteger GradeUntie(Game game, UntieCriteria criteria)
        {
            if (criteria == null) throw new ArgumentNullException(nameof(criteria));

            var gameInThisPrediction = this.PredictionOfGame(game);
            BigInteger currentPoints = gameInThisPrediction.GradeUntie(criteria);
            return currentPoints;
        }

        private List<Game> GamesModifiedInThisPrediction(List<GameInThisPrediction> predictions)
        {
            var gamesModified = new List<Game>();
            foreach (var prediction in predictions)
            {
                var originalPrediction = results.Find(g => g.Equals(prediction));
                if (!prediction.Scores.HasTheSameWinnerThan(originalPrediction.Scores))
                {
                    gamesModified.Add(prediction.Game);
                }
            }
            return gamesModified;
        }

        internal int Count()
        {
            return results.Count;
        }

        internal bool StartedOn(Round round)
        {
            if (round == null) throw new ArgumentNullException(nameof(round));

            return InitialRoundOfPredictions().Equals(round);
        }

        internal Round InitialRoundOfPredictions()
        {
            return roundWhereStartPreditions;
        }

        private bool ExistGameInPredictions(Game game)
        {
            var exists = this.results.Exists(item => item.Game == game);
            return exists;
        }
    }

    internal class PredictionVersionInfo : VersionInfo
    {
        private readonly List<GameInThisPrediction> results = new List<GameInThisPrediction>();
        private readonly Bracket bracket;

        internal PredictionVersionInfo(List<GameInThisPrediction> currPredictions, Bracket bracket, DateTime now) : base(now)
        {
            if (currPredictions == null) throw new ArgumentNullException(nameof(currPredictions));
            if (bracket == null) throw new ArgumentNullException(nameof(bracket));

            this.results = currPredictions.ConvertAll(x => new GameInThisPrediction(x));
            this.bracket = bracket;
        }

        internal PredictionVersionInfo PreviousVersion()
        {
            return (PredictionVersionInfo)base.GetPreviousVersion;
        }

        internal PredictionVersionInfo NextVersion()
        {
            return (PredictionVersionInfo)base.GetNextVersion;
        }

        internal IEnumerable<GameInThisPrediction> Results
        {
            get
            {
                return this.results;
            }
        }

        internal IEnumerable<GameInThisPrediction> ChangedOnPreviousResults()
        {
            if (this.IsTheFirstVersion) throw new GameEngineException("This is the first version. It does not have previous version");

            List<GameInThisPrediction> changes = new List<GameInThisPrediction>();

            var previousVersion = this.PreviousVersion();
            var previousResult = previousVersion.Results;
            var zip = results.Zip(previousResult, (n, p) => new { currentPrediction = n, previousPrediction = p });

            foreach (var z in zip)
            {
                var gameInPredictionOfCurrent = z.currentPrediction;
                bool changed = !z.currentPrediction.Scores.HasTheSameWinnerThan(z.previousPrediction.Scores) ||
                    z.currentPrediction.TeamA != z.previousPrediction.TeamA ||
                    z.currentPrediction.TeamB != z.previousPrediction.TeamB;
                if (changed)
                {
                    changes.Add(gameInPredictionOfCurrent);
                }
            }
            return changes;
        }

        internal IEnumerable<GameInThisPrediction> ChangedOnCurrentResults()
        {
            if (this.IsTheFirstVersion) throw new GameEngineException("This is the first version. It does not have previous version");

            List<GameInThisPrediction> changes = new List<GameInThisPrediction>();

            var previousVersion = this.PreviousVersion();
            var previousResult = previousVersion.Results;
            var zip = results.Zip(previousResult, (n, p) => new { currentPrediction = n, previousPrediction = p });

            foreach (var z in zip)
            {
                var gameInPredictionOfCurrent = z.previousPrediction;
                bool changed = !z.currentPrediction.Scores.HasTheSameWinnerThan(z.previousPrediction.Scores) ||
                    z.currentPrediction.TeamA != z.previousPrediction.TeamA ||
                    z.currentPrediction.TeamB != z.previousPrediction.TeamB;
                if (changed)
                {
                    changes.Add(gameInPredictionOfCurrent);
                }
            }
            return changes;
        }
    }

    [Puppet]
    internal class GameInThisPrediction : Objeto
    {
        private readonly Predictions preditions;
        private readonly Game gameOfTheTournament;
        private Team teamA;
        private Team teamB;
        private readonly ScorePair predictedScore;

        internal GameInThisPrediction(Predictions preditions, Game gameOfTheTournament)
        {
            if (preditions == null) throw new ArgumentNullException(nameof(Predictions));
            if (gameOfTheTournament == null) throw new ArgumentNullException(nameof(GameInThisPrediction));

            this.gameOfTheTournament = gameOfTheTournament;
            this.preditions = preditions;
            if (gameOfTheTournament.HasTeamA())
            {
                this.teamA = gameOfTheTournament.TeamA;
            }
            if (gameOfTheTournament.HasTeamB())
            {
                this.teamB = gameOfTheTournament.TeamB;
            }
            predictedScore = new ScorePair(gameOfTheTournament);
        }

        internal GameInThisPrediction(GameInThisPrediction gameInThisPrediction)
        {
            if (gameInThisPrediction == null) throw new ArgumentNullException(nameof(GameInThisPrediction));
            gameOfTheTournament = gameInThisPrediction.Game;
            preditions = gameInThisPrediction.preditions;
            teamA = gameInThisPrediction.TeamA;
            teamB = gameInThisPrediction.TeamB;
            predictedScore = new ScorePair(gameOfTheTournament);
            predictedScore.ScoreTeamA = gameInThisPrediction.predictedScore.ScoreTeamA;
            predictedScore.ScoreTeamB = gameInThisPrediction.predictedScore.ScoreTeamB;
        }

        internal Game Game
        {
            get
            {
                return gameOfTheTournament;
            }

        }

        internal Team TeamA
        {
            get
            {
                if (teamA == null) throw new GameEngineException("Team A is not been configured.");
                return teamA;
            }
            set
            {
                if (value == null) throw new ArgumentNullException(nameof(value));
                teamA = value;
            }
        }

        internal Team TeamB
        {
            get
            {
                if (teamB == null) throw new GameEngineException("Team B is not been configured.");
                return teamB;
            }
            set
            {
                if (value == null) throw new ArgumentNullException(nameof(value));
                teamB = value;
            }
        }

        internal bool HasPredecessors()
        {
            return gameOfTheTournament.HasPredecessors() &&
                !preditions.StartedOn(gameOfTheTournament.CurrentRound);
        }

        internal bool MatchedPlayerA
        {
            get
            {
                if (!gameOfTheTournament.HasPredecessors()) throw new GameEngineException($"Game {gameOfTheTournament.Number} is a game of {preditions.InitialRoundOfPredictions().Name} so it has not predecessors on these predictions");
                var fatherOfA = PredecessorGameOfA();
                GameInThisPrediction prev = preditions.PredictionOf(fatherOfA.Number);
                bool matched = prev.Game.IsGameOver() && prev.PredictedWinner() == prev.Game.Winner();
                return matched;
            }
        }

        internal bool MatchedPlayerB
        {
            get
            {
                if (!gameOfTheTournament.HasPredecessors()) throw new GameEngineException($"Game {gameOfTheTournament.Number} is a game of {preditions.InitialRoundOfPredictions().Name} so it has not predecessors on these predictions");
                var fatherOfB = PredecessorGameOfB();
                GameInThisPrediction prev = preditions.PredictionOf(fatherOfB.Number);
                bool matched = prev.Game.IsGameOver() && prev.PredictedWinner() == prev.Game.Winner();
                return matched;
            }
        }

        internal Game PredecessorGameOfA()
        {
            if (!HasPredecessors()) throw new GameEngineException($"Predictions do not include a predecessor of Game {gameOfTheTournament.Number}");
            NGame thisGame = (NGame)gameOfTheTournament;
            return thisGame.PredecessorGameOfA();
        }

        internal Game PredecessorGameOfB()
        {
            if (!HasPredecessors()) throw new GameEngineException($"Predictions do not include a predecessor of Game {gameOfTheTournament.Number}");
            NGame thisGame = (NGame)gameOfTheTournament;
            return thisGame.PredecessorGameOfB();
        }

        internal ScorePair Scores
        {
            get
            {
                return predictedScore;
            }
        }

        private int points = 0;
        internal int GradeAndSetPoints(Predictions.PointsCriteria criteria)
        {
            points = 0;
            if (
                (gameOfTheTournament.HasTeamA() && this.HasTeamA() && gameOfTheTournament.TeamAIsWinner() && gameOfTheTournament.TeamA == this.TeamA) ||
                (gameOfTheTournament.HasTeamB() && this.HasTeamB() && !gameOfTheTournament.TeamAIsWinner() && gameOfTheTournament.TeamB == this.TeamB)
            )
            {
                points = criteria(gameOfTheTournament, gameOfTheTournament.Scores, this.Scores);
            }
            return points;
        }

        internal BigInteger GradeUntie(Predictions.UntieCriteria criteria)
        {
            BigInteger pointsOfUntie = criteria();
            return pointsOfUntie;
        }

        internal int Points()
        {
            return points;
        }

        public override bool Equals(object obj)
        {
            if (this == obj) return true;

            GameInThisPrediction temp = (GameInThisPrediction)obj;
            return this.gameOfTheTournament.Equals(temp.gameOfTheTournament);
        }

        internal bool HasTeamA()
        {
            return this.teamA != null;
        }

        internal bool HasTeamB()
        {
            return this.teamB != null;
        }

        internal bool HasResult()
        {
            return this.predictedScore != null && !this.predictedScore.IsAtLeastOneScoreNotConfiguregYet();
        }

        internal Team RetrieveTheOtherPlayer(Team currentTeam)
        {
            if (currentTeam.Equals(TeamA))
            {
                return TeamB;
            }
            else if (currentTeam.Equals(TeamB))
            {
                return TeamA;
            }
            else
            {
                throw new GameEngineException("Current team is not part of this game.");
            }
        }

        internal Team PredictedWinner()
        {
            var isPlayerA = predictedScore.TeamAisWinner();
            return isPlayerA ? TeamA : TeamB;
        }

        internal bool IsWinnerPlayerA()
        {
            return PredictedWinner() == teamA;
        }

        internal void WriteANewResult(Team teamA, Score scoreA, Team teamB, Score scoreB)
        {
            this.teamA = teamA;
            this.teamB = teamB;

            predictedScore.ScoreTeamA = scoreA;
            predictedScore.ScoreTeamB = scoreB;
        }

        internal bool HasTheSamePlayers(GameInThisPrediction gameInThisPrediction)
        {
            return this.teamA.Equals(gameInThisPrediction.TeamA) && this.teamB.Equals(gameInThisPrediction.TeamB);
        }
    }

}
