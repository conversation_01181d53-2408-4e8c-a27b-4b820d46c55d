﻿using GamesEngine.Bets;
using GamesEngine.Games.Lotto;
using GamesEngine.Tools;
using System;
using System.Linq;

namespace GamesEngine.Gameboards.Lotto
{
    internal abstract class TicketKenoMultiplierAndBulleye : TicketKeno
    {
        internal TicketKenoMultiplierAndBulleye(Player player, decimal baseCost, LotteryKeno lotteryKeno, DateTime drawDate, string drawId, int[] numbers, DateTime creationDate, decimal ticketCost, Prizes prizes) :
           base(player, baseCost, lotteryKeno, drawDate, drawId, numbers, true, true, creationDate, ticketCost, prizes)

        {
        }

    }
    internal sealed class TicketKeno10MultiplierAndBulleye : TicketKenoMultiplierAndBulleye
    {
        internal TicketKeno10MultiplierAndBulleye(Player player, decimal baseCost, LotteryKeno lotteryKeno, DateTime drawDate, string drawId, int[] numbers, DateTime creationDate, decimal ticketCost, Prizes prizes) :
           base(player, baseCost, lotteryKeno, drawDate, drawId, numbers, creationDate, ticketCost, prizes)

        {
        }

        internal override TicketType IdOfType()
        {
            return TicketType.K10;
        }

        internal override string GameTypeForReports()
        {
            return QueryMakerOfHistoricalKeno.ID_K10_MUL_BULL;
        }

    }
    internal sealed class TicketKeno12MultiplierAndBulleye : TicketKenoMultiplierAndBulleye
    {
        internal TicketKeno12MultiplierAndBulleye(Player player, decimal baseCost, LotteryKeno lotteryKeno, DateTime drawDate, string drawId, int[] numbers, DateTime creationDate, decimal ticketCost, Prizes prizes) :
           base(player, baseCost, lotteryKeno, drawDate, drawId, numbers, creationDate, ticketCost, prizes)

        {
        }

        internal override TicketType IdOfType()
        {
            return TicketType.K12;
        }

        internal override string GameTypeForReports()
        {
            return QueryMakerOfHistoricalKeno.ID_K12_MUL_BULL;
        }

    }

}
