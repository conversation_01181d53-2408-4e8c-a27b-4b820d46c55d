﻿using System;
using System.Collections.Generic;

namespace ExchangeAPI.ExchangeEntities
{
    public partial class Profitabletransaction
    {
        public long Id { get; set; }
        public int Authorizationid { get; set; }
        public DateTime Date { get; set; }
        public int Fromcurrencyid { get; set; }
        public int Tocurrencyid { get; set; }
        public string Type { get; set; }
        public long Exchangerateid { get; set; }
        public long Whocreated { get; set; }
        public long? Whoapproved { get; set; }
        public string Fromcustomer { get; set; }
        public string Tocustomer { get; set; }
        public decimal Amount { get; set; }
        public long Batchnumber { get; set; }
        public decimal Gross { get; set; }
        public decimal Net { get; set; }
        public decimal Profit { get; set; }
        public string Account { get; set; }
        public string Targetaccount { get; set; }

        public virtual Exchangerate Exchangerate { get; set; }
        public virtual Currency Fromcurrency { get; set; }
        public virtual Currency Tocurrency { get; set; }
        public virtual Exchangeuser WhoapprovedNavigation { get; set; }
        public virtual Exchangeuser WhocreatedNavigation { get; set; }
    }
}
