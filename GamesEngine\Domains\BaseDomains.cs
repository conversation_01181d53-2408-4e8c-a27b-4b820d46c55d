﻿using Puppeteer.EventSourcing.Libraries;
using System;
using System.Linq;
using System.Collections.Generic;
using System.Text;
using Puppeteer.EventSourcing;
using GamesEngine.Business;
using static GamesEngine.Exchange.PaymentProcessorsAndActionsByDomains;
using static GamesEngine.Finance.PaymentChannels;

namespace GamesEngine.Domains
{
    [Puppet]
    internal abstract class BaseDomains:Objeto
    {
        private readonly Dictionary<int, Domain> domains = new Dictionary<int, Domain>();
        private readonly Company company;
        private int nextConsecutive = 0;

        internal BaseDomains(Company company)
        {
            this.company = company;
        }

        protected Company Company
        {
            get
            {
                return company;
            }
        }

        internal IEnumerable<Domain> GetAll
        {
            get
            {
                return domains.Values.ToList();
            }
        }

        internal IEnumerable<Domain> Visibles
        {
            get
            {
                return domains.Values.Where(domain => domain.Visible).ToList();
            }
        }

        internal int Count => domains.Count;

        

        protected Domain CreateDomain(bool itIsThePresent, int id, string url, Agents agent, Logs.Log log)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(url)) throw new ArgumentNullException(nameof(url));
            if (agent == null) throw new ArgumentNullException(nameof(agent));
            if (log == null) throw new ArgumentNullException(nameof(log));
            if (this.company.System.Domains.Exists(url)) throw new GameEngineException($"URL {url} already exist. Use GetOrCreate instead");

            var normalizedUrl = Domain.NormalizeUrl(url);
            var domain = new Domain(itIsThePresent, id, normalizedUrl, agent, log);
            Add(domain);
            return domain;
        }

        internal virtual void Add(Domain domain)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (Exists(domain.Id)) throw new GameEngineException($"Domain with {nameof(domain.Id)} '{domain.Id}' already exist");
            
            domains.Add(domain.Id, domain);
            nextConsecutive = domain.Id;
        }

        [Obsolete("Use UpdateDomainUrl with parameter itIsThePresent.")]
        internal void UpdateDomainUrl(string domainUrl, string newUrl)
        {
            if (String.IsNullOrWhiteSpace(domainUrl)) throw new ArgumentNullException(nameof(domainUrl));
            if (String.IsNullOrWhiteSpace(newUrl)) throw new ArgumentNullException(nameof(newUrl));
            UpdateDomainUrl(false, domainUrl, newUrl);
        }

        internal virtual void UpdateDomainUrl(bool itIsThePresent, string domainUrl, string newUrl)
        {
            if (String.IsNullOrWhiteSpace(domainUrl)) throw new ArgumentNullException(nameof(domainUrl));
            if (String.IsNullOrWhiteSpace(newUrl)) throw new ArgumentNullException(nameof(newUrl));

            var normalizedDomainUrl = Domain.NormalizeUrl(domainUrl);
            var normalizedNewUrl = Domain.NormalizeUrl(newUrl);
            if (!Exists(normalizedDomainUrl)) throw new GameEngineException($"Domain '{normalizedDomainUrl}' does not exist");
            if (normalizedDomainUrl == normalizedNewUrl) throw new GameEngineException($"Both urls are the same. It cannot be updated");

            var currentDomain = DomainFrom(normalizedDomainUrl);
            currentDomain.UpdateMainUrl(itIsThePresent, newUrl);
            foreach (var key in currentDomain.ResourceKeys)
            {
                if (!currentDomain.HasResourceUrl(key))
                {
                    var isFlexibleUrl = currentDomain.IsFlexibleResourceUrl(key);
                    if (isFlexibleUrl)
                    {
                        currentDomain.AddFlexibleResourceUrl(key, currentDomain.ResourceUrl(key));
                    }
                    else
                    {
                        currentDomain.AddResourceUrl(key, currentDomain.ResourceUrl(key));
                    }
                }
            }
        }

        internal void UpdateResourceUrl(int id, string resourceKey, string newResourceUrl)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");
            if (String.IsNullOrWhiteSpace(resourceKey)) throw new ArgumentNullException(nameof(resourceKey));
            if (String.IsNullOrWhiteSpace(newResourceUrl)) throw new ArgumentNullException(nameof(newResourceUrl));

            var normalizedResourceKey = resourceKey.ToLower().Trim();
            var normalizedNewResourceUrl = newResourceUrl.ToLower().Trim();

            var domain = DomainFrom(id);
            domain.UpdateResourceUrl(normalizedResourceKey, normalizedNewResourceUrl);
        }

        internal void AddResourceUrl(int id, string resourceKey, string newResourceUrl)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");
            if (String.IsNullOrWhiteSpace(resourceKey)) throw new ArgumentNullException(nameof(resourceKey));
            if (String.IsNullOrWhiteSpace(newResourceUrl)) throw new ArgumentNullException(nameof(newResourceUrl));

            var normalizedResourceKey = resourceKey.ToLower().Trim();
            var normalizedNewResourceUrl = newResourceUrl.ToLower().Trim();

            var domain = DomainFrom(id);
            domain.AddResourceUrl(normalizedResourceKey, normalizedNewResourceUrl);
        }

        internal void AddFlexibleResourceUrl(int id, string resourceKey, string newResourceUrl)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");
            if (String.IsNullOrWhiteSpace(resourceKey)) throw new ArgumentNullException(nameof(resourceKey));
            if (String.IsNullOrWhiteSpace(newResourceUrl)) throw new ArgumentNullException(nameof(newResourceUrl));

            var normalizedResourceKey = resourceKey.ToLower().Trim();
            var normalizedNewResourceUrl = newResourceUrl.ToLower().Trim();

            var domain = DomainFrom(id);
            domain.AddFlexibleResourceUrl(normalizedResourceKey, normalizedNewResourceUrl);
        }

        [Obsolete("Use DomainFrom or CreateDomain instead.")]
        internal virtual Domain GetOrCreate(string url)
        {
            if (String.IsNullOrWhiteSpace(url)) throw new ArgumentNullException(nameof(url));

            var normalizedUrl = Domain.NormalizeUrl(url);
            Domain domain;
            if (Exists(normalizedUrl))
            {
                domain = DomainFrom(normalizedUrl);
            }
            else
            {
                var id = this.NextDomainConsecutive();
                domain = company.Sales.CreateDomain(false, id, normalizedUrl, Agents.INSIDER);
                Add(domain);
            }
            return domain;
        }

        internal int NextDomainConsecutive()
        {
            return nextConsecutive + 1;
        }


        internal Domain DomainFrom(string url)
        {
            if (String.IsNullOrWhiteSpace(url)) throw new ArgumentNullException(nameof(url));
            var normalizedUrl = Domain.NormalizeUrl(url);

            foreach (var domain in domains.Values)
            {
                if (domain.HasTheSame(normalizedUrl)) return domain;
            }
            throw new GameEngineException($"Url: {url} normalized to {normalizedUrl} does not exist");
        }

        internal Domain DomainFrom(int id)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");
            if (!Exists(id)) throw new GameEngineException($"Domain with {nameof(id)} '{id}' does not exist");

            var domain = domains[id];
            return domain;
        }

        internal bool Exists(string url)
        {
            if (String.IsNullOrWhiteSpace(url)) throw new ArgumentNullException(nameof(url));

            var normalizedUrl = Domain.NormalizeUrl(url);
            foreach (var domain in domains.Values)
            {
                if (domain.Url == normalizedUrl) return true;
            }
            return false;
        }

        internal bool Exists(int id)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} must be greater than 0");

            var result = domains.ContainsKey(id);
            return result;
        }

        internal bool Contains(Domain domain)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            var result = domains.ContainsValue(domain);
            return result;
        }

        internal void Remove(Domain domain)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            domains.Remove(domain.Id);
        }

        protected int Consecutive 
        {
            get
            {
                return nextConsecutive;
            }
            set
            {
                if (value <= 0) throw new GameEngineException($"{nameof(nextConsecutive)} must be greater than 0");
                nextConsecutive = value;
            }
        }

        internal static Domain DefaultDomain { get; } = new Domain(false, int.MaxValue, "-", Agents.INSIDER);
        
    }
}
