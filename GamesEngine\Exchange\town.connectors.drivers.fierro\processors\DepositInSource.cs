﻿using Connectors.town.connectors.driver.transactions;
using GamesEngine.Finance;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Http;
using System;
using System.Threading.Tasks;

using static town.connectors.drivers.Result;

namespace town.connectors.drivers.fiero
{
	[Obsolete]
    internal class DepositInSource : FierroProcessorDriver
	{
		private const float VERSION = 1.0F;
		public override string Description => $"Fiero {nameof(DepositInSource)} driver {VERSION}";
		public DepositInSource()
			: base(TransactionType.Deposit, VERSION)
        {
        }
		public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			var result = await AddAmountAsync(now, recordSet);
			AuthorizationTransaction auth;
			if (result > 0)
			{
				auth = new AuthorizationTransaction(result, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes);
				return (T)Convert.ChangeType(auth, typeof(T));
			}
			auth = new AuthorizationTransaction(result, TransactionStatus.DENIED, Entity, PaymentMethod, CurrencyIsoCodes);
			return (T)Convert.ChangeType(auth, typeof(T));
			
		}
		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			throw new NotImplementedException();
		}

		public override void Prepare(DateTime now)
		{
			CustomSettings.AddVariableParameter("actor");
			CustomSettings.AddVariableParameter("context");
			CustomSettings.AddVariableParameter("atAddress");
			CustomSettings.AddVariableParameter("sourceId");
			CustomSettings.AddVariableParameter("who");
			CustomSettings.AddVariableParameter("amount");
			CustomSettings.AddVariableParameter("storeId");
			CustomSettings.AddVariableParameter("concept");
			CustomSettings.AddVariableParameter("accountNumber");
			//CustomSettings.Prepare();
		}
		private async Task<int> AddAmountAsync(DateTime now, CustomSettings.RecordSet recordSet)
		{
			var Actor = recordSet.Mappings["actor"];
			var HttpContext = recordSet.Mappings["context"];
			var AtAddress = recordSet.Mappings["atAddress"];
			var SourceId = recordSet.Mappings["sourceId"];
			var Who = recordSet.Mappings["who"];
			var Amount = recordSet.Mappings["amount"];
			var StoreId = recordSet.Mappings["storeId"];
			var Concept = recordSet.Mappings["concept"];
			var AccountNumber = recordSet.Mappings["accountNumber"];

			var actor = Actor.As<RestAPISpawnerActor>();
			var context = HttpContext.As<HttpContext>();
			var atAddress = AtAddress.AsString;
			var sourceId = SourceId.AsInt;
			var who = Who.AsString;
			var amount = Amount.As<Currency>();
			var storeId = StoreId.AsInt;
			var concept = Concept.AsString;
			var accountNumber = AccountNumber.AsString;

			int authorization = await DepositAsync(actor, context, atAddress, sourceId, who, amount, storeId, concept, accountNumber);
			return authorization;
		}

		async Task<int> NextAuthorizationNumberAsync()
		{
			return await Movements.StorageAsync.NextValueAuthorizationNumberAsync();
		}

		async Task<int> DepositAsync(RestAPISpawnerActor actor, HttpContext context, string atAddress, int sourceId, string who, Currency amount, int storeId, string concept, string accountNumber)
		{
			var authorization = await NextAuthorizationNumberAsync();

			var result = await actor.PerformCmdAsync(atAddress, context, $@"
				{{
					source = atAddress.GetSource({sourceId});
					store = company.Sales.StoreById( {storeId});
					source.Accredit(itIsThePresent, Now, Currency('{amount.CurrencyCode}',{amount.Value}), '{who}', '{authorization}', store, '{concept}', '{authorization}', '{accountNumber}');
					print {authorization} authorizationId;
				}}
			");
			return authorization;
		}

	}
}
